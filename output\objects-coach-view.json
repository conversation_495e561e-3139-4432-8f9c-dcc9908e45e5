{"typeName": "Coach <PERSON>", "count": 40, "objects": [{"id": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "versionId": "2de00cf6-d27b-4cc6-81a2-d1b6bba2edef", "name": "Attachment", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "attach", "configOptions": ["canUpdate", "canCreate", "canDelete", "ECMProperties", "remittanceLetter<PERSON>ath"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.add = function() {\r\r\n//\talert(\"adding\");\r\r\n////\tthis.context.binding.get(\"value\").set(\"name\", \"Others\");\r\r\n////\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n////\t\r\r\n//}\r\r\n//this.context.options.selectedAttach.get(\"value\").get(\"name\")\r\r\n\r\r\nvar flag = false\r\r\nthis.makeflagtrue = function () {\r\r\n\tflag = true;\r\r\n\talert(\"makeing\");\r\r\n}\r\r\nthis.addAttacmentValidation=function () {\r\r\n\talert(flag);\r\r\n\tvar len = this.context.binding.get(\"value\").length();\r\r\n\tthis.context.binding.get(\"value\").get(len-1).set(\"name\", \"others\")\r\r\n//\t\r\r\n//\tthis.context.binding.get(\"value\").get(len).set(\"name\", \"Others\");\r\r\n//\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n\r\r\n\t\r\r\n}\r\r\nthis.visControl = function  () {\r\r\n\r\r\n\tif (this.context.options.visiable.get(\"value\")) {\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,false);\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}\r\r\nthis.setCMISquery = function  () {\r\r\n\tvar parent=this.context.options.parentPath;\r\r\n\tvar query =\"SELECT * FROM cmis:Document WHERE IN_TREE('\"+parent+\"')\";\r\r\n\tthis.context.options.cmisQuery.set(\"value\", query);\r\r\n}"}]}, "hasDetails": true}, {"id": "64.3361b968-662d-491d-8e02-666b6b3648ec", "versionId": "5bc4218b-087f-424c-9ba1-0eb7aced65ca", "name": "Attachment 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "attach", "configOptions": ["canUpdate", "canCreate", "canDelete", "ECMProperties", "remittanceLetter<PERSON>ath", "updateProperties"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.add = function() {\r\r\n//\talert(\"adding\");\r\r\n////\tthis.context.binding.get(\"value\").set(\"name\", \"Others\");\r\r\n////\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n////\t\r\r\n//}\r\r\n//this.context.options.selectedAttach.get(\"value\").get(\"name\")\r\r\n\r\r\nthis.test2 = function(){\r\r\n\tthis.context.options.updateProperties.get(\"value\").get(0).set(\"name\", \"Bate5aaa\");\r\r\n\tconsole.log(this.context.options.updateProperties.get(\"value\").items)\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\nvar flag = false\r\r\nthis.makeflagtrue = function () {\r\r\n\tflag = true;\r\r\n\talert(\"makeing\");\r\r\n}\r\r\nthis.addAttacmentValidation=function () {\r\r\n\talert(flag);\r\r\n\tvar len = this.context.binding.get(\"value\").length();\r\r\n\tthis.context.binding.get(\"value\").get(len-1).set(\"name\", \"others\")\r\r\n//\t\r\r\n//\tthis.context.binding.get(\"value\").get(len).set(\"name\", \"Others\");\r\r\n//\tthis.ui.get(\"documentTypeDescription\").setEnabled(true);\r\r\n\r\r\n\t\r\r\n}\r\r\nthis.visControl = function  () {\r\r\n\r\r\n\tif (this.context.options.visiable.get(\"value\")) {\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"Table\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,false);\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}\r\r\nthis.setCMISquery = function  () {\r\r\n\tvar parent=this.context.options.parentPath;\r\r\n\tvar query =\"SELECT * FROM cmis:Document WHERE IN_TREE('\"+parent+\"')\";\r\r\n\tthis.context.options.cmisQuery.set(\"value\", query);\r\r\n}"}]}, "hasDetails": true}, {"id": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "versionId": "122c2152-ac38-426a-a5d0-7bd7f51c5c49", "name": "Basic Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "this.valueDate = bpmext.ui.getView('/DC_Templete1/8/valueDate').setEnabled()", "bindingType": "basicDetails", "configOptions": ["parentRequestNoVis", "flexCubeContractNoVIS", "basicDetailsCVVIS", "contractStageVIS", "multiTenorDatesVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.multiTenorDatesVIS = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"paymentTerms\").get(\"name\") == \"001\")\r\r\n\t\tthis.context.options.multiTenorDatesVIS.set(\"value\", \"None\");\r\r\n\t\t\r\r\n\telse\r\r\n\t\tthis.context.options.multiTenorDatesVIS.set(\"value\", \"Editable\");\r\r\n}\r\r\n\r\r\nthis.testVis = function(){\r\r\n\tvar valueDateView = this.valueDate;\r\r\n}"}]}, "hasDetails": true}, {"id": "64.74c6fa59-aa38-49a4-89bc-f00692e89191", "versionId": "83f0eba5-cd01-40ef-b913-38fd871d516c", "name": "Charges And Commissions CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "chargesAndCommission", "configOptions": ["chargesAndCommVis", "btnVis", "amountCollectableByNBE", "addchargeBtn", "exchangeRate"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//console.log(\"outside vis function \"+this.context.options.addchargeBtn.get(\"value\"));\r\r\n\r\r\n//this.chargesSectionVIS = function()\r\r\n//{\r\r\n//\tvar addChargeBtn= this.context.options.addchargeBtn.get(\"value\") ;\r\r\n//\tconsole.log(\"inside vis function \"+addChargeBtn);\r\r\n//\t\r\r\n//\tif(addChargeBtn == false)\r\r\n//\t\tthis.ui.get(\"chargesAndCommVis\").setVisible(false,true);\r\r\n//\telse\r\r\n//debugger;\r\r\n//\t\tthis.ui.get(\"chargesVIS\").setVisible(true,true);\r\r\n//\t\tthis.ui.get(\"chargesVIS\").setVisible(true,true);\r\r\n//}\r\r\n\r\r\n//this.calculateDefaultAmount = function(index)\r\r\n//{console.log(\"inside  calculateDefaultAmount function\");\r\r\n//\tvar Index      = index.ui.getIndex();\r\r\n//\tvar rateType   = this.ui.get(\"rateType[\"+index+\"]\").getData();\r\r\n//\tvar flatAmount = this.ui.get(\"flatAmount[\"+index+\"]\").getData();\r\r\n//\tvar rate       = this.ui.get(\"rate[\"+index+\"]\").getData();\r\r\n//\t\r\r\n//\tconsole.log(\"index >>\"+Index);\r\r\n//\tconsole.log(\"rate type >>\"+rateType);\r\r\n//\tconsole.log(\"flat amount >>\"+flatAmount);\r\r\n//\t\r\r\n//\tif(this.ui.get(\"rateType[\"+Index+\"]\").getData() == \"flat amount\")\r\r\n//\t{\r\r\n//\t\tthis.ui.get(\"defaultAmount[\"+Index+\"]\").setData(flatAmount);\r\r\n//\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setData(flatAmount);\r\r\n//\t}\r\r\n//\telse if(this.ui.get(\"rateType[\"+Index+\"]\").getData() == \"fixed rate\")\r\r\n//\t{\r\r\n//\t\tvar result = (rate/100)* this.context.options.amountCollectableByNBE;\r\r\n//\t\t\r\r\n//\t\tthis.ui.get(\"defaultAmount[\"+Index+\"]\").setData(result);\r\r\n//\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setData(result)\r\r\n//\t\t\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//this.accountTypes = function (value){\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (value.getData() == \"Customer Account\"){\r\r\n//\t\tthis.ui.get(\"customerAccountNo[\"+index+\"]\").setEnabled(true);\r\r\n//\t\t\r\r\n//       \tthis.ui.get(\"accountBranchCode[\"+index+\"]\").setEnabled(false);\r\r\n//       \tthis.ui.get(\"accountCurrency[\"+index+\"]\").setEnabled(false);\r\r\n//\t\tthis.ui.get(\"glAccountNo[\"+index+\"]\").setEnabled(false);\r\r\n//    \t      this.ui.get(\"glAccountNo[\"+index+\"]\").setData(\"\");\r\r\n//    \t\t\r\r\n//\t}else if (value.getData() == \"GL Account\"){\r\r\n//\t\tthis.ui.get(\"customerAccountNo[\"+index+\"]\").setEnabled(false);\r\r\n//\t\t  this.ui.get(\"customerAccountNo[\"+index+\"]\").setData(\"\");\r\r\n//\t\t  \r\r\n//       \tthis.ui.get(\"accountBranchCode[\"+index+\"]\").setEnabled(true);\r\r\n//       \tthis.ui.get(\"accountCurrency[\"+index+\"]\").setEnabled(true);\r\r\n//\t\tthis.ui.get(\"glAccountNo[\"+index+\"]\").setEnabled(true);\r\r\n//    \t    \r\r\n//    }\r\r\n//}\r\r\n//\r\r\n//this.calculateDebitAmount = function (value){\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tvar changeAmount = this.ui.get(\"changeAmount[\"+index+\"]\").getData();\r\r\n//\tvar negRate = this.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").getData();\r\r\n//\tvar result = changeAmount * negRate;\r\r\n//\tthis.ui.get(\"debitedamount[\"+index+\"]\").setData(result);\r\r\n//}"}]}, "hasDetails": true}, {"id": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "versionId": "a72c1fc9-ee22-4196-9904-eb4eb8bfe774", "name": "Charges And Commissions CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (!this.context.options.isChecker.get(\"value\")) {\r\r\n\tthis.ui.get(\"GetChargesBtn\").click();\r\r\n}else{\r\r\n\tthis.ui.get(\"Vertical_Layout7\").setEnabled(false);\r\r\n}", "bindingType": "chargesAndCommission", "configOptions": ["amountCollectableByNBE", "accountList", "accountNo", "index", "commissionSectionVIS", "calculatedChangeAmnt", "chargesCustomerAccountList", "exRate", "<PERSON><PERSON><PERSON><PERSON>"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//On Change Amount and Negotiated Exchange Rate - calculate Debited Amount\r\r\nthis.calculateDebitedAmount = function(index){\r\r\n\tvar chargeAmount = this.context.binding.get(\"value\").get(index).get(\"changeAmount\");\r\r\n\tvar NegotiableRate = this.context.binding.get(\"value\").get(index).get(\"debitedAmount\").get(\"negotiatedExRate\");\r\r\n\tvar debitedAmount = chargeAmount * NegotiableRate;\r\r\n\t\r\r\n\tthis.context.binding.get(\"value\").get(index).get(\"debitedAmount\").set(\"amountInAccount\", debitedAmount);\r\r\n}\r\r\n\r\r\n//On Account Class - Set vis for GL and Customer account\r\r\n//this.accountNumVis = function(value){\r\r\n//\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\") == \"001\") {\r\r\n//\t    //Customer\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setData(\"\");\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t    }\r\r\n//\t\r\r\n//\t} else {\r\r\n//\t    //GL\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setData(null);\r\r\n//\t\r\r\n//\t        //GL account cant be overDrafted or not\r\r\n//\t        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setEnabled(false);\r\r\n//\t    }\r\r\n//\t}\r\r\n//\r\r\n//}\r\r\n\r\r\n//On Rate Type - Set Vis for Flat amount and fixed rate\r\r\nthis.rateTypeVis = function(value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rateType= value.getData();\r\r\n\tif(rateType!=null && rateType == \"Flat Amount\"){\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setData(0);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setData(0);\r\r\n\t}\r\r\n\telse if(rateType!=null && rateType == \"Fixed Rate\"){\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//====================================================================================================\r\r\n//Generic Validate Positive amount (accepts 0) (Change Amount)\r\r\nthis.validatePositive = function(value){\r\r\n\tif (value.getData() < 0) {\r\r\n\t\tvalue.setValid(false,\"Must be >= 0\");\r\r\n//\t\tvalue.setData(0.0);\r\r\n\t}else{\r\r\n\t\tvalue.setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//Generic Validate Digits Only (Account Branch Code)\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Change of Change Percentage - Validate Change percentage before calculate change amount\r\r\nthis.amountValidation = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"rateType\") ==\"Fixed Rate\") {\t\t\r\r\n\t\tif (value.getData() < 0 || value.getData() > 100){\r\r\n\t\t\tvalue.setData(0);\r\r\n\t\t\tvalue.setValid(false, \"Must be >= 0 and < 100\");\r\r\n\t\t}else{\r\r\n\t\t\tvalue.setValid(true);\r\r\n\t\t\tthis.calcChangeAmnt(value);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nvar newIndex;\r\r\n//call from amountValidation function - Calculate change amount service call\r\r\nthis.calcChangeAmnt = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar requestAmnt = this.context.options.amountCollectableByNBE.get(\"value\");\r\r\n\tvar changePercentage = this.context.binding.get(\"value\").get(index).get(\"changePercentage\");\r\r\n\t\r\r\n\tvar concatedDefault = requestAmnt + \",\" + changePercentage;\r\r\n\tthis.ui.get(\"calculateChangeAmount1[\"+index+\"]\").execute(concatedDefault);\r\r\n\tnewIndex = index;\r\r\n}\r\r\n\r\r\n//On result of calculateChangeAmount1 - Map Service call ouput\r\r\nthis.setChangeAmnt = function(){\r\r\n\tvar index = newIndex;\r\r\n\tvar changeAmount = this.context.options.calculatedChangeAmnt.get(\"value\");\r\r\n\tif(!!changeAmount){\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",changeAmount);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",0);\r\r\n\t}\r\r\n} \r\r\n\r\r\n//On change of Account Num - Set Account Info for each customer Account\r\r\nthis.fillAccountData = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tfor (var i = 0; i < this.context.options.chargesCustomerAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t//Set isOverDraft\r\r\n\t\t\tvar commClassCode = this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0, 1);\r\r\n\r\r\n\t\t\tif (code == \"O\" || code == \"D\") {\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\r\r\n\t\t\t//Set Account Info\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"branchCode\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\", {});\r\r\n\t\t\tthis.ui.get(\"accountCurrency[\"+index+\"]\").setData(this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").set(\"vale\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\",this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balance\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t\t\r\r\n\t\t\tthis.context.options.index.set(\"value\", index);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of GL Account Num - Reset Data on GL account change\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"isGLFound\",false);\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"glVerifyMSG\",\"\");\r\r\n}\r\r\n\r\r\n//On click of validate GL Btn - Call A service call to validate GL\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"glAccountNo\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\n//On result of Validate GL - Map the result of GL service call\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"isGLFound\") ) {\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(true);\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"Verified\");\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of Account Cry - Apply vis and Call A service to get exchange rate between Default Cry and Account Cry\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar cryCode = value.getData();\r\r\n//\tvar cryCode = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").get(\"value\");\r\r\n\tif(cryCode != \"\" && cryCode != null && cryCode != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == cryCode) {\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = cryCode;\r\r\n\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Result of get exchange rate\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\n//On Debited Amount in Account Currency and Account Balance- validate is over Draft\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar accountBalance = this.ui.get(\"accountBalance[\"+index+\"]\").getData();\r\r\n\tvar debitedAmount = this.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").getData();\r\r\n\t\r\r\n\t//Need to add &&accountClass is Customer Account-->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\r\n\tif (debitedAmount > accountBalance) {\r\r\n\t\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t\t}else if (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == false){\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"Error: Must be < Account Balance\");\r\r\n\t\t}\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n// Reusable function to set visibility and enabled state\r\r\nthis.setUIVisibilityAndState = function (ui, index, config) {\r\r\n    for (var i = 0; i < config.length; i++) {\r\r\n\t\tvar item = config[i];\r\r\n\t\tui.get(item.name + \"[\" + index + \"]\").setVisible(item.visible, true);\r\r\n\t\tif (item.enabled !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setEnabled(item.enabled);\r\r\n\t\t}\r\r\n\t\tif (item.data !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setData(item.data);\r\r\n\t\t}\r\r\n    }\r\r\n};\r\r\n\r\r\n// Handle Customer Account\r\r\nthis.handleCustomerAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: true },\r\r\n        { name: \"accountBalance\", visible: true },\r\r\n        { name: \"customerAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: false },\r\r\n        { name: \"GLSection1\", visible: false },\r\r\n        { name: \"accountCurrency\", enabled: false }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"glAccountNo\", data: \"\" });\r\r\n    } else {\r\r\n        config.push({ name: \"customerAccountNo\", enabled: false });\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n    this.ui.get(\"accountBranchCode[\" + index + \"]\").setEnabled(false);\r\r\n};\r\r\n\r\r\n// Handle GL Account\r\r\nthis.handleGLAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: false },\r\r\n        { name: \"accountBalance\", visible: false },\r\r\n        { name: \"customerAccountNo\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"GLSection1\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: true, enabled: !isChecker },\r\r\n        { name: \"accountCurrency\", enabled: !isChecker },\r\r\n        { name: \"accountBranchCode\", enabled: !isChecker }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"customerAccountNo\", data: null });\r\r\n        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n};\r\r\n\r\r\n// Main function\r\r\nthis.accountNumVis = function (value) {\r\r\n    var index = value;\r\r\n    var accountClass = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\");\r\r\n    var isChecker = this.context.options.isChecker.get(\"value\");\r\r\n\r\r\n    if (accountClass === \"001\") {\r\r\n        // Customer\r\r\n        this.handleCustomerAccount(index, isChecker);\r\r\n    } else {\r\r\n        // GL\r\r\n        this.handleGLAccount(index, isChecker);\r\r\n    }\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": ".panel-heading {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    justify-content: space-between; /* Distribute space between title and controls */\r\r\n    height: 50px; /* Fixed height for the panel heading */\r\r\n    padding: 0 10px; /* Add horizontal padding */\r\r\n    box-sizing: border-box; /* Include padding and border in height calculations */\r\r\n    overflow: hidden; /* Hide any overflowing content */\r\r\n}\r\r\n\r\r\n.panel-title {\r\r\n    flex: 1; /* Allow title to grow and fill available space */\r\r\n    white-space: nowrap; /* Prevent text from wrapping */\r\r\n    overflow: hidden; /* Hide overflow text */\r\r\n    text-overflow: ellipsis; /* Add ellipsis if text overflows */\r\r\n    font-size: 14px; /* Adjust font size to make the title smaller */\r\r\n    line-height: 1; /* Adjust line height for better text fitting */\r\r\n    margin: 0; /* Ensure no extra margin affecting layout */\r\r\n}\r\r\n\r\r\n.panel-heading-controls {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    margin-left: auto; /* Push controls to the right */\r\r\n}"}]}, "hasDetails": true}, {"id": "64.24e33a8b-e481-4548-81a5-6647b0c96320", "versionId": "9bd3cc2d-089b-489c-9a22-9c78560bf5ce", "name": "Commissions And Charges", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "CommissionsAndChargesDetails", "configOptions": ["CommAccountList", "customerCIF", "accounteeCIF", "caseCIF", "draweeCIF", "<PERSON><PERSON><PERSON><PERSON>", "exRate", "accountIndexC", "alertMessage", "errorVis"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.debitedAmountSum = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar changeAm = this.ui.get(\"ChangeAmount[\"+index+\"]\").getData();\r\r\n\tvar nRate = this.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").getData();\r\r\n\tvar sum = changeAm * nRate;\r\r\n\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setData(sum);\r\r\n}\r\r\n\r\r\nthis.executeService = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (value.getData() == \"Customer Account\"){\r\r\n       \tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setEnabled(false);\r\r\n       \tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setEnabled(false);\r\r\n        \tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setEnabled(false);\r\r\n       \tthis.ui.get(\"Owner1[\"+index+\"]\").setEnabled(true);\r\r\n    \t\tif (this.ui.get(\"Owner1[\"+index+\"]\").getData() != \"\") {\r\r\n   \t  \t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(true);\r\r\n    \t  \t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setData(\"\");\r\r\n    \t\t}\r\r\n\t}else if (value.getData() == \"GL Account\"){\r\r\n\t\tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setEnabled(true);\r\r\n       \tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(false);\r\r\n\t\tthis.ui.get(\"Owner1[\"+index+\"]\").setEnabled(false);\r\r\n   \t  \tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+index+\"]\").setEnabled(true);\r\r\n\t\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"Decimal1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"Output_Text1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"AccountBranchCode1[\"+index+\"]\").setData(\"\");\r\r\n\t\tthis.ui.get(\"AccountCurrency1[\"+index+\"]\").setData(\"\");\r\r\n    }\r\r\n}\r\r\n\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\tthis.context.options.accountIndexC.set(\"value\", value.ui.getIndex());\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"GLAccountNumber\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\nthis.setAccountInfo = function (value){\r\r\n\tfor (var i=0; i<this.context.options.CommAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t// this.context.options.commClassCode.set(\"value\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountClassCode\"));\r\r\n\t\t\t// var commClassCode =  this.context.options.commClassCode.get(\"value\");//SA04\r\r\n\t\t\tcommClassCode = this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0,1);\r\r\n\t\t\t\r\r\n\t\t\tif (code == \"O\" || code == \"D\"){\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountBranchCode\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountCurrency\",{});\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").get(\"accountCurrency\").set(\"code\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"accountBalance\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(value.ui.getIndex()).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.CommAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"debitedAccount\").get(\"isGLFoundC\")) {\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"AccountNumberGL[\"+record+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.setPartyCIF = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.ui.get(\"AccountNumber[\"+index+\"]\").setEnabled(true);\r\r\n\tif (value.getData() == \"Accountee\") {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.accounteeCIF.get(\"value\"));\r\r\n\t}else if (value.getData() == \"Case In Need\") {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.caseCIF.get(\"value\"));\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"commCIF\", this.context.options.draweeCIF.get(\"value\"));\r\r\n\t}\r\r\n\tthis.ui.get(\"GetCustomerAccount1[\"+index+\"]\").execute(this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"commCIF\"));\r\r\n}\r\r\n\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(value.getData() != \"\" && value.getData() != null && value.getData() != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == value.getData()) {\r\r\n\t\t\tthis.ui.get(\"StandardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setData(this.ui.get(\"StandardExchangeRate[\"+index+\"]\").getData());\r\r\n\t\t}else{\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = value.getData();\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"StandardExchangeRate[\"+index+\"]\").setData(rate.toFixed(6));\r\r\n\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setData(rate.toFixed(6));\r\r\n\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\nthis.setCommAccounts = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"commAccountList\" , []);\r\r\n\tfor (var i=0; i<this.context.options.CommAccountList.get(\"value\").length(); i++) {\r\r\n\t\tthis.context.binding.get(\"value\").get(index).get(\"commAccountList\").add({name:this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\") , value:this.context.options.CommAccountList.get(\"value\").get(i).get(\"accountNO\")});\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.ui.get(\"VerifyGLAccountBtn[\"+index+\"]\").setData(false);\r\r\n}\r\r\n\r\r\nthis.accountVis = function(value){\r\r\n\tvalue.setEnabled(false);\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(this.context.options.isChecker.get(\"value\") == true || this.ui.get(\"Owner1[\"+index+\"]\").getData() == \"\"){\r\r\n\t\tvalue.setEnabled(false);\r\r\n\t}else\r\r\n\t\tvalue.setEnabled(true);\r\r\n}\r\r\n\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"DebitedAmountinAccountCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n//--------------------------------------------------------------------------------Drop_2----------------------------------------------------------------------------------------\r\r\n//------------------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}"}]}, "hasDetails": true}, {"id": "64.0f40f56d-733f-4bd5-916c-92ae7dccbb10", "versionId": "af64a198-833a-4315-bc6b-a2056724e2f5", "name": "Contract Creation CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "contractCreation", "configOptions": ["contractCreationVis", "contractStage", "bpmRequestNumber", "currency", "nbeCollectableAmount", "todayDate"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.testSave = function(){\r\r\n\tvar saveBtn = bpmext.ui.getView(\"/DC_Templete1/saveState\");\r\r\n\tconsole.dir(saveBtn);\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\nthis.getViewByPath = function (path) {\r\r\n\treturn bpmext.ui.getView(path) || null;\r\r\n};\r\r\n\r\r\nthis.getFieldById = function (path) {\r\r\n\treturn this.ui.get(path) || null;\r\r\n};"}]}, "hasDetails": true}, {"id": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "versionId": "64571bed-66b5-41fa-87ad-9a9c8819f9f3", "name": "Contract Liquidation", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (this.context.options.isChecker.get(\"value\")) {\r\r\n\r\r\n\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(false);\r\r\n\tthis.ui.get(\"debitedAccountNo1\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(false);\r\r\n\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(false);\r\r\n}else{\r\r\n//\tthis.ui.get(\"informCADAboutTheCollection1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_LiquidationSummary\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedNostro\").setEnabled(true);\r\r\n//\tthis.ui.get(\"debitedAccountNo1\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_CreditedAccount\").setEnabled(true);\r\r\n//\tthis.ui.get(\"HL_DebitedAmount\").setEnabled(true);\r\r\n}", "bindingType": "odcRequest", "configOptions": ["glAccountVerified", "contractLiqVis", "customerAccounts", "exchangeRate", "<PERSON><PERSON><PERSON><PERSON>", "contractLiquidatedMSG", "verifyGLMsg"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//Validate Debited Nostro/Vostro Account\t\t\t\t\t\t\t\t\t\t\r\r\n this.valiadateAccCurrency = function (value)\r\r\n {\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo   = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\");\t\r\r\n\tvar currency    = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\tvar liqCurency  = this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"liqCurrency\");\t\r\r\n\t\r\r\n\tif (accountNo == value.getData() && currency != liqCurency) { \r\r\n//\t\t value.setData(\"\");\r\r\n//\t\t this.ui.get( \"debitedAccountNo1\").setValid(false,\"This account's currency must be the same as liquidation currency\");\t\r\r\n \tbreak;\r\r\n \t}\r\r\n\telse\r\r\n\t\t this.ui.get( \"debitedAccountNo1\").setValid(true);\t\r\r\n\t}//end of for\r\r\n}\r\r\n\r\r\n//Init Credit Account Data\r\r\nthis.setAccountInfo = function (value)\r\r\n {\r\r\n\r\r\n\tfor (var i=0; i<this.context.options.customerAccounts.get(\"value\").length(); i++) \r\r\n\t{\r\r\n\tvar accountNo= this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") ? this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountNO\") : \"\";\r\r\n\t\tif (accountNo == value.getData()) { \r\r\n\t\t\tvar branchCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"branchCode\");\r\r\n\t\t\tvar currency = this.context.options.customerAccounts.get(\"value\").get(i).get(\"currencyCode\");\r\r\n\t\t\tvar balance = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balance\");\r\r\n\t\t\tvar balanceSign = this.context.options.customerAccounts.get(\"value\").get(i).get(\"balanceType\");\r\r\n\t\t\tvar classCode = this.context.options.customerAccounts.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t \t\tclassCode= classCode? classCode.substring(0,1):\"\";\r\r\n\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"branchCode\", branchCode);\r\r\n\t \t\t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balance\", balance);\t \t\t\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"balanceSign\", balanceSign);\r\r\n//\t \t\tconsole.log(\"------------------classCode: \"+classCode);\r\r\n\t \t\tif(classCode == \"O\" || classCode == \"D\")\r\r\n\t \t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",true);\r\r\n\t\t\telse\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"isOverDraft\",false);\r\r\n\t\t\t\t\t\t\r\r\n//\t\t\tvar od= this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n//\t\t\tconsole.log(\"------------------is over draft : \"+od);\t\t \t\t\r\r\n\t \t\t//SET CURRENCY\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").set(\"currency\", {} );\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"name\", currency);\r\r\n\t \t\tthis.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"currency\").set(\"value\", currency);\r\r\n\t\t\t\r\r\n\t\t\t//SET EXCHANGE RATE\r\r\n//\t\t\tview.setNegoRate(currency);\t\r\r\n \t\t}\r\r\n\t}\r\r\n}\r\r\n //Set credited Amount\r\r\n this.setNegoRate = function(value)\r\r\n {\t\r\r\n\r\r\n//\tconsole.log(\"----------------------INSIDE setNegoRate\");\r\r\n  \tvar liqCurr   =  this.ui.get(\"LiquidateionCurrency\").getData()? this.ui.get(\"LiquidateionCurrency\").getData():\"\";  \t\t \t\r\r\n  \tvar accCurr   =  value.getData()? value.getData():\"\"; \t \t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr == accCurr ) ) { \r\r\n\t\tthis.ui.get(\"standardExRate\").setData(1);\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setData(1);\r\r\n  \t\tthis.ui.get(\"negotiatedExRate\").setEnabled(false); \t\r\r\n \t} \t\t\r\r\n\r\r\n \tif ( liqCurr != \"\" && accCurr != \"\" && ( liqCurr != accCurr ) ){\r\r\n//\t\t//call get exchange rate service\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n//\t\t console.log(\"---------- liqCurr != accCurr -------------------------\");\r\r\n//\t\t console.log(\"*******************************************************************\");\r\r\n\t\tthis.ui.get(\"negotiatedExRate\").setEnabled(true);\r\r\n//  \t\tvar inputCurrency={fromCurrency : liqCurr , toCurrency : accCurr};\r\r\n//  \t\tconsole.log(\"----------inputCurrency \"+inputCurrency);\r\r\n  \t\t\r\r\n//  \t\tvar parsedInputCurrency =  JSON.stringify(inputCurrency);\r\r\n//  \t\tconsole.log(\"----------parsedInputCurrency=   \"+parsedInputCurrency);\r\r\n//     \tconsole.log(\"*******************************************************************\");\r\r\n//\t\talert(parsedInputCurrency);\r\r\n//\t\tthis.ui.get(\"SC_getExRate\").execute(parsedInputCurrency);\r\r\n\t\t\r\r\n\t\tconcatedCurrency = {ccFrom : liqCurr , ccTo : accCurr , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\tinputCurr = JSON.stringify(concatedCurrency);\r\r\n\r\r\n\t\tthis.ui.get(\"GetExchangeRate\").execute(inputCurr);\r\r\n \t}\t\t\t\r\r\n}\r\r\n//SET Credited Amount From Exchange Service\r\r\n this.SetCreditAmountDetails = function(value){\r\r\n\r\r\n\tconsole.log(\"----------------------INSIDE SetCreditAmountDetails\");\r\r\n\tvar exRate = this.context.options.exchangeRate.get(\"value\");\r\r\n\r\r\n\tconsole.log(\"--------------- rate \"+ exRate);\r\r\n\tconsole.log(\"---------------ExRate fixed =  \"+ exRate.toFixed(6));\r\r\n\t\r\r\n\tthis.ui.get(\"standardExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExRate\").setData(Number(exRate.toFixed(6)));\r\r\n\t\r\r\n\r\r\n } \r\r\n//calculate credited amount in cuurency\r\r\nthis.setAmountInCurrency = function(){\r\r\n\tvar liqAmount  =  this.ui.get(\"LiquidationAmount\").getData()? this.ui.get(\"LiquidationAmount\").getData():0;\r\r\n\tvar negoExRate =  this.ui.get(\"negotiatedExRate\").getData()? this.ui.get(\"negotiatedExRate\").getData():0;\r\r\n\t\r\r\n\tif( liqAmount > 0   &&   negoExRate > 0 )\r\r\n\t\tthis.ui.get(\"creditedAmountinAccCurrency\").setData(liqAmount * negoExRate);\r\r\n}\r\r\n\r\r\n//validate overDraft\r\r\nthis.validateOverDraft = function () {\r\r\n\tvar AccBalance          =  this.ui.get(\"AccountBalance\").getData()? this.ui.get(\"AccountBalance\").getData() : 0;\r\r\n\tvar amountinAccCurrency =  this.ui.get(\"creditedAmountinAccCurrency\").getData()? this.ui.get(\"creditedAmountinAccCurrency\").getData() : 0;\r\r\n\tvar overDraft           =  this.context.binding.get(\"value\").get(\"ContractLiquidation\").get(\"creditedAccount\").get(\"isOverDraft\");\r\r\n\t\r\r\n\tif(amountinAccCurrency > AccBalance){\r\r\n\t\tif (overDraft) \r\r\n\t\t\t{\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"WARNING: Amount in account currency should be less than Account Balance\");\r\r\n\t\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(false,\"ERROR: Amount in Account Currency > Account Balance and Account Type is not Overdraft\");\r\r\n\t\t\t}\r\r\n\t\r\r\n\t}else{\r\r\n\t\tthis.ui.get( \"creditedAmountinAccCurrency\").setValid(true);\r\r\n\t}//end of if\r\r\n\t\r\r\n}\r\r\n\r\r\n//On load of account class - set vis according to account class (customer & gl)\r\r\nthis.accountNumVis = function (value) {\r\r\n    var accountClass = value.getData().name;\r\r\n    \r\r\n    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n        if (accountClass == \"Customer Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true,true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true,true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n\r\r\n        } else if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBranchCode\").setEnabled(true);\r\r\n            this.ui.get(\"accountCurrency\").setEnabled(true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false,true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false,true);\r\r\n\r\r\n        }\r\r\n    } else {\r\r\n        this.ui.get(\"AccountBranchCode\").setEnabled(false);\r\r\n        this.ui.get(\"accountCurrency\").setEnabled(false);\r\r\n        \r\r\n        if (accountClass == \"GL Account\") {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(true, true);\r\r\n            this.ui.get(\"glAccountNo\").setEnabled(false);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n   \r\r\n            this.ui.get(\"customerAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"AccountBalance\").setVisible(false, true);\r\r\n            this.ui.get(\"BalanceSign\").setVisible(false, true);\r\r\n            \r\r\n        } else {\r\r\n            this.ui.get(\"glAccountNo\").setVisible(false, true);\r\r\n            this.ui.get(\"verifyGlAccountBtn\").setVisible(false, true);\r\r\n\r\r\n            this.ui.get(\"customerAccountNo\").setVisible(true, true);\r\r\n\t\tthis.ui.get(\"customerAccountNo\").setEnabled(false);\r\r\n\t\t\r\r\n            this.ui.get(\"AccountBalance\").setVisible(true, true);\r\r\n            this.ui.get(\"AccountBalance\").setEnabled(false);\r\r\n\r\r\n            this.ui.get(\"BalanceSign\").setVisible(true, true);\r\r\n            this.ui.get(\"BalanceSign\").setEnabled(false);\r\r\n        }\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of account class - reset data before choosing another account\r\r\nthis.resetAccountInfo = function (value){\r\r\n    this.ui.get(\"glAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"customerAccountNo\").setData(\"\");\r\r\n    this.ui.get(\"AccountBalance\").setData(\"\");\r\r\n    this.ui.get(\"AccountBranchCode\").setData(\"\");\r\r\n    this.ui.get(\"BalanceSign\").setData(\"\");\r\r\n    this.ui.get(\"accountCurrency\").setData(\"\");\r\r\n    \r\r\n    this.ui.get(\"standardExRate\").setData(\"\");\r\r\n    this.ui.get(\"creditedAmountinAccCurrency\").setData(\"\");\r\r\n    this.ui.get(\"negotiatedExRate\").setData(\"\");\r\r\n    this.ui.get(\"verifiedText\").setData(\"\");\r\r\n    \r\r\n    this.accountNumVis(value);\r\r\n}"}]}, "hasDetails": true}, {"id": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "versionId": "b2affbad-b447-40bf-9336-acb174aa96ad", "name": "Customer Information cv", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "customerInformation", "configOptions": ["customerInfoVIS", "listsVIS"]}, "hasDetails": true}, {"id": "64.5df8245e-3f18-41b6-8394-548397e4652f", "versionId": "385262a6-4cc6-44c7-aa6d-6db9baf128aa", "name": "DC History", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "stepLog"}, "hasDetails": true}, {"id": "64.5e7389cc-b0e4-431e-a356-479a9cca165a", "versionId": "3f5595f6-740a-432d-af7f-278d37e4845c", "name": "DC start request template", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "odcRequest", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "Untitled"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.showApprovals = function  () {\r\r\n//\tif (this.context.options.hasApprovals.get(\"value\")) {\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(true,true);\r\r\n//\t\tif (this.context.options.approvalsReadOnly.get(\"value\")) {\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(false);\r\r\n//\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"approvalsLayout\").setEnabled(true);\r\r\n//\t\t}\r\r\n//\r\r\n//\t}else{\r\r\n//\r\r\n//\t\tthis.ui.get(\"approvalsLayout\").setVisible(false,true);\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//\r\r\n//this.showReturnReason = function  () {\r\r\n//\tif (this.context.options.hasReturnReason.get(\"value\") && ((this.context.options.selectedAction.get(\"value\") == \"Return To Trade FO\") || (this.context.options.selectedAction.get(\"value\") == \"Return to Initiator\") || (this.context.options.selectedAction.get(\"value\") == \"Return To Maker\"))) {\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(true,true);\r\r\n//\t}else{\r\r\n//\t\tthis.ui.get(\"ReturnReason\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n//\r\r\n//this.showAction = function  () {\r\r\n//\tif (this.context.binding.get(\"value\").get(\"subStatus\") == \"\") {\r\r\n//\t\tthis.ui.get(\"Single_Select1\").setVisible(false,true);\r\r\n//\t\tthis.ui.get(\"Text1\").setVisible(false,true);\r\r\n//\t}\r\r\n//}\r\r\n\r\r\n//updateActionList = function  () {\r\r\n//\r\r\n//\r\r\n//console.log(this.context.options.errorPanelVIS.get(\"value\"));\r\r\n//\r\r\n////var compApp = ;\r\r\n//\r\r\n////\tif(this.context.options.complianceApproval.get(\"value\"))\r\r\n////\t{\r\r\n////\t\tthis.context.options.action.set(\"value\", [\"123\",\"34\"]);\r\r\n////\t}\r\r\n//}"}]}, "hasDetails": true}, {"id": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "versionId": "a4863016-bca6-415f-9407-7cd45040cbde", "name": "DC Templete", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "appinfo", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "actionConditions", "returnReasonVIS", "tradeFoComment", "tradeFoCommentVis", "exeHubMkrComment", "exeHubMkrCommentVis", "compcheckerComment", "compcheckerCommentVIS", "disableSubmit"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": "/* Main container for tabs */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n    display: flex;\r\r\n    flex-wrap: wrap; /* Allow tabs to wrap onto multiple lines */\r\r\n    align-items: center;\r\r\n    padding: 5px;\r\r\n    border-bottom: 1px solid #ddd;\r\r\n    background-color: #f8f9fa;\r\r\n    list-style-type: none; /* Remove default list styling */\r\r\n    margin: 0;\r\r\n}\r\r\n\r\r\n/* Style each tab */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n    /*margin-right: 5px;*/\r\r\n    margin-bottom: 2px;\r\r\n    list-style-type: none; /* Ensure no bullets */\r\r\n    white-space: nowrap; /* Prevent text wrapping inside each tab */\r\r\n}\r\r\n\r\r\n/* Tab anchor styling */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n    display: block;\r\r\n    padding: 5px 5px;\r\r\n    background-color: #ffffff;\r\r\n    border: 1px solid #ddd;\r\r\n    border-radius: 3px;\r\r\n    color: #007bff;\r\r\n    text-decoration: none;\r\r\n    transition: background-color 0.3s ease, color 0.3s ease;\r\r\n    height:26px;\r\r\n}\r\r\n\r\r\n/* Active tab and hover state */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.active > a,\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a:hover {\r\r\n    background-color: #007bff;\r\r\n    color: #ffffff;\r\r\n}\r\r\n\r\r\n/* Hide the secondary dropdown menu if it exists */\r\r\nul.nav.nav-tabs-mnu.nav-tabs-simple.tabs-primary {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Make sure the first <ul> doesn't get altered by dropdown behavior */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-toggle {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-menu {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Responsive adjustments to ensure tabs wrap correctly */\r\r\n@media (max-width: 768px) {\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {\r\r\n        flex-direction: row;\r\r\n        flex-wrap: wrap; /* Ensure tabs wrap on small screens */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li {\r\r\n        margin-right: 0;\r\r\n        white-space: normal; /* Allow text to wrap within tabs */\r\r\n    }\r\r\n\r\r\n    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li > a {\r\r\n        text-align: center; /* Center text within tabs */\r\r\n    }\r\r\n}\r\r\n/* Ensure tabs don't leave white space when removed */\r\r\nul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs > li.hidden {\r\r\n    display: none !important;\r\r\n}\r\r\n\r\r\n/* Ensure all tabs are displayed */\r\r\n.nav-tabs > li {\r\r\n    display: block !important; /* Override any display:none */\r\r\n    /*visibility: visible !important; /* Ensure visibility */\r\r\n}"}]}, "hasDetails": true}, {"id": "64.77f06102-fddf-42a1-9b6b-6580b729871c", "versionId": "2c6b6d88-a3e8-4b28-9aa5-e87623af52eb", "name": "DC Templete 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "require([\r\r\n    \"dojo/_base/lang\", \r\r\n    \"com.ibm.bpm.coach/engine\"\r\r\n], function (lang, engine) {\r\r\n\r\r\n    // Function to get the coach data\r\r\n    function getCoachData() {\r\r\n        var coachData = null;\r\r\n\t\r\r\n\t  console.log(\"<<<<ENGINE>>>\");\r\r\n        console.dir(engine.GlobalAssets);\r\r\n        \r\r\n        console.log(\"\");\r\r\n        if (engine && engine.coachView) {\r\r\n            coachData = engine.coachView.getCoachData();\r\r\n        }\r\r\n\r\r\n        if (!coachData) {\r\r\n            console.log(\"Coach data not found\");\r\r\n            return null;\r\r\n        }\r\r\n\r\r\n        return coachData;\r\r\n    }\r\r\n\r\r\n    // Example usage\r\r\n    var data = getCoachData();\r\r\n    console.log(\"Coach Data:\", data);\r\r\n});", "bindingType": "appinfo", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "data", "conditions"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.setActions = function () {\r\r\nvar complianceApproval= true;\r\r\n//\tthis.ui.get(\"retrieveRequest\").setData(false);\r\r\n\tthis.ui.get(\"\")\r\r\n\r\r\n}"}]}, "hasDetails": true}, {"id": "64.1d99aba8-195f-4eee-ba8c-a47926bc21e2", "versionId": "1a1713a3-de37-49e2-bdcb-d84680269095", "name": "Document Generation CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "documentGeneration", "configOptions": ["requestTypeVIS", "regeneratedRemittanceLetterTitleVIS", "deliveryTerms", "paymentTerms", "specialInstructions", "instructions", "requestType", "remittanceLetter<PERSON><PERSON>on", "barcode", "generationStatus", "documentGenerationVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.EngLetterRegExp = function(char)\r\r\n//{\r\r\n//console.log(\"inside reg exp fn\");\r\r\n//console.log(char +\"chaaracterrr\");\r\r\n//\t//if(this.context.options.requestType.get(\"value\") == \"create\" ||this.context.options.requestType.get(\"value\") == \"amend\" ||this.context.options.requestType.get(\"value\") == \"recreate\")\r\r\n//\t\r\r\n//\t\tvar regex = \"/^[A-Za-z][A-Za-z0-9]*$/\";\r\r\n////\t\tchar.match(regex);\r\r\n//\t\tif( regex.test(char) == true)\r\r\n//\t\t{\r\r\n//\t\t\tthis.ui.get(\"customerName\").setValid(true);\r\r\n//\t\t\tconsole.log(\"match\");\r\r\n//\t\t}\t\r\r\n//\t\telse\r\r\n//\t\t{\r\r\n//\t\t\tthis.ui.get(\"customerName\").setValid(false , \"This field accepts alpanumeric only\");\r\r\n//\t\t\tconsole.log(\"doesnt match\");\r\r\n//\t\t}\t\t\t\r\r\n//}\r\r\nthis.ShowRegeneratedRemittanceLetter = function()\r\r\n{\r\r\n\t//if(this.context.binding.get(\"value\").get(\"regenerateRemLetter\") == true\r\r\n\tif(this.context.binding.get(\"value\").get(\"regenerateRemLetter\") == true && this.context.binding.get(\"value\").get(\"regenerateRemLetterOption\").get(\"value\") == \"other\")\r\r\n\t{\t\r\r\n\t\tthis.context.options.regeneratedRemittanceLetterTitleVIS.set(\"value\", \"EDITABLE\");\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\tthis.context.options.regeneratedRemittanceLetterTitleVIS.set(\"value\", \"NONE\");\r\r\n\t}\r\r\n}\r\r\n////////////////////////////////////"}]}, "hasDetails": true}, {"id": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "versionId": "f28116da-b2e5-4a00-a7a7-1562a7ca4ae3", "name": "FC Collections CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "fcCollections", "configOptions": ["FCVIS", "retrieveBtnVis", "customerCif", "collectionCurrencyVIS", "negotiatedExchangeRateVIS", "requestCurrency", "addBtnVIS", "activityType"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//amountAllocatedForCurrentRequest * negotiatedExchangeRate\r\r\n //allocatedAmountInRequestCurrency\r\r\n //Function to calculate allocated amount in request currency\r\r\n// this.calculateAllocatedAmount = function(){\r\r\n//\tvar rate = this.context.binding.get(\"value\").get(\"negotiatedExchangeRate\");\r\r\n//\tconsole.log(\"Negotiated Exchange Rate\");\r\r\n//\tvar amount ;\r\r\n//\t\t\r\r\n//\tvar result = rate * amount;\r\r\n//}\r\r\n\r\r\n\r\r\nthis.negotiatedVIS = function  (me) {\r\r\n\tif (me.getData() == 1) {\r\r\n\t\tthis.context.options.negotiatedExchangeRateVIS.set(\"value\", \"READONLY\");\r\r\n\t}else{\r\r\n\t\tthis.context.options.negotiatedExchangeRateVIS.set(\"value\", \"DEFAULT\");\r\r\n\t}\r\r\n} \r\r\n\r\r\n\r\r\nthis.showRetrieveTransactionBtn = function(){\r\r\n//console.log(\"///////////////////\"+this.context.options.activityType.get(\"value\"));\r\r\nif(this.context.options.activityType.get(\"value\") == \"write\")\r\r\n{\r\r\n\ttry {\r\r\n\t\t\r\r\n\t\r\r\n\t\tif(this.context.binding.get(\"value\").get(\"currency\").get(\"name\") == \"\" || this.context.binding.get(\"value\").get(\"fromDate\") == null ||\r\r\n\t\tthis.context.binding.get(\"value\").get(\"ToDate\") == null || this.context.binding.get(\"value\").get(\"accountNo\").get(\"value\") == \"\") \r\r\n\t\t{\r\r\n\t\t \tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t\t \tconsole.log(\"not visible\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.ui.get(\"Button1\").setVisible(true,true);\t\r\r\n\t\t\tconsole.log(\" visible\");\r\r\n\t\t}\r\r\n\t} catch (err) {\r\r\n\t\tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n\telse{\r\r\n\t\t\tthis.ui.get(\"Button1\").setVisible(false,true);\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}, {"id": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "versionId": "51456dd2-66ca-4a40-8805-14a62712aefd", "name": "Financial Details Branch", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetails", "configOptions": ["financialDetailsCVVis", "currencyDocAmountVIS", "today"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.FCVIS= function()\r\r\n{\r\r\n\tif(this.context.binding.get(\"value\").get(\"amountAdvanced\") > 0)\r\r\n\t\tthis.ui.get(\"fcPanel\").setVisible(true);\r\r\n\telse\r\r\n\t\tthis.ui.get(\"fcPanel\").setVisible(false,true);\r\r\n\r\r\n}"}]}, "hasDetails": true}, {"id": "64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1", "versionId": "3afd7001-91c6-496d-bb5a-cb784ede3ad6", "name": "Financial Details Trade FO CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetailsFO", "configOptions": ["financialDetailsFOVis", "requestType", "act3VIS", "multiTenorDatesVIS", "todayDate", "documentAmount", "amountAdvanced"], "inlineScripts": []}, "hasDetails": true}, {"id": "64.af339c2a-1b07-4416-a616-ec3121b73970", "versionId": "ee583e61-8bd1-4df8-a72c-7697f5b2d063", "name": "Financial Details Trade FO CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "financialDetailsFO", "configOptions": ["financialDetailsFOVis", "requestType", "act3VIS", "multiTenorDatesVIS", "todayDate", "documentAmount", "amountAdvanced", "columns"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.seColumnVis = function(me) {\r\r\n\tvar tableColumn=[\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"V\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false},\r\r\n\t    {renderAs:\"V\",visibility:\"N\",sortable:false,options:\"\",css:\"\",width:\"\",label:\"\",showLabel:false,rearrangeColumn:false}\r\r\n\t];\r\r\n\t\r\r\nvar columnss2 = [\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 0,\r\r\n        type: \"Date_Time_Picker\",\r\r\n        label: \"Installment Date التاريخ\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 1,\r\r\n        type: \"Date_Time_Picker\",\r\r\n        label: \"Mat Date\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 2,\r\r\n        type: \"Integer\",\r\r\n        label: \"Tenor Days\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 3,\r\r\n        type: \"Decimal\",\r\r\n        label: \"Installment Amount المبلغ\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    },\r\r\n    {\r\r\n        renderAs: \"V\",\r\r\n        columnOrder: 4,\r\r\n        type: \"Decimal\",\r\r\n        label: \"Rebate\",\r\r\n        visibility: \"V\",\r\r\n        css: \"\",\r\r\n        width: \"\"\r\r\n    }\r\r\n]\r\r\n\t\r\r\n//\tthis.context.options.columns.set(\"value\", tableColumn);\r\r\n\tme.setColumns(columnss2);\r\r\n//\tconsole.log( JSON.stringify(me.getColumns()));\r\r\n//\tme.refresh();\r\r\n\t\r\r\n}"}]}, "hasDetails": true}, {"id": "64.63a48491-564d-4a28-972c-df08820c76ff", "versionId": "9038759a-d909-4706-88ae-ae7a4ed18868", "name": "functionSearch", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "// Constants\r\r\n\r\r\nconst EVENT_TYPES = [\r\r\n\t\"eventON_LOAD\",\r\r\n\t\"eventON_CHANGE\",\r\r\n\t\"eventON_INPUT\",\r\r\n\t\"eventON_BLUR\",\r\r\n\t\"eventON_FOCUS\",\r\r\n\t\"eventON_SVCERROR\",\r\r\n\t\"eventON_SVCITEMS\",\r\r\n\t\"eventON_BEFORE_SHOW_DAY\"\r\r\n];\r\r\nconst SEARCH_OPTIONS = {\r\r\n\tALL: \"all\",\r\r\n\tFUNCTION_NAME: \"funName\",\r\r\n\tNOT_USED: \"notUsed\"\r\r\n};\r\r\nconst SYSTEM_PROPERTIES = ['constructor', 'prototype', '__proto__'];\r\r\n\r\r\n/**\r\r\n * Builds a map of control labels to their bound object values for specified events\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {Array<string>} eventTypes - Array of event types to check for\r\r\n * @returns {Map<string, Array<{eventType: string, value: string}>} Map of control labels to arrays of event type and bound object value pairs\r\r\n */\r\r\nfunction buildBoundObjectMap(viewName, eventTypes) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst boundObjectMap = new Map();\r\r\n\tconsole.dir(view);\r\r\n\tif (!view || !view._bpmextViewNode || !view._bpmextViewNode._children) {\r\r\n\t\tconsole.error(\"View or view children not found\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\r\r\n\tconst children = view._bpmextViewNode._children;\r\r\n\tif (!children || Object.keys(children).length === 0) {\r\r\n\t\tconsole.error(\"No children found in the view\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\t// Iterate through all child controls\r\r\n\tfor (const controlType in children) {\r\r\n\t\tif (Object.prototype.hasOwnProperty.call(children, controlType)) {\r\r\n\t\t\tconst controls = children[controlType];\r\r\n\r\r\n\t\t\t// Process each control of this type\r\r\n\t\t\tfor (const control of controls) {\r\r\n\t\t\t\tif (!control._data) continue;\r\r\n\r\r\n\t\t\t\tconst label = control._data.getLabel ? control._data.getLabel() : controlType;\r\r\n\r\r\n\t\t\t\t// Check for each event type\r\r\n\t\t\t\tfor (const eventType of eventTypes) {\r\r\n\t\t\t\t\tif (control._data.context && control._data.context.options && control._data.context.options[eventType] && control._data.context.options[eventType].boundObject && control._data.context.options[eventType].boundObject.value) {\r\r\n\t\t\t\t\t\t// Get the bound object value\r\r\n\t\t\t\t\t\tconst value = control._data.context.options[eventType].boundObject.value;\r\r\n\r\r\n\t\t\t\t\t\t// Initialize array for this label if it doesn't exist\r\r\n\t\t\t\t\t\tif (!boundObjectMap.has(label)) {\r\r\n\t\t\t\t\t\t\tboundObjectMap.set(label, []);\r\r\n\t\t\t\t\t\t}\r\r\n\r\r\n\t\t\t\t\t\t// Add event type and value to the array for this label\r\r\n\t\t\t\t\t\tboundObjectMap.get(label).push({\r\r\n\t\t\t\t\t\t\teventType: eventType,\r\r\n\t\t\t\t\t\t\tvalue: value,\r\r\n\t\t\t\t\t\t});\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn boundObjectMap;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for a string in the bound object values and returns matching control labels\r\r\n * @param {Map<string, Array<{eventType: string, value: string}>} boundObjectMap - Map of control labels to arrays of event type and bound object value pairs\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchBoundObjects(boundObjectMap, searchString) {\r\r\n\tif (!searchString || typeof searchString !== \"string\") {\r\r\n\t\treturn [];\r\r\n\t}\r\r\n\r\r\n\tconst matchingResults = [];\r\r\n\r\r\n\t// Convert search string to lowercase for case-insensitive search\r\r\n\tconst searchLower = searchString.toLowerCase();\r\r\n\r\r\n\t// Search through all values in the map\r\r\n\tboundObjectMap.forEach((eventArray, label) => {\r\r\n\t\t// Find matching events for this control\r\r\n\t\tconst matchingEvents = eventArray.filter((event) => typeof event.value === \"string\" && event.value.toLowerCase().includes(searchLower));\r\r\n\r\r\n\t\t// If we found any matches, add this control to the results\r\r\n\t\tif (matchingEvents.length > 0) {\r\r\n\t\t\tmatchingResults.push({\r\r\n\t\t\t\tlabel: label,\r\r\n\t\t\t\tevents: matchingEvents,\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn matchingResults;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Main function to search for bound objects in a view\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchFunctionsInView(viewName, searchString) {\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\treturn searchBoundObjects(boundObjectMap, searchString);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets just the labels of controls that match the search string\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<string>} Array of distinct control labels that match the search\r\r\n */\r\r\nfunction getMatchingControlLabels(viewName, searchString) {\r\r\n\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\treturn results.map((result) => result.label);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats search results as an HTML table\r\r\n * @param {Array<{label: string, events: Array<{eventType: string, value: string}>}>} results - Search results from searchFunctionsInView\r\r\n * @param {string} searchString - The search string used (for highlighting)\r\r\n * @returns {string} HTML table representation of the search results\r\r\n */\r\r\nfunction formatResultsAsHtml(results, searchString) {\r\r\n\tif (!results || results.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No matching controls found</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn text.replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Highlight the search string in the value\r\r\n\tfunction highlightSearchString(text, search) {\r\r\n\t\tif (!search || !text.includes(search)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(search), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with unified design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each result\r\r\n\tresults.forEach((result) => {\r\r\n\t\tresult.events.forEach((event, index) => {\r\r\n\t\t\thtml += `\r\r\n            <tr>\r\r\n                <td>${escapeHtml(searchString)}</td>\r\r\n                <td>${index === 0 ? escapeHtml(result.label) : \"\"}</td>\r\r\n                <td class=\"event-type\">${escapeHtml(event.eventType.replace(\"event\", \"\"))}</td>\r\r\n                <td>${highlightSearchString(event.value, searchString)}</td>\r\r\n            </tr>\r\r\n            `;\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Checks if a function name is a system function\r\r\n * @param {string} functionName - The function name to check\r\r\n * @returns {boolean} True if it's a system function\r\r\n */\r\r\nfunction isSystemFunction(functionName) {\r\r\n\treturn functionName.startsWith('event') ||\r\r\n\t\t   functionName.includes('ON_') ||\r\r\n\t\t   functionName.startsWith('get') ||\r\r\n\t\t   functionName.startsWith('set') ||\r\r\n\t\t   functionName.startsWith('_');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets all user-defined functions from a view\r\r\n * @param {Object} view - The view object\r\r\n * @returns {Array<string>} Array of user function names\r\r\n */\r\r\nfunction getUserFunctions(view) {\r\r\n\tconst userFunctions = [];\r\r\n\tconst ownPropertyNames = Object.getOwnPropertyNames(view);\r\r\n\r\r\n\tfor (const key of ownPropertyNames) {\r\r\n\t\ttry {\r\r\n\t\t\t// Skip system properties\r\r\n\t\t\tif (SYSTEM_PROPERTIES.includes(key)) {\r\r\n\t\t\t\tcontinue;\r\r\n\t\t\t}\r\r\n\r\r\n\t\t\tconst value = view[key];\r\r\n\r\r\n\t\t\t// Check if it's a user-defined function\r\r\n\t\t\tif (typeof value === 'function' && !isSystemFunction(key)) {\r\r\n\t\t\t\tuserFunctions.push(key);\r\r\n\t\t\t}\r\r\n\t\t} catch (e) {\r\r\n\t\t\tconsole.log(`Error accessing property ${key}: ${e.message}`);\r\r\n\t\t}\r\r\n\t}\r\r\n\r\r\n\treturn userFunctions;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Extracts all user-defined function names from a view and finds their usage in controls\r\r\n * @param {string} viewName - The name of the view to extract functions from\r\r\n * @returns {Array<{name: string, controller: string, eventType: string, context: string}>} Array of function details found in the view\r\r\n */\r\r\nfunction extractFunctionNamesFromView(viewName) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst functionDetails = [];\r\r\n\r\r\n\tif (!view) {\r\r\n\t\tconsole.error(\"View not found\");\r\r\n\t\treturn functionDetails;\r\r\n\t}\r\r\n\r\r\n\tconsole.dir(view); // Debug logging\r\r\n\r\r\n\t// Get all user-defined functions\r\r\n\tconst userFunctions = getUserFunctions(view);\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\r\r\n\t// Find usage for each function\r\r\n\tuserFunctions.forEach(functionName => {\r\r\n\t\tconst usageResults = searchBoundObjects(boundObjectMap, functionName);\r\r\n\r\r\n\t\tif (usageResults.length > 0) {\r\r\n\t\t\t// Function is used in controls\r\r\n\t\t\tusageResults.forEach(result => {\r\r\n\t\t\t\tresult.events.forEach(event => {\r\r\n\t\t\t\t\tfunctionDetails.push({\r\r\n\t\t\t\t\t\tname: functionName,\r\r\n\t\t\t\t\t\tcontroller: result.label,\r\r\n\t\t\t\t\t\teventType: event.eventType.replace(\"event\", \"\"),\r\r\n\t\t\t\t\t\tcontext: event.value || functionName\r\r\n\t\t\t\t\t});\r\r\n\t\t\t\t});\r\r\n\t\t\t});\r\r\n\t\t} else {\r\r\n\t\t\t// Function exists but not used\r\r\n\t\t\tfunctionDetails.push({\r\r\n\t\t\t\tname: functionName,\r\r\n\t\t\t\tcontroller: 'Not Used',\r\r\n\t\t\t\teventType: 'Available',\r\r\n\t\t\t\tcontext: 'Function not bound to any control'\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn functionDetails;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Filters function details to show only unused functions\r\r\n * @param {Array<{name: string, controller: string, eventType: string}>} functionDetails - All function details\r\r\n * @returns {Array<{name: string, controller: string, eventType: string}>} Filtered function details\r\r\n */\r\r\nfunction getNotUsedFunctions(functionDetails) {\r\r\n\treturn functionDetails.filter(detail => detail.controller === 'Not Used');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats function details as an HTML table with grouping\r\r\n * @param {Array<{name: string, controller: string, eventType: string, context: string}>} functionDetails - Array of function details\r\r\n * @returns {string} HTML table representation of the function details\r\r\n */\r\r\nfunction formatFunctionNamesAsHtml(functionDetails) {\r\r\n\tif (!functionDetails || functionDetails.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No functions found in the view</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn String(text).replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Group by function name first, then by controller\r\r\n\tconst groupedByFunction = {};\r\r\n\tfunctionDetails.forEach(detail => {\r\r\n\t\tif (!groupedByFunction[detail.name]) {\r\r\n\t\t\tgroupedByFunction[detail.name] = {};\r\r\n\t\t}\r\r\n\t\tif (!groupedByFunction[detail.name][detail.controller]) {\r\r\n\t\t\tgroupedByFunction[detail.name][detail.controller] = [];\r\r\n\t\t}\r\r\n\t\tgroupedByFunction[detail.name][detail.controller].push({\r\r\n\t\t\teventType: detail.eventType,\r\r\n\t\t\tcontext: detail.context\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\t// Sort function names alphabetically\r\r\n\tconst sortedFunctionNames = Object.keys(groupedByFunction).sort((a, b) =>\r\r\n\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t);\r\r\n\r\r\n\t// Highlight function name in context\r\r\n\tfunction highlightFunctionInContext(text, functionName) {\r\r\n\t\tif (!functionName || !text.includes(functionName)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(functionName), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with simple, clean design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each function group\r\r\n\tsortedFunctionNames.forEach((functionName) => {\r\r\n\t\tconst controllers = groupedByFunction[functionName];\r\r\n\t\tconst sortedControllers = Object.keys(controllers).sort((a, b) =>\r\r\n\t\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t\t);\r\r\n\r\r\n\t\tlet isFirstRowForFunction = true;\r\r\n\r\r\n\t\tsortedControllers.forEach((controller) => {\r\r\n\t\t\tconst eventDetails = controllers[controller];\r\r\n\t\t\tlet isFirstRowForController = true;\r\r\n\r\r\n\t\t\teventDetails.forEach((detail) => {\r\r\n\t\t\t\tconst functionGroupClass = isFirstRowForFunction ? 'function-group' : '';\r\r\n\t\t\t\tconst controllerGroupClass = isFirstRowForController ? 'controller-group' : '';\r\r\n\r\r\n\t\t\t\thtml += `\r\r\n                <tr class=\"${functionGroupClass}\">\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForFunction ? escapeHtml(functionName) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForController ? escapeHtml(controller) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"event-type\">${escapeHtml(detail.eventType)}</td>\r\r\n                    <td>${highlightFunctionInContext(detail.context, functionName)}</td>\r\r\n                </tr>\r\r\n                `;\r\r\n\r\r\n\t\t\t\tisFirstRowForFunction = false;\r\r\n\t\t\t\tisFirstRowForController = false;\r\r\n\t\t\t});\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for functions in a view and returns the results as HTML\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchFor - Search option (all, funName, notUsed)\r\r\n * @param {string} searchString - String to search for in bound object values (used when searchFor is funName)\r\r\n * @returns {string} HTML representation of the search results\r\r\n */\r\r\nfunction searchFunctionsAndFormatAsHtml(viewName, searchFor, searchString) {\r\r\n\tswitch (searchFor) {\r\r\n\t\tcase SEARCH_OPTIONS.ALL:\r\r\n\t\t\t// Show all user-defined functions\r\r\n\t\t\tconst allFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(allFunctionDetails);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.FUNCTION_NAME:\r\r\n\t\t\t// Search for specific function name\r\r\n\t\t\tif (!searchString) {\r\r\n\t\t\t\treturn \"<div class='no-results'>Please enter a function name to search for</div>\";\r\r\n\t\t\t}\r\r\n\t\t\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\t\t\treturn formatResultsAsHtml(results, searchString);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.NOT_USED:\r\r\n\t\t\t// Show only unused functions\r\r\n\t\t\tconst allFunctions = extractFunctionNamesFromView(viewName);\r\r\n\t\t\tconst notUsedFunctions = getNotUsedFunctions(allFunctions);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(notUsedFunctions);\r\r\n\r\r\n\t\tdefault:\r\r\n\t\t\t// Default to showing all functions\r\r\n\t\t\tconst defaultFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(defaultFunctionDetails);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.executeSearch = function () {\r\r\n\tvar viewName = this.ui.get(\"viewName\").getData();\r\r\n\tif (!viewName) {\r\r\n\t\tconsole.error(\"View name is empty\");\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tvar searchForVal = this.ui.get(\"searchFor\").getData();\r\r\n\tvar funName = this.ui.get(\"functionName\").getData();\r\r\n\r\r\n\tconsole.log(`Search type: ${searchForVal}, Function name: ${funName || 'N/A'}, View: ${viewName}`);\r\r\n\r\r\n\tvar htmlResults = searchFunctionsAndFormatAsHtml(viewName, searchForVal, funName);\r\r\n\tif (!!htmlResults) {\r\r\n\t\tthis.ui.get(\"result\").setData(htmlResults);\r\r\n\t}\r\r\n};"}]}, "hasDetails": true}, {"id": "64.c50b6ab8-5869-4c25-8bc0-97a15f4d7c4a", "versionId": "50d524c6-1b7b-4b28-a77e-5a644b96a625", "name": "IDC Party", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "", "bindingType": "input", "configOptions": ["partyTypeList", "warningMess", "BeneficiaryDetails", "partyIndex", "customerFullDetails", "selectedBIC", "customerCIF", "accounteeCIF", "caseCIF", "addressBICList", "selectedCIF", "alertMessage", "errorVis", "draweeCIF", "requestType", "<PERSON><PERSON><PERSON><PERSON>", "requestState", "columns", "addParty", "deleteParty", "deletedCIF", "appID"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "var isAmndFinal = false;\r\r\nvar length = 0;\r\r\nthis.setColumnsVis = function () {\r\r\n\tvar vis = \"V\";\r\r\n\tif ((this.context.options.requestType.get(\"value\") == \"IDC Amendment\" || this.context.options.requestType.get(\"value\") == \"IDC Completion\") && !this.context.options.isChecker.get(\"value\")) {\r\r\n        this.context.options.addParty.set(\"value\", false);\r\r\n        this.context.options.deleteParty.set(\"value\", false);\r\r\n        if (this.context.options.requestState.get(\"value\") == \"Initial\"){\r\r\n            vis = \"R\";\r\r\n            isAmndFinal = false;\r\r\n        }else if (this.context.options.requestState.get(\"value\") == \"Final\"){\r\r\n        \tvis = \"R\";\r\r\n            length = this.context.binding.get(\"value\").length();\r\r\n            if (length < 5) {\r\r\n                this.context.options.addParty.set(\"value\", true);\r\r\n                isAmndFinal = true;\r\r\n            }    \r\r\n        }\r\r\n    }else{\r\r\n        vis = \"V\";\r\r\n        this.context.options.addParty.set(\"value\", true);\r\r\n        this.context.options.deleteParty.set(\"value\", true);\r\r\n        isAmndFinal = false;\r\r\n    }\r\r\n\r\r\n    var columns = [];\r\r\n    columns = [\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: vis},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"},\r\r\n    {renderAs: \"V\", visibility: \"V\"}\r\r\n    ];\r\r\n    this.context.options.columns.set(\"value\", []);\r\r\n    this.context.options.columns.set(\"value\", columns);\r\r\n}\r\r\n\r\r\nthis.addAccountee = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n   \tif (isAmndFinal && record == length) {\r\r\n\t    \tvalue.setEnabled(true);\r\r\n\t    \tthis.context.options.addParty.set(\"value\", false);\r\r\n\t    \tthis.context.options.deleteParty.set(\"value\", true);\r\r\n\t    \tthis.ui.get(\"PartyTable/partytype[\"+record+\"]\").setData({name:\"Accountee\",value:\"Accountee\"});\r\r\n    \t}\r\r\n}\r\r\n\r\r\nthis.setMediaVis = function (value) {\r\r\n\tvar r = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(r).get(\"partyType\").get(\"name\") == \"Remitting Bank\") {\r\r\n\t\tvalue.setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tvalue.hide();\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.onDeleteParty = function (value1,value2) {\r\r\n\tvar i = value1.getRecordIndex(value2);\r\r\n\tvar type = value2.partyType.name;\r\r\n\r\r\n\tif((i == 0 || i == 1) || (isAmndFinal && i != length)) {\r\r\n\t\talert(\"You can not delete this row\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n\t\tthis.context.options.addParty.set(\"value\", true);\r\r\n\r\r\n\t\tthis.context.options.deletedCIF.set(\"value\", value2.partyId);\r\r\n\t\tif (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \t\tthis.ui.get(\"update\").click();\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n//------------------------------------------------------------------\r\r\nthis.validateOneType = function (type,value) {\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar n = 0;\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == type){\r\r\n\t\t\tn+=1\r\r\n\t\t}\r\r\n\t}\r\r\n\tif(value.getData().name == type && n>1){\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"Only one  is allowed of same Type\");\r\r\n\t\tthis.context.binding.get(\"value\").remove(record);\r\r\n\t}else{\r\r\n\t\tthis.context.options.warningMess.set(\"value\", \"\");\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.resetPartyItem = function (record) {\r\r\n\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/CIF[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/partyname[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/country[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/language[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/reference[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address1[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address2[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address3[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setData(\"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n}\r\r\n\r\r\nthis.validateParty = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"country\", \"EG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setData(\"\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\")\r\r\n\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(false);\r\r\n\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(false);\r\r\n\t//validateOneType\r\r\n\tthis.validateOneType(\"Accountee\",value);\r\r\n\tthis.validateOneType(\"Drawee\",value);\r\r\n\tthis.validateOneType(\"Case in Need\",value);\r\r\n\tthis.validateOneType(\"Drawer\",value);\r\r\n\tthis.validateOneType(\"Remitting Bank\",value);\r\r\n\t\r\r\n\t//Reset Data\r\r\n\tthis.resetPartyItem(record);\t\r\r\n    \t//Default values\r\r\n\r\r\n\tif (this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\") == \"Remitting Bank\"){\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"SWIFT\");\r\r\n\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setVisible(true,true);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"media\", \"\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address\", \"\");\r\r\n//\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n//\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t}\r\r\n\t\r\r\n\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n\r\r\n}\r\r\n\r\r\nthis.setOwnerCIF = function () {\r\r\n\tfor (var i=0; i< this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Accountee\"){\r\r\n\t\t\tthis.context.options.accounteeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Drawee\"){\r\r\n\t\t\tthis.context.options.draweeCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t\tif(this.context.binding.get(\"value\").get(i).get(\"partyType\").get(\"name\") == \"Case in Need\"){\r\r\n\t\t\tthis.context.options.caseCIF.set(\"value\", this.context.binding.get(\"value\").get(i).get(\"CIF\"));\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\nthis.setPartyId = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tvar type = this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"name\");\r\r\n\tthis.context.binding.get(\"value\").get(record).set(\"isRetrived\", false);\r\r\n\t\r\r\n\t//Set Owner CIF\r\r\n//\tthis.setOwnerCIF();\r\r\n\t//Validation\r\r\n\tif(isNaN(Number(value.getData())) || value.getData().length < 8){\r\r\n        value.setValid(false , \"must be 8 digits\");\r\r\n\t\treturn false;\r\r\n\t}else{\r\r\n        value.setValid(true);\r\r\n        if (type == \"Accountee\" || type == \"Drawee\") {\r\r\n        \tthis.ui.get(\"update\").click();\r\r\n        }\r\r\n\t\treturn true;\t\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.concatOnDelete = function (value){\r\r\n\tvar newString = \"\";\r\r\n\tfor (var i=0; i<this.context.binding.get(\"value\").length(); i++) {\r\r\n\t\tif (i == value && i > 1) {\r\r\n\t\t\tcontinue;\r\r\n\t\t}\r\r\n\t\tnewString +=this.context.binding.get(\"value\").get(i).get(\"partyType\").name +\",\";\r\r\n\t}\r\r\n\tthis.context.options.concatString.set(\"value\", newString);\r\r\n}\r\r\n\r\r\nthis.drawerNotNBE = function (){\r\r\n    if (this.context.binding.get(\"value\").get(1).get(\"isNbeCustomer\") == false){\r\r\n        this.context.binding.get(\"value\").get(1).set(\"country\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"country\").get(\"englishdescription\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(1).set(\"name\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"name\"));\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.mapPartyData = function (){\r\r\n      record = this.context.options.partyIndex.get(\"value\");\r\r\n\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") == \"Remitting Bank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\"){\r\r\n\r\r\n\t\tthis.ui.get(\"PartyTable/partyCIF[\"+record+\"]\").setValid(false,\"This CIF is not corresponding to a Bank\");\r\r\n\t}else{\t\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"nationality\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",\"EG\");\r\r\n\t\t}\r\r\n\t\telse{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"country\",this.context.options.customerFullDetails.get(\"value\").get(\"nationality\"));\r\r\n\t\t}\r\r\n\t\tif (this.context.options.customerFullDetails.get(\"value\").get(\"language\") == \"\" || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == null || this.context.options.customerFullDetails.get(\"value\").get(\"language\") == undefined ) {\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", \"ENG\");\r\r\n\t\t}else{\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"language\", this.context.options.customerFullDetails.get(\"value\").get(\"language\"));\r\r\n\t\t}\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"branch\", {});\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"value\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tthis.context.binding.get(\"value\").get(record).get(\"branch\").set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"customerBranch\"));\r\r\n\t\tif(this.context.binding.get(\"value\").get(record).get(\"partyType\").get(\"value\") === \"Remitting Bank\"){\t\r\r\n\t\t\t\r\r\n\t\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\t\tvar input = cif+\"-\"+appID;\r\r\n\t\t\tthis.ui.get(\"GetAddressBIC\").execute(input);\r\r\n\t\t\tif (!!this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\")) {\t\r\r\n\t\t\t\t\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", this.context.options.BeneficiaryDetails.get(\"value\").get(\"correspondentRefNum\"));\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\",\"NO REF\");\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").setEnabled(true);\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").setEnabled(true);\r\r\n\t\t}else{\t\r\r\n\t\t\tthis.context.binding.get(\"value\").get(record).set(\"reference\", \"NO REF\");\r\r\n\t\t\tthis.ui.get(\"PartyTable/address[\"+record+\"]\").hide();\r\r\n\t\t\tthis.ui.get(\"PartyTable/media[\"+record+\"]\").hide();\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.clickEx = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tthis.context.options.partyIndex.set(\"value\", record);\r\r\n\tvalue.setData(true);\r\r\n\tif (!!this.context.binding.get(\"value\").get(record).get(\"partyCIF\")) {\r\r\n\t\tvar cif = this.context.binding.get(\"value\").get(record).get(\"partyCIF\");\r\r\n\t\tvar appID = this.context.options.appID.get(\"value\");\r\r\n\t\tvar input = cif +\"-\"+appID;\r\r\n\t    \tthis.ui.get(\"GetPartyDetails1\").execute(input);\r\r\n//\t    \tthis.context.binding.get(\"value\").get(record).set(\"partyId\",  this.context.binding.get(\"value\").get(record).get(\"partyCIF\"));\r\r\n\t}\t\r\r\n}\r\r\n\r\r\nthis.setBIC = function (value){\r\r\n    this.context.options.selectedBIC.set(\"value\", value.getData());\r\r\n}\r\r\n//---------------------------------------------------------------------------Drop_2--------------------------------------------------\r\r\n//function to view alert in case of get customer info error\r\r\nthis.AjaxErrorHandling = function(errorMSG)\r\r\n{\r\r\n\tthis.context.options.alertMessage.set(\"value\", errorMSG);\r\r\n\tthis.context.options.errorVis.set(\"value\", \"EDITABLE\")\r\r\n}\r\r\n\r\r\nthis.disableRetrieveBtn = function (value){\r\r\n\tvar record = value.ui.getIndex();\r\r\n\tif (record != 1){\r\r\n//\t\tvalue.setEnabled(false);\r\r\n\t\tvalue.hide();\r\r\n\t}else{\r\r\n\t\tvalue.setEnabled(true);\r\r\n\t}\r\r\n\tif(record == 1 && this.context.binding.get(\"value\").get(record).get(\"isNbeCustomer\") == true){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else if (record != 1){\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(true);\r\r\n\t}else {\r\r\n\t\tthis.ui.get(\"PartyTable/RetrieveBtn[\"+record+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}, {"id": "64.0ff96fd8-0740-4d17-887e-a56c8ef7921b", "versionId": "f2f1658d-ae7b-45ee-a60e-36b46b282024", "name": "Importer Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "importerDetails", "configOptions": ["importerDetailsCVVIS", "requestType", "bankRefVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.collectingBankRefVIS  = function()\r\r\n{\r\r\n\tif(this.context.options.requestType.get(\"value\") == \"amend\" || this.context.options.requestType.get(\"value\") == \"recreate\" )\r\r\n\t\tthis.context.options.bankRefVIS.set(\"value\", \"Editable\");\r\r\n\telse\r\r\n\r\r\n\t\tthis.context.options.bankRefVIS.set(\"value\", \"None\");\r\r\n}"}]}, "hasDetails": true}, {"id": "64.1da5da8a-d92b-4788-add6-533189d60ad0", "versionId": "8c515d6c-be9c-40f0-b3da-415911a71f4b", "name": "layout", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {}, "hasDetails": false}, {"id": "64.3e998f87-f87f-443f-b820-c2f7d465fa68", "versionId": "7ea0a2ea-391b-4ffd-b4eb-f2fd890c4773", "name": "Layout 3P CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {}, "hasDetails": false}, {"id": "64.701ac070-61ee-4489-9c65-6cc48db9510a", "versionId": "9eeb1345-7563-4417-88a6-845d0a059643", "name": "Layout 4P CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {}, "hasDetails": false}, {"id": "64.7efffff5-e4b6-4afc-a9a2-4e9b478d26a5", "versionId": "e146c342-750c-4f66-b669-01213bf55fe1", "name": "Multi Tenor Dates CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "multiTenorDates"}, "hasDetails": true}, {"id": "64.041a4da1-f77a-4f68-8837-a712d1a38d59", "versionId": "dc0256a7-8e64-4c99-9b51-54d7514a45ec", "name": "ODC Collections CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "odcCollection", "configOptions": ["odcCollectionVis"]}, "hasDetails": true}, {"id": "64.3d608948-c1fa-4ac3-8d4b-6a42f90a5132", "versionId": "5d16a9a4-8300-453d-869f-94ee95145d51", "name": "ODC Parties CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (!this.context.options.isReturned.get(\"value\")) {\t\r\r\n\tthis.ui.get(\"GetCustomerAndPartyAccountListBtn\").click();\r\r\n}", "bindingType": "Parties", "configOptions": ["odcPartiesCVVIS", "odcPartiesCVBTNVIS", "partyTypeName", "customerFullDetails", "addressBICList", "isReturned", "<PERSON><PERSON><PERSON><PERSON>"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//Note : old script is below commented!!\r\r\n\r\r\n//On retrieve cif info btn - service call to retrieve party info\r\r\nthis.getPartyDetails = function (cif,name) {\r\r\n\t\r\r\n    this.context.options.partyTypeName.set(\"value\", name);\r\r\n    \r\r\n    var customerCIF = this.ui.get(cif).getData();\r\r\n    //validate cif before using it\r\r\n    if (isNaN(Number(customerCIF)) || customerCIF.length < 8) {\r\r\n        this.ui.get(cif).setValid(false, \"CIF must be 8 Digits\");\r\r\n\r\r\n    } else {\r\r\n        this.ui.get(cif).setValid(true);\r\r\n        this.ui.get(\"GetPartyDetails\").execute(customerCIF);\r\r\n    }\r\r\n}\r\r\n\r\r\n//on GetPartyDetails results - map the retrieved data\r\r\nthis.mapPartyData = function (name) {\r\r\n\r\r\n    if (name == \"collectingBank\" && this.context.options.customerFullDetails.get(\"value\").get(\"customerType\") != \"B\") {\r\r\n        var cif = \"collectingBankCIF\";\r\r\n        this.ui.get(cif).setValid(false, \"This CIF is not corresponding to a Bank\");\r\r\n\r\r\n    } else if (name == \"collectingBank\") {\r\r\n\t  var bankCIF = this.context.options.customerFullDetails.get(\"value\").get(\"customerNo\")\r\r\n        this.context.binding.get(\"value\").get(name).set(\"id\", bankCIF);\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"name\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"EG\");\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"reference\", \"NO REF\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"media\", \"SWIFT\");\r\r\n\t  \r\r\n\t  //Service call to get Address code list\r\r\n        this.ui.get(\"GetAddressBIC\").execute(bankCIF);\r\r\n\r\r\n    } else {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyId\", this.context.options.customerFullDetails.get(\"value\").get(\"customerNo\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine1\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine2\"));\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", this.context.options.customerFullDetails.get(\"value\").get(\"customerAddress\").get(\"addressLine3\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyName\", this.context.options.customerFullDetails.get(\"value\").get(\"EnglishName\"));\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"EG\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"ENG\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"refrence\", \"NO REF\");\r\r\n        \r\r\n        //Hidden btn to get Customer And Party Account List for charges - also its called on view load!!!\r\r\n        this.ui.get(\"GetCustomerAndPartyAccountListBtn\").click();\r\r\n    }\r\r\n}\r\r\n\r\r\n//On change of cif field - reset party info\r\r\nthis.resetPartyOnCifChange = function (name){\r\r\n    if (name == \"collectingBank\") {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"id\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"name\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"reference\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"media\", \"\");\r\r\n        this.context.options.addressBICList.set(\"value\", [])\r\r\n\r\r\n    } else {\r\r\n\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyId\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address1\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address2\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"address3\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"partyName\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"country\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"language\", \"\");\r\r\n        this.context.binding.get(\"value\").get(name).set(\"refrence\", \"\");\r\r\n    }\r\r\n}\r\r\n\r\r\n\r\r\n// this.showCollectigBankRefSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifInfo.get(\"value\") == true) {\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}\r\r\n// this.partyTypeSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifNOForPartyTypes.get(\"value\") == true){\r\r\n//\t console.log(this.context.options.retrieveCifInfo)\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}"}]}, "hasDetails": true}, {"id": "64.04664cbf-b838-4a5b-bf47-c4160069d58d", "versionId": "30bb0b7d-c031-4cfa-8bf1-cb139dccd90b", "name": "ODC Parties CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "Parties", "configOptions": ["odcPartiesCVVIS", "odcPartiesCVBTNVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "// this.showCollectigBankRefSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifInfo.get(\"value\") == true) {\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"collectingBankSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}\r\r\n// this.partyTypeSection = function()\r\r\n//{\r\r\n//\t if(this.context.options.retrieveCifNOForPartyTypes.get(\"value\") == true){\r\r\n//\t console.log(this.context.options.retrieveCifInfo)\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(true,true);\r\r\n//\t }\r\r\n//\t \r\r\n//\t else{\r\r\n//\t this.ui.get(\"PartyTypesSection\").setVisible(false,true);\r\r\n//\t }\r\r\n//}"}]}, "hasDetails": true}, {"id": "64.9d678f8f-ae90-4e9d-bfb5-10fbbf775731", "versionId": "dbb77c8b-14e2-4925-aafe-7b469c794b54", "name": "Parties CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "parties", "configOptions": ["partiesVis"], "inlineScripts": []}, "hasDetails": true}, {"id": "64.5510de3d-75a5-4353-8d6d-82f540b6d556", "versionId": "8e1130c2-4503-4eb0-a825-dd9c0bdb85c5", "name": "Print Barcode", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "barcode", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.load=function(){\r\r\n\r\r\nvar generatedBarCode=this.ui.get(\"Data1\");\r\r\n\r\r\n\r\r\nif(generatedBarCode!=null){\r\r\n\r\r\ndocument.getElementById('tdbarcode').innerHTML=generatedBarCode.getText();\r\r\n\r\r\n\r\r\n}\r\r\n\r\r\n}"}]}, "hasDetails": true}, {"id": "64.5c068fcf-4364-4084-8825-6bb898b38609", "versionId": "7db23117-87d3-4297-98bc-73fd5c7cf4e9", "name": "Products and Shipment Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "shipmentDetails", "configOptions": ["productsVis", "shipmentVis"]}, "hasDetails": true}, {"id": "64.f0c268ac-0772-4735-af5b-5fc6caec30a1", "versionId": "20ae7397-1248-4d0c-853a-6467781ec4ed", "name": "Reversal Closure CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "reversalClosureReason", "configOptions": ["reversalReasonVIS", "closureReasonVIS", "executionHubVIS"]}, "hasDetails": true}, {"id": "64.e2910043-5f79-408a-81b4-3af68cb1deac", "versionId": "35e0a560-da5a-41c8-815e-07c4f6458377", "name": "Search Templete", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "appinfo", "configOptions": ["backBtnVIS", "EndBtnVIS", "errorMsg"], "inlineScripts": []}, "hasDetails": true}, {"id": "64.7c4993bc-f7d5-4689-96c6-89c5b908583f", "versionId": "3e728fac-930b-4161-9fb2-c7fcac0ddd33", "name": "Start New Request CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "ODCRequest", "configOptions": ["retrieveRequestBtn", "createRequestBtn", "errorMessgae", "retrieveCustomer", "errorPnlVis", "parentrequestTypeVIS", "userConditions", "role"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "///show parent request number if request nature is update request\r\r\nthis.showParentRequestNo = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"update\"){\r\r\n\t\t//this.context.options.parentrequestTypeVIS.set(\"Editable\",  \"NONE\");\r\r\n\t\tthis.ui.get(\"updateRequestTypeSection\").setVisible(true,true);\r\r\n\t}\r\r\n\telse{\r\r\n\t\t//this.context.options.parentrequestTypeVIS.set(\"value\",  \"NONE\");\r\r\n\t\tthis.ui.get(\"updateRequestTypeSection\").setVisible(false,true);\r\r\n\t}\r\r\n}\r\r\n// hide create contract if request nature , request type , cif no and parent request no is empty\r\r\nthis.showCreateBtn = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"\" ||this.context.binding.get(\"value\").get(\"requestType\").get(\"value\") == \"\" ||this.context.binding.get(\"value\").get(\"cif\") == \"\" ||\r\r\n\t(this.context.binding.get(\"value\").get(\"requestNature\").get(\"value\") == \"update\" && this.context.binding.get(\"value\").get(\"parentRequestNo\") == \"\" ))\r\r\n\t{\r\r\n\t \tthis.ui.get(\"createRequestBtn\").setVisible(false,true);\r\r\n\t}\r\r\n\telse{\r\r\n\t\tthis.ui.get(\"createRequestBtn\").setVisible(true,true);\t\r\r\n\t}\r\r\n}"}]}, "hasDetails": true}, {"id": "64.331e6374-b546-4447-9923-316c70693dd1", "versionId": "0db3b06a-9f16-4991-8ced-d67558109b4c", "name": "temp", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "//alert(this.context.viewid)", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.mandatory2 = function (me){\r\r\n//\tme.setEnabled(false,true)\r\r\n\tme.setValid(false,\"shit\");\r\r\n//\tif(me.getData() == null || me.getData() == undefined){\r\r\n//\t\tme.setValid(false,\"shit\");\r\r\n//\t\treturn false\r\r\n//\t}else{\r\r\n//\t\tme.setValid(true);\r\r\n//\t\treturn true\r\r\n//\t} \r\r\n}\r\r\n\r\r\n//---------------------------------------Validation Library------------------------------------\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nvar validationList = [];\r\r\nthis.addError = function (path, message) {\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n\t// return validationList;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the field is null 'Mandatory', \r\r\n   message is OPTIONAL!! , DEFAULT is 'This Field Is Mandatory',\r\r\n   EX: mandatory(tw.local.name , 'tw.local.name', 'validation message') */\r\r\n\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\tif (!message) {\r\r\n\t\tmessage = \"This Field Is Mandatory\";\r\r\n\t}\r\r\n\r\r\n\tif (value == null) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\tthis.addError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.mandatoryList = function (mandatoryList) {\r\r\n\tmandatoryList.forEach((element) => {\r\r\n\t\tthis.mandatory(element[0], element[1], element[2]);\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past and the last variable is OPTIONAL to exclude today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude) {\r\r\n\t\tif (value != null && value < checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value <= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, message, exclude) {\r\r\n\tif (!value) return;\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\tif (exclude && exclude === true) {\r\r\n\t\tif (value != null && value > checkDate) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\tif (value != null && value >= new Date()) {\r\r\n\t\t\tthis.addError(path, message);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (value && value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (value && value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.exactLength = function (value, path, len, message) {\r\r\n\tif (value && value.length != len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (value && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (value && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Validates the before and after decimal length (even if string).\r\r\n   message is OPTIONAL , DEFAULT `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it` \r\r\n   Ex: validateDecimal (tw.local.decimal, 'tw.local.decimal', 10, 6, 'validation message')*/\r\r\nthis.validateDecimal = function (value, path, beforeDecimal, afterDecimal, message) {\r\r\n\tif (!value) return;\r\r\n\tif (!message || message == \"\") {\r\r\n\t\tmessage = `Max is (${beforeDecimal}) before decimal and (${afterDecimal}) after it`;\r\r\n\t}\r\r\n\t// Handle string or number input\r\r\n\tif (typeof value === \"number\") {\r\r\n\t\tvalue = value.toString();\r\r\n\t}\r\r\n\t// Regex to check overall format\r\r\n\tconst regex = new RegExp(\"^\\\\d{1,\" + beforeDecimal + \"}\\\\.?\\\\d{0,\" + afterDecimal + \"}$\");\r\r\n\r\r\n\tif (regex.test(value) == false) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\tthis.setErrorList();\r\r\n};\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};"}]}, "hasDetails": true}, {"id": "64.98459c6f-cb8f-462d-9fae-63d331db4606", "versionId": "9088f2b0-71c5-4a5b-991b-658253716d2c", "name": "test view", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "newtest", "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.changeIt = function(me){\r\r\n\tvar v1 = me.getData();\r\r\n\tvar test2 = bpmext.ui.getView(\"/test_view_21\");\r\r\n\ttest2.setData(v1);\r\r\n\t\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n//---------------VALIDATION FUNCTIONS---------------------------------------------------------\r\r\n\r\r\n/* \r\r\nHow to use:\r\r\n    - put these functions at the inline JS of the template.\r\r\n\t- in the CSHS script, add the following line:\r\r\n\t\tvar lib = bpmext.ui.getView('templatePathName');\r\r\n\t- at te start of the script, add the following line, to clear the error list:\r\r\n\t\tlib.setErrorList();\r\r\n\t- use the functions as follows:\r\r\n\t\tlib.addError(path, message);\r\r\n\t\tlib.mandatory(value, path, message);\r\r\n\t- at the end of the script, add the following line, to get the error list:\r\r\n\t\tlib.getErrorList();\r\r\n*/\r\r\n\r\r\nvar dateOptions = { day: \"numeric\", month: \"long\", year: \"numeric\" };\r\r\nvar validationList = [];\r\r\n\r\r\n/* Add a coach validation error if the field is empty, message is OPTIONAL!!\r\r\nmandatory(tw.local.input, \"tw.local.input\", message : concat) : CSHS */\r\r\nthis.mandatory = function (value, path, message) {\r\r\n\r\r\n\tmessage = message || 'This field is Mandatory';\r\r\n\r\r\n\tif (value == null || value == undefined) {\r\r\n\t\taddError(path, message);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof value) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (value.trim().length == 0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (value == 0.0 || value < 0.0) {\r\r\n\t\t\t\t\taddError(path, message);\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error \t\t\r\r\nEX: addError(tw.local.name , 'validation message!')*/\r\r\nfunction addError (path, message) {\r\r\n\t\r\r\n\tif (validationList == null) {\r\r\n\t\tvalidationList = [];\r\r\n\t}\r\r\n\tvalidationList.push({\r\r\n\t\terrorBOPath: path,\r\r\n\t\terrorMessage: message,\r\r\n\t});\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the past today.\r\r\n   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/\r\r\nthis.notPastDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || \"Please input a valid future date\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value < checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value <= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is between two dates.\r\r\n   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/\r\r\nthis.dateBetween = function (value, path, date1, date2, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage =\r\r\n\t\tmessage ||\r\r\n\t\t`This date must be between ${date1.toLocaleDateString(\r\r\n\t\t\t\"en-GB\",\r\r\n\t\t\tdateOptions\r\r\n\t\t)} and ${date2.toLocaleDateString(\"en-GB\", dateOptions)}`;\r\r\n\r\r\n\tif (value && value > date1 && value < date2) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\r\r\n\tthis.addError(path, message);\r\r\n\treturn false;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.\r\r\n  EX:\tnotFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/\r\r\nthis.notFutureDate = function (value, path, exclude, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || \"Please input a valid date in the past\";\r\r\n\r\r\n\tvar checkDate = this.getMidNightDate();\r\r\n\t//exclude today?\r\r\n\tif (exclude && value > checkDate) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tif (!exclude && value >= new Date()) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is greater than given length (len).\r\r\n  EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\tmessage = message || `This field can only contain up to ${len} characters.`;\r\r\n\tif (value.length > len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the STRING length is less than given length (len).\r\r\n   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/\r\r\nthis.minLength = function (value, path, len, message) {\r\r\n\tif (!value) return;\r\r\n\r\r\n\tmessage = message || `This field must contain at least ${len} characters.`;\r\r\n\tif (value.length < len) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is greater than given value (max).\r\r\n * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/\r\r\nthis.maxNumber = function (value, path, max, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be < ${max}`;\r\r\n\tif (!isNaN(value) && value > max) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n/* Add a coach validation error if the number is less than given value (min).\r\r\n   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/\r\r\nthis.minNumber = function (value, path, min, message) {\r\r\n\tif (!value) return;\r\r\n\t\r\r\n\tmessage = message || `This value must be > ${min}`;\r\r\n\tif (!isNaN(value) && value < min) {\r\r\n\t\tthis.addError(path, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\n// FOR VIEW\r\r\n//prevents input length exceeding len, only works for string and on input event\r\r\nthis.maxLenOnInput = function (me, potential, len, message) {\r\r\n\tif (potential.length > len) {\r\r\n\t\tvar label = me.getLabel();\r\r\n\t\tmessage = message || `${label} can only contain up to ${len} characters.`;\r\r\n\t\tme.setValid(false, message);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\tme.setValid(true);\r\r\n\treturn true;\r\r\n};\r\r\n\r\r\nthis.isChecker = function (fun, me, ...args) {\r\r\n\tif (!me) return;\r\r\n\tif (me.getVisibility() == \"READONLY\" || me.getVisibility() == \"HIDDEN\") {\r\r\n\t\treturn;\r\r\n\t}\r\r\n\treturn fun(...args);\r\r\n};\r\r\n//-------------------------------------Private-----------------------------------------------------\r\r\n// returns 'new Date()' but reset it to '00:00:00'.\r\r\nthis.getMidNightDate = function () {\r\r\n\tvar midNightDate = new Date();\r\r\n\t// Set hours, minutes, seconds and milliseconds to 0\r\r\n\tmidNightDate.setHours(0);\r\r\n\tmidNightDate.setMinutes(0);\r\r\n\tmidNightDate.setSeconds(0);\r\r\n\tmidNightDate.setMilliseconds(0);\r\r\n\r\r\n\treturn midNightDate;\r\r\n};\r\r\n\r\r\n// getter for 'validationErrors', input is (tw.system.coachValidation).\r\r\nthis.getErrorList = function (coachValidation) {\r\r\n\tcoachValidation.validationErrors = validationList;\r\r\n\t// this.setErrorList();\r\r\n      validationList = [];\r\r\n}\r\r\n\r\r\n// setter for 'validationErrors'.\r\r\nthis.setErrorList = function () {\r\r\n\tvalidationList = [];\r\r\n};\r\r\n\r\r\nthis.camelCaseToTitle = function (camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n};"}]}, "hasDetails": true}, {"id": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "versionId": "2991e37c-d215-49ba-a874-3dbff01d5c0a", "name": "test view 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "test2", "inlineScripts": []}, "hasDetails": true}, {"id": "64.436a7747-a90a-4b7e-8d02-ff4803d46ce0", "versionId": "15d228f6-0bd1-4af6-88b3-1af012b927a5", "name": "testTable", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "fo"}, "hasDetails": true}, {"id": "64.b4f7785b-f352-434f-9dc2-a68e51beac4e", "versionId": "4c168860-9eae-4b1d-a4e0-c42078b4ed87", "name": "testView", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "testData"}, "hasDetails": true}, {"id": "64.72428c7b-aa19-4400-bea7-59743c5442cc", "versionId": "c79eb96d-5560-467c-84c7-7135acc836e4", "name": "Validation Helper", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"configOptions": ["runTimeValid", "stop"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "_this = this;\r\r\n\r\r\nvar displayText = \"Display_Text1\";\r\r\nconst generalTab = \"GENERAL\";\r\r\nvar tabSectionId = \"\";\r\r\nvar tabSection = \"\";\r\r\nconst errorSectionId = \"ErrorSection_Panel1\";\r\r\nconst stopVU = this.context.options.stop.get(\"value\") || false;\r\r\nconst runTime = this.context.options.runTimeValid.get(\"value\") || false;\r\r\nconst initExpanded = this.context.options.initExpanded?.get(\"value\") || true;\r\r\n\r\r\n_this.startVU = function () {\r\r\n\ttabSection = document.querySelector('[role=\"tablist\"]');\r\r\n\ttabSectionId = _this.getTabSectionId(tabSection);\r\r\n\tconst redCircles = document.querySelectorAll(\".red-circle\");\r\r\n\r\r\n\tvar viewErrorList = [];\r\r\n\tviewErrorList = bpmext.ui.getInvalidViews();\r\r\n\r\r\n\tif (viewErrorList.length == 0) {\r\r\n\t\tthis.ui.get(displayText).setText(\"\");\r\r\n\t\t_this.ui.get(errorSectionId).setVisible(false, true);\r\r\n\t\t_this.resetRedCircle(redCircles);\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tconst errMapList = viewErrorList\r\r\n\t\t.map((view) => _this.constructErrorMap(view))\r\r\n\t\t.filter(function (obj) {\r\r\n\t\t\treturn obj != null;\r\r\n\t\t});\r\r\n\r\r\n\tvar viewMapList = [];\r\r\n\t_this.resetRedCircle(redCircles);\r\r\n\tviewMapList = _this.organizeErrorsByTab(errMapList);\r\r\n\tconsole.dir(viewMapList);\r\r\n\t// Add counter red circle\r\r\n\t_this.resetRedCircle(redCircles);\r\r\n\tviewMapList.forEach((viewMap) => _this.addRedCircleToTab(viewMap));\r\r\n\r\r\n\t//Add panel with tabs and messages\r\r\n\t_this.ui.get(errorSectionId).setVisible(true, true);\r\r\n\tsetTimeout(() => {\r\r\n\t\t_this.constructValidPanel(viewMapList);\r\r\n\t}, 200);\r\r\n};\r\r\n\r\r\n_this.getTabInfoFirst = function (tabSection) {\r\r\n\tconst tabElementList = tabSection?.children;\r\r\n\tvar tabsInfo = {};\r\r\n\tfor (var i = 0; i < tabElementList.length; i++) {\r\r\n\t\tvar tabElement = tabElementList[i];\r\r\n\r\r\n\t\tvar tabInnerText = tabElement.innerText.split(\"\\n\")[0].replaceAll(\" \", \"\");\r\r\n\t\tif (!tabInnerText || tabElement.getAttribute(\"role\") !== \"tab\") continue;\r\r\n\r\r\n\t\ttabsInfo[tabInnerText] = {\r\r\n\t\t\ttabDomID: tabElement.id,\r\r\n\t\t\ttabPathId: i,\r\r\n\t\t};\r\r\n\t}\r\r\n\r\r\n\treturn tabsInfo;\r\r\n};\r\r\n\r\r\n_this.resetRedCircle = function (redCircles) {\r\r\n\t// const redCircles = document.querySelectorAll(\".red-circle\");\r\r\n\tif (!redCircles) return;\r\r\n\tredCircles.forEach((circle) => circle.remove());\r\r\n};\r\r\n\r\r\n_this.getTabSectionId = function (tabSection) {\r\r\n\tif (!tabSection) return;\r\r\n\tvar currentElement = tabSection;\r\r\n\r\r\n\twhile (currentElement && currentElement !== document.body) {\r\r\n\t\tif (currentElement.classList.contains(\"Tab_Section\")) {\r\r\n\t\t\ttabSectionId = currentElement.getAttribute(\"control-name\");\r\r\n\t\t\tbreak;\r\r\n\t\t}\r\r\n\r\r\n\t\tcurrentElement = currentElement.parentElement;\r\r\n\t}\r\r\n\r\r\n\treturn tabSectionId;\r\r\n};\r\r\n\r\r\n_this.constructValidPanel = function (viewMapList) {\r\r\n\tif (!viewMapList || viewMapList.length == 0) return;\r\r\n\tvar tabNameListHTML = ``;\r\r\n\r\r\n\tfor (var i = 0; i < viewMapList.length; i++) {\r\r\n\t\tvar tabData = viewMapList[i].tab;\r\r\n\t\tvar messageList = viewMapList[i].messages;\r\r\n\r\r\n\t\tif (!tabData) continue;\r\r\n\r\r\n\t\tvar tabDomId = tabData.domId;\r\r\n\t\tvar tabName = tabData.name || generalTab;\r\r\n\t\tvar errorListId = `error-list-${tabDomId}`;\r\r\n\t\tvar tabIndex = tabData.pathId;\r\r\n\t\tvar errorListHTML = _this.generateErrorListHTML(messageList, tabName, tabIndex);\r\r\n\r\r\n\t\ttabNameListHTML += _this.generateTabItemHTML(tabName, tabDomId, errorListId, tabIndex, errorListHTML);\r\r\n\t}\r\r\n\r\r\n\ttabNameListHTML = `<ul class=\"tab-list\">${tabNameListHTML}</ul>`;\r\r\n\t_this.ui.get(displayText).setText(tabNameListHTML);\r\r\n};\r\r\n\r\r\n_this.activateTab = function (tabDomId, tabName, tabIndex) {\r\r\n\tif (!tabName || !tabIndex) return;\r\r\n\r\r\n\tif (tabName !== generalTab) {\r\r\n\t\tpage.ui.get(tabSectionId).setCurrentPane(tabIndex);\r\r\n\t\tif (tabDomId) {\r\r\n\t\t\tvar tabElement = document.getElementById(tabDomId);\r\r\n\t\t\t_this.highLighElement(tabElement);\r\r\n\t\t}\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.activateField = function (tabName, panelString, tabIndex, fieldPathId) {\r\r\n\tconst panelList = panelString.split(\"@@\").filter((e) => e !== \"\");\r\r\n\r\r\n\tif (tabIndex && tabSectionId && tabName !== generalTab) {\r\r\n\t\tpage.ui.get(tabSectionId).setCurrentPane(tabIndex);\r\r\n\t}\r\r\n\tif (panelList && panelList.length > 0) {\r\r\n\t\tfor (let i = 0; i < panelList.length; i++) {\r\r\n\t\t\tpage.ui.get(panelList[i]).expand();\r\r\n\t\t}\r\r\n\t}\r\r\n\tif (!fieldPathId) return;\r\r\n\r\r\n\tsetTimeout(() => {\r\r\n\t\t_this.focusOnElement(fieldPathId);\r\r\n\t}, 300);\r\r\n};\r\r\n\r\r\n_this.focusOnElement = function (fieldPathId) {\r\r\n\tvar fieldElement = page.ui.get(fieldPathId).context.element;\r\r\n\t_this.highLighElement(fieldElement);\r\r\n\r\r\n\tpage.ui.get(fieldPathId).focus();\r\r\n};\r\r\n\r\r\n_this.highLighElement = function (fieldElement) {\r\r\n\tif (!fieldElement) return;\r\r\n\r\r\n\tfieldElement.classList.add(\"highlighted-field\");\r\r\n\tsetTimeout(function () {\r\r\n\t\tfieldElement.classList.remove(\"highlighted-field\");\r\r\n\t}, 1500);\r\r\n};\r\r\n\r\r\n_this.addRedCircleToTab = function (viewMap) {\r\r\n\tif (!viewMap.tab.domId) return;\r\r\n\r\r\n\tconst messagesCount = viewMap.messages.length;\r\r\n\tconst tabDomId = viewMap.tab.domId;\r\r\n\tconst tabElement = document.getElementById(tabDomId);\r\r\n\tif (!tabElement) return;\r\r\n\r\r\n\t// Combine DOM reads\r\r\n\tconst existingCircle = tabElement.querySelector(\".red-circle\");\r\r\n\tconst newCircleContent = `<div class=\"red-circle\">${messagesCount}</div>`;\r\r\n\r\r\n\t// Combine DOM writes\r\r\n\tif (!existingCircle) {\r\r\n\t\ttabElement.insertAdjacentHTML(\"beforeend\", newCircleContent);\r\r\n\t} else {\r\r\n\t\texistingCircle.innerText = messagesCount;\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.constructErrorMap = function (fieldElement) {\r\r\n\tif (!fieldElement || !fieldElement.context.element || !fieldElement._bpmextViewNode) return null;\r\r\n\r\r\n\tvar fieldDomId = fieldElement.context.element.id;\r\r\n\tvar fieldParents = _this.getFieldParents(fieldDomId);\r\r\n      // var isField = Object.keys(fieldElement._bpmextViewNode._data.context.subview).length === 0;\r\r\n      var isField = true;\r\r\n\tif (isField) {\r\r\n\t\terrorMap = {\r\r\n\t\t\tfield: {\r\r\n\t\t\t\tmessage: fieldElement._bpmextVE?.errors?.[0]?.message || \"\",\r\r\n\t\t\t\tdomId: fieldDomId,\r\r\n\t\t\t\tpathId: fieldElement.context.element.getAttribute(\"control-name\"),\r\r\n\t\t\t\tviewId: fieldElement.context.element.getAttribute(\"data-viewid\"),\r\r\n\t\t\t\tlabel: fieldElement.getLabel(),\r\r\n\t\t\t},\r\r\n\r\r\n\t\t\tpanels: fieldParents.cPanelList /*[list of \"SPARKCPanel\"]*/,\r\r\n\r\r\n\t\t\tview: fieldParents.viewObj,\r\r\n\t\t};\r\r\n\t\treturn errorMap;\r\r\n\t}\r\r\n};\r\r\n\r\r\n_this.getFieldParents = function (elementId) {\r\r\n\tvar fieldParents = {\r\r\n\t\tviewObj: {\r\r\n\t\t\tname: \"\",\r\r\n\t\t\tdomId: \"\",\r\r\n\t\t\tpathId: \"\",\r\r\n\t\t},\r\r\n\t\tcPanelList: [],\r\r\n\t};\r\r\n\tconst cPanelClass = \"Collapsible_Panel\";\r\r\n\tconst tabClass = \"tab-pane\";\r\r\n\r\r\n\tvar currentElement = document.getElementById(elementId);\r\r\n\r\r\n\twhile (currentElement && currentElement !== document.body) {\r\r\n\t\tif (currentElement.classList.contains(tabClass)) {\r\r\n\t\t\tfieldParents.viewObj.name = currentElement.getAttribute(\"aria-label\");\r\r\n\t\t\tfieldParents.viewObj.domId = currentElement.id;\r\r\n\t\t\tfieldParents.viewObj.pathId = currentElement.getAttribute(\"control-name\");\r\r\n\t\t} else if (currentElement.classList.contains(cPanelClass)) {\r\r\n\t\t\tfieldParents.cPanelList.unshift(currentElement.getAttribute(\"control-name\"));\r\r\n\t\t}\r\r\n\r\r\n\t\tcurrentElement = currentElement.parentNode;\r\r\n\t}\r\r\n\tconsole.dir(fieldParents);\r\r\n\treturn fieldParents;\r\r\n};\r\r\n\r\r\n_this.organizeErrorsByTab = function (errorList) {\r\r\n\tconst viewMap = new Map();\r\r\n\tlet tabsInfo = {};\r\r\n\r\r\n\tif (tabSection) {\r\r\n\t\ttabsInfo = _this.getTabInfoFirst(tabSection);\r\r\n\t}\r\r\n\r\r\n\terrorList.forEach((error) => {\r\r\n\t\tif (error) {\r\r\n\t\t\tconst viewName = error.view.name;\r\r\n\t\t\tconst sanitizedViewName = viewName?.replaceAll(\" \", \"\");\r\r\n\r\r\n\t\t\tif (!viewMap.has(viewName)) {\r\r\n\t\t\t\tviewMap.set(viewName, {\r\r\n\t\t\t\t\tview: {\r\r\n\t\t\t\t\t\tname: viewName,\r\r\n\t\t\t\t\t\tdomId: error.view.domId,\r\r\n\t\t\t\t\t\tpathId: error.view.pathId,\r\r\n\t\t\t\t\t},\r\r\n\t\t\t\t\tmessages: [],\r\r\n\t\t\t\t\ttab: {\r\r\n\t\t\t\t\t\tname: viewName,\r\r\n\t\t\t\t\t\tdomId: tabsInfo[sanitizedViewName]?.tabDomID,\r\r\n\t\t\t\t\t\tpathId: tabsInfo[sanitizedViewName]?.tabPathId,\r\r\n\t\t\t\t\t},\r\r\n\t\t\t\t});\r\r\n\t\t\t}\r\r\n\t\t\t// Add the error message to the corresponding tab entry\r\r\n\t\t\tconst viewEntry = viewMap.get(viewName);\r\r\n\t\t\tviewEntry.messages.push({\r\r\n\t\t\t\tmessage: error.field.message,\r\r\n\t\t\t\tfield: {\r\r\n\t\t\t\t\tdomId: error.field.domId,\r\r\n\t\t\t\t\tpathId: error.field.pathId,\r\r\n\t\t\t\t\tviewId: error.field.viewId,\r\r\n\t\t\t\t\tlabel: error.field.label,\r\r\n\t\t\t\t\tpanels: [...error.panels],\r\r\n\t\t\t\t},\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\t// Convert the map values to an array of tab objects\r\r\n\treturn [...viewMap.values()];\r\r\n};\r\r\n\r\r\n_this.generateTabItemHTML = function (tabName, tabDomId, errorListId, tabIndex, errorListHTML) {\r\r\n\tconst initialButtonText = initExpanded ? \"Hide\" : \"Show\";\r\r\n\tconst initialDisplayStyle = initExpanded ? \"block\" : \"none\";\r\r\n\treturn `<li class=\"tab-item\">\r\r\n            <div class=\"tab-container\">\r\r\n                <div class=\"tab-header\">\r\r\n                    <div class=\"gradient-box\">\r\r\n                        <a href=\"#${tabDomId}\" class=\"tab-name\"\r\r\n                            onclick=\"_this.toggleErrorList('${errorListId}'); event.preventDefault(); event.stopPropagation();\">${tabName}</a>\r\r\n                    </div>\r\r\n                </div>\r\r\n                <ul id=\"${errorListId}\" class=\"error-list\" style=\"display: ${initialDisplayStyle};\">${errorListHTML}</ul>\r\r\n            </div>\r\r\n        </li>`;\r\r\n};\r\r\n\r\r\n_this.generateErrorListHTML = function (listOfErrors, tabName, tabIndex) {\r\r\n\treturn listOfErrors\r\r\n\t\t.map(function (error) {\r\r\n\t\t\tconst fieldDomId = error.field.domId;\r\r\n\t\t\tconst fieldPathId = error.field.pathId;\r\r\n\t\t\tconst label = error.field.label;\r\r\n\t\t\tconst targetMessage = `<b>${label}</b> : ${error.message}`;\r\r\n\t\t\tconst panelString = error.field.panels.join(\"@@\");\r\r\n\r\r\n\t\t\treturn `<li><span class=\"bullet\">&#8226;</span> <a href=\"#${fieldDomId}\" class=\"message-link\" message-id=\"${fieldDomId}\" onclick=\"_this.activateField('${tabName}','${panelString}','${tabIndex}','${fieldPathId}');\">${targetMessage}</a></li>`;\r\r\n\t\t})\r\r\n\t\t.join(\"\");\r\r\n};\r\r\n\r\r\n_this.toggleErrorList = function (errorListId) {\r\r\n\tconst errorList = document.getElementById(errorListId);\r\r\n\tif (errorList) {\r\r\n\t\tconst isCollapsed = errorList.style.display === \"none\" || !errorList.style.display;\r\r\n\t\terrorList.style.display = isCollapsed ? \"block\" : \"none\";\r\r\n\t\t//   if (button) {\r\r\n\t\t//       button.textContent = isCollapsed ? \"Hide\" : \"Show\";\r\r\n\t\t//   }\r\r\n\t}\r\r\n};\r\r\n//=======================================REQUIRED===============================================//\r\r\nrequire([\"com.ibm.bpm.coach/engine\"], function (engine) {\r\r\n\tvar dve = engine._deliverValidationEvents;\r\r\n\tengine._deliverValidationEvents = function (event, viewMap, isClear) {\r\r\n\t\tdve(event, viewMap, isClear); // original processing first\r\r\n\t\t// console.log(\"_deliverValidationEvents\", event, viewMap, isClear);\r\r\n\t}.bind(engine);\r\r\n\tvar hve = engine.handleValidationEvent;\r\r\n\tengine.handleValidationEvent = function (event) {\r\r\n\t\thve(event);\r\r\n\t\t// console.log(\"handleValidationEvent\", event);\r\r\n\t\tif (!stopVU) {\r\r\n\t\t\t_this.startVU();\r\r\n\t\t}\r\r\n\t}.bind(engine);\r\r\n});\r\r\n\r\r\nvar uvvs = bpmext && bpmext.ui && bpmext.ui.updateViewValidationState;\r\r\nif (uvvs) {\r\r\n\tbpmext.ui.updateViewValidationState = function (view, event) {\r\r\n\t\tuvvs(view, event); //call original handler\r\r\n\t\t// console.log(\"updateViewValidationState\", view, event);\r\r\n\t\tif (!stopVU && runTime == true) {\r\r\n\t\t\t_this.startVU();\r\r\n\t\t}\r\r\n\t};\r\r\n}"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": "/* Style for the red circle counter */\r\r\n.red-circle {\r\r\n\tposition: absolute;\r\r\n\ttop: 0;\r\r\n\tright: 0;\r\r\n\twidth: 17px;\r\r\n\theight: 17px;\r\r\n\tbackground-color: red;\r\r\n\tborder-radius: 50%;\r\r\n\tdisplay: flex;\r\r\n\tjustify-content: center;\r\r\n\talign-items: center;\r\r\n\tcolor: white;\r\r\n\tfont-weight: bold;\r\r\n}\r\r\n\r\r\n.tab-link {\r\r\n\tfont-size: medium;\r\r\n}\r\r\n\r\r\n/* Style for the tab list */\r\r\n.tab-list {\r\r\n\tlist-style-type: none;\r\r\n\tpadding: 0;\r\r\n\tmargin: 0; /* Remove default margin */\r\r\n}\r\r\n\r\r\n/* Style for each tab item */\r\r\n.tab-item {\r\r\n\tmargin-bottom: 5px; /* Reduce space between tabs */\r\r\n\tborder: none;\r\r\n\tpadding: 5px;\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\tlist-style: none;\r\r\n}\r\r\n\r\r\n/* Style for the tab name */\r\r\n.tab-name {\r\r\n\tfont-size: 16px;\r\r\n\tmargin: 0;\r\r\n\tcolor: #8a1412; /* Replaced red with a shade of orange */\r\r\n\ttext-decoration: none;\r\r\n\tfont-weight: bold;\r\r\n}\r\r\n\r\r\n.tab-name:hover {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400;\r\r\n}\r\r\n\r\r\n.tab-name:focus {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400;\r\r\n}\r\r\n\r\r\n.tab-container {\r\r\n\tdisplay: flex;\r\r\n\tflex-direction: column;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n.tab-header {\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n/* Style for the gradient box */\r\r\n.gradient-box {\r\r\n\tflex: 1;\r\r\n\tpadding: 10px 20px;\r\r\n\tbackground: linear-gradient(45deg, rgba(255, 255, 255, 0.8), rgba(240, 240, 240, 0.8));\r\r\n\tborder-radius: 4px;\r\r\n\tdisplay: flex;\r\r\n\talign-items: center;\r\r\n\tcursor: auto; /* Change cursor to auto to indicate non-clickable */\r\r\n\ttransition: none;\r\r\n}\r\r\n\r\r\n/* .gradient-box:hover {\r\r\n\ttransform: scale(1.05);\r\r\n\tbackground: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(240, 240, 240, 1));\r\r\n} */\r\r\n\r\r\n.tab {\r\r\n\tposition: relative;\r\r\n\twidth: 100%;\r\r\n}\r\r\n\r\r\n.tab::after {\r\r\n\tcontent: attr(error-count);\r\r\n\tcolor: red;\r\r\n\tfont-size: 10px;\r\r\n\tposition: absolute;\r\r\n\tright: 5px;\r\r\n\ttop: 5px;\r\r\n}\r\r\n\r\r\n/* Add animation for the highlighted field */\r\r\n.highlighted-field {\r\r\n\tanimation-name: highlight;\r\r\n\tanimation-duration: 1.5s;\r\r\n}\r\r\n\r\r\n@keyframes highlight {\r\r\n\tfrom {\r\r\n\t\tbackground-color: yellow;\r\r\n\t}\r\r\n\tto {\r\r\n\t\tbackground-color: initial;\r\r\n\t}\r\r\n}\r\r\n\r\r\n.error-list {\r\r\n\tdisplay: none;\r\r\n\tmargin-left: 20px;\r\r\n\tpadding: 0;\r\r\n\tlist-style-type: none;\r\r\n\ttransition: max-height 0.2s ease-out;\r\r\n\toverflow: hidden;\r\r\n}\r\r\n\r\r\n.error-list.collapsed {\r\r\n\tmax-height: 0;\r\r\n\ttransition: max-height 0.3s ease-out;\r\r\n}\r\r\n\r\r\n.error-list.expanded {\r\r\n\tmax-height: 500px; /* Adjust this value as needed */\r\r\n\ttransition: max-height 0.3s ease-in;\r\r\n}\r\r\n\r\r\n/* Style for the toggle button */\r\r\n.toggle-button {\r\r\n\tmargin-left: 10px;\r\r\n\tbackground-color: #f7f7f7; /* Off-white background */\r\r\n\tcolor: #8a1412; /* Darker red for text */\r\r\n\tborder: 1px solid #e0e0e0; /* Off-white border */\r\r\n\tborder-radius: 4px;\r\r\n\tpadding: 5px 10px;\r\r\n\tcursor: pointer;\r\r\n\ttransition: background-color 0.3s ease, transform 0.3s ease;\r\r\n}\r\r\n\r\r\n.toggle-button:hover {\r\r\n\tbackground-color: #e8e8e8; /* Slightly darker off-white on hover */\r\r\n\ttransform: scale(1.05);\r\r\n}\r\r\n\r\r\n.bullet {\r\r\n\tcolor: #ff8c00; /* Replaced red with a shade of orange */\r\r\n\tmargin-right: 5px; /* Adjust spacing between bullet and link */\r\r\n}\r\r\n\r\r\n.message-link {\r\r\n\tcursor: pointer;\r\r\n\tcolor: #8a1412; /* Replaced red with a shade of orange */\r\r\n\ttext-decoration: none;\r\r\n\tpadding: 5px; /* Add padding for better hover area */\r\r\n\ttransition: color 0.2s ease, background-color 0.2s ease; /* Add transition for smooth animation */\r\r\n\tposition: relative;\r\r\n}\r\r\n\r\r\n.message-link:hover {\r\r\n\ttext-decoration: underline;\r\r\n\tcolor: #d35400; /* Darker shade of orange on hover */\r\r\n\t/* background-color: rgba(255, 140, 0, 0.1); */\r\r\n}"}]}, "hasDetails": true}]}