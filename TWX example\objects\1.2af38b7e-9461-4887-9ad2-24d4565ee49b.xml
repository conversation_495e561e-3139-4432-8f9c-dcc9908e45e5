<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2af38b7e-9461-4887-9ad2-24d4565ee49b" name="ClosureACT01 - Create ODC Closure Request">
        <lastModified>1700639854590</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.e4cf9de2-e5d0-4a03-a72b-72f1bbfb749c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:2890</guid>
        <versionId>2e4dd8db-74dc-496c-b70b-cbc0710d127c</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.db47cbbe-bc52-4d35-85bc-3b4582440df7"],"isInterrupting":true,"extensionElements":{"default":["2027.db47cbbe-bc52-4d35-85bc-3b4582440df7"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":96,"y":189,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"200e515a-b957-4921-aff9-69a90a6725de"},{"outgoing":["2027.fd00c9d6-b87d-4c67-879b-97657ffd0860","2027.ccd9b3c1-b1e1-4cfe-88af-c2abc75a06bc"],"incoming":["2027.12022b1e-21e0-44d1-8371-76819e0c9599","2027.1ed36109-9e62-4156-82b7-7164eab48e8c","2027.f6d87303-d60e-46a0-8d23-da3b41399154"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":675,"y":166,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":[]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Reversal_Closure_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9841f22a-86f7-4268-8614-2bf1b93c65b3","optionName":"@label","value":"Closure"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bd2e3bc4-8529-4902-8605-e79c399aca95","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7bf83568-3bcb-4ef3-82bd-38e54babfe85","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6d009428-8241-4149-85e2-2cd53c638677","optionName":"closureReasonVIS","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"61731a71-ba29-4f1b-8449-1e7e813b99be","optionName":"reversalReasonVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e8d70db9-50d0-478d-8acb-0fc5c064c794","optionName":"executionHubVIS","value":"None"}],"viewUUID":"64.f0c268ac-0772-4735-af5b-5fc6caec30a1","binding":"tw.local.odcRequest.ReversalReason","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f3e073ed-c0ad-41c6-87f4-65292ae6209b","version":"8550"},{"layoutItemId":"Basic_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"42154bb1-96c4-4467-868b-be64dd0c255a","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"01524c75-6877-4368-8da2-60699e351557","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0d38b59d-7b87-41d1-8ee6-7c4bfa733a7f","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"997d4efb-3bd1-46b1-8ab8-da50b4a8b20b","optionName":"parentRequestNoVis","value":"Editable"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e9d98baf-ffda-4545-8257-84e8b9bfa048","optionName":"basicDetailsCVVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"31048dd5-f08b-40f0-8eaf-01debaa636c9","optionName":"multiTenorDatesVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b335004e-bfae-4f6c-8862-5ac9249e5de6","optionName":"contractStageVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"29d7ccca-5fe1-40a5-8694-37211b896b45","optionName":"flexCubeContractNoVIS","value":"None"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5b59bbc5-2232-428a-8dd2-f209e742f171","version":"8550"},{"layoutItemId":"Customer_Information_cv1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"400f35d8-d666-4274-826a-84088157a98a","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3ecab61-d398-43e2-8763-608edc957a70","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3e75d7ab-b5d5-4cae-8d63-afedc3992216","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0c4fcf65-7902-4d6d-8faa-1d351aa26718","optionName":"customerInfoVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e85d3e3a-98d0-4cd4-851b-b2abe92d66a9","optionName":"listsVIS","value":"Readonly"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a6ee34b7-4a43-4bee-8068-31d9ac49f3cc","version":"8550"},{"layoutItemId":"Financial_Details_Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"986a83ea-c3a9-41cc-8cd6-9dce0b013ae8","optionName":"@label","value":"Financial Details Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2225c32b-c4f9-45db-80b1-f9ace81f8a38","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9a291085-38c2-4295-8e36-e399174cec83","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2e7240ae-aaec-4327-8bde-351259897ebd","optionName":"financialDetailsCVVis","value":"READONLY"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"*************-418c-8c59-2f200f40298c","optionName":"currencyDocAmountVIS","value":"READONLY"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c6a5e815-2dff-4a04-80c0-5677bd9eec81","version":"8550"},{"layoutItemId":"FC_Collections_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"31072334-b0e6-49a8-8688-64ae59f24a7f","optionName":"@label","value":"FC Collections"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8b6fe408-fe7c-4459-8628-f1a639faf0c3","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a3d07278-450d-45be-88df-aebc41e56178","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3f3a138c-30c9-4e9c-8217-659bb37fb483","optionName":"FCVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2fe7a6bd-09c6-4f2a-8bd3-505bd697e3a9","optionName":"retrieveBtnVis","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"774b2f54-738d-45a4-81dd-186a45ad8c0f","optionName":"addBtnVIS","value":"NONE"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0ce28cf1-c9bb-4101-84e1-0d23d6617296","optionName":"customerCif","value":"tw.local.odcRequest.cif"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0ff424a2-03c9-44a2-8d55-75620027777d","optionName":"requestCurrency","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"19a71149-3d91-4048-8834-0b382f275955","optionName":"negotiatedExchangeRateVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6772d1e0-a791-4fdf-8af6-af1463ef7b1d","optionName":"collectionCurrencyVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"104514bb-41e4-41ac-8bfe-4434165c6438","optionName":"activityType","value":"read"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","binding":"tw.local.odcRequest.FcCollections","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"735333e2-5935-40e7-8ee7-a1a95588cfe4","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"40b2ef0f-95a2-4779-8137-8588b9b30a7a","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cb58d0c7-17b3-46ca-8b17-e58f84350511","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"704bb9de-f79a-4c07-89e6-629c1824964c","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"025aeb5b-3c5a-45ec-8f6b-bd22863a782a","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"09884fe7-193d-4256-8d98-86da122092ed","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c230ed5b-5d1f-431a-817b-b3fcaad86d2d","optionName":"canDelete","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"35172dfd-1c25-4107-847e-479a124891d4","optionName":"canCreate","value":"true"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"fb5df2f3-d575-45f0-8ee4-8d016999ce24","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cf6073d4-58db-47a6-88b2-407e89e88cf3","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d6c5c1ef-95ad-4b95-8b21-029093f2c61a","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e00c08f1-db1c-4d51-8a58-5147d38c8e18","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"472fec1d-475b-45a7-8466-4d28b0a17cc7","optionName":"@visibility.script","value":"{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f861b053-5388-4b67-81c2-359444b4e4ed","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"25b8772e-4c35-40c5-851e-c979d0071c92"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"891b1d5b-a6d7-453e-8f42-5d0115baba38","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"11cc8956-ea45-41dc-8851-81c7880bd8f4","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b9e93053-4586-48e8-8344-233e3c169a35","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b58865b8-f05f-4dc6-8336-151563148a4d","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"D\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"2e30a2c2-068d-46c9-8998-35220b079d68","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"20ce8bfe-50ce-4797-839c-9507f89996b7"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b7e5213b-634e-47f9-8ce8-75100d423465","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c8bde732-d56b-4c21-8e86-ca26f00f2c57","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ad3856a7-1acc-468f-86dd-ed629fbae507","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1b114d75-2cd5-46ce-886c-1f5f4041a9e9","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7b0e3bda-298e-4e95-85f7-54ee84709d10","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"07ecca57-4b87-4b4b-8231-287cf262ab97","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a68d3710-01d5-45b3-82b9-1e4b9e2aa03a","optionName":"complianceApprovalVis","value":"None"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7b6826cf-b5dd-4ea1-83f7-f35ad7a5dc48","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"969e390c-6bec-4266-80b6-3d10cf811937","optionName":"actionConditions","value":"tw.local.actionConditions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b55875fd-f6ed-4a46-8003-f967da2187b9","optionName":"terminateReasonVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"83a1d023-4976-4ae9-8a20-b6e1d9f81655","optionName":"returnReasonVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"66ea5c98-7c1a-4a6d-82cc-1e10121b24f9","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f5582806-df01-4722-8ecc-0147dfea5493","optionName":"tradeFoCommentVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"619447e1-9383-4c5b-8f23-eefaee4669e5","optionName":"exeHubMkrCommentVis","value":"None"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"fcef938c-21dc-4e84-8205-8fbe2835cc17","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Create ODC ClosureRequest","isForCompensation":false,"completionQuantity":1,"id":"2025.c8658e03-60de-4fa8-a57f-6c90f4409008"},{"incoming":["2027.83b78791-0fad-4713-825b-d99c0b5e7e8d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1708,"y":188,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}],"preAssignmentScript":[]},"name":"End","declaredType":"endEvent","id":"86f229a8-00e0-4d37-a1e2-c66a900f865e"},{"outgoing":["2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a"],"incoming":["2027.775c28c6-fffb-4d36-8ae1-e084a4527d22"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":1340,"y":167,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a","name":"History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.role"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.b6a40d64-f76a-4b40-81f6-73e002c4da88","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"targetRef":"2025.80010558-7d54-476b-8eb7-356705b0b86c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To audited?","declaredType":"sequenceFlow","id":"2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a","sourceRef":"2025.b6a40d64-f76a-4b40-81f6-73e002c4da88"},{"startQuantity":1,"outgoing":["2027.775c28c6-fffb-4d36-8ae1-e084a4527d22"],"incoming":["2027.64d24d08-bef1-4886-8149-a9f6cff75ea0"],"default":"2027.775c28c6-fffb-4d36-8ae1-e084a4527d22","extensionElements":{"nodeVisualInfo":[{"width":95,"x":1162,"y":166,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Setting Status&amp;Substatus","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.6bae6255-8660-418f-895d-2f09c97c6e4c","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status   =\"Initiated\";\r\n\t\r\n\tif(tw.local.role == tw.epv.userRole.branch )\r\n\t{\r\n\t\ttw.local.odcRequest.appInfo.subStatus = \"Pending Review\";\r\n\t}\r\n\telse\r\n\t{\r\n\t\ttw.local.odcRequest.appInfo.subStatus  = \"Pending Review\";\r\n\t}\r\n\t\r\n\ttw.local.lastAction=  tw.epv.CreationActions.submitRequest;\r\n}\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"Initiated\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending Cancelation Confirmation\";\r\n\ttw.local.lastAction                   =tw.epv.CreationActions.cancelRequest;\r\n}\r\n"]}},{"targetRef":"2025.b6a40d64-f76a-4b40-81f6-73e002c4da88","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To History","declaredType":"sequenceFlow","id":"2027.775c28c6-fffb-4d36-8ae1-e084a4527d22","sourceRef":"2025.6bae6255-8660-418f-895d-2f09c97c6e4c"},{"startQuantity":1,"outgoing":["2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060"],"incoming":["2027.307f8a27-2a7d-4e33-80d8-4a559b87af02"],"default":"2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060","extensionElements":{"nodeVisualInfo":[{"width":95,"x":286,"y":166,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"init Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.41be52e1-c8e6-4e47-8545-24786aba6c2e","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.value;\r\n tw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.value;\r\n tw.local.odcRequest.BasicDetails.parentRequestNo = tw.local.odcRequest.parentRequestNo;\r\n \r\nif(tw.local.isFirstTime == false)\r\n{\r\ntw.local.odcRequest.stepLog ={};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.closureACT01;\r\n\r\ntw.local.actionConditions = {};\r\ntw.local.actionConditions.complianceApproval= false;\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT01;\r\n\r\n\ttw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT01;\r\n\t\/\/tw.local.odcRequest.appInfo.instanceID = tw.system.processInstance.id;\r\n\tif(tw.local.routingDetails.branchCode != \"\" &amp;&amp; tw.local.routingDetails.branchCode != null)\r\n\t{\r\n\t\ttw.local.role = tw.epv.userRole.branch;\r\n\t}\r\n\telse\r\n\t{\r\n\t\ttw.local.role = tw.epv.userRole.hub;\r\n\t}\r\n\r\n}\r\n\r\nelse\r\n{\r\n\tvar date = new Date();\r\n\ttw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\/' +(date.getMonth() + 1) + '\/' + date.getFullYear();\r\n\t\r\n\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\n\ttw.local.odcRequest.appInfo.requestName = \"ODC Closure\";\r\n\r\n\ttw.local.odcRequest.appInfo.status =\"Initiated\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Initiated\";\r\n\ttw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT01;\r\n\ttw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;\r\n\tif(tw.local.routingDetails.branchCode != \"\" &amp;&amp; tw.local.routingDetails.branchCode != null)\r\n\t{\r\n\t\ttw.local.role = tw.epv.userRole.branch;\r\n\t}\r\n\telse\r\n\t{\r\n\t\ttw.local.role = tw.epv.userRole.hub;\r\n\t}\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Undergoing closure\";\r\n\t\r\n\ttw.local.odcRequest.stepLog ={};\r\n\ttw.local.odcRequest.stepLog.startTime = new Date();\r\n\ttw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.closureACT01;\/\/\"Create ODC Closure Request \u2013 \u0627\u0646\u0634\u0627\u0621 \u0637\u0644\u0628 \u0627\u0642\u0641\u0627\u0644 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631 \";\r\n\t\r\n\ttw.local.actionConditions = {};\r\n\ttw.local.actionConditions.complianceApproval= false;\r\n\ttw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT01;\r\n}\t"]}},{"targetRef":"2025.77a1270f-579b-4401-8b2d-5e76d7f9cd36","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create ODC ClosureRequest","declaredType":"sequenceFlow","id":"2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060","sourceRef":"2025.41be52e1-c8e6-4e47-8545-24786aba6c2e"},{"startQuantity":1,"outgoing":["2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e"],"incoming":["2027.ccd9b3c1-b1e1-4cfe-88af-c2abc75a06bc"],"default":"2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":861,"y":166,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.685e3dba-1d37-4fae-8394-0d6dc3364e3b","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false;\r\n \r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\n\r\n\/\/if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)\r\n\/\/{\r\n\tmandatory(tw.local.odcRequest.ReversalReason.closureReason, \"tw.local.odcRequest.ReversalReason.closureReason\");\r\n\tmaxLength(tw.local.odcRequest.ReversalReason.closureReason , \"tw.local.odcRequest.ReversalReason.closureReason\", 160 , \"Shouldn't be more than 160 character\" , \"Closure Reason: \" + \"Shouldn't be more than 160 character\" );\r\n\r\n\t\r\n\t\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\t"]}},{"outgoing":["2027.64d24d08-bef1-4886-8149-a9f6cff75ea0","2027.588f4a71-a9ad-463f-81a4-636100b917d1"],"incoming":["2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e"],"default":"2027.588f4a71-a9ad-463f-81a4-636100b917d1","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1022,"y":186,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.065eaafb-78de-4d56-876a-7a731b27aa49"},{"targetRef":"2025.065eaafb-78de-4d56-876a-7a731b27aa49","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e","sourceRef":"2025.685e3dba-1d37-4fae-8394-0d6dc3364e3b"},{"targetRef":"2025.6bae6255-8660-418f-895d-2f09c97c6e4c","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.64d24d08-bef1-4886-8149-a9f6cff75ea0","sourceRef":"2025.065eaafb-78de-4d56-876a-7a731b27aa49"},{"incoming":["2027.588f4a71-a9ad-463f-81a4-636100b917d1"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1003,"y":312,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.8af130b0-aacb-462a-8cd8-160548f553d1"},{"targetRef":"2025.8af130b0-aacb-462a-8cd8-160548f553d1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.588f4a71-a9ad-463f-81a4-636100b917d1","sourceRef":"2025.065eaafb-78de-4d56-876a-7a731b27aa49"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.bf5f7fee-e8bc-4430-847e-cac7e8abca68"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"role","isCollection":false,"declaredType":"dataObject","id":"2056.5e9d3a25-fb66-49ff-8810-283ba802c1d6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.862f1958-c721-49a7-8e30-8c7e5b073fc4"},{"outgoing":["2027.12022b1e-21e0-44d1-8371-76819e0c9599"],"incoming":["2027.fd00c9d6-b87d-4c67-879b-97657ffd0860"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.12022b1e-21e0-44d1-8371-76819e0c9599"],"nodeVisualInfo":[{"width":24,"x":675,"y":15,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.723e5c41-5929-4fd4-8fa9-bd2936ea27a0"},{"targetRef":"2025.723e5c41-5929-4fd4-8fa9-bd2936ea27a0","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"c3b17497-2027-4f38-8f27-baf940fcd9ff","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page 1","declaredType":"sequenceFlow","id":"2027.fd00c9d6-b87d-4c67-879b-97657ffd0860","sourceRef":"2025.c8658e03-60de-4fa8-a57f-6c90f4409008"},{"targetRef":"2025.685e3dba-1d37-4fae-8394-0d6dc3364e3b","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"5b13c369-dd1d-4e82-8069-620c2255b2fe","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validation Script","declaredType":"sequenceFlow","id":"2027.ccd9b3c1-b1e1-4cfe-88af-c2abc75a06bc","sourceRef":"2025.c8658e03-60de-4fa8-a57f-6c90f4409008"},{"targetRef":"2025.c8658e03-60de-4fa8-a57f-6c90f4409008","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create ODC ClosureRequest","declaredType":"sequenceFlow","id":"2027.12022b1e-21e0-44d1-8371-76819e0c9599","sourceRef":"2025.723e5c41-5929-4fd4-8fa9-bd2936ea27a0"},{"startQuantity":1,"outgoing":["2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b"],"default":"2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b","extensionElements":{"nodeVisualInfo":[{"width":95,"x":230,"y":289,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Attachments","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.bc71d33b-6109-487c-8486-58d3038a27ac","scriptFormat":"text\/x-javascript","script":{"content":["\/\/tw.local.odcRequest.attachmentDetails =  {};\r\nif(!tw.local.odcRequest.attachmentDetails)\r\n  tw.local.odcRequest.attachmentDetails = {};\r\nif (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {\r\n    \r\n\ttw.local.odcRequest.attachmentDetails.attachment = []; \r\n\t\r\n}\t\r\n\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"Customer Request\";\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"Customer Request\";\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"\u0637\u0644\u0628 \u0627\u0644\u0639\u0645\u064a\u0644\" ;\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[1] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[1].name = \"BILL OF EXCHANGE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[1].description = \"BILL OF EXCHANGE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[1].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[2] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[2].name = \"Invoice\";\r\ntw.local.odcRequest.attachmentDetails.attachment[2].description = \"Invoice\";\r\ntw.local.odcRequest.attachmentDetails.attachment[2].arabicName = \"\u0641\u0627\u062a\u0648\u0631\u0629\" ;\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[3] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[3].name = \"BILL OF LADING\";\r\ntw.local.odcRequest.attachmentDetails.attachment[3].description = \"BILL OF LADING\";\r\ntw.local.odcRequest.attachmentDetails.attachment[3].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[4] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[4].name = \"AIRWAY BILL\";\r\ntw.local.odcRequest.attachmentDetails.attachment[4].description = \"AIRWAY BILL\";\r\ntw.local.odcRequest.attachmentDetails.attachment[4].arabicName = \"\" ;\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[5] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[5].name = \"TRUCK CONSIGNMENT NOTE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[5].description =\"TRUCK CONSIGNMENT NOTE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[5].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[6] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[6].name = \"N\/N BILL OF LADING\";\r\ntw.local.odcRequest.attachmentDetails.attachment[6].description = \"N\/N BILL OF LADING\";\r\ntw.local.odcRequest.attachmentDetails.attachment[6].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[7] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[7].name = \"COURIER \/ POST RECEIPT\";\r\ntw.local.odcRequest.attachmentDetails.attachment[7].description = \"COURIER \/ POST RECEIPT\";\r\ntw.local.odcRequest.attachmentDetails.attachment[7].arabicName = \"\" ;\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[8] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[8].name = \"PACKING LIST\";\r\ntw.local.odcRequest.attachmentDetails.attachment[8].description = \"PACKING LIST\";\r\ntw.local.odcRequest.attachmentDetails.attachment[8].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[9] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[9].name = \"CERTIFICATE OF ORIGIN\";\r\ntw.local.odcRequest.attachmentDetails.attachment[9].description = \"CERTIFICATE OF ORIGIN\";\r\ntw.local.odcRequest.attachmentDetails.attachment[9].arabicName = \"\" ;\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[10] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[10].name = \"CERTIFICATE OF ANALYSIS\";\r\ntw.local.odcRequest.attachmentDetails.attachment[10].description = \"CERTIFICATE OF ANALYSIS\";\r\ntw.local.odcRequest.attachmentDetails.attachment[10].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[11] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[11].name = \"INSURANCE POLICY \/ CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[11].description = \"INSURANCE POLICY \/ CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[11].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[12] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[12].name = \"BENEFECIARY DECLARATION\";\r\ntw.local.odcRequest.attachmentDetails.attachment[12].description = \"BENEFECIARY DECLARATION\";\r\ntw.local.odcRequest.attachmentDetails.attachment[12].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[13] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[13].name = \"NON RADIOACTIVE CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[13].description = \"NON RADIOACTIVE CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[13].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[14] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[14].name = \"PHYTOSANITARY CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[14].description = \"PHYTOSANITARY CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[14].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[15] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[15].name = \"CERTIFICATE OF ANALYSIS\";\r\ntw.local.odcRequest.attachmentDetails.attachment[15].description = \"Bill of exchange\/draft\";\r\ntw.local.odcRequest.attachmentDetails.attachment[15].arabicName = \"\u0627\u0644\u0643\u0645\u0628\u064a\u0627\u0644\u0629\" ;\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[16] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[16].name = \"HEALTH CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[16].description = \"HEALTH CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[16].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[17] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[17].name = \"INSPECTION CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[17].description = \"INSPECTION CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[17].arabicName = \"\";;\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[18] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[18].name = \"WARRANTY CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[18].description = \"WARRANTY CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[18].arabicName = \"\";\r\n\r\n\r\ntw.local.odcRequest.attachmentDetails.attachment[19] = {};\r\ntw.local.odcRequest.attachmentDetails.attachment[19].name = \"TEST CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[19].description = \"TEST CERTIFICATE\";\r\ntw.local.odcRequest.attachmentDetails.attachment[19].arabicName = \"\";\r\n\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = {};\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};\r\n"]}},{"targetRef":"2025.a67095bb-daee-431d-8587-245274d2e7f7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create ODC ClosureRequest","declaredType":"sequenceFlow","id":"2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b","sourceRef":"2025.bc71d33b-6109-487c-8486-58d3038a27ac"},{"outgoing":["2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f"],"incoming":["2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":361,"y":289,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f","name":"set document default property","dataInputAssociation":[{"targetRef":"2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.CustomerInfo.cif"]}}]},{"targetRef":"2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.appRef"]}}]},{"targetRef":"2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}]},{"targetRef":"2055.6f216377-34de-4f5e-8ab5-adc2796733ee","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.CustomerInfo.customerName"]}}]},{"targetRef":"2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.system.user.fullName"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.a67095bb-daee-431d-8587-245274d2e7f7","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.eac829db-66fe-43f5-810c-6faa514533a2","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties"]}}],"sourceRef":["2055.81cce609-b3fe-4f11-809f-c3a599908595"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.a1997cf3-423a-4552-89fc-573430917816"]}],"calledElement":"1.2d3ab562-82df-48a5-9de7-f5d964218191"},{"outgoing":["2027.1ed36109-9e62-4156-82b7-7164eab48e8c"],"incoming":["2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f"],"extensionElements":{"postAssignmentScript":["tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;\/\/ tw.local.parentPath\r\ntw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;"],"nodeVisualInfo":[{"width":95,"x":489,"y":289,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.1ed36109-9e62-4156-82b7-7164eab48e8c","name":"Create Filenet folder","dataInputAssociation":[{"targetRef":"2055.4156964b-1c67-40bc-8f62-3804c71cf908","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.e2ce0eed-342c-4942-8214-83e964b550e5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.code"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.8d9b55f8-135e-442f-823a-8ad93ae763b7","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.fullPath"]}}],"sourceRef":["2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderID"]}}],"sourceRef":["2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.214c7268-80d0-444d-8702-dd0d5462dbe7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.676e3a06-e2cc-4855-84d6-6f82a350500a"]}],"calledElement":"1.46b984a3-b4ad-405a-abd3-8631f907efe4"},{"targetRef":"2025.8d9b55f8-135e-442f-823a-8ad93ae763b7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Filenet folder","declaredType":"sequenceFlow","id":"2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f","sourceRef":"2025.a67095bb-daee-431d-8587-245274d2e7f7"},{"targetRef":"2025.c8658e03-60de-4fa8-a57f-6c90f4409008","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Create ODC ClosureRequest","declaredType":"sequenceFlow","id":"2027.1ed36109-9e62-4156-82b7-7164eab48e8c","sourceRef":"2025.8d9b55f8-135e-442f-823a-8ad93ae763b7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fullPath","isCollection":false,"declaredType":"dataObject","id":"2056.e1dd6fb4-0f74-4831-8655-9751f49d1f7b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"code","isCollection":false,"declaredType":"dataObject","id":"2056.f62ddea9-715f-4a6d-8418-1c062f9bb1b5"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.07efcd3c-c58c-498e-8373-c322a6750c61"},{"outgoing":["2027.307f8a27-2a7d-4e33-80d8-4a559b87af02"],"incoming":["2027.db47cbbe-bc52-4d35-85bc-3b4582440df7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":162,"y":166,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.307f8a27-2a7d-4e33-80d8-4a559b87af02","name":"Get RequestNo","dataInputAssociation":[{"targetRef":"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.parentRequestNo"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.4b2dc63a-c255-480c-8027-f55203a24fc3","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}],"sourceRef":["2055.29fabc80-90b8-4cad-81e3-1c319c6f595a"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.4ec4b61d-5cd9-43b6-827c-c1801162373f"]}],"calledElement":"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546"},{"targetRef":"2025.41be52e1-c8e6-4e47-8545-24786aba6c2e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.307f8a27-2a7d-4e33-80d8-4a559b87af02","sourceRef":"2025.4b2dc63a-c255-480c-8027-f55203a24fc3"},{"outgoing":["2027.83b78791-0fad-4713-825b-d99c0b5e7e8d","2027.8a35599e-9cbf-4be8-8ac7-66f880051cec"],"incoming":["2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a"],"default":"2027.8a35599e-9cbf-4be8-8ac7-66f880051cec","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1470,"y":186,"declaredType":"TNodeVisualInfo","height":32}]},"name":"audited?","declaredType":"exclusiveGateway","id":"2025.80010558-7d54-476b-8eb7-356705b0b86c"},{"targetRef":"86f229a8-00e0-4d37-a1e2-c66a900f865e","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.83b78791-0fad-4713-825b-d99c0b5e7e8d","sourceRef":"2025.80010558-7d54-476b-8eb7-356705b0b86c"},{"incoming":["2027.8a35599e-9cbf-4be8-8ac7-66f880051cec"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1544,"y":238,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 2","declaredType":"intermediateThrowEvent","id":"2025.7b94cd4f-60d7-48ea-8ccc-abc1120d4641"},{"targetRef":"2025.7b94cd4f-60d7-48ea-8ccc-abc1120d4641","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage==null||tw.local.errorMessage==\"\")"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.8a35599e-9cbf-4be8-8ac7-66f880051cec","sourceRef":"2025.80010558-7d54-476b-8eb7-356705b0b86c"},{"targetRef":"2025.4b2dc63a-c255-480c-8027-f55203a24fc3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get RequestNo","declaredType":"sequenceFlow","id":"2027.db47cbbe-bc52-4d35-85bc-3b4582440df7","sourceRef":"200e515a-b957-4921-aff9-69a90a6725de"},{"outgoing":["2027.33391659-17e8-4c29-87b9-ac4670d31ac1"],"incoming":["2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":405,"y":166,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.33391659-17e8-4c29-87b9-ac4670d31ac1","name":"Set ECM default properties","dataInputAssociation":[{"targetRef":"2055.399c7a58-00b5-4451-9813-41c0b9652088","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.CustomerInfo.cif"]}}]},{"targetRef":"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}]},{"targetRef":"2055.25394215-074f-4b79-8e84-9a96d32cc83b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.77a1270f-579b-4401-8b2d-5e76d7f9cd36","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails"]}}],"sourceRef":["2055.7d269650-ee48-4101-80db-2807cf921562"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.0261e8ad-a540-4682-88c5-87dff3eab23c"]}],"calledElement":"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8"},{"outgoing":["2027.f6d87303-d60e-46a0-8d23-da3b41399154"],"incoming":["2027.33391659-17e8-4c29-87b9-ac4670d31ac1"],"extensionElements":{"mode":["InvokeService"],"postAssignmentScript":["tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;\/\/ tw.local.parentPath\r\ntw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;\r\n\r\n\/\/if(!!tw.local.error &amp;&amp; tw.local.error.errorText!=null)\r\n\/\/\ttw.local.errorMessage+= tw.local.error.errorText;"],"nodeVisualInfo":[{"width":95,"x":524,"y":166,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.f6d87303-d60e-46a0-8d23-da3b41399154","name":"Create ECM Folder","dataInputAssociation":[{"targetRef":"2055.4156964b-1c67-40bc-8f62-3804c71cf908","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.e2ce0eed-342c-4942-8214-83e964b550e5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.code"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.409b9718-4904-43bc-80e6-605201848cd0","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderID"]}}],"sourceRef":["2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}],"sourceRef":["2055.5f955245-0538-4e40-80a6-12f45c3102f3"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.214c7268-80d0-444d-8702-dd0d5462dbe7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.fullPath"]}}],"sourceRef":["2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.676e3a06-e2cc-4855-84d6-6f82a350500a"]}],"calledElement":"1.46b984a3-b4ad-405a-abd3-8631f907efe4"},{"targetRef":"2025.409b9718-4904-43bc-80e6-605201848cd0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create ECM Folder","declaredType":"sequenceFlow","id":"2027.33391659-17e8-4c29-87b9-ac4670d31ac1","sourceRef":"2025.77a1270f-579b-4401-8b2d-5e76d7f9cd36"},{"targetRef":"2025.c8658e03-60de-4fa8-a57f-6c90f4409008","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create ODC ClosureRequest","declaredType":"sequenceFlow","id":"2027.f6d87303-d60e-46a0-8d23-da3b41399154","sourceRef":"2025.409b9718-4904-43bc-80e6-605201848cd0"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"c01b6acd-c912-4121-a5f4-52848e1af1a6","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"599138f6-9d1a-437c-bbf6-389b49ffb597","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"ClosureACT01 - Create ODC Closure Request","declaredType":"globalUserTask","id":"1.2af38b7e-9461-4887-9ad2-24d4565ee49b","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.f98fed95-51fc-473c-8003-aeb193a5d3df"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.1498704b-0db6-4050-868a-d35f1d79314c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.fd93251a-76e2-4304-8758-8af89440122b"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.4ba81f45-279e-42be-811b-85dacffc78d2"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"1bae564a-5797-45fa-8998-032b20a0175d","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"9fb2391e-4e3c-4c14-8e3b-627fa72c460e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.93c5c002-7ac4-4283-83ee-63b8662f9223","epvProcessLinkId":"df9e90f1-2429-4685-89f3-9bb53e928f71","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"9507625e-e9ea-44cd-89b7-c74a991caf52","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"907e6df8-efe2-448a-824d-bd1bd9747d89","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"7cbca2bc-2272-44cd-8b47-fa4b35f44ed9","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"_9d1aebb6-691e-4cd7-827d-df6fbe49a24f"}],"outputSet":[{"id":"_8613e1a1-bc35-4da4-ba92-eaf1610558b2"}],"dataInput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.365895d0-a5ae-49f2-86d1-de0ac795fbe9"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.hubCode = \"077\";\nautoObject.branchCode = \"\";\nautoObject.initiatorUser = \"\";\nautoObject.branchName = \"\";\nautoObject.hubName = \"\";\nautoObject.branchSeq = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","name":"routingDetails","isCollection":false,"id":"2055.6fa94dca-e752-4ace-8264-0d551b9ec5bd"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isFirstTime","isCollection":false,"id":"2055.5b04a5b6-755b-4702-8c66-d9a4455f4598"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.872cc9b3-563d-4875-81d4-07416c1f8697"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"093399e7-c2ed-4776-a61e-2694ebe1e854"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.365895d0-a5ae-49f2-86d1-de0ac795fbe9</processParameterId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6bc6ac4c-8359-4d33-bd80-d5efdfcfab07</guid>
            <versionId>54a970af-7fda-40fe-a6d7-50d9fb20e177</versionId>
        </processParameter>
        <processParameter name="routingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6fa94dca-e752-4ace-8264-0d551b9ec5bd</processParameterId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f4c93604-b1b0-4077-9389-8f02d45fbd8e</guid>
            <versionId>53c3782e-ae00-41d8-aae2-b81126782921</versionId>
        </processParameter>
        <processParameter name="isFirstTime">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5b04a5b6-755b-4702-8c66-d9a4455f4598</processParameterId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4d73bdc2-6732-487c-841d-8816bf34e358</guid>
            <versionId>ed8dbe7b-f91e-40b0-9f4a-fa6149417c55</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.872cc9b3-563d-4875-81d4-07416c1f8697</processParameterId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7f9346eb-adce-455d-aaf4-00e6391189a3</guid>
            <versionId>fc651c01-3f82-4a2f-9d65-8c3507f263a9</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f98fed95-51fc-473c-8003-aeb193a5d3df</processParameterId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5407fc1d-da96-4b7b-a9b4-912887fc4a64</guid>
            <versionId>547b3141-b658-4d27-8a43-28c615552f78</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1498704b-0db6-4050-868a-d35f1d79314c</processParameterId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a4390fb3-d7ba-4734-89a3-1bf6d7b27f34</guid>
            <versionId>4953263b-85d6-4090-9041-f36e09434a2c</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fd93251a-76e2-4304-8758-8af89440122b</processParameterId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2c16d105-62d2-4451-91b2-dc09c1252235</guid>
            <versionId>3fa0684e-6698-4d68-ae9d-084c3155464b</versionId>
        </processParameter>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bf5f7fee-e8bc-4430-847e-cac7e8abca68</processVariableId>
            <description isNull="true" />
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f636aa3e-b8f7-400e-856a-e449748dcbf7</guid>
            <versionId>8fb93ca2-2335-4f66-9024-4f37e3c51b1d</versionId>
        </processVariable>
        <processVariable name="role">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5e9d3a25-fb66-49ff-8810-283ba802c1d6</processVariableId>
            <description isNull="true" />
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e04a4359-bf3d-4236-bacf-3c09fa65083e</guid>
            <versionId>3c5252a4-f8cb-46e4-a5e0-638fc3a5a1a4</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.862f1958-c721-49a7-8e30-8c7e5b073fc4</processVariableId>
            <description isNull="true" />
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>093eee0a-ce3e-4beb-8d4d-af64b8378901</guid>
            <versionId>1b12e218-e3e5-4f4f-b587-cd02cc57f61d</versionId>
        </processVariable>
        <processVariable name="fullPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e1dd6fb4-0f74-4831-8655-9751f49d1f7b</processVariableId>
            <description isNull="true" />
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5aad0471-d131-40e8-b5f3-9f7bd0424c2b</guid>
            <versionId>e1578a08-3de2-45ab-b2bf-6d8e2ebf407a</versionId>
        </processVariable>
        <processVariable name="code">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f62ddea9-715f-4a6d-8418-1c062f9bb1b5</processVariableId>
            <description isNull="true" />
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>88e06910-774a-45e5-974c-c177cd978f8f</guid>
            <versionId>42d56be1-109b-400e-8204-c34a59d74574</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.07efcd3c-c58c-498e-8373-c322a6750c61</processVariableId>
            <description isNull="true" />
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f8a270f1-12ab-4471-b791-48295d4075cf</guid>
            <versionId>a04a606d-1218-4740-b7ee-20a175a9854e</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a67095bb-daee-431d-8587-245274d2e7f7</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>set document default property</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.67fc5acb-6e5d-4dc7-84d4-edcd6601c588</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a9fddf040a0e47ab:-5b64a0cf:18a7e1fab29:310f</guid>
            <versionId>042c0fab-55c2-432c-8c8a-199907acf5a5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.67fc5acb-6e5d-4dc7-84d4-edcd6601c588</subProcessId>
                <attachedProcessRef>/1.2d3ab562-82df-48a5-9de7-f5d964218191</attachedProcessRef>
                <guid>b57aedb5-1a94-4702-bec5-8b878f3f39e5</guid>
                <versionId>01b3d70f-4212-4bc7-ba77-ee655f8d704f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.77a1270f-579b-4401-8b2d-5e76d7f9cd36</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>Set ECM default properties</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.fed9f5fb-68eb-47d1-bf57-90a137c679d2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-38bd</guid>
            <versionId>0e522e29-3095-4ac5-8050-aa69b054fda8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.fed9f5fb-68eb-47d1-bf57-90a137c679d2</subProcessId>
                <attachedProcessRef>/1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</attachedProcessRef>
                <guid>72148327-34fc-411e-b336-1438a86706ed</guid>
                <versionId>63a53446-fb03-4a66-8c97-f5be2c5da802</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.409b9718-4904-43bc-80e6-605201848cd0</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>Create ECM Folder</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.cf9e1e29-1d04-4f74-b340-7b4f0fbae181</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-38bc</guid>
            <versionId>4f6a9666-6b1a-4b64-a09e-b219697508e5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.cf9e1e29-1d04-4f74-b340-7b4f0fbae181</subProcessId>
                <attachedProcessRef>/1.46b984a3-b4ad-405a-abd3-8631f907efe4</attachedProcessRef>
                <guid>cde0d5d6-208e-44b3-855e-05cd9df5385f</guid>
                <versionId>51aee8ca-805a-4165-93df-48280ca9e324</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4b2dc63a-c255-480c-8027-f55203a24fc3</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>Get RequestNo</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4e31d925-5a71-4f76-9e55-cc831babd491</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-6f1</guid>
            <versionId>8b273298-6974-4562-97fb-3811e40e991d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4e31d925-5a71-4f76-9e55-cc831babd491</subProcessId>
                <attachedProcessRef>/1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</attachedProcessRef>
                <guid>f8e41007-f0bb-49fd-8592-c33b53591193</guid>
                <versionId>1fef6a99-6ede-44d7-84f0-8c17b33ad7e9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b6a40d64-f76a-4b40-81f6-73e002c4da88</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.288982ce-d5a4-47fa-852b-d55467c32f10</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:2b95</guid>
            <versionId>98012f57-6b76-46d3-afa8-07d5c7571ac3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.288982ce-d5a4-47fa-852b-d55467c32f10</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>66d36ff7-fdb9-4bb6-8a95-7d8bd6014fb3</guid>
                <versionId>853c0471-e41a-4e3d-99c8-66962e3addbc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e4cf9de2-e5d0-4a03-a72b-72f1bbfb749c</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.b344019e-f80d-4af9-889c-5a649222edac</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:2892</guid>
            <versionId>b9b12f92-0896-4ec8-9ec3-f4c2664b925f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.10c5daa6-02b9-44b4-b5ce-d89e10a5ac15</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.d8ea1177-1b7a-4ed0-9623-4b0add5ed727</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a60b4a587:2891</guid>
            <versionId>cc4eac61-fff2-410a-bc0e-5bdae4b8fa3d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.d8ea1177-1b7a-4ed0-9623-4b0add5ed727</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>33ce12bd-dead-42ed-b038-8bed498ae574</guid>
                <versionId>609046f9-904c-494e-9afd-5c241ac30b3d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8d9b55f8-135e-442f-823a-8ad93ae763b7</processItemId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <name>Create Filenet folder</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.46f0dc7a-0ced-4b58-94a2-fe23d6b05450</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a9fddf040a0e47ab:-5b64a0cf:18a7e1fab29:3110</guid>
            <versionId>d4a6c3df-43f6-43e1-a2a8-6ae0cade1ee8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.46f0dc7a-0ced-4b58-94a2-fe23d6b05450</subProcessId>
                <attachedProcessRef>/1.46b984a3-b4ad-405a-abd3-8631f907efe4</attachedProcessRef>
                <guid>81f5962d-4ec9-447c-ab2c-c20c7fd9dd5a</guid>
                <versionId>a86691c5-d7e6-45ce-b859-deaa54022cfb</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.ba9537e4-59c1-45ff-a306-700d21f1c3de</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <guid>bac7b1a5-f013-4b71-a0c9-7678f7bfe5a6</guid>
            <versionId>1fa2c106-bc5f-43c9-b8b5-e15c13662d28</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.4ec6f057-fbe7-4b10-98d0-c341c6da2626</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <guid>28347d8b-2d0b-437a-a9ab-d3c06f381143</guid>
            <versionId>3ae7a582-d15e-4f96-b9fe-a9aa9c779935</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.7a8c06a8-8e4f-4a68-84e5-96040e377240</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <guid>29557328-59f3-4402-9c02-b8d10eb0521e</guid>
            <versionId>3c2b2057-133c-459f-9a60-c450aa741d08</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.c1114ab9-4d67-4488-82e1-ac40e2f2f117</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <guid>78171b5e-b620-4758-b1aa-fec9fb088402</guid>
            <versionId>57843f80-b291-4f03-a7fd-17b0a071b230</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.8f63c1e8-3da8-4863-b24a-03689df66799</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <guid>27f19242-64f9-4020-a636-96ef25d728f5</guid>
            <versionId>ac409952-a55d-48f6-b46b-be543fb67503</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.ac8f373e-105b-4979-b66c-15af373ad52e</epvProcessLinkId>
            <epvId>/21.93c5c002-7ac4-4283-83ee-63b8662f9223</epvId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <guid>3d910087-73d7-481d-98dd-cabe9136e91d</guid>
            <versionId>d354c6a5-0a4a-45dc-8da5-e3856c33bd40</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.e46faa48-ac06-47b4-ad01-347e9d490092</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <guid>fb923bad-e3c6-4a7e-96da-6c943003a57c</guid>
            <versionId>7b92e5c4-ba4f-4158-811f-5c5a933bae72</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.e4cf9de2-e5d0-4a03-a72b-72f1bbfb749c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="093399e7-c2ed-4776-a61e-2694ebe1e854" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="ClosureACT01 - Create ODC Closure Request" id="1.2af38b7e-9461-4887-9ad2-24d4565ee49b">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="599138f6-9d1a-437c-bbf6-389b49ffb597">
                            
                            
                            <ns16:startEvent name="Start" id="200e515a-b957-4921-aff9-69a90a6725de">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="96" y="189" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:default>2027.db47cbbe-bc52-4d35-85bc-3b4582440df7</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.db47cbbe-bc52-4d35-85bc-3b4582440df7</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns3:formTask name="Create ODC ClosureRequest" id="2025.c8658e03-60de-4fa8-a57f-6c90f4409008">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                    
                                    <ns13:nodeVisualInfo x="675" y="166" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.12022b1e-21e0-44d1-8371-76819e0c9599</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.1ed36109-9e62-4156-82b7-7164eab48e8c</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.f6d87303-d60e-46a0-8d23-da3b41399154</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.fd00c9d6-b87d-4c67-879b-97657ffd0860</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.ccd9b3c1-b1e1-4cfe-88af-c2abc75a06bc</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>fcef938c-21dc-4e84-8205-8fbe2835cc17</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b7e5213b-634e-47f9-8ce8-75100d423465</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>c8bde732-d56b-4c21-8e86-ca26f00f2c57</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>ad3856a7-1acc-468f-86dd-ed629fbae507</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>1b114d75-2cd5-46ce-886c-1f5f4041a9e9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7b0e3bda-298e-4e95-85f7-54ee84709d10</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>07ecca57-4b87-4b4b-8231-287cf262ab97</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>a68d3710-01d5-45b3-82b9-1e4b9e2aa03a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>7b6826cf-b5dd-4ea1-83f7-f35ad7a5dc48</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>969e390c-6bec-4266-80b6-3d10cf811937</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b55875fd-f6ed-4a46-8003-f967da2187b9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>83a1d023-4976-4ae9-8a20-b6e1d9f81655</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>returnReasonVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>66ea5c98-7c1a-4a6d-82cc-1e10121b24f9</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f5582806-df01-4722-8ecc-0147dfea5493</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>619447e1-9383-4c5b-8f23-eefaee4669e5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>20ce8bfe-50ce-4797-839c-9507f89996b7</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>2e30a2c2-068d-46c9-8998-35220b079d68</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>891b1d5b-a6d7-453e-8f42-5d0115baba38</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>11cc8956-ea45-41dc-8851-81c7880bd8f4</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b9e93053-4586-48e8-8344-233e3c169a35</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>b58865b8-f05f-4dc6-8336-151563148a4d</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>25b8772e-4c35-40c5-851e-c979d0071c92</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>f3e073ed-c0ad-41c6-87f4-65292ae6209b</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Reversal_Closure_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9841f22a-86f7-4268-8614-2bf1b93c65b3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Closure</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bd2e3bc4-8529-4902-8605-e79c399aca95</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>7bf83568-3bcb-4ef3-82bd-38e54babfe85</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6d009428-8241-4149-85e2-2cd53c638677</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>closureReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>61731a71-ba29-4f1b-8449-1e7e813b99be</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>reversalReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e8d70db9-50d0-478d-8acb-0fc5c064c794</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>executionHubVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f0c268ac-0772-4735-af5b-5fc6caec30a1</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.ReversalReason</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>5b59bbc5-2232-428a-8dd2-f209e742f171</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>42154bb1-96c4-4467-868b-be64dd0c255a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>01524c75-6877-4368-8da2-60699e351557</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0d38b59d-7b87-41d1-8ee6-7c4bfa733a7f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>997d4efb-3bd1-46b1-8ab8-da50b4a8b20b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Editable</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e9d98baf-ffda-4545-8257-84e8b9bfa048</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>31048dd5-f08b-40f0-8eaf-01debaa636c9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b335004e-bfae-4f6c-8862-5ac9249e5de6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>29d7ccca-5fe1-40a5-8694-37211b896b45</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>a6ee34b7-4a43-4bee-8068-31d9ac49f3cc</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information_cv1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>400f35d8-d666-4274-826a-84088157a98a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a3ecab61-d398-43e2-8763-608edc957a70</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3e75d7ab-b5d5-4cae-8d63-afedc3992216</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0c4fcf65-7902-4d6d-8faa-1d351aa26718</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e85d3e3a-98d0-4cd4-851b-b2abe92d66a9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>c6a5e815-2dff-4a04-80c0-5677bd9eec81</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details_Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>986a83ea-c3a9-41cc-8cd6-9dce0b013ae8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2225c32b-c4f9-45db-80b1-f9ace81f8a38</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9a291085-38c2-4295-8e36-e399174cec83</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2e7240ae-aaec-4327-8bde-351259897ebd</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>*************-418c-8c59-2f200f40298c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>735333e2-5935-40e7-8ee7-a1a95588cfe4</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>FC_Collections_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>31072334-b0e6-49a8-8688-64ae59f24a7f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>FC Collections</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8b6fe408-fe7c-4459-8628-f1a639faf0c3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a3d07278-450d-45be-88df-aebc41e56178</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3f3a138c-30c9-4e9c-8217-659bb37fb483</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>FCVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>2fe7a6bd-09c6-4f2a-8bd3-505bd697e3a9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>retrieveBtnVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>NONE</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>774b2f54-738d-45a4-81dd-186a45ad8c0f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addBtnVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>NONE</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0ce28cf1-c9bb-4101-84e1-0d23d6617296</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerCif</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.cif</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0ff424a2-03c9-44a2-8d55-75620027777d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>requestCurrency</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>19a71149-3d91-4048-8834-0b382f275955</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>negotiatedExchangeRateVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6772d1e0-a791-4fdf-8af6-af1463ef7b1d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>collectionCurrencyVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>104514bb-41e4-41ac-8bfe-4434165c6438</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>activityType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>read</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FcCollections</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>fb5df2f3-d575-45f0-8ee4-8d016999ce24</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>40b2ef0f-95a2-4779-8137-8588b9b30a7a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cb58d0c7-17b3-46ca-8b17-e58f84350511</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>704bb9de-f79a-4c07-89e6-629c1824964c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>025aeb5b-3c5a-45ec-8f6b-bd22863a782a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>09884fe7-193d-4256-8d98-86da122092ed</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c230ed5b-5d1f-431a-817b-b3fcaad86d2d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>35172dfd-1c25-4107-847e-479a124891d4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>true</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>f861b053-5388-4b67-81c2-359444b4e4ed</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cf6073d4-58db-47a6-88b2-407e89e88cf3</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d6c5c1ef-95ad-4b95-8b21-029093f2c61a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e00c08f1-db1c-4d51-8a58-5147d38c8e18</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>472fec1d-475b-45a7-8466-4d28b0a17cc7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"@class":"com.ibm.bpm.coachNG.visibility.VisibilityRules","rules":[{"@class":"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule","var_conditions":[{"timeInMs":null,"var":"tw.local.isFirstTime","operand":true,"operator":0}],"action":"NONE"}],"defaultAction":"DEFAULT"}</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:endEvent name="End" id="86f229a8-00e0-4d37-a1e2-c66a900f865e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1708" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.83b78791-0fad-4713-825b-d99c0b5e7e8d</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" default="2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a" name="History" id="2025.b6a40d64-f76a-4b40-81f6-73e002c4da88">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1340" y="167" width="95" height="70" />
                                    
                                    
                                    <ns3:postAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.775c28c6-fffb-4d36-8ae1-e084a4527d22</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.role</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.b6a40d64-f76a-4b40-81f6-73e002c4da88" targetRef="2025.80010558-7d54-476b-8eb7-356705b0b86c" name="To audited?" id="2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.775c28c6-fffb-4d36-8ae1-e084a4527d22" name="Setting Status&amp;Substatus" id="2025.6bae6255-8660-418f-895d-2f09c97c6e4c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1162" y="166" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.64d24d08-bef1-4886-8149-a9f6cff75ea0</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.775c28c6-fffb-4d36-8ae1-e084a4527d22</ns16:outgoing>
                                
                                
                                <ns16:script>if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status   ="Initiated";&#xD;
	&#xD;
	if(tw.local.role == tw.epv.userRole.branch )&#xD;
	{&#xD;
		tw.local.odcRequest.appInfo.subStatus = "Pending Review";&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		tw.local.odcRequest.appInfo.subStatus  = "Pending Review";&#xD;
	}&#xD;
	&#xD;
	tw.local.lastAction=  tw.epv.CreationActions.submitRequest;&#xD;
}&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="Initiated";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Pending Cancelation Confirmation";&#xD;
	tw.local.lastAction                   =tw.epv.CreationActions.cancelRequest;&#xD;
}&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6bae6255-8660-418f-895d-2f09c97c6e4c" targetRef="2025.b6a40d64-f76a-4b40-81f6-73e002c4da88" name="To History" id="2027.775c28c6-fffb-4d36-8ae1-e084a4527d22">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060" name="init Script" id="2025.41be52e1-c8e6-4e47-8545-24786aba6c2e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="286" y="166" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.307f8a27-2a7d-4e33-80d8-4a559b87af02</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060</ns16:outgoing>
                                
                                
                                <ns16:script> tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.value;&#xD;
 tw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.value;&#xD;
 tw.local.odcRequest.BasicDetails.parentRequestNo = tw.local.odcRequest.parentRequestNo;&#xD;
 &#xD;
if(tw.local.isFirstTime == false)&#xD;
{&#xD;
tw.local.odcRequest.stepLog ={};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.closureACT01;&#xD;
&#xD;
tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.complianceApproval= false;&#xD;
tw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT01;&#xD;
&#xD;
	tw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT01;&#xD;
	//tw.local.odcRequest.appInfo.instanceID = tw.system.processInstance.id;&#xD;
	if(tw.local.routingDetails.branchCode != "" &amp;&amp; tw.local.routingDetails.branchCode != null)&#xD;
	{&#xD;
		tw.local.role = tw.epv.userRole.branch;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		tw.local.role = tw.epv.userRole.hub;&#xD;
	}&#xD;
&#xD;
}&#xD;
&#xD;
else&#xD;
{&#xD;
	var date = new Date();&#xD;
	tw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();&#xD;
	&#xD;
	tw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+"( "+ tw.system.user.name+")";&#xD;
	tw.local.odcRequest.appInfo.requestName = "ODC Closure";&#xD;
&#xD;
	tw.local.odcRequest.appInfo.status ="Initiated";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Initiated";&#xD;
	tw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT01;&#xD;
	tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;&#xD;
	if(tw.local.routingDetails.branchCode != "" &amp;&amp; tw.local.routingDetails.branchCode != null)&#xD;
	{&#xD;
		tw.local.role = tw.epv.userRole.branch;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		tw.local.role = tw.epv.userRole.hub;&#xD;
	}&#xD;
	tw.local.odcRequest.BasicDetails.requestState = "Undergoing closure";&#xD;
	&#xD;
	tw.local.odcRequest.stepLog ={};&#xD;
	tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
	tw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.closureACT01;//"Create ODC Closure Request – انشاء طلب اقفال تحصيل مستندى تصدير ";&#xD;
	&#xD;
	tw.local.actionConditions = {};&#xD;
	tw.local.actionConditions.complianceApproval= false;&#xD;
	tw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT01;&#xD;
}	</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.41be52e1-c8e6-4e47-8545-24786aba6c2e" targetRef="2025.77a1270f-579b-4401-8b2d-5e76d7f9cd36" name="To Create ODC ClosureRequest" id="2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e" name="validation Script" id="2025.685e3dba-1d37-4fae-8394-0d6dc3364e3b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="861" y="166" width="95" height="70" color="#FF7782" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.ccd9b3c1-b1e1-4cfe-88af-c2abc75a06bc</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e</ns16:outgoing>
                                
                                
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false;&#xD;
 &#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");&#xD;
&#xD;
//if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)&#xD;
//{&#xD;
	mandatory(tw.local.odcRequest.ReversalReason.closureReason, "tw.local.odcRequest.ReversalReason.closureReason");&#xD;
	maxLength(tw.local.odcRequest.ReversalReason.closureReason , "tw.local.odcRequest.ReversalReason.closureReason", 160 , "Shouldn't be more than 160 character" , "Closure Reason: " + "Shouldn't be more than 160 character" );&#xD;
&#xD;
	&#xD;
	&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
	</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:exclusiveGateway default="2027.588f4a71-a9ad-463f-81a4-636100b917d1" name="Valid?" id="2025.065eaafb-78de-4d56-876a-7a731b27aa49">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1022" y="186" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.64d24d08-bef1-4886-8149-a9f6cff75ea0</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.588f4a71-a9ad-463f-81a4-636100b917d1</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.685e3dba-1d37-4fae-8394-0d6dc3364e3b" targetRef="2025.065eaafb-78de-4d56-876a-7a731b27aa49" name="To Valid?" id="2027.0e1f39ab-4902-42fd-8ac7-7bbb94dab08e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.065eaafb-78de-4d56-876a-7a731b27aa49" targetRef="2025.6bae6255-8660-418f-895d-2f09c97c6e4c" name="Yes" id="2027.64d24d08-bef1-4886-8149-a9f6cff75ea0">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.8af130b0-aacb-462a-8cd8-160548f553d1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1003" y="312" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.588f4a71-a9ad-463f-81a4-636100b917d1</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.065eaafb-78de-4d56-876a-7a731b27aa49" targetRef="2025.8af130b0-aacb-462a-8cd8-160548f553d1" name="No" id="2027.588f4a71-a9ad-463f-81a4-636100b917d1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.bf5f7fee-e8bc-4430-847e-cac7e8abca68" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="role" id="2056.5e9d3a25-fb66-49ff-8810-283ba802c1d6" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.862f1958-c721-49a7-8e30-8c7e5b073fc4" />
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.723e5c41-5929-4fd4-8fa9-bd2936ea27a0">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="675" y="15" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.12022b1e-21e0-44d1-8371-76819e0c9599</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.fd00c9d6-b87d-4c67-879b-97657ffd0860</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.12022b1e-21e0-44d1-8371-76819e0c9599</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.c8658e03-60de-4fa8-a57f-6c90f4409008" targetRef="2025.723e5c41-5929-4fd4-8fa9-bd2936ea27a0" name="To Stay on page 1" id="2027.fd00c9d6-b87d-4c67-879b-97657ffd0860">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="c3b17497-2027-4f38-8f27-baf940fcd9ff">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.c8658e03-60de-4fa8-a57f-6c90f4409008" targetRef="2025.685e3dba-1d37-4fae-8394-0d6dc3364e3b" name="To validation Script" id="2027.ccd9b3c1-b1e1-4cfe-88af-c2abc75a06bc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="5b13c369-dd1d-4e82-8069-620c2255b2fe">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.723e5c41-5929-4fd4-8fa9-bd2936ea27a0" targetRef="2025.c8658e03-60de-4fa8-a57f-6c90f4409008" name="To Create ODC ClosureRequest" id="2027.12022b1e-21e0-44d1-8371-76819e0c9599">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b" name="Set Attachments" id="2025.bc71d33b-6109-487c-8486-58d3038a27ac">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="230" y="289" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b</ns16:outgoing>
                                
                                
                                <ns16:script>//tw.local.odcRequest.attachmentDetails =  {};&#xD;
if(!tw.local.odcRequest.attachmentDetails)&#xD;
  tw.local.odcRequest.attachmentDetails = {};&#xD;
if (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {&#xD;
    &#xD;
	tw.local.odcRequest.attachmentDetails.attachment = []; &#xD;
	&#xD;
}	&#xD;
&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].name = "Customer Request";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].description = "Customer Request";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[0].arabicName = "طلب العميل" ;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[1] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[1].name = "BILL OF EXCHANGE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[1].description = "BILL OF EXCHANGE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[1].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[2] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[2].name = "Invoice";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[2].description = "Invoice";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[2].arabicName = "فاتورة" ;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[3] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[3].name = "BILL OF LADING";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[3].description = "BILL OF LADING";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[3].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[4] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[4].name = "AIRWAY BILL";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[4].description = "AIRWAY BILL";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[4].arabicName = "" ;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[5] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[5].name = "TRUCK CONSIGNMENT NOTE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[5].description ="TRUCK CONSIGNMENT NOTE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[5].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[6] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[6].name = "N/N BILL OF LADING";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[6].description = "N/N BILL OF LADING";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[6].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[7] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[7].name = "COURIER / POST RECEIPT";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[7].description = "COURIER / POST RECEIPT";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[7].arabicName = "" ;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[8] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[8].name = "PACKING LIST";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[8].description = "PACKING LIST";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[8].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[9] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[9].name = "CERTIFICATE OF ORIGIN";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[9].description = "CERTIFICATE OF ORIGIN";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[9].arabicName = "" ;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[10] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[10].name = "CERTIFICATE OF ANALYSIS";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[10].description = "CERTIFICATE OF ANALYSIS";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[10].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[11] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[11].name = "INSURANCE POLICY / CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[11].description = "INSURANCE POLICY / CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[11].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[12] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[12].name = "BENEFECIARY DECLARATION";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[12].description = "BENEFECIARY DECLARATION";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[12].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[13] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[13].name = "NON RADIOACTIVE CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[13].description = "NON RADIOACTIVE CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[13].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[14] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[14].name = "PHYTOSANITARY CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[14].description = "PHYTOSANITARY CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[14].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[15] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[15].name = "CERTIFICATE OF ANALYSIS";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[15].description = "Bill of exchange/draft";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[15].arabicName = "الكمبيالة" ;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[16] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[16].name = "HEALTH CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[16].description = "HEALTH CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[16].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[17] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[17].name = "INSPECTION CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[17].description = "INSPECTION CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[17].arabicName = "";;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[18] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[18].name = "WARRANTY CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[18].description = "WARRANTY CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[18].arabicName = "";&#xD;
&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.attachment[19] = {};&#xD;
tw.local.odcRequest.attachmentDetails.attachment[19].name = "TEST CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[19].description = "TEST CERTIFICATE";&#xD;
tw.local.odcRequest.attachmentDetails.attachment[19].arabicName = "";&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties = {};&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.bc71d33b-6109-487c-8486-58d3038a27ac" targetRef="2025.a67095bb-daee-431d-8587-245274d2e7f7" name="To Create ODC ClosureRequest" id="2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.2d3ab562-82df-48a5-9de7-f5d964218191" default="2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f" name="set document default property" id="2025.a67095bb-daee-431d-8587-245274d2e7f7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="361" y="289" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.04a224a0-e88c-4b26-8db8-2a6308a41a7b</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.CustomerInfo.cif</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.appRef</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.6f216377-34de-4f5e-8ab5-adc2796733ee</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.CustomerInfo.customerName</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.system.user.fullName</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.81cce609-b3fe-4f11-809f-c3a599908595</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.eac829db-66fe-43f5-810c-6faa514533a2">tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.a1997cf3-423a-4552-89fc-573430917816</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:callActivity calledElement="1.46b984a3-b4ad-405a-abd3-8631f907efe4" default="2027.1ed36109-9e62-4156-82b7-7164eab48e8c" name="Create Filenet folder" id="2025.8d9b55f8-135e-442f-823a-8ad93ae763b7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="489" y="289" width="95" height="70" />
                                    
                                    
                                    <ns3:postAssignmentScript>tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;// tw.local.parentPath&#xD;
tw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;</ns3:postAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.1ed36109-9e62-4156-82b7-7164eab48e8c</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.4156964b-1c67-40bc-8f62-3804c71cf908</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.e2ce0eed-342c-4942-8214-83e964b550e5</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.code</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fullPath</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.folderID</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.214c7268-80d0-444d-8702-dd0d5462dbe7</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.676e3a06-e2cc-4855-84d6-6f82a350500a</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a67095bb-daee-431d-8587-245274d2e7f7" targetRef="2025.8d9b55f8-135e-442f-823a-8ad93ae763b7" name="To Create Filenet folder" id="2027.4a0fd571-fcd9-4edb-8edd-c45045d9be4f">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8d9b55f8-135e-442f-823a-8ad93ae763b7" targetRef="2025.c8658e03-60de-4fa8-a57f-6c90f4409008" name="To Create ODC ClosureRequest" id="2027.1ed36109-9e62-4156-82b7-7164eab48e8c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="fullPath" id="2056.e1dd6fb4-0f74-4831-8655-9751f49d1f7b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="code" id="2056.f62ddea9-715f-4a6d-8418-1c062f9bb1b5" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.07efcd3c-c58c-498e-8373-c322a6750c61" />
                            
                            
                            <ns16:callActivity calledElement="1.509c6ef8-f489-4ab8-bcb8-45c4531aa546" default="2027.307f8a27-2a7d-4e33-80d8-4a559b87af02" name="Get RequestNo" id="2025.4b2dc63a-c255-480c-8027-f55203a24fc3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="162" y="166" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.db47cbbe-bc52-4d35-85bc-3b4582440df7</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.307f8a27-2a7d-4e33-80d8-4a559b87af02</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.e1ca7465-4084-405d-8ea6-ab3f3762de92</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.parentRequestNo</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.29fabc80-90b8-4cad-81e3-1c319c6f595a</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.4ec4b61d-5cd9-43b6-827c-c1801162373f</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.4b2dc63a-c255-480c-8027-f55203a24fc3" targetRef="2025.41be52e1-c8e6-4e47-8545-24786aba6c2e" name="To Coach" id="2027.307f8a27-2a7d-4e33-80d8-4a559b87af02">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.8a35599e-9cbf-4be8-8ac7-66f880051cec" name="audited?" id="2025.80010558-7d54-476b-8eb7-356705b0b86c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1470" y="186" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f1c4af05-e54b-4c1a-8038-7e89f821fc9a</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.83b78791-0fad-4713-825b-d99c0b5e7e8d</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.8a35599e-9cbf-4be8-8ac7-66f880051cec</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.80010558-7d54-476b-8eb7-356705b0b86c" targetRef="86f229a8-00e0-4d37-a1e2-c66a900f865e" name="yes" id="2027.83b78791-0fad-4713-825b-d99c0b5e7e8d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 2" id="2025.7b94cd4f-60d7-48ea-8ccc-abc1120d4641">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1544" y="238" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8a35599e-9cbf-4be8-8ac7-66f880051cec</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.80010558-7d54-476b-8eb7-356705b0b86c" targetRef="2025.7b94cd4f-60d7-48ea-8ccc-abc1120d4641" name="no" id="2027.8a35599e-9cbf-4be8-8ac7-66f880051cec">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage==null||tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="200e515a-b957-4921-aff9-69a90a6725de" targetRef="2025.4b2dc63a-c255-480c-8027-f55203a24fc3" name="To Get RequestNo" id="2027.db47cbbe-bc52-4d35-85bc-3b4582440df7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.33391659-17e8-4c29-87b9-ac4670d31ac1" name="Set ECM default properties" id="2025.77a1270f-579b-4401-8b2d-5e76d7f9cd36">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="405" y="166" width="95" height="70" />
                                    
                                    
                                    <ns3:mode>InvokeService</ns3:mode>
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5fa53371-8ad7-4daa-8f01-9b54c5c03060</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.33391659-17e8-4c29-87b9-ac4670d31ac1</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.399c7a58-00b5-4451-9813-41c0b9652088</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.CustomerInfo.cif</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.c28023fb-b45e-4b63-ae36-97e6df6421bc</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.25394215-074f-4b79-8e84-9a96d32cc83b</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.7d269650-ee48-4101-80db-2807cf921562</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9">tw.local.odcRequest.attachmentDetails</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.0261e8ad-a540-4682-88c5-87dff3eab23c</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:callActivity calledElement="1.46b984a3-b4ad-405a-abd3-8631f907efe4" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.f6d87303-d60e-46a0-8d23-da3b41399154" name="Create ECM Folder" id="2025.409b9718-4904-43bc-80e6-605201848cd0">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="524" y="166" width="95" height="70" />
                                    
                                    
                                    <ns3:mode>InvokeService</ns3:mode>
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                    
                                    <ns3:postAssignmentScript>tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;// tw.local.parentPath&#xD;
tw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;&#xD;
&#xD;
//if(!!tw.local.error &amp;&amp; tw.local.error.errorText!=null)&#xD;
//	tw.local.errorMessage+= tw.local.error.errorText;</ns3:postAssignmentScript>
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.33391659-17e8-4c29-87b9-ac4670d31ac1</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.f6d87303-d60e-46a0-8d23-da3b41399154</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.4156964b-1c67-40bc-8f62-3804c71cf908</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.e2ce0eed-342c-4942-8214-83e964b550e5</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.code</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.folderID</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.5f955245-0538-4e40-80a6-12f45c3102f3</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.214c7268-80d0-444d-8702-dd0d5462dbe7</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fullPath</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.676e3a06-e2cc-4855-84d6-6f82a350500a</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.77a1270f-579b-4401-8b2d-5e76d7f9cd36" targetRef="2025.409b9718-4904-43bc-80e6-605201848cd0" name="To Create ECM Folder" id="2027.33391659-17e8-4c29-87b9-ac4670d31ac1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.409b9718-4904-43bc-80e6-605201848cd0" targetRef="2025.c8658e03-60de-4fa8-a57f-6c90f4409008" name="To Create ODC ClosureRequest" id="2027.f6d87303-d60e-46a0-8d23-da3b41399154">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="c01b6acd-c912-4121-a5f4-52848e1af1a6">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.4ba81f45-279e-42be-811b-85dacffc78d2</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="1bae564a-5797-45fa-8998-032b20a0175d" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="9fb2391e-4e3c-4c14-8e3b-627fa72c460e" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.93c5c002-7ac4-4283-83ee-63b8662f9223" epvProcessLinkId="df9e90f1-2429-4685-89f3-9bb53e928f71" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="9507625e-e9ea-44cd-89b7-c74a991caf52" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="907e6df8-efe2-448a-824d-bd1bd9747d89" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="7cbca2bc-2272-44cd-8b47-fa4b35f44ed9" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.365895d0-a5ae-49f2-86d1-de0ac795fbe9" />
                        
                        
                        <ns16:dataInput name="routingDetails" itemSubjectRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727" isCollection="false" id="2055.6fa94dca-e752-4ace-8264-0d551b9ec5bd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.hubCode = "077";
autoObject.branchCode = "";
autoObject.initiatorUser = "";
autoObject.branchName = "";
autoObject.hubName = "";
autoObject.branchSeq = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="isFirstTime" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.5b04a5b6-755b-4702-8c66-d9a4455f4598" />
                        
                        
                        <ns16:dataInput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.872cc9b3-563d-4875-81d4-07416c1f8697" />
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.f98fed95-51fc-473c-8003-aeb193a5d3df" />
                        
                        
                        <ns16:dataOutput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1498704b-0db6-4050-868a-d35f1d79314c" />
                        
                        
                        <ns16:dataOutput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.fd93251a-76e2-4304-8758-8af89440122b" />
                        
                        
                        <ns16:inputSet id="_9d1aebb6-691e-4cd7-827d-df6fbe49a24f" />
                        
                        
                        <ns16:outputSet id="_8613e1a1-bc35-4da4-ba92-eaf1610558b2" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bad2c69f-**************-22ec8e57f62d</processLinkId>
            <processId>1.2af38b7e-9461-4887-9ad2-24d4565ee49b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e4cf9de2-e5d0-4a03-a72b-72f1bbfb749c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.10c5daa6-02b9-44b4-b5ce-d89e10a5ac15</toProcessItemId>
            <guid>613a0ccc-d6ad-483c-a1e0-31ab91010400</guid>
            <versionId>94373c97-0cf3-4492-a38c-ee9349cfcd02</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.e4cf9de2-e5d0-4a03-a72b-72f1bbfb749c</fromProcessItemId>
            <toProcessItemId>2025.10c5daa6-02b9-44b4-b5ce-d89e10a5ac15</toProcessItemId>
        </link>
    </process>
</teamworks>

