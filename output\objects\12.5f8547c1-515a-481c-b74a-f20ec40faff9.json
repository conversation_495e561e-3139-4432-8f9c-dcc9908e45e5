{"id": "12.5f8547c1-515a-481c-b74a-f20ec40faff9", "versionId": "c23098df-71e9-4add-9e4a-203c60e154f7", "name": "ODCRequest", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.5f8547c1-515a-481c-b74a-f20ec40faff9", "name": "ODCRequest", "lastModified": "1700592716612", "lastModifiedBy": "heba", "classId": "12.5f8547c1-515a-481c-b74a-f20ec40faff9", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/NBEODCR\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{}],\"shadow\":[false]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"initiator\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"initiator\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"requestNature\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"requestNature\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}NameValuePair\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"requestType\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"requestType\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}NameValuePair\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"cif\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"cif\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"customerName\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"customerName\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"parentRequestNo\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"parentRequestNo\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"requestDate\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"requestDate\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Date\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ImporterName\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"ImporterName\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"appInfo\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"appInfo\",\"type\":\"{http:\\/\\/NBEC}AppInfo\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"CustomerInfo\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"CustomerInfo\",\"type\":\"{http:\\/\\/NBEODCR}CustomerInfo\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"BasicDetails\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"BasicDetails\",\"type\":\"{http:\\/\\/NBEODCR}BasicDetails\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.ba753452-90b9-4866-9471-8425f722be03\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"GeneratedDocumentInfo\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"GeneratedDocumentInfo\",\"type\":\"{http:\\/\\/NBEODCR}GeneratedDocumentInfo\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.1974aaf6-f654-42f2-b5ef-93386ef5d51e\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"FinancialDetailsBR\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"FinancialDetailsBR\",\"type\":\"{http:\\/\\/NBEODCR}FinancialDetailsBranch\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.d2a537b9-92c2-48c5-a18c-72e499761454\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"FcCollections\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"FcCollections\",\"type\":\"{http:\\/\\/NBEODCR}FCCollections\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.88fbc33e-4ee3-4815-9620-f04917f7a3a2\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"FinancialDetailsFO\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"FinancialDetailsFO\",\"type\":\"{http:\\/\\/NBEODCR}FinancialDetailsFO\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.65f0e3ad-27ed-4e8c-9713-56c6ccbffff5\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ImporterDetails\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"ImporterDetails\",\"type\":\"{http:\\/\\/NBEODCR}ImporterDetails\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.53a1b1c3-7342-40f0-9f89-24bf1d5ea7cf\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ProductShipmentDetails\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"ProductShipmentDetails\",\"type\":\"{http:\\/\\/NBEODCR}ProductShipmentDetails\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.aad95560-c6ad-4add-bae6-8a1b432b6545\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"OdcCollection\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"OdcCollection\",\"type\":\"{http:\\/\\/NBEODCR}ODCCollection\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.6caeaaf8-3886-4b02-bf7a-25371ece2294\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ReversalReason\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"ReversalReason\",\"type\":\"{http:\\/\\/NBEODCR}ReversalReason\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.cd8189aa-0888-4e27-9937-95dfb001a822\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ContractCreation\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"ContractCreation\",\"type\":\"{http:\\/\\/NBEODCR}ContractCreation\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.80dacdbe-03aa-474f-9fb6-3a43c32fef58\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"Parties\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"Parties\",\"type\":\"{http:\\/\\/NBEODCR}odcParties\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.cbf7e7d2-cb5c-4667-889f-9f05b7c947ce\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ChargesAndCommissions\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"ChargesAndCommissions\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/NBEODCR}ChargesAndCommissions\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.e3018bad-b453-4bf5-96fd-09a5141cd061\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ContractLiquidation\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"ContractLiquidation\",\"type\":\"{http:\\/\\/NBEODCR}ContractLiquidation\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.29905e05-3ca0-49e0-96a6-a1035efdf052\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"complianceApproval\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"complianceApproval\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Boolean\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"stepLog\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"stepLog\",\"type\":\"{http:\\/\\/NBEODCR}StepLog\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.e4654440-58a7-47b2-8f98-3eaa9cccad49\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"actions\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"actions\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"attachmentDetails\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"attachmentDetails\",\"type\":\"{http:\\/\\/NBEODCR}attachmentDetails\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.55bb335a-d3b3-4749-a082-859e2a48ace9\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"complianceComments\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"complianceComments\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/NBEODCR}StepLog\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.e4654440-58a7-47b2-8f98-3eaa9cccad49\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"History\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"History\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/NBEODCR}StepLog\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.e4654440-58a7-47b2-8f98-3eaa9cccad49\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"documentSource\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"documentSource\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}NameValuePair\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"folderID\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"folderID\",\"type\":\"{http:\\/\\/CIT}ECMID\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.3212d5b3-f692-41b3-a893-343dc5c3df01\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"isLiquidated\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"isLiquidated\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Boolean\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"requestNo\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"requestNo\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"folderPath\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"folderPath\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"templateDocID\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"templateDocID\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"requestID\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"requestID\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Integer\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"customerAndPartyAccountList\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"customerAndPartyAccountList\",\"maxOccurs\":\"unbounded\",\"type\":\"{http:\\/\\/NBEINTT}Account\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\"}},{\"annotation\":{\"documentation\":[{\"content\":[\"\\/\\/TradeFo and comp Comment\"]}],\"appinfo\":[{\"propertyName\":[\"tradeFoComment\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"tradeFoComment\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"exeHubMkrComment\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"exeHubMkrComment\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"compcheckerComment\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"compcheckerComment\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}}]},\"name\":\"ODCRequest\"}],\"id\":\"_12.5f8547c1-515a-481c-b74a-f20ec40faff9\"}", "description": {"isNull": "true"}, "guid": "guid:d694a63221635d5b:6baf87c4:1896e72cf4d:5b7", "versionId": "c23098df-71e9-4add-9e4a-203c60e154f7", "definition": {"property": [{"name": "initiator", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "requestNature", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "requestType", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "cif", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "customerName", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "parentRequestNo", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "requestDate", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ImporterName", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "appInfo", "description": {"isNull": "true"}, "classRef": "341e5684-f12d-4cf6-aac8-d2dc77d648ab/12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "CustomerInfo", "description": {"isNull": "true"}, "classRef": "/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "BasicDetails", "description": {"isNull": "true"}, "classRef": "/12.ba753452-90b9-4866-9471-8425f722be03", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "GeneratedDocumentInfo", "description": {"isNull": "true"}, "classRef": "/12.1974aaf6-f654-42f2-b5ef-93386ef5d51e", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "FinancialDetailsBR", "description": {"isNull": "true"}, "classRef": "/12.d2a537b9-92c2-48c5-a18c-72e499761454", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "FcCollections", "description": {"isNull": "true"}, "classRef": "/12.88fbc33e-4ee3-4815-9620-f04917f7a3a2", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "FinancialDetailsFO", "description": {"isNull": "true"}, "classRef": "/12.65f0e3ad-27ed-4e8c-9713-56c6ccbffff5", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ImporterDetails", "description": {"isNull": "true"}, "classRef": "/12.53a1b1c3-7342-40f0-9f89-24bf1d5ea7cf", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ProductShipmentDetails", "description": {"isNull": "true"}, "classRef": "/12.aad95560-c6ad-4add-bae6-8a1b432b6545", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "OdcCollection", "description": {"isNull": "true"}, "classRef": "/12.6caeaaf8-3886-4b02-bf7a-25371ece2294", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ReversalReason", "description": {"isNull": "true"}, "classRef": "/12.cd8189aa-0888-4e27-9937-95dfb001a822", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ContractCreation", "description": {"isNull": "true"}, "classRef": "/12.80dacdbe-03aa-474f-9fb6-3a43c32fef58", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "Parties", "description": {"isNull": "true"}, "classRef": "/12.cbf7e7d2-cb5c-4667-889f-9f05b7c947ce", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ChargesAndCommissions", "description": {"isNull": "true"}, "classRef": "/12.e3018bad-b453-4bf5-96fd-09a5141cd061", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ContractLiquidation", "description": {"isNull": "true"}, "classRef": "/12.29905e05-3ca0-49e0-96a6-a1035efdf052", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "complianceApproval", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "stepLog", "description": {"isNull": "true"}, "classRef": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "actions", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "attachmentDetails", "description": {"isNull": "true"}, "classRef": "/12.55bb335a-d3b3-4749-a082-859e2a48ace9", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "complianceComments", "description": {"isNull": "true"}, "classRef": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "History", "description": {"isNull": "true"}, "classRef": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "documentSource", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "folderID", "description": {"isNull": "true"}, "classRef": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "isLiquidated", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "requestNo", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "folderPath", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "templateDocID", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "requestID", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "customerAndPartyAccountList", "description": {"isNull": "true"}, "classRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "arrayProperty": "true", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "tradeFoComment", "description": "//TradeFo and comp Comment", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "exeHubMkrComment", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "compcheckerComment", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}], "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "ODCRequest", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}