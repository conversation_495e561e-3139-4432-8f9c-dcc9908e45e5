{"id": "64.77f06102-fddf-42a1-9b6b-6580b729871c", "versionId": "2c6b6d88-a3e8-4b28-9aa5-e87623af52eb", "name": "DC Templete 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "require([\r\r\n    \"dojo/_base/lang\", \r\r\n    \"com.ibm.bpm.coach/engine\"\r\r\n], function (lang, engine) {\r\r\n\r\r\n    // Function to get the coach data\r\r\n    function getCoachData() {\r\r\n        var coachData = null;\r\r\n\t\r\r\n\t  console.log(\"<<<<ENGINE>>>\");\r\r\n        console.dir(engine.GlobalAssets);\r\r\n        \r\r\n        console.log(\"\");\r\r\n        if (engine && engine.coachView) {\r\r\n            coachData = engine.coachView.getCoachData();\r\r\n        }\r\r\n\r\r\n        if (!coachData) {\r\r\n            console.log(\"Coach data not found\");\r\r\n            return null;\r\r\n        }\r\r\n\r\r\n        return coachData;\r\r\n    }\r\r\n\r\r\n    // Example usage\r\r\n    var data = getCoachData();\r\r\n    console.log(\"Coach Data:\", data);\r\r\n});", "bindingType": "appinfo", "configOptions": ["stepLog", "action", "selectedAction", "complianceApproval", "complianceApprovalVis", "errorMsg", "terminateReasonVIS", "errorPanelVIS", "data", "conditions"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.setActions = function () {\r\r\nvar complianceApproval= true;\r\r\n//\tthis.ui.get(\"retrieveRequest\").setData(false);\r\r\n\tthis.ui.get(\"\")\r\r\n\r\r\n}"}]}, "hasDetails": true}