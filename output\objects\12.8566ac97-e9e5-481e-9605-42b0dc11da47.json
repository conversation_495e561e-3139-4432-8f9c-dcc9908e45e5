{"id": "12.8566ac97-e9e5-481e-9605-42b0dc11da47", "versionId": "841603be-bbfc-45e7-a27a-72b8b1fc5b25", "name": "sequence", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.8566ac97-e9e5-481e-9605-42b0dc11da47", "name": "sequence", "lastModified": "1691679743843", "lastModifiedBy": "heba", "classId": "12.8566ac97-e9e5-481e-9605-42b0dc11da47", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.8566ac97-e9e5-481e-9605-42b0dc11da47", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/NBEODCR\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{}],\"shadow\":[false]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"Request_Number\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"Request_Number\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Integer\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\"}}]},\"name\":\"sequence\"}],\"id\":\"_12.8566ac97-e9e5-481e-9605-42b0dc11da47\"}", "description": {"isNull": "true"}, "guid": "guid:fef170a08f25d496:5466e087:189df5f8551:-155b", "versionId": "841603be-bbfc-45e7-a27a-72b8b1fc5b25", "definition": {"property": {"name": "Request_Number", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "sequence", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}