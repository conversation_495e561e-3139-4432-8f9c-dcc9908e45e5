{"id": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "versionId": "2ab775de-cfd8-4ef4-84c3-78d836c0426f", "name": "Get account transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "Get account transactions", "lastModified": "*************", "lastModifiedBy": "<PERSON><PERSON>", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "2025.08c0b8cd-ebfd-4b99-82fd-cc0307e7001d"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eef37169:-5f44", "versionId": "2ab775de-cfd8-4ef4-84c3-78d836c0426f", "dependencySummary": "<dependencySummary id=\"bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-58e0\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.c07510e2-a54a-4610-8eb5-63b7709d42d6\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"829fe9b7-ca68-44b8-8210-9d1511c1c233\"},{\"incoming\":[\"0a8be54e-21d6-4bac-8d4d-398c9f0fa2a7\",\"474a40ed-ce3f-457d-8242-69b6184ee508\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1073,\"y\":131,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:6ad7eb4224455a46:11f23e39:189eef37169:-5f42\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a\"},{\"targetRef\":\"08c0b8cd-ebfd-4b99-82fd-cc0307e7001d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To MW_FC get account transactions\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c07510e2-a54a-4610-8eb5-63b7709d42d6\",\"sourceRef\":\"829fe9b7-ca68-44b8-8210-9d1511c1c233\"},{\"startQuantity\":1,\"outgoing\":[\"16f9f7e7-eb3b-47f4-8ac8-65cea6c0b4ae\"],\"incoming\":[\"2027.c07510e2-a54a-4610-8eb5-63b7709d42d6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":196,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"MW_FC get account transactions\",\"dataInputAssociation\":[{\"targetRef\":\"2055.6845ae83-c123-4104-859c-fc7937e46cc6\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.prefix\"]}}]},{\"targetRef\":\"2055.1e298ed1-f38a-4e6c-8c0e-d4e31abca2ea\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.accountBranch\"]}}]},{\"targetRef\":\"2055.f6d334a0-4ad4-4b9f-837d-b2bea698081e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.accountNumber\"]}}]},{\"targetRef\":\"2055.db603b6d-7548-4bb7-8b31-764aabfa24ab\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.fromDate\"]}}]},{\"targetRef\":\"2055.79fc8a2e-ef51-435f-859b-1c46a99267e6\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.toDate\"]}}]},{\"targetRef\":\"2055.********-487a-40d2-86a8-2722487e79d5\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user_id\"]}}]},{\"targetRef\":\"2055.437055a6-034a-4fce-8ddd-e05b44da5a54\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.currentProcessInstanceID\"]}}]},{\"targetRef\":\"2055.62f1b194-0c75-498f-825f-f0d9ca326f88\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.37cfc79c-d060-461a-8b19-67392d2a090c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.73e8f907-20d1-4d22-8f9c-b019f734a110\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"ODC Creation \\/ Amendment Process Details\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"08c0b8cd-ebfd-4b99-82fd-cc0307e7001d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.56c0867b-ca4e-4017-8a44-034da2f642a5\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorCode\"]}}],\"sourceRef\":[\"2055.0d9138e6-4851-498b-8103-8d09bbb15c65\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.d845fc49-3a52-40cf-867f-b488750411d2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.22c9a740-5d76-4ed9-b6c0-1cc1dc179061\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.MWtransactions\"]}}],\"sourceRef\":[\"2055.8935ec09-9ec9-4351-89e9-82fc37d5f181\"]}],\"calledElement\":\"1.be206b9e-752b-4175-a4c6-f5757b8bfe55\"},{\"targetRef\":\"eb9f079b-064d-4754-8bd8-ee6c6d6f7561\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:6ad7eb4224455a46:11f23e39:189eae0bd83:57ce\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To isSuccessful?\",\"declaredType\":\"sequenceFlow\",\"id\":\"16f9f7e7-eb3b-47f4-8ac8-65cea6c0b4ae\",\"sourceRef\":\"08c0b8cd-ebfd-4b99-82fd-cc0307e7001d\"},{\"startQuantity\":1,\"outgoing\":[\"33c738dc-9ffc-4c0b-8754-1996963996dd\"],\"incoming\":[\"1a781759-abf8-43b9-8857-53d38784ace8\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":486,\"y\":58,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Map output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"d254b8b8-0be1-42a6-8469-ac5ba9c69aac\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.transaction = new tw.object.listOf.FCTransactions();\\r\\n\\r\\nif(tw.local.MWtransactions != null && tw.local.MWtransactions.listLength != 0)\\r\\n{\\r\\n\\tfor(var i=0;i<tw.local.MWtransactions.listLength;i++)\\r\\n\\t{       \\r\\n\\t\\ttw.local.transaction[i] = new tw.object.FCTransactions();\\r\\n\\t\\ttw.local.transaction[i].accountNo = tw.local.MWtransactions[i].accountNumber;\\r\\n\\t\\ttw.local.transaction[i].referenceNumber = tw.local.MWtransactions[i].referenceNumber;\\r\\n\\t\\ttw.local.transaction[i].valueDate = tw.local.MWtransactions[i].valueDate;\\r\\n\\t\\ttw.local.transaction[i].postingDate = tw.local.MWtransactions[i].postingDate;\\r\\n\\t\\ttw.local.transaction[i].transactionAmount = tw.local.MWtransactions[i].transactionAmount;\\r\\n\\t\\t\\r\\n\\t}\\r\\n}\"]}},{\"targetRef\":\"7c3b193d-f715-4901-86f6-00dbc0de7fa2\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"33c738dc-9ffc-4c0b-8754-1996963996dd\",\"sourceRef\":\"d254b8b8-0be1-42a6-8469-ac5ba9c69aac\"},{\"itemSubjectRef\":\"itm.12.22c9a740-5d76-4ed9-b6c0-1cc1dc179061\",\"name\":\"MWtransactions\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.4bf29aca-972b-4575-8f10-268772b748f5\"},{\"incoming\":[\"363bf548-f220-4cc9-8ce3-6ab1885be5cd\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"276f4986-4c02-4bfb-81f9-d1f5c9bf9028\",\"otherAttributes\":{\"eventImplId\":\"4a8fee25-dd36-44bc-8f50-43b1e15624e6\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":752,\"y\":193,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"4d8467c7-ac87-4200-8f1a-6e9c3efcd27a\"},{\"startQuantity\":1,\"outgoing\":[\"363bf548-f220-4cc9-8ce3-6ab1885be5cd\"],\"incoming\":[\"1b552984-7d29-4ac8-8229-d0e8caadcf4b\",\"b970bcb3-83ee-4528-887a-0b058173e975\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":561,\"y\":170,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Get Accounts Transactions\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"bb1e74ba-6c25-46a2-8715-dce08181fa95\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"4d8467c7-ac87-4200-8f1a-6e9c3efcd27a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"363bf548-f220-4cc9-8ce3-6ab1885be5cd\",\"sourceRef\":\"bb1e74ba-6c25-46a2-8715-dce08181fa95\"},{\"startQuantity\":1,\"outgoing\":[\"71bfabdf-225d-4f19-83a2-c1e0853b2409\"],\"incoming\":[\"33c738dc-9ffc-4c0b-8754-1996963996dd\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":648,\"y\":58,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"SQL\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"7c3b193d-f715-4901-86f6-00dbc0de7fa2\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sql = \\\"select TRANSACTIONREFNO ,SUM(AMOUNTFORCURRENTREQUEST) from \\\"+tw.env.DBSchema +\\\".ODC_FCTRANSACTION where ACCOUNTNO = '\\\"+tw.local.accountNumber+\\\"' GROUP BY TRANSACTIONREFNO ;\\\";\"]}},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d4a9632d-**************-3efbf194beb1\"},{\"startQuantity\":1,\"outgoing\":[\"15df7103-f967-4c12-8405-f1d1cf8f6865\"],\"incoming\":[\"71bfabdf-225d-4f19-83a2-c1e0853b2409\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":770,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Execute SQL\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"3cf4d89f-b336-46fa-8425-3477430d7888\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"results\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.942dbf74-48b3-4201-8163-25d08ad3bd33\"},{\"startQuantity\":1,\"outgoing\":[\"0a8be54e-21d6-4bac-8d4d-398c9f0fa2a7\"],\"incoming\":[\"15df7103-f967-4c12-8405-f1d1cf8f6865\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":913,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Mapping\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"efaf7742-2862-459a-8433-45c70b64960e\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(!!tw.local.results){\\r\\n\\r\\n   for(var i=0;i<tw.local.results[0].rows.listLength;i++){\\r\\n       for(var j=0 ; j< tw.local.transaction.listLength; j++){\\r\\n        if( tw.local.results[0].rows[i].data[0] == tw.local.transaction[j].referenceNumber){\\r\\n           tw.local.transaction[j].existAmount = tw.local.results[0].rows[i].data[1];\\r\\n           if(tw.local.transaction[j].existAmount == tw.local.transaction[j].transactionAmount)\\r\\n             tw.local.transaction.removeIndex(j);\\r\\n           break;\\r\\n            }\\r\\n        }\\r\\n       } \\r\\n}\"]}},{\"targetRef\":\"3cf4d89f-b336-46fa-8425-3477430d7888\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Execute SQL\",\"declaredType\":\"sequenceFlow\",\"id\":\"71bfabdf-225d-4f19-83a2-c1e0853b2409\",\"sourceRef\":\"7c3b193d-f715-4901-86f6-00dbc0de7fa2\"},{\"targetRef\":\"efaf7742-2862-459a-8433-45c70b64960e\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"15df7103-f967-4c12-8405-f1d1cf8f6865\",\"sourceRef\":\"3cf4d89f-b336-46fa-8425-3477430d7888\"},{\"targetRef\":\"3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"0a8be54e-21d6-4bac-8d4d-398c9f0fa2a7\",\"sourceRef\":\"efaf7742-2862-459a-8433-45c70b64960e\"},{\"outgoing\":[\"1a781759-abf8-43b9-8857-53d38784ace8\",\"474a40ed-ce3f-457d-8242-69b6184ee508\"],\"incoming\":[\"16f9f7e7-eb3b-47f4-8ac8-65cea6c0b4ae\"],\"default\":\"474a40ed-ce3f-457d-8242-69b6184ee508\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":328,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"isSuccessful?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"eb9f079b-064d-4754-8bd8-ee6c6d6f7561\"},{\"targetRef\":\"d254b8b8-0be1-42a6-8469-ac5ba9c69aac\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\\t  ==\\t  true\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Map output\",\"declaredType\":\"sequenceFlow\",\"id\":\"1a781759-abf8-43b9-8857-53d38784ace8\",\"sourceRef\":\"eb9f079b-064d-4754-8bd8-ee6c6d6f7561\"},{\"targetRef\":\"3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"474a40ed-ce3f-457d-8242-69b6184ee508\",\"sourceRef\":\"eb9f079b-064d-4754-8bd8-ee6c6d6f7561\"},{\"parallelMultiple\":false,\"outgoing\":[\"1b552984-7d29-4ac8-8229-d0e8caadcf4b\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"20c46c13-9af3-49e1-8cce-6cad41f5f86f\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"7cfdf9ca-e30f-4510-8bab-9be806b6f31c\",\"otherAttributes\":{\"eventImplId\":\"bc589fa2-5d32-43cf-8137-e880473d2776\"}}],\"attachedToRef\":\"08c0b8cd-ebfd-4b99-82fd-cc0307e7001d\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":231,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"852996c6-9aad-4cb1-83c9-a0f84ab98567\",\"outputSet\":{}},{\"targetRef\":\"bb1e74ba-6c25-46a2-8715-dce08181fa95\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"1b552984-7d29-4ac8-8229-d0e8caadcf4b\",\"sourceRef\":\"852996c6-9aad-4cb1-83c9-a0f84ab98567\"},{\"parallelMultiple\":false,\"outgoing\":[\"b970bcb3-83ee-4528-887a-0b058173e975\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"c2630365-3783-4145-814f-08e93aada118\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"02247c9e-1445-4de0-8337-c4eab9644b9c\",\"otherAttributes\":{\"eventImplId\":\"b505b0de-85d8-4713-8be1-ed16fccd2d85\"}}],\"attachedToRef\":\"3cf4d89f-b336-46fa-8425-3477430d7888\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":805,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"4b149218-1a60-475b-87a1-02ed573620d5\",\"outputSet\":{}},{\"targetRef\":\"bb1e74ba-6c25-46a2-8715-dce08181fa95\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightTop\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"b970bcb3-83ee-4528-887a-0b058173e975\",\"sourceRef\":\"4b149218-1a60-475b-87a1-02ed573620d5\"}],\"laneSet\":[{\"id\":\"ca3beeb5-cdf0-4ea1-8c16-eab74b5747b5\",\"lane\":[{\"flowNodeRef\":[\"829fe9b7-ca68-44b8-8210-9d1511c1c233\",\"3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a\",\"08c0b8cd-ebfd-4b99-82fd-cc0307e7001d\",\"d254b8b8-0be1-42a6-8469-ac5ba9c69aac\",\"4d8467c7-ac87-4200-8f1a-6e9c3efcd27a\",\"bb1e74ba-6c25-46a2-8715-dce08181fa95\",\"7c3b193d-f715-4901-86f6-00dbc0de7fa2\",\"3cf4d89f-b336-46fa-8425-3477430d7888\",\"efaf7742-2862-459a-8433-45c70b64960e\",\"eb9f079b-064d-4754-8bd8-ee6c6d6f7561\",\"852996c6-9aad-4cb1-83c9-a0f84ab98567\",\"4b149218-1a60-475b-87a1-02ed573620d5\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"36d74043-42c0-427e-8dd1-287314b16637\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get account transactions\",\"declaredType\":\"process\",\"id\":\"1.e48b8940-fb79-4255-9301-1ce59d8cfa3b\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134\",\"name\":\"transaction\",\"isCollection\":true,\"id\":\"2055.863e4f64-1adc-44a3-8b7c-f14f92f00d66\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.1fb10221-c28b-436a-846e-60bdaa692291\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorCode\",\"isCollection\":false,\"id\":\"2055.32d2ea86-b14b-4cd7-8d04-4b1d55d93fd3\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"id\":\"2055.efb29d3d-d3ba-4e2d-8216-597c0eec80be\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.c480ed60-61cd-4824-8a4f-181e6b5ad541\",\"2055.676facb9-f340-4be1-8aec-d2bdee4233af\",\"2055.4b2027cd-0272-4e63-8d83-8e29bae003ca\",\"2055.2566eac7-c31f-48ea-8a24-9b38b1022610\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.863e4f64-1adc-44a3-8b7c-f14f92f00d66\",\"2055.1fb10221-c28b-436a-846e-60bdaa692291\",\"2055.32d2ea86-b14b-4cd7-8d04-4b1d55d93fd3\",\"2055.efb29d3d-d3ba-4e2d-8216-597c0eec80be\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"001\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"accountBranch\",\"isCollection\":false,\"id\":\"2055.c480ed60-61cd-4824-8a4f-181e6b5ad541\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"0012070302465900023\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"accountNumber\",\"isCollection\":false,\"id\":\"2055.676facb9-f340-4be1-8aec-d2bdee4233af\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"new Date(\\\"2000-01-04\\\")\"}]},\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"fromDate\",\"isCollection\":false,\"id\":\"2055.4b2027cd-0272-4e63-8d83-8e29bae003ca\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"new TWDate()\"}]},\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"toDate\",\"isCollection\":false,\"id\":\"2055.2566eac7-c31f-48ea-8a24-9b38b1022610\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "accountBranch", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c480ed60-61cd-4824-8a4f-181e6b5ad541", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "\"001\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "5db3f1a0-f063-4eb2-b6cd-38fb344e648f", "versionId": "1c0dccdb-8ece-4482-b6a0-93706d94b775"}, {"name": "accountNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.676facb9-f340-4be1-8aec-d2bdee4233af", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"0012070302465900023\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "8ee048fc-b887-4191-a5e9-4188f20c6228", "versionId": "9522974d-a92d-4bf2-bc04-42f63ee20166"}, {"name": "fromDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4b2027cd-0272-4e63-8d83-8e29bae003ca", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "seq": "3", "hasDefault": "true", "defaultValue": "new Date(\"2000-01-04\")", "isLocked": "false", "description": {"isNull": "true"}, "guid": "856ff910-e76f-4953-b788-a420d3adb19c", "versionId": "49ef275d-3b27-4338-a958-7ea49c0e98f2"}, {"name": "toDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2566eac7-c31f-48ea-8a24-9b38b1022610", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "seq": "4", "hasDefault": "true", "defaultValue": "new TWDate()", "isLocked": "false", "description": {"isNull": "true"}, "guid": "8c646268-a09b-4c06-b4f1-daf15a707405", "versionId": "88baf666-80da-43c5-bbc2-584052e09ef3"}, {"name": "transaction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.863e4f64-1adc-44a3-8b7c-f14f92f00d66", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "2", "isArrayOf": "true", "classId": "/12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "d1cc04b2-780a-4864-b852-1e30b52966a0", "versionId": "e92e7080-2573-4ba7-8a90-081f082efda7"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.1fb10221-c28b-436a-846e-60bdaa692291", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f8135376-71e0-4a36-a6dd-9e35ceed9922", "versionId": "a79592f9-dbf3-4adf-9614-42f37cd820a2"}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.32d2ea86-b14b-4cd7-8d04-4b1d55d93fd3", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "7", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "48b01961-c25f-4a10-b1d8-dda05a83ffe2", "versionId": "13ac02a3-38cc-469c-b007-7a209bae4d28"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.efb29d3d-d3ba-4e2d-8216-597c0eec80be", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4d9f3096-a086-433f-94a5-410b7bee9a6a", "versionId": "7897df86-4fbf-4713-96aa-5b161f08dab4"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.51c623d9-81c6-4052-a2f7-c6cd4d8248e3", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "parameterType": "3", "isArrayOf": "false", "classId": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "seq": "9", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1f0a3607-c1a1-438d-bb81-a61f39c24cb2", "versionId": "aba84673-0ee9-40d8-a993-2749d0f594df"}], "processVariable": [{"name": "MWtransactions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.4bf29aca-972b-4575-8f10-268772b748f5", "description": {"isNull": "true"}, "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.22c9a740-5d76-4ed9-b6c0-1cc1dc179061", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b919add2-fc96-4fd9-9015-9fc24c12a404", "versionId": "9a198cc7-0d0b-4675-bad3-5b7375604deb"}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d4a9632d-**************-3efbf194beb1", "description": {"isNull": "true"}, "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9fe44e30-b670-4ebc-987f-856b7c2d59e6", "versionId": "8bb3cf66-5602-4168-b315-ad991e780271"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.942dbf74-48b3-4201-8163-25d08ad3bd33", "description": {"isNull": "true"}, "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c0e01882-0c15-4513-bc67-4f1236665aaf", "versionId": "b3cb2337-bb32-45c5-9a7d-d28dafe5d574"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bb1e74ba-6c25-46a2-8715-dce08181fa95", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.12f8d7de-5235-4cd0-bf7a-a179e39bb2e6", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cde91268c0d1a44e:-10e36a59:18badede6df:-3ed5", "versionId": "06425c06-412b-46ab-9afe-520e974514e6", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "561", "y": "170", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.12f8d7de-5235-4cd0-bf7a-a179e39bb2e6", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "9cf11f69-325b-4acc-b0bf-93708cae04a4", "versionId": "9732cc9b-5049-4179-8f1f-abf67d8f5f66", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0a5a21ce-eeb6-4aa9-a974-81246f5b5bee", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.12f8d7de-5235-4cd0-bf7a-a179e39bb2e6", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "78efe59c-d221-4a3f-8a04-652fe71bfa64", "versionId": "6afc33a0-01c6-4e86-a74f-46c58e64d07f", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a62cf6e4-a7c4-4f65-a0db-9b839f554a4d", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.12f8d7de-5235-4cd0-bf7a-a179e39bb2e6", "useDefault": "false", "value": "\"Get Accounts Transactions\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "8347cacc-9344-4f0f-ad7c-2986a3daca36", "versionId": "836a938d-c851-496e-85ba-8a0ba45a4fa7", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6d53c814-cf26-47ae-94f8-e31ecac861f0", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.12f8d7de-5235-4cd0-bf7a-a179e39bb2e6", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "eae0595a-e86f-49b5-83bd-c155929883af", "versionId": "de5fbb97-6962-4d74-b51c-3436699b1cc9", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "isSuccessful?", "tWComponentName": "Switch", "tWComponentId": "3013.69c57d78-77e4-433d-bbf4-199a6763e29c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-5985", "versionId": "260b581f-b2da-4408-94ae-7d9674e75402", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "328", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.69c57d78-77e4-433d-bbf4-199a6763e29c", "guid": "b2e29017-5ba2-454c-886a-f61a1870ba45", "versionId": "ac35ff74-3a13-4b10-8a73-272c4228daa5", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.7b0dfbd8-de02-4394-80d6-21accaf76cd3", "switchId": "3013.69c57d78-77e4-433d-bbf4-199a6763e29c", "seq": "1", "endStateId": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-58e1", "condition": "tw.local.isSuccessful\t  ==\t  true", "guid": "205df6b4-2840-4d84-b82a-5674e1b24f36", "versionId": "2b059f8c-fc65-465d-a81d-aa6e665feae3"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7c3b193d-f715-4901-86f6-00dbc0de7fa2", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "SQL", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.a2cdb735-5b68-4821-b372-38026115305d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-292", "versionId": "3a9f95c5-4763-4d48-9503-a1f8ef2f75f7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "648", "y": "58", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.a2cdb735-5b68-4821-b372-38026115305d", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sql = \"select TRANSAC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,SUM(AMOUNTFORCURRENTREQUEST) from \"+tw.env.DBSchema +\".ODC_FCTRANSACTION where ACCOUNTNO = '\"+tw.local.accountNumber+\"' GROUP BY TRANSACTIONREFNO ;\";", "isRule": "false", "guid": "62c5e094-9daf-4a73-8e8a-047a47c79b62", "versionId": "6030d955-bdd9-4155-880d-e817fe42a242"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "MW_FC get account transactions", "tWComponentName": "SubProcess", "tWComponentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.bb1e74ba-6c25-46a2-8715-dce08181fa95", "guid": "guid:b947f5711d9aa2da:-100b3077:189ef71b618:208", "versionId": "62428afc-44d2-4379-92e4-8cd766b9784f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "196", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:cde91268c0d1a44e:-10e36a59:18badede6df:-3ed5", "errorHandlerItemId": "2025.bb1e74ba-6c25-46a2-8715-dce08181fa95", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.be206b9e-752b-4175-a4c6-f5757b8bfe55", "guid": "aeb97199-cc5e-4146-90cd-d5a7476af328", "versionId": "eba6a28f-e7ff-41f8-af1d-22ed059c6614", "parameterMapping": [{"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.*************-4a38-97b6-d40a72da8569", "processParameterId": "2055.********-487a-40d2-86a8-2722487e79d5", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.system.user_id", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "570f0ad2-9fc3-4677-8fba-e74229af2cfe", "versionId": "378c3f7f-d922-454f-8361-c8765558fb96", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5cde8725-00a1-4994-8c33-4065c1643e56", "processParameterId": "2055.62f1b194-0c75-498f-825f-f0d9ca326f88", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ce6b4b5e-5aa2-46aa-9507-bff65796eaaa", "versionId": "4226fb0b-368a-4335-87d0-a898e9f18762", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.8ff6d23d-6823-4ec7-81c4-e1206ecbdf5c", "processParameterId": "2055.d845fc49-3a52-40cf-867f-b488750411d2", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "b6aa6e93-62fc-42e7-bfe1-3d222dd9a909", "versionId": "4721231e-6613-4391-be4d-71c0bc9dd413", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c6ddcb40-6236-43e7-92a9-792ed13b6a3d", "processParameterId": "2055.437055a6-034a-4fce-8ddd-e05b44da5a54", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.system.currentProcessInstanceID", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "7a85fe67-6b2a-4592-82eb-6b1e7d10f16d", "versionId": "55f463f6-48e8-4db5-946b-be418af1b923", "description": {"isNull": "true"}}, {"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.70ed1c3b-399a-4c9a-8d70-a317e2baffbc", "processParameterId": "2055.37cfc79c-d060-461a-8b19-67392d2a090c", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "27caeaec-67c0-47da-8904-d42ec28a0489", "versionId": "65574c86-f08b-421d-bf4c-932b83ab0c77", "description": {"isNull": "true"}}, {"name": "transactionTo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f751e61a-3ad2-45f9-a482-e93bae331c1b", "processParameterId": "2055.79fc8a2e-ef51-435f-859b-1c46a99267e6", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.toDate", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isList": "false", "isInput": "true", "guid": "7303ce95-7441-4322-9e4f-22e3fc568337", "versionId": "671b82ea-223a-4fd1-a175-2d911ee178e9", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.cf512ece-a690-4fc9-8af8-59834af71280", "processParameterId": "2055.6845ae83-c123-4104-859c-fc7937e46cc6", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "7c306e2c-720a-4a36-9e4e-fefdd54c9d23", "versionId": "7a21b21b-6b56-46d1-9388-8464bb41eeaf", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d2fc3174-a95b-4cd5-9423-2221942419a3", "processParameterId": "2055.0d9138e6-4851-498b-8103-8d09bbb15c65", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.errorCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "b2a74860-4275-44a2-94e0-0d4accc1ca85", "versionId": "7c5f8726-148b-4a0a-a09d-df03d32341b1", "description": {"isNull": "true"}}, {"name": "transactionFrom", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.078ef724-fe19-427b-b152-98fc2eef40ff", "processParameterId": "2055.db603b6d-7548-4bb7-8b31-764aabfa24ab", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.fromDate", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isList": "false", "isInput": "true", "guid": "04d2bd77-9466-4510-8f30-89e940feeae1", "versionId": "8b90802e-cd6f-4577-9a31-53c5c4e59d3a", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f66b782e-5b20-4351-a290-6aa22107d11a", "processParameterId": "2055.56c0867b-ca4e-4017-8a44-034da2f642a5", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "39fc4772-f619-42bc-ad8e-6e314e99ba88", "versionId": "930a8bbe-6845-4557-bdea-3e53241a6b1e", "description": {"isNull": "true"}}, {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.526c1e2e-1e16-4bdd-b030-2c7b56dcea1c", "processParameterId": "2055.73e8f907-20d1-4d22-8f9c-b019f734a110", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "\"ODC Creation / Amendment Process Details\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "e8cc2087-e915-457d-ab12-43f24020c9ac", "versionId": "********-2dd8-49f8-b4ef-fc30849fe32b", "description": {"isNull": "true"}}, {"name": "accountNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.bac4641a-80c5-46b9-bd5c-baf46ae87f02", "processParameterId": "2055.f6d334a0-4ad4-4b9f-837d-b2bea698081e", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.accountNumber", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "e6a412d4-3b02-423f-b340-53933cb9049d", "versionId": "b95aef4f-1676-46da-ad32-18b0aa2f0733", "description": {"isNull": "true"}}, {"name": "accountBranchCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6cac8595-6fba-4155-ab95-ebe46f5bb9b1", "processParameterId": "2055.1e298ed1-f38a-4e6c-8c0e-d4e31abca2ea", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.accountBranch", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "158c4e63-31b5-4b5e-af27-604fe9b9f046", "versionId": "cb3fea3c-3289-44a8-b19b-50e71904745b", "description": {"isNull": "true"}}, {"name": "accountTransactions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.9ab055ed-cb42-41b8-8d34-26a2c6cacb53", "processParameterId": "2055.8935ec09-9ec9-4351-89e9-82fc37d5f181", "parameterMappingParentId": "3012.03c8c69d-f8b5-4174-8698-105b99a002f1", "useDefault": "false", "value": "tw.local.MWtransactions", "classRef": "/12.22c9a740-5d76-4ed9-b6c0-1cc1dc179061", "isList": "true", "isInput": "false", "guid": "d837f53d-53d4-4487-b5c1-2ba072007ed3", "versionId": "f1b6fcb7-9daf-4dc6-a986-1c14cdd8ae61", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.efaf7742-2862-459a-8433-45c70b64960e", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "Mapping", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.543fdc12-1d53-4e6c-91f9-511ae17168d1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:47e", "versionId": "842bfbbd-f94d-4a34-8c69-ebfd8557b26e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "913", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.543fdc12-1d53-4e6c-91f9-511ae17168d1", "scriptTypeId": "2", "isActive": "true", "script": "if(!!tw.local.results){\r\r\n\r\r\n   for(var i=0;i<tw.local.results[0].rows.listLength;i++){\r\r\n       for(var j=0 ; j< tw.local.transaction.listLength; j++){\r\r\n        if( tw.local.results[0].rows[i].data[0] == tw.local.transaction[j].referenceNumber){\r\r\n           tw.local.transaction[j].existAmount = tw.local.results[0].rows[i].data[1];\r\r\n           if(tw.local.transaction[j].existAmount == tw.local.transaction[j].transactionAmount)\r\r\n             tw.local.transaction.removeIndex(j);\r\r\n           break;\r\r\n            }\r\r\n        }\r\r\n       } \r\r\n}", "isRule": "false", "guid": "f8c84216-19f8-4534-a59c-64f3b37625f7", "versionId": "2c2f1358-c7d6-463c-a81f-23e9031b5629"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.52b7fd67-a17d-4982-b50c-db4b840ef205", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eef37169:-5f42", "versionId": "8ae1216f-5a8f-4d65-8682-9649cab5093b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1073", "y": "131", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.52b7fd67-a17d-4982-b50c-db4b840ef205", "haltProcess": "false", "guid": "84068ad4-d7c9-4e0a-8dc6-84db01f74033", "versionId": "c029abc7-26ab-4088-8180-0e9981724439"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d254b8b8-0be1-42a6-8469-ac5ba9c69aac", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "Map output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.e9e7d860-2968-4dcf-a51e-d88bb4845a41", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:b947f5711d9aa2da:-100b3077:189ef71b618:209", "versionId": "9fbcfe35-58f8-4861-8df8-c933a2288ffe", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "486", "y": "58", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.e9e7d860-2968-4dcf-a51e-d88bb4845a41", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.transaction = new tw.object.listOf.FCTransactions();\r\r\n\r\r\nif(tw.local.MWtransactions != null && tw.local.MWtransactions.listLength != 0)\r\r\n{\r\r\n\tfor(var i=0;i<tw.local.MWtransactions.listLength;i++)\r\r\n\t{       \r\r\n\t\ttw.local.transaction[i] = new tw.object.FCTransactions();\r\r\n\t\ttw.local.transaction[i].accountNo = tw.local.MWtransactions[i].accountNumber;\r\r\n\t\ttw.local.transaction[i].referenceNumber = tw.local.MWtransactions[i].referenceNumber;\r\r\n\t\ttw.local.transaction[i].valueDate = tw.local.MWtransactions[i].valueDate;\r\r\n\t\ttw.local.transaction[i].postingDate = tw.local.MWtransactions[i].postingDate;\r\r\n\t\ttw.local.transaction[i].transactionAmount = tw.local.MWtransactions[i].transactionAmount;\r\r\n\t\t\r\r\n\t}\r\r\n}", "isRule": "false", "guid": "adb4d79b-5bb2-43aa-8549-a4d76301bc00", "versionId": "c56d55c2-8be1-4d49-9405-04280ef165a1"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3cf4d89f-b336-46fa-8425-3477430d7888", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "Execute SQL", "tWComponentName": "SubProcess", "tWComponentId": "3012.a215c7e6-9ea6-427c-8e83-debecca9ce5f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.bb1e74ba-6c25-46a2-8715-dce08181fa95", "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:42d", "versionId": "ab0e9c91-645a-4bab-8425-5122b5636bd9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "770", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:cde91268c0d1a44e:-10e36a59:18badede6df:-3ed5", "errorHandlerItemId": "2025.bb1e74ba-6c25-46a2-8715-dce08181fa95", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "rightTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.a215c7e6-9ea6-427c-8e83-debecca9ce5f", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "752609bc-efdf-44fd-bd93-cfad38cc8ce8", "versionId": "cddd4c08-2dd5-46c2-af77-ad71df3904a5", "parameterMapping": [{"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4e301543-c3b0-4659-9374-1b709781be32", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.a215c7e6-9ea6-427c-8e83-debecca9ce5f", "useDefault": "true", "value": {"isNull": "true"}, "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "a72234b6-6d5f-4f40-b8ca-9c84e9a13e3e", "versionId": "05f52b4e-2ef6-4955-be74-a934e4d5231a", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.9b48f74c-dbaa-4f4f-a961-aac291918507", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.a215c7e6-9ea6-427c-8e83-debecca9ce5f", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "fa3d9981-ca6e-4af5-8c6b-1a61b457e08d", "versionId": "12cb23b2-b663-4c60-8a94-f4440b579d09", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d629174e-a953-4f3f-9f93-0b56b35e54ba", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.a215c7e6-9ea6-427c-8e83-debecca9ce5f", "useDefault": "true", "value": {"isNull": "true"}, "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "ee71915b-3988-48d7-9b9c-3e5b59f21d82", "versionId": "47cb72eb-d7fc-4a56-a3c1-8f809f23be82", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e5e4e128-10ff-422a-8b0f-24f35bfd29c1", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.a215c7e6-9ea6-427c-8e83-debecca9ce5f", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "a757376c-c304-40c2-b949-a784361afffa", "versionId": "4b587974-5f97-4816-87cd-3fd822480c72", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a52dcd32-1e4f-44f3-8ce1-ea6b49831a4c", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.a215c7e6-9ea6-427c-8e83-debecca9ce5f", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "d5e03ed0-0001-4ff2-bc51-aeddfd307ff4", "versionId": "85cf4ea0-ab1b-414b-93a7-403ce600c7e2", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4d8467c7-ac87-4200-8f1a-6e9c3efcd27a", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.a656357e-e121-4d81-b6bc-5db9b0c98b29", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cde91268c0d1a44e:-10e36a59:18badede6df:-3ed4", "versionId": "ff3e71a0-2b11-41a8-ac21-e86e987e3b12", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "752", "y": "193", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.a656357e-e121-4d81-b6bc-5db9b0c98b29", "message": "", "faultStyle": "1", "guid": "4d9f4c38-460d-47a8-9c11-d114920323cb", "versionId": "1eadd3b9-2e92-4e70-afef-99e043eda340", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.73da905a-bd64-47e0-9366-13e1b29f18cf", "processParameterId": "2055.51c623d9-81c6-4052-a2f7-c6cd4d8248e3", "parameterMappingParentId": "3007.a656357e-e121-4d81-b6bc-5db9b0c98b29", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "isList": "false", "isInput": "false", "guid": "f71cca41-af02-45a8-9ad4-8521e4669f25", "versionId": "7a7f5e85-c627-4f65-b903-bac183a066cb", "description": {"isNull": "true"}}}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get account transactions", "id": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "accountBranch", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.c480ed60-61cd-4824-8a4f-181e6b5ad541", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"001\"", "useDefault": "true"}}}, {"name": "accountNumber", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.676facb9-f340-4be1-8aec-d2bdee4233af", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"0012070302465900023\"", "useDefault": "true"}}}, {"name": "fromDate", "itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "id": "2055.4b2027cd-0272-4e63-8d83-8e29bae003ca", "ns16:extensionElements": {"ns3:defaultValue": {"_": "new Date(\"2000-01-04\")", "useDefault": "true"}}}, {"name": "toDate", "itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "id": "2055.2566eac7-c31f-48ea-8a24-9b38b1022610", "ns16:extensionElements": {"ns3:defaultValue": {"_": "new TWDate()", "useDefault": "true"}}}], "ns16:dataOutput": [{"name": "transaction", "itemSubjectRef": "itm.12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134", "isCollection": "true", "id": "2055.863e4f64-1adc-44a3-8b7c-f14f92f00d66"}, {"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.1fb10221-c28b-436a-846e-60bdaa692291"}, {"name": "errorCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.32d2ea86-b14b-4cd7-8d04-4b1d55d93fd3"}, {"name": "isSuccessful", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.efb29d3d-d3ba-4e2d-8216-597c0eec80be"}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.c480ed60-61cd-4824-8a4f-181e6b5ad541", "2055.676facb9-f340-4be1-8aec-d2bdee4233af", "2055.4b2027cd-0272-4e63-8d83-8e29bae003ca", "2055.2566eac7-c31f-48ea-8a24-9b38b1022610"]}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.863e4f64-1adc-44a3-8b7c-f14f92f00d66", "2055.1fb10221-c28b-436a-846e-60bdaa692291", "2055.32d2ea86-b14b-4cd7-8d04-4b1d55d93fd3", "2055.efb29d3d-d3ba-4e2d-8216-597c0eec80be"]}}, "ns16:laneSet": {"id": "ca3beeb5-cdf0-4ea1-8c16-eab74b5747b5", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "36d74043-42c0-427e-8dd1-287314b16637", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["829fe9b7-ca68-44b8-8210-9d1511c1c233", "3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a", "08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "d254b8b8-0be1-42a6-8469-ac5ba9c69aac", "4d8467c7-ac87-4200-8f1a-6e9c3efcd27a", "bb1e74ba-6c25-46a2-8715-dce08181fa95", "7c3b193d-f715-4901-86f6-00dbc0de7fa2", "3cf4d89f-b336-46fa-8425-3477430d7888", "efaf7742-2862-459a-8433-45c70b64960e", "eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "852996c6-9aad-4cb1-83c9-a0f84ab98567", "4b149218-1a60-475b-87a1-02ed573620d5"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "829fe9b7-ca68-44b8-8210-9d1511c1c233", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.c07510e2-a54a-4610-8eb5-63b7709d42d6"}, "ns16:endEvent": [{"name": "End", "id": "3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1073", "y": "131", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:6ad7eb4224455a46:11f23e39:189eef37169:-5f42"}, "ns16:incoming": ["0a8be54e-21d6-4bac-8d4d-398c9f0fa2a7", "474a40ed-ce3f-457d-8242-69b6184ee508"]}, {"name": "End Event", "id": "4d8467c7-ac87-4200-8f1a-6e9c3efcd27a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "752", "y": "193", "width": "24", "height": "24"}}, "ns16:incoming": "363bf548-f220-4cc9-8ce3-6ab1885be5cd", "ns16:errorEventDefinition": {"id": "276f4986-4c02-4bfb-81f9-d1f5c9bf9028", "eventImplId": "4a8fee25-dd36-44bc-8f50-43b1e15624e6", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "829fe9b7-ca68-44b8-8210-9d1511c1c233", "targetRef": "08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "name": "To MW_FC get account transactions", "id": "2027.c07510e2-a54a-4610-8eb5-63b7709d42d6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "targetRef": "eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "name": "To isSuccessful?", "id": "16f9f7e7-eb3b-47f4-8ac8-65cea6c0b4ae", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:57ce"}}, {"sourceRef": "d254b8b8-0be1-42a6-8469-ac5ba9c69aac", "targetRef": "7c3b193d-f715-4901-86f6-00dbc0de7fa2", "name": "To End", "id": "33c738dc-9ffc-4c0b-8754-1996963996dd", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "bb1e74ba-6c25-46a2-8715-dce08181fa95", "targetRef": "4d8467c7-ac87-4200-8f1a-6e9c3efcd27a", "name": "To End Event", "id": "363bf548-f220-4cc9-8ce3-6ab1885be5cd", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}, {"sourceRef": "7c3b193d-f715-4901-86f6-00dbc0de7fa2", "targetRef": "3cf4d89f-b336-46fa-8425-3477430d7888", "name": "To Execute SQL", "id": "71bfabdf-225d-4f19-83a2-c1e0853b2409", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "3cf4d89f-b336-46fa-8425-3477430d7888", "targetRef": "efaf7742-2862-459a-8433-45c70b64960e", "name": "To End", "id": "15df7103-f967-4c12-8405-f1d1cf8f6865", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "efaf7742-2862-459a-8433-45c70b64960e", "targetRef": "3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a", "name": "To End", "id": "0a8be54e-21d6-4bac-8d4d-398c9f0fa2a7", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "targetRef": "d254b8b8-0be1-42a6-8469-ac5ba9c69aac", "name": "To Map output", "id": "1a781759-abf8-43b9-8857-53d38784ace8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.isSuccessful\t  ==\t  true", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "targetRef": "3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a", "name": "To End", "id": "474a40ed-ce3f-457d-8242-69b6184ee508", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "852996c6-9aad-4cb1-83c9-a0f84ab98567", "targetRef": "bb1e74ba-6c25-46a2-8715-dce08181fa95", "name": "To Exception Handling", "id": "1b552984-7d29-4ac8-8229-d0e8caadcf4b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4b149218-1a60-475b-87a1-02ed573620d5", "targetRef": "bb1e74ba-6c25-46a2-8715-dce08181fa95", "name": "To Exception Handling", "id": "b970bcb3-83ee-4528-887a-0b058173e975", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:callActivity": [{"calledElement": "1.be206b9e-752b-4175-a4c6-f5757b8bfe55", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "MW_FC get account transactions", "id": "08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "196", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "2027.c07510e2-a54a-4610-8eb5-63b7709d42d6", "ns16:outgoing": "16f9f7e7-eb3b-47f4-8ac8-65cea6c0b4ae", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.6845ae83-c123-4104-859c-fc7937e46cc6", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.1e298ed1-f38a-4e6c-8c0e-d4e31abca2ea", "ns16:assignment": {"ns16:from": {"_": "tw.local.accountBranch", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.f6d334a0-4ad4-4b9f-837d-b2bea698081e", "ns16:assignment": {"ns16:from": {"_": "tw.local.accountNumber", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.db603b6d-7548-4bb7-8b31-764aabfa24ab", "ns16:assignment": {"ns16:from": {"_": "tw.local.fromDate", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}}}, {"ns16:targetRef": "2055.79fc8a2e-ef51-435f-859b-1c46a99267e6", "ns16:assignment": {"ns16:from": {"_": "tw.local.toDate", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}}}, {"ns16:targetRef": "2055.********-487a-40d2-86a8-2722487e79d5", "ns16:assignment": {"ns16:from": {"_": "tw.system.user_id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.437055a6-034a-4fce-8ddd-e05b44da5a54", "ns16:assignment": {"ns16:from": {"_": "tw.system.currentProcessInstanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.62f1b194-0c75-498f-825f-f0d9ca326f88", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.37cfc79c-d060-461a-8b19-67392d2a090c", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.73e8f907-20d1-4d22-8f9c-b019f734a110", "ns16:assignment": {"ns16:from": {"_": "\"ODC Creation / Amendment Process Details\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.56c0867b-ca4e-4017-8a44-034da2f642a5", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.0d9138e6-4851-498b-8103-8d09bbb15c65", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.d845fc49-3a52-40cf-867f-b488750411d2", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.8935ec09-9ec9-4351-89e9-82fc37d5f181", "ns16:assignment": {"ns16:to": {"_": "tw.local.MWtransactions", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.22c9a740-5d76-4ed9-b6c0-1cc1dc179061"}}}]}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Exception Handling", "id": "bb1e74ba-6c25-46a2-8715-dce08181fa95", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "561", "y": "170", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["1b552984-7d29-4ac8-8229-d0e8caadcf4b", "b970bcb3-83ee-4528-887a-0b058173e975"], "ns16:outgoing": "363bf548-f220-4cc9-8ce3-6ab1885be5cd", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Get Accounts Transactions\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "Execute SQL", "id": "3cf4d89f-b336-46fa-8425-3477430d7888", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "770", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "71bfabdf-225d-4f19-83a2-c1e0853b2409", "ns16:outgoing": "15df7103-f967-4c12-8405-f1d1cf8f6865", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Map output", "id": "d254b8b8-0be1-42a6-8469-ac5ba9c69aac", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "486", "y": "58", "width": "95", "height": "70"}}, "ns16:incoming": "1a781759-abf8-43b9-8857-53d38784ace8", "ns16:outgoing": "33c738dc-9ffc-4c0b-8754-1996963996dd", "ns16:script": "tw.local.transaction = new tw.object.listOf.FCTransactions();\r\r\n\r\r\nif(tw.local.MWtransactions != null && tw.local.MWtransactions.listLength != 0)\r\r\n{\r\r\n\tfor(var i=0;i<tw.local.MWtransactions.listLength;i++)\r\r\n\t{       \r\r\n\t\ttw.local.transaction[i] = new tw.object.FCTransactions();\r\r\n\t\ttw.local.transaction[i].accountNo = tw.local.MWtransactions[i].accountNumber;\r\r\n\t\ttw.local.transaction[i].referenceNumber = tw.local.MWtransactions[i].referenceNumber;\r\r\n\t\ttw.local.transaction[i].valueDate = tw.local.MWtransactions[i].valueDate;\r\r\n\t\ttw.local.transaction[i].postingDate = tw.local.MWtransactions[i].postingDate;\r\r\n\t\ttw.local.transaction[i].transactionAmount = tw.local.MWtransactions[i].transactionAmount;\r\r\n\t\t\r\r\n\t}\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "SQL", "id": "7c3b193d-f715-4901-86f6-00dbc0de7fa2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "648", "y": "58", "width": "95", "height": "70"}}, "ns16:incoming": "33c738dc-9ffc-4c0b-8754-1996963996dd", "ns16:outgoing": "71bfabdf-225d-4f19-83a2-c1e0853b2409", "ns16:script": "tw.local.sql = \"select TRANSAC<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON> ,SUM(AMOUNTFORCURRENTREQUEST) from \"+tw.env.DBSchema +\".ODC_FCTRANSACTION where ACCOUNTNO = '\"+tw.local.accountNumber+\"' GROUP BY TRANSACTIONREFNO ;\";"}, {"scriptFormat": "text/x-javascript", "name": "Mapping", "id": "efaf7742-2862-459a-8433-45c70b64960e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "913", "y": "56", "width": "95", "height": "70"}}, "ns16:incoming": "15df7103-f967-4c12-8405-f1d1cf8f6865", "ns16:outgoing": "0a8be54e-21d6-4bac-8d4d-398c9f0fa2a7", "ns16:script": "if(!!tw.local.results){\r\r\n\r\r\n   for(var i=0;i<tw.local.results[0].rows.listLength;i++){\r\r\n       for(var j=0 ; j< tw.local.transaction.listLength; j++){\r\r\n        if( tw.local.results[0].rows[i].data[0] == tw.local.transaction[j].referenceNumber){\r\r\n           tw.local.transaction[j].existAmount = tw.local.results[0].rows[i].data[1];\r\r\n           if(tw.local.transaction[j].existAmount == tw.local.transaction[j].transactionAmount)\r\r\n             tw.local.transaction.removeIndex(j);\r\r\n           break;\r\r\n            }\r\r\n        }\r\r\n       } \r\r\n}"}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.22c9a740-5d76-4ed9-b6c0-1cc1dc179061", "isCollection": "true", "name": "MWtransactions", "id": "2056.4bf29aca-972b-4575-8f10-268772b748f5"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.d4a9632d-**************-3efbf194beb1"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.942dbf74-48b3-4201-8163-25d08ad3bd33"}], "ns16:exclusiveGateway": {"default": "474a40ed-ce3f-457d-8242-69b6184ee508", "name": "isSuccessful?", "id": "eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "328", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "16f9f7e7-eb3b-47f4-8ac8-65cea6c0b4ae", "ns16:outgoing": ["1a781759-abf8-43b9-8857-53d38784ace8", "474a40ed-ce3f-457d-8242-69b6184ee508"]}, "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "parallelMultiple": "false", "name": "Error", "id": "852996c6-9aad-4cb1-83c9-a0f84ab98567", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "231", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "1b552984-7d29-4ac8-8229-d0e8caadcf4b", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "20c46c13-9af3-49e1-8cce-6cad41f5f86f"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "7cfdf9ca-e30f-4510-8bab-9be806b6f31c", "eventImplId": "bc589fa2-5d32-43cf-8137-e880473d2776", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "3cf4d89f-b336-46fa-8425-3477430d7888", "parallelMultiple": "false", "name": "Error1", "id": "4b149218-1a60-475b-87a1-02ed573620d5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "805", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "b970bcb3-83ee-4528-887a-0b058173e975", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "c2630365-3783-4145-814f-08e93aada118"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "02247c9e-1445-4de0-8337-c4eab9644b9c", "eventImplId": "b505b0de-85d8-4713-8be1-ed16fccd2d85", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.15df7103-f967-4c12-8405-f1d1cf8f6865", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.3cf4d89f-b336-46fa-8425-3477430d7888", "2025.3cf4d89f-b336-46fa-8425-3477430d7888"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.efaf7742-2862-459a-8433-45c70b64960e", "2025.efaf7742-2862-459a-8433-45c70b64960e"], "guid": "502b6c10-7609-4a68-a93a-98e59222669c", "versionId": "11568f4b-dc1f-4c09-aec2-02c5dea3ab68", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Map output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1a781759-abf8-43b9-8857-53d38784ace8", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "2025.eb9f079b-064d-4754-8bd8-ee6c6d6f7561"], "endStateId": "guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:-58e1", "toProcessItemId": ["2025.d254b8b8-0be1-42a6-8469-ac5ba9c69aac", "2025.d254b8b8-0be1-42a6-8469-ac5ba9c69aac"], "guid": "971c0bb5-c432-4283-aa6c-8bfe998a8bd2", "versionId": "2232d238-453f-4e27-8c86-c8206c83986d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Execute SQL", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.71bfabdf-225d-4f19-83a2-c1e0853b2409", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.7c3b193d-f715-4901-86f6-00dbc0de7fa2", "2025.7c3b193d-f715-4901-86f6-00dbc0de7fa2"], "endStateId": "Out", "toProcessItemId": ["2025.3cf4d89f-b336-46fa-8425-3477430d7888", "2025.3cf4d89f-b336-46fa-8425-3477430d7888"], "guid": "d7de6c4f-ac63-405c-814b-d48739a40b5e", "versionId": "5691d109-9f82-4a0d-840c-cb2023011eac", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End Event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.363bf548-f220-4cc9-8ce3-6ab1885be5cd", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.bb1e74ba-6c25-46a2-8715-dce08181fa95", "2025.bb1e74ba-6c25-46a2-8715-dce08181fa95"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.4d8467c7-ac87-4200-8f1a-6e9c3efcd27a", "2025.4d8467c7-ac87-4200-8f1a-6e9c3efcd27a"], "guid": "c8bc31ed-f26e-4d42-9ab7-747c911f50b1", "versionId": "a982ad43-e28e-4390-acbf-1d5d0014ed64", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To isSuccessful?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.16f9f7e7-eb3b-47f4-8ac8-65cea6c0b4ae", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.08c0b8cd-ebfd-4b99-82fd-cc0307e7001d", "2025.08c0b8cd-ebfd-4b99-82fd-cc0307e7001d"], "endStateId": "guid:6ad7eb4224455a46:11f23e39:189eae0bd83:57ce", "toProcessItemId": ["2025.eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "2025.eb9f079b-064d-4754-8bd8-ee6c6d6f7561"], "guid": "cf8bc1cf-15c4-4704-a20b-eba7034b0bd6", "versionId": "b0b62f70-b67b-40de-ae26-e5cd657c2140", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.0a8be54e-21d6-4bac-8d4d-398c9f0fa2a7", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.efaf7742-2862-459a-8433-45c70b64960e", "2025.efaf7742-2862-459a-8433-45c70b64960e"], "endStateId": "Out", "toProcessItemId": ["2025.3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a", "2025.3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a"], "guid": "87904079-f866-453f-a0d3-a0a52537db4e", "versionId": "bda3d4ca-1516-4c21-972c-ac860b1af87b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.33c738dc-9ffc-4c0b-8754-1996963996dd", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.d254b8b8-0be1-42a6-8469-ac5ba9c69aac", "2025.d254b8b8-0be1-42a6-8469-ac5ba9c69aac"], "endStateId": "Out", "toProcessItemId": ["2025.7c3b193d-f715-4901-86f6-00dbc0de7fa2", "2025.7c3b193d-f715-4901-86f6-00dbc0de7fa2"], "guid": "b50c9d14-675c-41cb-85f1-16d89543db3c", "versionId": "d0c8a744-7fa3-4c2c-9248-a3ab67cda77f", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.474a40ed-ce3f-457d-8242-69b6184ee508", "processId": "1.e48b8940-fb79-4255-9301-1ce59d8cfa3b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.eb9f079b-064d-4754-8bd8-ee6c6d6f7561", "2025.eb9f079b-064d-4754-8bd8-ee6c6d6f7561"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a", "2025.3fb177d8-fc2c-45a4-8c5e-c43f6d7c0c9a"], "guid": "4d4043a6-f0a9-4b6b-9bbb-dcf8cdcede7d", "versionId": "f3d7eb84-3d53-428f-b3f9-d5f1140d6fa3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}