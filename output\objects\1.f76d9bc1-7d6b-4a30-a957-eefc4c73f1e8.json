{"id": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "versionId": "1812600f-233f-4424-8b78-d0d82862e944", "name": "Branch Hub filter service", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "name": "Branch Hub filter service", "lastModified": "1691183197613", "lastModifiedBy": "heba", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.70833324-3be4-4040-809a-81faaa9881ca", "2025.70833324-3be4-4040-809a-81faaa9881ca"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "2529e9f7-5865-462d-b6f5-19c20bd757d0", "versionId": "1812600f-233f-4424-8b78-d0d82862e944", "dependencySummary": "<dependencySummary id=\"220ef5bc-97ee-4aa9-aa02-d0755d9361e0\" />", "jsonData": {"isNull": "true"}, "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "originalTeam", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.7e19664f-430b-48a0-a9c6-ed148686698e", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9c06d0ea-f466-4f13-8cad-a86323c1f3ab", "versionId": "0046fc4a-ac63-4248-b58d-9253028d7feb"}, {"name": "branchCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b849887a-3e1e-4633-be89-6e2adce84383", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3d6aac9b-d956-48c0-a114-35f8d5c88728", "versionId": "c6d154ed-4a56-4294-8416-8c1d951dec81"}, {"name": "hubCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4949a95d-1755-4fae-b755-b8cf9b4597ec", "versionId": "600f7fa6-fc38-4115-a306-189de5d7c286"}, {"name": "branchGroupName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "fefd8e3e-39e5-4a30-9f7e-8c82ba9ceec0", "versionId": "4d8b4546-9abb-47e7-bc62-8b13c1d9ea0b"}, {"name": "hubGroupName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d5717fc2-69b2-4168-a2af-80632cedf962", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "0144e756-d6fa-4814-b0fb-70d8a04ec201", "versionId": "31d68e30-eb73-41da-aa96-a5d4d72c26c2"}, {"name": "filteredTeam", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b11e99c6-7c3b-44b5-9e1e-0e2d0eae1375", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7ea951fb-a168-4e8c-ba15-6754f9f94961", "versionId": "a89bd25f-5048-4916-89cd-506e7a16cefe"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.70833324-3be4-4040-809a-81faaa9881ca", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "name": "IsHub?", "tWComponentName": "Switch", "tWComponentId": "3013.74ee4e65-ae87-42cc-9dc2-1c266f5e41cc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5557", "versionId": "3770a89a-9025-406e-b098-a9faa2bbe93a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "125", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.74ee4e65-ae87-42cc-9dc2-1c266f5e41cc", "guid": "b3993c17-c024-473e-9276-7746a805bff4", "versionId": "4c106fc6-2121-47b9-9ae8-a586ca79e2f0", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.26975985-5ad1-4af6-8808-34635886b52e", "switchId": "3013.74ee4e65-ae87-42cc-9dc2-1c266f5e41cc", "seq": "1", "endStateId": "guid:14611baa90d47fd1:-642f9074:1894f1bfa8e:469", "condition": "tw.local.hubCode != \"\" && tw.local.hubCode != null", "guid": "c7da17e0-0b13-46e4-9e82-7df900c6b217", "versionId": "9553d105-92b0-4bab-a5ed-3e693b59e754"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.737d2eee-815a-42c1-88ac-41e98ae4495b", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.e8caafbd-60e9-4ae6-b927-02de6f06f39d", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5556", "versionId": "7aa281b9-878b-4f82-863f-527ea7bededf", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.e8caafbd-60e9-4ae6-b927-02de6f06f39d", "haltProcess": "false", "guid": "b95b9633-4ad9-471f-a585-7abfdb542981", "versionId": "6780209f-6078-4e3e-916a-3a9f0bdff80a"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3322adc2-980e-42a4-9f4e-185032fc7bcb", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "name": "Get Branch team", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.290c22b3-1800-4a05-b2c9-d2c2e30e590e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5555", "versionId": "83bc73f6-4cbf-4edc-901f-a0d2f3ed8b82", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "266", "y": "210", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.290c22b3-1800-4a05-b2c9-d2c2e30e590e", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.filteredTeam = new tw.object.Team();\r\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\r\n\r\r\nlog.info(\"filtered team before branch call# \"+tw.local.filteredTeam.name);\r\r\n\r\r\nvar branch_users = tw.system.org.findRoleByName(tw.local.branchCode).allUsers;\r\r\nvar group_users = tw.system.org.findRoleByName(tw.local.branchGroupName).allUsers;\r\r\n\t\r\r\nlog.info(\"users::: \"+ branch_users);\r\r\n\tfor (var i=0;i<branch_users.listLength;i++)\r\r\n\t{\r\r\n\t\tfor(var j=0;j<group_users.listLength;j++)\r\r\n\t\t{\r\r\n\t\t\tif(branch_users[i].name == group_users[j].name && group_users[j].name != \"\") \r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.filteredTeam.members[j] = group_users[j].name;\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t\t\r\r\n\t}\r\r\n\tlog.info(\"Filtered team :# \"+tw.local.filteredTeam);", "isRule": "false", "guid": "9b9202ea-ffb7-4717-8610-8d456d0b95f6", "versionId": "53c22720-9a14-4380-89a4-9b133fc8c7e8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "name": "Get HUB team", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.bd8a5c92-d444-47f2-a8e6-929f3d675c3b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5554", "versionId": "f2f29ef8-2551-44f7-af91-e43ceb311479", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "278", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.bd8a5c92-d444-47f2-a8e6-929f3d675c3b", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.filteredTeam = new tw.object.Team();\r\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\r\n\r\r\nvar users = tw.system.org.findRoleByName(tw.local.hubGroupName).allUsers;\r\r\n\t\r\r\nlog.info(\"users::: \"+ users);\r\r\n\tfor (var i = 0; i < users.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.filteredTeam.members[i] = users[i].name;\r\r\n\t}", "isRule": "false", "guid": "46fa6ea7-df19-45a9-af5e-536fd7b06df9", "versionId": "2c5a3d40-7aa9-429f-87a0-7f7863a9ed18"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Branch Hub filter service", "id": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns3:readOnlyOutputs": "true", "ns16:dataInput": [{"name": "originalTeam", "itemSubjectRef": "itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "isCollection": "false", "id": "2055.7e19664f-430b-48a0-a9c6-ed148686698e", "ns3:readOnly": "true"}, {"name": "branchCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.b849887a-3e1e-4633-be89-6e2adce84383", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"BR001_SHR\"", "useDefault": "false"}}}, {"name": "hubCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"077\"", "useDefault": "false"}}}, {"name": "branchGroupName", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"BPM_IDC_BR_COMP_REP_CHKR\"", "useDefault": "false"}}}, {"name": "hubGroupName", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.d5717fc2-69b2-4168-a2af-80632cedf962", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"BPM_IDC_HUB_077_COMP_CHKR\"", "useDefault": "false"}}}], "ns16:dataOutput": {"name": "filteredTeam", "itemSubjectRef": "itm.12.4f114e73-2520-40d7-b2ea-db9dcc4aa1f0", "isCollection": "false", "id": "2055.b11e99c6-7c3b-44b5-9e1e-0e2d0eae1375", "ns3:readOnly": "true"}, "ns16:inputSet": {"ns16:dataInputRefs": ["2055.7e19664f-430b-48a0-a9c6-ed148686698e", "2055.b849887a-3e1e-4633-be89-6e2adce84383", "2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857", "2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6", "2055.d5717fc2-69b2-4168-a2af-80632cedf962"]}, "ns16:outputSet": ""}, "ns16:laneSet": {"id": "3d594f05-a229-44b2-b433-ba9d2a06bff7", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "2c643e15-7ac8-4575-8379-84abbf677436", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["f364d556-9235-4885-8cfd-ee7a045a1664", "737d2eee-815a-42c1-88ac-41e98ae4495b", "720aa561-61d8-4ad4-bda1-f6b2f93b67d6", "70833324-3be4-4040-809a-81faaa9881ca", "3322adc2-980e-42a4-9f4e-185032fc7bcb"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "f364d556-9235-4885-8cfd-ee7a045a1664", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.fcbd468a-4705-4edb-a4ae-31a781ae52db"}, "ns16:endEvent": {"name": "End", "id": "737d2eee-815a-42c1-88ac-41e98ae4495b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5556"}, "ns16:incoming": ["cea29c01-2a83-4866-9a75-49e63811431d", "70a344c8-a850-438c-974e-13a1b6c1ae93"]}, "ns16:sequenceFlow": [{"sourceRef": "f364d556-9235-4885-8cfd-ee7a045a1664", "targetRef": "70833324-3be4-4040-809a-81faaa9881ca", "name": "To IsHub?", "id": "2027.fcbd468a-4705-4edb-a4ae-31a781ae52db", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "720aa561-61d8-4ad4-bda1-f6b2f93b67d6", "targetRef": "737d2eee-815a-42c1-88ac-41e98ae4495b", "name": "To End", "id": "cea29c01-2a83-4866-9a75-49e63811431d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "70833324-3be4-4040-809a-81faaa9881ca", "targetRef": "720aa561-61d8-4ad4-bda1-f6b2f93b67d6", "name": "Yes", "id": "974b51b4-dc30-4dea-8351-24b486874304", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.hubCode != \"\" && tw.local.hubCode != null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "70833324-3be4-4040-809a-81faaa9881ca", "targetRef": "3322adc2-980e-42a4-9f4e-185032fc7bcb", "name": "No", "id": "fa3f942d-6a8a-46e0-9815-a4c2421d12fe", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "3322adc2-980e-42a4-9f4e-185032fc7bcb", "targetRef": "737d2eee-815a-42c1-88ac-41e98ae4495b", "name": "To End", "id": "70a344c8-a850-438c-974e-13a1b6c1ae93", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Get HUB team", "id": "720aa561-61d8-4ad4-bda1-f6b2f93b67d6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "278", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "974b51b4-dc30-4dea-8351-24b486874304", "ns16:outgoing": "cea29c01-2a83-4866-9a75-49e63811431d", "ns16:script": "tw.local.filteredTeam = new tw.object.Team();\r\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\r\n\r\r\nvar users = tw.system.org.findRoleByName(tw.local.hubGroupName).allUsers;\r\r\n\t\r\r\nlog.info(\"users::: \"+ users);\r\r\n\tfor (var i = 0; i < users.listLength ; i++)\r\r\n\t{\r\r\n\t\ttw.local.filteredTeam.members[i] = users[i].name;\r\r\n\t}"}, {"scriptFormat": "text/x-javascript", "name": "Get Branch team", "id": "3322adc2-980e-42a4-9f4e-185032fc7bcb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "266", "y": "210", "width": "95", "height": "70"}}, "ns16:incoming": "fa3f942d-6a8a-46e0-9815-a4c2421d12fe", "ns16:outgoing": "70a344c8-a850-438c-974e-13a1b6c1ae93", "ns16:script": "tw.local.filteredTeam = new tw.object.Team();\r\r\ntw.local.filteredTeam.members = new tw.object.listOf.String();\r\r\ntw.local.filteredTeam.managerTeam = tw.local.originalTeam.managerTeam;\r\r\ntw.local.filteredTeam.name = tw.local.originalTeam.name;\r\r\n\r\r\nlog.info(\"filtered team before branch call# \"+tw.local.filteredTeam.name);\r\r\n\r\r\nvar branch_users = tw.system.org.findRoleByName(tw.local.branchCode).allUsers;\r\r\nvar group_users = tw.system.org.findRoleByName(tw.local.branchGroupName).allUsers;\r\r\n\t\r\r\nlog.info(\"users::: \"+ branch_users);\r\r\n\tfor (var i=0;i<branch_users.listLength;i++)\r\r\n\t{\r\r\n\t\tfor(var j=0;j<group_users.listLength;j++)\r\r\n\t\t{\r\r\n\t\t\tif(branch_users[i].name == group_users[j].name && group_users[j].name != \"\") \r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.filteredTeam.members[j] = group_users[j].name;\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t\t\r\r\n\t}\r\r\n\tlog.info(\"Filtered team :# \"+tw.local.filteredTeam);"}], "ns16:exclusiveGateway": {"default": "fa3f942d-6a8a-46e0-9815-a4c2421d12fe", "name": "IsHub?", "id": "70833324-3be4-4040-809a-81faaa9881ca", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "125", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "2027.fcbd468a-4705-4edb-a4ae-31a781ae52db", "ns16:outgoing": ["974b51b4-dc30-4dea-8351-24b486874304", "fa3f942d-6a8a-46e0-9815-a4c2421d12fe"]}}}}, "link": [{"name": "No", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.08aaa8ba-254d-4d3a-aa4d-48c2190548dd", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.70833324-3be4-4040-809a-81faaa9881ca", "2025.70833324-3be4-4040-809a-81faaa9881ca"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.3322adc2-980e-42a4-9f4e-185032fc7bcb", "2025.3322adc2-980e-42a4-9f4e-185032fc7bcb"], "guid": "a4d6c0c3-6514-49a9-8c33-a812b06ecaed", "versionId": "5fc85884-5b08-4f26-a913-562be3453d8d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.5392c57d-a7c5-44f9-9157-81992ae6b995", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.3322adc2-980e-42a4-9f4e-185032fc7bcb", "2025.3322adc2-980e-42a4-9f4e-185032fc7bcb"], "endStateId": "Out", "toProcessItemId": ["2025.737d2eee-815a-42c1-88ac-41e98ae4495b", "2025.737d2eee-815a-42c1-88ac-41e98ae4495b"], "guid": "72037cbb-adae-44e0-93b5-4c57de5b0da6", "versionId": "88563146-a222-4816-9500-801d089397be", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "Yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d2ee7b44-a5ee-47a9-8dbe-42f000f2a203", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.70833324-3be4-4040-809a-81faaa9881ca", "2025.70833324-3be4-4040-809a-81faaa9881ca"], "endStateId": "guid:14611baa90d47fd1:-642f9074:1894f1bfa8e:469", "toProcessItemId": ["2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6", "2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6"], "guid": "4e634fed-6b41-46f6-b8d0-a735bc08fcc9", "versionId": "98badc20-82a4-4110-975a-62239b103200", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.2272e93e-6e5b-40c1-91f6-025df7b1d366", "processId": "1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6", "2025.720aa561-61d8-4ad4-bda1-f6b2f93b67d6"], "endStateId": "Out", "toProcessItemId": ["2025.737d2eee-815a-42c1-88ac-41e98ae4495b", "2025.737d2eee-815a-42c1-88ac-41e98ae4495b"], "guid": "7ce76dda-170b-4404-a8d1-624a70a7d581", "versionId": "d65ed943-3f69-43df-8a99-f39d21481d8e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}