{"id": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "versionId": "8856132e-d9e1-48a0-99c8-999f2e996e33", "name": "Init ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "name": "Init ODC Request", "lastModified": "1738480102663", "lastModifiedBy": "bawa<PERSON><PERSON>", "processId": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f", "2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2868", "versionId": "8856132e-d9e1-48a0-99c8-999f2e996e33", "dependencySummary": "<dependencySummary id=\"bpdid:f7f6553919972802:-74de7c2c:194a0205f0e:298d\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.9d7c8285-0639-40bc-85c7-70dd53b43140\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"048a42a8-dbc4-4c10-8206-713a33ddf554\"},{\"incoming\":[\"6b52cd5b-72b2-4808-86e5-b29efcacefb1\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2866\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"ddc67558-5d18-4966-8a8a-3880c50d3f63\"},{\"targetRef\":\"fdb88fd2-37b3-4259-85d0-c35f4de2843f\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9d7c8285-0639-40bc-85c7-70dd53b43140\",\"sourceRef\":\"048a42a8-dbc4-4c10-8206-713a33ddf554\"},{\"startQuantity\":1,\"outgoing\":[\"6b52cd5b-72b2-4808-86e5-b29efcacefb1\"],\"incoming\":[\"2027.9d7c8285-0639-40bc-85c7-70dd53b43140\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":360,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Init ODC Request\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"fdb88fd2-37b3-4259-85d0-c35f4de2843f\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.requestDate=  new TWDate();\\r\\ntw.local.odcRequest.ImporterName= \\\"Somaia Galal\\\";\\r\\ntw.local.odcRequest.BasicDetails.requestState = \\\"new\\\";\\r\\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \\\"00104230000788\\\";\\r\\ntw.local.odcRequest.BasicDetails.contractStage = \\\"stage\\\";\\r\\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \\\"export purpose\\\";\\r\\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \\\"payment terms\\\";\\r\\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.productCategory.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.productCategory.value = \\\"product category\\\";\\r\\ntw.local.odcRequest.BasicDetails.commodityDescription = \\\"commodity Description\\\";\\r\\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \\\"Egypt\\\";\\r\\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\\r\\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\\r\\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \\\"2341234\\\";\\r\\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\r\\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\r\\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\\r\\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \\\"7890234\\\";\\r\\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\r\\n\\/\\/tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\r\\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 20.0;\\r\\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \\\"EGP\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \\\"EGP\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\\r\\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 10000.0;\\r\\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"********\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \\\"2********\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \\\"********\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \\\"12345\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \\\"12345\\\";\\r\\ntw.local.odcRequest.complianceApproval = false;\\r\\n\\/\\/tw.local.odcRequest.stepLog = new tw.object.StepLog();\\r\\ntw.local.odcRequest.stepLog.startTime = new TWDate();\\r\\ntw.local.odcRequest.stepLog.endTime = new TWDate();\\r\\ntw.local.odcRequest.stepLog.userName = \\\"\\\";\\r\\ntw.local.odcRequest.stepLog.role = \\\"\\\";\\r\\ntw.local.odcRequest.stepLog.step = \\\"\\\";\\r\\ntw.local.odcRequest.stepLog.action = \\\"\\\";\\r\\ntw.local.odcRequest.stepLog.comment = \\\"\\\";\\r\\ntw.local.odcRequest.stepLog.terminateReason = \\\"\\\";\\r\\ntw.local.odcRequest.stepLog.returnReason = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.actions[0] = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\\r\\ntw.local.odcRequest.attachmentDetails.folderID = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\r\\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.History = new tw.object.listOf.StepLog();\\r\\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\\r\\ntw.local.odcRequest.History[0].startTime = new TWDate();\\r\\ntw.local.odcRequest.History[0].endTime = new TWDate();\\r\\ntw.local.odcRequest.History[0].userName = \\\"\\\";\\r\\ntw.local.odcRequest.History[0].role = \\\"Maker\\\";\\r\\ntw.local.odcRequest.History[0].step = \\\"act06\\\";\\r\\ntw.local.odcRequest.History[0].action = \\\"submit\\\";\\r\\ntw.local.odcRequest.History[0].comment = \\\"no comment\\\";\\r\\ntw.local.odcRequest.History[0].terminateReason = \\\"\\\";\\r\\ntw.local.odcRequest.History[0].returnReason = \\\"\\\";\\r\\n\"]}},{\"targetRef\":\"ddc67558-5d18-4966-8a8a-3880c50d3f63\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"6b52cd5b-72b2-4808-86e5-b29efcacefb1\",\"sourceRef\":\"fdb88fd2-37b3-4259-85d0-c35f4de2843f\"},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":362,\"y\":184,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Copy of Init ODC Request\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"baaed40a-0898-4f73-89dd-f33711c93c61\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest = new tw.object.ODCRequest();\\r\\ntw.local.odcRequest.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.requestNature.name = \\\"Update Request\\\";\\r\\ntw.local.odcRequest.requestNature.value = \\\"update\\\";\\r\\ntw.local.odcRequest.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.requestType.name = \\\"collection\\\" ;\\r\\ntw.local.odcRequest.requestType.value = \\\"ODC Collection\\\";\\r\\ntw.local.odcRequest.cif = \\\"02366014\\\";\\r\\ntw.local.odcRequest.customerName = \\\"somaia\\\";\\r\\ntw.local.odcRequest.parentRequestNo = \\\"\\\";\\r\\ntw.local.odcRequest.requestDate = new TWDate();\\r\\ntw.local.odcRequest.ImporterName = \\\"importer name\\\";\\r\\ntw.local.odcRequest.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\r\\ntw.local.odcRequest.appInfo.requestDate = \\\"\\\";\\r\\ntw.local.odcRequest.appInfo.status = \\\"In progress\\\";\\r\\ntw.local.odcRequest.appInfo.subStatus = \\\"Initiated\\\";\\r\\ntw.local.odcRequest.appInfo.initiator = \\\"odchubcumkr10\\\";\\r\\ntw.local.odcRequest.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.appInfo.branch.name = \\\"\\\";\\r\\ntw.local.odcRequest.appInfo.branch.value = \\\"\\\";\\r\\ntw.local.odcRequest.appInfo.requestName = \\\"ODC Collection\\\";\\r\\ntw.local.odcRequest.appInfo.requestType = \\\"\\\";\\r\\ntw.local.odcRequest.appInfo.stepName = \\\"\\\";\\r\\ntw.local.odcRequest.appInfo.appRef = \\\"\\\";\\r\\ntw.local.odcRequest.appInfo.appID = \\\"\\\";\\r\\ntw.local.odcRequest.appInfo.instanceID = tw.system.currentProcessInstance.id;\\r\\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\\r\\ntw.local.odcRequest.CustomerInfo.cif = \\\"02366014\\\";\\r\\ntw.local.odcRequest.CustomerInfo.customerName = \\\"Somaia\\\";\\r\\ntw.local.odcRequest.CustomerInfo.addressLine1 = \\\"Nasr Ciy\\\";\\r\\ntw.local.odcRequest.CustomerInfo.addressLine2 = \\\"addressLine2\\\";\\r\\ntw.local.odcRequest.CustomerInfo.addressLine3 = \\\"\\\";\\r\\ntw.local.odcRequest.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.CustomerInfo.customerSector.name = \\\"customerSector\\\";\\r\\ntw.local.odcRequest.CustomerInfo.customerSector.value = \\\"customerSector\\\";\\r\\ntw.local.odcRequest.CustomerInfo.customerType = \\\"Individual\\\";\\r\\ntw.local.odcRequest.CustomerInfo.customerNoCBE = \\\"\\\";\\r\\ntw.local.odcRequest.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.CustomerInfo.facilityType.name = \\\"facilityType\\\";\\r\\ntw.local.odcRequest.CustomerInfo.facilityType.value = \\\"facilityType\\\";\\r\\ntw.local.odcRequest.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\r\\ntw.local.odcRequest.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\r\\ntw.local.odcRequest.CustomerInfo.taxCardNo = \\\"222\\\";\\r\\ntw.local.odcRequest.CustomerInfo.importCardNo = \\\"1245\\\";\\r\\ntw.local.odcRequest.CustomerInfo.initiationHub = \\\"initiationHub\\\";\\r\\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\\r\\ntw.local.odcRequest.BasicDetails.requestNature = \\\"update\\\";\\r\\ntw.local.odcRequest.BasicDetails.requestType = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.parentRequestNo = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.requestState = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.contractStage = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.productCategory.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.productCategory.value = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.commodityDescription = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\\r\\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\\r\\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\r\\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\r\\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\\r\\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\r\\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.customerName = \\\"Somaia\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.customerAddress = \\\"Nasr City\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = \\\"Instructions Extra\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = \\\"special Instructions Extra\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\r\\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\r\\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 0.0;\\r\\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \\\"EGP\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \\\"EGP\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\\r\\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 0.0;\\r\\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \\\"collectionAccount\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \\\"collectionAccount\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\\r\\ntw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FcCollections.currency.name = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.currency.value = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.standardExchangeRate = 0.0;\\r\\ntw.local.odcRequest.FcCollections.negotiatedExchangeRate = 0.0;\\r\\ntw.local.odcRequest.FcCollections.fromDate = new TWDate();\\r\\ntw.local.odcRequest.FcCollections.ToDate = new TWDate();\\r\\ntw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FcCollections.accountNo.name = \\\"34566\\\";\\r\\ntw.local.odcRequest.FcCollections.accountNo.value = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\r\\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 566.0;\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 232.0;\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \\\"EG\\\";\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\r\\ntw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\r\\ntw.local.odcRequest.FcCollections.isReversed = false;\\r\\ntw.local.odcRequest.FcCollections.usedAmount = 0.0;\\r\\ntw.local.odcRequest.FcCollections.calculatedAmount = 0.0;\\r\\ntw.local.odcRequest.FcCollections.totalAllocatedAmount = 0.0;\\r\\ntw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FcCollections.listOfAccounts[0].name = \\\"\\\";\\r\\ntw.local.odcRequest.FcCollections.listOfAccounts[0].value = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\r\\ntw.local.odcRequest.FinancialDetailsFO.discount = 0.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.extraCharges = 0.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.ourCharges = 0.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.amountSight = 340.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization = 20.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.amountDefAvalization = 30.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.collectableAmount = 0.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 0.0;\\r\\ntw.local.odcRequest.FinancialDetailsFO.maturityDate = new TWDate();\\r\\ntw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity = 2;\\r\\ntw.local.odcRequest.FinancialDetailsFO.referenceNo = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsFO.financeApprovalNo = \\\"234\\\";\\r\\ntw.local.odcRequest.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.FinancialDetailsFO.executionHub.name = \\\"\\\";\\r\\ntw.local.odcRequest.FinancialDetailsFO.executionHub.value = \\\"\\\"; \\r\\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\\r\\ntw.local.odcRequest.OdcCollection.amount = 0.0;\\r\\ntw.local.odcRequest.OdcCollection.currency = \\\"\\\";\\r\\ntw.local.odcRequest.OdcCollection.informCADAboutTheCollection = false; \\r\\n tw.local.odcRequest.complianceApproval = false;\\r\\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\\r\\ntw.local.odcRequest.stepLog.startTime = new TWDate();\\r\\ntw.local.odcRequest.stepLog.endTime = new TWDate();\\r\\n\\/\\/tw.local.odcRequest.stepLog.userName = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.stepLog.role = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.stepLog.step = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.stepLog.action = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.stepLog.comment = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.stepLog.terminateReason = \\\"\\\";\\r\\n\\/\\/tw.local.odcRequest.stepLog.returnReason = \\\"\\\";\\r\\ntw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\\r\\ntw.local.odcRequest.actions[0] = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\\r\\ntw.local.odcRequest.attachmentDetails.folderID = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\r\\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\r\\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \\\"\\\";\\r\\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\r\\ntw.local.odcRequest.initiator = \\\"\\\";\\r\\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\\r\\ntw.local.odcRequest.complianceComments[0] = new tw.object.StepLog();\\r\\ntw.local.odcRequest.complianceComments[0].startTime = new TWDate();\\r\\ntw.local.odcRequest.complianceComments[0].endTime = new TWDate();\\r\\ntw.local.odcRequest.complianceComments[0].userName = \\\"\\\";\\r\\ntw.local.odcRequest.complianceComments[0].role = \\\"\\\";\\r\\ntw.local.odcRequest.complianceComments[0].step = \\\"\\\";\\r\\ntw.local.odcRequest.complianceComments[0].action = \\\"\\\";\\r\\ntw.local.odcRequest.complianceComments[0].comment = \\\"\\\";\\r\\ntw.local.odcRequest.complianceComments[0].terminateReason = \\\"\\\";\\r\\ntw.local.odcRequest.complianceComments[0].returnReason = \\\"\\\";\\r\\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\\r\\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\\r\\ntw.local.odcRequest.History[0].startTime = new TWDate();\\r\\ntw.local.odcRequest.History[0].endTime = new TWDate()+5;\\r\\ntw.local.odcRequest.History[0].userName = \\\"Maker\\\";\\r\\ntw.local.odcRequest.History[0].role = \\\"Maker\\\";\\r\\ntw.local.odcRequest.History[0].step = \\\"Act06\\\";\\r\\ntw.local.odcRequest.History[0].action = \\\"Approve\\\";\\r\\ntw.local.odcRequest.History[0].comment = \\\"comment1\\\";\\r\\ntw.local.odcRequest.History[0].terminateReason = \\\"\\\";\\r\\ntw.local.odcRequest.History[0].returnReason = \\\"\\\";\\r\\ntw.local.odcRequest.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\ntw.local.odcRequest.documentSource.name = \\\"\\\";\\r\\ntw.local.odcRequest.documentSource.value = \\\"\\\";\\r\\ntw.local.odcRequest.folderID = \\\"\\\";\\r\\n\\r\\n\\r\\n\"]}}],\"laneSet\":[{\"id\":\"d98653bf-4f2a-4878-8e5f-31117e8c085f\",\"lane\":[{\"flowNodeRef\":[\"048a42a8-dbc4-4c10-8206-713a33ddf554\",\"ddc67558-5d18-4966-8a8a-3880c50d3f63\",\"fdb88fd2-37b3-4259-85d0-c35f4de2843f\",\"baaed40a-0898-4f73-89dd-f33711c93c61\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"7376be27-ce95-4f86-87f9-4874d70f3b3c\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Init ODC Request\",\"declaredType\":\"process\",\"id\":\"1.8e36e548-f4cb-44df-9910-c33585076ffa\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.071eaf11-ca85-4a87-8d0a-3e010885561e\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.071eaf11-ca85-4a87-8d0a-3e010885561e\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.MultiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.MultiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.MultiTenorDates[0].date = new TWDate();\\nautoObject.MultiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = 0;\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].rate = 0.0;\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].minimumAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.initiator = \\\"\\\";\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb", "processId": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "bb896884-24b4-4bc3-9f79-d13718aa9ed1", "versionId": "6ca65c9a-0f2b-4d7b-92cd-ddab08279e8f"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.071eaf11-ca85-4a87-8d0a-3e010885561e", "processId": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "afe0ecf0-eaaf-475f-85ae-ce4a4125f416", "versionId": "212b3e0f-3491-41e6-9536-090bd940fac2"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f", "processId": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "name": "Init ODC Request", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.b51189dc-236d-4dda-bd3d-7e03d4eeb5a7", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2830", "versionId": "5a63b2db-e5de-44ee-8b3a-d3ac50bb3c20", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.62769f3c-a6f5-421a-8e71-c2b61e2b897c", "processItemId": "2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f", "location": "2", "script": {"isNull": "true"}, "guid": "31abb769-393e-45b3-82dd-0748003e061c", "versionId": "092fbeac-a900-4d48-b1cb-384864019ad3"}, "layoutData": {"x": "360", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.b51189dc-236d-4dda-bd3d-7e03d4eeb5a7", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.odcRequest.requestDate=  new TWDate();\r\r\ntw.local.odcRequest.ImporterName= \"Somaia Galal\";\r\r\ntw.local.odcRequest.BasicDetails.requestState = \"new\";\r\r\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \"00104230000788\";\r\r\ntw.local.odcRequest.BasicDetails.contractStage = \"stage\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \"export purpose\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \"payment terms\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.productCategory.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory.value = \"product category\";\r\r\ntw.local.odcRequest.BasicDetails.commodityDescription = \"commodity Description\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \"Egypt\";\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"2341234\";\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"7890234\";\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\n//tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 20.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\r\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 10000.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"********\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \"2********\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \"********\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \"12345\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \"12345\";\r\r\ntw.local.odcRequest.complianceApproval = false;\r\r\n//tw.local.odcRequest.stepLog = new tw.object.StepLog();\r\r\ntw.local.odcRequest.stepLog.startTime = new TWDate();\r\r\ntw.local.odcRequest.stepLog.endTime = new TWDate();\r\r\ntw.local.odcRequest.stepLog.userName = \"\";\r\r\ntw.local.odcRequest.stepLog.role = \"\";\r\r\ntw.local.odcRequest.stepLog.step = \"\";\r\r\ntw.local.odcRequest.stepLog.action = \"\";\r\r\ntw.local.odcRequest.stepLog.comment = \"\";\r\r\ntw.local.odcRequest.stepLog.terminateReason = \"\";\r\r\ntw.local.odcRequest.stepLog.returnReason = \"\";\r\r\n//tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.actions[0] = \"\";\r\r\n//tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\r\ntw.local.odcRequest.attachmentDetails.folderID = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"\";\r\r\n//tw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\r\r\ntw.local.odcRequest.History[0].startTime = new TWDate();\r\r\ntw.local.odcRequest.History[0].endTime = new TWDate();\r\r\ntw.local.odcRequest.History[0].userName = \"\";\r\r\ntw.local.odcRequest.History[0].role = \"Maker\";\r\r\ntw.local.odcRequest.History[0].step = \"act06\";\r\r\ntw.local.odcRequest.History[0].action = \"submit\";\r\r\ntw.local.odcRequest.History[0].comment = \"no comment\";\r\r\ntw.local.odcRequest.History[0].terminateReason = \"\";\r\r\ntw.local.odcRequest.History[0].returnReason = \"\";\r\r\n", "isRule": "false", "guid": "4a613616-4451-45af-a43d-8318d70c959b", "versionId": "f8286857-d99a-41ce-9790-7b420c784d75"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ddc67558-5d18-4966-8a8a-3880c50d3f63", "processId": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.f47f3b6c-8a3f-4193-8d91-e25f8553cb70", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2866", "versionId": "d784f284-d086-4b2f-a312-39fe4efd71f6", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.f47f3b6c-8a3f-4193-8d91-e25f8553cb70", "haltProcess": "false", "guid": "ab7e7089-8fe7-48e7-a7b6-3c3e769a9e7a", "versionId": "c99af813-92c5-440f-a031-e80f74cf99df"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.baaed40a-0898-4f73-89dd-f33711c93c61", "processId": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "name": "Copy of Init ODC Request", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.e691e74d-ccdf-446f-89d2-bdc63a6f093f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:aa4c986259b1691d:-23a6d209:18a12cf2ccc:228a", "versionId": "e50bffe1-e699-413f-b4a9-8f3bc29aed26", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "362", "y": "184", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.e691e74d-ccdf-446f-89d2-bdc63a6f093f", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.odcRequest = new tw.object.ODCRequest();\r\r\ntw.local.odcRequest.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.requestNature.name = \"Update Request\";\r\r\ntw.local.odcRequest.requestNature.value = \"update\";\r\r\ntw.local.odcRequest.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.requestType.name = \"collection\" ;\r\r\ntw.local.odcRequest.requestType.value = \"ODC Collection\";\r\r\ntw.local.odcRequest.cif = \"02366014\";\r\r\ntw.local.odcRequest.customerName = \"somaia\";\r\r\ntw.local.odcRequest.parentRequestNo = \"\";\r\r\ntw.local.odcRequest.requestDate = new TWDate();\r\r\ntw.local.odcRequest.ImporterName = \"importer name\";\r\r\ntw.local.odcRequest.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\r\ntw.local.odcRequest.appInfo.requestDate = \"\";\r\r\ntw.local.odcRequest.appInfo.status = \"In progress\";\r\r\ntw.local.odcRequest.appInfo.subStatus = \"Initiated\";\r\r\ntw.local.odcRequest.appInfo.initiator = \"odchubcumkr10\";\r\r\ntw.local.odcRequest.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.appInfo.branch.name = \"\";\r\r\ntw.local.odcRequest.appInfo.branch.value = \"\";\r\r\ntw.local.odcRequest.appInfo.requestName = \"ODC Collection\";\r\r\ntw.local.odcRequest.appInfo.requestType = \"\";\r\r\ntw.local.odcRequest.appInfo.stepName = \"\";\r\r\ntw.local.odcRequest.appInfo.appRef = \"\";\r\r\ntw.local.odcRequest.appInfo.appID = \"\";\r\r\ntw.local.odcRequest.appInfo.instanceID = tw.system.currentProcessInstance.id;\r\r\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\r\r\ntw.local.odcRequest.CustomerInfo.cif = \"02366014\";\r\r\ntw.local.odcRequest.CustomerInfo.customerName = \"Somaia\";\r\r\ntw.local.odcRequest.CustomerInfo.addressLine1 = \"Nasr Ciy\";\r\r\ntw.local.odcRequest.CustomerInfo.addressLine2 = \"addressLine2\";\r\r\ntw.local.odcRequest.CustomerInfo.addressLine3 = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.CustomerInfo.customerSector.name = \"customerSector\";\r\r\ntw.local.odcRequest.CustomerInfo.customerSector.value = \"customerSector\";\r\r\ntw.local.odcRequest.CustomerInfo.customerType = \"Individual\";\r\r\ntw.local.odcRequest.CustomerInfo.customerNoCBE = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.CustomerInfo.facilityType.name = \"facilityType\";\r\r\ntw.local.odcRequest.CustomerInfo.facilityType.value = \"facilityType\";\r\r\ntw.local.odcRequest.CustomerInfo.commercialRegistrationNo = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.commercialRegistrationOffice = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.taxCardNo = \"222\";\r\r\ntw.local.odcRequest.CustomerInfo.importCardNo = \"1245\";\r\r\ntw.local.odcRequest.CustomerInfo.initiationHub = \"initiationHub\";\r\r\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\r\r\ntw.local.odcRequest.BasicDetails.requestNature = \"update\";\r\r\ntw.local.odcRequest.BasicDetails.requestType = \"\";\r\r\ntw.local.odcRequest.BasicDetails.parentRequestNo = \"\";\r\r\ntw.local.odcRequest.BasicDetails.requestState = \"\";\r\r\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \"\";\r\r\ntw.local.odcRequest.BasicDetails.contractStage = \"\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.productCategory.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.commodityDescription = \"\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.customerName = \"Somaia\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.customerAddress = \"Nasr City\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = \"Instructions Extra\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = \"special Instructions Extra\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\r\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \"collectionAccount\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \"collectionAccount\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\r\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\r\ntw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.currency.name = \"\";\r\r\ntw.local.odcRequest.FcCollections.currency.value = \"\";\r\r\ntw.local.odcRequest.FcCollections.standardExchangeRate = 0.0;\r\r\ntw.local.odcRequest.FcCollections.negotiatedExchangeRate = 0.0;\r\r\ntw.local.odcRequest.FcCollections.fromDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.ToDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.accountNo.name = \"34566\";\r\r\ntw.local.odcRequest.FcCollections.accountNo.value = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \"\";\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 566.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 232.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \"EG\";\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\ntw.local.odcRequest.FcCollections.isReversed = false;\r\r\ntw.local.odcRequest.FcCollections.usedAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.calculatedAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.totalAllocatedAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0].name = \"\";\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0].value = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\r\ntw.local.odcRequest.FinancialDetailsFO.discount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.extraCharges = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.ourCharges = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.amountSight = 340.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization = 20.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.amountDefAvalization = 30.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.collectableAmount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.maturityDate = new TWDate();\r\r\ntw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity = 2;\r\r\ntw.local.odcRequest.FinancialDetailsFO.referenceNo = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsFO.financeApprovalNo = \"234\";\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub.name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub.value = \"\"; \r\r\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\r\r\ntw.local.odcRequest.OdcCollection.amount = 0.0;\r\r\ntw.local.odcRequest.OdcCollection.currency = \"\";\r\r\ntw.local.odcRequest.OdcCollection.informCADAboutTheCollection = false; \r\r\n tw.local.odcRequest.complianceApproval = false;\r\r\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\r\r\ntw.local.odcRequest.stepLog.startTime = new TWDate();\r\r\ntw.local.odcRequest.stepLog.endTime = new TWDate();\r\r\n//tw.local.odcRequest.stepLog.userName = \"\";\r\r\n//tw.local.odcRequest.stepLog.role = \"\";\r\r\n//tw.local.odcRequest.stepLog.step = \"\";\r\r\n//tw.local.odcRequest.stepLog.action = \"\";\r\r\n//tw.local.odcRequest.stepLog.comment = \"\";\r\r\n//tw.local.odcRequest.stepLog.terminateReason = \"\";\r\r\n//tw.local.odcRequest.stepLog.returnReason = \"\";\r\r\ntw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.actions[0] = \"\";\r\r\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\r\ntw.local.odcRequest.attachmentDetails.folderID = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"\";\r\r\ntw.local.odcRequest.initiator = \"\";\r\r\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.complianceComments[0] = new tw.object.StepLog();\r\r\ntw.local.odcRequest.complianceComments[0].startTime = new TWDate();\r\r\ntw.local.odcRequest.complianceComments[0].endTime = new TWDate();\r\r\ntw.local.odcRequest.complianceComments[0].userName = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].role = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].step = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].action = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].comment = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].terminateReason = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].returnReason = \"\";\r\r\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\r\r\ntw.local.odcRequest.History[0].startTime = new TWDate();\r\r\ntw.local.odcRequest.History[0].endTime = new TWDate()+5;\r\r\ntw.local.odcRequest.History[0].userName = \"Maker\";\r\r\ntw.local.odcRequest.History[0].role = \"Maker\";\r\r\ntw.local.odcRequest.History[0].step = \"Act06\";\r\r\ntw.local.odcRequest.History[0].action = \"Approve\";\r\r\ntw.local.odcRequest.History[0].comment = \"comment1\";\r\r\ntw.local.odcRequest.History[0].terminateReason = \"\";\r\r\ntw.local.odcRequest.History[0].returnReason = \"\";\r\r\ntw.local.odcRequest.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.documentSource.name = \"\";\r\r\ntw.local.odcRequest.documentSource.value = \"\";\r\r\ntw.local.odcRequest.folderID = \"\";\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "ff72e73d-37af-4c64-96aa-71ccbc6f237b", "versionId": "ac2fa919-e0c1-4b56-8eae-94a7d1874ac2"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Init ODC Request", "id": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.MultiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.MultiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.MultiTenorDates[0].date = new TWDate();\r\nautoObject.MultiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = 0;\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].rate = 0.0;\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].minimumAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.initiator = \"\";\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject", "useDefault": "false"}}}, "ns16:dataOutput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.071eaf11-ca85-4a87-8d0a-3e010885561e"}, "ns16:inputSet": {"ns16:dataInputRefs": "2055.dceb084b-62b9-4e27-8cb9-bba56bf718cb"}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.071eaf11-ca85-4a87-8d0a-3e010885561e"}}, "ns16:laneSet": {"id": "d98653bf-4f2a-4878-8e5f-31117e8c085f", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "7376be27-ce95-4f86-87f9-4874d70f3b3c", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["048a42a8-dbc4-4c10-8206-713a33ddf554", "ddc67558-5d18-4966-8a8a-3880c50d3f63", "fdb88fd2-37b3-4259-85d0-c35f4de2843f", "baaed40a-0898-4f73-89dd-f33711c93c61"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "048a42a8-dbc4-4c10-8206-713a33ddf554", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.9d7c8285-0639-40bc-85c7-70dd53b43140"}, "ns16:endEvent": {"name": "End", "id": "ddc67558-5d18-4966-8a8a-3880c50d3f63", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:-2866"}, "ns16:incoming": "6b52cd5b-72b2-4808-86e5-b29efcacefb1"}, "ns16:sequenceFlow": [{"sourceRef": "048a42a8-dbc4-4c10-8206-713a33ddf554", "targetRef": "fdb88fd2-37b3-4259-85d0-c35f4de2843f", "name": "To End", "id": "2027.9d7c8285-0639-40bc-85c7-70dd53b43140", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fdb88fd2-37b3-4259-85d0-c35f4de2843f", "targetRef": "ddc67558-5d18-4966-8a8a-3880c50d3f63", "name": "To End", "id": "6b52cd5b-72b2-4808-86e5-b29efcacefb1", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Init ODC Request", "id": "fdb88fd2-37b3-4259-85d0-c35f4de2843f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "360", "y": "57", "width": "95", "height": "70"}, "ns3:postAssignmentScript": ""}, "ns16:incoming": "2027.9d7c8285-0639-40bc-85c7-70dd53b43140", "ns16:outgoing": "6b52cd5b-72b2-4808-86e5-b29efcacefb1", "ns16:script": "tw.local.odcRequest.requestDate=  new TWDate();\r\r\ntw.local.odcRequest.ImporterName= \"Somaia Galal\";\r\r\ntw.local.odcRequest.BasicDetails.requestState = \"new\";\r\r\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \"00104230000788\";\r\r\ntw.local.odcRequest.BasicDetails.contractStage = \"stage\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \"export purpose\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \"payment terms\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.productCategory.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory.value = \"product category\";\r\r\ntw.local.odcRequest.BasicDetails.commodityDescription = \"commodity Description\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \"Egypt\";\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"2341234\";\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"7890234\";\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\n//tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 20.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\r\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 10000.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"********\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \"2********\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \"********\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \"12345\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \"12345\";\r\r\ntw.local.odcRequest.complianceApproval = false;\r\r\n//tw.local.odcRequest.stepLog = new tw.object.StepLog();\r\r\ntw.local.odcRequest.stepLog.startTime = new TWDate();\r\r\ntw.local.odcRequest.stepLog.endTime = new TWDate();\r\r\ntw.local.odcRequest.stepLog.userName = \"\";\r\r\ntw.local.odcRequest.stepLog.role = \"\";\r\r\ntw.local.odcRequest.stepLog.step = \"\";\r\r\ntw.local.odcRequest.stepLog.action = \"\";\r\r\ntw.local.odcRequest.stepLog.comment = \"\";\r\r\ntw.local.odcRequest.stepLog.terminateReason = \"\";\r\r\ntw.local.odcRequest.stepLog.returnReason = \"\";\r\r\n//tw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.actions[0] = \"\";\r\r\n//tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\r\ntw.local.odcRequest.attachmentDetails.folderID = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"\";\r\r\n//tw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\r\r\ntw.local.odcRequest.History[0].startTime = new TWDate();\r\r\ntw.local.odcRequest.History[0].endTime = new TWDate();\r\r\ntw.local.odcRequest.History[0].userName = \"\";\r\r\ntw.local.odcRequest.History[0].role = \"Maker\";\r\r\ntw.local.odcRequest.History[0].step = \"act06\";\r\r\ntw.local.odcRequest.History[0].action = \"submit\";\r\r\ntw.local.odcRequest.History[0].comment = \"no comment\";\r\r\ntw.local.odcRequest.History[0].terminateReason = \"\";\r\r\ntw.local.odcRequest.History[0].returnReason = \"\";\r\r\n"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Copy of Init ODC Request", "id": "baaed40a-0898-4f73-89dd-f33711c93c61", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "362", "y": "184", "width": "95", "height": "70"}}, "ns16:script": "tw.local.odcRequest = new tw.object.ODCRequest();\r\r\ntw.local.odcRequest.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.requestNature.name = \"Update Request\";\r\r\ntw.local.odcRequest.requestNature.value = \"update\";\r\r\ntw.local.odcRequest.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.requestType.name = \"collection\" ;\r\r\ntw.local.odcRequest.requestType.value = \"ODC Collection\";\r\r\ntw.local.odcRequest.cif = \"02366014\";\r\r\ntw.local.odcRequest.customerName = \"somaia\";\r\r\ntw.local.odcRequest.parentRequestNo = \"\";\r\r\ntw.local.odcRequest.requestDate = new TWDate();\r\r\ntw.local.odcRequest.ImporterName = \"importer name\";\r\r\ntw.local.odcRequest.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\r\ntw.local.odcRequest.appInfo.requestDate = \"\";\r\r\ntw.local.odcRequest.appInfo.status = \"In progress\";\r\r\ntw.local.odcRequest.appInfo.subStatus = \"Initiated\";\r\r\ntw.local.odcRequest.appInfo.initiator = \"odchubcumkr10\";\r\r\ntw.local.odcRequest.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.appInfo.branch.name = \"\";\r\r\ntw.local.odcRequest.appInfo.branch.value = \"\";\r\r\ntw.local.odcRequest.appInfo.requestName = \"ODC Collection\";\r\r\ntw.local.odcRequest.appInfo.requestType = \"\";\r\r\ntw.local.odcRequest.appInfo.stepName = \"\";\r\r\ntw.local.odcRequest.appInfo.appRef = \"\";\r\r\ntw.local.odcRequest.appInfo.appID = \"\";\r\r\ntw.local.odcRequest.appInfo.instanceID = tw.system.currentProcessInstance.id;\r\r\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\r\r\ntw.local.odcRequest.CustomerInfo.cif = \"02366014\";\r\r\ntw.local.odcRequest.CustomerInfo.customerName = \"Somaia\";\r\r\ntw.local.odcRequest.CustomerInfo.addressLine1 = \"Nasr Ciy\";\r\r\ntw.local.odcRequest.CustomerInfo.addressLine2 = \"addressLine2\";\r\r\ntw.local.odcRequest.CustomerInfo.addressLine3 = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.CustomerInfo.customerSector.name = \"customerSector\";\r\r\ntw.local.odcRequest.CustomerInfo.customerSector.value = \"customerSector\";\r\r\ntw.local.odcRequest.CustomerInfo.customerType = \"Individual\";\r\r\ntw.local.odcRequest.CustomerInfo.customerNoCBE = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.CustomerInfo.facilityType.name = \"facilityType\";\r\r\ntw.local.odcRequest.CustomerInfo.facilityType.value = \"facilityType\";\r\r\ntw.local.odcRequest.CustomerInfo.commercialRegistrationNo = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.commercialRegistrationOffice = \"\";\r\r\ntw.local.odcRequest.CustomerInfo.taxCardNo = \"222\";\r\r\ntw.local.odcRequest.CustomerInfo.importCardNo = \"1245\";\r\r\ntw.local.odcRequest.CustomerInfo.initiationHub = \"initiationHub\";\r\r\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\r\r\ntw.local.odcRequest.BasicDetails.requestNature = \"update\";\r\r\ntw.local.odcRequest.BasicDetails.requestType = \"\";\r\r\ntw.local.odcRequest.BasicDetails.parentRequestNo = \"\";\r\r\ntw.local.odcRequest.BasicDetails.requestState = \"\";\r\r\ntw.local.odcRequest.BasicDetails.flexCubeContractNo = \"\";\r\r\ntw.local.odcRequest.BasicDetails.contractStage = \"\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.exportPurpose.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.paymentTerms.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.productCategory.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.productCategory.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.commodityDescription = \"\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.name = \"\";\r\r\ntw.local.odcRequest.BasicDetails.CountryOfOrigin.value = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"\";\r\r\ntw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.customerName = \"Somaia\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.customerAddress = \"Nasr City\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = \"Instructions Extra\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = \"special Instructions Extra\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\r\ntw.local.odcRequest.FinancialDetailsBR.documentAmount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.name = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.currency.value = \"EGP\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\r\ntw.local.odcRequest.FinancialDetailsBR.amountAdvanced = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.name = \"collectionAccount\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.collectionAccount.value = \"collectionAccount\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\r\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\r\ntw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.currency.name = \"\";\r\r\ntw.local.odcRequest.FcCollections.currency.value = \"\";\r\r\ntw.local.odcRequest.FcCollections.standardExchangeRate = 0.0;\r\r\ntw.local.odcRequest.FcCollections.negotiatedExchangeRate = 0.0;\r\r\ntw.local.odcRequest.FcCollections.fromDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.ToDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.accountNo.name = \"34566\";\r\r\ntw.local.odcRequest.FcCollections.accountNo.value = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \"\";\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \"\";\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 566.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 232.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \"EG\";\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\ntw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\ntw.local.odcRequest.FcCollections.isReversed = false;\r\r\ntw.local.odcRequest.FcCollections.usedAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.calculatedAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.totalAllocatedAmount = 0.0;\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0].name = \"\";\r\r\ntw.local.odcRequest.FcCollections.listOfAccounts[0].value = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\r\ntw.local.odcRequest.FinancialDetailsFO.discount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.extraCharges = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.ourCharges = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.amountSight = 340.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization = 20.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.amountDefAvalization = 30.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.collectableAmount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.outstandingAmount = 0.0;\r\r\ntw.local.odcRequest.FinancialDetailsFO.maturityDate = new TWDate();\r\r\ntw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity = 2;\r\r\ntw.local.odcRequest.FinancialDetailsFO.referenceNo = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsFO.financeApprovalNo = \"234\";\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub.name = \"\";\r\r\ntw.local.odcRequest.FinancialDetailsFO.executionHub.value = \"\"; \r\r\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\r\r\ntw.local.odcRequest.OdcCollection.amount = 0.0;\r\r\ntw.local.odcRequest.OdcCollection.currency = \"\";\r\r\ntw.local.odcRequest.OdcCollection.informCADAboutTheCollection = false; \r\r\n tw.local.odcRequest.complianceApproval = false;\r\r\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\r\r\ntw.local.odcRequest.stepLog.startTime = new TWDate();\r\r\ntw.local.odcRequest.stepLog.endTime = new TWDate();\r\r\n//tw.local.odcRequest.stepLog.userName = \"\";\r\r\n//tw.local.odcRequest.stepLog.role = \"\";\r\r\n//tw.local.odcRequest.stepLog.step = \"\";\r\r\n//tw.local.odcRequest.stepLog.action = \"\";\r\r\n//tw.local.odcRequest.stepLog.comment = \"\";\r\r\n//tw.local.odcRequest.stepLog.terminateReason = \"\";\r\r\n//tw.local.odcRequest.stepLog.returnReason = \"\";\r\r\ntw.local.odcRequest.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\r\ntw.local.odcRequest.actions[0] = \"\";\r\r\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\r\ntw.local.odcRequest.attachmentDetails.folderID = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\r\ntw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].name = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].description = \"\";\r\r\ntw.local.odcRequest.attachmentDetails.attachment[0].arabicName = \"\";\r\r\ntw.local.odcRequest.initiator = \"\";\r\r\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.complianceComments[0] = new tw.object.StepLog();\r\r\ntw.local.odcRequest.complianceComments[0].startTime = new TWDate();\r\r\ntw.local.odcRequest.complianceComments[0].endTime = new TWDate();\r\r\ntw.local.odcRequest.complianceComments[0].userName = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].role = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].step = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].action = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].comment = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].terminateReason = \"\";\r\r\ntw.local.odcRequest.complianceComments[0].returnReason = \"\";\r\r\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\r\ntw.local.odcRequest.History[0] = new tw.object.StepLog();\r\r\ntw.local.odcRequest.History[0].startTime = new TWDate();\r\r\ntw.local.odcRequest.History[0].endTime = new TWDate()+5;\r\r\ntw.local.odcRequest.History[0].userName = \"Maker\";\r\r\ntw.local.odcRequest.History[0].role = \"Maker\";\r\r\ntw.local.odcRequest.History[0].step = \"Act06\";\r\r\ntw.local.odcRequest.History[0].action = \"Approve\";\r\r\ntw.local.odcRequest.History[0].comment = \"comment1\";\r\r\ntw.local.odcRequest.History[0].terminateReason = \"\";\r\r\ntw.local.odcRequest.History[0].returnReason = \"\";\r\r\ntw.local.odcRequest.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\ntw.local.odcRequest.documentSource.name = \"\";\r\r\ntw.local.odcRequest.documentSource.value = \"\";\r\r\ntw.local.odcRequest.folderID = \"\";\r\r\n\r\r\n\r\r\n"}]}}}, "link": {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6b52cd5b-72b2-4808-86e5-b29efcacefb1", "processId": "1.8e36e548-f4cb-44df-9910-c33585076ffa", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f", "2025.fdb88fd2-37b3-4259-85d0-c35f4de2843f"], "endStateId": "Out", "toProcessItemId": ["2025.ddc67558-5d18-4966-8a8a-3880c50d3f63", "2025.ddc67558-5d18-4966-8a8a-3880c50d3f63"], "guid": "ee1659db-711d-4c86-9c2b-aa95dc17b3be", "versionId": "094f94f8-7c5d-4530-8d3b-aa37ef0428e7", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}}}}, "subType": "12", "hasDetails": false}