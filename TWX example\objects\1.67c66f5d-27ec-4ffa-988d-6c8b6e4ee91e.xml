<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e" name="ODC BO Initialization">
        <lastModified>1699519439885</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4942</guid>
        <versionId>9ee21618-4ac4-44bd-9e2a-77dc15c7a31c</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:651a1a6abf396537:64776e00:18baeba64af:23fa" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.4df43bda-00dd-43c7-819d-1c19f07a5762"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"7d625bde-de84-4fde-8a9a-65f20a475b1c"},{"incoming":["5cfabfe4-3dc6-4722-8876-88d28df60370","56a02411-9a5c-4fdc-8a44-e84dbe8ea39d"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f"},{"targetRef":"ce49342f-4b17-433b-8e8d-9fb7f05403cb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Init","declaredType":"sequenceFlow","id":"2027.4df43bda-00dd-43c7-819d-1c19f07a5762","sourceRef":"7d625bde-de84-4fde-8a9a-65f20a475b1c"},{"startQuantity":1,"outgoing":["5cfabfe4-3dc6-4722-8876-88d28df60370"],"incoming":["2027.4df43bda-00dd-43c7-819d-1c19f07a5762"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":270,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Init","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ce49342f-4b17-433b-8e8d-9fb7f05403cb","scriptFormat":"text\/x-javascript","script":{"content":["if (!tw.local.odcRequest)\r\n\ttw.local.odcRequest = new tw.object.ODCRequest();\r\ntw.local.odcRequest.actions = new tw.object.listOf.String();\r\nif (!tw.local.odcRequest.appInfo) \r\n\ttw.local.odcRequest.appInfo = new tw.object.AppInfo();\r\ntw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();\r\ntw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\ntw.local.odcRequest.attachmentDetails.ecmProperties= new tw.object.ECMproperties();\r\ntw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();\r\ntw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\ntw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\n\/\/tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new tw.object.Date();\r\n\/\/tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\ntw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\ntw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\n\/\/tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new tw.object.Date();\r\n\/\/tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"\";\r\ntw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\ntw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\ntw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 0.0;\r\ntw.local.odcRequest.ChargesAndCommissions[0].waiver=false;\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.NameValuePair();\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.NameValuePair();\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\ntw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\n\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.NameValuePair();\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\ntw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\n\r\n\r\ntw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();\r\ntw.local.odcRequest.ContractCreation = new tw.object.ContractCreation();\r\ntw.local.odcRequest.ContractCreation.baseDate = new Date();\r\ntw.local.odcRequest.ContractCreation.valueDate = new tw.object.Date();\r\ntw.local.odcRequest.ContractCreation.sourceReference =\"\";\r\ntw.local.odcRequest.ContractLiquidation = new tw.object.ContractLiquidation();\r\ntw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();\r\ntw.local.odcRequest.documentSource = new tw.object.NameValuePair();\r\ntw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\ntw.local.odcRequest.FinancialDetailsBR.currency={};\r\n\r\ntw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\ntw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\ntw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\ntw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\n\r\ntw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\n\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date =new tw.object.Date();\r\ntw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\n\r\n\r\n\r\n\r\n\r\n\/\/tw.local.odcRequest.folderID= new tw.object.ECM;\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.ECMFolder();\r\ntw.local.odcRequest.History = new tw.object.listOf.StepLog();\r\ntw.local.odcRequest.ImporterDetails = new tw.object.ImporterDetails();\r\n\r\ntw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();\r\ntw.local.odcRequest.Parties = new tw.object.odcParties();\r\ntw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\r\ntw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\r\n\/\/tw.local.odcRequest.Parties.party = new tw.object.listOf.partyTypes();\r\ntw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\r\ntw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\r\n\r\ntw.local.odcRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\ntw.local.odcRequest.requestDate = new tw.object.Date();\r\ntw.local.odcRequest.requestNature = new tw.object.NameValuePair();\r\ntw.local.odcRequest.requestNature.name = \"\";\r\ntw.local.odcRequest.requestNature.value = \"\";\r\ntw.local.odcRequest.requestType = new tw.object.NameValuePair();\r\ntw.local.odcRequest.requestType.name = \"\";\r\ntw.local.odcRequest.requestType.value = \"\";\r\ntw.local.odcRequest.ReversalReason = new tw.object.ReversalReason();\r\ntw.local.odcRequest.stepLog = new tw.object.StepLog();\r\n\r\ntw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;\r\ntw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\ntw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\n\r\n\r\n"]}},{"targetRef":"65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"5cfabfe4-3dc6-4722-8876-88d28df60370","sourceRef":"ce49342f-4b17-433b-8e8d-9fb7f05403cb"},{"startQuantity":1,"outgoing":["56a02411-9a5c-4fdc-8a44-e84dbe8ea39d"],"incoming":["ced22e79-23d6-4e88-8bd7-6e2da82e20e0"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":392,"y":140,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exp Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC BO Initialization\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"f6b6f3ee-04e7-4475-81d5-5ecdbf209604","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"56a02411-9a5c-4fdc-8a44-e84dbe8ea39d","sourceRef":"f6b6f3ee-04e7-4475-81d5-5ecdbf209604"},{"parallelMultiple":false,"outgoing":["ced22e79-23d6-4e88-8bd7-6e2da82e20e0"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"c52428cc-4b91-4b8a-8739-2af36e1115a5"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0dc2de68-b5db-4fd5-8665-729cd1e05d24","otherAttributes":{"eventImplId":"4109dacd-dce3-4098-835c-68dd14f7e876"}}],"attachedToRef":"ce49342f-4b17-433b-8e8d-9fb7f05403cb","extensionElements":{"nodeVisualInfo":[{"width":24,"x":305,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"77057b21-26bc-4e8a-889f-e9724bcea10b","outputSet":{}},{"targetRef":"f6b6f3ee-04e7-4475-81d5-5ecdbf209604","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exp Handling","declaredType":"sequenceFlow","id":"ced22e79-23d6-4e88-8bd7-6e2da82e20e0","sourceRef":"77057b21-26bc-4e8a-889f-e9724bcea10b"}],"laneSet":[{"id":"c979ec57-8e23-4209-8bba-3b9939ee5878","lane":[{"flowNodeRef":["7d625bde-de84-4fde-8a9a-65f20a475b1c","65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f","ce49342f-4b17-433b-8e8d-9fb7f05403cb","f6b6f3ee-04e7-4475-81d5-5ecdbf209604","77057b21-26bc-4e8a-889f-e9724bcea10b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"59ded5d7-dda4-437b-8ebb-16dc4cd8de36","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"ODC BO Initialization","declaredType":"process","id":"1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.247d12a8-cf76-495c-8cd4-700f32286023"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"id":"2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6"}],"inputSet":[{"dataInputRefs":["2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5"]}],"outputSet":[{"dataOutputRefs":["2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de","2055.247d12a8-cf76-495c-8cd4-700f32286023","2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","documentation":[{"content":["&lt;div&gt;&lt;br \/&gt;&lt;\/div&gt;"],"textFormat":"text\/plain"}],"name":"odcRequest","isCollection":false,"id":"2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5</processParameterId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description>&lt;div&gt;&lt;br /&gt;&lt;/div&gt;</description>
            <guid>f5c43f37-1754-4d6d-adb1-fcbec40c42b4</guid>
            <versionId>1954078e-6b8a-4092-9bfc-8a277fa6603a</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de</processParameterId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f5efe421-81dc-4e3e-ab93-b6badfbcd64e</guid>
            <versionId>bc69dacd-3313-486d-81ac-9b96cb5bfc35</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.247d12a8-cf76-495c-8cd4-700f32286023</processParameterId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>12d4e3bc-5095-41e3-8cdc-1bf4b48b7aab</guid>
            <versionId>65239ed9-6a92-4f0a-b639-7fd864a926a6</versionId>
        </processParameter>
        <processParameter name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6</processParameterId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6dad8549-a860-4746-989f-b6b151c64ec4</guid>
            <versionId>dc799e82-8536-454e-9b93-f7d69a520c47</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb</processItemId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <name>Init</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.95a70005-c473-454e-9824-b4e7e1d4f5d8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604</errorHandlerItemId>
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-493c</guid>
            <versionId>0be483a3-d9cb-4f81-81da-66bad74eb3bd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.ef2503e9-5815-4033-81d3-8163af620ee0</processItemPrePostId>
                <processItemId>2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>344825a3-f211-4142-babe-cd237e2e8f0f</guid>
                <versionId>ec63844e-8c55-4130-9f78-c7a3d8ee0f86</versionId>
            </processPrePosts>
            <layoutData x="270" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac6fe7417:-4328</errorHandlerItem>
                <errorHandlerItemId>2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.95a70005-c473-454e-9824-b4e7e1d4f5d8</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (!tw.local.odcRequest)&#xD;
	tw.local.odcRequest = new tw.object.ODCRequest();&#xD;
tw.local.odcRequest.actions = new tw.object.listOf.String();&#xD;
if (!tw.local.odcRequest.appInfo) &#xD;
	tw.local.odcRequest.appInfo = new tw.object.AppInfo();&#xD;
tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();&#xD;
tw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties= new tw.object.ECMproperties();&#xD;
tw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();&#xD;
tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new tw.object.Date();&#xD;
//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "";&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new tw.object.Date();&#xD;
//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 0.0;&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].waiver=false;&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();&#xD;
&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = "";&#xD;
&#xD;
&#xD;
tw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.ContractCreation = new tw.object.ContractCreation();&#xD;
tw.local.odcRequest.ContractCreation.baseDate = new Date();&#xD;
tw.local.odcRequest.ContractCreation.valueDate = new tw.object.Date();&#xD;
tw.local.odcRequest.ContractCreation.sourceReference ="";&#xD;
tw.local.odcRequest.ContractLiquidation = new tw.object.ContractLiquidation();&#xD;
tw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();&#xD;
tw.local.odcRequest.documentSource = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency={};&#xD;
&#xD;
tw.local.odcRequest.FcCollections = new tw.object.FCCollections();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
&#xD;
tw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date =new tw.object.Date();&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
//tw.local.odcRequest.folderID= new tw.object.ECM;&#xD;
tw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.ECMFolder();&#xD;
tw.local.odcRequest.History = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.ImporterDetails = new tw.object.ImporterDetails();&#xD;
&#xD;
tw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();&#xD;
tw.local.odcRequest.Parties = new tw.object.odcParties();&#xD;
tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();&#xD;
tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();&#xD;
//tw.local.odcRequest.Parties.party = new tw.object.listOf.partyTypes();&#xD;
tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();&#xD;
tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
&#xD;
tw.local.odcRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();&#xD;
tw.local.odcRequest.requestDate = new tw.object.Date();&#xD;
tw.local.odcRequest.requestNature = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.requestNature.name = "";&#xD;
tw.local.odcRequest.requestNature.value = "";&#xD;
tw.local.odcRequest.requestType = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.requestType.name = "";&#xD;
tw.local.odcRequest.requestType.value = "";&#xD;
tw.local.odcRequest.ReversalReason = new tw.object.ReversalReason();&#xD;
tw.local.odcRequest.stepLog = new tw.object.StepLog();&#xD;
&#xD;
tw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>9b21ca21-1f9d-4bc5-88f2-785f583c2156</guid>
                <versionId>0e27f33b-d462-43fa-817b-dee4d5ab58d6</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604</processItemId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <name>Exp Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac6fe7417:-4328</guid>
            <versionId>3b2cb8db-5403-4037-8ab2-038f6710e897</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="392" y="140">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>0d4ae84c-ffba-4597-ad99-204f82fdb107</guid>
                <versionId>f6aa0249-ffd5-4a41-ad2d-8b65dd214dc0</versionId>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4dc7f40e-c8a3-450f-b7e2-e939c187fca3</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC BO Initialization"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4f00d360-e752-4838-a4fc-ff3b73f68759</guid>
                    <versionId>86cf9931-8fcd-43d4-8b92-32ed62413276</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c824fc10-0703-43e0-92a6-f7036d9a793d</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>fbb2a34f-e8b7-4ac6-98be-f91a38f5786a</guid>
                    <versionId>91559e7b-d410-40b1-9855-71fb06db83c6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.685300c7-e659-4b0c-956f-eaad293febce</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.78f5c1d8-8475-4cf3-9218-f3f2e9a139c5</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8cecbdf1-b7ab-480d-8997-a867009cdb12</guid>
                    <versionId>dd71fdef-e0bf-4c60-a212-8d84598ebde5</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f</processItemId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.0846ef85-b728-4fa5-8c98-6264d5f93857</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940</guid>
            <versionId>d0b0b8ee-d1c9-4342-aec1-cd29b1385706</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.0846ef85-b728-4fa5-8c98-6264d5f93857</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>1857fbba-1eaa-4a82-bce3-31e7937b2feb</guid>
                <versionId>43fe6059-5499-4e01-aa96-2466cc3dd088</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="ODC BO Initialization" id="1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5">
                            
                            
                            <ns16:documentation textFormat="text/plain">&lt;div&gt;&lt;br /&gt;&lt;/div&gt;</ns16:documentation>
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de" />
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.247d12a8-cf76-495c-8cd4-700f32286023" />
                        
                        
                        <ns16:dataOutput name="isSuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.247d12a8-cf76-495c-8cd4-700f32286023</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c979ec57-8e23-4209-8bba-3b9939ee5878">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="59ded5d7-dda4-437b-8ebb-16dc4cd8de36" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>7d625bde-de84-4fde-8a9a-65f20a475b1c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ce49342f-4b17-433b-8e8d-9fb7f05403cb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f6b6f3ee-04e7-4475-81d5-5ecdbf209604</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>77057b21-26bc-4e8a-889f-e9724bcea10b</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="7d625bde-de84-4fde-8a9a-65f20a475b1c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.4df43bda-00dd-43c7-819d-1c19f07a5762</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:c400b33e5f41dfd9:-18f49999:18a091cbccc:-4940</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5cfabfe4-3dc6-4722-8876-88d28df60370</ns16:incoming>
                        
                        
                        <ns16:incoming>56a02411-9a5c-4fdc-8a44-e84dbe8ea39d</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="7d625bde-de84-4fde-8a9a-65f20a475b1c" targetRef="ce49342f-4b17-433b-8e8d-9fb7f05403cb" name="To Init" id="2027.4df43bda-00dd-43c7-819d-1c19f07a5762">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Init" id="ce49342f-4b17-433b-8e8d-9fb7f05403cb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="270" y="57" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.4df43bda-00dd-43c7-819d-1c19f07a5762</ns16:incoming>
                        
                        
                        <ns16:outgoing>5cfabfe4-3dc6-4722-8876-88d28df60370</ns16:outgoing>
                        
                        
                        <ns16:script>if (!tw.local.odcRequest)&#xD;
	tw.local.odcRequest = new tw.object.ODCRequest();&#xD;
tw.local.odcRequest.actions = new tw.object.listOf.String();&#xD;
if (!tw.local.odcRequest.appInfo) &#xD;
	tw.local.odcRequest.appInfo = new tw.object.AppInfo();&#xD;
tw.local.odcRequest.attachmentDetails = new tw.object.attachmentDetails();&#xD;
tw.local.odcRequest.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
tw.local.odcRequest.attachmentDetails.ecmProperties= new tw.object.ECMproperties();&#xD;
tw.local.odcRequest.BasicDetails = new tw.object.BasicDetails();&#xD;
tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new tw.object.Date();&#xD;
//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "";&#xD;
tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new tw.object.Date();&#xD;
//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 0.0;&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].waiver=false;&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();&#xD;
&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = "";&#xD;
tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = "";&#xD;
&#xD;
&#xD;
tw.local.odcRequest.complianceComments = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.ContractCreation = new tw.object.ContractCreation();&#xD;
tw.local.odcRequest.ContractCreation.baseDate = new Date();&#xD;
tw.local.odcRequest.ContractCreation.valueDate = new tw.object.Date();&#xD;
tw.local.odcRequest.ContractCreation.sourceReference ="";&#xD;
tw.local.odcRequest.ContractLiquidation = new tw.object.ContractLiquidation();&#xD;
tw.local.odcRequest.CustomerInfo = new tw.object.CustomerInfo();&#xD;
tw.local.odcRequest.documentSource = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.odcRequest.FinancialDetailsBR.currency={};&#xD;
&#xD;
tw.local.odcRequest.FcCollections = new tw.object.FCCollections();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
&#xD;
tw.local.odcRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date =new tw.object.Date();&#xD;
tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
//tw.local.odcRequest.folderID= new tw.object.ECM;&#xD;
tw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.ECMFolder();&#xD;
tw.local.odcRequest.History = new tw.object.listOf.StepLog();&#xD;
tw.local.odcRequest.ImporterDetails = new tw.object.ImporterDetails();&#xD;
&#xD;
tw.local.odcRequest.OdcCollection = new tw.object.ODCCollection();&#xD;
tw.local.odcRequest.Parties = new tw.object.odcParties();&#xD;
tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();&#xD;
tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();&#xD;
//tw.local.odcRequest.Parties.party = new tw.object.listOf.partyTypes();&#xD;
tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();&#xD;
tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
&#xD;
tw.local.odcRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();&#xD;
tw.local.odcRequest.requestDate = new tw.object.Date();&#xD;
tw.local.odcRequest.requestNature = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.requestNature.name = "";&#xD;
tw.local.odcRequest.requestNature.value = "";&#xD;
tw.local.odcRequest.requestType = new tw.object.NameValuePair();&#xD;
tw.local.odcRequest.requestType.name = "";&#xD;
tw.local.odcRequest.requestType.value = "";&#xD;
tw.local.odcRequest.ReversalReason = new tw.object.ReversalReason();&#xD;
tw.local.odcRequest.stepLog = new tw.object.StepLog();&#xD;
&#xD;
tw.local.odcRequest.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = false;&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ce49342f-4b17-433b-8e8d-9fb7f05403cb" targetRef="65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f" name="To End" id="5cfabfe4-3dc6-4722-8876-88d28df60370">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exp Handling" id="f6b6f3ee-04e7-4475-81d5-5ecdbf209604">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="392" y="140" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ced22e79-23d6-4e88-8bd7-6e2da82e20e0</ns16:incoming>
                        
                        
                        <ns16:outgoing>56a02411-9a5c-4fdc-8a44-e84dbe8ea39d</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC BO Initialization"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="f6b6f3ee-04e7-4475-81d5-5ecdbf209604" targetRef="65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f" name="To End" id="56a02411-9a5c-4fdc-8a44-e84dbe8ea39d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ce49342f-4b17-433b-8e8d-9fb7f05403cb" parallelMultiple="false" name="Error" id="77057b21-26bc-4e8a-889f-e9724bcea10b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="305" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ced22e79-23d6-4e88-8bd7-6e2da82e20e0</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="c52428cc-4b91-4b8a-8739-2af36e1115a5" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="0dc2de68-b5db-4fd5-8665-729cd1e05d24" eventImplId="4109dacd-dce3-4098-835c-68dd14f7e876">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="77057b21-26bc-4e8a-889f-e9724bcea10b" targetRef="f6b6f3ee-04e7-4475-81d5-5ecdbf209604" name="To Exp Handling" id="ced22e79-23d6-4e88-8bd7-6e2da82e20e0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.56a02411-9a5c-4fdc-8a44-e84dbe8ea39d</processLinkId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f</toProcessItemId>
            <guid>e27b1c4f-90d5-4533-8daf-fce8dbdd673c</guid>
            <versionId>6bc1982f-6680-4eb3-b1cc-2252b8a72eba</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f6b6f3ee-04e7-4475-81d5-5ecdbf209604</fromProcessItemId>
            <toProcessItemId>2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5cfabfe4-3dc6-4722-8876-88d28df60370</processLinkId>
            <processId>1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f</toProcessItemId>
            <guid>76f1373a-00ce-41ba-b129-78077d0cf6e1</guid>
            <versionId>f01b2ca5-88a2-4d6e-bda2-f6675187ad56</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ce49342f-4b17-433b-8e8d-9fb7f05403cb</fromProcessItemId>
            <toProcessItemId>2025.65f88e6a-3f3a-44a1-8e3f-a6a9ae8a7b3f</toProcessItemId>
        </link>
    </process>
</teamworks>

