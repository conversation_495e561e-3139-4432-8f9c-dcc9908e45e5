<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc" name="getEPV">
        <lastModified>1722522871531</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.39a85833-64ab-4a64-8d61-22808a0e1be7</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:d227ae9e677c32d5:-661aced:190e953c57b:4766</guid>
        <versionId>2c9659be-2ace-42c5-a494-3e8842519db1</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:d227ae9e677c32d5:-661aced:190e953c57b:48b4" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e7494c7a-e72d-40c2-802a-f9604a2b563a"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"4a9cefa4-f1c6-4789-85cf-b5ddda16d6fc"},{"incoming":["c9fb5cbd-5bd8-4830-8bdb-aef3d2cea8c5"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:d227ae9e677c32d5:-661aced:190e953c57b:4768"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"91ea363c-d223-4133-8b16-c7f16169a210"},{"targetRef":"39a85833-64ab-4a64-8d61-22808a0e1be7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"2027.e7494c7a-e72d-40c2-802a-f9604a2b563a","sourceRef":"4a9cefa4-f1c6-4789-85cf-b5ddda16d6fc"},{"startQuantity":1,"outgoing":["c9fb5cbd-5bd8-4830-8bdb-aef3d2cea8c5"],"incoming":["2027.e7494c7a-e72d-40c2-802a-f9604a2b563a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":180,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"39a85833-64ab-4a64-8d61-22808a0e1be7","scriptFormat":"text\/x-javascript","script":{"content":["var epv = tw.epv[tw.local.data];\r\nvar epvMap = getEPVChildrenAsString(epv);\r\n\/\/tw.local.epvMap = tw.object.Map();\r\ntw.local.epvMap = epvMap;\r\n\r\n\/\/ Function to retrieve EPV children as a concatenated string\r\nfunction getEPVChildrenAsString(epv) {\r\n\tvar resultString = \"\";\r\n\r\n\t\/\/ Check if epv is an object\r\n\tif (epv) {\r\n\t\t\/\/ Use Object.keys() to get all keys of the EPV object\r\n\t\tvar keys = Object.keys(epv);\r\n\r\n\t\tkeys.forEach(function (childKey) {\r\n\t\t\tvar childValue = epv[childKey];\r\n\t\t\t\r\n\t\t\t\t\/\/ Ensure it's a string value\r\n\t\t\t\tresultString += childKey + \": \" + childValue + \"; \";\r\n\t\t\t\r\n\t\t});\r\n\t} else {\r\n\t\tthrow new Error(\"EPV is not an object or does not have child properties.\");\r\n\t}\r\n\t\/\/ Trim the trailing semicolon and space if needed\r\n      return resultString;\r\n}"]}},{"targetRef":"91ea363c-d223-4133-8b16-c7f16169a210","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"c9fb5cbd-5bd8-4830-8bdb-aef3d2cea8c5","sourceRef":"39a85833-64ab-4a64-8d61-22808a0e1be7"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"epvMap","isCollection":false,"declaredType":"dataObject","id":"2056.689a2bfd-6dd6-470e-8732-fde4dbd02282"}],"laneSet":[{"id":"d4f251a9-8859-474e-8c23-6021d7199826","lane":[{"flowNodeRef":["4a9cefa4-f1c6-4789-85cf-b5ddda16d6fc","91ea363c-d223-4133-8b16-c7f16169a210","39a85833-64ab-4a64-8d61-22808a0e1be7"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"cc88e8af-5c69-4c8f-8741-62ef7f93c389","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"getEPV","declaredType":"process","id":"1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.dccd662d-157f-4226-8f3f-a32d8bb13783"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.86cb7aa4-ed28-4dc0-8b48-f8a1ad7bfaf6"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"929f1a37-19f8-4ac1-8020-cff680cab9d2","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"5b55457e-7da5-4a99-8203-a2e954fe1ef6","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"RequestType\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.f221670b-cf7e-4891-808a-1e5bdaf0ec0a"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f221670b-cf7e-4891-808a-1e5bdaf0ec0a</processParameterId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"RequestType"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ab3a0a9a-eac0-474a-b802-0094b19742b7</guid>
            <versionId>2e7bafa6-d36f-4bca-be80-8e80148edc0b</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dccd662d-157f-4226-8f3f-a32d8bb13783</processParameterId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>5254a74f-a117-4071-a182-fc26f6413516</guid>
            <versionId>7675f6cc-89a8-4d1d-8fa2-21c3c34932cb</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.86cb7aa4-ed28-4dc0-8b48-f8a1ad7bfaf6</processParameterId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8e2b2fa4-657f-416b-9cda-d401263be176</guid>
            <versionId>0e2cc9f1-4ede-45aa-ba35-464c796101fc</versionId>
        </processParameter>
        <processVariable name="epvMap">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.689a2bfd-6dd6-470e-8732-fde4dbd02282</processVariableId>
            <description isNull="true" />
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <guid>318af5d1-82f6-4f15-93c2-a81983da420d</guid>
            <versionId>35dfa174-e91b-4ef3-92af-375793e446c1</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.39a85833-64ab-4a64-8d61-22808a0e1be7</processItemId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.76b7b216-993f-4b9e-81e3-eae0bd100e4f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d227ae9e677c32d5:-661aced:190e953c57b:4776</guid>
            <versionId>a9a64039-90ed-4ee9-9be2-898787082f27</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="180" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.76b7b216-993f-4b9e-81e3-eae0bd100e4f</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var epv = tw.epv[tw.local.data];&#xD;
var epvMap = getEPVChildrenAsString(epv);&#xD;
//tw.local.epvMap = tw.object.Map();&#xD;
tw.local.epvMap = epvMap;&#xD;
&#xD;
// Function to retrieve EPV children as a concatenated string&#xD;
function getEPVChildrenAsString(epv) {&#xD;
	var resultString = "";&#xD;
&#xD;
	// Check if epv is an object&#xD;
	if (epv) {&#xD;
		// Use Object.keys() to get all keys of the EPV object&#xD;
		var keys = Object.keys(epv);&#xD;
&#xD;
		keys.forEach(function (childKey) {&#xD;
			var childValue = epv[childKey];&#xD;
			&#xD;
				// Ensure it's a string value&#xD;
				resultString += childKey + ": " + childValue + "; ";&#xD;
			&#xD;
		});&#xD;
	} else {&#xD;
		throw new Error("EPV is not an object or does not have child properties.");&#xD;
	}&#xD;
	// Trim the trailing semicolon and space if needed&#xD;
      return resultString;&#xD;
}</script>
                <isRule>false</isRule>
                <guid>220174e8-85d7-4b05-a908-1926f0de36e0</guid>
                <versionId>073c7690-b404-406f-b40f-11d7c3011902</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.91ea363c-d223-4133-8b16-c7f16169a210</processItemId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.f1a37e5f-56e4-4022-b57e-45cc4f00bfca</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d227ae9e677c32d5:-661aced:190e953c57b:4768</guid>
            <versionId>d53e525b-a50e-4c0b-92b8-54f7ee11cb7b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.f1a37e5f-56e4-4022-b57e-45cc4f00bfca</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>0a066a52-d603-4718-a76e-5d449baf0993</guid>
                <versionId>31045f53-00a3-44b2-b5f2-5b6eb321c3c1</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.1417ab6d-7bcc-4f92-9f46-423d20c0c695</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <guid>f3fe7388-c015-4e74-b8b3-2a71885764c6</guid>
            <versionId>885c32c5-c0e9-4988-9027-1d9af6a931e1</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.b67c017a-422f-497f-9162-b65921e3d6e9</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <guid>732ee341-fc05-46b2-b779-23eff0177d4c</guid>
            <versionId>e8135429-6932-464e-a90f-222f34ce5603</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.39a85833-64ab-4a64-8d61-22808a0e1be7</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="getEPV" id="1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:isSecured>true</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="929f1a37-19f8-4ac1-8020-cff680cab9d2" />
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="5b55457e-7da5-4a99-8203-a2e954fe1ef6" />
                            </ns3:epvProcessLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.f221670b-cf7e-4891-808a-1e5bdaf0ec0a">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">"RequestType"</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.dccd662d-157f-4226-8f3f-a32d8bb13783" />
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.86cb7aa4-ed28-4dc0-8b48-f8a1ad7bfaf6" />
                        <ns16:inputSet />
                        <ns16:outputSet />
                    </ns16:ioSpecification>
                    <ns16:laneSet id="d4f251a9-8859-474e-8c23-6021d7199826">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="cc88e8af-5c69-4c8f-8741-62ef7f93c389" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>4a9cefa4-f1c6-4789-85cf-b5ddda16d6fc</ns16:flowNodeRef>
                            <ns16:flowNodeRef>91ea363c-d223-4133-8b16-c7f16169a210</ns16:flowNodeRef>
                            <ns16:flowNodeRef>39a85833-64ab-4a64-8d61-22808a0e1be7</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="4a9cefa4-f1c6-4789-85cf-b5ddda16d6fc">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.e7494c7a-e72d-40c2-802a-f9604a2b563a</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:endEvent name="End" id="91ea363c-d223-4133-8b16-c7f16169a210">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            <ns3:endStateId>guid:d227ae9e677c32d5:-661aced:190e953c57b:4768</ns3:endStateId>
                        </ns16:extensionElements>
                        <ns16:incoming>c9fb5cbd-5bd8-4830-8bdb-aef3d2cea8c5</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="4a9cefa4-f1c6-4789-85cf-b5ddda16d6fc" targetRef="39a85833-64ab-4a64-8d61-22808a0e1be7" name="To Script Task" id="2027.e7494c7a-e72d-40c2-802a-f9604a2b563a">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="39a85833-64ab-4a64-8d61-22808a0e1be7">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="180" y="57" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.e7494c7a-e72d-40c2-802a-f9604a2b563a</ns16:incoming>
                        <ns16:outgoing>c9fb5cbd-5bd8-4830-8bdb-aef3d2cea8c5</ns16:outgoing>
                        <ns16:script>var epv = tw.epv[tw.local.data];&#xD;
var epvMap = getEPVChildrenAsString(epv);&#xD;
//tw.local.epvMap = tw.object.Map();&#xD;
tw.local.epvMap = epvMap;&#xD;
&#xD;
// Function to retrieve EPV children as a concatenated string&#xD;
function getEPVChildrenAsString(epv) {&#xD;
	var resultString = "";&#xD;
&#xD;
	// Check if epv is an object&#xD;
	if (epv) {&#xD;
		// Use Object.keys() to get all keys of the EPV object&#xD;
		var keys = Object.keys(epv);&#xD;
&#xD;
		keys.forEach(function (childKey) {&#xD;
			var childValue = epv[childKey];&#xD;
			&#xD;
				// Ensure it's a string value&#xD;
				resultString += childKey + ": " + childValue + "; ";&#xD;
			&#xD;
		});&#xD;
	} else {&#xD;
		throw new Error("EPV is not an object or does not have child properties.");&#xD;
	}&#xD;
	// Trim the trailing semicolon and space if needed&#xD;
      return resultString;&#xD;
}</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="39a85833-64ab-4a64-8d61-22808a0e1be7" targetRef="91ea363c-d223-4133-8b16-c7f16169a210" name="To End" id="c9fb5cbd-5bd8-4830-8bdb-aef3d2cea8c5">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="epvMap" id="2056.689a2bfd-6dd6-470e-8732-fde4dbd02282">
                        <ns16:extensionElements>
                            <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                        </ns16:extensionElements>
                    </ns16:dataObject>
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c9fb5cbd-5bd8-4830-8bdb-aef3d2cea8c5</processLinkId>
            <processId>1.a66a1436-7eaf-4955-a2d9-de93b65b9dcc</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.39a85833-64ab-4a64-8d61-22808a0e1be7</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.91ea363c-d223-4133-8b16-c7f16169a210</toProcessItemId>
            <guid>ab1e20da-3691-455d-84ed-932b5afe4cc0</guid>
            <versionId>3c6ecbea-4867-415e-9fba-0f8c120da64e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.39a85833-64ab-4a64-8d61-22808a0e1be7</fromProcessItemId>
            <toProcessItemId>2025.91ea363c-d223-4133-8b16-c7f16169a210</toProcessItemId>
        </link>
    </process>
</teamworks>

