{"id": "64.1d99aba8-195f-4eee-ba8c-a47926bc21e2", "versionId": "1a1713a3-de37-49e2-bdcb-d84680269095", "name": "Document Generation CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "documentGeneration", "configOptions": ["requestTypeVIS", "regeneratedRemittanceLetterTitleVIS", "deliveryTerms", "paymentTerms", "specialInstructions", "instructions", "requestType", "remittanceLetter<PERSON><PERSON>on", "barcode", "generationStatus", "documentGenerationVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//this.EngLetterRegExp = function(char)\r\r\n//{\r\r\n//console.log(\"inside reg exp fn\");\r\r\n//console.log(char +\"chaaracterrr\");\r\r\n//\t//if(this.context.options.requestType.get(\"value\") == \"create\" ||this.context.options.requestType.get(\"value\") == \"amend\" ||this.context.options.requestType.get(\"value\") == \"recreate\")\r\r\n//\t\r\r\n//\t\tvar regex = \"/^[A-Za-z][A-Za-z0-9]*$/\";\r\r\n////\t\tchar.match(regex);\r\r\n//\t\tif( regex.test(char) == true)\r\r\n//\t\t{\r\r\n//\t\t\tthis.ui.get(\"customerName\").setValid(true);\r\r\n//\t\t\tconsole.log(\"match\");\r\r\n//\t\t}\t\r\r\n//\t\telse\r\r\n//\t\t{\r\r\n//\t\t\tthis.ui.get(\"customerName\").setValid(false , \"This field accepts alpanumeric only\");\r\r\n//\t\t\tconsole.log(\"doesnt match\");\r\r\n//\t\t}\t\t\t\r\r\n//}\r\r\nthis.ShowRegeneratedRemittanceLetter = function()\r\r\n{\r\r\n\t//if(this.context.binding.get(\"value\").get(\"regenerateRemLetter\") == true\r\r\n\tif(this.context.binding.get(\"value\").get(\"regenerateRemLetter\") == true && this.context.binding.get(\"value\").get(\"regenerateRemLetterOption\").get(\"value\") == \"other\")\r\r\n\t{\t\r\r\n\t\tthis.context.options.regeneratedRemittanceLetterTitleVIS.set(\"value\", \"EDITABLE\");\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\tthis.context.options.regeneratedRemittanceLetterTitleVIS.set(\"value\", \"NONE\");\r\r\n\t}\r\r\n}\r\r\n////////////////////////////////////"}]}, "hasDetails": true}