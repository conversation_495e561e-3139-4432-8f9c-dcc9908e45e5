<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964" name="ODC Closure اقفال تحصيل مستندى تصدير">
        <bpdParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.b01cffc3-d443-456f-83aa-ccefe78d1e40</bpdParameterId>
            <bpdId>25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>0</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isReadOnly>false</isReadOnly>
            <guid>1743b20c-7243-4dd2-9d40-1e7f05b07ae8</guid>
            <versionId>4ec19ddc-50d4-4e51-8b6a-3aef83affba8</versionId>
        </bpdParameter>
        <bpdParameter name="routingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.9e5082af-a9b5-42f8-813c-dd8d7d001e4b</bpdParameterId>
            <bpdId>25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
            <seq>1</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isReadOnly>false</isReadOnly>
            <guid>77cee649-1486-4cb2-af41-96dec61908a6</guid>
            <versionId>1725a2f0-7108-4552-83aa-46da9228e602</versionId>
        </bpdParameter>
        <bpdParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.e45e587b-7a69-43ca-87bf-4c739f50a885</bpdParameterId>
            <bpdId>25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964</bpdId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>0</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isReadOnly>false</isReadOnly>
            <guid>c241e4a7-6f61-4529-8ab7-e60ae19b5ad9</guid>
            <versionId>6803000a-1a0b-4eca-b4ca-b69a8d492f40</versionId>
        </bpdParameter>
        <lastModified>1700658771634</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <bpdId>25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</perfMetricParticipantRef>
        <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ownerTeamParticipantRef>
        <timeScheduleType isNull="true" />
        <timeScheduleName>NBEWork</timeScheduleName>
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName>NBEHoliday</holidayScheduleName>
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone>Africa/Cairo</timezone>
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="1dd604e3-a9a7-4a75-afac-32350fbf6687" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="ODC Closure اقفال تحصيل مستندى تصدير" id="25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation /&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot; Request Type: &amp;quot;+ tw.local.odcRequest.requestType.name  +&amp;quot; , &amp;quot;+&amp;quot; CIF: &amp;quot;+ tw.local.odcRequest.cif +&amp;quot; , &amp;quot; +&amp;quot;Request Number: &amp;quot; +tw.system.process.instanceId" dueDateEnabled="true" atRiskCalcEnabled="false" enableTracking="false" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="false" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at1693724550026"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:timezone&gt;Africa/Cairo&lt;/ns4:timezone&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="02c63cda-04f4-40f8-877a-01ecea9469b3" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:extensionElements&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="3f6851e8-edd7-40cb-819e-cae6ed69c342" /&gt;&lt;ns3:epvProcessLinkRef epvId="21.dbbaa047-8f02-4397-b1b5-41f11b0256b3" epvProcessLinkId="5002cae0-be64-4941-8e83-9c01aff49ffe" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2007.b01cffc3-d443-456f-83aa-ccefe78d1e40"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:searchableField id="9cf59869-19ec-4116-8f5c-d974f4049ba5" alias="cif" path=".cif" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="e6ae263b-b95e-4e92-83b1-3658ce343e09" alias="customerName" path=".customerName" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="ea50ce4d-3a9d-4aff-8094-b7801792467b" alias="parentRequestNo" path=".parentRequestNo" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="eb6dfa5f-4fda-4b9c-83cc-5b4e4395c01f" alias="InitiatorUserName" path=".initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="1806bb4f-20cb-4115-80f9-2bf0e836b210" alias="requestDate" path=".appInfo.requestDate" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="655eb844-98fb-41e4-854f-0611a316c79d" alias="requeststatus" path=".appInfo.status" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="449d6dd1-6621-4542-84f6-f6ac56f38088" alias="InitiatorBranchHub" path=".appInfo.initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataInput&gt;&lt;ns15:dataInput name="routingDetails" itemSubjectRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727" isCollection="false" id="2007.9e5082af-a9b5-42f8-813c-dd8d7d001e4b" /&gt;&lt;ns15:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2007.e45e587b-7a69-43ca-87bf-4c739f50a885"&gt;&lt;ns15:extensionElements /&gt;&lt;/ns15:dataOutput&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="_1d37f7c2-ec9c-45c5-95f6-fd2214859053" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="_85968968-8bbb-4deb-8075-dba72353d31b" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="abe9ba35-6110-4d4b-a253-3faed7c8efae"&gt;&lt;ns15:lane name="Branch / Hub Maker" partitionElementRef="24.e7977935-dba2-437e-9671-4ec41d29e437" id="800d3d50-7f3e-4836-90c8-7199a2a30bb8" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;198ea770-2e81-49a9-8c6c-0e341b5f2c32&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;68e55774-7619-4db3-8548-1d17e2918203&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;c42f5667-bb85-4d5f-8748-15716544be33&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Branch Hub Compliance Rep" partitionElementRef="24.b1b8b540-604c-41d9-8fd7-6253273000f8" id="472e41d2-c4a6-4b03-85f4-b22cbb093dd4" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;6bf177a9-2ccf-48dc-8575-5c4463d30254&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;bbb343ab-cad3-42e8-8cdf-927be59b54c7&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;96ed6632-0e83-41a4-8ad8-96e57d448f32&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;25d557e0-9c3e-4a33-8872-e5e32b064bfe&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Trade Front Office" partitionElementRef="24.e92aee43-caab-4c22-88e1-3f7edf14b1ba" id="258baacc-eed6-4c0f-8a64-ab22b7f72406" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;fb9fca06-f077-4411-861c-9e7b74adf765&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;318eaa03-4c5d-4df7-8381-c901dd2eb63b&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Trade Front Office Checker" partitionElementRef="24.b336dee6-981f-436d-aff5-ad4053a7a3fe" id="bdab0fad-28c7-4a91-8c7e-f4d29021fa89" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="603" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;7701697d-2665-4cc9-8960-93b07a3367aa&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;c58e107b-da02-48b8-8a6c-708b76fa25ed&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;5df6c921-78dc-44c4-8085-bdebe578009a&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="back to exe hub maker" partitionElementRef="24.3ff202c1-5f0d-4c91-acc4-e0dbd69cf4e0" id="b4b7185c-2c29-4055-81ec-895287c51334" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="804" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;925db450-d9d8-4d13-83cc-f0508b787c62&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;26070753-3a9b-471f-80aa-d538dee7d0c9&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Execution Hub Checker" partitionElementRef="24.8e005024-3fe0-4848-8c3c-f1e9483900c6" id="ef55820f-7b10-4fcf-8bb8-659fc7de19ce" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="1005" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;cab9b5ba-5485-402b-8bb5-862badcf84e5&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;29265816-0f66-4a98-8df4-fb7790107405&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;ef030c4a-84df-4dec-868a-607a8059d8a4&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;aad24506-f23e-4e16-8a43-8d25597668c2&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="76ad5f2b-581d-4307-b43f-95388f8aa989" ns4:isSystemLane="true"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="1206" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;975c2a34-b327-4628-89c0-703bc40eac9d&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;40bc9c49-3420-4b68-82fe-da8c33a8ce6a&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="198ea770-2e81-49a9-8c6c-0e341b5f2c32"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="62" y="63" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:default&gt;54d888c8-a1d8-40c3-a319-f36b82cdb852&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;true&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;54d888c8-a1d8-40c3-a319-f36b82cdb852&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:sequenceFlow sourceRef="198ea770-2e81-49a9-8c6c-0e341b5f2c32" targetRef="68e55774-7619-4db3-8548-1d17e2918203" name="To Inline User Task" id="54d888c8-a1d8-40c3-a319-f36b82cdb852"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.2af38b7e-9461-4887-9ad2-24d4565ee49b" default="b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13" name="ACT01 Create ODC Closure Request" id="68e55774-7619-4db3-8548-1d17e2918203"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="442" y="57" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Create ODC Closure Request – إنشاء طلب اقفال تحصيل مستندى تصدير &amp;lt;#= tw.system.process.instanceId #&amp;gt;&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.closureACT01)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;false&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;6a299dca-fbb1-48c8-8778-fbad8360de7f&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;b37475e2-fcda-4734-8e24-c7032026c625&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;54d888c8-a1d8-40c3-a319-f36b82cdb852&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.365895d0-a5ae-49f2-86d1-de0ac795fbe9&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.6fa94dca-e752-4ace-8264-0d551b9ec5bd&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"&gt;tw.local.routingDetails&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.5b04a5b6-755b-4702-8c66-d9a4455f4598&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.872cc9b3-563d-4875-81d4-07416c1f8697&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.f98fed95-51fc-473c-8003-aeb193a5d3df&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.1498704b-0db6-4050-868a-d35f1d79314c&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.fd93251a-76e2-4304-8758-8af89440122b&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="LastUser" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b849887a-3e1e-4633-be89-6e2adce84383&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.branchCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"Branch_Operation_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.d5717fc2-69b2-4168-a2af-80632cedf962&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_CR_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.0694c1c7-863e-4809-8c83-bafbba3eb2bb" default="4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7" name="ACT02 Review ODC Closure Request" id="6bf177a9-2ccf-48dc-8575-5c4463d30254"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="441" y="62" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review ODC Closure Request by Compliance Rep – مراجعة طلب اقفال تحصيل مستندى تصدير من ممثل الإلتزام &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.closureACT02)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.d4fa9d21-5e1a-4996-8745-6d9b1ba1d005&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.cda6b97c-96b9-488b-8055-47d670e552ac&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"&gt;tw.local.routingDetails&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a3159314-99a0-4745-8a3b-4be34b9b6f7b&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.790399ab-7b67-4adc-8647-410880393efa&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.cd4ee7b8-3ac7-4a16-8a0b-023b2838e7f1&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="LastUser" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b849887a-3e1e-4633-be89-6e2adce84383&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.branchCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"Compliance_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.d5717fc2-69b2-4168-a2af-80632cedf962&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_COMP_CHKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="68e55774-7619-4db3-8548-1d17e2918203" targetRef="6bf177a9-2ccf-48dc-8575-5c4463d30254" name="To ACT02 Review ODC Closure Request" id="b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="63190125-a4ec-4e19-8583-4944b90db2f0" name="ACT02Decision" id="bbb343ab-cad3-42e8-8cdf-927be59b54c7"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="611" y="81" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;6a299dca-fbb1-48c8-8778-fbad8360de7f&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;2774b9c8-37d0-42eb-8408-bf6e7973513b&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;63190125-a4ec-4e19-8583-4944b90db2f0&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="6bf177a9-2ccf-48dc-8575-5c4463d30254" targetRef="bbb343ab-cad3-42e8-8cdf-927be59b54c7" name="To ACT02Decision" id="4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="bbb343ab-cad3-42e8-8cdf-927be59b54c7" targetRef="68e55774-7619-4db3-8548-1d17e2918203" name="Back to initiator" id="6a299dca-fbb1-48c8-8778-fbad8360de7f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="cancel" id="96ed6632-0e83-41a4-8ad8-96e57d448f32"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="753" y="87" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;2774b9c8-37d0-42eb-8408-bf6e7973513b&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="bbb343ab-cad3-42e8-8cdf-927be59b54c7" targetRef="96ed6632-0e83-41a4-8ad8-96e57d448f32" name="Cancel" id="2774b9c8-37d0-42eb-8408-bf6e7973513b"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.cancelRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8" default="e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7" name=" ACT03 Review ODC Closure Request By Trade Fo" id="fb9fca06-f077-4411-861c-9e7b74adf765"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="592" y="56" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review ODC Closure Request by Trade FO &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.closureACT03)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;63190125-a4ec-4e19-8583-4944b90db2f0&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;2e4f57ab-4f8d-456e-87d3-756c485591a0&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;311909c0-a416-4e25-88da-19bb1c47a1e1&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.7b8524b0-bfb2-46dd-854d-9382344f5d1d&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.aa22438f-a40d-4a2b-8574-34965edd357f&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.a695b666-deac-4677-831b-28362ebe5d26&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="bbb343ab-cad3-42e8-8cdf-927be59b54c7" targetRef="fb9fca06-f077-4411-861c-9e7b74adf765" name="approve" id="63190125-a4ec-4e19-8583-4944b90db2f0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.da205dca-ccab-4203-9f89-719cb8957cc5" default="a9a6619c-f2f2-4eee-8e20-f56a0143cc3b" name="ACT04 Review ODC Closure Request By Trade Fo Checker" id="7701697d-2665-4cc9-8960-93b07a3367aa"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="592" y="56" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review ODC Closure Request by Trade FO Checker&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.closureACT04)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;a9a6619c-f2f2-4eee-8e20-f56a0143cc3b&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b22dee7e-b212-48ff-8e2e-95bc9f0371e6&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.7e77ef12-1a3f-42ec-86be-d745ee08a665&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.ddfa07cf-291a-4338-89e0-3e7a37c265ae&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.c20f4aee-748a-4a36-8508-67ebf5f69f81&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="fb9fca06-f077-4411-861c-9e7b74adf765" targetRef="7701697d-2665-4cc9-8960-93b07a3367aa" name="To ACT04 Review ODC Closure Request By Trade Fo Checker" id="e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="b93038e6-0d51-44b7-87a5-8f870bd2e6e2" name="ACT04Decision" id="81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="780" y="79" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;a9a6619c-f2f2-4eee-8e20-f56a0143cc3b&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;2e4f57ab-4f8d-456e-87d3-756c485591a0&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;b37475e2-fcda-4734-8e24-c7032026c625&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;b93038e6-0d51-44b7-87a5-8f870bd2e6e2&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;b3a4187f-1f31-4677-83d5-f99e3c8a0c80&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="7701697d-2665-4cc9-8960-93b07a3367aa" targetRef="81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1" name="To ACT04Decision" id="a9a6619c-f2f2-4eee-8e20-f56a0143cc3b"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1" targetRef="fb9fca06-f077-4411-861c-9e7b74adf765" name="return to maker" id="2e4f57ab-4f8d-456e-87d3-756c485591a0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightBottom&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1" targetRef="68e55774-7619-4db3-8548-1d17e2918203" name="return to init" id="b37475e2-fcda-4734-8e24-c7032026c625"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.93bd8272-8f96-42db-92a3-1bdb463c2c28" default="542ed14c-ab9a-4b28-87ba-463041af5a1b" name="ACT05  ODC Closure Execution" id="925db450-d9d8-4d13-83cc-f0508b787c62"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="1002" y="37" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;ODC Closure Execution &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.closureACT05)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;b93038e6-0d51-44b7-87a5-8f870bd2e6e2&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;8eb214a8-7ac3-48b3-8f2a-79b6fc863d52&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;542ed14c-ab9a-4b28-87ba-463041af5a1b&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;542ed14c-ab9a-4b28-87ba-463041af5a1b&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;27a4a67a-1c71-4cd5-84a9-303e35df221a&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.7e82bcd2-ff2f-4edf-8e16-cb3fc06e5642&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b7b1c9dc-eb0d-4c19-836b-e89ff2fa3215&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.fed13d4d-ae3e-4307-847d-fea93244b330&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.31aa1c84-386b-4b78-8766-571d25ab8d35&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.odcRequest.ReversalReason.executionHub.value+"_EXE_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.1f1dbd1c-004f-40a3-bca6-511fc4094964" default="a923d1e1-dae3-45e7-8f83-4e583006496a" name="ACT06  ODC Closure Execution" id="cab9b5ba-5485-402b-8bb5-862badcf84e5"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="1003" y="40" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;ODC Closure Execution Review&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.closureACT06)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;27a4a67a-1c71-4cd5-84a9-303e35df221a&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;a923d1e1-dae3-45e7-8f83-4e583006496a&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.def8c2e1-d89c-4fdb-8491-8ca568d3dbc9&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a2de4a27-e1c1-4a5b-8df4-451dfa95ffad&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.e8f79546-2c25-4f06-8077-a129808f6df9&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.odcRequest.ReversalReason.executionHub.value+"_EXE_CHKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1" targetRef="925db450-d9d8-4d13-83cc-f0508b787c62" name="approve" id="b93038e6-0d51-44b7-87a5-8f870bd2e6e2"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="Terminate" id="c58e107b-da02-48b8-8a6c-708b76fa25ed"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="947" y="60" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;b3a4187f-1f31-4677-83d5-f99e3c8a0c80&lt;/ns15:incoming&gt;&lt;ns15:terminateEventDefinition id="c0f0316b-c305-4cee-88d8-d6ef9f56f2c8" eventImplId="30d8938e-6e0c-4783-8be6-45ca43dc2b2c"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:terminateEventSettings&gt;&lt;ns4:terminateEntireProcess&gt;false&lt;/ns4:terminateEntireProcess&gt;&lt;ns4:deleteProcessInstance&gt;false&lt;/ns4:deleteProcessInstance&gt;&lt;ns4:deleteCaseFolder&gt;false&lt;/ns4:deleteCaseFolder&gt;&lt;/ns4:terminateEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:terminateEventDefinition&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1" targetRef="c58e107b-da02-48b8-8a6c-708b76fa25ed" name="To Terminate" id="b3a4187f-1f31-4677-83d5-f99e3c8a0c80"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.terminateRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="8eb214a8-7ac3-48b3-8f2a-79b6fc863d52" name="ACT06Decision" id="29265816-0f66-4a98-8df4-fb7790107405"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1187" y="59" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;a923d1e1-dae3-45e7-8f83-4e583006496a&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;fec2cdcd-40b9-4f20-8741-4992adf46534&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;8eb214a8-7ac3-48b3-8f2a-79b6fc863d52&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;311909c0-a416-4e25-88da-19bb1c47a1e1&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="cab9b5ba-5485-402b-8bb5-862badcf84e5" targetRef="29265816-0f66-4a98-8df4-fb7790107405" name="To ACT06Decision" id="a923d1e1-dae3-45e7-8f83-4e583006496a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="End Event" id="ef030c4a-84df-4dec-868a-607a8059d8a4"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1349" y="66" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;fec2cdcd-40b9-4f20-8741-4992adf46534&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="29265816-0f66-4a98-8df4-fb7790107405" targetRef="ef030c4a-84df-4dec-868a-607a8059d8a4" name="End" id="fec2cdcd-40b9-4f20-8741-4992adf46534"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.approveRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="29265816-0f66-4a98-8df4-fb7790107405" targetRef="925db450-d9d8-4d13-83cc-f0508b787c62" name="To ACT05  ODC Closure Execution" id="8eb214a8-7ac3-48b3-8f2a-79b6fc863d52"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="29265816-0f66-4a98-8df4-fb7790107405" targetRef="fb9fca06-f077-4411-861c-9e7b74adf765" name="back to trade fo maker" id="311909c0-a416-4e25-88da-19bb1c47a1e1"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToTradeFo&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isFirstTime" id="8f89d758-510b-46f0-8ad1-31880445669f"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:defaultValue useDefault="true"&gt;true&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataObject&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="lastAction" id="c6f98abb-d7f9-4cdd-851f-bd88fdbcb7fe" /&gt;&lt;ns15:callActivity calledElement="1.d7acf968-6740-4e52-b037-2049466eeeb2" default="6fb1423d-2e91-49d1-8f28-193c1ac41fd6" name="send Escalation Mail" id="975c2a34-b327-4628-89c0-703bc40eac9d"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns13:nodeVisualInfo x="837" y="48" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;687d9cba-9f5d-4614-89fc-d42f433aeb0d&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;e4e19c0e-f75e-42ef-8956-1435e7093032&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;bd80f29f-1547-45d4-8fcc-47ceb057f7d5&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;e4b8fd71-18fb-4cb3-84e1-e071579d0b17&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;dc58487a-1894-411b-8f22-4d570b1ee0f9&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;6fb1423d-2e91-49d1-8f28-193c1ac41fd6&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.9771d7e8-ca59-430e-8b1a-194cf04c1182&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="cab9b5ba-5485-402b-8bb5-862badcf84e5" parallelMultiple="false" name="Boundary Event ACT06" id="aad24506-f23e-4e16-8a43-8d25597668c2"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1038" y="98" width="24" height="24" /&gt;&lt;ns3:default&gt;687d9cba-9f5d-4614-89fc-d42f433aeb0d&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;687d9cba-9f5d-4614-89fc-d42f433aeb0d&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="1b42d7c5-bc94-4470-85d9-e504ede4bce9" eventImplId="d455ac38-0703-467d-8a0c-455253a2e45d"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="aad24506-f23e-4e16-8a43-8d25597668c2" targetRef="975c2a34-b327-4628-89c0-703bc40eac9d" name="To send Escalation Mail" id="687d9cba-9f5d-4614-89fc-d42f433aeb0d"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightBottom&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="taskID" id="9879897e-788a-46a9-8bf5-efb685725d8c" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="ef652e8d-24a4-46d6-8150-33b5b4105303" /&gt;&lt;ns15:endEvent name="End Event1" id="40bc9c49-3420-4b68-82fe-da8c33a8ce6a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="851" y="141" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;6fb1423d-2e91-49d1-8f28-193c1ac41fd6&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="975c2a34-b327-4628-89c0-703bc40eac9d" targetRef="40bc9c49-3420-4b68-82fe-da8c33a8ce6a" name="To End Event1" id="6fb1423d-2e91-49d1-8f28-193c1ac41fd6"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="925db450-d9d8-4d13-83cc-f0508b787c62" parallelMultiple="false" name="Boundary EventACT05" id="26070753-3a9b-471f-80aa-d538dee7d0c9"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="990" y="78" width="24" height="24" /&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:default&gt;5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="207815a6-c105-4c16-8cd3-b75b81e25c18" eventImplId="642b0752-3169-4fa9-8c93-7293934c037d"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="925db450-d9d8-4d13-83cc-f0508b787c62" targetRef="925db450-d9d8-4d13-83cc-f0508b787c62" name="To ACT05  ODC Closure Execution" id="542ed14c-ab9a-4b28-87ba-463041af5a1b"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="7701697d-2665-4cc9-8960-93b07a3367aa" parallelMultiple="false" name="Boundary EventACT04" id="5df6c921-78dc-44c4-8085-bdebe578009a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="627" y="114" width="24" height="24" /&gt;&lt;ns3:default&gt;e4e19c0e-f75e-42ef-8956-1435e7093032&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;e4e19c0e-f75e-42ef-8956-1435e7093032&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="3caa741a-205c-4111-8b15-dd2fda73b8e2" eventImplId="fcfd7f5b-35e8-4826-8a08-8c864758a5f1"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="5df6c921-78dc-44c4-8085-bdebe578009a" targetRef="975c2a34-b327-4628-89c0-703bc40eac9d" name="To send Escalation Mail" id="e4e19c0e-f75e-42ef-8956-1435e7093032"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftBottom&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="fb9fca06-f077-4411-861c-9e7b74adf765" parallelMultiple="false" name="Boundary EventACT03" id="318eaa03-4c5d-4df7-8381-c901dd2eb63b"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="580" y="60" width="24" height="24" /&gt;&lt;ns3:default&gt;bd80f29f-1547-45d4-8fcc-47ceb057f7d5&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;bd80f29f-1547-45d4-8fcc-47ceb057f7d5&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="ac571174-cac7-4576-8307-5a4b81320211" eventImplId="27bfc5cb-b1c5-4c2e-8671-b640861ae503"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="318eaa03-4c5d-4df7-8381-c901dd2eb63b" targetRef="975c2a34-b327-4628-89c0-703bc40eac9d" name="To send Escalation Mail" id="bd80f29f-1547-45d4-8fcc-47ceb057f7d5"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="6bf177a9-2ccf-48dc-8575-5c4463d30254" parallelMultiple="false" name="Boundary EventACT02" id="25d557e0-9c3e-4a33-8872-e5e32b064bfe"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="429" y="85" width="24" height="24" /&gt;&lt;ns3:default&gt;e4b8fd71-18fb-4cb3-84e1-e071579d0b17&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;e4b8fd71-18fb-4cb3-84e1-e071579d0b17&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="6846a41f-f67e-4f8d-81e6-275fbfeaa6f0" eventImplId="7d20dde6-f4bc-4c6c-85ce-9ff8a0211042"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="25d557e0-9c3e-4a33-8872-e5e32b064bfe" targetRef="975c2a34-b327-4628-89c0-703bc40eac9d" name="To send Escalation Mail" id="e4b8fd71-18fb-4cb3-84e1-e071579d0b17"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="68e55774-7619-4db3-8548-1d17e2918203" parallelMultiple="false" name="Boundary EventACT01" id="c42f5667-bb85-4d5f-8748-15716544be33"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="430" y="98" width="24" height="24" /&gt;&lt;ns3:default&gt;dc58487a-1894-411b-8f22-4d570b1ee0f9&lt;/ns3:default&gt;&lt;ns3:preAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:preAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;dc58487a-1894-411b-8f22-4d570b1ee0f9&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="550fba45-aab0-4d3b-8bf3-2668dca6957b" eventImplId="ff3fa9d0-e8c5-4196-87ce-1e60d05cf43e"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="c42f5667-bb85-4d5f-8748-15716544be33" targetRef="975c2a34-b327-4628-89c0-703bc40eac9d" name="To send Escalation Mail" id="dc58487a-1894-411b-8f22-4d570b1ee0f9"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="26070753-3a9b-471f-80aa-d538dee7d0c9" targetRef="975c2a34-b327-4628-89c0-703bc40eac9d" name="To send Escalation Mail" id="5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="925db450-d9d8-4d13-83cc-f0508b787c62" targetRef="cab9b5ba-5485-402b-8bb5-862badcf84e5" name="To ACT06  ODC Closure Execution" id="27a4a67a-1c71-4cd5-84a9-303e35df221a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentPath" id="ebd90392-b6bc-46c9-8909-0008c56b9255" /&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.48d9e8e1-2b51-4173-ac71-9a6c533d134e&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="ODC Closure اقفال تحصيل مستندى تصديرInterface" id="_8ff71a51-0d88-4f43-bcb1-d5c449bd965f" /&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["54d888c8-a1d8-40c3-a319-f36b82cdb852"],"isInterrupting":true,"extensionElements":{"default":["54d888c8-a1d8-40c3-a319-f36b82cdb852"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":62,"y":63,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["true"]}}]},"name":"Start","declaredType":"startEvent","id":"198ea770-2e81-49a9-8c6c-0e341b5f2c32"},{"targetRef":"68e55774-7619-4db3-8548-1d17e2918203","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Inline User Task","declaredType":"sequenceFlow","id":"54d888c8-a1d8-40c3-a319-f36b82cdb852","sourceRef":"198ea770-2e81-49a9-8c6c-0e341b5f2c32"},{"outgoing":["b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13"],"incoming":["6a299dca-fbb1-48c8-8778-fbad8360de7f","b37475e2-fcda-4734-8e24-c7032026c625","54d888c8-a1d8-40c3-a319-f36b82cdb852"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":442,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Create ODC Closure Request \u2013 \u0625\u0646\u0634\u0627\u0621 \u0637\u0644\u0628 \u0627\u0642\u0641\u0627\u0644 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631 &lt;#= tw.system.process.instanceId #&gt;","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.closureACT01)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["false"]}}]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"LastUser","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b849887a-3e1e-4633-be89-6e2adce84383","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.branchCode"]}}]},{"targetRef":"2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Branch_Operation_MKR\""]}}]},{"targetRef":"2055.d5717fc2-69b2-4168-a2af-80632cedf962","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_CR_MKR\""]}}]}],"serviceRef":"1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13","name":"ACT01 Create ODC Closure Request","dataInputAssociation":[{"targetRef":"2055.365895d0-a5ae-49f2-86d1-de0ac795fbe9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.6fa94dca-e752-4ace-8264-0d551b9ec5bd","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","declaredType":"TFormalExpression","content":["tw.local.routingDetails"]}}]},{"targetRef":"2055.5b04a5b6-755b-4702-8c66-d9a4455f4598","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]}}]},{"targetRef":"2055.872cc9b3-563d-4875-81d4-07416c1f8697","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"68e55774-7619-4db3-8548-1d17e2918203","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.f98fed95-51fc-473c-8003-aeb193a5d3df"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}],"sourceRef":["2055.1498704b-0db6-4050-868a-d35f1d79314c"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.fd93251a-76e2-4304-8758-8af89440122b"]}],"calledElement":"1.2af38b7e-9461-4887-9ad2-24d4565ee49b"},{"outgoing":["4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7"],"incoming":["b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":441,"y":62,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review ODC Closure Request by Compliance Rep \u2013 \u0645\u0631\u0627\u062c\u0639\u0629 \u0637\u0644\u0628 \u0627\u0642\u0641\u0627\u0644 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631 \u0645\u0646 \u0645\u0645\u062b\u0644 \u0627\u0644\u0625\u0644\u062a\u0632\u0627\u0645 ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.closureACT02)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"LastUser","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b849887a-3e1e-4633-be89-6e2adce84383","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.branchCode"]}}]},{"targetRef":"2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Compliance_MKR\""]}}]},{"targetRef":"2055.d5717fc2-69b2-4168-a2af-80632cedf962","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_COMP_CHKR\""]}}]}],"serviceRef":"1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7","name":"ACT02 Review ODC Closure Request","dataInputAssociation":[{"targetRef":"2055.d4fa9d21-5e1a-4996-8745-6d9b1ba1d005","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.cda6b97c-96b9-488b-8055-47d670e552ac","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","declaredType":"TFormalExpression","content":["tw.local.routingDetails"]}}]},{"targetRef":"2055.a3159314-99a0-4745-8a3b-4be34b9b6f7b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]},{"targetRef":"2055.790399ab-7b67-4adc-8647-410880393efa","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"6bf177a9-2ccf-48dc-8575-5c4463d30254","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.cd4ee7b8-3ac7-4a16-8a0b-023b2838e7f1"]}],"calledElement":"1.0694c1c7-863e-4809-8c83-bafbba3eb2bb"},{"targetRef":"6bf177a9-2ccf-48dc-8575-5c4463d30254","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT02 Review ODC Closure Request","declaredType":"sequenceFlow","id":"b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13","sourceRef":"68e55774-7619-4db3-8548-1d17e2918203"},{"outgoing":["6a299dca-fbb1-48c8-8778-fbad8360de7f","2774b9c8-37d0-42eb-8408-bf6e7973513b","63190125-a4ec-4e19-8583-4944b90db2f0"],"incoming":["4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7"],"default":"63190125-a4ec-4e19-8583-4944b90db2f0","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":611,"y":81,"declaredType":"TNodeVisualInfo","height":32}]},"name":"ACT02Decision","declaredType":"exclusiveGateway","id":"bbb343ab-cad3-42e8-8cdf-927be59b54c7"},{"targetRef":"bbb343ab-cad3-42e8-8cdf-927be59b54c7","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT02Decision","declaredType":"sequenceFlow","id":"4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7","sourceRef":"6bf177a9-2ccf-48dc-8575-5c4463d30254"},{"targetRef":"68e55774-7619-4db3-8548-1d17e2918203","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToInitiator"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Back to initiator","declaredType":"sequenceFlow","id":"6a299dca-fbb1-48c8-8778-fbad8360de7f","sourceRef":"bbb343ab-cad3-42e8-8cdf-927be59b54c7"},{"incoming":["2774b9c8-37d0-42eb-8408-bf6e7973513b"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":753,"y":87,"declaredType":"TNodeVisualInfo","height":24}]},"name":"cancel","declaredType":"endEvent","id":"96ed6632-0e83-41a4-8ad8-96e57d448f32"},{"targetRef":"96ed6632-0e83-41a4-8ad8-96e57d448f32","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"Cancel","declaredType":"sequenceFlow","id":"2774b9c8-37d0-42eb-8408-bf6e7973513b","sourceRef":"bbb343ab-cad3-42e8-8cdf-927be59b54c7"},{"outgoing":["e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7"],"incoming":["63190125-a4ec-4e19-8583-4944b90db2f0","2e4f57ab-4f8d-456e-87d3-756c485591a0","311909c0-a416-4e25-88da-19bb1c47a1e1"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":592,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review ODC Closure Request by Trade FO ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.closureACT03)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7","name":" ACT03 Review ODC Closure Request By Trade Fo","dataInputAssociation":[{"targetRef":"2055.7b8524b0-bfb2-46dd-854d-9382344f5d1d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"fb9fca06-f077-4411-861c-9e7b74adf765","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.aa22438f-a40d-4a2b-8574-34965edd357f"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}],"sourceRef":["2055.a695b666-deac-4677-831b-28362ebe5d26"]}],"calledElement":"1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8"},{"targetRef":"fb9fca06-f077-4411-861c-9e7b74adf765","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"approve","declaredType":"sequenceFlow","id":"63190125-a4ec-4e19-8583-4944b90db2f0","sourceRef":"bbb343ab-cad3-42e8-8cdf-927be59b54c7"},{"outgoing":["a9a6619c-f2f2-4eee-8e20-f56a0143cc3b"],"incoming":["e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":592,"y":56,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review ODC Closure Request by Trade FO Checker","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.closureACT04)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"a9a6619c-f2f2-4eee-8e20-f56a0143cc3b","name":"ACT04 Review ODC Closure Request By Trade Fo Checker","dataInputAssociation":[{"targetRef":"2055.b22dee7e-b212-48ff-8e2e-95bc9f0371e6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.7e77ef12-1a3f-42ec-86be-d745ee08a665","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]},{"targetRef":"2055.ddfa07cf-291a-4338-89e0-3e7a37c265ae","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"7701697d-2665-4cc9-8960-93b07a3367aa","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.c20f4aee-748a-4a36-8508-67ebf5f69f81"]}],"calledElement":"1.da205dca-ccab-4203-9f89-719cb8957cc5"},{"targetRef":"7701697d-2665-4cc9-8960-93b07a3367aa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT04 Review ODC Closure Request By Trade Fo Checker","declaredType":"sequenceFlow","id":"e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7","sourceRef":"fb9fca06-f077-4411-861c-9e7b74adf765"},{"outgoing":["2e4f57ab-4f8d-456e-87d3-756c485591a0","b37475e2-fcda-4734-8e24-c7032026c625","b93038e6-0d51-44b7-87a5-8f870bd2e6e2","b3a4187f-1f31-4677-83d5-f99e3c8a0c80"],"incoming":["a9a6619c-f2f2-4eee-8e20-f56a0143cc3b"],"default":"b93038e6-0d51-44b7-87a5-8f870bd2e6e2","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":780,"y":79,"declaredType":"TNodeVisualInfo","height":32}]},"name":"ACT04Decision","declaredType":"exclusiveGateway","id":"81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1"},{"targetRef":"81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT04Decision","declaredType":"sequenceFlow","id":"a9a6619c-f2f2-4eee-8e20-f56a0143cc3b","sourceRef":"7701697d-2665-4cc9-8960-93b07a3367aa"},{"targetRef":"fb9fca06-f077-4411-861c-9e7b74adf765","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToMaker"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"return to maker","declaredType":"sequenceFlow","id":"2e4f57ab-4f8d-456e-87d3-756c485591a0","sourceRef":"81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1"},{"targetRef":"68e55774-7619-4db3-8548-1d17e2918203","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToInitiator"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"return to init","declaredType":"sequenceFlow","id":"b37475e2-fcda-4734-8e24-c7032026c625","sourceRef":"81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1"},{"outgoing":["542ed14c-ab9a-4b28-87ba-463041af5a1b","27a4a67a-1c71-4cd5-84a9-303e35df221a"],"incoming":["b93038e6-0d51-44b7-87a5-8f870bd2e6e2","8eb214a8-7ac3-48b3-8f2a-79b6fc863d52","542ed14c-ab9a-4b28-87ba-463041af5a1b"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":1002,"y":37,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"ODC Closure Execution ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.closureACT05)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.odcRequest.ReversalReason.executionHub.value+\"_EXE_MKR\""]}}]}],"serviceRef":"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"542ed14c-ab9a-4b28-87ba-463041af5a1b","name":"ACT05  ODC Closure Execution","dataInputAssociation":[{"targetRef":"2055.7e82bcd2-ff2f-4edf-8e16-cb3fc06e5642","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.b7b1c9dc-eb0d-4c19-836b-e89ff2fa3215","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"925db450-d9d8-4d13-83cc-f0508b787c62","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.fed13d4d-ae3e-4307-847d-fea93244b330"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}],"sourceRef":["2055.31aa1c84-386b-4b78-8766-571d25ab8d35"]}],"calledElement":"1.93bd8272-8f96-42db-92a3-1bdb463c2c28"},{"outgoing":["a923d1e1-dae3-45e7-8f83-4e583006496a"],"incoming":["27a4a67a-1c71-4cd5-84a9-303e35df221a"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":1003,"y":40,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"ODC Closure Execution Review","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.closureACT06)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.odcRequest.ReversalReason.executionHub.value+\"_EXE_CHKR\""]}}]}],"serviceRef":"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"a923d1e1-dae3-45e7-8f83-4e583006496a","name":"ACT06  ODC Closure Execution","dataInputAssociation":[{"targetRef":"2055.def8c2e1-d89c-4fdb-8491-8ca568d3dbc9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.a2de4a27-e1c1-4a5b-8df4-451dfa95ffad","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"cab9b5ba-5485-402b-8bb5-862badcf84e5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.e8f79546-2c25-4f06-8077-a129808f6df9"]}],"calledElement":"1.1f1dbd1c-004f-40a3-bca6-511fc4094964"},{"targetRef":"925db450-d9d8-4d13-83cc-f0508b787c62","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"approve","declaredType":"sequenceFlow","id":"b93038e6-0d51-44b7-87a5-8f870bd2e6e2","sourceRef":"81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1"},{"incoming":["b3a4187f-1f31-4677-83d5-f99e3c8a0c80"],"eventDefinition":[{"extensionElements":{"terminateEventSettings":[{"terminateEntireProcess":false,"deleteCaseFolder":false,"deleteProcessInstance":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTerminateEventSettings"}]},"declaredType":"terminateEventDefinition","id":"c0f0316b-c305-4cee-88d8-d6ef9f56f2c8","otherAttributes":{"eventImplId":"30d8938e-6e0c-4783-8be6-45ca43dc2b2c"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":947,"y":60,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Terminate","declaredType":"endEvent","id":"c58e107b-da02-48b8-8a6c-708b76fa25ed"},{"targetRef":"c58e107b-da02-48b8-8a6c-708b76fa25ed","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.terminateRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"To Terminate","declaredType":"sequenceFlow","id":"b3a4187f-1f31-4677-83d5-f99e3c8a0c80","sourceRef":"81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1"},{"outgoing":["fec2cdcd-40b9-4f20-8741-4992adf46534","8eb214a8-7ac3-48b3-8f2a-79b6fc863d52","311909c0-a416-4e25-88da-19bb1c47a1e1"],"incoming":["a923d1e1-dae3-45e7-8f83-4e583006496a"],"default":"8eb214a8-7ac3-48b3-8f2a-79b6fc863d52","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1187,"y":59,"declaredType":"TNodeVisualInfo","height":32}]},"name":"ACT06Decision","declaredType":"exclusiveGateway","id":"29265816-0f66-4a98-8df4-fb7790107405"},{"targetRef":"29265816-0f66-4a98-8df4-fb7790107405","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT06Decision","declaredType":"sequenceFlow","id":"a923d1e1-dae3-45e7-8f83-4e583006496a","sourceRef":"cab9b5ba-5485-402b-8bb5-862badcf84e5"},{"incoming":["fec2cdcd-40b9-4f20-8741-4992adf46534"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1349,"y":66,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"ef030c4a-84df-4dec-868a-607a8059d8a4"},{"targetRef":"ef030c4a-84df-4dec-868a-607a8059d8a4","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.approveRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"End","declaredType":"sequenceFlow","id":"fec2cdcd-40b9-4f20-8741-4992adf46534","sourceRef":"29265816-0f66-4a98-8df4-fb7790107405"},{"targetRef":"925db450-d9d8-4d13-83cc-f0508b787c62","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToMaker"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To ACT05  ODC Closure Execution","declaredType":"sequenceFlow","id":"8eb214a8-7ac3-48b3-8f2a-79b6fc863d52","sourceRef":"29265816-0f66-4a98-8df4-fb7790107405"},{"targetRef":"fb9fca06-f077-4411-861c-9e7b74adf765","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToTradeFo"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"back to trade fo maker","declaredType":"sequenceFlow","id":"311909c0-a416-4e25-88da-19bb1c47a1e1","sourceRef":"29265816-0f66-4a98-8df4-fb7790107405"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isFirstTime","isCollection":false,"declaredType":"dataObject","id":"8f89d758-510b-46f0-8ad1-31880445669f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"declaredType":"dataObject","id":"c6f98abb-d7f9-4cdd-851f-bd88fdbcb7fe"},{"outgoing":["6fb1423d-2e91-49d1-8f28-193c1ac41fd6"],"incoming":["687d9cba-9f5d-4614-89fc-d42f433aeb0d","e4e19c0e-f75e-42ef-8956-1435e7093032","bd80f29f-1547-45d4-8fcc-47ceb057f7d5","e4b8fd71-18fb-4cb3-84e1-e071579d0b17","dc58487a-1894-411b-8f22-4d570b1ee0f9","5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"deleteTaskOnCompletion":[true],"nodeVisualInfo":[{"width":95,"x":837,"y":48,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"6fb1423d-2e91-49d1-8f28-193c1ac41fd6","name":"send Escalation Mail","dataInputAssociation":[{"targetRef":"2055.9771d7e8-ca59-430e-8b1a-194cf04c1182","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]},{"targetRef":"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.taskID"]}}]},{"targetRef":"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"975c2a34-b327-4628-89c0-703bc40eac9d","calledElement":"1.d7acf968-6740-4e52-b037-2049466eeeb2"},{"parallelMultiple":false,"outgoing":["687d9cba-9f5d-4614-89fc-d42f433aeb0d"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"1b42d7c5-bc94-4470-85d9-e504ede4bce9","otherAttributes":{"eventImplId":"d455ac38-0703-467d-8a0c-455253a2e45d"}}],"attachedToRef":"cab9b5ba-5485-402b-8bb5-862badcf84e5","extensionElements":{"default":["687d9cba-9f5d-4614-89fc-d42f433aeb0d"],"nodeVisualInfo":[{"width":24,"x":1038,"y":98,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Boundary Event ACT06","declaredType":"boundaryEvent","id":"aad24506-f23e-4e16-8a43-8d25597668c2"},{"targetRef":"975c2a34-b327-4628-89c0-703bc40eac9d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To send Escalation Mail","declaredType":"sequenceFlow","id":"687d9cba-9f5d-4614-89fc-d42f433aeb0d","sourceRef":"aad24506-f23e-4e16-8a43-8d25597668c2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskID","isCollection":false,"declaredType":"dataObject","id":"9879897e-788a-46a9-8bf5-efb685725d8c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"declaredType":"dataObject","id":"ef652e8d-24a4-46d6-8150-33b5b4105303"},{"incoming":["6fb1423d-2e91-49d1-8f28-193c1ac41fd6"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":851,"y":141,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event1","declaredType":"endEvent","id":"40bc9c49-3420-4b68-82fe-da8c33a8ce6a"},{"targetRef":"40bc9c49-3420-4b68-82fe-da8c33a8ce6a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End Event1","declaredType":"sequenceFlow","id":"6fb1423d-2e91-49d1-8f28-193c1ac41fd6","sourceRef":"975c2a34-b327-4628-89c0-703bc40eac9d"},{"parallelMultiple":false,"outgoing":["5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"207815a6-c105-4c16-8cd3-b75b81e25c18","otherAttributes":{"eventImplId":"642b0752-3169-4fa9-8c93-7293934c037d"}}],"attachedToRef":"925db450-d9d8-4d13-83cc-f0508b787c62","extensionElements":{"default":["5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3"],"nodeVisualInfo":[{"width":24,"x":990,"y":78,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Boundary EventACT05","declaredType":"boundaryEvent","id":"26070753-3a9b-471f-80aa-d538dee7d0c9"},{"targetRef":"925db450-d9d8-4d13-83cc-f0508b787c62","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT05  ODC Closure Execution","declaredType":"sequenceFlow","id":"542ed14c-ab9a-4b28-87ba-463041af5a1b","sourceRef":"925db450-d9d8-4d13-83cc-f0508b787c62"},{"parallelMultiple":false,"outgoing":["e4e19c0e-f75e-42ef-8956-1435e7093032"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"3caa741a-205c-4111-8b15-dd2fda73b8e2","otherAttributes":{"eventImplId":"fcfd7f5b-35e8-4826-8a08-8c864758a5f1"}}],"attachedToRef":"7701697d-2665-4cc9-8960-93b07a3367aa","extensionElements":{"default":["e4e19c0e-f75e-42ef-8956-1435e7093032"],"nodeVisualInfo":[{"width":24,"x":627,"y":114,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Boundary EventACT04","declaredType":"boundaryEvent","id":"5df6c921-78dc-44c4-8085-bdebe578009a"},{"targetRef":"975c2a34-b327-4628-89c0-703bc40eac9d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To send Escalation Mail","declaredType":"sequenceFlow","id":"e4e19c0e-f75e-42ef-8956-1435e7093032","sourceRef":"5df6c921-78dc-44c4-8085-bdebe578009a"},{"parallelMultiple":false,"outgoing":["bd80f29f-1547-45d4-8fcc-47ceb057f7d5"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"ac571174-cac7-4576-8307-5a4b81320211","otherAttributes":{"eventImplId":"27bfc5cb-b1c5-4c2e-8671-b640861ae503"}}],"attachedToRef":"fb9fca06-f077-4411-861c-9e7b74adf765","extensionElements":{"default":["bd80f29f-1547-45d4-8fcc-47ceb057f7d5"],"nodeVisualInfo":[{"width":24,"x":580,"y":60,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Boundary EventACT03","declaredType":"boundaryEvent","id":"318eaa03-4c5d-4df7-8381-c901dd2eb63b"},{"targetRef":"975c2a34-b327-4628-89c0-703bc40eac9d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To send Escalation Mail","declaredType":"sequenceFlow","id":"bd80f29f-1547-45d4-8fcc-47ceb057f7d5","sourceRef":"318eaa03-4c5d-4df7-8381-c901dd2eb63b"},{"parallelMultiple":false,"outgoing":["e4b8fd71-18fb-4cb3-84e1-e071579d0b17"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"6846a41f-f67e-4f8d-81e6-275fbfeaa6f0","otherAttributes":{"eventImplId":"7d20dde6-f4bc-4c6c-85ce-9ff8a0211042"}}],"attachedToRef":"6bf177a9-2ccf-48dc-8575-5c4463d30254","extensionElements":{"default":["e4b8fd71-18fb-4cb3-84e1-e071579d0b17"],"nodeVisualInfo":[{"width":24,"x":429,"y":85,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Boundary EventACT02","declaredType":"boundaryEvent","id":"25d557e0-9c3e-4a33-8872-e5e32b064bfe"},{"targetRef":"975c2a34-b327-4628-89c0-703bc40eac9d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To send Escalation Mail","declaredType":"sequenceFlow","id":"e4b8fd71-18fb-4cb3-84e1-e071579d0b17","sourceRef":"25d557e0-9c3e-4a33-8872-e5e32b064bfe"},{"parallelMultiple":false,"outgoing":["dc58487a-1894-411b-8f22-4d570b1ee0f9"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"550fba45-aab0-4d3b-8bf3-2668dca6957b","otherAttributes":{"eventImplId":"ff3fa9d0-e8c5-4196-87ce-1e60d05cf43e"}}],"attachedToRef":"68e55774-7619-4db3-8548-1d17e2918203","extensionElements":{"default":["dc58487a-1894-411b-8f22-4d570b1ee0f9"],"nodeVisualInfo":[{"width":24,"x":430,"y":98,"declaredType":"TNodeVisualInfo","height":24}],"preAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}}]},"cancelActivity":false,"name":"Boundary EventACT01","declaredType":"boundaryEvent","id":"c42f5667-bb85-4d5f-8748-15716544be33"},{"targetRef":"975c2a34-b327-4628-89c0-703bc40eac9d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To send Escalation Mail","declaredType":"sequenceFlow","id":"dc58487a-1894-411b-8f22-4d570b1ee0f9","sourceRef":"c42f5667-bb85-4d5f-8748-15716544be33"},{"targetRef":"975c2a34-b327-4628-89c0-703bc40eac9d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To send Escalation Mail","declaredType":"sequenceFlow","id":"5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3","sourceRef":"26070753-3a9b-471f-80aa-d538dee7d0c9"},{"targetRef":"cab9b5ba-5485-402b-8bb5-862badcf84e5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT06  ODC Closure Execution","declaredType":"sequenceFlow","id":"27a4a67a-1c71-4cd5-84a9-303e35df221a","sourceRef":"925db450-d9d8-4d13-83cc-f0508b787c62"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"declaredType":"dataObject","id":"ebd90392-b6bc-46c9-8909-0008c56b9255"}],"laneSet":[{"id":"abe9ba35-6110-4d4b-a253-3faed7c8efae","lane":[{"flowNodeRef":["198ea770-2e81-49a9-8c6c-0e341b5f2c32","68e55774-7619-4db3-8548-1d17e2918203","c42f5667-bb85-4d5f-8748-15716544be33"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Branch \/ Hub Maker","partitionElementRef":"24.e7977935-dba2-437e-9671-4ec41d29e437","declaredType":"lane","id":"800d3d50-7f3e-4836-90c8-7199a2a30bb8","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["6bf177a9-2ccf-48dc-8575-5c4463d30254","bbb343ab-cad3-42e8-8cdf-927be59b54c7","96ed6632-0e83-41a4-8ad8-96e57d448f32","25d557e0-9c3e-4a33-8872-e5e32b064bfe"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Branch Hub Compliance Rep","partitionElementRef":"24.b1b8b540-604c-41d9-8fd7-6253273000f8","declaredType":"lane","id":"472e41d2-c4a6-4b03-85f4-b22cbb093dd4","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["fb9fca06-f077-4411-861c-9e7b74adf765","318eaa03-4c5d-4df7-8381-c901dd2eb63b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":402,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Trade Front Office","partitionElementRef":"24.e92aee43-caab-4c22-88e1-3f7edf14b1ba","declaredType":"lane","id":"258baacc-eed6-4c0f-8a64-ab22b7f72406","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["7701697d-2665-4cc9-8960-93b07a3367aa","81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1","c58e107b-da02-48b8-8a6c-708b76fa25ed","5df6c921-78dc-44c4-8085-bdebe578009a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":603,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Trade Front Office Checker","partitionElementRef":"24.b336dee6-981f-436d-aff5-ad4053a7a3fe","declaredType":"lane","id":"bdab0fad-28c7-4a91-8c7e-f4d29021fa89","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["925db450-d9d8-4d13-83cc-f0508b787c62","26070753-3a9b-471f-80aa-d538dee7d0c9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":804,"declaredType":"TNodeVisualInfo","height":200}]},"name":"back to exe hub maker","partitionElementRef":"24.3ff202c1-5f0d-4c91-acc4-e0dbd69cf4e0","declaredType":"lane","id":"b4b7185c-2c29-4055-81ec-895287c51334","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["cab9b5ba-5485-402b-8bb5-862badcf84e5","29265816-0f66-4a98-8df4-fb7790107405","ef030c4a-84df-4dec-868a-607a8059d8a4","aad24506-f23e-4e16-8a43-8d25597668c2"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":1005,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Execution Hub Checker","partitionElementRef":"24.8e005024-3fe0-4848-8c3c-f1e9483900c6","declaredType":"lane","id":"ef55820f-7b10-4fcf-8bb8-659fc7de19ce","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["975c2a34-b327-4628-89c0-703bc40eac9d","40bc9c49-3420-4b68-82fe-da8c33a8ce6a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":1206,"declaredType":"TNodeVisualInfo","height":200}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"76ad5f2b-581d-4307-b43f-95388f8aa989","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"resourceRole":[{"name":"participantRef","declaredType":"resourceRole"},{"name":"businessDataParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6","name":"perfMetricParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.48d9e8e1-2b51-4173-ac71-9a6c533d134e","name":"ownerTeamParticipantRef","declaredType":"resourceRole"}],"isClosed":false,"extensionElements":{"bpdExtension":[{"allowContentOperations":false,"enableTracking":false,"workSchedule":{"timezoneType":0,"timeScheduleType":0,"timezone":"Africa\/Cairo","timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"instanceName":"\" Request Type: \"+ tw.local.odcRequest.requestType.name  +\" , \"+\" CIF: \"+ tw.local.odcRequest.cif +\" , \" +\"Request Number: \" +tw.system.process.instanceId","dueDateSettings":{"dueDate":{"unit":"Hours","value":"8","timeOfDay":"00:00"},"type":"TimeCalculation"},"autoTrackingName":"at1693724550026","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension","optimizeExecForLatency":false,"dueDateEnabled":true,"atRiskCalcEnabled":false,"allowProjectedPathManagement":false,"autoTrackingEnabled":false,"sboSyncEnabled":false}],"caseExtension":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension","caseFolder":{"allowSubfoldersCreation":false,"allowLocalDoc":false,"id":"02c63cda-04f4-40f8-877a-01ecea9469b3","allowExternalFolder":false,"allowExternalDoc":false}}],"isConvergedProcess":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"ODC Closure \u0627\u0642\u0641\u0627\u0644 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631","declaredType":"process","id":"25.8bd72fcf-6286-46f9-b8c6-fcfd060f8964","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"longRunning"},"ioSpecification":{"dataOutput":[{"extensionElements":{},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2007.e45e587b-7a69-43ca-87bf-4c739f50a885"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"3f6851e8-edd7-40cb-819e-cae6ed69c342","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.dbbaa047-8f02-4397-b1b5-41f11b0256b3","epvProcessLinkId":"5002cae0-be64-4941-8e83-9c01aff49ffe","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{},{"id":"_1d37f7c2-ec9c-45c5-95f6-fd2214859053"}],"outputSet":[{},{"id":"_85968968-8bbb-4deb-8075-dba72353d31b"}],"dataInput":[{"extensionElements":{"searchableField":[{"path":".cif","alias":"cif","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"9cf59869-19ec-4116-8f5c-d974f4049ba5","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".customerName","alias":"customerName","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"e6ae263b-b95e-4e92-83b1-3658ce343e09","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".parentRequestNo","alias":"parentRequestNo","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"ea50ce4d-3a9d-4aff-8094-b7801792467b","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".initiator","alias":"InitiatorUserName","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"eb6dfa5f-4fda-4b9c-83cc-5b4e4395c01f","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.requestDate","alias":"requestDate","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"1806bb4f-20cb-4115-80f9-2bf0e836b210","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.status","alias":"requeststatus","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"655eb844-98fb-41e4-854f-0611a316c79d","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.initiator","alias":"InitiatorBranchHub","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"449d6dd1-6621-4542-84f6-f6ac56f38088","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2007.b01cffc3-d443-456f-83aa-ccefe78d1e40"},{"itemSubjectRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","name":"routingDetails","isCollection":false,"id":"2007.9e5082af-a9b5-42f8-813c-dd8d7d001e4b"}]}},{"name":"ODC Closure \u0627\u0642\u0641\u0627\u0644 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631Interface","declaredType":"interface","id":"_8ff71a51-0d88-4f43-bcb1-d5c449bd965f"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1dd604e3-a9a7-4a75-afac-32350fbf6687"}</jsonData>
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a50e4858f:-982</guid>
        <versionId>8085dbb1-53f3-4f16-b3ba-7ce403f4f24e</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-168">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>ODC Closure اقفال تحصيل مستندى تصدير</name>
            <documentation></documentation>
            <name>ODC Closure اقفال تحصيل مستندى تصدير</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>mohamed.reda</author>
            <isTrackingEnabled>false</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>true</isDueDateEnabled>
            <isAtRiskCalcEnabled>false</isAtRiskCalcEnabled>
            <creationDate>1693724550033</creationDate>
            <modificationDate>1702815828398</modificationDate>
            <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</perfMetricParticipantRef>
            <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ownerTeamParticipantRef>
            <metricSettings itemType="2" />
            <instanceNameExpression>" Request Type: "+ tw.local.odcRequest.requestType.name  +" , "+" CIF: "+ tw.local.odcRequest.cif +" , " +"Request Number: " +tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>1</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <timeScheduleName>NBEWork</timeScheduleName>
            <holidayScheduleName>NBEHoliday</holidayScheduleName>
            <holidayScheduleType>0</holidayScheduleType>
            <timezone>Africa/Cairo</timezone>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>false</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="abe9ba35-6110-4d4b-a253-3faed7c8efae" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-183f" />
            <ownerTeamInstanceUI id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1840" />
            <simulationScenario id="bpdid:ab265fcfa74a0689:-2c3f1876:18a50e4858f:-96f">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1693724550397</startTime>
            </simulationScenario>
            <flow id="a923d1e1-dae3-45e7-8f83-4e583006496a" connectionType="SequenceFlow">
                <name>To ACT06Decision</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a1" />
                </connection>
            </flow>
            <flow id="a9a6619c-f2f2-4eee-8e20-f56a0143cc3b" connectionType="SequenceFlow">
                <name>To ACT04Decision</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a0" />
                </connection>
            </flow>
            <flow id="b93038e6-0d51-44b7-87a5-8f870bd2e6e2" connectionType="SequenceFlow">
                <name>approve</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-179f" />
                </connection>
            </flow>
            <flow id="e4b8fd71-18fb-4cb3-84e1-e071579d0b17" connectionType="SequenceFlow">
                <name>To send Escalation Mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-179e" />
                </connection>
            </flow>
            <flow id="4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7" connectionType="SequenceFlow">
                <name>To ACT02Decision</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-179d" />
                </connection>
            </flow>
            <flow id="b3a4187f-1f31-4677-83d5-f99e3c8a0c80" connectionType="SequenceFlow">
                <name>To Terminate</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c0">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.terminateRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13" connectionType="SequenceFlow">
                <name>To ACT02 Review ODC Closure Request</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-179c" />
                </connection>
            </flow>
            <flow id="687d9cba-9f5d-4614-89fc-d42f433aeb0d" connectionType="SequenceFlow">
                <name>To send Escalation Mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-179b" />
                </connection>
            </flow>
            <flow id="e4e19c0e-f75e-42ef-8956-1435e7093032" connectionType="SequenceFlow">
                <name>To send Escalation Mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-179a" />
                </connection>
            </flow>
            <flow id="6a299dca-fbb1-48c8-8778-fbad8360de7f" connectionType="SequenceFlow">
                <name>Back to initiator</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d2">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="b37475e2-fcda-4734-8e24-c7032026c625" connectionType="SequenceFlow">
                <name>return to init</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c3">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="6fb1423d-2e91-49d1-8f28-193c1ac41fd6" connectionType="SequenceFlow">
                <name>To End Event1</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1799" />
                </connection>
            </flow>
            <flow id="54d888c8-a1d8-40c3-a319-f36b82cdb852" connectionType="SequenceFlow">
                <name>To Inline User Task</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1798" />
                </connection>
            </flow>
            <flow id="e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7" connectionType="SequenceFlow">
                <name>To ACT04 Review ODC Closure Request By Trade Fo Checker</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1797" />
                </connection>
            </flow>
            <flow id="2e4f57ab-4f8d-456e-87d3-756c485591a0" connectionType="SequenceFlow">
                <name>return to maker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c5">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="dc58487a-1894-411b-8f22-4d570b1ee0f9" connectionType="SequenceFlow">
                <name>To send Escalation Mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1796" />
                </connection>
            </flow>
            <flow id="2774b9c8-37d0-42eb-8408-bf6e7973513b" connectionType="SequenceFlow">
                <name>Cancel</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d0">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.cancelRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3" connectionType="SequenceFlow">
                <name>To send Escalation Mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1795" />
                </connection>
            </flow>
            <flow id="fec2cdcd-40b9-4f20-8741-4992adf46534" connectionType="SequenceFlow">
                <name>End</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b9">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.approveRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="27a4a67a-1c71-4cd5-84a9-303e35df221a" connectionType="SequenceFlow">
                <name>To ACT06  ODC Closure Execution</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1794" />
                </connection>
            </flow>
            <flow id="542ed14c-ab9a-4b28-87ba-463041af5a1b" connectionType="SequenceFlow">
                <name>To ACT05  ODC Closure Execution</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1793" />
                </connection>
            </flow>
            <flow id="8eb214a8-7ac3-48b3-8f2a-79b6fc863d52" connectionType="SequenceFlow">
                <name>To ACT05  ODC Closure Execution</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b7">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="63190125-a4ec-4e19-8583-4944b90db2f0" connectionType="SequenceFlow">
                <name>approve</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1792" />
                </connection>
            </flow>
            <flow id="311909c0-a416-4e25-88da-19bb1c47a1e1" connectionType="SequenceFlow">
                <name>back to trade fo maker</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b5">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToTradeFo</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="bd80f29f-1547-45d4-8fcc-47ceb057f7d5" connectionType="SequenceFlow">
                <name>To send Escalation Mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1791" />
                </connection>
            </flow>
            <pool id="abe9ba35-6110-4d4b-a253-3faed7c8efae">
                <name>Pool</name>
                <documentation></documentation>
                <restrictedName>at1693724550026</restrictedName>
                <dimension>
                    <size w="3000" h="1400" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="800d3d50-7f3e-4836-90c8-7199a2a30bb8">
                    <name>Branch / Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>/24.e7977935-dba2-437e-9671-4ec41d29e437</attachedParticipant>
                    <flowObject id="68e55774-7619-4db3-8548-1d17e2918203" componentType="Activity">
                        <name>ACT01 Create ODC Closure Request</name>
                        <position>
                            <location x="442" y="57" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.2af38b7e-9461-4887-9ad2-24d4565ee49b</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>3</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.closureACT01)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Create ODC Closure Request – إنشاء طلب اقفال تحصيل مستندى تصدير &lt;#= tw.system.process.instanceId #&gt;</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-182c">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.365895d0-a5ae-49f2-86d1-de0ac795fbe9</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-182b">
                                    <name>routingDetails</name>
                                    <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails</value>
                                    <parameterId>2055.6fa94dca-e752-4ace-8264-0d551b9ec5bd</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-182a">
                                    <name>isFirstTime</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                                    <input>true</input>
                                    <value>tw.local.isFirstTime</value>
                                    <parameterId>2055.5b04a5b6-755b-4702-8c66-d9a4455f4598</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1829">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.872cc9b3-563d-4875-81d4-07416c1f8697</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1828">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.f98fed95-51fc-473c-8003-aeb193a5d3df</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1827">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.1498704b-0db6-4050-868a-d35f1d79314c</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1826">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.fd93251a-76e2-4304-8758-8af89440122b</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1825">
                                    <serviceType>1</serviceType>
                                    <teamRef>/24.e7977935-dba2-437e-9671-4ec41d29e437</teamRef>
                                    <attachedActivityId>/1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1824">
                                        <name>branchCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.branchCode</value>
                                        <parameterId>2055.b849887a-3e1e-4633-be89-6e2adce84383</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1823">
                                        <name>hubCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.hubCode</value>
                                        <parameterId>2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1822">
                                        <name>branchGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"Branch_Operation_MKR"</value>
                                        <parameterId>2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1821">
                                        <name>hubGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_CR_MKR"</value>
                                        <parameterId>2055.d5717fc2-69b2-4168-a2af-80632cedf962</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1820">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17db">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="54d888c8-a1d8-40c3-a319-f36b82cdb852" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d3">
                            <positionId>rightCenter</positionId>
                            <input>true</input>
                            <flow ref="6a299dca-fbb1-48c8-8778-fbad8360de7f" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c4">
                            <positionId>rightTop</positionId>
                            <input>true</input>
                            <flow ref="b37475e2-fcda-4734-8e24-c7032026c625" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17da">
                            <positionId>bottomCenter</positionId>
                            <flow ref="b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13" />
                        </outputPort>
                        <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-182e">
                            <assignTime>2</assignTime>
                            <to>false</to>
                            <from>tw.local.isFirstTime</from>
                        </assignment>
                        <attachedEvent id="c42f5667-bb85-4d5f-8748-15716544be33" componentType="Event">
                            <name>Boundary EventACT01</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>leftBottom</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="550fba45-aab0-4d3b-8bf3-2668dca6957b">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="ff3fa9d0-e8c5-4196-87ce-1e60d05cf43e">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a7">
                                <positionId>leftCenter</positionId>
                                <flow ref="dc58487a-1894-411b-8f22-4d570b1ee0f9" />
                            </outputPort>
                            <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17df">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="198ea770-2e81-49a9-8c6c-0e341b5f2c32" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="62" y="63" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17dc">
                            <positionId>rightCenter</positionId>
                            <flow ref="54d888c8-a1d8-40c3-a319-f36b82cdb852" />
                        </outputPort>
                        <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17f2">
                            <assignTime>2</assignTime>
                            <to>true</to>
                            <from>tw.local.isFirstTime</from>
                        </assignment>
                    </flowObject>
                </lane>
                <lane id="472e41d2-c4a6-4b03-85f4-b22cbb093dd4">
                    <name>Branch Hub Compliance Rep</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.b1b8b540-604c-41d9-8fd7-6253273000f8</attachedParticipant>
                    <flowObject id="6bf177a9-2ccf-48dc-8575-5c4463d30254" componentType="Activity">
                        <name>ACT02 Review ODC Closure Request</name>
                        <position>
                            <location x="441" y="62" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.0694c1c7-863e-4809-8c83-bafbba3eb2bb</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>3</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.closureACT02)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review ODC Closure Request by Compliance Rep – مراجعة طلب اقفال تحصيل مستندى تصدير من ممثل الإلتزام </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-181e">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.d4fa9d21-5e1a-4996-8745-6d9b1ba1d005</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-181d">
                                    <name>routingDetails</name>
                                    <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails</value>
                                    <parameterId>2055.cda6b97c-96b9-488b-8055-47d670e552ac</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-181c">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.a3159314-99a0-4745-8a3b-4be34b9b6f7b</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-181b">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.790399ab-7b67-4adc-8647-410880393efa</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-181a">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.cd4ee7b8-3ac7-4a16-8a0b-023b2838e7f1</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1819">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.b1b8b540-604c-41d9-8fd7-6253273000f8</teamRef>
                                    <attachedActivityId>/1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1818">
                                        <name>branchCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.branchCode</value>
                                        <parameterId>2055.b849887a-3e1e-4633-be89-6e2adce84383</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1817">
                                        <name>hubCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.hubCode</value>
                                        <parameterId>2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1816">
                                        <name>branchGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"Compliance_MKR"</value>
                                        <parameterId>2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1815">
                                        <name>hubGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_COMP_CHKR"</value>
                                        <parameterId>2055.d5717fc2-69b2-4168-a2af-80632cedf962</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1814">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d9">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="b6f1f90e-54bf-4f15-8ad5-95cd8c2c7e13" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d8">
                            <positionId>rightCenter</positionId>
                            <flow ref="4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7" />
                        </outputPort>
                        <attachedEvent id="25d557e0-9c3e-4a33-8872-e5e32b064bfe" componentType="Event">
                            <name>Boundary EventACT02</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>leftCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="6846a41f-f67e-4f8d-81e6-275fbfeaa6f0">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="7d20dde6-f4bc-4c6c-85ce-9ff8a0211042">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a9">
                                <positionId>leftCenter</positionId>
                                <flow ref="e4b8fd71-18fb-4cb3-84e1-e071579d0b17" />
                            </outputPort>
                            <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17e2">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="96ed6632-0e83-41a4-8ad8-96e57d448f32" componentType="Event">
                        <name>cancel</name>
                        <documentation></documentation>
                        <position>
                            <location x="753" y="87" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d1">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="2774b9c8-37d0-42eb-8408-bf6e7973513b" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="bbb343ab-cad3-42e8-8cdf-927be59b54c7" componentType="Gateway">
                        <name>ACT02Decision</name>
                        <documentation></documentation>
                        <position>
                            <location x="611" y="81" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d7">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="4c9fa7b5-d4c2-464f-8af2-39c5f5d144b7" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d6">
                            <positionId>topCenter</positionId>
                            <flow ref="6a299dca-fbb1-48c8-8778-fbad8360de7f" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d5">
                            <positionId>rightCenter</positionId>
                            <flow ref="2774b9c8-37d0-42eb-8408-bf6e7973513b" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17d4">
                            <positionId>bottomCenter</positionId>
                            <flow ref="63190125-a4ec-4e19-8583-4944b90db2f0" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="258baacc-eed6-4c0f-8a64-ab22b7f72406">
                    <name>Trade Front Office</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.e92aee43-caab-4c22-88e1-3f7edf14b1ba</attachedParticipant>
                    <flowObject id="fb9fca06-f077-4411-861c-9e7b74adf765" componentType="Activity">
                        <name> ACT03 Review ODC Closure Request By Trade Fo</name>
                        <position>
                            <location x="592" y="56" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.closureACT03)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review ODC Closure Request by Trade FO </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1812">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.7b8524b0-bfb2-46dd-854d-9382344f5d1d</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1811">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.aa22438f-a40d-4a2b-8574-34965edd357f</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1810">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.a695b666-deac-4677-831b-28362ebe5d26</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-180f">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.e92aee43-caab-4c22-88e1-3f7edf14b1ba</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-180e">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17cf">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="63190125-a4ec-4e19-8583-4944b90db2f0" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c6">
                            <positionId>rightBottom</positionId>
                            <input>true</input>
                            <flow ref="2e4f57ab-4f8d-456e-87d3-756c485591a0" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b6">
                            <positionId>rightCenter</positionId>
                            <input>true</input>
                            <flow ref="311909c0-a416-4e25-88da-19bb1c47a1e1" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ce">
                            <positionId>bottomCenter</positionId>
                            <flow ref="e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7" />
                        </outputPort>
                        <attachedEvent id="318eaa03-4c5d-4df7-8381-c901dd2eb63b" componentType="Event">
                            <name>Boundary EventACT03</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>leftTop</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="ac571174-cac7-4576-8307-5a4b81320211">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="27bfc5cb-b1c5-4c2e-8671-b640861ae503">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ab">
                                <positionId>leftCenter</positionId>
                                <flow ref="bd80f29f-1547-45d4-8fcc-47ceb057f7d5" />
                            </outputPort>
                            <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17e5">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                </lane>
                <lane id="bdab0fad-28c7-4a91-8c7e-f4d29021fa89">
                    <name>Trade Front Office Checker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.b336dee6-981f-436d-aff5-ad4053a7a3fe</attachedParticipant>
                    <flowObject id="7701697d-2665-4cc9-8960-93b07a3367aa" componentType="Activity">
                        <name>ACT04 Review ODC Closure Request By Trade Fo Checker</name>
                        <position>
                            <location x="592" y="56" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.da205dca-ccab-4203-9f89-719cb8957cc5</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.closureACT04)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review ODC Closure Request by Trade FO Checker</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-180c">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.b22dee7e-b212-48ff-8e2e-95bc9f0371e6</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-180b">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.7e77ef12-1a3f-42ec-86be-d745ee08a665</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-180a">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.ddfa07cf-291a-4338-89e0-3e7a37c265ae</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1809">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.c20f4aee-748a-4a36-8508-67ebf5f69f81</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1808">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.b336dee6-981f-436d-aff5-ad4053a7a3fe</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1807">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17cd">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="e01b6fc8-54f7-49ca-8c20-dff16b6d4ce7" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17cc">
                            <positionId>rightCenter</positionId>
                            <flow ref="a9a6619c-f2f2-4eee-8e20-f56a0143cc3b" />
                        </outputPort>
                        <attachedEvent id="5df6c921-78dc-44c4-8085-bdebe578009a" componentType="Event">
                            <name>Boundary EventACT04</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="3caa741a-205c-4111-8b15-dd2fda73b8e2">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="fcfd7f5b-35e8-4826-8a08-8c864758a5f1">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ad">
                                <positionId>bottomCenter</positionId>
                                <flow ref="e4e19c0e-f75e-42ef-8956-1435e7093032" />
                            </outputPort>
                            <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17e8">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="c58e107b-da02-48b8-8a6c-708b76fa25ed" componentType="Event">
                        <name>Terminate</name>
                        <documentation></documentation>
                        <position>
                            <location x="947" y="60" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                            <EventAction id="c0f0316b-c305-4cee-88d8-d6ef9f56f2c8">
                                <actionType>8</actionType>
                                <actionSubType>0</actionSubType>
                                <EventActionImplementation id="30d8938e-6e0c-4783-8be6-45ca43dc2b2c">
                                    <terminateEntireProcess>false</terminateEntireProcess>
                                    <deleteProcessInstance>false</deleteProcessInstance>
                                    <deleteCaseFolder>false</deleteCaseFolder>
                                </EventActionImplementation>
                            </EventAction>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c1">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="b3a4187f-1f31-4677-83d5-f99e3c8a0c80" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="81f25fd5-a3ce-4e0e-8c8d-e7dd3fe4e6a1" componentType="Gateway">
                        <name>ACT04Decision</name>
                        <documentation></documentation>
                        <position>
                            <location x="780" y="79" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17cb">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="a9a6619c-f2f2-4eee-8e20-f56a0143cc3b" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ca">
                            <positionId>topCenter</positionId>
                            <flow ref="2e4f57ab-4f8d-456e-87d3-756c485591a0" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c9">
                            <positionId>topCenter</positionId>
                            <flow ref="b37475e2-fcda-4734-8e24-c7032026c625" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c7">
                            <positionId>topCenter</positionId>
                            <flow ref="b3a4187f-1f31-4677-83d5-f99e3c8a0c80" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c8">
                            <positionId>rightCenter</positionId>
                            <flow ref="b93038e6-0d51-44b7-87a5-8f870bd2e6e2" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="b4b7185c-2c29-4055-81ec-895287c51334">
                    <name>back to exe hub maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.3ff202c1-5f0d-4c91-acc4-e0dbd69cf4e0</attachedParticipant>
                    <flowObject id="925db450-d9d8-4d13-83cc-f0508b787c62" componentType="Activity">
                        <name>ACT05  ODC Closure Execution</name>
                        <position>
                            <location x="1002" y="37" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.93bd8272-8f96-42db-92a3-1bdb463c2c28</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.closureACT05)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>ODC Closure Execution </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1805">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.7e82bcd2-ff2f-4edf-8e16-cb3fc06e5642</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1804">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.b7b1c9dc-eb0d-4c19-836b-e89ff2fa3215</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1803">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.fed13d4d-ae3e-4307-847d-fea93244b330</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1802">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.31aa1c84-386b-4b78-8766-571d25ab8d35</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1801">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.3ff202c1-5f0d-4c91-acc4-e0dbd69cf4e0</teamRef>
                                    <attachedActivityId>/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1800">
                                        <name>groupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <useDefault>true</useDefault>
                                        <value>"BPM_IDC_HUB_"+tw.local.odcRequest.ReversalReason.executionHub.value+"_EXE_MKR"</value>
                                        <parameterId>2055.b95f0910-f183-4dab-9065-28297a14ceef</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ff">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17c2">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="b93038e6-0d51-44b7-87a5-8f870bd2e6e2" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b8">
                            <positionId>rightCenter</positionId>
                            <input>true</input>
                            <flow ref="8eb214a8-7ac3-48b3-8f2a-79b6fc863d52" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ae">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="542ed14c-ab9a-4b28-87ba-463041af5a1b" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17af">
                            <positionId>bottomCenter</positionId>
                            <flow ref="27a4a67a-1c71-4cd5-84a9-303e35df221a" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b0">
                            <positionId>leftCenter</positionId>
                            <flow ref="542ed14c-ab9a-4b28-87ba-463041af5a1b" />
                        </outputPort>
                        <attachedEvent id="26070753-3a9b-471f-80aa-d538dee7d0c9" componentType="Event">
                            <name>Boundary EventACT05</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>leftBottom</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="207815a6-c105-4c16-8cd3-b75b81e25c18">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="642b0752-3169-4fa9-8c93-7293934c037d">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a5">
                                <positionId>leftCenter</positionId>
                                <flow ref="5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3" />
                            </outputPort>
                            <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ec">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17eb">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                </lane>
                <lane id="ef55820f-7b10-4fcf-8bb8-659fc7de19ce">
                    <name>Execution Hub Checker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</attachedParticipant>
                    <flowObject id="cab9b5ba-5485-402b-8bb5-862badcf84e5" componentType="Activity">
                        <name>ACT06  ODC Closure Execution</name>
                        <position>
                            <location x="1003" y="40" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.1f1dbd1c-004f-40a3-bca6-511fc4094964</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.closureACT06)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>ODC Closure Execution Review</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17fd">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.def8c2e1-d89c-4fdb-8491-8ca568d3dbc9</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17fc">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.a2de4a27-e1c1-4a5b-8df4-451dfa95ffad</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17fb">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.e8f79546-2c25-4f06-8077-a129808f6df9</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17fa">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</teamRef>
                                    <attachedActivityId>/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17f9">
                                        <name>groupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <useDefault>true</useDefault>
                                        <value>"BPM_IDC_HUB_"+tw.local.odcRequest.ReversalReason.executionHub.value+"_EXE_CHKR"</value>
                                        <parameterId>2055.b95f0910-f183-4dab-9065-28297a14ceef</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17f8">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a3">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="27a4a67a-1c71-4cd5-84a9-303e35df221a" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17bf">
                            <positionId>rightCenter</positionId>
                            <flow ref="a923d1e1-dae3-45e7-8f83-4e583006496a" />
                        </outputPort>
                        <attachedEvent id="aad24506-f23e-4e16-8a43-8d25597668c2" componentType="Event">
                            <name>Boundary Event ACT06</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="1b42d7c5-bc94-4470-85d9-e504ede4bce9">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="d455ac38-0703-467d-8a0c-455253a2e45d">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b4">
                                <positionId>bottomCenter</positionId>
                                <flow ref="687d9cba-9f5d-4614-89fc-d42f433aeb0d" />
                            </outputPort>
                            <assignment id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ef">
                                <assignTime>1</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="ef030c4a-84df-4dec-868a-607a8059d8a4" componentType="Event">
                        <name>End Event</name>
                        <documentation></documentation>
                        <position>
                            <location x="1349" y="66" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ba">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="fec2cdcd-40b9-4f20-8741-4992adf46534" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="29265816-0f66-4a98-8df4-fb7790107405" componentType="Gateway">
                        <name>ACT06Decision</name>
                        <documentation></documentation>
                        <position>
                            <location x="1187" y="59" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17be">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="a923d1e1-dae3-45e7-8f83-4e583006496a" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17bd">
                            <positionId>rightCenter</positionId>
                            <flow ref="fec2cdcd-40b9-4f20-8741-4992adf46534" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17bb">
                            <positionId>topCenter</positionId>
                            <flow ref="311909c0-a416-4e25-88da-19bb1c47a1e1" />
                        </outputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17bc">
                            <positionId>topCenter</positionId>
                            <flow ref="8eb214a8-7ac3-48b3-8f2a-79b6fc863d52" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="76ad5f2b-581d-4307-b43f-95388f8aa989">
                    <name>System</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>true</systemLane>
                    <attachedParticipant>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</attachedParticipant>
                    <flowObject id="975c2a34-b327-4628-89c0-703bc40eac9d" componentType="Activity">
                        <name>send Escalation Mail</name>
                        <documentation>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</documentation>
                        <position>
                            <location x="837" y="48" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.d7acf968-6740-4e52-b037-2049466eeeb2</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17f6">
                                    <name>taskId</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.taskID</value>
                                    <parameterId>2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17f5">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17f4">
                                    <serviceType>1</serviceType>
                                    <teamRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17f3">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b3">
                            <positionId>rightBottom</positionId>
                            <input>true</input>
                            <flow ref="687d9cba-9f5d-4614-89fc-d42f433aeb0d" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17ac">
                            <positionId>leftBottom</positionId>
                            <input>true</input>
                            <flow ref="e4e19c0e-f75e-42ef-8956-1435e7093032" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17aa">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="bd80f29f-1547-45d4-8fcc-47ceb057f7d5" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a8">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="e4b8fd71-18fb-4cb3-84e1-e071579d0b17" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a6">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="dc58487a-1894-411b-8f22-4d570b1ee0f9" />
                        </inputPort>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a4">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="5a52f1b7-44ff-4d96-88b0-baacf5b0b9f3" />
                        </inputPort>
                        <outputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b2">
                            <positionId>bottomCenter</positionId>
                            <flow ref="6fb1423d-2e91-49d1-8f28-193c1ac41fd6" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="40bc9c49-3420-4b68-82fe-da8c33a8ce6a" componentType="Event">
                        <name>End Event1</name>
                        <documentation></documentation>
                        <position>
                            <location x="851" y="141" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17b1">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="6fb1423d-2e91-49d1-8f28-193c1ac41fd6" />
                        </inputPort>
                    </flowObject>
                </lane>
                <inputParameter id="b01cffc3-d443-456f-83aa-ccefe78d1e40">
                    <bpdParameterId>2007.b01cffc3-d443-456f-83aa-ccefe78d1e40</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <inputParameter id="9e5082af-a9b5-42f8-813c-dd8d7d001e4b">
                    <bpdParameterId>2007.9e5082af-a9b5-42f8-813c-dd8d7d001e4b</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <outputParameter id="e45e587b-7a69-43ca-87bf-4c739f50a885">
                    <bpdParameterId>2007.e45e587b-7a69-43ca-87bf-4c739f50a885</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </outputParameter>
                <privateVariable id="8f89d758-510b-46f0-8ad1-31880445669f">
                    <name>isFirstTime</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>true</hasDefault>
                    <defaultValue>true</defaultValue>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="c6f98abb-d7f9-4cdd-851f-bd88fdbcb7fe">
                    <name>lastAction</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="9879897e-788a-46a9-8bf5-efb685725d8c">
                    <name>taskID</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="ef652e8d-24a4-46d6-8150-33b5b4105303">
                    <name>mailTo</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="ebd90392-b6bc-46c9-8909-0008c56b9255">
                    <name>parentPath</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <epv id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-1830">
                    <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
                </epv>
                <epv id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-182f">
                    <epvId>/21.dbbaa047-8f02-4397-b1b5-41f11b0256b3</epvId>
                </epv>
                <searchableField id="9cf59869-19ec-4116-8f5c-d974f4049ba5">
                    <name>cif</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4884">
                        <expression>tw.local.odcRequest.cif</expression>
                    </expression>
                </searchableField>
                <searchableField id="e6ae263b-b95e-4e92-83b1-3658ce343e09">
                    <name>customerName</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4883">
                        <expression>tw.local.odcRequest.customerName</expression>
                    </expression>
                </searchableField>
                <searchableField id="ea50ce4d-3a9d-4aff-8094-b7801792467b">
                    <name>parentRequestNo</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4882">
                        <expression>tw.local.odcRequest.parentRequestNo</expression>
                    </expression>
                </searchableField>
                <searchableField id="eb6dfa5f-4fda-4b9c-83cc-5b4e4395c01f">
                    <name>InitiatorUserName</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4881">
                        <expression>tw.local.odcRequest.initiator</expression>
                    </expression>
                </searchableField>
                <searchableField id="1806bb4f-20cb-4115-80f9-2bf0e836b210">
                    <name>requestDate</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4880">
                        <expression>tw.local.odcRequest.appInfo.requestDate</expression>
                    </expression>
                </searchableField>
                <searchableField id="655eb844-98fb-41e4-854f-0611a316c79d">
                    <name>requeststatus</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-487f">
                        <expression>tw.local.odcRequest.appInfo.status</expression>
                    </expression>
                </searchableField>
                <searchableField id="449d6dd1-6621-4542-84f6-f6ac56f38088">
                    <name>InitiatorBranchHub</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-487e">
                        <expression>tw.local.odcRequest.appInfo.initiator</expression>
                    </expression>
                </searchableField>
            </pool>
            <extension id="bpdid:70b7183b552e7c25:-32679a29:18bf6c257ad:-17a2" type="CASE">
                <caseFolder id="02c63cda-04f4-40f8-877a-01ecea9469b3">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

