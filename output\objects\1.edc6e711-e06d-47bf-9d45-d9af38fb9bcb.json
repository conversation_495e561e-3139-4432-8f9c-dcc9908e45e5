{"id": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "versionId": "d7b6d4fd-c803-4126-b7ce-66b725ee7fe1", "name": "Get Customer Information", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "name": "Get Customer Information", "lastModified": "1699541778269", "lastModifiedBy": "so<PERSON>ia", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.********-5c76-4190-87a9-017bf9132a05", "2025.********-5c76-4190-87a9-017bf9132a05"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.5689dc4e-85be-4ee9-85e7-996d3ef723bd", "2025.5689dc4e-85be-4ee9-85e7-996d3ef723bd"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189daab98ba:-7890", "versionId": "d7b6d4fd-c803-4126-b7ce-66b725ee7fe1", "dependencySummary": "<dependencySummary id=\"bpdid:a6be0630e884ccca:-427a0b52:18bb428309d:-4de0\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.5a7ca466-9828-400a-8834-2f34cb046a16\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":205,\"y\":171,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"0ada557d-8131-4a9d-84e8-e97e5799afc4\"},{\"incoming\":[\"3165d5ae-5944-4c25-84e9-ff3a421f73f0\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":860,\"y\":171,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:83e1efe624431d49:-7084ad56:189daab98ba:-788e\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"2967e294-5e12-4441-8e18-1a5ac719f9c4\"},{\"targetRef\":\"********-5c76-4190-87a9-017bf9132a05\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5a7ca466-9828-400a-8834-2f34cb046a16\",\"sourceRef\":\"0ada557d-8131-4a9d-84e8-e97e5799afc4\"},{\"startQuantity\":1,\"outgoing\":[\"3165d5ae-5944-4c25-84e9-ff3a421f73f0\"],\"incoming\":[\"ad1605ac-eb04-4985-8cb2-05b57ab4961a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":653,\"y\":148,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Customer\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"e22f136f-6c80-4698-867d-6b11bc57ebad\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if (tw.local.isSuccessful) {\\r\\n\\ttw.local.customerInfo = new tw.object.CustomerInfo();\\r\\n\\ttw.local.customerInfo.addressLine1= tw.local.customerFullDetails.customerAddress.addressLine1;\\r\\n\\ttw.local.customerInfo.addressLine2= tw.local.customerFullDetails.customerAddress.addressLine2;\\r\\n\\ttw.local.customerInfo.addressLine3= tw.local.customerFullDetails.customerAddress.addressLine3;\\r\\n\\ttw.local.customerInfo.cif= tw.local.customerFullDetails.customerNo;\\r\\n\\ttw.local.customerInfo.commercialRegistrationNo= tw.local.customerFullDetails.commercialRegisterNo;\\r\\n\\ttw.local.customerInfo.commercialRegistrationOffice=  tw.local.customerFullDetails.commercialRegisterOffice;\\t\\r\\n\\ttw.local.customerInfo.customerName= tw.local.customerFullDetails.arabicName;\\t\\t\\r\\n\\ttw.local.customerInfo.customerNoCBE= tw.local.customerFullDetails.customerCBENumber;\\t\\t\\t\\r\\n\\ttw.local.customerInfo.customerSector= {};\\r\\n\\ttw.local.customerInfo.customerType= tw.local.customerFullDetails.customerType;\\r\\n\\ttw.local.customerInfo.facilityType={};\\r\\n\\ttw.local.customerInfo.facilityType.name= \\\"\\\"; \\r\\n\\ttw.local.customerInfo.facilityType.value= tw.local.customerFullDetails.faciliyBranchCode;\\r\\n\\ttw.local.customerInfo.importCardNo = \\\"\\\";\\t\\r\\n\\ttw.local.customerInfo.taxCardNo= tw.local.customerFullDetails.cardTaxNo;\\r\\n\\ttw.local.customerInfo.country = tw.local.customerFullDetails.country.name;\\r\\n\\t\\r\\n}else{\\r\\n\\ttw.local.customerInfo = new tw.object.CustomerInfo();\\r\\n\\ttw.local.customerInfo.cif = tw.local.data;\\r\\n}\\r\\n\\ttw.local.results = {};\\r\\n\\ttw.local.results = tw.local.customerInfo;\\r\\n\\t\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"ad1605ac-eb04-4985-8cb2-05b57ab4961a\"],\"incoming\":[\"2027.5a7ca466-9828-400a-8834-2f34cb046a16\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":395,\"y\":148,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\" \\/\\/tw.local.error={};\"],\"activityType\":[\"CalledProcess\"]},\"name\":\"Linked Service Flow\",\"dataInputAssociation\":[{\"targetRef\":\"2055.9e66331e-0f98-44e0-b836-7f39c9a6d317\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.prefix\"]}}]},{\"targetRef\":\"2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.data\"]}}]},{\"targetRef\":\"2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"ODC Creation \\/ Amendment Process Details\\\"\"]}}]},{\"targetRef\":\"2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user_id\"]}}]},{\"targetRef\":\"2055.b540e2f5-2008-4705-b4e1-7edcf2a379df\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.currentProcessInstanceID\"]}}]},{\"targetRef\":\"2055.4fcde37d-b029-4d8e-ae28-d26a621479e6\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.40e09d4c-cff9-4d38-bd81-0995df08be6f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"********-5c76-4190-87a9-017bf9132a05\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.customerFullDetails\"]}}],\"sourceRef\":[\"2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorCode\"]}}],\"sourceRef\":[\"2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.3d943bb5-aec9-4b29-862f-735a93741afa\"]}],\"calledElement\":\"1.fd9b955b-0237-4cbe-86d6-cd0d295550aa\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.CustomerInfo();\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.addressLine1 = \\\"\\\";\\nautoObject.addressLine2 = \\\"\\\";\\nautoObject.addressLine3 = \\\"\\\";\\nautoObject.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.customerSector.name = \\\"\\\";\\nautoObject.customerSector.value = \\\"\\\";\\nautoObject.customerType = \\\"\\\";\\nautoObject.customerNoCBE = \\\"\\\";\\nautoObject.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.facilityType.name = \\\"\\\";\\nautoObject.facilityType.value = \\\"\\\";\\nautoObject.commercialRegistrationNo = \\\"\\\";\\nautoObject.commercialRegistrationOffice = \\\"\\\";\\nautoObject.taxCardNo = \\\"\\\";\\nautoObject.importCardNo = \\\"\\\";\\nautoObject.initiationHub = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a\",\"name\":\"customerInfo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.********-08a2-4808-89c9-8e44a4d6bd2b\"},{\"itemSubjectRef\":\"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651\",\"name\":\"customerFullDetails\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.3e4694b0-3f2f-44c4-87b8-d88d58928640\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorCode\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.762cf5d2-385d-44ea-89c7-274f79f82d50\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.5157cf4a-8096-484a-8265-fb87ae7ea079\"},{\"targetRef\":\"e22f136f-6c80-4698-867d-6b11bc57ebad\",\"extensionElements\":{\"endStateId\":[\"guid:e30c377f7882db6a:324bd248:186d6576310:-2a57\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Set Customer\",\"declaredType\":\"sequenceFlow\",\"id\":\"ad1605ac-eb04-4985-8cb2-05b57ab4961a\",\"sourceRef\":\"********-5c76-4190-87a9-017bf9132a05\"},{\"targetRef\":\"2967e294-5e12-4441-8e18-1a5ac719f9c4\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"3165d5ae-5944-4c25-84e9-ff3a421f73f0\",\"sourceRef\":\"e22f136f-6c80-4698-867d-6b11bc57ebad\"},{\"incoming\":[\"a0b9951b-43f3-4208-8cfd-431f59c3aeed\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"fb4477fe-d475-4e10-82fe-0ffb6db86ad6\",\"otherAttributes\":{\"eventImplId\":\"139e9b59-4d3e-4266-8d2b-842729075e52\"}}],\"extensionElements\":{\"postAssignmentScript\":[\" \\r\\nlog.info(\\\"********************************** ODC ***************************************\\\");\\r\\nlog.info(\\\"[Get Customer Information -> Log Error ]- start\\\");\\r\\nlog.info(\\\"ProcessInstance :\\\" +tw.system.currentProcessInstanceID + \\\" Error : \\\" + tw.system.error.toString(true));\\r\\n\\r\\nvar attribute = String(tw.system.error.getAttribute(\\\"type\\\"));\\r\\n\\/\\/var element = String(tw.system.error.getElementByTagName(\\\"localizedMessage\\\").item(0).getText());\\r\\ntw.local.errorMsg = attribute + \\\",\\\" ;\\r\\n\\r\\nlog.info(\\\"[Get Customer Information -> Log Error ]- END\\\");\\r\\nlog.info(\\\"*============================================*\\\");\\r\\n\\r\\ntw.local.error.errorText = tw.local.errorMsg;\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":652,\"y\":280,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"preAssignmentScript\":[]},\"name\":\"End Event\",\"dataInputAssociation\":[{\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}]}],\"declaredType\":\"endEvent\",\"id\":\"bdcfb3b3-cad7-48bc-8513-1a5038f7cd07\"},{\"parallelMultiple\":false,\"outgoing\":[\"28475c70-a3b0-4271-833d-22d14cebfa18\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"89bbd14b-517a-47f8-8d27-4dcb52fd0a7d\",\"otherAttributes\":{\"eventImplId\":\"bf7503a1-7341-473d-8843-b508be8b0f0b\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":396,\"y\":281,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"3a428599-9102-422b-879b-13d9ccb36834\"},{\"startQuantity\":1,\"outgoing\":[\"a0b9951b-43f3-4208-8cfd-431f59c3aeed\"],\"incoming\":[\"28475c70-a3b0-4271-833d-22d14cebfa18\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":497,\"y\":256,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Get Collecting Bank Ref Info - \\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"5689dc4e-85be-4ee9-85e7-996d3ef723bd\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"5689dc4e-85be-4ee9-85e7-996d3ef723bd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"28475c70-a3b0-4271-833d-22d14cebfa18\",\"sourceRef\":\"3a428599-9102-422b-879b-13d9ccb36834\"},{\"targetRef\":\"bdcfb3b3-cad7-48bc-8513-1a5038f7cd07\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"a0b9951b-43f3-4208-8cfd-431f59c3aeed\",\"sourceRef\":\"5689dc4e-85be-4ee9-85e7-996d3ef723bd\"}],\"laneSet\":[{\"id\":\"5ff2e70d-1216-4e6d-8d26-c641dad2254b\",\"lane\":[{\"flowNodeRef\":[\"0ada557d-8131-4a9d-84e8-e97e5799afc4\",\"2967e294-5e12-4441-8e18-1a5ac719f9c4\",\"e22f136f-6c80-4698-867d-6b11bc57ebad\",\"********-5c76-4190-87a9-017bf9132a05\",\"bdcfb3b3-cad7-48bc-8513-1a5038f7cd07\",\"3a428599-9102-422b-879b-13d9ccb36834\",\"5689dc4e-85be-4ee9-85e7-996d3ef723bd\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":1037,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":446}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"ccf096f6-a940-417e-812a-7229c555903a\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get Customer Information\",\"declaredType\":\"process\",\"id\":\"1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"results\",\"isCollection\":false,\"id\":\"2055.373a4a6c-181b-43d5-8f0e-8b400049f72e\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4\"}],\"inputSet\":[{}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.373a4a6c-181b-43d5-8f0e-8b400049f72e\",\"2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\/\\/\\\"03024659\\\"\\r\\n\\/\\/\\\"03024659\\\"\\r\\n\\\"02366014\\\"\\r\\n\"}]},\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"documentation\":[{\"content\":[\"&quot;02366014&quot;\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"data\",\"isCollection\":false,\"id\":\"2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "1", "hasDefault": "true", "defaultValue": "//\"03024659\"\r\r\n//\"03024659\"\r\r\n\"02366014\"\r\r\n", "isLocked": "false", "description": "&quot;02366014&quot;", "guid": "d2e3b963-17a1-44e6-962e-6844ea6cffa3", "versionId": "cd2b9277-af49-45dc-86ec-22d31a55894b"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.373a4a6c-181b-43d5-8f0e-8b400049f72e", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "d3105d2d-5e94-474a-8778-176266b8af9c", "versionId": "d453cc12-ad05-465d-82df-b2619d632450"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "981a0dc3-5906-49a9-a0d4-c631de48c919", "versionId": "54d882dc-c231-4600-bbc8-7d8d0b930429"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.db9d3943-9d1f-4a1b-b6d7-cd384a254bd6", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "parameterType": "3", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "05910b35-df2a-40d0-b99a-2260c9581510", "versionId": "806d1778-8087-4e91-9518-b9c7a4dff0ad"}], "processVariable": [{"name": "customerInfo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.********-08a2-4808-89c9-8e44a4d6bd2b", "description": {"isNull": "true"}, "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1b2598e5-31d4-4325-8666-65657213e2a8", "versionId": "76c5984c-f160-419e-893d-0c78851db056"}, {"name": "customerFullDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3e4694b0-3f2f-44c4-87b8-d88d58928640", "description": {"isNull": "true"}, "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "37fc44ac-9ae1-4327-a3de-06a1797ceed0", "versionId": "2bbaa336-3f70-4ff3-a877-babbd9c059e5"}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.762cf5d2-385d-44ea-89c7-274f79f82d50", "description": {"isNull": "true"}, "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9dc5559e-6ba8-47dd-bc4e-b602e6903c74", "versionId": "95dba6db-15c7-44b4-83e6-4353a154a86b"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5157cf4a-8096-484a-8265-fb87ae7ea079", "description": {"isNull": "true"}, "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b2f67fba-a696-457c-abdc-fe6b7da856f7", "versionId": "c04e441d-9c34-4c31-b1f3-b51f83775724"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.2967e294-5e12-4441-8e18-1a5ac719f9c4", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.86aae311-f196-4565-a5a5-ce7cd9ac4d2b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189daab98ba:-788e", "versionId": "9371e1b0-1773-4656-a215-8ef314c98b9d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "860", "y": "171", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.86aae311-f196-4565-a5a5-ce7cd9ac4d2b", "haltProcess": "false", "guid": "13dd6102-4766-4925-b785-cb05fef38365", "versionId": "23daf5d7-3ca3-4564-bed8-b8cf56850d1d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e22f136f-6c80-4698-867d-6b11bc57ebad", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "name": "Set Customer", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.a005d197-207f-4a89-9172-aedf7c8f7303", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189daab98ba:-76cb", "versionId": "c583ba56-f3f3-4db2-8112-26c918d78ed1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "653", "y": "148", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.a005d197-207f-4a89-9172-aedf7c8f7303", "scriptTypeId": "2", "isActive": "true", "script": "if (tw.local.isSuccessful) {\r\r\n\ttw.local.customerInfo = new tw.object.CustomerInfo();\r\r\n\ttw.local.customerInfo.addressLine1= tw.local.customerFullDetails.customerAddress.addressLine1;\r\r\n\ttw.local.customerInfo.addressLine2= tw.local.customerFullDetails.customerAddress.addressLine2;\r\r\n\ttw.local.customerInfo.addressLine3= tw.local.customerFullDetails.customerAddress.addressLine3;\r\r\n\ttw.local.customerInfo.cif= tw.local.customerFullDetails.customerNo;\r\r\n\ttw.local.customerInfo.commercialRegistrationNo= tw.local.customerFullDetails.commercialRegisterNo;\r\r\n\ttw.local.customerInfo.commercialRegistrationOffice=  tw.local.customerFullDetails.commercialRegisterOffice;\t\r\r\n\ttw.local.customerInfo.customerName= tw.local.customerFullDetails.arabicName;\t\t\r\r\n\ttw.local.customerInfo.customerNoCBE= tw.local.customerFullDetails.customerCBENumber;\t\t\t\r\r\n\ttw.local.customerInfo.customerSector= {};\r\r\n\ttw.local.customerInfo.customerType= tw.local.customerFullDetails.customerType;\r\r\n\ttw.local.customerInfo.facilityType={};\r\r\n\ttw.local.customerInfo.facilityType.name= \"\"; \r\r\n\ttw.local.customerInfo.facilityType.value= tw.local.customerFullDetails.faciliyBranchCode;\r\r\n\ttw.local.customerInfo.importCardNo = \"\";\t\r\r\n\ttw.local.customerInfo.taxCardNo= tw.local.customerFullDetails.cardTaxNo;\r\r\n\ttw.local.customerInfo.country = tw.local.customerFullDetails.country.name;\r\r\n\t\r\r\n}else{\r\r\n\ttw.local.customerInfo = new tw.object.CustomerInfo();\r\r\n\ttw.local.customerInfo.cif = tw.local.data;\r\r\n}\r\r\n\ttw.local.results = {};\r\r\n\ttw.local.results = tw.local.customerInfo;\r\r\n\t\r\r\n", "isRule": "false", "guid": "86de0def-beb2-41a6-8aca-1193a741529f", "versionId": "2c68ed3c-322c-488e-89ce-6a1a4056f511"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.5689dc4e-85be-4ee9-85e7-996d3ef723bd", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.8f568e26-89ea-4aa9-9b29-8e85c66caf19", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:651a1a6abf396537:64776e00:18baeba64af:-3756", "versionId": "d9e1e995-43a9-4815-ad83-dbed84f8c905", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "497", "y": "256", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.8f568e26-89ea-4aa9-9b29-8e85c66caf19", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "16628646-50eb-4550-8f1f-e5350b8f802f", "versionId": "19930238-481a-4811-b576-************", "parameterMapping": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d0f0bc7f-4b33-459f-b262-44a8260a2bc5", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.8f568e26-89ea-4aa9-9b29-8e85c66caf19", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "d9ac528e-4eec-4565-ae0c-945be305bf60", "versionId": "2ec3aa2a-dfa3-4eed-8923-b5dc4b52f1b8", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.2265a5d8-859a-46d0-8c98-125c18ee3c48", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.8f568e26-89ea-4aa9-9b29-8e85c66caf19", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "a102ecca-3f89-4651-9049-e7f8cb7921b5", "versionId": "6c0dbf00-ae5f-4825-839a-00ad92c8af5c", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.8a02f55b-5355-4124-b3c1-ff6f15d3f9f3", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.8f568e26-89ea-4aa9-9b29-8e85c66caf19", "useDefault": "false", "value": "\"Get Collecting Bank Ref Info - \"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "565fa8c2-f181-4545-bd0e-5662fd952165", "versionId": "bdc20a77-1e23-43da-847e-faa40436bd69", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.********-5c76-4190-87a9-017bf9132a05", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "name": "Linked Service Flow", "tWComponentName": "SubProcess", "tWComponentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189daab98ba:-76ca", "versionId": "f190960f-a044-4e80-aa83-250ca47ca143", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.a1722930-d4e6-4668-bb6d-c5b541966fe3", "processItemId": "2025.********-5c76-4190-87a9-017bf9132a05", "location": "1", "script": " //tw.local.error={};", "guid": "36864758-4b7d-46cf-84e5-6d47c77b0469", "versionId": "c44c550b-3afd-421c-a4c9-a00b24a53992"}, "layoutData": {"x": "395", "y": "148", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.fd9b955b-0237-4cbe-86d6-cd0d295550aa", "guid": "987eb4ce-a871-4230-9193-5e9afb440516", "versionId": "813dba37-8a36-407f-87cc-90b0531d724c", "parameterMapping": [{"name": "customerCif", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.47000d14-887b-444f-9dc8-957c00eb6040", "processParameterId": "2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.local.data", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "075cd3c1-3b98-45ff-84b9-d33978e90f46", "versionId": "0ddc3057-4ba2-4f26-80d9-6b5704dbd20a", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3cdf5b24-c0d6-45e9-8b77-e9ad19a3f285", "processParameterId": "2055.9e66331e-0f98-44e0-b836-7f39c9a6d317", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "94eb8a56-1a71-462d-9afd-290337e5ec31", "versionId": "6a744bae-d90f-4b06-b170-84cf1bcc0f23", "description": {"isNull": "true"}}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.534b1892-0e1f-4d40-be78-e3f27ff9b032", "processParameterId": "2055.3d943bb5-aec9-4b29-862f-735a93741afa", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "01de1cb4-d3ba-492c-9b74-7a9d281b0b6f", "versionId": "7ee5d07b-5b90-4afe-978b-e4ca0a018bb7", "description": {"isNull": "true"}}, {"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3ec4c921-41f7-4f48-9cab-66710bb56c0a", "processParameterId": "2055.40e09d4c-cff9-4d38-bd81-0995df08be6f", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "null", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "adb9a6e8-2492-4b65-9594-70d9ac7172af", "versionId": "80dbadb0-aeb9-4d31-9d47-dcab9a950319", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.fc55227c-d8a4-41ed-977f-dc1223e9a8a1", "processParameterId": "2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.local.errorCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "125b9d5e-9ce7-4e1a-863b-a94ee0f03197", "versionId": "839f9a30-4e45-4ec6-97be-5fb9d3895a91", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.eb53d669-4679-4f3a-b197-85bea86ca686", "processParameterId": "2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "b9a8f53e-1f85-4937-aa81-c5ab7a982dc9", "versionId": "90a54a93-0b6b-461c-a160-bb37590b9f80", "description": {"isNull": "true"}}, {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5ba0b2e8-f9f6-4521-a7a3-ec6d84b76eeb", "processParameterId": "2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "\"ODC Creation / Amendment Process Details\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "38808528-f3f5-435d-9e05-bd27333924b6", "versionId": "a0b1872f-d743-4c6a-87af-ef42ede747be", "description": {"isNull": "true"}}, {"name": "customerFullDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.60617a4c-9f32-402a-94d2-11569ce5e059", "processParameterId": "2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.local.customerFullDetails", "classRef": "/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "isList": "false", "isInput": "false", "guid": "3dad86d1-1f4b-48e4-9830-3550ac10c80e", "versionId": "b0ee3253-3113-4d97-ae94-513684eae28c", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7f77a43d-d9e8-49b5-96df-30c4035f29ba", "processParameterId": "2055.4fcde37d-b029-4d8e-ae28-d26a621479e6", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "f48791b4-6dad-4cce-ba0d-1426b236b821", "versionId": "b2d2df53-2fda-4eb4-9272-b2bb9730ab0f", "description": {"isNull": "true"}}, {"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3001846b-eeb2-4233-a51f-d086124a88b9", "processParameterId": "2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.system.user_id", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "9eac9f59-e4b1-4eeb-bd47-d3073e13e4ac", "versionId": "db921ff3-9df9-491d-b728-bdacd5bf121c", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.97291905-c005-4b63-af9b-ba4bc9fa8fed", "processParameterId": "2055.b540e2f5-2008-4705-b4e1-7edcf2a379df", "parameterMappingParentId": "3012.4096774c-1ac9-45ab-b59b-0604dc6b1a08", "useDefault": "false", "value": "tw.system.currentProcessInstanceID", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "67dcdb67-6d50-4cf3-8557-ecebbed4d8da", "versionId": "e8205402-b417-4a0e-b0af-bf2ff3b29de6", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bdcfb3b3-cad7-48bc-8513-1a5038f7cd07", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.c473d2e1-d3d7-412b-ad60-ac4ed4f70ded", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a9fddf040a0e47ab:-5b64a0cf:18a6f4800db:-3509", "versionId": "ffaae42d-1fe5-4baf-8e37-0e18329712f7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.b5fb8c5d-84b8-45f3-826f-f27b4c56b1c2", "processItemId": "2025.bdcfb3b3-cad7-48bc-8513-1a5038f7cd07", "location": "1", "script": {"isNull": "true"}, "guid": "334f7ce1-c70c-4e86-bc1b-3dac75dbcb87", "versionId": "4bb3e64e-1c95-4b3a-89e1-6b9583c9943c"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.eb69321f-2fbf-4e58-847a-0b0d2b9ed5f3", "processItemId": "2025.bdcfb3b3-cad7-48bc-8513-1a5038f7cd07", "location": "2", "script": " \r\r\nlog.info(\"********************************** ODC ***************************************\");\r\r\nlog.info(\"[Get Customer Information -> Log Error ]- start\");\r\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\r\n\r\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\r\n//var element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\ntw.local.errorMsg = attribute + \",\" ;\r\r\n\r\r\nlog.info(\"[Get Customer Information -> Log Error ]- END\");\r\r\nlog.info(\"*============================================*\");\r\r\n\r\r\ntw.local.error.errorText = tw.local.errorMsg;", "guid": "f9691a72-62f5-4f2c-a56e-f7ab09561180", "versionId": "5d0a9cb3-45cc-4a4d-9159-03a2749ec6a8"}], "layoutData": {"x": "652", "y": "280", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.c473d2e1-d3d7-412b-ad60-ac4ed4f70ded", "message": "", "faultStyle": "1", "guid": "572744ea-369f-46c3-8d66-5037e26b3a47", "versionId": "f52b2bfa-152d-4958-8a7a-3ba9a067fcf2", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.66f20371-2ea4-47b7-bc39-4ff3d8323646", "processParameterId": "2055.db9d3943-9d1f-4a1b-b6d7-cd384a254bd6", "parameterMappingParentId": "3007.c473d2e1-d3d7-412b-ad60-ac4ed4f70ded", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "f6d32c34-5e23-4e00-8aa3-e0fa1fc9bdca", "versionId": "9d19921c-a2e0-4948-a7af-6c8ad1abb672", "description": {"isNull": "true"}}}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "205", "y": "171", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "396", "y": "281", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Customer Information", "id": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "data", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed", "ns16:documentation": {"_": "&quot;02366014&quot;", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "//\"03024659\"\r\r\n//\"03024659\"\r\r\n\"02366014\"\r\r\n", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "results", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.373a4a6c-181b-43d5-8f0e-8b400049f72e"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4"}], "ns16:inputSet": "", "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.373a4a6c-181b-43d5-8f0e-8b400049f72e", "2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4"]}}, "ns16:laneSet": {"id": "5ff2e70d-1216-4e6d-8d26-c641dad2254b", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "ccf096f6-a940-417e-812a-7229c555903a", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "1037", "height": "446", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["0ada557d-8131-4a9d-84e8-e97e5799afc4", "2967e294-5e12-4441-8e18-1a5ac719f9c4", "e22f136f-6c80-4698-867d-6b11bc57ebad", "********-5c76-4190-87a9-017bf9132a05", "bdcfb3b3-cad7-48bc-8513-1a5038f7cd07", "3a428599-9102-422b-879b-13d9ccb36834", "5689dc4e-85be-4ee9-85e7-996d3ef723bd"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "0ada557d-8131-4a9d-84e8-e97e5799afc4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "205", "y": "171", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.5a7ca466-9828-400a-8834-2f34cb046a16"}, "ns16:endEvent": [{"name": "End", "id": "2967e294-5e12-4441-8e18-1a5ac719f9c4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "860", "y": "171", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:83e1efe624431d49:-7084ad56:189daab98ba:-788e"}, "ns16:incoming": "3165d5ae-5944-4c25-84e9-ff3a421f73f0"}, {"name": "End Event", "id": "bdcfb3b3-cad7-48bc-8513-1a5038f7cd07", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "652", "y": "280", "width": "24", "height": "24"}, "ns3:postAssignmentScript": " \r\r\nlog.info(\"********************************** ODC ***************************************\");\r\r\nlog.info(\"[Get Customer Information -> Log Error ]- start\");\r\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\r\n\r\r\nvar attribute = String(tw.system.error.getAttribute(\"type\"));\r\r\n//var element = String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\ntw.local.errorMsg = attribute + \",\" ;\r\r\n\r\r\nlog.info(\"[Get Customer Information -> Log Error ]- END\");\r\r\nlog.info(\"*============================================*\");\r\r\n\r\r\ntw.local.error.errorText = tw.local.errorMsg;", "ns3:preAssignmentScript": ""}, "ns16:incoming": "a0b9951b-43f3-4208-8cfd-431f59c3aeed", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:errorEventDefinition": {"id": "fb4477fe-d475-4e10-82fe-0ffb6db86ad6", "eventImplId": "139e9b59-4d3e-4266-8d2b-842729075e52", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "0ada557d-8131-4a9d-84e8-e97e5799afc4", "targetRef": "********-5c76-4190-87a9-017bf9132a05", "name": "To End", "id": "2027.5a7ca466-9828-400a-8834-2f34cb046a16", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "********-5c76-4190-87a9-017bf9132a05", "targetRef": "e22f136f-6c80-4698-867d-6b11bc57ebad", "name": "To Set Customer", "id": "ad1605ac-eb04-4985-8cb2-05b57ab4961a", "ns16:extensionElements": {"ns3:endStateId": "guid:e30c377f7882db6a:324bd248:186d6576310:-2a57", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "e22f136f-6c80-4698-867d-6b11bc57ebad", "targetRef": "2967e294-5e12-4441-8e18-1a5ac719f9c4", "name": "To End", "id": "3165d5ae-5944-4c25-84e9-ff3a421f73f0", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "3a428599-9102-422b-879b-13d9ccb36834", "targetRef": "5689dc4e-85be-4ee9-85e7-996d3ef723bd", "name": "To Exception Handling", "id": "28475c70-a3b0-4271-833d-22d14cebfa18", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "5689dc4e-85be-4ee9-85e7-996d3ef723bd", "targetRef": "bdcfb3b3-cad7-48bc-8513-1a5038f7cd07", "name": "To End Event", "id": "a0b9951b-43f3-4208-8cfd-431f59c3aeed", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Set Customer", "id": "e22f136f-6c80-4698-867d-6b11bc57ebad", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "653", "y": "148", "width": "95", "height": "70"}}, "ns16:incoming": "ad1605ac-eb04-4985-8cb2-05b57ab4961a", "ns16:outgoing": "3165d5ae-5944-4c25-84e9-ff3a421f73f0", "ns16:script": "if (tw.local.isSuccessful) {\r\r\n\ttw.local.customerInfo = new tw.object.CustomerInfo();\r\r\n\ttw.local.customerInfo.addressLine1= tw.local.customerFullDetails.customerAddress.addressLine1;\r\r\n\ttw.local.customerInfo.addressLine2= tw.local.customerFullDetails.customerAddress.addressLine2;\r\r\n\ttw.local.customerInfo.addressLine3= tw.local.customerFullDetails.customerAddress.addressLine3;\r\r\n\ttw.local.customerInfo.cif= tw.local.customerFullDetails.customerNo;\r\r\n\ttw.local.customerInfo.commercialRegistrationNo= tw.local.customerFullDetails.commercialRegisterNo;\r\r\n\ttw.local.customerInfo.commercialRegistrationOffice=  tw.local.customerFullDetails.commercialRegisterOffice;\t\r\r\n\ttw.local.customerInfo.customerName= tw.local.customerFullDetails.arabicName;\t\t\r\r\n\ttw.local.customerInfo.customerNoCBE= tw.local.customerFullDetails.customerCBENumber;\t\t\t\r\r\n\ttw.local.customerInfo.customerSector= {};\r\r\n\ttw.local.customerInfo.customerType= tw.local.customerFullDetails.customerType;\r\r\n\ttw.local.customerInfo.facilityType={};\r\r\n\ttw.local.customerInfo.facilityType.name= \"\"; \r\r\n\ttw.local.customerInfo.facilityType.value= tw.local.customerFullDetails.faciliyBranchCode;\r\r\n\ttw.local.customerInfo.importCardNo = \"\";\t\r\r\n\ttw.local.customerInfo.taxCardNo= tw.local.customerFullDetails.cardTaxNo;\r\r\n\ttw.local.customerInfo.country = tw.local.customerFullDetails.country.name;\r\r\n\t\r\r\n}else{\r\r\n\ttw.local.customerInfo = new tw.object.CustomerInfo();\r\r\n\ttw.local.customerInfo.cif = tw.local.data;\r\r\n}\r\r\n\ttw.local.results = {};\r\r\n\ttw.local.results = tw.local.customerInfo;\r\r\n\t\r\r\n"}, "ns16:callActivity": [{"calledElement": "1.fd9b955b-0237-4cbe-86d6-cd0d295550aa", "name": "Linked Service Flow", "id": "********-5c76-4190-87a9-017bf9132a05", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "395", "y": "148", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:preAssignmentScript": " //tw.local.error={};"}, "ns16:incoming": "2027.5a7ca466-9828-400a-8834-2f34cb046a16", "ns16:outgoing": "ad1605ac-eb04-4985-8cb2-05b57ab4961a", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.9e66331e-0f98-44e0-b836-7f39c9a6d317", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.6d96c319-14fc-4f53-bfc9-616e32a9bd21", "ns16:assignment": {"ns16:from": {"_": "tw.local.data", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.7a3a0a2b-b93b-413a-a5d2-39379f6f9c25", "ns16:assignment": {"ns16:from": {"_": "\"ODC Creation / Amendment Process Details\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.3bd5d9a2-01ae-4985-b10b-0730bb711d06", "ns16:assignment": {"ns16:from": {"_": "tw.system.user_id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b540e2f5-2008-4705-b4e1-7edcf2a379df", "ns16:assignment": {"ns16:from": {"_": "tw.system.currentProcessInstanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.4fcde37d-b029-4d8e-ae28-d26a621479e6", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.40e09d4c-cff9-4d38-bd81-0995df08be6f", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.a151f59c-b3dc-437a-a85f-7fa7693c9fac", "ns16:assignment": {"ns16:to": {"_": "tw.local.customerFullDetails", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651"}}}, {"ns16:sourceRef": "2055.9d25b4e8-eee0-456b-9fd5-f45b05243e22", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.11bd3c00-84ee-4f9b-91ba-0995007a3ac9", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.3d943bb5-aec9-4b29-862f-735a93741afa", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Exception Handling", "id": "5689dc4e-85be-4ee9-85e7-996d3ef723bd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "497", "y": "256", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "28475c70-a3b0-4271-833d-22d14cebfa18", "ns16:outgoing": "a0b9951b-43f3-4208-8cfd-431f59c3aeed", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Get Collecting Bank Ref Info - \"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "isCollection": "false", "name": "customerInfo", "id": "2056.********-08a2-4808-89c9-8e44a4d6bd2b", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.CustomerInfo();\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.addressLine1 = \"\";\r\nautoObject.addressLine2 = \"\";\r\nautoObject.addressLine3 = \"\";\r\nautoObject.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.customerSector.name = \"\";\r\nautoObject.customerSector.value = \"\";\r\nautoObject.customerType = \"\";\r\nautoObject.customerNoCBE = \"\";\r\nautoObject.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.facilityType.name = \"\";\r\nautoObject.facilityType.value = \"\";\r\nautoObject.commercialRegistrationNo = \"\";\r\nautoObject.commercialRegistrationOffice = \"\";\r\nautoObject.taxCardNo = \"\";\r\nautoObject.importCardNo = \"\";\r\nautoObject.initiationHub = \"\";\r\nautoObject", "useDefault": "false"}}}, {"itemSubjectRef": "itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "isCollection": "false", "name": "customerFullDetails", "id": "2056.3e4694b0-3f2f-44c4-87b8-d88d58928640"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorCode", "id": "2056.762cf5d2-385d-44ea-89c7-274f79f82d50"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.5157cf4a-8096-484a-8265-fb87ae7ea079"}], "ns16:intermediateCatchEvent": {"parallelMultiple": "false", "name": "Error Event", "id": "3a428599-9102-422b-879b-13d9ccb36834", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "396", "y": "281", "width": "24", "height": "24"}}, "ns16:outgoing": "28475c70-a3b0-4271-833d-22d14cebfa18", "ns16:errorEventDefinition": {"id": "89bbd14b-517a-47f8-8d27-4dcb52fd0a7d", "eventImplId": "bf7503a1-7341-473d-8843-b508be8b0f0b", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": [{"name": "To Set Customer", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ad1605ac-eb04-4985-8cb2-05b57ab4961a", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.********-5c76-4190-87a9-017bf9132a05", "2025.********-5c76-4190-87a9-017bf9132a05"], "endStateId": "guid:e30c377f7882db6a:324bd248:186d6576310:-2a57", "toProcessItemId": ["2025.e22f136f-6c80-4698-867d-6b11bc57ebad", "2025.e22f136f-6c80-4698-867d-6b11bc57ebad"], "guid": "cec9e701-2bef-4934-a737-c9f1d94b090a", "versionId": "514cad5d-8538-418b-b11b-b90c50178427", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End Event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a0b9951b-43f3-4208-8cfd-431f59c3aeed", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.5689dc4e-85be-4ee9-85e7-996d3ef723bd", "2025.5689dc4e-85be-4ee9-85e7-996d3ef723bd"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.bdcfb3b3-cad7-48bc-8513-1a5038f7cd07", "2025.bdcfb3b3-cad7-48bc-8513-1a5038f7cd07"], "guid": "106164d7-78d9-4098-b6aa-e074c3fd6cf5", "versionId": "bc05c234-b212-482c-b354-6a2fea80d3d6", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.3165d5ae-5944-4c25-84e9-ff3a421f73f0", "processId": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e22f136f-6c80-4698-867d-6b11bc57ebad", "2025.e22f136f-6c80-4698-867d-6b11bc57ebad"], "endStateId": "Out", "toProcessItemId": ["2025.2967e294-5e12-4441-8e18-1a5ac719f9c4", "2025.2967e294-5e12-4441-8e18-1a5ac719f9c4"], "guid": "274a414f-111c-41ed-8c4d-40eca95b91a7", "versionId": "d2abc83d-6ace-4460-b6c4-209bb0c1ccb6", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}