<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a9e6ba43-572e-4e44-b72a-285af67ec153" name="Get ODC charges">
        <lastModified>1700054132401</lastModified>
        <lastModifiedBy>fatma</lastModifiedBy>
        <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.1bf4d450-2909-4cd3-8291-f400930ec311</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:d01</guid>
        <versionId>59c62612-504b-4989-a138-67fa4172d4bf</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23f6" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.538f60bf-610e-494f-8b3e-bcb3f15e81f6"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"cd1cef48-2168-4c58-8301-c95ebc36cbbc"},{"incoming":["e1c95fba-caa8-4bca-89a3-ed459c3a8dbe","9c275fc6-9882-4122-81d0-844ac1d7caf1","167cd9b1-0c96-45f4-805b-85d5cbf3b66e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1426,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:aa4c986259b1691d:-23a6d209:18a11a6235c:d03"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"c201527a-c604-4770-8910-c33a954b9f94"},{"targetRef":"1bf4d450-2909-4cd3-8291-f400930ec311","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To MW_FC Retrieve Commission and Charges","declaredType":"sequenceFlow","id":"2027.538f60bf-610e-494f-8b3e-bcb3f15e81f6","sourceRef":"cd1cef48-2168-4c58-8301-c95ebc36cbbc"},{"startQuantity":1,"outgoing":["c7fec2cc-a8ac-45f8-893c-428c96d35e2e"],"incoming":["2027.538f60bf-610e-494f-8b3e-bcb3f15e81f6"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":72,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"MW_FC Retrieve Commission and Charges","dataInputAssociation":[{"targetRef":"2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.productCode"]}}]},{"targetRef":"2055.d8783716-2453-46e3-8522-b1b2504092a2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.event"]}}]},{"targetRef":"2055.fc2b795d-a78b-4e6c-8b59-c821d346b07f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.contractCurrency"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1bf4d450-2909-4cd3-8291-f400930ec311","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e","declaredType":"TFormalExpression","content":["tw.local.chargesAndCommisions"]}}],"sourceRef":["2055.dd74dd40-8fa5-4359-8243-08e144b543d2"]}],"calledElement":"1.e059295b-f72a-4e32-a329-8d32ebe941de"},{"targetRef":"6dad6d61-f9c4-4838-8dab-dff3832ac22e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Get currency list","declaredType":"sequenceFlow","id":"c7fec2cc-a8ac-45f8-893c-428c96d35e2e","sourceRef":"1bf4d450-2909-4cd3-8291-f400930ec311"},{"startQuantity":1,"outgoing":["d3843721-3e59-447a-8600-a6177efeeb4d"],"incoming":["00265bad-2475-404f-867d-e00442fde081"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1141,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8fc3fef3-da35-42a1-877a-ad45808ccf5b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.counter += 1;"]}},{"itemSubjectRef":"itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e","name":"chargesAndCommisions","isCollection":true,"declaredType":"dataObject","id":"2056.73d8f8ab-73ea-451d-82e3-5865f0396162"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"currencyList","isCollection":true,"declaredType":"dataObject","id":"2056.580ab99c-91df-4b8e-85af-88189fcdf397"},{"startQuantity":1,"outgoing":["881072cd-cbf2-4b81-8aa7-************"],"incoming":["c7fec2cc-a8ac-45f8-893c-428c96d35e2e"],"extensionElements":{"postAssignmentScript":["tw.local.counter = 0;"],"nodeVisualInfo":[{"width":95,"x":187,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get currency list","dataInputAssociation":[{"targetRef":"2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM.IDC_CURRENCY\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"6dad6d61-f9c4-4838-8dab-dff3832ac22e","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.currencyList"]}}],"sourceRef":["2055.91436cf4-7a08-4359-8624-bbfa8c1147c0"]}],"calledElement":"1.2f93c4b5-368e-4a13-aff6-b12926260bb3"},{"targetRef":"e26d983d-d905-429d-82d9-1b33505b2a57","extensionElements":{"endStateId":["guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To is contract CCY?","declaredType":"sequenceFlow","id":"881072cd-cbf2-4b81-8aa7-************","sourceRef":"6dad6d61-f9c4-4838-8dab-dff3832ac22e"},{"parallelMultiple":false,"outgoing":["307779f2-1198-4c88-8c4d-561b4f13b2e5"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"8a714ee8-7ef7-4c21-8a4d-067d98271659"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4b8775c6-6709-47a4-860f-1ff53e0ecd1e","otherAttributes":{"eventImplId":"dbf93067-1f96-4ff4-8962-b85a095895ff"}}],"attachedToRef":"1bf4d450-2909-4cd3-8291-f400930ec311","extensionElements":{"nodeVisualInfo":[{"width":24,"x":107,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"51ccdd63-5578-461b-8a1e-fe3de4212156","outputSet":{}},{"parallelMultiple":false,"outgoing":["e4722332-e9ed-4343-8f9c-2cc485804e07"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"319c5ba1-a480-489d-8530-69008e2b627f"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"824fc12f-0242-4c85-8d01-231a66087ad2","otherAttributes":{"eventImplId":"77620a38-8675-4d2e-8d95-4cd22621090d"}}],"attachedToRef":"6dad6d61-f9c4-4838-8dab-dff3832ac22e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":222,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"b5a3c5dc-bd06-405d-8a80-5464db9d20b8","outputSet":{}},{"parallelMultiple":false,"outgoing":["ccc7e936-4a49-401e-83d3-34caf13b7eb1"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"c9cf9ce1-dd46-4ada-83e4-7911c6ff0110"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"1b460882-afe1-4d24-84f5-5e40e6c83bbb","otherAttributes":{"eventImplId":"50889845-f21f-4888-8c87-fadcedf193a6"}}],"attachedToRef":"8fc3fef3-da35-42a1-877a-ad45808ccf5b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1176,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"45c4b0c9-4645-4fdc-80e7-1f8679a0e7fe","outputSet":{}},{"targetRef":"06bacaef-64f5-46f3-80cc-3f652386932f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"307779f2-1198-4c88-8c4d-561b4f13b2e5","sourceRef":"51ccdd63-5578-461b-8a1e-fe3de4212156"},{"targetRef":"06bacaef-64f5-46f3-80cc-3f652386932f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"e4722332-e9ed-4343-8f9c-2cc485804e07","sourceRef":"b5a3c5dc-bd06-405d-8a80-5464db9d20b8"},{"targetRef":"06bacaef-64f5-46f3-80cc-3f652386932f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"ccc7e936-4a49-401e-83d3-34caf13b7eb1","sourceRef":"45c4b0c9-4645-4fdc-80e7-1f8679a0e7fe"},{"startQuantity":1,"outgoing":["8f70dfda-7d85-4465-8748-1c4b0ac7ff40"],"incoming":["7e1f7ec7-3617-4d4c-8553-fcfa4623d88a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":845,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Calculate default amount","dataInputAssociation":[{"targetRef":"2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.nbeCollectableAmount"]}}]},{"targetRef":"2055.23a7051d-b8b1-4a36-8c49-592e7028cd89","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.chargesList[tw.local.counter].defaultPercentage"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"c2bb7e5f-5830-4ee0-824d-7671c05ecc74","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.chargesList[tw.local.counter].defaultAmount"]}}],"sourceRef":["2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648"]}],"calledElement":"1.769a8281-bf53-46c6-a4c8-571fc192f312"},{"targetRef":"1af6993a-052e-4cf0-8573-019503e48fa7","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Calculate change amount","declaredType":"sequenceFlow","id":"8f70dfda-7d85-4465-8748-1c4b0ac7ff40","sourceRef":"c2bb7e5f-5830-4ee0-824d-7671c05ecc74"},{"outgoing":["7e1f7ec7-3617-4d4c-8553-fcfa4623d88a","9c275fc6-9882-4122-81d0-844ac1d7caf1"],"incoming":["e6549e69-69da-497d-8a61-818b1f7f9e79","7b5eebe1-f537-44b9-8e27-12a033ee6a31"],"default":"9c275fc6-9882-4122-81d0-844ac1d7caf1","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":772,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"fixed rate?","declaredType":"exclusiveGateway","id":"c39c05f0-da5c-4eae-83ad-63d4ee4d518f"},{"targetRef":"c2bb7e5f-5830-4ee0-824d-7671c05ecc74","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.chargesAndCommisions[tw.local.counter].rateType != \"\" &amp;&amp; tw.local.chargesAndCommisions[tw.local.counter].rateType != null &amp;&amp; tw.local.chargesAndCommisions[tw.local.counter].rateType.toLowerCase() == \"fixed rate\""]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Calculate default amount","declaredType":"sequenceFlow","id":"7e1f7ec7-3617-4d4c-8553-fcfa4623d88a","sourceRef":"c39c05f0-da5c-4eae-83ad-63d4ee4d518f"},{"targetRef":"c201527a-c604-4770-8910-c33a954b9f94","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true,"customBendPoint":[{"x":946,"y":21}]}]},"name":"To End","declaredType":"sequenceFlow","id":"9c275fc6-9882-4122-81d0-844ac1d7caf1","sourceRef":"c39c05f0-da5c-4eae-83ad-63d4ee4d518f"},{"targetRef":"d4fc24f2-d637-4e78-82e4-cd23ce6ee208","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To rows still exits?","declaredType":"sequenceFlow","id":"d3843721-3e59-447a-8600-a6177efeeb4d","sourceRef":"8fc3fef3-da35-42a1-877a-ad45808ccf5b"},{"startQuantity":1,"outgoing":["00265bad-2475-404f-867d-e00442fde081"],"incoming":["8f70dfda-7d85-4465-8748-1c4b0ac7ff40"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":996,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Calculate change amount","dataInputAssociation":[{"targetRef":"2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.nbeCollectableAmount"]}}]},{"targetRef":"2055.23a7051d-b8b1-4a36-8c49-592e7028cd89","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.chargesList[tw.local.counter].changePercentage"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1af6993a-052e-4cf0-8573-019503e48fa7","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.chargesList[tw.local.counter].changeAmount"]}}],"sourceRef":["2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648"]}],"calledElement":"1.769a8281-bf53-46c6-a4c8-571fc192f312"},{"targetRef":"8fc3fef3-da35-42a1-877a-ad45808ccf5b","extensionElements":{"endStateId":["guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"00265bad-2475-404f-867d-e00442fde081","sourceRef":"1af6993a-052e-4cf0-8573-019503e48fa7"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"counter","isCollection":false,"declaredType":"dataObject","id":"2056.c1af395a-a90d-4b7d-89ee-1fe20199ee62"},{"startQuantity":1,"outgoing":["d0806dfe-c7b4-4d2b-85ae-47450f50aafa"],"incoming":["c658f7bf-4742-4ffc-8e62-96fee3f780b5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":495,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"contract currency","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"e6e8aafe-ac56-4c9a-8d60-83cf82b1b707","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();\r\nif(tw.local.chargesAndCommisions != null)\r\n{\r\n\tfor(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)\r\n\t{\r\n\t\tif(tw.local.chargesAndCommisions[i].ruleType!= \"\" &amp;&amp; tw.local.chargesAndCommisions[i].ruleType != null &amp;&amp; tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == \"charge\")\r\n\t\t{\r\n\t\t\ttw.local.chargesList[i] = new tw.object.ChargesAndCommissions();\r\n\t\t\ttw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;\r\n\t\t\ttw.local.chargesList[i].description = tw.local.chargesAndCommisions[i].description;\r\n\t\t\ttw.local.chargesList[i].basicAmountCurrency = tw.local.requestCurrency.name;\r\n\t\t\ttw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();\r\n\r\n\r\n\t\t\t\r\n\t\t\ttw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;\r\n\t\t\ttw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.name = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.value = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.name = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.value = \"\";\r\n\/\/\t\t\ttw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;\r\n\t\t\t\r\n\t\t\tif(tw.local.chargesAndCommisions[i].rateType!= \"\" &amp;&amp; tw.local.chargesAndCommisions[i].rateType != null &amp;&amp; tw.local.chargesAndCommisions[i].rateType.toLowerCase() == \"flat amount\")\r\n\t\t\t{\r\n\t\t\t\t\r\n\t\t\t\tfor(var x=0;x&lt;tw.local.currencyList.listLength;x++)\r\n\t\t\t\t{\r\n\t\t\t\t\tif(tw.local.chargesList[i].basicAmountCurrency != \"\" &amp;&amp; tw.local.chargesList[i].basicAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;\r\n\t\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\t\/\/tw.local.chargesList[i].flatAmount = tw.local.chargesAndCommisions[i].flatAmount;\r\n\t\t\t\t\/\/tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].flatAmount;\/\/added by sg\r\n\t\t\t\t\/\/tw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].flatAmount;\r\n\t\t\t\t\r\n\t\t\t\t\/\/based on the latest Charges updates as per the SRS\r\n\t\t\t\ttw.local.chargesList[i].flatAmount = tw.local.equivelantAmount;\r\n\t\t\t\ttw.local.chargesList[i].defaultAmount = tw.local.equivelantAmount;\r\n\t\t\t\ttw.local.chargesList[i].changeAmount = tw.local.equivelantAmount;\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\ttw.local.chargesList[i].defaultPercentage = tw.local.chargesAndCommisions[i].rate;\r\n\t\t\t\ttw.local.chargesList[i].changePercentage = tw.local.chargesList[i].defaultPercentage;\r\n\t\t\t\ttw.local.chargesList[i].defaultCurrency.name = tw.local.requestCurrency.name;\r\n\t\t\t\ttw.local.chargesList[i].defaultCurrency.value = tw.local.requestCurrency.value;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(tw.local.chargesAndCommisions[i].defaultWaived != \"\" &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived != null &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == \"y\")\r\n\t\t\t{\r\n\t\t\t\ttw.local.chargesList[i].waiver = true;\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\ttw.local.chargesList[i].waiver = false;\t\t\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttw.local.chargesList[i].debitedAccount.balance = 0.0;\r\n\t\t\ttw.local.chargesList[i].debitedAccount.balanceSign = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.isOverDraft = false;\r\n\t\t\t\r\n\t\t\ttw.local.chargesList[i].debitedAmount = {};\r\n\t\t\ttw.local.chargesList[i].debitedAmount.amountInAccount = 0.0;\r\n\t\t\ttw.local.chargesList[i].debitedAmount.negotiatedExRate = 0.0;\r\n\t\t\ttw.local.chargesList[i].debitedAmount.standardExRate = 0.0;\r\n\r\n\t\t}\r\n\t\t\t\t\r\n\t}\r\n}\r\n\r\ntw.local.counter += 1;"]}},{"targetRef":"41f7cdce-775f-46b0-8858-2c3d5961920c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get equivalent amount","declaredType":"sequenceFlow","id":"d0806dfe-c7b4-4d2b-85ae-47450f50aafa","sourceRef":"e6e8aafe-ac56-4c9a-8d60-83cf82b1b707"},{"outgoing":["e1c95fba-caa8-4bca-89a3-ed459c3a8dbe","e6549e69-69da-497d-8a61-818b1f7f9e79"],"incoming":["d3843721-3e59-447a-8600-a6177efeeb4d"],"default":"e1c95fba-caa8-4bca-89a3-ed459c3a8dbe","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1237,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"rows still exits?","declaredType":"exclusiveGateway","id":"d4fc24f2-d637-4e78-82e4-cd23ce6ee208"},{"targetRef":"c201527a-c604-4770-8910-c33a954b9f94","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"e1c95fba-caa8-4bca-89a3-ed459c3a8dbe","sourceRef":"d4fc24f2-d637-4e78-82e4-cd23ce6ee208"},{"targetRef":"c39c05f0-da5c-4eae-83ad-63d4ee4d518f","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.counter\t  &lt;\t  tw.local.chargesList.listLength"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true,"customBendPoint":[{"x":855,"y":166}]}]},"name":"Yes","declaredType":"sequenceFlow","id":"e6549e69-69da-497d-8a61-818b1f7f9e79","sourceRef":"d4fc24f2-d637-4e78-82e4-cd23ce6ee208"},{"parallelMultiple":false,"outgoing":["9a825d2e-3e89-4a4d-86ea-fe055e6ae38c"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2da27818-ae42-45d1-865f-772b0e9e376d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e338081e-c7f5-4cf5-80b0-6448675e2540","otherAttributes":{"eventImplId":"d16c136d-e116-40ae-8588-286b55544363"}}],"attachedToRef":"c2bb7e5f-5830-4ee0-824d-7671c05ecc74","extensionElements":{"nodeVisualInfo":[{"width":24,"x":880,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"9d33ed9b-7ba1-4b67-8ac1-f1586b4c9eb4","outputSet":{}},{"parallelMultiple":false,"outgoing":["c3717e77-fdf5-4feb-8411-b6d5500ea5f3"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e6d54df3-1e16-4a9d-84f0-a998a8e5d504"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"99f8c8e7-5aad-4f75-89c9-46d5e456c82e","otherAttributes":{"eventImplId":"0ca8aa82-6170-4d13-8cec-c0c253faee7c"}}],"attachedToRef":"1af6993a-052e-4cf0-8573-019503e48fa7","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1031,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error4","declaredType":"boundaryEvent","id":"60db507f-986d-4b56-894a-72a3b64c250e","outputSet":{}},{"parallelMultiple":false,"outgoing":["3515bc0f-bd1b-4596-8bc6-a32795f05466"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"5bfe66e0-ea8e-42a2-8a75-22d6a06e3121"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"d1cd184d-3bac-490b-8264-885078e86502","otherAttributes":{"eventImplId":"f8054a85-32a1-4764-8dda-0e20afe6c517"}}],"attachedToRef":"e6e8aafe-ac56-4c9a-8d60-83cf82b1b707","extensionElements":{"nodeVisualInfo":[{"width":24,"x":530,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error5","declaredType":"boundaryEvent","id":"0d14fd7e-ae4f-4019-8b86-2cd1e008450b","outputSet":{}},{"targetRef":"06bacaef-64f5-46f3-80cc-3f652386932f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"3515bc0f-bd1b-4596-8bc6-a32795f05466","sourceRef":"0d14fd7e-ae4f-4019-8b86-2cd1e008450b"},{"targetRef":"06bacaef-64f5-46f3-80cc-3f652386932f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"c3717e77-fdf5-4feb-8411-b6d5500ea5f3","sourceRef":"60db507f-986d-4b56-894a-72a3b64c250e"},{"targetRef":"06bacaef-64f5-46f3-80cc-3f652386932f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"9a825d2e-3e89-4a4d-86ea-fe055e6ae38c","sourceRef":"9d33ed9b-7ba1-4b67-8ac1-f1586b4c9eb4"},{"startQuantity":1,"outgoing":["781de947-255e-460b-8654-a6d64d19b2bc"],"incoming":["d31b071c-2fae-46b1-856e-c1b307aba01a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":435,"y":158,"declaredType":"TNodeVisualInfo","height":70}]},"name":"charge currency","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"be44fca7-988b-47d1-85ff-11c67c66be42","scriptFormat":"text\/x-javascript","script":{"content":["\r\ntw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();\r\nif(tw.local.chargesAndCommisions != null)\r\n{\r\n\tfor(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)\r\n\t{\r\n\t\tif(tw.local.chargesAndCommisions[i].ruleType!= \"\" &amp;&amp; tw.local.chargesAndCommisions[i].ruleType != null &amp;&amp; tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == \"charge\")\r\n\t\t{\r\n\t\t\ttw.local.chargesList[i] = new tw.object.ChargesAndCommissions();\r\n\t\t\ttw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;\r\n\t\t\ttw.local.chargesList[i].description = tw.local.chargesAndCommisions[i].description;\r\n\t\t\ttw.local.chargesList[i].basicAmountCurrency = tw.local.chargesAndCommisions[i].basisAmountCurrency;\r\n\t\t\ttw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();\r\n\r\n\t\t\t\r\n\t\t\t\/\/tw.local.chargesList[i].defaultCurrency.name = tw.local.chargesAndCommisions[i].basisAmountCurrency;\r\n\t\t\ttw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;\r\n\t\t\ttw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.name = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.accountClass.value = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.name = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.currency.value = \"\";\r\n\/\/\t\t\ttw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;\r\n\t\t\t\r\n\t\t\tif(tw.local.chargesAndCommisions[i].rateType!= \"\" &amp;&amp; tw.local.chargesAndCommisions[i].rateType != null &amp;&amp; tw.local.chargesAndCommisions[i].rateType.toLowerCase() == \"flat amount\")\r\n\t\t\t{\r\n\t\t\t\t\r\n\t\t\t\tfor(var x=0;x&lt;tw.local.currencyList.listLength;x++)\r\n\t\t\t\t{\r\n\t\t\t\t\tif(tw.local.chargesAndCommisions[i].basisAmountCurrency != \"\" &amp;&amp; tw.local.chargesAndCommisions[i].basisAmountCurrency != null &amp;&amp; tw.local.chargesAndCommisions[i].basisAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())\r\n\t\t\t\t\t{\r\n\t\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;\r\n\t\t\t\t\t\ttw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;\r\n\t\t\t\t\t\tbreak;\r\n\t\t\t\t\t}\r\n\t\t\t\t}\r\n\t\t\t\r\n\t\t\t\ttw.local.chargesList[i].flatAmount = tw.local.chargesAndCommisions[i].flatAmount;\r\n\t\t\t\ttw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].flatAmount;\/\/added by sg\r\n\t\t\t\ttw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].flatAmount;\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\ttw.local.chargesList[i].defaultPercentage = tw.local.chargesAndCommisions[i].rate;\r\n\t\t\t\ttw.local.chargesList[i].changePercentage = tw.local.chargesList[i].defaultPercentage;\r\n\t\t\t\ttw.local.chargesList[i].defaultCurrency.name = tw.local.requestCurrency.name;\r\n\t\t\t\ttw.local.chargesList[i].defaultCurrency.value = tw.local.requestCurrency.value;\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\tif(tw.local.chargesAndCommisions[i].defaultWaived != \"\" &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived != null &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == \"y\")\r\n\t\t\t{\r\n\t\t\t\ttw.local.chargesList[i].waiver = true;\r\n\t\t\t}\r\n\t\t\telse\r\n\t\t\t{\r\n\t\t\t\ttw.local.chargesList[i].waiver = false;\t\t\r\n\t\t\t}\r\n\t\t\t\r\n\t\t\ttw.local.chargesList[i].debitedAccount.balance = 0.0;\r\n\t\t\ttw.local.chargesList[i].debitedAccount.balanceSign = \"\";\r\n\t\t\ttw.local.chargesList[i].debitedAccount.isOverDraft = false;\r\n\t\t\t\r\n\t\t\ttw.local.chargesList[i].debitedAmount = {};\r\n\t\t\ttw.local.chargesList[i].debitedAmount.amountInAccount = 0.0;\r\n\t\t\ttw.local.chargesList[i].debitedAmount.negotiatedExRate = 0.0;\r\n\t\t\ttw.local.chargesList[i].debitedAmount.standardExRate = 0.0;\r\n\r\n\t\t}\r\n\t\t\t\t\r\n\t}\r\n}"]}},{"targetRef":"942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To set counter","declaredType":"sequenceFlow","id":"781de947-255e-460b-8654-a6d64d19b2bc","sourceRef":"be44fca7-988b-47d1-85ff-11c67c66be42"},{"outgoing":["73dcb302-8089-4946-89d5-bf95dbc8dcc5","d31b071c-2fae-46b1-856e-c1b307aba01a"],"incoming":["881072cd-cbf2-4b81-8aa7-************","a746a64e-939c-490b-8171-ec4bd4d77cac"],"default":"d31b071c-2fae-46b1-856e-c1b307aba01a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":294,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is contract CCY?","declaredType":"exclusiveGateway","id":"e26d983d-d905-429d-82d9-1b33505b2a57"},{"targetRef":"5474f549-a95b-4d80-8fb7-1ba56d40d9d7","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.chargesAndCommisions[tw.local.counter].bookingCcy\t  ==\t  \"Contract CCY\""]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"73dcb302-8089-4946-89d5-bf95dbc8dcc5","sourceRef":"e26d983d-d905-429d-82d9-1b33505b2a57"},{"startQuantity":1,"outgoing":["c658f7bf-4742-4ffc-8e62-96fee3f780b5"],"incoming":["73dcb302-8089-4946-89d5-bf95dbc8dcc5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":365,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Get equivalent amount","dataInputAssociation":[{"targetRef":"2055.*************-4e4c-8a23-3a8758392285","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"S\""]}}]},{"targetRef":"2055.f532fd4f-b514-4927-8cce-fd794f488e0d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.293d7351-d3dc-48dc-896c-14e707fd7cba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC creation and amendment\""]}}]},{"targetRef":"2055.6acacd41-404d-41cd-8b73-a6a3f53de59c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"TRANSFER\""]}}]},{"targetRef":"2055.282350b1-2727-4aa1-8118-3d5c3316136a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requestCurrency.name"]}}]},{"targetRef":"2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.chargesAndCommisions[tw.local.counter].basisAmountCurrency"]}}]},{"targetRef":"2055.0427603a-263e-49f1-8ebc-12396435a8f9","assignment":[{"from":{"declaredType":"TFormalExpression","content":["String(tw.local.chargesAndCommisions[tw.local.counter].flatAmount)"]}}]},{"targetRef":"2055.497359ed-14f4-4269-82b7-f09ee83dd3dc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.94fc6012-982f-4612-82c9-77bc1256e0b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.359d19b4-5948-47d2-814d-d3bbc031e650","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"5474f549-a95b-4d80-8fb7-1ba56d40d9d7","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.equivelantAmount"]}}],"sourceRef":["2055.122b436b-8719-4a3f-81ab-c7897c8c2554"]}],"calledElement":"1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de"},{"targetRef":"be44fca7-988b-47d1-85ff-11c67c66be42","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.chargesAndCommisions[0].bookingCcy  "]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"d31b071c-2fae-46b1-856e-c1b307aba01a","sourceRef":"e26d983d-d905-429d-82d9-1b33505b2a57"},{"outgoing":["fbbf7115-ca38-4ed1-8397-7229f7473430","a746a64e-939c-490b-8171-ec4bd4d77cac"],"incoming":["d0806dfe-c7b4-4d2b-85ae-47450f50aafa"],"default":"fbbf7115-ca38-4ed1-8397-7229f7473430","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":32,"x":589,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"counter not equals length?","declaredType":"exclusiveGateway","id":"41f7cdce-775f-46b0-8858-2c3d5961920c"},{"targetRef":"942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"No","declaredType":"sequenceFlow","id":"fbbf7115-ca38-4ed1-8397-7229f7473430","sourceRef":"41f7cdce-775f-46b0-8858-2c3d5961920c"},{"targetRef":"e26d983d-d905-429d-82d9-1b33505b2a57","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.counter\t  &lt;\t  tw.local.chargesAndCommisions.listLength"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true,"customBendPoint":[{"x":504,"y":18}]}]},"name":"Yes","declaredType":"sequenceFlow","id":"a746a64e-939c-490b-8171-ec4bd4d77cac","sourceRef":"41f7cdce-775f-46b0-8858-2c3d5961920c"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"equivelantAmount","isCollection":false,"declaredType":"dataObject","id":"2056.f81ff863-3736-4297-82a8-255b068ae2a6"},{"targetRef":"e6e8aafe-ac56-4c9a-8d60-83cf82b1b707","extensionElements":{"endStateId":["guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To contract currency","declaredType":"sequenceFlow","id":"c658f7bf-4742-4ffc-8e62-96fee3f780b5","sourceRef":"5474f549-a95b-4d80-8fb7-1ba56d40d9d7"},{"startQuantity":1,"outgoing":["7b5eebe1-f537-44b9-8e27-12a033ee6a31"],"incoming":["781de947-255e-460b-8654-a6d64d19b2bc","fbbf7115-ca38-4ed1-8397-7229f7473430"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":668,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"set counter","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.counter = 0;"]}},{"targetRef":"c39c05f0-da5c-4eae-83ad-63d4ee4d518f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To fixed rate?","declaredType":"sequenceFlow","id":"7b5eebe1-f537-44b9-8e27-12a033ee6a31","sourceRef":"942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"amount","isCollection":false,"declaredType":"dataObject","id":"2056.8e1d51a5-4b7a-4469-8d71-373032fe5849"},{"parallelMultiple":false,"outgoing":["7bc37c82-6a81-4ee3-8773-a0ccfc6107c3"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"ee323c69-2e63-4282-8925-10920e24a752"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"9eefcefd-4fbd-414a-84bb-d2eb9dfac4ed","otherAttributes":{"eventImplId":"8d70e568-4a18-40b5-880c-d23e11b5b107"}}],"attachedToRef":"5474f549-a95b-4d80-8fb7-1ba56d40d9d7","extensionElements":{"nodeVisualInfo":[{"width":24,"x":400,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error6","declaredType":"boundaryEvent","id":"7f26776d-118d-4afe-8b9b-d86a81064796","outputSet":{}},{"targetRef":"06bacaef-64f5-46f3-80cc-3f652386932f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"7bc37c82-6a81-4ee3-8773-a0ccfc6107c3","sourceRef":"7f26776d-118d-4afe-8b9b-d86a81064796"},{"startQuantity":1,"outgoing":["167cd9b1-0c96-45f4-805b-85d5cbf3b66e"],"incoming":["7bc37c82-6a81-4ee3-8773-a0ccfc6107c3","e4722332-e9ed-4343-8f9c-2cc485804e07","307779f2-1198-4c88-8c4d-561b4f13b2e5","9a825d2e-3e89-4a4d-86ea-fe055e6ae38c","c3717e77-fdf5-4feb-8411-b6d5500ea5f3","3515bc0f-bd1b-4596-8bc6-a32795f05466","ccc7e936-4a49-401e-83d3-34caf13b7eb1"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":419,"y":368,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Get ODC Charges\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"06bacaef-64f5-46f3-80cc-3f652386932f","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"c201527a-c604-4770-8910-c33a954b9f94","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"167cd9b1-0c96-45f4-805b-85d5cbf3b66e","sourceRef":"06bacaef-64f5-46f3-80cc-3f652386932f"}],"laneSet":[{"id":"69acd631-26d8-4086-875a-3eb00a34e077","lane":[{"flowNodeRef":["cd1cef48-2168-4c58-8301-c95ebc36cbbc","c201527a-c604-4770-8910-c33a954b9f94","1bf4d450-2909-4cd3-8291-f400930ec311","8fc3fef3-da35-42a1-877a-ad45808ccf5b","6dad6d61-f9c4-4838-8dab-dff3832ac22e","51ccdd63-5578-461b-8a1e-fe3de4212156","b5a3c5dc-bd06-405d-8a80-5464db9d20b8","45c4b0c9-4645-4fdc-80e7-1f8679a0e7fe","c2bb7e5f-5830-4ee0-824d-7671c05ecc74","c39c05f0-da5c-4eae-83ad-63d4ee4d518f","1af6993a-052e-4cf0-8573-019503e48fa7","e6e8aafe-ac56-4c9a-8d60-83cf82b1b707","d4fc24f2-d637-4e78-82e4-cd23ce6ee208","9d33ed9b-7ba1-4b67-8ac1-f1586b4c9eb4","60db507f-986d-4b56-894a-72a3b64c250e","0d14fd7e-ae4f-4019-8b86-2cd1e008450b","be44fca7-988b-47d1-85ff-11c67c66be42","e26d983d-d905-429d-82d9-1b33505b2a57","5474f549-a95b-4d80-8fb7-1ba56d40d9d7","41f7cdce-775f-46b0-8858-2c3d5961920c","942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e","7f26776d-118d-4afe-8b9b-d86a81064796","06bacaef-64f5-46f3-80cc-3f652386932f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":1639,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":811}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"e17619f8-3089-45b7-8950-631ac07377e2","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get ODC charges","declaredType":"process","id":"1.a9e6ba43-572e-4e44-b72a-285af67ec153","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061","name":"chargesList","isCollection":true,"id":"2055.04bc68b6-4056-494c-88c6-9cd8ca9fff44"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.809fa05d-ac51-4ed4-866f-ba555d7fcbee"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"id":"2055.a658075e-29bf-4268-8ecd-5be28307cfa8"}],"extensionElements":{"localizationResourceLinks":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}]},"inputSet":[{"dataInputRefs":["2055.e0211148-8032-4b33-8419-b9bba769dba0","2055.974e8b27-bff7-4a03-8e24-ea4d1c97615a","2055.bd37aabd-7dc4-4378-8486-caa82c3cf78a","2055.eff195a0-db60-4737-8f65-3217327b57ac","2055.6f69e2f6-2d65-4df8-8e40-b8d8f4ca1bef"]}],"outputSet":[{"dataOutputRefs":["2055.04bc68b6-4056-494c-88c6-9cd8ca9fff44","2055.809fa05d-ac51-4ed4-866f-ba555d7fcbee","2055.a658075e-29bf-4268-8ecd-5be28307cfa8"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"OSBC\"\r\n\/\/\"OUBC\"\r\n\"OMBC\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"productCode","isCollection":false,"id":"2055.e0211148-8032-4b33-8419-b9bba769dba0"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"INIT\"\r\n\/\/\"AMND\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"event","isCollection":false,"id":"2055.974e8b27-bff7-4a03-8e24-ea4d1c97615a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"1200.0"}]},"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"nbeCollectableAmount","isCollection":false,"id":"2055.bd37aabd-7dc4-4378-8486-caa82c3cf78a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.name = \"EGP\";\nautoObject.value = \"EGP\";\nautoObject"}]},"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"requestCurrency","isCollection":false,"id":"2055.eff195a0-db60-4737-8f65-3217327b57ac"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"USD\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"contractCurrency","isCollection":false,"id":"2055.6f69e2f6-2d65-4df8-8e40-b8d8f4ca1bef"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e0211148-8032-4b33-8419-b9bba769dba0</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"OSBC"&#xD;
//"OUBC"&#xD;
"OMBC"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>44ead51a-be31-4171-95b7-70198bb18ff7</guid>
            <versionId>b0b33fbe-e55b-42f6-bcea-9524146b6547</versionId>
        </processParameter>
        <processParameter name="event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.974e8b27-bff7-4a03-8e24-ea4d1c97615a</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"INIT"&#xD;
//"AMND"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>932dfb65-4b7f-40df-829b-85f63c8e7460</guid>
            <versionId>e30f5bcc-9133-4649-a682-9746b962f92f</versionId>
        </processParameter>
        <processParameter name="nbeCollectableAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bd37aabd-7dc4-4378-8486-caa82c3cf78a</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <seq>3</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>1200.0</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0a13ce4d-e38b-4e4e-ab4e-066cc78f39b9</guid>
            <versionId>6237be58-ed0a-457d-b705-dbc8b2bb7db8</versionId>
        </processParameter>
        <processParameter name="requestCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.eff195a0-db60-4737-8f65-3217327b57ac</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <seq>4</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.name = "EGP";
autoObject.value = "EGP";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d37beae5-79f4-492f-ace0-5f1fd393b258</guid>
            <versionId>c58a9b73-7260-4a1e-91df-869bf580ed09</versionId>
        </processParameter>
        <processParameter name="contractCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6f69e2f6-2d65-4df8-8e40-b8d8f4ca1bef</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"USD"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0c52137a-b4f6-42ba-9b34-ccd6785f9ed9</guid>
            <versionId>95c648f2-cf4d-4c37-b6d8-95befeb422be</versionId>
        </processParameter>
        <processParameter name="chargesList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.04bc68b6-4056-494c-88c6-9cd8ca9fff44</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.e3018bad-b453-4bf5-96fd-09a5141cd061</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>23b0d694-8bc7-4ba1-ac21-0c378a64618e</guid>
            <versionId>12c2293d-57d7-4756-91df-0ca60dfd7136</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.809fa05d-ac51-4ed4-866f-ba555d7fcbee</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>11abfc01-b03f-417c-8c05-ab29f41383d0</guid>
            <versionId>dcc49dcc-7224-4d65-adc9-a0b9a7502e6d</versionId>
        </processParameter>
        <processParameter name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a658075e-29bf-4268-8ecd-5be28307cfa8</processParameterId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>315624fa-324b-4293-a6b8-a6a41feeed21</guid>
            <versionId>3beec6f6-7289-4b22-98ac-7dae1fce7aff</versionId>
        </processParameter>
        <processVariable name="chargesAndCommisions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.73d8f8ab-73ea-451d-82e3-5865f0396162</processVariableId>
            <description isNull="true" />
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>030736ff-3e4c-4891-9746-b77d29772b30</guid>
            <versionId>e2a6cac6-f080-4a56-8add-2ef2772587cc</versionId>
        </processVariable>
        <processVariable name="currencyList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.580ab99c-91df-4b8e-85af-88189fcdf397</processVariableId>
            <description isNull="true" />
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>15303b1a-2d09-42c9-81f8-4bb255b73a02</guid>
            <versionId>254e075c-a810-4be8-930d-656fb64cfcc7</versionId>
        </processVariable>
        <processVariable name="counter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c1af395a-a90d-4b7d-89ee-1fe20199ee62</processVariableId>
            <description isNull="true" />
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c3c66580-d5cc-4e87-9af5-bf1c57d27b3b</guid>
            <versionId>7784c10c-3568-4612-b4c1-f8b1db76b2df</versionId>
        </processVariable>
        <processVariable name="equivelantAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f81ff863-3736-4297-82a8-255b068ae2a6</processVariableId>
            <description isNull="true" />
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>909d75c3-06b8-4160-bd59-c9a71a449f24</guid>
            <versionId>84fa9328-5e2b-4ecf-acd9-e358c7ea963b</versionId>
        </processVariable>
        <processVariable name="amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8e1d51a5-4b7a-4469-8d71-373032fe5849</processVariableId>
            <description isNull="true" />
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>689fa866-bad2-4dea-833e-da94591373fe</guid>
            <versionId>a3aa52b5-dde3-4a57-9983-385ea6b8d8a6</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1bf4d450-2909-4cd3-8291-f400930ec311</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>MW_FC Retrieve Commission and Charges</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:dd8</guid>
            <versionId>3bbdb26c-9207-4c57-94c6-9265e5a04f61</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="72" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</errorHandlerItem>
                <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.e059295b-f72a-4e32-a329-8d32ebe941de</attachedProcessRef>
                <guid>b0ab9eb5-ed3e-4444-bcf5-618a54de3f0e</guid>
                <versionId>9dffdc6a-7e48-406b-95f3-22c11da58f46</versionId>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.867ba228-7f2b-44a6-97eb-97e2656bb3f8</parameterMappingId>
                    <processParameterId>2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a8a2f960-f034-4e76-89b6-ada6851ac65a</guid>
                    <versionId>23023969-a672-4d59-a6ad-49e9800999e5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="ContractCurrency">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b7511088-1451-4edd-98b5-2b29cc96740c</parameterMappingId>
                    <processParameterId>2055.fc2b795d-a78b-4e6c-8b59-c821d346b07f</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.contractCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f2837e4d-7405-4c36-938f-f58de42282ee</guid>
                    <versionId>2ca3f681-d341-4693-9200-262d52baffd0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e574965d-13fb-43ec-848a-635cb638d063</parameterMappingId>
                    <processParameterId>2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>31c5d36b-cf14-4ad8-865f-cadd696229bf</guid>
                    <versionId>3028cc05-0319-49d0-b59b-fb5c06137185</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="event">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ac93bc16-2b0e-44ed-8c8a-de5bad4f9427</parameterMappingId>
                    <processParameterId>2055.d8783716-2453-46e3-8522-b1b2504092a2</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.event</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>882c82e5-d50b-4668-8ba2-8e46f9e7f577</guid>
                    <versionId>360a7a6c-0d2a-459a-8414-41af005e99bb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="chargesAndInterest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7985c3d6-d4ff-45a8-8e33-45b68a0f4a8f</parameterMappingId>
                    <processParameterId>2055.dd74dd40-8fa5-4359-8243-08e144b543d2</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.chargesAndCommisions</value>
                    <classRef>/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>5648f6e4-8f2e-454b-9cbe-29836d40ee74</guid>
                    <versionId>44867720-25d5-46e1-b710-30d70989b313</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e831ac7f-63b6-4f7d-a74f-0b08838d9f54</parameterMappingId>
                    <processParameterId>2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d70c1a00-5aa5-4570-bbd1-32e170572bed</guid>
                    <versionId>56eee21a-fd70-4650-a652-1f4fd903f5a4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6c45e205-c797-4996-96e7-da3c909ff67c</parameterMappingId>
                    <processParameterId>2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9cfd1e23-446b-400a-9731-edeab3ee8424</guid>
                    <versionId>67d02a29-2669-4460-9e4d-ba95eb69e86c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.992dea55-c963-4a62-8be6-5822762365e6</parameterMappingId>
                    <processParameterId>2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>13b29cd2-965a-4417-bbc7-359285907edc</guid>
                    <versionId>974a7c82-8407-424b-8d7a-ada3613ef91d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cff931b9-9ada-4578-a54b-194bda5cdb97</parameterMappingId>
                    <processParameterId>2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8a102155-433e-4f1e-8464-8cb46894f510</guid>
                    <versionId>9b1c2c3d-6d2b-4b59-a8be-4a2f6a1933e5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3909ce98-1f48-4188-b84d-3e07fe99d030</parameterMappingId>
                    <processParameterId>2055.b6919232-c019-43a5-8742-9783cfd63371</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>dbe0582f-37b8-4fa1-909d-9ef481e76c67</guid>
                    <versionId>afb4ed21-a645-43e3-bd34-6560b9ca3eab</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.39917429-732c-49af-86e1-5d1940700005</parameterMappingId>
                    <processParameterId>2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>807db2ed-19a6-4f35-b3ab-21cde3d6dc2a</guid>
                    <versionId>dba09da7-98e6-4be0-81cc-a1b8f6a14de9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e255d632-35a3-4061-b088-f368ba262104</parameterMappingId>
                    <processParameterId>2055.78c4796d-3236-45f3-883b-556500834b95</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b62360e9-de6b-4b80-9c18-20117f56cc84</guid>
                    <versionId>f9203059-8d6a-411a-8863-a84236cb0c6a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bfe4ff01-07b1-46b8-a781-d60e0e81eea4</parameterMappingId>
                    <processParameterId>2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a</processParameterId>
                    <parameterMappingParentId>3012.7a553734-2cf1-4224-9a92-a4a2a33a0a4a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.productCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>dce3c130-022a-45a2-8475-920513b7e317</guid>
                    <versionId>ffbcb8e8-8eb5-46ca-8472-3ac768d7a717</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.be44fca7-988b-47d1-85ff-11c67c66be42</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>charge currency</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.50274fc5-ec87-450f-850d-48a333930c10</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:673e</guid>
            <versionId>43c0a5a5-abc0-491b-bd4f-097f18b50137</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="435" y="158">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.50274fc5-ec87-450f-850d-48a333930c10</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();&#xD;
if(tw.local.chargesAndCommisions != null)&#xD;
{&#xD;
	for(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.chargesAndCommisions[i].ruleType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].ruleType != null &amp;&amp; tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == "charge")&#xD;
		{&#xD;
			tw.local.chargesList[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;&#xD;
			tw.local.chargesList[i].description = tw.local.chargesAndCommisions[i].description;&#xD;
			tw.local.chargesList[i].basicAmountCurrency = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
&#xD;
			&#xD;
			//tw.local.chargesList[i].defaultCurrency.name = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;&#xD;
			tw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.value = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.currency.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency.value = "";&#xD;
//			tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].rateType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].rateType != null &amp;&amp; tw.local.chargesAndCommisions[i].rateType.toLowerCase() == "flat amount")&#xD;
			{&#xD;
				&#xD;
				for(var x=0;x&lt;tw.local.currencyList.listLength;x++)&#xD;
				{&#xD;
					if(tw.local.chargesAndCommisions[i].basisAmountCurrency != "" &amp;&amp; tw.local.chargesAndCommisions[i].basisAmountCurrency != null &amp;&amp; tw.local.chargesAndCommisions[i].basisAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())&#xD;
					{&#xD;
						tw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;&#xD;
						tw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;&#xD;
						break;&#xD;
					}&#xD;
				}&#xD;
			&#xD;
				tw.local.chargesList[i].flatAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
				tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].flatAmount;//added by sg&#xD;
				tw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].defaultPercentage = tw.local.chargesAndCommisions[i].rate;&#xD;
				tw.local.chargesList[i].changePercentage = tw.local.chargesList[i].defaultPercentage;&#xD;
				tw.local.chargesList[i].defaultCurrency.name = tw.local.requestCurrency.name;&#xD;
				tw.local.chargesList[i].defaultCurrency.value = tw.local.requestCurrency.value;&#xD;
			}&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].defaultWaived != "" &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived != null &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = false;		&#xD;
			}&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAccount.balance = 0.0;&#xD;
			tw.local.chargesList[i].debitedAccount.balanceSign = "";&#xD;
			tw.local.chargesList[i].debitedAccount.isOverDraft = false;&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAmount = {};&#xD;
			tw.local.chargesList[i].debitedAmount.amountInAccount = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.negotiatedExRate = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.standardExRate = 0.0;&#xD;
&#xD;
		}&#xD;
				&#xD;
	}&#xD;
}</script>
                <isRule>false</isRule>
                <guid>e3777e85-5794-4575-abf0-b004e0c9a6c1</guid>
                <versionId>0e9a6012-a469-4798-840d-3aa1f96934d1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>counter not equals length?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.047285aa-c0db-4eb0-86eb-302599d8b9d4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aff098473ecd546d:1d42df0a:18b1700f163:-6f12</guid>
            <versionId>5915d6bd-a02e-4792-a249-425f1a5998e3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.ff7ab4c5-c61d-43a8-a948-944c4321866e</processItemPrePostId>
                <processItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>bcc987d3-a73d-4c8f-a983-cca3a61d8f9f</guid>
                <versionId>f30b2357-6386-4ad8-978b-948b831c7c0d</versionId>
            </processPrePosts>
            <layoutData x="589" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.047285aa-c0db-4eb0-86eb-302599d8b9d4</switchId>
                <guid>598bd596-3b3c-4e9a-994e-248567d379ea</guid>
                <versionId>a0003645-78ec-4796-9589-d4c3e0711cca</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.e37bd7e2-296e-4964-9225-986442fdc227</switchConditionId>
                    <switchId>3013.047285aa-c0db-4eb0-86eb-302599d8b9d4</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23f7</endStateId>
                    <condition>tw.local.counter	  &lt;	  tw.local.chargesAndCommisions.listLength</condition>
                    <guid>02a40720-7f1a-4f36-9698-9a2030b56ced</guid>
                    <versionId>57ccdc92-f2aa-4ca5-9fac-62702b7764be</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6dad6d61-f9c4-4838-8dab-dff3832ac22e</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>Get currency list</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d5035983-d0a6-4878-9605-f5d4794efce9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a13ad7c11:68eb</guid>
            <versionId>5af1b919-41f2-42ee-af34-20d241b3cc30</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.362e889d-052d-411b-a9e1-d43f1764a754</processItemPrePostId>
                <processItemId>2025.6dad6d61-f9c4-4838-8dab-dff3832ac22e</processItemId>
                <location>2</location>
                <script>tw.local.counter = 0;</script>
                <guid>9bb57592-23b6-48fb-859f-3876f7bb156b</guid>
                <versionId>bc0f5d4b-913d-4af3-b00c-66a4305d5d46</versionId>
            </processPrePosts>
            <layoutData x="187" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</errorHandlerItem>
                <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d5035983-d0a6-4878-9605-f5d4794efce9</subProcessId>
                <attachedProcessRef>/1.2f93c4b5-368e-4a13-aff6-b12926260bb3</attachedProcessRef>
                <guid>155b6ea8-c65e-4f73-b330-d4e05d81209c</guid>
                <versionId>6053fd66-f136-4977-8007-255978b94e45</versionId>
                <parameterMapping name="data">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7ce28c4f-68d0-404b-8b05-eca122509eb2</parameterMappingId>
                    <processParameterId>2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e</processParameterId>
                    <parameterMappingParentId>3012.d5035983-d0a6-4878-9605-f5d4794efce9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"BPM.IDC_CURRENCY"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7065fe7c-b29f-443b-82fa-ed61fbbaf628</guid>
                    <versionId>524bbfea-8a54-4355-bb02-74979310ebf2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.352ba992-3131-474d-bc1a-3078e798eb75</parameterMappingId>
                    <processParameterId>2055.91436cf4-7a08-4359-8624-bbfa8c1147c0</processParameterId>
                    <parameterMappingParentId>3012.d5035983-d0a6-4878-9605-f5d4794efce9</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.currencyList</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>899fa3b4-fe54-48e6-89b8-a6e12eb058f1</guid>
                    <versionId>cd1cecd0-2cb6-4de9-801d-f9c5ef902d18</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>is contract CCY?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.cdf7afcd-e7dd-4c65-a5f6-66488f02748f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aff098473ecd546d:1d42df0a:18b1700f163:-7047</guid>
            <versionId>62553856-a7c0-4445-bcc5-5e594132ede0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="294" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.cdf7afcd-e7dd-4c65-a5f6-66488f02748f</switchId>
                <guid>a507f8df-869e-4673-b011-7ccacc1f1f49</guid>
                <versionId>21fc4e7a-d2ba-4e35-ac52-85e0d52e079c</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.8ce6dfcc-5719-4f44-b36e-71213fcd9541</switchConditionId>
                    <switchId>3013.cdf7afcd-e7dd-4c65-a5f6-66488f02748f</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23f8</endStateId>
                    <condition>tw.local.chargesAndCommisions[tw.local.counter].bookingCcy	  ==	  "Contract CCY"</condition>
                    <guid>636e14d1-53c5-47dc-b324-5ff2b1faa976</guid>
                    <versionId>8e6843c3-1e13-4ca4-bdc2-f650715da22f</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1af6993a-052e-4cf0-8573-019503e48fa7</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>Calculate change amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.1878f541-05b7-4402-b9ad-e2e294a56f5b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:356</guid>
            <versionId>8a906d88-7960-49bc-b27a-489612022e40</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="996" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error4</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</errorHandlerItem>
                <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.1878f541-05b7-4402-b9ad-e2e294a56f5b</subProcessId>
                <attachedProcessRef>/1.769a8281-bf53-46c6-a4c8-571fc192f312</attachedProcessRef>
                <guid>c69ebf07-a093-4f26-aeb7-096c84b6f4ef</guid>
                <versionId>8dd620ee-5980-45e5-9ab0-b9096150e03b</versionId>
                <parameterMapping name="inAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ea59ceda-5f4e-43b6-b621-2ef50f35ec5d</parameterMappingId>
                    <processParameterId>2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8</processParameterId>
                    <parameterMappingParentId>3012.1878f541-05b7-4402-b9ad-e2e294a56f5b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.nbeCollectableAmount</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cb9834b5-bd36-4d8f-a34c-b634ac9ca882</guid>
                    <versionId>1cb39da6-b354-4732-9bc6-5805f11cb79e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="percentage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.23cdbbcf-aa4f-4083-a084-6b46916b6c4e</parameterMappingId>
                    <processParameterId>2055.23a7051d-b8b1-4a36-8c49-592e7028cd89</processParameterId>
                    <parameterMappingParentId>3012.1878f541-05b7-4402-b9ad-e2e294a56f5b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.chargesList[tw.local.counter].changePercentage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>447163ca-e895-4654-a80e-71c2eac78bf9</guid>
                    <versionId>5e48fe39-cbb6-4830-98c7-e7242dd31f0b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="chargeAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9c6a12dd-9897-4a86-810b-52cb9e8b3996</parameterMappingId>
                    <processParameterId>2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648</processParameterId>
                    <parameterMappingParentId>3012.1878f541-05b7-4402-b9ad-e2e294a56f5b</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.chargesList[tw.local.counter].changeAmount</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e300e1b9-3f00-4dcc-9bf0-be3f7b38b477</guid>
                    <versionId>a369db56-3004-4697-b9be-326d7a851ad5</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5474f549-a95b-4d80-8fb7-1ba56d40d9d7</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>Get equivalent amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
            <guid>guid:aff098473ecd546d:1d42df0a:18b1700f163:-6f3a</guid>
            <versionId>9fd7ffb9-7b58-469e-9daf-7d4125cf34fb</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.df119c14-689c-4ed2-bdec-99ae63027449</processItemPrePostId>
                <processItemId>2025.5474f549-a95b-4d80-8fb7-1ba56d40d9d7</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>4b09add9-564b-4357-8857-3d24dbcdb25c</guid>
                <versionId>a4002ed3-4aca-4a34-96cc-5d4fe8412791</versionId>
            </processPrePosts>
            <layoutData x="365" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error6</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</errorHandlerItem>
                <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de</attachedProcessRef>
                <guid>8ee20045-5be7-4224-8aa0-e1b475d355ac</guid>
                <versionId>0e210255-c599-4d2f-ad02-c6605da5c26a</versionId>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3a829e13-499b-49c4-a152-f666b99fcb17</parameterMappingId>
                    <processParameterId>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>899fd7bc-98f5-47ae-a166-4a74178d6b32</guid>
                    <versionId>06927b7e-64d6-4718-89df-71547bbd1b9a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateSubType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ba402971-4c70-4e08-923d-b22890a707d7</parameterMappingId>
                    <processParameterId>2055.*************-4e4c-8a23-3a8758392285</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"S"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>bef191b8-c7d3-4f60-8942-00389e14d573</guid>
                    <versionId>4c81b861-499c-4ea8-98e3-4e34a6d81459</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2fe47d94-3866-4722-a6de-007ec5113f25</parameterMappingId>
                    <processParameterId>2055.359d19b4-5948-47d2-814d-d3bbc031e650</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8d2fdb4a-88b6-4b26-9e55-fb4edd898903</guid>
                    <versionId>52987a15-18c6-4503-b7b0-1bcea0e90919</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8af4a5d5-ae82-4d19-80a7-6a74dbbd8d27</parameterMappingId>
                    <processParameterId>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>47138444-2a9d-446e-bc18-b91c7b686cce</guid>
                    <versionId>535b4943-58f4-45ad-ac5b-4bc27451ff6e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency1">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bac4d3b1-de72-4ec7-9bd3-dc0d8efb5131</parameterMappingId>
                    <processParameterId>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.chargesAndCommisions[tw.local.counter].basisAmountCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f301ad55-d1bb-4235-a7d2-06dcb9afc330</guid>
                    <versionId>5789089a-bebc-4e14-9ce6-01320c03542b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3627d917-b0c5-4739-95b7-7b753c677cfe</parameterMappingId>
                    <processParameterId>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3c825161-90c5-4bc6-a57a-ce39cde269bd</guid>
                    <versionId>5fec5716-0c43-484e-acfa-3f1567714bcd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.91e270cb-e1c6-42cd-9674-58353d152a77</parameterMappingId>
                    <processParameterId>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>12cb658d-69fc-40a4-98bc-49f864b49c3f</guid>
                    <versionId>7159e650-41e5-4b6a-9b1c-97025fe262f3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cb6a00c4-e3c5-4e2b-9cb9-f2d193d3e9d4</parameterMappingId>
                    <processParameterId>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC creation and amendment"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>731646d7-3930-4d89-bf8a-f024d45a4b7e</guid>
                    <versionId>7b88d631-811c-4540-a733-d95bbd941b99</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e471116f-cd30-433d-babb-3e1a964d11ce</parameterMappingId>
                    <processParameterId>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>961ff95b-aac6-4418-a876-b823bfed7baf</guid>
                    <versionId>920d2c0c-f99d-4eba-9786-ac0bc20046b7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f357a52c-4413-4fb1-8e55-0f093037590e</parameterMappingId>
                    <processParameterId>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>bba95e01-82c3-4313-9404-1b6e42cbb5c7</guid>
                    <versionId>9b882131-8f55-4107-9b20-9ed54d92ed35</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency2">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3a08edf2-def1-4210-be44-86c66437c0cb</parameterMappingId>
                    <processParameterId>2055.282350b1-2727-4aa1-8118-3d5c3316136a</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestCurrency.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7c1d103d-10b3-408c-9e8a-ba842ecdeec6</guid>
                    <versionId>b6732e90-a458-464e-b06d-6dc078f4dfa3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c000cb9b-4728-4b0a-97aa-f4699cd27184</parameterMappingId>
                    <processParameterId>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"TRANSFER"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ee3f4ab2-01ff-49d3-9513-b20fd707bb0b</guid>
                    <versionId>ba5f7d51-73fe-4087-bcb4-79a6a1014aca</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a56e2299-601d-4083-ae17-9c842a0068a8</parameterMappingId>
                    <processParameterId>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6ffe1f23-583c-451d-931b-a539b4d65b36</guid>
                    <versionId>ed4b668c-69dd-4df3-9672-76498b3353df</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1b64b51a-b6fb-41d2-a62e-109b77b01e0c</parameterMappingId>
                    <processParameterId>2055.0427603a-263e-49f1-8ebc-12396435a8f9</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>String(tw.local.chargesAndCommisions[tw.local.counter].flatAmount)</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d784bcf2-07d5-499e-8a14-74913a89701d</guid>
                    <versionId>f81f5b96-a0c8-4328-b300-1501e0edfe1d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amountResult">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2fb03be1-f811-40ed-b6c7-e0a6c74ae50a</parameterMappingId>
                    <processParameterId>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</processParameterId>
                    <parameterMappingParentId>3012.4e342006-730d-46b9-b692-373f1e3e1c51</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.equivelantAmount</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>f893cf42-d3dc-4d15-83d9-08929c144c0e</guid>
                    <versionId>fe9d71d2-72a0-4a99-a941-6468d75effdf</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e6e8aafe-ac56-4c9a-8d60-83cf82b1b707</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>contract currency</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.71f8091e-3669-414c-a0f6-bf1939cbad57</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:365</guid>
            <versionId>a181b370-21e6-4e8b-be89-859353669a1d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="495" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error5</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</errorHandlerItem>
                <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.71f8091e-3669-414c-a0f6-bf1939cbad57</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();&#xD;
if(tw.local.chargesAndCommisions != null)&#xD;
{&#xD;
	for(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.chargesAndCommisions[i].ruleType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].ruleType != null &amp;&amp; tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == "charge")&#xD;
		{&#xD;
			tw.local.chargesList[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;&#xD;
			tw.local.chargesList[i].description = tw.local.chargesAndCommisions[i].description;&#xD;
			tw.local.chargesList[i].basicAmountCurrency = tw.local.requestCurrency.name;&#xD;
			tw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
&#xD;
&#xD;
			&#xD;
			tw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;&#xD;
			tw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.value = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.currency.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency.value = "";&#xD;
//			tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].rateType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].rateType != null &amp;&amp; tw.local.chargesAndCommisions[i].rateType.toLowerCase() == "flat amount")&#xD;
			{&#xD;
				&#xD;
				for(var x=0;x&lt;tw.local.currencyList.listLength;x++)&#xD;
				{&#xD;
					if(tw.local.chargesList[i].basicAmountCurrency != "" &amp;&amp; tw.local.chargesList[i].basicAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())&#xD;
					{&#xD;
						tw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;&#xD;
						tw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;&#xD;
						break;&#xD;
					}&#xD;
				}&#xD;
			&#xD;
				//tw.local.chargesList[i].flatAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
				//tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].flatAmount;//added by sg&#xD;
				//tw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
				&#xD;
				//based on the latest Charges updates as per the SRS&#xD;
				tw.local.chargesList[i].flatAmount = tw.local.equivelantAmount;&#xD;
				tw.local.chargesList[i].defaultAmount = tw.local.equivelantAmount;&#xD;
				tw.local.chargesList[i].changeAmount = tw.local.equivelantAmount;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].defaultPercentage = tw.local.chargesAndCommisions[i].rate;&#xD;
				tw.local.chargesList[i].changePercentage = tw.local.chargesList[i].defaultPercentage;&#xD;
				tw.local.chargesList[i].defaultCurrency.name = tw.local.requestCurrency.name;&#xD;
				tw.local.chargesList[i].defaultCurrency.value = tw.local.requestCurrency.value;&#xD;
			}&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].defaultWaived != "" &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived != null &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = false;		&#xD;
			}&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAccount.balance = 0.0;&#xD;
			tw.local.chargesList[i].debitedAccount.balanceSign = "";&#xD;
			tw.local.chargesList[i].debitedAccount.isOverDraft = false;&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAmount = {};&#xD;
			tw.local.chargesList[i].debitedAmount.amountInAccount = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.negotiatedExRate = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.standardExRate = 0.0;&#xD;
&#xD;
		}&#xD;
				&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.counter += 1;</script>
                <isRule>false</isRule>
                <guid>adaed469-ef5a-4df0-ac5c-482b78a03f24</guid>
                <versionId>8825756b-cbab-4a45-adbf-1d718b2fea79</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d4fc24f2-d637-4e78-82e4-cd23ce6ee208</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>rows still exits?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.4a392313-0ec9-4f9a-9e79-1dcbbaa200bf</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:373</guid>
            <versionId>b18e2326-0ebc-4d4c-9803-f4a42149eced</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1237" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.4a392313-0ec9-4f9a-9e79-1dcbbaa200bf</switchId>
                <guid>4364a142-0f3d-4a13-8d5f-2af8df945a57</guid>
                <versionId>f2527c4c-e7e6-435e-ad71-34ce73904d0c</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.a7c990fa-59ef-4ddf-8df0-e4113d966b6e</switchConditionId>
                    <switchId>3013.4a392313-0ec9-4f9a-9e79-1dcbbaa200bf</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23f9</endStateId>
                    <condition>tw.local.counter	  &lt;	  tw.local.chargesList.listLength</condition>
                    <guid>8f2f106a-4d18-4c16-8b31-1538379b8cab</guid>
                    <versionId>53b3671c-9431-4695-aff7-a16e86a51e2d</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>fixed rate?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.14140d08-cea7-4658-91b0-0b9f1d9a0346</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:31f</guid>
            <versionId>b84b117b-4c27-456d-b8b1-2fa6cd4128fd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="772" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.14140d08-cea7-4658-91b0-0b9f1d9a0346</switchId>
                <guid>f8d5e07a-6400-4217-8cd5-92131fd4d914</guid>
                <versionId>a408429c-8cf5-4afb-baae-a9e75ebd0f3f</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.34e29414-88f6-4799-a9c2-148381202181</switchConditionId>
                    <switchId>3013.14140d08-cea7-4658-91b0-0b9f1d9a0346</switchId>
                    <seq>1</seq>
                    <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23fa</endStateId>
                    <condition>tw.local.chargesAndCommisions[tw.local.counter].rateType != "" &amp;&amp; tw.local.chargesAndCommisions[tw.local.counter].rateType != null &amp;&amp; tw.local.chargesAndCommisions[tw.local.counter].rateType.toLowerCase() == "fixed rate"</condition>
                    <guid>852c7725-3175-4460-be58-931e9162d984</guid>
                    <versionId>571ae89a-7cd6-4c11-860c-61cda04f25ec</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.a7d0f164-d8ca-4111-826b-c0c52d2ba381</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</guid>
            <versionId>c6576922-7279-4d88-bb93-74251ba599c8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="419" y="368">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.a7d0f164-d8ca-4111-826b-c0c52d2ba381</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>80271559-dce2-4b4d-b1f9-616c58ac4222</guid>
                <versionId>0ba08ba3-4046-4ede-bf16-2f53552b9d99</versionId>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.074f61bc-96ba-490c-aae3-3585e34df0e5</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.a7d0f164-d8ca-4111-826b-c0c52d2ba381</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Get ODC Charges"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b611ce13-830d-4b81-9017-3d0c169a42a4</guid>
                    <versionId>c78f9479-4a90-44b7-ad2f-9486366718e8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1ec1ea52-4cfa-482d-9a6c-9b0a2e130cfe</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.a7d0f164-d8ca-4111-826b-c0c52d2ba381</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>de0a6d2d-91e5-4239-aef0-bd101ffdb305</guid>
                    <versionId>ee29816d-9f3c-424f-a179-573de5b952a0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.df80e6ca-9810-404b-b797-c90973f0bdb8</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.a7d0f164-d8ca-4111-826b-c0c52d2ba381</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>02f98c65-a6e1-407c-b6e0-03cab75b4772</guid>
                    <versionId>fddb109c-2032-4ad0-92b3-f754c483afd6</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c2bb7e5f-5830-4ee0-824d-7671c05ecc74</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>Calculate default amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.20aa1eb3-0626-4d36-86e5-91e74517c473</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:310</guid>
            <versionId>d4438d08-9b57-4b4f-ad74-1fa621a56148</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="845" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</errorHandlerItem>
                <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.20aa1eb3-0626-4d36-86e5-91e74517c473</subProcessId>
                <attachedProcessRef>/1.769a8281-bf53-46c6-a4c8-571fc192f312</attachedProcessRef>
                <guid>8c05e2bd-cba6-46eb-9144-3a45c3b946be</guid>
                <versionId>5ff14c05-e504-4ad7-992c-0bb7a61a404c</versionId>
                <parameterMapping name="chargeAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ee838f21-9620-4d18-b939-0de162a877d8</parameterMappingId>
                    <processParameterId>2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648</processParameterId>
                    <parameterMappingParentId>3012.20aa1eb3-0626-4d36-86e5-91e74517c473</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.chargesList[tw.local.counter].defaultAmount</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>02672198-ecd3-43eb-abaf-7adbacda1de1</guid>
                    <versionId>0ab0bf84-4c9a-4f60-b88a-bd25ee509aaf</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="percentage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1dc4e594-7277-4223-9d61-43116f11f1bd</parameterMappingId>
                    <processParameterId>2055.23a7051d-b8b1-4a36-8c49-592e7028cd89</processParameterId>
                    <parameterMappingParentId>3012.20aa1eb3-0626-4d36-86e5-91e74517c473</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.chargesList[tw.local.counter].defaultPercentage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>52d014cd-4dc8-4ab5-b05a-090552ec2881</guid>
                    <versionId>84df2b7e-c068-4ed4-a11c-a94702c203fc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="inAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1ddfe499-f9fb-459f-8e81-b2db5bf6b723</parameterMappingId>
                    <processParameterId>2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8</processParameterId>
                    <parameterMappingParentId>3012.20aa1eb3-0626-4d36-86e5-91e74517c473</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.nbeCollectableAmount</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>3b09acf8-f06d-48e8-8524-dfc9dd2690d5</guid>
                    <versionId>e7207bd6-7952-4b13-a477-9dbb479dff57</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>set counter</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.12ca9356-9a31-4638-9dfa-9a77b30eb4bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aff098473ecd546d:1d42df0a:18b1700f163:-53c1</guid>
            <versionId>e8ee973b-d6d8-4a9f-a767-5f8b22d48bb6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="668" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.12ca9356-9a31-4638-9dfa-9a77b30eb4bb</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.counter = 0;</script>
                <isRule>false</isRule>
                <guid>06f5a32b-bced-437a-921b-4b7909e76107</guid>
                <versionId>a825419c-289e-4b12-8f04-2c2d6d10f563</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8fc3fef3-da35-42a1-877a-ad45808ccf5b</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.7dbd42f1-925a-4e91-92e7-18dfdd877c8a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:dd9</guid>
            <versionId>f3652fd3-c3d3-4443-9404-3fdf22cfdd8e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1141" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:-3114</errorHandlerItem>
                <errorHandlerItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightBottom" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.7dbd42f1-925a-4e91-92e7-18dfdd877c8a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.counter += 1;</script>
                <isRule>false</isRule>
                <guid>3a910733-4eb5-42fb-917e-b6ffbbd1cbd3</guid>
                <versionId>24b50c24-866f-42a0-9fc7-793b9923b66d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c201527a-c604-4770-8910-c33a954b9f94</processItemId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.b97408be-f1eb-4a8b-a736-e1c5372740fb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:d03</guid>
            <versionId>f5c9c325-44d5-4053-818a-012f7429df0b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1426" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.b97408be-f1eb-4a8b-a736-e1c5372740fb</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>95e64dbd-c04f-40c3-8818-15361df19329</guid>
                <versionId>879631dc-5d3d-4025-b20b-d63f3bd09d07</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.1bf4d450-2909-4cd3-8291-f400930ec311</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get ODC charges" id="1.a9e6ba43-572e-4e44-b72a-285af67ec153" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="productCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.e0211148-8032-4b33-8419-b9bba769dba0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"OSBC"&#xD;
//"OUBC"&#xD;
"OMBC"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="event" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.974e8b27-bff7-4a03-8e24-ea4d1c97615a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"INIT"&#xD;
//"AMND"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="nbeCollectableAmount" itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" id="2055.bd37aabd-7dc4-4378-8486-caa82c3cf78a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">1200.0</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="requestCurrency" itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="false" id="2055.eff195a0-db60-4737-8f65-3217327b57ac">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.name = "EGP";
autoObject.value = "EGP";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="contractCurrency" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.6f69e2f6-2d65-4df8-8e40-b8d8f4ca1bef">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"USD"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="chargesList" itemSubjectRef="itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061" isCollection="true" id="2055.04bc68b6-4056-494c-88c6-9cd8ca9fff44" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.809fa05d-ac51-4ed4-866f-ba555d7fcbee" />
                        
                        
                        <ns16:dataOutput name="isSuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.a658075e-29bf-4268-8ecd-5be28307cfa8" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.e0211148-8032-4b33-8419-b9bba769dba0</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.974e8b27-bff7-4a03-8e24-ea4d1c97615a</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.bd37aabd-7dc4-4378-8486-caa82c3cf78a</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.eff195a0-db60-4737-8f65-3217327b57ac</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.6f69e2f6-2d65-4df8-8e40-b8d8f4ca1bef</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.04bc68b6-4056-494c-88c6-9cd8ca9fff44</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.809fa05d-ac51-4ed4-866f-ba555d7fcbee</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.a658075e-29bf-4268-8ecd-5be28307cfa8</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="69acd631-26d8-4086-875a-3eb00a34e077">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="e17619f8-3089-45b7-8950-631ac07377e2" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="1639" height="811" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>cd1cef48-2168-4c58-8301-c95ebc36cbbc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c201527a-c604-4770-8910-c33a954b9f94</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1bf4d450-2909-4cd3-8291-f400930ec311</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8fc3fef3-da35-42a1-877a-ad45808ccf5b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6dad6d61-f9c4-4838-8dab-dff3832ac22e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>51ccdd63-5578-461b-8a1e-fe3de4212156</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b5a3c5dc-bd06-405d-8a80-5464db9d20b8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>45c4b0c9-4645-4fdc-80e7-1f8679a0e7fe</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c2bb7e5f-5830-4ee0-824d-7671c05ecc74</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c39c05f0-da5c-4eae-83ad-63d4ee4d518f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1af6993a-052e-4cf0-8573-019503e48fa7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e6e8aafe-ac56-4c9a-8d60-83cf82b1b707</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d4fc24f2-d637-4e78-82e4-cd23ce6ee208</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9d33ed9b-7ba1-4b67-8ac1-f1586b4c9eb4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>60db507f-986d-4b56-894a-72a3b64c250e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0d14fd7e-ae4f-4019-8b86-2cd1e008450b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>be44fca7-988b-47d1-85ff-11c67c66be42</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e26d983d-d905-429d-82d9-1b33505b2a57</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5474f549-a95b-4d80-8fb7-1ba56d40d9d7</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>41f7cdce-775f-46b0-8858-2c3d5961920c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7f26776d-118d-4afe-8b9b-d86a81064796</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>06bacaef-64f5-46f3-80cc-3f652386932f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="cd1cef48-2168-4c58-8301-c95ebc36cbbc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.538f60bf-610e-494f-8b3e-bcb3f15e81f6</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="c201527a-c604-4770-8910-c33a954b9f94">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1426" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:aa4c986259b1691d:-23a6d209:18a11a6235c:d03</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e1c95fba-caa8-4bca-89a3-ed459c3a8dbe</ns16:incoming>
                        
                        
                        <ns16:incoming>9c275fc6-9882-4122-81d0-844ac1d7caf1</ns16:incoming>
                        
                        
                        <ns16:incoming>167cd9b1-0c96-45f4-805b-85d5cbf3b66e</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="cd1cef48-2168-4c58-8301-c95ebc36cbbc" targetRef="1bf4d450-2909-4cd3-8291-f400930ec311" name="To MW_FC Retrieve Commission and Charges" id="2027.538f60bf-610e-494f-8b3e-bcb3f15e81f6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.e059295b-f72a-4e32-a329-8d32ebe941de" isForCompensation="false" startQuantity="1" completionQuantity="1" name="MW_FC Retrieve Commission and Charges" id="1bf4d450-2909-4cd3-8291-f400930ec311">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="72" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.538f60bf-610e-494f-8b3e-bcb3f15e81f6</ns16:incoming>
                        
                        
                        <ns16:outgoing>c7fec2cc-a8ac-45f8-893c-428c96d35e2e</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.productCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d8783716-2453-46e3-8522-b1b2504092a2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.event</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.fc2b795d-a78b-4e6c-8b59-c821d346b07f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.contractCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.dd74dd40-8fa5-4359-8243-08e144b543d2</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e">tw.local.chargesAndCommisions</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="1bf4d450-2909-4cd3-8291-f400930ec311" targetRef="6dad6d61-f9c4-4838-8dab-dff3832ac22e" name="To Get currency list" id="c7fec2cc-a8ac-45f8-893c-428c96d35e2e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="8fc3fef3-da35-42a1-877a-ad45808ccf5b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1141" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>00265bad-2475-404f-867d-e00442fde081</ns16:incoming>
                        
                        
                        <ns16:outgoing>d3843721-3e59-447a-8600-a6177efeeb4d</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.counter += 1;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e" isCollection="true" name="chargesAndCommisions" id="2056.73d8f8ab-73ea-451d-82e3-5865f0396162" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="currencyList" id="2056.580ab99c-91df-4b8e-85af-88189fcdf397" />
                    
                    
                    <ns16:callActivity calledElement="1.2f93c4b5-368e-4a13-aff6-b12926260bb3" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get currency list" id="6dad6d61-f9c4-4838-8dab-dff3832ac22e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="187" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>tw.local.counter = 0;</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c7fec2cc-a8ac-45f8-893c-428c96d35e2e</ns16:incoming>
                        
                        
                        <ns16:outgoing>881072cd-cbf2-4b81-8aa7-************</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"BPM.IDC_CURRENCY"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.91436cf4-7a08-4359-8624-bbfa8c1147c0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.currencyList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6dad6d61-f9c4-4838-8dab-dff3832ac22e" targetRef="e26d983d-d905-429d-82d9-1b33505b2a57" name="To is contract CCY?" id="881072cd-cbf2-4b81-8aa7-************">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1bf4d450-2909-4cd3-8291-f400930ec311" parallelMultiple="false" name="Error" id="51ccdd63-5578-461b-8a1e-fe3de4212156">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="107" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>307779f2-1198-4c88-8c4d-561b4f13b2e5</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="8a714ee8-7ef7-4c21-8a4d-067d98271659" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="4b8775c6-6709-47a4-860f-1ff53e0ecd1e" eventImplId="dbf93067-1f96-4ff4-8962-b85a095895ff">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6dad6d61-f9c4-4838-8dab-dff3832ac22e" parallelMultiple="false" name="Error1" id="b5a3c5dc-bd06-405d-8a80-5464db9d20b8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="222" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e4722332-e9ed-4343-8f9c-2cc485804e07</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="319c5ba1-a480-489d-8530-69008e2b627f" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="824fc12f-0242-4c85-8d01-231a66087ad2" eventImplId="77620a38-8675-4d2e-8d95-4cd22621090d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8fc3fef3-da35-42a1-877a-ad45808ccf5b" parallelMultiple="false" name="Error2" id="45c4b0c9-4645-4fdc-80e7-1f8679a0e7fe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1176" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>ccc7e936-4a49-401e-83d3-34caf13b7eb1</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="c9cf9ce1-dd46-4ada-83e4-7911c6ff0110" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="1b460882-afe1-4d24-84f5-5e40e6c83bbb" eventImplId="50889845-f21f-4888-8c87-fadcedf193a6">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="51ccdd63-5578-461b-8a1e-fe3de4212156" targetRef="06bacaef-64f5-46f3-80cc-3f652386932f" name="To End Event" id="307779f2-1198-4c88-8c4d-561b4f13b2e5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="b5a3c5dc-bd06-405d-8a80-5464db9d20b8" targetRef="06bacaef-64f5-46f3-80cc-3f652386932f" name="To End Event" id="e4722332-e9ed-4343-8f9c-2cc485804e07">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="45c4b0c9-4645-4fdc-80e7-1f8679a0e7fe" targetRef="06bacaef-64f5-46f3-80cc-3f652386932f" name="To End Event" id="ccc7e936-4a49-401e-83d3-34caf13b7eb1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.769a8281-bf53-46c6-a4c8-571fc192f312" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Calculate default amount" id="c2bb7e5f-5830-4ee0-824d-7671c05ecc74">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="845" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7e1f7ec7-3617-4d4c-8553-fcfa4623d88a</ns16:incoming>
                        
                        
                        <ns16:outgoing>8f70dfda-7d85-4465-8748-1c4b0ac7ff40</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.nbeCollectableAmount</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.23a7051d-b8b1-4a36-8c49-592e7028cd89</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.chargesList[tw.local.counter].defaultPercentage</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.chargesList[tw.local.counter].defaultAmount</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="c2bb7e5f-5830-4ee0-824d-7671c05ecc74" targetRef="1af6993a-052e-4cf0-8573-019503e48fa7" name="To Calculate change amount" id="8f70dfda-7d85-4465-8748-1c4b0ac7ff40">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="9c275fc6-9882-4122-81d0-844ac1d7caf1" name="fixed rate?" id="c39c05f0-da5c-4eae-83ad-63d4ee4d518f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="772" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e6549e69-69da-497d-8a61-818b1f7f9e79</ns16:incoming>
                        
                        
                        <ns16:incoming>7b5eebe1-f537-44b9-8e27-12a033ee6a31</ns16:incoming>
                        
                        
                        <ns16:outgoing>7e1f7ec7-3617-4d4c-8553-fcfa4623d88a</ns16:outgoing>
                        
                        
                        <ns16:outgoing>9c275fc6-9882-4122-81d0-844ac1d7caf1</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="c39c05f0-da5c-4eae-83ad-63d4ee4d518f" targetRef="c2bb7e5f-5830-4ee0-824d-7671c05ecc74" name="To Calculate default amount" id="7e1f7ec7-3617-4d4c-8553-fcfa4623d88a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.chargesAndCommisions[tw.local.counter].rateType != "" &amp;&amp; tw.local.chargesAndCommisions[tw.local.counter].rateType != null &amp;&amp; tw.local.chargesAndCommisions[tw.local.counter].rateType.toLowerCase() == "fixed rate"</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c39c05f0-da5c-4eae-83ad-63d4ee4d518f" targetRef="c201527a-c604-4770-8910-c33a954b9f94" name="To End" id="9c275fc6-9882-4122-81d0-844ac1d7caf1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="946" y="21" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8fc3fef3-da35-42a1-877a-ad45808ccf5b" targetRef="d4fc24f2-d637-4e78-82e4-cd23ce6ee208" name="To rows still exits?" id="d3843721-3e59-447a-8600-a6177efeeb4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.769a8281-bf53-46c6-a4c8-571fc192f312" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Calculate change amount" id="1af6993a-052e-4cf0-8573-019503e48fa7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="996" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8f70dfda-7d85-4465-8748-1c4b0ac7ff40</ns16:incoming>
                        
                        
                        <ns16:outgoing>00265bad-2475-404f-867d-e00442fde081</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.nbeCollectableAmount</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.23a7051d-b8b1-4a36-8c49-592e7028cd89</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.chargesList[tw.local.counter].changePercentage</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.chargesList[tw.local.counter].changeAmount</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="1af6993a-052e-4cf0-8573-019503e48fa7" targetRef="8fc3fef3-da35-42a1-877a-ad45808ccf5b" name="To End" id="00265bad-2475-404f-867d-e00442fde081">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="counter" id="2056.c1af395a-a90d-4b7d-89ee-1fe20199ee62" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="contract currency" id="e6e8aafe-ac56-4c9a-8d60-83cf82b1b707">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="495" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c658f7bf-4742-4ffc-8e62-96fee3f780b5</ns16:incoming>
                        
                        
                        <ns16:outgoing>d0806dfe-c7b4-4d2b-85ae-47450f50aafa</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();&#xD;
if(tw.local.chargesAndCommisions != null)&#xD;
{&#xD;
	for(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.chargesAndCommisions[i].ruleType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].ruleType != null &amp;&amp; tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == "charge")&#xD;
		{&#xD;
			tw.local.chargesList[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;&#xD;
			tw.local.chargesList[i].description = tw.local.chargesAndCommisions[i].description;&#xD;
			tw.local.chargesList[i].basicAmountCurrency = tw.local.requestCurrency.name;&#xD;
			tw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
&#xD;
&#xD;
			&#xD;
			tw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;&#xD;
			tw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.value = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.currency.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency.value = "";&#xD;
//			tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].rateType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].rateType != null &amp;&amp; tw.local.chargesAndCommisions[i].rateType.toLowerCase() == "flat amount")&#xD;
			{&#xD;
				&#xD;
				for(var x=0;x&lt;tw.local.currencyList.listLength;x++)&#xD;
				{&#xD;
					if(tw.local.chargesList[i].basicAmountCurrency != "" &amp;&amp; tw.local.chargesList[i].basicAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())&#xD;
					{&#xD;
						tw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;&#xD;
						tw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;&#xD;
						break;&#xD;
					}&#xD;
				}&#xD;
			&#xD;
				//tw.local.chargesList[i].flatAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
				//tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].flatAmount;//added by sg&#xD;
				//tw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
				&#xD;
				//based on the latest Charges updates as per the SRS&#xD;
				tw.local.chargesList[i].flatAmount = tw.local.equivelantAmount;&#xD;
				tw.local.chargesList[i].defaultAmount = tw.local.equivelantAmount;&#xD;
				tw.local.chargesList[i].changeAmount = tw.local.equivelantAmount;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].defaultPercentage = tw.local.chargesAndCommisions[i].rate;&#xD;
				tw.local.chargesList[i].changePercentage = tw.local.chargesList[i].defaultPercentage;&#xD;
				tw.local.chargesList[i].defaultCurrency.name = tw.local.requestCurrency.name;&#xD;
				tw.local.chargesList[i].defaultCurrency.value = tw.local.requestCurrency.value;&#xD;
			}&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].defaultWaived != "" &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived != null &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = false;		&#xD;
			}&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAccount.balance = 0.0;&#xD;
			tw.local.chargesList[i].debitedAccount.balanceSign = "";&#xD;
			tw.local.chargesList[i].debitedAccount.isOverDraft = false;&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAmount = {};&#xD;
			tw.local.chargesList[i].debitedAmount.amountInAccount = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.negotiatedExRate = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.standardExRate = 0.0;&#xD;
&#xD;
		}&#xD;
				&#xD;
	}&#xD;
}&#xD;
&#xD;
tw.local.counter += 1;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e6e8aafe-ac56-4c9a-8d60-83cf82b1b707" targetRef="41f7cdce-775f-46b0-8858-2c3d5961920c" name="To Get equivalent amount" id="d0806dfe-c7b4-4d2b-85ae-47450f50aafa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="e1c95fba-caa8-4bca-89a3-ed459c3a8dbe" name="rows still exits?" id="d4fc24f2-d637-4e78-82e4-cd23ce6ee208">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1237" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d3843721-3e59-447a-8600-a6177efeeb4d</ns16:incoming>
                        
                        
                        <ns16:outgoing>e1c95fba-caa8-4bca-89a3-ed459c3a8dbe</ns16:outgoing>
                        
                        
                        <ns16:outgoing>e6549e69-69da-497d-8a61-818b1f7f9e79</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="d4fc24f2-d637-4e78-82e4-cd23ce6ee208" targetRef="c201527a-c604-4770-8910-c33a954b9f94" name="To End" id="e1c95fba-caa8-4bca-89a3-ed459c3a8dbe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d4fc24f2-d637-4e78-82e4-cd23ce6ee208" targetRef="c39c05f0-da5c-4eae-83ad-63d4ee4d518f" name="Yes" id="e6549e69-69da-497d-8a61-818b1f7f9e79">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="855" y="166" />
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.counter	  &lt;	  tw.local.chargesList.listLength</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="c2bb7e5f-5830-4ee0-824d-7671c05ecc74" parallelMultiple="false" name="Error3" id="9d33ed9b-7ba1-4b67-8ac1-f1586b4c9eb4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="880" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>9a825d2e-3e89-4a4d-86ea-fe055e6ae38c</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2da27818-ae42-45d1-865f-772b0e9e376d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="e338081e-c7f5-4cf5-80b0-6448675e2540" eventImplId="d16c136d-e116-40ae-8588-286b55544363">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="1af6993a-052e-4cf0-8573-019503e48fa7" parallelMultiple="false" name="Error4" id="60db507f-986d-4b56-894a-72a3b64c250e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1031" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c3717e77-fdf5-4feb-8411-b6d5500ea5f3</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e6d54df3-1e16-4a9d-84f0-a998a8e5d504" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="99f8c8e7-5aad-4f75-89c9-46d5e456c82e" eventImplId="0ca8aa82-6170-4d13-8cec-c0c253faee7c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="e6e8aafe-ac56-4c9a-8d60-83cf82b1b707" parallelMultiple="false" name="Error5" id="0d14fd7e-ae4f-4019-8b86-2cd1e008450b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="530" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>3515bc0f-bd1b-4596-8bc6-a32795f05466</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="5bfe66e0-ea8e-42a2-8a75-22d6a06e3121" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d1cd184d-3bac-490b-8264-885078e86502" eventImplId="f8054a85-32a1-4764-8dda-0e20afe6c517">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="0d14fd7e-ae4f-4019-8b86-2cd1e008450b" targetRef="06bacaef-64f5-46f3-80cc-3f652386932f" name="To End Event" id="3515bc0f-bd1b-4596-8bc6-a32795f05466">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="60db507f-986d-4b56-894a-72a3b64c250e" targetRef="06bacaef-64f5-46f3-80cc-3f652386932f" name="To End Event" id="c3717e77-fdf5-4feb-8411-b6d5500ea5f3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9d33ed9b-7ba1-4b67-8ac1-f1586b4c9eb4" targetRef="06bacaef-64f5-46f3-80cc-3f652386932f" name="To End Event" id="9a825d2e-3e89-4a4d-86ea-fe055e6ae38c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="charge currency" id="be44fca7-988b-47d1-85ff-11c67c66be42">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="435" y="158" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d31b071c-2fae-46b1-856e-c1b307aba01a</ns16:incoming>
                        
                        
                        <ns16:outgoing>781de947-255e-460b-8654-a6d64d19b2bc</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();&#xD;
if(tw.local.chargesAndCommisions != null)&#xD;
{&#xD;
	for(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.chargesAndCommisions[i].ruleType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].ruleType != null &amp;&amp; tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == "charge")&#xD;
		{&#xD;
			tw.local.chargesList[i] = new tw.object.ChargesAndCommissions();&#xD;
			tw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;&#xD;
			tw.local.chargesList[i].description = tw.local.chargesAndCommisions[i].description;&#xD;
			tw.local.chargesList[i].basicAmountCurrency = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
&#xD;
			&#xD;
			//tw.local.chargesList[i].defaultCurrency.name = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;&#xD;
			tw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.value = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.currency.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency.value = "";&#xD;
//			tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].rateType!= "" &amp;&amp; tw.local.chargesAndCommisions[i].rateType != null &amp;&amp; tw.local.chargesAndCommisions[i].rateType.toLowerCase() == "flat amount")&#xD;
			{&#xD;
				&#xD;
				for(var x=0;x&lt;tw.local.currencyList.listLength;x++)&#xD;
				{&#xD;
					if(tw.local.chargesAndCommisions[i].basisAmountCurrency != "" &amp;&amp; tw.local.chargesAndCommisions[i].basisAmountCurrency != null &amp;&amp; tw.local.chargesAndCommisions[i].basisAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())&#xD;
					{&#xD;
						tw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;&#xD;
						tw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;&#xD;
						break;&#xD;
					}&#xD;
				}&#xD;
			&#xD;
				tw.local.chargesList[i].flatAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
				tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].flatAmount;//added by sg&#xD;
				tw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].flatAmount;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].defaultPercentage = tw.local.chargesAndCommisions[i].rate;&#xD;
				tw.local.chargesList[i].changePercentage = tw.local.chargesList[i].defaultPercentage;&#xD;
				tw.local.chargesList[i].defaultCurrency.name = tw.local.requestCurrency.name;&#xD;
				tw.local.chargesList[i].defaultCurrency.value = tw.local.requestCurrency.value;&#xD;
			}&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].defaultWaived != "" &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived != null &amp;&amp; tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = false;		&#xD;
			}&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAccount.balance = 0.0;&#xD;
			tw.local.chargesList[i].debitedAccount.balanceSign = "";&#xD;
			tw.local.chargesList[i].debitedAccount.isOverDraft = false;&#xD;
			&#xD;
			tw.local.chargesList[i].debitedAmount = {};&#xD;
			tw.local.chargesList[i].debitedAmount.amountInAccount = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.negotiatedExRate = 0.0;&#xD;
			tw.local.chargesList[i].debitedAmount.standardExRate = 0.0;&#xD;
&#xD;
		}&#xD;
				&#xD;
	}&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="be44fca7-988b-47d1-85ff-11c67c66be42" targetRef="942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e" name="To set counter" id="781de947-255e-460b-8654-a6d64d19b2bc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="d31b071c-2fae-46b1-856e-c1b307aba01a" name="is contract CCY?" id="e26d983d-d905-429d-82d9-1b33505b2a57">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="294" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>881072cd-cbf2-4b81-8aa7-************</ns16:incoming>
                        
                        
                        <ns16:incoming>a746a64e-939c-490b-8171-ec4bd4d77cac</ns16:incoming>
                        
                        
                        <ns16:outgoing>73dcb302-8089-4946-89d5-bf95dbc8dcc5</ns16:outgoing>
                        
                        
                        <ns16:outgoing>d31b071c-2fae-46b1-856e-c1b307aba01a</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="e26d983d-d905-429d-82d9-1b33505b2a57" targetRef="5474f549-a95b-4d80-8fb7-1ba56d40d9d7" name="Yes" id="73dcb302-8089-4946-89d5-bf95dbc8dcc5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.chargesAndCommisions[tw.local.counter].bookingCcy	  ==	  "Contract CCY"</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get equivalent amount" id="5474f549-a95b-4d80-8fb7-1ba56d40d9d7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="365" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>73dcb302-8089-4946-89d5-bf95dbc8dcc5</ns16:incoming>
                        
                        
                        <ns16:outgoing>c658f7bf-4742-4ffc-8e62-96fee3f780b5</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4e4c-8a23-3a8758392285</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"S"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC creation and amendment"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"TRANSFER"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.282350b1-2727-4aa1-8118-3d5c3316136a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestCurrency.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.chargesAndCommisions[tw.local.counter].basisAmountCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0427603a-263e-49f1-8ebc-12396435a8f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">String(tw.local.chargesAndCommisions[tw.local.counter].flatAmount)</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.359d19b4-5948-47d2-814d-d3bbc031e650</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.equivelantAmount</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="e26d983d-d905-429d-82d9-1b33505b2a57" targetRef="be44fca7-988b-47d1-85ff-11c67c66be42" name="No" id="d31b071c-2fae-46b1-856e-c1b307aba01a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.chargesAndCommisions[0].bookingCcy  </ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="fbbf7115-ca38-4ed1-8397-7229f7473430" name="counter not equals length?" id="41f7cdce-775f-46b0-8858-2c3d5961920c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="589" y="76" width="32" height="32" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d0806dfe-c7b4-4d2b-85ae-47450f50aafa</ns16:incoming>
                        
                        
                        <ns16:outgoing>fbbf7115-ca38-4ed1-8397-7229f7473430</ns16:outgoing>
                        
                        
                        <ns16:outgoing>a746a64e-939c-490b-8171-ec4bd4d77cac</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="41f7cdce-775f-46b0-8858-2c3d5961920c" targetRef="942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e" name="No" id="fbbf7115-ca38-4ed1-8397-7229f7473430">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="41f7cdce-775f-46b0-8858-2c3d5961920c" targetRef="e26d983d-d905-429d-82d9-1b33505b2a57" name="Yes" id="a746a64e-939c-490b-8171-ec4bd4d77cac">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="504" y="18" />
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.counter	  &lt;	  tw.local.chargesAndCommisions.listLength</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="equivelantAmount" id="2056.f81ff863-3736-4297-82a8-255b068ae2a6" />
                    
                    
                    <ns16:sequenceFlow sourceRef="5474f549-a95b-4d80-8fb7-1ba56d40d9d7" targetRef="e6e8aafe-ac56-4c9a-8d60-83cf82b1b707" name="To contract currency" id="c658f7bf-4742-4ffc-8e62-96fee3f780b5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="set counter" id="942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="668" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>781de947-255e-460b-8654-a6d64d19b2bc</ns16:incoming>
                        
                        
                        <ns16:incoming>fbbf7115-ca38-4ed1-8397-7229f7473430</ns16:incoming>
                        
                        
                        <ns16:outgoing>7b5eebe1-f537-44b9-8e27-12a033ee6a31</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.counter = 0;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e" targetRef="c39c05f0-da5c-4eae-83ad-63d4ee4d518f" name="To fixed rate?" id="7b5eebe1-f537-44b9-8e27-12a033ee6a31">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="amount" id="2056.8e1d51a5-4b7a-4469-8d71-373032fe5849" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="5474f549-a95b-4d80-8fb7-1ba56d40d9d7" parallelMultiple="false" name="Error6" id="7f26776d-118d-4afe-8b9b-d86a81064796">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="400" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>7bc37c82-6a81-4ee3-8773-a0ccfc6107c3</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="ee323c69-2e63-4282-8925-10920e24a752" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="9eefcefd-4fbd-414a-84bb-d2eb9dfac4ed" eventImplId="8d70e568-4a18-40b5-880c-d23e11b5b107">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="7f26776d-118d-4afe-8b9b-d86a81064796" targetRef="06bacaef-64f5-46f3-80cc-3f652386932f" name="To End Event" id="7bc37c82-6a81-4ee3-8773-a0ccfc6107c3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exception Handling" id="06bacaef-64f5-46f3-80cc-3f652386932f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="419" y="368" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7bc37c82-6a81-4ee3-8773-a0ccfc6107c3</ns16:incoming>
                        
                        
                        <ns16:incoming>e4722332-e9ed-4343-8f9c-2cc485804e07</ns16:incoming>
                        
                        
                        <ns16:incoming>307779f2-1198-4c88-8c4d-561b4f13b2e5</ns16:incoming>
                        
                        
                        <ns16:incoming>9a825d2e-3e89-4a4d-86ea-fe055e6ae38c</ns16:incoming>
                        
                        
                        <ns16:incoming>c3717e77-fdf5-4feb-8411-b6d5500ea5f3</ns16:incoming>
                        
                        
                        <ns16:incoming>3515bc0f-bd1b-4596-8bc6-a32795f05466</ns16:incoming>
                        
                        
                        <ns16:incoming>ccc7e936-4a49-401e-83d3-34caf13b7eb1</ns16:incoming>
                        
                        
                        <ns16:outgoing>167cd9b1-0c96-45f4-805b-85d5cbf3b66e</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Get ODC Charges"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="06bacaef-64f5-46f3-80cc-3f652386932f" targetRef="c201527a-c604-4770-8910-c33a954b9f94" name="To End" id="167cd9b1-0c96-45f4-805b-85d5cbf3b66e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.fbbf7115-ca38-4ed1-8397-7229f7473430</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</toProcessItemId>
            <guid>c5504229-ea5d-4b9a-84f6-2df254baac1e</guid>
            <versionId>024beff3-ae43-45bb-aed2-52130f41da1f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</fromProcessItemId>
            <toProcessItemId>2025.942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.73dcb302-8089-4946-89d5-bf95dbc8dcc5</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</fromProcessItemId>
            <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23f8</endStateId>
            <toProcessItemId>2025.5474f549-a95b-4d80-8fb7-1ba56d40d9d7</toProcessItemId>
            <guid>7be88e35-d91f-48cb-9965-462ee88959e1</guid>
            <versionId>2052d024-12e0-4ad6-95f3-cb686d688cfd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</fromProcessItemId>
            <toProcessItemId>2025.5474f549-a95b-4d80-8fb7-1ba56d40d9d7</toProcessItemId>
        </link>
        <link name="To fixed rate?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7b5eebe1-f537-44b9-8e27-12a033ee6a31</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</toProcessItemId>
            <guid>26679591-3bca-47d3-8ce5-9384a61ea9dc</guid>
            <versionId>2c7db660-ce26-4f49-999f-df6540efc2a0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</fromProcessItemId>
            <toProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.00265bad-2475-404f-867d-e00442fde081</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1af6993a-052e-4cf0-8573-019503e48fa7</fromProcessItemId>
            <endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5</endStateId>
            <toProcessItemId>2025.8fc3fef3-da35-42a1-877a-ad45808ccf5b</toProcessItemId>
            <guid>9dce0113-0d4f-4def-b968-50ea0ebb7548</guid>
            <versionId>328eb7ec-31f3-4e7e-a031-fe8c596e4406</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1af6993a-052e-4cf0-8573-019503e48fa7</fromProcessItemId>
            <toProcessItemId>2025.8fc3fef3-da35-42a1-877a-ad45808ccf5b</toProcessItemId>
        </link>
        <link name="To Calculate change amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8f70dfda-7d85-4465-8748-1c4b0ac7ff40</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c2bb7e5f-5830-4ee0-824d-7671c05ecc74</fromProcessItemId>
            <endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5</endStateId>
            <toProcessItemId>2025.1af6993a-052e-4cf0-8573-019503e48fa7</toProcessItemId>
            <guid>1976e87d-ffac-4d9f-ba7a-21836e3fff50</guid>
            <versionId>377508d8-759b-42b4-8ec4-697d7ab4ae51</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c2bb7e5f-5830-4ee0-824d-7671c05ecc74</fromProcessItemId>
            <toProcessItemId>2025.1af6993a-052e-4cf0-8573-019503e48fa7</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e6549e69-69da-497d-8a61-818b1f7f9e79</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d4fc24f2-d637-4e78-82e4-cd23ce6ee208</fromProcessItemId>
            <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23f9</endStateId>
            <toProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</toProcessItemId>
            <guid>11b7c712-64d5-4cf2-8448-db1f688b6bbf</guid>
            <versionId>3848ce2d-1dd6-43ba-865d-ca6ee573c5c6</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="855" y="166" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.d4fc24f2-d637-4e78-82e4-cd23ce6ee208</fromProcessItemId>
            <toProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</toProcessItemId>
        </link>
        <link name="To rows still exits?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d3843721-3e59-447a-8600-a6177efeeb4d</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8fc3fef3-da35-42a1-877a-ad45808ccf5b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.d4fc24f2-d637-4e78-82e4-cd23ce6ee208</toProcessItemId>
            <guid>9d7a532b-b2a9-4ae8-818d-1678709d6a34</guid>
            <versionId>3a25a9bf-c35a-4341-b2ac-64fa9097b618</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8fc3fef3-da35-42a1-877a-ad45808ccf5b</fromProcessItemId>
            <toProcessItemId>2025.d4fc24f2-d637-4e78-82e4-cd23ce6ee208</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e1c95fba-caa8-4bca-89a3-ed459c3a8dbe</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d4fc24f2-d637-4e78-82e4-cd23ce6ee208</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.c201527a-c604-4770-8910-c33a954b9f94</toProcessItemId>
            <guid>769e6368-d9b3-483c-97c7-ed7cd77038ee</guid>
            <versionId>405b3e27-099d-4208-b268-dc842ac5d706</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d4fc24f2-d637-4e78-82e4-cd23ce6ee208</fromProcessItemId>
            <toProcessItemId>2025.c201527a-c604-4770-8910-c33a954b9f94</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a746a64e-939c-490b-8171-ec4bd4d77cac</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</fromProcessItemId>
            <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23f7</endStateId>
            <toProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</toProcessItemId>
            <guid>5126cbe4-2c48-4f5c-b8e2-8ccc7c47e27e</guid>
            <versionId>6c57826a-5f22-4101-895e-94f9865c85de</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="504" y="18" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</fromProcessItemId>
            <toProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</toProcessItemId>
        </link>
        <link name="To Get equivalent amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d0806dfe-c7b4-4d2b-85ae-47450f50aafa</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e6e8aafe-ac56-4c9a-8d60-83cf82b1b707</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</toProcessItemId>
            <guid>c2a1631a-cb22-457a-8c81-923a2f1ee769</guid>
            <versionId>718f015a-d0bf-4f74-895d-ff48b80032ab</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e6e8aafe-ac56-4c9a-8d60-83cf82b1b707</fromProcessItemId>
            <toProcessItemId>2025.41f7cdce-775f-46b0-8858-2c3d5961920c</toProcessItemId>
        </link>
        <link name="To Get currency list">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c7fec2cc-a8ac-45f8-893c-428c96d35e2e</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1bf4d450-2909-4cd3-8291-f400930ec311</fromProcessItemId>
            <endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20</endStateId>
            <toProcessItemId>2025.6dad6d61-f9c4-4838-8dab-dff3832ac22e</toProcessItemId>
            <guid>396f9280-8632-4116-9f43-1129268afe8b</guid>
            <versionId>9f0b6b98-1d3e-4e9e-b79c-b895868e1d35</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1bf4d450-2909-4cd3-8291-f400930ec311</fromProcessItemId>
            <toProcessItemId>2025.6dad6d61-f9c4-4838-8dab-dff3832ac22e</toProcessItemId>
        </link>
        <link name="To set counter">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.781de947-255e-460b-8654-a6d64d19b2bc</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.be44fca7-988b-47d1-85ff-11c67c66be42</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</toProcessItemId>
            <guid>80f713d7-3e28-40dd-b762-01e276e4b703</guid>
            <versionId>a0ab7bfd-635d-4c77-a68f-8cd0e706f9f9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.be44fca7-988b-47d1-85ff-11c67c66be42</fromProcessItemId>
            <toProcessItemId>2025.942bf1e6-1ce5-48cd-8975-dfa3d2f3c32e</toProcessItemId>
        </link>
        <link name="To is contract CCY?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.881072cd-cbf2-4b81-8aa7-************</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6dad6d61-f9c4-4838-8dab-dff3832ac22e</fromProcessItemId>
            <endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24</endStateId>
            <toProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</toProcessItemId>
            <guid>87885108-f803-444d-b70b-797920ba8f65</guid>
            <versionId>ca63d392-a466-45c2-add1-94927359ffdd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6dad6d61-f9c4-4838-8dab-dff3832ac22e</fromProcessItemId>
            <toProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d31b071c-2fae-46b1-856e-c1b307aba01a</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.be44fca7-988b-47d1-85ff-11c67c66be42</toProcessItemId>
            <guid>2a991691-bb2c-4e90-8e9f-78a5835ab161</guid>
            <versionId>cd7dae31-8131-4f4a-ac9d-cb0893377307</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.e26d983d-d905-429d-82d9-1b33505b2a57</fromProcessItemId>
            <toProcessItemId>2025.be44fca7-988b-47d1-85ff-11c67c66be42</toProcessItemId>
        </link>
        <link name="To contract currency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c658f7bf-4742-4ffc-8e62-96fee3f780b5</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5474f549-a95b-4d80-8fb7-1ba56d40d9d7</fromProcessItemId>
            <endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</endStateId>
            <toProcessItemId>2025.e6e8aafe-ac56-4c9a-8d60-83cf82b1b707</toProcessItemId>
            <guid>899a36fb-83c4-48e8-8b13-64877674f151</guid>
            <versionId>f5f1628a-c493-4814-b3fe-fcb1b5394fb0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5474f549-a95b-4d80-8fb7-1ba56d40d9d7</fromProcessItemId>
            <toProcessItemId>2025.e6e8aafe-ac56-4c9a-8d60-83cf82b1b707</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.167cd9b1-0c96-45f4-805b-85d5cbf3b66e</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.c201527a-c604-4770-8910-c33a954b9f94</toProcessItemId>
            <guid>c0800f4f-761f-468a-8bb8-7ccdebb84880</guid>
            <versionId>fac69a7a-4f11-4910-bb3f-2f0e689e1f5a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.06bacaef-64f5-46f3-80cc-3f652386932f</fromProcessItemId>
            <toProcessItemId>2025.c201527a-c604-4770-8910-c33a954b9f94</toProcessItemId>
        </link>
        <link name="To Calculate default amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7e1f7ec7-3617-4d4c-8553-fcfa4623d88a</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</fromProcessItemId>
            <endStateId>guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-23fa</endStateId>
            <toProcessItemId>2025.c2bb7e5f-5830-4ee0-824d-7671c05ecc74</toProcessItemId>
            <guid>8e9ceece-3b2f-4772-96bc-f1c2c14c522d</guid>
            <versionId>fd0112b6-9ba0-4879-a4c2-479aaa25c964</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</fromProcessItemId>
            <toProcessItemId>2025.c2bb7e5f-5830-4ee0-824d-7671c05ecc74</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9c275fc6-9882-4122-81d0-844ac1d7caf1</processLinkId>
            <processId>1.a9e6ba43-572e-4e44-b72a-285af67ec153</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.c201527a-c604-4770-8910-c33a954b9f94</toProcessItemId>
            <guid>fe90454f-0cec-4575-b19c-220dfdfac1b4</guid>
            <versionId>fde3475a-dc12-4089-b73d-8b440e0a4d36</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="946" y="21" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.c39c05f0-da5c-4eae-83ad-63d4ee4d518f</fromProcessItemId>
            <toProcessItemId>2025.c201527a-c604-4770-8910-c33a954b9f94</toProcessItemId>
        </link>
    </process>
</teamworks>

