{"id": "12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "versionId": "3793d607-c255-4971-80fe-9dd69b16f667", "name": "odcRoutingDetails", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "name": "odcRoutingDetails", "lastModified": "*************", "lastModifiedBy": "heba", "classId": "12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "false", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/NBEODCR\",\"complexType\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{}],\"shadow\":[false]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"hubCode\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"hubCode\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"branchCode\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"branchCode\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"initiatorUser\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"initiatorUser\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"branchName\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"branchName\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"hubName\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"hubName\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"branchSeq\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{}]}]},\"name\":\"branchSeq\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}}]},\"name\":\"odcRoutingDetails\"}],\"id\":\"_12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\"}", "description": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-13c9", "versionId": "3793d607-c255-4971-80fe-9dd69b16f667", "definition": {"property": [{"name": "hubCode", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "branchCode", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "initiatorUser", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "branchName", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "hubName", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "branchSeq", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": {"isNull": "true"}, "typeNamespace": {"isNull": "true"}, "minOccurs": {"isNull": "true"}, "maxOccurs": {"isNull": "true"}, "nillable": {"isNull": "true"}, "order": {"isNull": "true"}, "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}], "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "odcRoutingDetails", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}