<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f" name="Get BIC Codes">
        <lastModified>1700465707822</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.6963f4e2-f007-47e2-ba57-c1c37065dc29</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>151b3548-3bdc-495d-bf62-9e972864af3f</guid>
        <versionId>25d936f9-6075-44a7-8da2-d1e5f68e9901</versionId>
        <dependencySummary>&lt;dependencySummary id="2a34dc0d-ecbe-49e9-a99e-b0fe27eb7bdc" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.af5b2ab4-e26a-4f97-bca1-8c5c07be4c47</processParameterId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"06948425"&#xD;
"06955648"&#xD;
//"06316421"&#xD;
//"06948425"&#xD;
//"12345678"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f12d22b7-4497-412f-8e63-7b4cc690c91c</guid>
            <versionId>dc432a45-0274-43f4-952f-d102280f8a8d</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.632b73d8-ab6a-40c1-b3ae-90cb0d16fdaa</processParameterId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8e507d5c-8dcf-47e4-9408-4664f81a2fcf</guid>
            <versionId>af30668f-ccfa-4bae-8caf-3e6892711f82</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e5f02513-218b-4dab-828a-0f1294c5938d</processParameterId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fd2041f0-b39b-4028-88c0-e3400cb2e360</guid>
            <versionId>5fc557be-1f2d-4857-90df-576363105b16</versionId>
        </processParameter>
        <processVariable name="bicCodes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9a99f797-6a1a-49c1-ba66-325fd4ce4697</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9be402cc-**************-f719d1b76130</guid>
            <versionId>44fbad4e-072b-408c-a1fb-d421518a945e</versionId>
        </processVariable>
        <processVariable name="instanceID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0c16e863-28c4-4ea7-974a-163a7963748d</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7f2e5347-e190-426e-ba7b-8130c70b9479</guid>
            <versionId>2ed25282-7f2c-4ee2-aa6c-5823dfafc358</versionId>
        </processVariable>
        <processVariable name="prefix">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.49245ce6-d64a-4fb3-a21e-7e0063c2adf8</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4aa25d4f-7e1b-4a7e-a550-881f6f120329</guid>
            <versionId>5281945a-aed6-485b-9e68-1db58c266772</versionId>
        </processVariable>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bc204e69-08e7-41ea-ae74-740b4752f017</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5bb1dbdf-2e47-429c-898c-43dd24c302da</guid>
            <versionId>42f7631b-bfec-4b1a-8555-eb3bd292bc4f</versionId>
        </processVariable>
        <processVariable name="requestAppID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5b07d29e-0d28-43f2-a546-67b3f224bfde</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d1bafd5c-166e-435d-915f-be8d9044828d</guid>
            <versionId>6c7b8110-7867-49a0-a53e-4707db4e7018</versionId>
        </processVariable>
        <processVariable name="snapshot">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6f697020-2996-4102-bf33-a9ddab34c308</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>03a66e6a-6811-497e-8269-c216525993b4</guid>
            <versionId>26f8f386-0634-4308-9a25-6bac13715f54</versionId>
        </processVariable>
        <processVariable name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.add405e3-5daa-46df-9ab2-ee6d1d09c67e</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cda55108-674f-4ec4-893a-a3e02ad09272</guid>
            <versionId>f0cc9314-a779-45ef-a176-8c3fe00a9e5c</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.39fb5cbd-ed55-4afe-a7d6-e194726e6e44</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c34718f9-7de4-4e85-b680-68fa9cec310c</guid>
            <versionId>5f49850d-cd5c-4a02-8948-c78c9c00d727</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5b857c90-12f5-4e63-a4f0-861746eed557</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>23ed917a-**************-8a6ec96fd6c8</guid>
            <versionId>de9086f6-6aaa-4840-b05b-6dc1607e5698</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c3e76dc2-a1aa-4adb-9e43-9c538ad2aa7e</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5954fffb-c8ea-4552-8833-9e30838f6729</guid>
            <versionId>4e7705b5-6fb4-448b-bd61-3ce0750c1252</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.39d2f001-563e-4f07-aa12-05b794fd8007</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a3b5c483-5a3c-4cd6-affe-5c65e0d8fced</guid>
            <versionId>d2ac900f-2207-4882-9e85-45519044af19</versionId>
        </processVariable>
        <processVariable name="tempStr">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b446677-87ca-4ae3-af61-24321cebd0fb</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>73de0777-fa05-4e8a-b1bf-bba79278f948</guid>
            <versionId>e237b8c9-0e6d-4c16-9c46-5f9026463e5a</versionId>
        </processVariable>
        <processVariable name="customerCIF">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c4636b4c-9971-4e0f-8fe4-5e59032c3391</processVariableId>
            <description isNull="true" />
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ebd045bf-b57a-4bd8-a64c-563da3009518</guid>
            <versionId>9f4e0739-1377-403a-8161-4051f525fd08</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.199b28eb-0b72-44bb-baf4-0f0d19c827df</processItemId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <name>is Successful</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.1dbf48cc-b346-47bf-8c1b-4a8888ddfa75</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2b1528dec8afbe86:-38278192:18be81b4797:-68af</guid>
            <versionId>19cd6f54-01e9-41d5-8af3-b06a6d6a72b2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="336" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.1dbf48cc-b346-47bf-8c1b-4a8888ddfa75</switchId>
                <guid>727548ea-78b1-48b2-ac2e-b65c784f8c8c</guid>
                <versionId>37e3d006-41cc-411b-8c25-87fe0e3f2950</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.afbc2cf3-202f-4d5e-baf9-a96a5286769a</switchConditionId>
                    <switchId>3013.1dbf48cc-b346-47bf-8c1b-4a8888ddfa75</switchId>
                    <seq>1</seq>
                    <endStateId>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-7255</endStateId>
                    <condition>tw.local.isSuccessful == false &amp;&amp;tw.local.errorMSG.toLowerCase() != tw.local.tempStr.toLowerCase()	  ==	  false</condition>
                    <guid>86125070-f954-472c-aaff-0a8c2077199a</guid>
                    <versionId>3de834ab-5c33-4c4d-b5fc-4f8b99708342</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6963f4e2-f007-47e2-ba57-c1c37065dc29</processItemId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <name>Get BIC codes</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.5597e500-726c-43cd-a8e6-668a6454bb08</errorHandlerItemId>
            <guid>guid:2b1528dec8afbe86:-38278192:18be81b4797:-68b1</guid>
            <versionId>97bef004-831b-4abe-8eba-8e7c3c53327d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.52280a91-42fd-446d-9c5d-e4afa0dff7c2</processItemPrePostId>
                <processItemId>2025.6963f4e2-f007-47e2-ba57-c1c37065dc29</processItemId>
                <location>2</location>
                <script>if (!tw.local.isSuccessful) {&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
}&#xD;
tw.local.tempStr = "NO Valid Result Returned";</script>
                <guid>28657096-e6e5-439d-8307-3d2a1e2c1b62</guid>
                <versionId>9c6f11fd-c00c-49db-a668-ac77672a44cb</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.2c9687ec-e854-4c3a-9653-63257d5d8ad6</processItemPrePostId>
                <processItemId>2025.6963f4e2-f007-47e2-ba57-c1c37065dc29</processItemId>
                <location>1</location>
                <script>tw.local.customerCIF = tw.local.data.split("-")[0];&#xD;
tw.local.instanceID = tw.local.data.split("-")[1];</script>
                <guid>ed4288a2-eaf0-4469-87d2-9a0f0b9f908d</guid>
                <versionId>af8c4050-6965-4f75-92d3-82704cffe7e9</versionId>
            </processPrePosts>
            <layoutData x="152" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:2b1528dec8afbe86:-38278192:18be81b4797:-68ae</errorHandlerItem>
                <errorHandlerItemId>2025.5597e500-726c-43cd-a8e6-668a6454bb08</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62</attachedProcessRef>
                <guid>582df65d-7136-4180-a7f7-7a7353ff4b6e</guid>
                <versionId>7b926847-b7e5-4f88-a6eb-cdbbe621d0de</versionId>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.95833114-85f1-4fe4-9e94-00a16746cc99</parameterMappingId>
                    <processParameterId>2055.3dfd9149-fb93-49c0-8d6c-3bb6a31c868b</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>8d1bf4a6-2d45-4a29-9494-145294d0c54f</guid>
                    <versionId>0c3ad943-fef9-4c5e-8efe-4b0cb3658c11</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7f1f4389-0b69-475c-9335-3a9a5e12778a</parameterMappingId>
                    <processParameterId>2055.4ad7fd40-8451-480a-840d-e4350d23f2ba</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.customerCIF</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>27bb3798-8415-4b3d-87e6-693753136d95</guid>
                    <versionId>2164261e-d034-495d-93e2-ad75fae39b97</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d61598e2-fd38-421e-a174-9029f6ab6d2b</parameterMappingId>
                    <processParameterId>2055.034a46fd-24a2-4243-84ce-38fb46ffc959</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.instanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>cac66ca4-4ac8-451c-b5d5-d817de34aeaf</guid>
                    <versionId>24aade91-5fb5-47d4-9072-1016e62701a2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8916cf90-84fd-495f-8a0c-940f9c9ab48a</parameterMappingId>
                    <processParameterId>2055.*************-4d10-8b61-5e94d3ad31bd</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"INWARD DOCUMENTARY COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>505f694d-4dfe-40a3-9725-baf4d7ddf75e</guid>
                    <versionId>40666969-443c-49fb-b388-3e231cb8bf3c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9895b2e3-7fac-4ffe-b69c-e08327a1a682</parameterMappingId>
                    <processParameterId>2055.1094baed-e916-4772-8bd8-91a41441f7ad</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>dc9b2a93-ac52-41d7-b927-1203696f4a5a</guid>
                    <versionId>68e914e3-87d4-4f49-aa95-d009ae0bc10f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3ca1e526-4d31-472e-9dad-89bf3a33d61a</parameterMappingId>
                    <processParameterId>2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>02c11249-61b6-4e56-b7ca-69ba633e5f4f</guid>
                    <versionId>73a94729-d6fe-4fb1-aa93-05078662fe86</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c26b352d-2375-4303-a707-5391f718eee6</parameterMappingId>
                    <processParameterId>2055.21defa96-4307-47f8-8cb7-e0bc7234481b</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9b3d1a7f-367c-49fc-975c-07eb901aff6a</guid>
                    <versionId>80734067-dfeb-4768-9781-7308b0ef6605</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="bicCodes">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bccc0f94-9ade-4b19-98ba-ebae73ca1d99</parameterMappingId>
                    <processParameterId>2055.bb8dd17c-590d-440f-8545-364fdb26328e</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>a7254c35-391b-4afb-8620-939da25ee63e</guid>
                    <versionId>8413426c-97a5-4542-b1b1-87b936a67b0a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.df9ae53f-da25-4152-b9d6-56320ef694a3</parameterMappingId>
                    <processParameterId>2055.a5891acc-745c-47e0-825a-7e040254d8ec</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c41e42b5-be64-4983-b481-c25521f298c1</guid>
                    <versionId>94174a28-7b52-46e3-b949-d692cd04144f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e3a3b159-ff75-47ce-b980-ee1a84d43b28</parameterMappingId>
                    <processParameterId>2055.9a271b13-fe7a-4bc9-8573-99028a4ff122</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4ce818d0-3cb0-4b4c-918e-aa763fe5db16</guid>
                    <versionId>f2bb5dee-ac58-4806-9f70-8708af2f7d8a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9b7f84a6-d408-4339-b568-6e53401084aa</parameterMappingId>
                    <processParameterId>2055.390182d1-c178-4449-8178-edd289314ab2</processParameterId>
                    <parameterMappingParentId>3012.8eaaa163-5c37-4c0f-8dfa-07f32ec9bcea</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e61b9922-912f-441b-a900-e288154932fd</guid>
                    <versionId>f50cf004-3aad-4c74-a20c-80c3e2cdd7e6</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d39bf0a9-6629-4865-a2ba-619ae170ab28</processItemId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.3ab14fc4-139d-4e7b-a97f-0cf3f28647ee</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2b1528dec8afbe86:-38278192:18be81b4797:-68b0</guid>
            <versionId>a1845806-0534-409c-ba0e-a27da64b2844</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.3ab14fc4-139d-4e7b-a97f-0cf3f28647ee</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ea65e750-6ef8-4b70-a240-f4807dc174ba</guid>
                <versionId>e1906cd0-9c87-4850-91c4-c666dea081df</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5597e500-726c-43cd-a8e6-668a6454bb08</processItemId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <name>Catch Errors</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.34abee83-16a7-42ec-9693-054b37bcf356</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2b1528dec8afbe86:-38278192:18be81b4797:-68ae</guid>
            <versionId>bfadb6a3-ac21-4b47-8d67-fcd175f37df9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="321" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.34abee83-16a7-42ec-9693-054b37bcf356</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.error != null &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if ( tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;&#xD;
log.error("IDC PROCESS with ID "+tw.local.instanceID+" An Error Occured in Service Name : "+tw.system.serviceFlow.name+" with Error Message : "+tw.local.errorMSG);</script>
                <isRule>false</isRule>
                <guid>95adf658-d6f2-4041-89f8-d2be52c4d8f3</guid>
                <versionId>289d53f0-ee3f-48ee-b348-03fea39568e3</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.6963f4e2-f007-47e2-ba57-c1c37065dc29</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get BIC Codes" id="1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.af5b2ab4-e26a-4f97-bca1-8c5c07be4c47">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"06948425"&#xD;
"06955648"&#xD;
//"06316421"&#xD;
//"06948425"&#xD;
//"12345678"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.632b73d8-ab6a-40c1-b3ae-90cb0d16fdaa" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.e5f02513-218b-4dab-828a-0f1294c5938d" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="93739c47-6210-4632-a362-21da7b982d3c">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c81cd274-98c2-4435-804c-e6228412d6eb" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>44098aa5-e4d6-4fa9-a65f-6beb97d09277</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d39bf0a9-6629-4865-a2ba-619ae170ab28</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6963f4e2-f007-47e2-ba57-c1c37065dc29</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b80efac9-bab9-40ae-9dcd-ae5656f24313</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>199b28eb-0b72-44bb-baf4-0f0d19c827df</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5597e500-726c-43cd-a8e6-668a6454bb08</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="44098aa5-e4d6-4fa9-a65f-6beb97d09277">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.28fed871-8ff9-45aa-ae6c-6ab3bac6b1b1</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="d39bf0a9-6629-4865-a2ba-619ae170ab28">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:2b1528dec8afbe86:-38278192:18be81b4797:-68b0</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>19c6c489-4926-4784-a9e8-1166a82208e8</ns16:incoming>
                        
                        
                        <ns16:incoming>6ba6ee98-d50e-415c-8ea9-7610998ea855</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="44098aa5-e4d6-4fa9-a65f-6beb97d09277" targetRef="6963f4e2-f007-47e2-ba57-c1c37065dc29" name="To Get BIC codes" id="2027.28fed871-8ff9-45aa-ae6c-6ab3bac6b1b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62" name="Get BIC codes" id="6963f4e2-f007-47e2-ba57-c1c37065dc29">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="152" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>if (!tw.local.isSuccessful) {&#xD;
	tw.local.results = new tw.object.listOf.String();&#xD;
}&#xD;
tw.local.tempStr = "NO Valid Result Returned";</ns3:postAssignmentScript>
                            
                            
                            <ns3:preAssignmentScript>tw.local.customerCIF = tw.local.data.split("-")[0];&#xD;
tw.local.instanceID = tw.local.data.split("-")[1];</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.28fed871-8ff9-45aa-ae6c-6ab3bac6b1b1</ns16:incoming>
                        
                        
                        <ns16:outgoing>965b4101-f1e0-413b-afa8-c72e4e5ad659</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1094baed-e916-4772-8bd8-91a41441f7ad</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5891acc-745c-47e0-825a-7e040254d8ec</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4d10-8b61-5e94d3ad31bd</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"INWARD DOCUMENTARY COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.21defa96-4307-47f8-8cb7-e0bc7234481b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4ad7fd40-8451-480a-840d-e4350d23f2ba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.customerCIF</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.034a46fd-24a2-4243-84ce-38fb46ffc959</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.instanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.3dfd9149-fb93-49c0-8d6c-3bb6a31c868b</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.390182d1-c178-4449-8178-edd289314ab2</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.9a271b13-fe7a-4bc9-8573-99028a4ff122</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.bb8dd17c-590d-440f-8545-364fdb26328e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6963f4e2-f007-47e2-ba57-c1c37065dc29" targetRef="199b28eb-0b72-44bb-baf4-0f0d19c827df" name="To is Successful" id="965b4101-f1e0-413b-afa8-c72e4e5ad659">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="bicCodes" id="2056.9a99f797-6a1a-49c1-ba66-325fd4ce4697" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instanceID" id="2056.0c16e863-28c4-4ea7-974a-163a7963748d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="prefix" id="2056.49245ce6-d64a-4fb3-a21e-7e0063c2adf8" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.bc204e69-08e7-41ea-ae74-740b4752f017" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestAppID" id="2056.5b07d29e-0d28-43f2-a546-67b3f224bfde" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="snapshot" id="2056.6f697020-2996-4102-bf33-a9ddab34c308" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="userID" id="2056.add405e3-5daa-46df-9ab2-ee6d1d09c67e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.39fb5cbd-ed55-4afe-a7d6-e194726e6e44" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.5b857c90-12f5-4e63-a4f0-861746eed557" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.c3e76dc2-a1aa-4adb-9e43-9c538ad2aa7e" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6963f4e2-f007-47e2-ba57-c1c37065dc29" parallelMultiple="false" name="Error" id="b80efac9-bab9-40ae-9dcd-ae5656f24313">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="187" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>6df7412a-6d7e-4129-9532-bdfcaaae1a30</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="4337ecfd-b30a-4de1-b789-a4b44811f9da" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="76d555ad-2940-4f10-a6e9-72c1a6f95973" eventImplId="c9fd71fd-b2db-4fb0-88b3-4ec72bb0a903">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.39d2f001-563e-4f07-aa12-05b794fd8007" />
                    
                    
                    <ns16:exclusiveGateway default="19c6c489-4926-4784-a9e8-1166a82208e8" name="is Successful" id="199b28eb-0b72-44bb-baf4-0f0d19c827df">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="336" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>965b4101-f1e0-413b-afa8-c72e4e5ad659</ns16:incoming>
                        
                        
                        <ns16:outgoing>19c6c489-4926-4784-a9e8-1166a82208e8</ns16:outgoing>
                        
                        
                        <ns16:outgoing>2894e6cf-ab50-470d-bddf-79670d0307f4</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="199b28eb-0b72-44bb-baf4-0f0d19c827df" targetRef="d39bf0a9-6629-4865-a2ba-619ae170ab28" name="To Script Task" id="19c6c489-4926-4784-a9e8-1166a82208e8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="199b28eb-0b72-44bb-baf4-0f0d19c827df" targetRef="5597e500-726c-43cd-a8e6-668a6454bb08" name="To End Event" id="2894e6cf-ab50-470d-bddf-79670d0307f4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful == false &amp;&amp;tw.local.errorMSG.toLowerCase() != tw.local.tempStr.toLowerCase()	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="5597e500-726c-43cd-a8e6-668a6454bb08">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="321" y="182" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2894e6cf-ab50-470d-bddf-79670d0307f4</ns16:incoming>
                        
                        
                        <ns16:incoming>6df7412a-6d7e-4129-9532-bdfcaaae1a30</ns16:incoming>
                        
                        
                        <ns16:outgoing>6ba6ee98-d50e-415c-8ea9-7610998ea855</ns16:outgoing>
                        
                        
                        <ns16:script>if (tw.local.error != null &amp;&amp; !!tw.local.error.errorText) {&#xD;
	tw.local.errorMSG = tw.local.error.errorText;&#xD;
}else if ( tw.system.error != null) {&#xD;
	tw.local.errorMSG = String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}else{&#xD;
	tw.local.error = new tw.object.AjaxError();&#xD;
}&#xD;
&#xD;
tw.local.error.errorText = "Error Occured&lt;br&gt; Service Name : "+tw.system.serviceFlow.name+"&lt;br&gt; Error Message : "+tw.local.errorMSG;&#xD;
log.error("IDC PROCESS with ID "+tw.local.instanceID+" An Error Occured in Service Name : "+tw.system.serviceFlow.name+" with Error Message : "+tw.local.errorMSG);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b80efac9-bab9-40ae-9dcd-ae5656f24313" targetRef="5597e500-726c-43cd-a8e6-668a6454bb08" name="To Catch Errors" id="6df7412a-6d7e-4129-9532-bdfcaaae1a30">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="5597e500-726c-43cd-a8e6-668a6454bb08" targetRef="d39bf0a9-6629-4865-a2ba-619ae170ab28" name="To End" id="6ba6ee98-d50e-415c-8ea9-7610998ea855">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="tempStr" id="2056.9b446677-87ca-4ae3-af61-24321cebd0fb" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="customerCIF" id="2056.c4636b4c-9971-4e0f-8fe4-5e59032c3391" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Script Task">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ba924d69-e3d2-45be-a6e4-eaf6d0d53816</processLinkId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.199b28eb-0b72-44bb-baf4-0f0d19c827df</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.d39bf0a9-6629-4865-a2ba-619ae170ab28</toProcessItemId>
            <guid>22252845-f170-4400-8037-128610a70a7c</guid>
            <versionId>3ecdc0cd-557b-4faf-b6b7-2f067064c96a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.199b28eb-0b72-44bb-baf4-0f0d19c827df</fromProcessItemId>
            <toProcessItemId>2025.d39bf0a9-6629-4865-a2ba-619ae170ab28</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2100b9ed-ae05-4bed-b00c-4cb28738f1ab</processLinkId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.199b28eb-0b72-44bb-baf4-0f0d19c827df</fromProcessItemId>
            <endStateId>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-7255</endStateId>
            <toProcessItemId>2025.5597e500-726c-43cd-a8e6-668a6454bb08</toProcessItemId>
            <guid>e5f947cf-fa1f-4c6c-8103-9f210cf44601</guid>
            <versionId>731d2aa6-f028-4feb-836a-385116f9399f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.199b28eb-0b72-44bb-baf4-0f0d19c827df</fromProcessItemId>
            <toProcessItemId>2025.5597e500-726c-43cd-a8e6-668a6454bb08</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3a0bab6a-3bf1-4c5d-ace4-47454b0997f8</processLinkId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5597e500-726c-43cd-a8e6-668a6454bb08</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.d39bf0a9-6629-4865-a2ba-619ae170ab28</toProcessItemId>
            <guid>2c7a5ae7-efb6-4de9-95c0-d819057cae9e</guid>
            <versionId>92d17377-3506-4a21-ac0c-b8814d25add0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.5597e500-726c-43cd-a8e6-668a6454bb08</fromProcessItemId>
            <toProcessItemId>2025.d39bf0a9-6629-4865-a2ba-619ae170ab28</toProcessItemId>
        </link>
        <link name="To is Successful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.af4fae6e-17b0-4c55-9001-c5b0301fafa3</processLinkId>
            <processId>1.4b3af470-6f5b-4cc1-adad-eaa64ac5237f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6963f4e2-f007-47e2-ba57-c1c37065dc29</fromProcessItemId>
            <endStateId>guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a</endStateId>
            <toProcessItemId>2025.199b28eb-0b72-44bb-baf4-0f0d19c827df</toProcessItemId>
            <guid>ff9d9496-2cda-48e8-9c0a-41ac10d88303</guid>
            <versionId>aa7bc5fe-862a-4dd4-8ea3-7ce573af512f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6963f4e2-f007-47e2-ba57-c1c37065dc29</fromProcessItemId>
            <toProcessItemId>2025.199b28eb-0b72-44bb-baf4-0f0d19c827df</toProcessItemId>
        </link>
    </process>
</teamworks>

