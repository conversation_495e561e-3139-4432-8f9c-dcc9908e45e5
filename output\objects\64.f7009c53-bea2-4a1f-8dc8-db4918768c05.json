{"id": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "versionId": "122c2152-ac38-426a-a5d0-7bd7f51c5c49", "name": "Basic Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "this.valueDate = bpmext.ui.getView('/DC_Templete1/8/valueDate').setEnabled()", "bindingType": "basicDetails", "configOptions": ["parentRequestNoVis", "flexCubeContractNoVIS", "basicDetailsCVVIS", "contractStageVIS", "multiTenorDatesVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.multiTenorDatesVIS = function(){\r\r\n\tif(this.context.binding.get(\"value\").get(\"paymentTerms\").get(\"name\") == \"001\")\r\r\n\t\tthis.context.options.multiTenorDatesVIS.set(\"value\", \"None\");\r\r\n\t\t\r\r\n\telse\r\r\n\t\tthis.context.options.multiTenorDatesVIS.set(\"value\", \"Editable\");\r\r\n}\r\r\n\r\r\nthis.testVis = function(){\r\r\n\tvar valueDateView = this.valueDate;\r\r\n}"}]}, "hasDetails": true}