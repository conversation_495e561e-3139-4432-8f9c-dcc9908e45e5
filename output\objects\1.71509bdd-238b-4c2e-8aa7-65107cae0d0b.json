{"id": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "versionId": "bc883da2-9235-4dad-85f4-25817ed4b654", "name": "Start ODC Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}], "private": [{"name": "error", "hasDefault": false}, {"name": "errorMessage", "hasDefault": false}, {"name": "errorPnlVis", "hasDefault": false}, {"name": "instanceId", "hasDefault": false}, {"name": "isSuccessful", "hasDefault": false}, {"name": "taskId", "hasDefault": false}, {"name": "parentrequestTypeVIS", "hasDefault": false}, {"name": "testData", "hasDefault": false}, {"name": "routingDetails", "hasDefault": false}, {"name": "role", "hasDefault": false}, {"name": "actionConditins", "hasDefault": false}, {"name": "parentIsFound", "hasDefault": false}, {"name": "messageOFParentODC", "hasDefault": false}, {"name": "requestNature", "hasDefault": false}, {"name": "requestType", "hasDefault": false}, {"name": "customerInfo", "hasDefault": false}, {"name": "match", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "New Request Coach", "id": "2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Get customer Accounts", "id": "2025.1c64304e-e98e-438b-84a4-48e212ff86ff", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get customer info", "id": "2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Initiate ODC Process", "id": "2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Retrieve request and customer data", "id": "2025.57d2b1c1-9e2a-4263-8908-d5a175901be5", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Get ODC Initiators1", "id": "2025.511860dd-9e1f-4a02-8cd3-03a7837bb403", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Init ODC Request", "id": "2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Valid?", "id": "2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Is Successful?", "id": "2025.27ea4332-6690-4601-88e4-44f33eda9ec1", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Valid?", "id": "2025.f8a83829-62ea-4e8e-83f2-9b3378975433", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Valid?", "id": "2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Exclusive Gateway", "id": "2025.********-7a3b-4867-82bd-7917afd836ee", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "add list of accounts to BO", "id": "2025.6f1b5374-e9f7-4cd9-8652-96019a4c97eb", "script": "if(!!tw.local.odcRequest.FinancialDetailsBR){\r\r\n\r\r\n  if(!tw.local.odcRequest.FcCollections)\r\r\n      tw.local.odcRequest.FcCollections = {};\r\r\n      \r\r\n  tw.local.odcRequest.FcCollections.listOfAccounts = [];\r\r\n\t\r\r\n\tfor(var i=0;i<tw.local.odcRequest.FinancialDetailsBR.listOfAccounts.length;i++)\r\r\n\t{\r\r\n\t     \r\r\n\t\ttw.local.odcRequest.FcCollections.listOfAccounts[i] = {};\r\r\n\t\ttw.local.odcRequest.FcCollections.listOfAccounts[i]= tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[i];\r\r\n\t}\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Init Request Header", "id": "2025.a251a684-eb50-4960-8385-1975a1f4ab72", "script": "tw.local.isSuccessful=false;\r\r\ntw.local.error = {};\r\r\ntw.local.errorPnlVis= \"NONE\";\r\r\n\r\r\ntw.local.odcRequest={};\r\r\ntw.local.odcRequest.appInfo = {};\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\r\r\ntw.local.odcRequest.appInfo.branch = {};\r\r\n\r\r\n\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\n\ttw.local.odcRequest.appInfo.requestName = \"Outward Documentary Collection\";\r\r\n\ttw.local.odcRequest.appInfo.stepName =  \"Start New ODC Request\";\r\r\n\ttw.local.odcRequest.appInfo.status =\"New \";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"New \";\r\r\n\tif( tw.local.routingDetails.hubCode !=null)\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.appInfo.branch.name = \"HUB \"   + tw.local.routingDetails.hubCode;\r\r\n\t\ttw.local.odcRequest.appInfo.branch.value =  \"HUB \" + tw.local.routingDetails.hubCode;\r\r\n\t\t\r\r\n\t\ttw.local.role = \"hub\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.appInfo.branch.name  =  tw.local.routingDetails.branchName;\r\r\n\t\ttw.local.odcRequest.appInfo.branch.value =  tw.local.routingDetails.branchName;\r\r\n\t\ttw.local.role = \"branch\";\r\r\n\t}\r\r\n\t\r\r\ntw.local.actionConditins =\"new\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Validation", "id": "2025.cab3c66e-b183-4fe1-8c49-00cea1480619", "script": "tw.local.errorMessage =\"\";\r\r\nvar mandatoryTriggered = false;\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length < len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------START NEW REQUEST VALIDATION-----------------------------------------\r\r\n \r\r\nmandatory(tw.local.odcRequest.requestNature.value , \"tw.local.odcRequest.requestNature\");\r\r\nmandatory(tw.local.odcRequest.requestType.value , \"tw.local.odcRequest.requestType\");\r\r\nmandatory(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\");\r\r\nminLength(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\" , 8 , \"CIF can't be empty and must be 8 digits\" , \"CIF can't be empty and must be 8 digits\")\r\r\nmaxLength(tw.local.odcRequest.cif ,tw.local.odcRequest.cif , 8 , tw.resource.ValidationMessages.MaxLength8 ,\"CIF: \" + tw.resource.ValidationMessages.MaxLength8);\r\r\n\r\r\nif(tw.local.odcRequest.customerName == \"\" || tw.local.odcRequest.customerName == null)\r\r\n{\r\r\n\taddError(tw.local.odcRequest.customerName , \"Customer Data must be retrieved\" , \"Customer Data must be retrieved\");\r\r\n}\r\r\n \r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPnlVis =\"EDITABLE\": tw.local.errorPnlVis =\"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Validation", "id": "2025.c7059a1d-7148-45b9-8c72-dbb49859b70a", "script": "if(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest )\r\r\n{\t\r\r\n\tif(tw.local.odcRequest.parentRequestNo == null || tw.local.odcRequest.parentRequestNo == \"\"){\r\r\n\t\ttw.system.coachValidation.addValidationError(\"tw.local.odcRequest.parentRequestNo\", \"Mandatory Field\");\r\r\n\t\ttw.local.parentrequestTypeVIS= \"Editable\";\r\r\n\t}\r\r\n\telse if(tw.local.odcRequest.parentRequestNo.length !=14)\r\r\n\t{\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.odcRequest.parentRequestNo\", \"Parent Request number length is 14 digits\");\r\r\n\t}\r\r\n//\ttw.local.parentrequestTypeVIS= \"Editable\";\r\r\n\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Validate CIF", "id": "2025.5ff03e32-c758-4d31-87e6-e3a3b9b18baa", "script": "tw.local.errorMessage =\"\";\r\r\nvar mandatoryTriggered = false;\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length < len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------START NEW REQUEST VALIDATION-----------------------------------------\r\r\n \r\r\n//mandatory(tw.local.odcRequest.requestNature.value , \"tw.local.odcRequest.requestNature\");\r\r\n//mandatory(tw.local.odcRequest.requestType.value , \"tw.local.odcRequest.requestType\");\r\r\nmandatory(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\");\r\r\nminLength(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\" , 8 , \"CIF can't be empty and must be 8 digits\" , \"CIF can't be empty and must be 8 digits\")\r\r\nmaxLength(tw.local.odcRequest.cif ,tw.local.odcRequest.cif , 8 , tw.resource.ValidationMessages.MaxLength8 ,\"CIF: \" + tw.resource.ValidationMessages.MaxLength8);\r\r\n \r\r\ntw.local.errorMessage!=null ?  tw.local.errorPnlVis =\"EDITABLE\": tw.local.errorPnlVis =\"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Log", "id": "2025.adb831df-fa3a-4a39-80d3-634e6d74a0cf", "script": "console.log(\"********************************* ODC PROCESS STARTED SUCCESSFULLY **********************************************\");", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Exp Handling", "id": "2025.c8d210f8-1ebe-4a55-889d-88fd8af66479", "script": "tw.local.errorMessage =\"Can Not Retrieve Customer Data, Please Contact Administrator.\";\r\r\ntw.local.errorPnlVis=\"Editable\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Exp Handling", "id": "2025.8831f70c-3d21-41c8-8332-e16c1391865b", "script": "tw.local.errorMessage=\"Can Not Retrieve Request Data, Please Contact Administrator.\";\r\r\ntw.local.errorPnlVis=\"Editable\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Start ODC Request", "lastModified": "1700658951771", "lastModifiedBy": "heba", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.7e66d523-7bdb-481f-ab21-e5c56f7694e4", "2025.7e66d523-7bdb-481f-ab21-e5c56f7694e4"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": "/24.e7977935-dba2-437e-9671-4ec41d29e437", "exposedType": "2", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a0405b515:ff", "versionId": "bc883da2-9235-4dad-85f4-25817ed4b654", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"participantRef\":[\"24.e7977935-dba2-437e-9671-4ec41d29e437\"],\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.04dd44c6-4272-406e-8aac-2a58512438f3\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":-143,\"y\":180,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"764a39a0-e969-4dc7-8ff9-dfc1e2d6eee8\"},{\"targetRef\":\"2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Init ODC Request\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.04dd44c6-4272-406e-8aac-2a58512438f3\",\"sourceRef\":\"764a39a0-e969-4dc7-8ff9-dfc1e2d6eee8\"},{\"outgoing\":[\"2027.583eb4f6-332a-4717-8998-1ba5434caf93\",\"2027.07ff7019-86de-42d0-812f-07403c4e6137\",\"2027.3e87bb72-e5f8-4ba1-822b-a872aedeab52\"],\"incoming\":[\"2027.c0303047-c7de-4d2f-8917-5f1dd710d133\",\"2027.e57880dc-aa52-4d4d-84b3-13f7208fb67c\"],\"extensionElements\":{\"postAssignmentScript\":[\"if(tw.local.odcRequest.CustomerInfo.customerType !=\\\"\\\" && tw.local.odcRequest.CustomerInfo.customerType == \\\"C\\\")\\r\\n{\\r\\n\\ttw.local.odcRequest.CustomerInfo.customerType = \\\"Corporate\\\";\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\ttw.local.odcRequest.CustomerInfo.customerType = \\\"Individual\\\";\\r\\n}\\r\\n\\r\\ntw.local.requestNature = tw.local.odcRequest.requestNature.value;\\r\\ntw.local.requestType = tw.local.odcRequest.requestType.value;\\r\\ntw.local.customerInfo = tw.local.odcRequest.CustomerInfo;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":353,\"y\":158,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"],\"preAssignmentScript\":[]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"layoutItemId\":\"Header_View1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"884f8eeb-388b-48a5-8e78-1c3d1172eb0f\",\"optionName\":\"@label\",\"value\":\"Header View\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5518afd4-7fa9-4e77-8f1f-0deb46272fcd\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d1446490-b00d-4c4e-8e79-64de082d716c\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.55afafe5-9321-40fd-9b9a-cbbd556a8005\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"d3d803d3-a2a5-4826-8fce-30dde22474be\",\"version\":\"8550\"},{\"gridContainerLayoutItems\":[{\"gridContainerLayoutItems\":[{\"layoutItemId\":\"GridLayoutCell4\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f377ec63-03b5-4646-892b-3ff3521df2b1\",\"optionName\":\"@horizontalSpan\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":1},{\\\"deviceConfigID\\\":\\\"SmallID\\\",\\\"value\\\":12}]}\"}],\"declaredType\":\"com.ibm.bpmsdk.model.coach.GridLayoutCell\",\"id\":\"7e5faa96-f792-421c-8144-6331d3c3c06a\",\"version\":\"8550\",\"direction\":\"VERTICAL\"},{\"layoutItemId\":\"GridLayoutCell5\",\"gridCellLayoutItems\":[{\"layoutItemId\":\"Start_New_Request_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7a9c9090-ee4d-4adb-8779-df4784fd3ebb\",\"optionName\":\"@label\",\"value\":\"Start New Request CV\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d44b50cf-a6aa-45ec-821c-57414e035a75\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7f162bc6-f399-4f85-8581-0392c74f32e2\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"55a2dbae-f630-4ec9-8b2a-3ad17c6ce482\",\"optionName\":\"errorMessgae\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c3bd52e9-b91c-460d-8c62-3b711985c44d\",\"optionName\":\"parentrequestTypeVIS\",\"value\":\"\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"88a6ff2a-4b9f-4e19-8522-baacbb6778cb\",\"optionName\":\"errorPnlVis\",\"value\":\"tw.local.errorPnlVis\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8f1f2138-c95f-46ae-826f-3988f548777c\",\"optionName\":\"conditions\",\"value\":\"tw.local.conditions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"59e091f6-2919-4be2-8389-5c066e071669\",\"optionName\":\"userConditions\",\"value\":\"tw.local.actionConditins\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3c20018b-e9f6-43e5-8327-0e0de1495db7\",\"optionName\":\"role\",\"value\":\"tw.local.role\"}],\"viewUUID\":\"64.7c4993bc-f7d5-4689-96c6-89c5b908583f\",\"binding\":\"tw.local.odcRequest\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"fd9040a8-1ded-401d-8bb0-a457f9355a5e\",\"version\":\"8550\"}],\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"75f1e920-d092-4d79-865b-3aa63848783d\",\"optionName\":\"@horizontalSpan\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":10},{\\\"deviceConfigID\\\":\\\"SmallID\\\",\\\"value\\\":12}]}\"}],\"declaredType\":\"com.ibm.bpmsdk.model.coach.GridLayoutCell\",\"id\":\"4dc70e2d-4555-4ab5-872a-d29feeb509d9\",\"version\":\"8550\",\"direction\":\"VERTICAL\"},{\"layoutItemId\":\"GridLayoutCell6\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"54e064bd-04f9-4ba6-86f7-c62d8dcbdd9f\",\"optionName\":\"@horizontalSpan\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":1},{\\\"deviceConfigID\\\":\\\"SmallID\\\",\\\"value\\\":12}]}\"}],\"declaredType\":\"com.ibm.bpmsdk.model.coach.GridLayoutCell\",\"id\":\"7d3e5d66-ae94-47d7-8937-42b58317bb49\",\"version\":\"8550\",\"direction\":\"VERTICAL\"}],\"layoutItemId\":\"GridLayoutContainer5\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"eab53643-a395-4189-8bd5-6e457563ff7a\",\"optionName\":\"@horizontalSpan\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":12}]}\"}],\"declaredType\":\"com.ibm.bpmsdk.model.coach.GridLayoutContainer\",\"id\":\"e6526719-c765-4a39-82a6-7b2787a4f5e4\",\"version\":\"8550\",\"direction\":\"HORIZONTAL\"}],\"layoutItemId\":\"GridLayoutContainer4\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3f31b643-8cdf-418d-8c91-851e86b1446d\",\"optionName\":\"@horizontalSpan\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":12}]}\"}],\"declaredType\":\"com.ibm.bpmsdk.model.coach.GridLayoutContainer\",\"id\":\"211aa44e-8ccb-4d4b-871b-05f53de2d3cc\",\"version\":\"8550\",\"direction\":\"VERTICAL\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"New Request Coach\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96\"},{\"outgoing\":[\"2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf\"],\"incoming\":[\"2027.********-cc55-441d-85f9-fc7347b56e9c\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":740,\"y\":-71,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf\",\"name\":\"Get customer Accounts\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0bc98ce8-10aa-460a-8857-8a8600dafc90\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.1c64304e-e98e-438b-84a4-48e212ff86ff\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.FinancialDetailsBR.listOfAccounts\"]}}],\"sourceRef\":[\"2055.208f75bb-611b-4393-83d6-d60470c4a6a4\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.7fdee937-d555-46b6-88d2-46c40e165c27\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074\"]}],\"calledElement\":\"1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f\"},{\"outgoing\":[\"2027.********-cc55-441d-85f9-fc7347b56e9c\"],\"incoming\":[\"2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39\"],\"extensionElements\":{\"postAssignmentScript\":[\"\\r\\nif (!!tw.local.error && tw.local.error.errorText!=null)\\r\\n\\talert( tw.local.error.errorText );\\r\\n\\r\\ntw.local.odcRequest.customerName = tw.local.odcRequest.CustomerInfo.customerName;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":621,\"y\":-71,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.error={};\\r\\n\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.********-cc55-441d-85f9-fc7347b56e9c\",\"name\":\"Get customer info\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.cif\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo\"]}}],\"sourceRef\":[\"2055.373a4a6c-181b-43d5-8f0e-8b400049f72e\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.08a7970f-b648-4b7d-8d40-df8769fba145\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4\"]}],\"calledElement\":\"1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb\"},{\"startQuantity\":1,\"outgoing\":[\"2027.d8207bd6-bf8a-47c8-8663-e8035d29a909\"],\"incoming\":[\"2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf\"],\"default\":\"2027.d8207bd6-bf8a-47c8-8663-e8035d29a909\",\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":853,\"y\":-71,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"add list of accounts to BO\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.6f1b5374-e9f7-4cd9-8652-96019a4c97eb\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(!!tw.local.odcRequest.FinancialDetailsBR){\\r\\n\\r\\n  if(!tw.local.odcRequest.FcCollections)\\r\\n      tw.local.odcRequest.FcCollections = {};\\r\\n      \\r\\n  tw.local.odcRequest.FcCollections.listOfAccounts = [];\\r\\n\\t\\r\\n\\tfor(var i=0;i<tw.local.odcRequest.FinancialDetailsBR.listOfAccounts.length;i++)\\r\\n\\t{\\r\\n\\t     \\r\\n\\t\\ttw.local.odcRequest.FcCollections.listOfAccounts[i] = {};\\r\\n\\t\\ttw.local.odcRequest.FcCollections.listOfAccounts[i]= tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[i];\\r\\n\\t}\\r\\n} \\r\\n\"]}},{\"targetRef\":\"2025.6f1b5374-e9f7-4cd9-8652-96019a4c97eb\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To add list of accounts to BO\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf\",\"sourceRef\":\"2025.1c64304e-e98e-438b-84a4-48e212ff86ff\"},{\"targetRef\":\"2025.1c64304e-e98e-438b-84a4-48e212ff86ff\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get customer Accounts\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.********-cc55-441d-85f9-fc7347b56e9c\",\"sourceRef\":\"2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536\"},{\"targetRef\":\"2025.5ff03e32-c758-4d31-87e6-e3a3b9b18baa\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"e596643e-21ac-410e-87ff-7cc707dcf103\",\"coachEventPath\":\"Start_New_Request_CV1\\/retrieveCustomerBtn\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Get customer info\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.583eb4f6-332a-4717-8998-1ba5434caf93\",\"sourceRef\":\"2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96\"},{\"startQuantity\":1,\"outgoing\":[\"2027.c0303047-c7de-4d2f-8917-5f1dd710d133\"],\"incoming\":[\"2027.fa8f7885-4309-4b35-8734-0d5d280d1af8\"],\"default\":\"2027.c0303047-c7de-4d2f-8917-5f1dd710d133\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":220,\"y\":158,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Init Request Header\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.a251a684-eb50-4960-8385-1975a1f4ab72\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.isSuccessful=false;\\r\\ntw.local.error = {};\\r\\ntw.local.errorPnlVis= \\\"NONE\\\";\\r\\n\\r\\ntw.local.odcRequest={};\\r\\ntw.local.odcRequest.appInfo = {};\\r\\nvar date = new Date();\\r\\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\\/' +(date.getMonth() + 1) + '\\/' + date.getFullYear();\\r\\n\\r\\ntw.local.odcRequest.appInfo.branch = {};\\r\\n\\r\\n\\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\\\"( \\\"+ tw.system.user.name+\\\")\\\";\\r\\n\\ttw.local.odcRequest.appInfo.requestName = \\\"Outward Documentary Collection\\\";\\r\\n\\ttw.local.odcRequest.appInfo.stepName =  \\\"Start New ODC Request\\\";\\r\\n\\ttw.local.odcRequest.appInfo.status =\\\"New \\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\"New \\\";\\r\\n\\tif( tw.local.routingDetails.hubCode !=null)\\r\\n\\t{\\r\\n\\t\\ttw.local.odcRequest.appInfo.branch.name = \\\"HUB \\\"   + tw.local.routingDetails.hubCode;\\r\\n\\t\\ttw.local.odcRequest.appInfo.branch.value =  \\\"HUB \\\" + tw.local.routingDetails.hubCode;\\r\\n\\t\\t\\r\\n\\t\\ttw.local.role = \\\"hub\\\";\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\ttw.local.odcRequest.appInfo.branch.name  =  tw.local.routingDetails.branchName;\\r\\n\\t\\ttw.local.odcRequest.appInfo.branch.value =  tw.local.routingDetails.branchName;\\r\\n\\t\\ttw.local.role = \\\"branch\\\";\\r\\n\\t}\\r\\n\\t\\r\\ntw.local.actionConditins =\\\"new\\\";\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"2027.5ed17938-f539-4de9-859e-409c61a836e2\"],\"incoming\":[\"2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5\"],\"default\":\"2027.5ed17938-f539-4de9-859e-409c61a836e2\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":599,\"y\":94,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.cab3c66e-b183-4fe1-8c49-00cea1480619\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\" tw.local.errorMessage =\\\"\\\";\\r\\nvar mandatoryTriggered = false;\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- Validation Functions ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/*\\r\\n* =========================================================================================================\\r\\n*  \\r\\n* Add a coach validation error \\r\\n* \\t\\t\\r\\n* EX:\\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\\r\\n*\\r\\n* =========================================================================================================\\r\\n*\\/\\r\\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n{\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\n\\r\\n\\/*\\r\\n* =================================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is less than given length\\r\\n*\\t\\r\\n* EX:\\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =================================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field != null && field != undefined && field.length < len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\n\\/*\\r\\n* =======================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is greater than given length\\r\\n*\\t\\r\\n* EX:\\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =======================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field != null && field != undefined && field.length > len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\/*\\r\\n* ==================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the field is null 'Mandatory'\\r\\n*\\t\\r\\n* EX:\\tnotNull(tw.local.name , 'tw.local.name')\\r\\n*\\r\\n* ==================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction mandatory(field , fieldName)\\r\\n{\\r\\n\\tif (field == null || field == undefined )\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\tswitch (typeof field)\\r\\n\\t\\t{\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\r\\n\\/\\/-------------------------------------------------START NEW REQUEST VALIDATION-----------------------------------------\\r\\n \\r\\nmandatory(tw.local.odcRequest.requestNature.value , \\\"tw.local.odcRequest.requestNature\\\");\\r\\nmandatory(tw.local.odcRequest.requestType.value , \\\"tw.local.odcRequest.requestType\\\");\\r\\nmandatory(tw.local.odcRequest.cif , \\\"tw.local.odcRequest.cif\\\");\\r\\nminLength(tw.local.odcRequest.cif , \\\"tw.local.odcRequest.cif\\\" , 8 , \\\"CIF can't be empty and must be 8 digits\\\" , \\\"CIF can't be empty and must be 8 digits\\\")\\r\\nmaxLength(tw.local.odcRequest.cif ,tw.local.odcRequest.cif , 8 , tw.resource.ValidationMessages.MaxLength8 ,\\\"CIF: \\\" + tw.resource.ValidationMessages.MaxLength8);\\r\\n\\r\\nif(tw.local.odcRequest.customerName == \\\"\\\" || tw.local.odcRequest.customerName == null)\\r\\n{\\r\\n\\taddError(tw.local.odcRequest.customerName , \\\"Customer Data must be retrieved\\\" , \\\"Customer Data must be retrieved\\\");\\r\\n}\\r\\n \\r\\n\\ttw.local.errorMessage!=null ?  tw.local.errorPnlVis =\\\"EDITABLE\\\": tw.local.errorPnlVis =\\\"NONE\\\";\"]}},{\"targetRef\":\"2025.cab3c66e-b183-4fe1-8c49-00cea1480619\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Validation\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5\",\"sourceRef\":\"2025.********-7a3b-4867-82bd-7917afd836ee\"},{\"startQuantity\":1,\"outgoing\":[\"2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e\"],\"incoming\":[\"2027.07ff7019-86de-42d0-812f-07403c4e6137\"],\"default\":\"2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":593,\"y\":283,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.c7059a1d-7148-45b9-8c72-dbb49859b70a\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest )\\r\\n{\\t\\r\\n\\tif(tw.local.odcRequest.parentRequestNo == null || tw.local.odcRequest.parentRequestNo == \\\"\\\"){\\r\\n\\t\\ttw.system.coachValidation.addValidationError(\\\"tw.local.odcRequest.parentRequestNo\\\", \\\"Mandatory Field\\\");\\r\\n\\t\\ttw.local.parentrequestTypeVIS= \\\"Editable\\\";\\r\\n\\t}\\r\\n\\telse if(tw.local.odcRequest.parentRequestNo.length !=14)\\r\\n\\t{\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.odcRequest.parentRequestNo\\\", \\\"Parent Request number length is 14 digits\\\");\\r\\n\\t}\\r\\n\\/\\/\\ttw.local.parentrequestTypeVIS= \\\"Editable\\\";\\r\\n\\r\\n}\"]}},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b55e1f67-35bb-42a9-8d21-5b5b38125a41\"},{\"targetRef\":\"2025.c7059a1d-7148-45b9-8c72-dbb49859b70a\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"451e14b0-e6bf-4f3f-809f-dc6632067888\",\"coachEventPath\":\"Start_New_Request_CV1\\/retrieveRequestBtn\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightBottom\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Validation\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.07ff7019-86de-42d0-812f-07403c4e6137\",\"sourceRef\":\"2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.33b74951-3a81-4990-84c1-bb3ba8966a0d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorPnlVis\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.63e83afb-9835-499c-8299-c3b291102257\"},{\"targetRef\":\"2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5ed17938-f539-4de9-859e-409c61a836e2\",\"sourceRef\":\"2025.cab3c66e-b183-4fe1-8c49-00cea1480619\"},{\"targetRef\":\"2025.f8a83829-62ea-4e8e-83f2-9b3378975433\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e\",\"sourceRef\":\"2025.c7059a1d-7148-45b9-8c72-dbb49859b70a\"},{\"startQuantity\":1,\"outgoing\":[\"2027.24c62e6d-f2d2-41b4-852c-75662821c37f\"],\"incoming\":[\"2027.583eb4f6-332a-4717-8998-1ba5434caf93\"],\"default\":\"2027.24c62e6d-f2d2-41b4-852c-75662821c37f\",\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":420,\"y\":-71,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Validate CIF\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.5ff03e32-c758-4d31-87e6-e3a3b9b18baa\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\" tw.local.errorMessage =\\\"\\\";\\r\\nvar mandatoryTriggered = false;\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- Validation Functions ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/*\\r\\n* =========================================================================================================\\r\\n*  \\r\\n* Add a coach validation error \\r\\n* \\t\\t\\r\\n* EX:\\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\\r\\n*\\r\\n* =========================================================================================================\\r\\n*\\/\\r\\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n{\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\n\\r\\n\\/*\\r\\n* =================================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is less than given length\\r\\n*\\t\\r\\n* EX:\\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =================================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field != null && field != undefined && field.length < len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\n\\/*\\r\\n* =======================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the string length is greater than given length\\r\\n*\\t\\r\\n* EX:\\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\\r\\n*\\r\\n* =======================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field != null && field != undefined && field.length > len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\/*\\r\\n* ==================================================================================================================\\r\\n*\\r\\n* Add a coach validation error if the field is null 'Mandatory'\\r\\n*\\t\\r\\n* EX:\\tnotNull(tw.local.name , 'tw.local.name')\\r\\n*\\r\\n* ==================================================================================================================\\r\\n*\\/\\r\\n\\r\\nfunction mandatory(field , fieldName)\\r\\n{\\r\\n\\tif (field == null || field == undefined )\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\tswitch (typeof field)\\r\\n\\t\\t{\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\r\\n\\/\\/-------------------------------------------------START NEW REQUEST VALIDATION-----------------------------------------\\r\\n \\r\\n\\/\\/mandatory(tw.local.odcRequest.requestNature.value , \\\"tw.local.odcRequest.requestNature\\\");\\r\\n\\/\\/mandatory(tw.local.odcRequest.requestType.value , \\\"tw.local.odcRequest.requestType\\\");\\r\\nmandatory(tw.local.odcRequest.cif , \\\"tw.local.odcRequest.cif\\\");\\r\\nminLength(tw.local.odcRequest.cif , \\\"tw.local.odcRequest.cif\\\" , 8 , \\\"CIF can't be empty and must be 8 digits\\\" , \\\"CIF can't be empty and must be 8 digits\\\")\\r\\nmaxLength(tw.local.odcRequest.cif ,tw.local.odcRequest.cif , 8 , tw.resource.ValidationMessages.MaxLength8 ,\\\"CIF: \\\" + tw.resource.ValidationMessages.MaxLength8);\\r\\n \\r\\ntw.local.errorMessage!=null ?  tw.local.errorPnlVis =\\\"EDITABLE\\\": tw.local.errorPnlVis =\\\"NONE\\\";\"]}},{\"targetRef\":\"2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.24c62e6d-f2d2-41b4-852c-75662821c37f\",\"sourceRef\":\"2025.5ff03e32-c758-4d31-87e6-e3a3b9b18baa\"},{\"incoming\":[\"2027.d8207bd6-bf8a-47c8-8663-e8035d29a909\",\"2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":949,\"y\":-47,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.ae9d3422-ed8b-496b-855a-a5ac9c1793c9\"},{\"targetRef\":\"2025.ae9d3422-ed8b-496b-855a-a5ac9c1793c9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Stay on page\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d8207bd6-bf8a-47c8-8663-e8035d29a909\",\"sourceRef\":\"2025.6f1b5374-e9f7-4cd9-8652-96019a4c97eb\"},{\"outgoing\":[\"2027.3770419b-abc2-4c23-8f28-fa27b5f1d122\",\"2027.fdeea0f0-045f-4724-8021-655bd3da3904\"],\"incoming\":[\"2027.5ed17938-f539-4de9-859e-409c61a836e2\"],\"default\":\"2027.fdeea0f0-045f-4724-8021-655bd3da3904\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":714,\"y\":113,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff\"},{\"targetRef\":\"2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3770419b-abc2-4c23-8f28-fa27b5f1d122\",\"sourceRef\":\"2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff\"},{\"incoming\":[\"2027.fdeea0f0-045f-4724-8021-655bd3da3904\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":731,\"y\":233,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.e339591e-f753-463c-8eec-88f5fab4bcc6\"},{\"targetRef\":\"2025.e339591e-f753-463c-8eec-88f5fab4bcc6\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fdeea0f0-045f-4724-8021-655bd3da3904\",\"sourceRef\":\"2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff\"},{\"incoming\":[\"2027.c63ff4b7-1199-4175-8459-96274f810734\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1266,\"y\":118,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End 1\",\"declaredType\":\"endEvent\",\"id\":\"2025.bc42e81e-a5b4-4f64-805a-67b338e3e04f\"},{\"outgoing\":[\"2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe\"],\"incoming\":[\"2027.3770419b-abc2-4c23-8f28-fa27b5f1d122\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":879,\"y\":98,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\" tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\\r\\n tw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name;\\r\\n tw.local.odcRequest.BasicDetails.parentRequestNo = tw.local.odcRequest.parentRequestNo;\\r\\n\\r\\n \"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe\",\"name\":\"Initiate ODC Process\",\"dataInputAssociation\":[{\"targetRef\":\"2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.value\"]}}]},{\"targetRef\":\"2055.82e3268d-9082-40bf-8a48-16f0710ff117\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails\"]}}]},{\"targetRef\":\"2055.17c166a1-1274-4247-89a3-e21805af343c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.de6250d6-ea05-49bc-896c-eada027a9562\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.instanceId\"]}}],\"sourceRef\":[\"2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.taskId\"]}}],\"sourceRef\":[\"2055.743a63fc-5839-4d37-8d0e-a582fda8ee91\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.192ba003-65ef-451c-89ea-378afe19f644\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.testData\"]}}],\"sourceRef\":[\"2055.bdae3091-5a11-4be9-8bab-5c185829c1d3\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails\"]}}],\"sourceRef\":[\"2055.d6436a9f-5a30-4f42-8b03-224e2373a5a4\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.5368acb7-69e7-470f-8f37-b60b75384c47\"]}],\"calledElement\":\"1.2302fe8e-614d-43eb-91b6-2956193bc993\"},{\"targetRef\":\"2025.27ea4332-6690-4601-88e4-44f33eda9ec1\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Is Successful?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe\",\"sourceRef\":\"2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6\"},{\"startQuantity\":1,\"outgoing\":[\"2027.c63ff4b7-1199-4175-8459-96274f810734\"],\"incoming\":[\"2027.1db69037-6f65-4698-8ae1-1d626c0e547a\"],\"default\":\"2027.c63ff4b7-1199-4175-8459-96274f810734\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1133,\"y\":95,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Log\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.adb831df-fa3a-4a39-80d3-634e6d74a0cf\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"console.log(\\\"********************************* ODC PROCESS STARTED SUCCESSFULLY **********************************************\\\");\"]}},{\"targetRef\":\"2025.bc42e81e-a5b4-4f64-805a-67b338e3e04f\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c63ff4b7-1199-4175-8459-96274f810734\",\"sourceRef\":\"2025.adb831df-fa3a-4a39-80d3-634e6d74a0cf\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"instanceId\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.*************-4dc0-884e-4892e15a8920\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.bb085204-fd0e-4f4e-8c8b-48551c29f91b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"taskId\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.11e3f0c2-08d7-400a-8282-6c97dbe66b03\"},{\"outgoing\":[\"2027.1db69037-6f65-4698-8ae1-1d626c0e547a\",\"2027.5d67e7f8-dbc4-4a42-8d5e-0737f7d03a62\"],\"incoming\":[\"2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe\",\"2027.86a06a2f-7003-4159-8106-9c6fb96f9e10\"],\"default\":\"2027.1db69037-6f65-4698-8ae1-1d626c0e547a\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1022,\"y\":117,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Is Successful?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.27ea4332-6690-4601-88e4-44f33eda9ec1\"},{\"targetRef\":\"2025.adb831df-fa3a-4a39-80d3-634e6d74a0cf\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\\t  ==\\t  \"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1db69037-6f65-4698-8ae1-1d626c0e547a\",\"sourceRef\":\"2025.27ea4332-6690-4601-88e4-44f33eda9ec1\"},{\"incoming\":[\"2027.5d67e7f8-dbc4-4a42-8d5e-0737f7d03a62\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1051,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}],\"preAssignmentScript\":[\"alert(\\\"Can Not Intiate New Process, Please Contact Administrator.\\\");\\r\\nconsole.log(\\\"********************************* ODC PROCESS CAN NOT START **********************************************\\\");\"]},\"name\":\"Stay on page 2\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.2d1d2ec7-ab7a-45dd-82c8-68e9bfece5cc\"},{\"targetRef\":\"2025.2d1d2ec7-ab7a-45dd-82c8-68e9bfece5cc\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\\t  ==\\t  false\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5d67e7f8-dbc4-4a42-8d5e-0737f7d03a62\",\"sourceRef\":\"2025.27ea4332-6690-4601-88e4-44f33eda9ec1\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentrequestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c2f5f445-9eae-42f2-849f-469114746d3c\"},{\"outgoing\":[\"2027.2dfd22ad-e1e6-4169-8141-a6fa665dae6d\",\"2027.f2b4d906-4421-4932-83a6-4caacafcd6da\"],\"incoming\":[\"2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e\"],\"default\":\"2027.f2b4d906-4421-4932-83a6-4caacafcd6da\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":624,\"y\":374,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.f8a83829-62ea-4e8e-83f2-9b3378975433\"},{\"targetRef\":\"2025.57d2b1c1-9e2a-4263-8908-d5a175901be5\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"leftCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2dfd22ad-e1e6-4169-8141-a6fa665dae6d\",\"sourceRef\":\"2025.f8a83829-62ea-4e8e-83f2-9b3378975433\"},{\"targetRef\":\"2025.f3eab63c-273f-4365-81f9-d46a38fd63fc\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  !=\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f2b4d906-4421-4932-83a6-4caacafcd6da\",\"sourceRef\":\"2025.f8a83829-62ea-4e8e-83f2-9b3378975433\"},{\"outgoing\":[\"2027.04cb5201-2bb1-490a-83b3-3939d688fa9d\"],\"incoming\":[\"2027.2dfd22ad-e1e6-4169-8141-a6fa665dae6d\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"postAssignmentScript\":[\"if (tw.local.parentIsFound == false) {\\r\\n\\r\\n\\ttw.system.coachValidation.addValidationError(\\\"tw.local.odcRequest.parentRequestNo\\\", tw.local.messageOFParentODC);\\r\\n\\ttw.local.match = false;\\r\\n}\\r\\n\\r\\n  tw.local.odcRequest.requestNature.value=tw.local.requestNature;\\r\\n  tw.local.odcRequest.requestType.value = tw.local.requestType;\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":480,\"y\":406,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.04cb5201-2bb1-490a-83b3-3939d688fa9d\",\"name\":\"Retrieve request and customer data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.a6cf9e03-c07b-4bb0-81a8-c74e6c8f64da\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.CustomerInfo.cif\"]}}]},{\"targetRef\":\"2055.d9a8520f-f8b4-408f-8cab-eafb8f688056\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.67ed8128-22da-46f1-894a-ab580c23f58f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.57d2b1c1-9e2a-4263-8908-d5a175901be5\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.6448783e-44be-4081-88f8-e6d08fe0c107\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentIsFound\"]}}],\"sourceRef\":[\"2055.f7fc5ea8-49a6-40b1-8b25-250a8fdd25fa\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.messageOFParentODC\"]}}],\"sourceRef\":[\"2055.b1f93375-04e2-4a6a-8e23-a158a2320eb6\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.58299858-8daf-4093-89ff-ec58ea61013c\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.match\"]}}],\"sourceRef\":[\"2055.013d14bf-4f05-44e3-89dd-360f5ad5ceb8\"]}],\"calledElement\":\"1.58febcb0-50a3-4963-8368-32c96c00d116\"},{\"targetRef\":\"2025.f3eab63c-273f-4365-81f9-d46a38fd63fc\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightBottom\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Stay on page 3\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.04cb5201-2bb1-490a-83b3-3939d688fa9d\",\"sourceRef\":\"2025.57d2b1c1-9e2a-4263-8908-d5a175901be5\"},{\"incoming\":[\"2027.f2b4d906-4421-4932-83a6-4caacafcd6da\",\"2027.047323b2-b2be-4496-8a49-edf981e8d2f2\",\"2027.04cb5201-2bb1-490a-83b3-3939d688fa9d\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":676,\"y\":375,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 3\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.f3eab63c-273f-4365-81f9-d46a38fd63fc\"},{\"parallelMultiple\":false,\"outgoing\":[\"2027.266dbba4-42b1-47ed-8511-9923ff05bc91\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2025.4e7aa27c-54bd-4822-87e1-703a9affa393\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\"}],\"attachedToRef\":\"2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536\",\"extensionElements\":{\"default\":[\"2027.266dbba4-42b1-47ed-8511-9923ff05bc91\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":656,\"y\":-13,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"2025.55b4a008-52de-4c7d-8ba0-eef90baf6e45\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"2027.33cad873-9cec-4aa0-8a17-290acb5feefb\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2025.8eb8c012-df86-42fb-8810-d83245f739a4\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\"}],\"attachedToRef\":\"2025.1c64304e-e98e-438b-84a4-48e212ff86ff\",\"extensionElements\":{\"default\":[\"2027.33cad873-9cec-4aa0-8a17-290acb5feefb\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":775,\"y\":-13,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error 1\",\"declaredType\":\"boundaryEvent\",\"id\":\"2025.0864acdc-858d-4f64-8b57-3a4d2ce31604\",\"outputSet\":{}},{\"startQuantity\":1,\"outgoing\":[\"2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576\"],\"incoming\":[\"2027.266dbba4-42b1-47ed-8511-9923ff05bc91\",\"2027.33cad873-9cec-4aa0-8a17-290acb5feefb\"],\"default\":\"2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":739,\"y\":27,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Exp Handling\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.c8d210f8-1ebe-4a55-889d-88fd8af66479\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.errorMessage =\\\"Can Not Retrieve Customer Data, Please Contact Administrator.\\\";\\r\\ntw.local.errorPnlVis=\\\"Editable\\\";\"]}},{\"targetRef\":\"2025.c8d210f8-1ebe-4a55-889d-88fd8af66479\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exp Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.266dbba4-42b1-47ed-8511-9923ff05bc91\",\"sourceRef\":\"2025.55b4a008-52de-4c7d-8ba0-eef90baf6e45\"},{\"targetRef\":\"2025.c8d210f8-1ebe-4a55-889d-88fd8af66479\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exp Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.33cad873-9cec-4aa0-8a17-290acb5feefb\",\"sourceRef\":\"2025.0864acdc-858d-4f64-8b57-3a4d2ce31604\"},{\"targetRef\":\"2025.ae9d3422-ed8b-496b-855a-a5ac9c1793c9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Stay on page\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576\",\"sourceRef\":\"2025.c8d210f8-1ebe-4a55-889d-88fd8af66479\"},{\"parallelMultiple\":false,\"outgoing\":[\"2027.9315199b-0bda-4103-8748-a73f2c698641\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2025.b5867aa6-89f1-46b8-8750-1eb640536d5d\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\"}],\"attachedToRef\":\"2025.57d2b1c1-9e2a-4263-8908-d5a175901be5\",\"extensionElements\":{\"default\":[\"2027.9315199b-0bda-4103-8748-a73f2c698641\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":515,\"y\":464,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error 2\",\"declaredType\":\"boundaryEvent\",\"id\":\"2025.83804793-3887-45b9-86ba-debf601e5a61\",\"outputSet\":{}},{\"targetRef\":\"2025.8831f70c-3d21-41c8-8332-e16c1391865b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Client-Side Script 1\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9315199b-0bda-4103-8748-a73f2c698641\",\"sourceRef\":\"2025.83804793-3887-45b9-86ba-debf601e5a61\"},{\"startQuantity\":1,\"outgoing\":[\"2027.047323b2-b2be-4496-8a49-edf981e8d2f2\"],\"incoming\":[\"2027.9315199b-0bda-4103-8748-a73f2c698641\"],\"default\":\"2027.047323b2-b2be-4496-8a49-edf981e8d2f2\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":748,\"y\":519,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Exp Handling\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.8831f70c-3d21-41c8-8332-e16c1391865b\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.errorMessage=\\\"Can Not Retrieve Request Data, Please Contact Administrator.\\\";\\r\\ntw.local.errorPnlVis=\\\"Editable\\\";\"]}},{\"targetRef\":\"2025.f3eab63c-273f-4365-81f9-d46a38fd63fc\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Stay on page 3\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.047323b2-b2be-4496-8a49-edf981e8d2f2\",\"sourceRef\":\"2025.8831f70c-3d21-41c8-8332-e16c1391865b\"},{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"testData\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c16cf93c-6c6f-42da-8b0c-f00c5234f32a\"},{\"outgoing\":[\"2027.fa8f7885-4309-4b35-8734-0d5d280d1af8\"],\"incoming\":[\"2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":84,\"y\":158,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.error={};\\r\\ntw.local.routingDetails ={};\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.fa8f7885-4309-4b35-8734-0d5d280d1af8\",\"name\":\"Get ODC Initiators1\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e791ee53-6e73-4c61-a509-f183adf32d40\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user.fullName\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.511860dd-9e1f-4a02-8cd3-03a7837bb403\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.hubCode\"]}}],\"sourceRef\":[\"2055.a17679eb-ad20-4778-b1c1-06b7dc04228f\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.branchCode\"]}}],\"sourceRef\":[\"2055.4e8f9aa5-7f46-45cc-b0ee-47bfd2137cde\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.branchName\"]}}],\"sourceRef\":[\"2055.ea1a2239-6be8-4443-a29e-1a1d65d70dda\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.hubName\"]}}],\"sourceRef\":[\"2055.c9bdd6b8-1830-4f27-8a5f-dafc6532eb02\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.branchSeq\"]}}],\"sourceRef\":[\"2055.8b261e83-fd81-4f5c-aee4-4011dfc21d9f\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.routingDetails.initiatorUser\"]}}],\"sourceRef\":[\"2055.194ea2a4-0d33-4fd5-8ddb-eb734d149fdb\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.d0ef6455-5fd6-4815-82bc-b93bd42e3532\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.6cab4049-7518-47ae-8460-bd5b20f589c3\"]}],\"calledElement\":\"1.76de764f-7233-4b6f-8130-202e1d0049ac\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.hubCode = \\\"\\\";\\nautoObject.branchCode = \\\"\\\";\\nautoObject.initiatorUser = \\\"\\\";\\nautoObject.branchName = \\\"\\\";\\nautoObject.hubName = \\\"\\\";\\nautoObject.branchSeq = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727\",\"name\":\"routingDetails\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6aa633ed-3cc2-48ad-8f5f-e012837727c1\"},{\"targetRef\":\"2025.a251a684-eb50-4960-8385-1975a1f4ab72\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Init Request Header\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.fa8f7885-4309-4b35-8734-0d5d280d1af8\",\"sourceRef\":\"2025.511860dd-9e1f-4a02-8cd3-03a7837bb403\"},{\"targetRef\":\"2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To New Request Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.c0303047-c7de-4d2f-8917-5f1dd710d133\",\"sourceRef\":\"2025.a251a684-eb50-4960-8385-1975a1f4ab72\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"role\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.55ac1d60-8e92-42b8-8c49-a14ee97f0a37\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"actionConditins\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.39593f81-2758-44ae-8a82-012904210d53\"},{\"outgoing\":[\"2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39\",\"2027.8c4524f7-79d1-4606-8cc7-2bb22ed9d2f7\"],\"incoming\":[\"2027.24c62e6d-f2d2-41b4-852c-75662821c37f\"],\"default\":\"2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":526,\"y\":-52,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a\"},{\"targetRef\":\"2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"YES\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39\",\"sourceRef\":\"2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a\"},{\"incoming\":[\"2027.8c4524f7-79d1-4606-8cc7-2bb22ed9d2f7\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":546,\"y\":6,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 4\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.84e7c76f-c916-45ec-8118-19f96e8b4e2d\"},{\"targetRef\":\"2025.84e7c76f-c916-45ec-8118-19f96e8b4e2d\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  !=\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"NO\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8c4524f7-79d1-4606-8cc7-2bb22ed9d2f7\",\"sourceRef\":\"2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a\"},{\"parallelMultiple\":false,\"outgoing\":[\"2027.86a06a2f-7003-4159-8106-9c6fb96f9e10\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2025.53d618c7-1a55-4157-8a9b-f2260fee8e9e\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\"}],\"attachedToRef\":\"2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6\",\"extensionElements\":{\"default\":[\"2027.86a06a2f-7003-4159-8106-9c6fb96f9e10\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":914,\"y\":156,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error 3\",\"declaredType\":\"boundaryEvent\",\"id\":\"2025.8eee1217-c861-46a5-8225-7129e237bdf0\",\"outputSet\":{}},{\"targetRef\":\"2025.27ea4332-6690-4601-88e4-44f33eda9ec1\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Is Successful?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.86a06a2f-7003-4159-8106-9c6fb96f9e10\",\"sourceRef\":\"2025.8eee1217-c861-46a5-8225-7129e237bdf0\"},{\"outgoing\":[\"2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d\"],\"incoming\":[\"2027.04dd44c6-4272-406e-8aac-2a58512438f3\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":-95,\"y\":158,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"tw.local.error={};\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d\",\"name\":\"Init ODC Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.247d12a8-cf76-495c-8cd4-700f32286023\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6\"]}],\"calledElement\":\"1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e\"},{\"targetRef\":\"2025.511860dd-9e1f-4a02-8cd3-03a7837bb403\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Init\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d\",\"sourceRef\":\"2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"parentIsFound\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a7e91e91-005a-448f-8a37-381b5f34cbea\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"messageOFParentODC\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8d8ce351-eee6-4234-8cbc-b63fbf9f605b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestNature\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.0155092e-203d-49f7-8ec8-a4ff6b57262b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestType\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ba713ab7-2969-4da8-832d-47a0802687d4\"},{\"itemSubjectRef\":\"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a\",\"name\":\"customerInfo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d2ecf21b-a1ce-4213-8ff4-3199f9b301cf\"},{\"outgoing\":[\"2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5\",\"2027.e57880dc-aa52-4d4d-84b3-13f7208fb67c\"],\"incoming\":[\"2027.3e87bb72-e5f8-4ba1-822b-a872aedeab52\"],\"default\":\"2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":32,\"x\":536,\"y\":152,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}],\"preAssignmentScript\":[\"if(tw.local.match == false)\\r\\n{\\r\\ntw.system.coachValidation.addValidationError(\\\"tw.local.\\\", message)\\r\\n\\r\\n}\"]},\"name\":\"Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.********-7a3b-4867-82bd-7917afd836ee\"},{\"targetRef\":\"2025.********-7a3b-4867-82bd-7917afd836ee\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"4819d209-8f61-46d3-82a0-ed4be9a68e08\",\"coachEventPath\":\"Start_New_Request_CV1\\/createRequestBtn\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightTop\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exclusive Gateway\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3e87bb72-e5f8-4ba1-822b-a872aedeab52\",\"sourceRef\":\"2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96\"},{\"targetRef\":\"2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.match\\t  ==\\t  false\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To New Request Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e57880dc-aa52-4d4d-84b3-13f7208fb67c\",\"sourceRef\":\"2025.********-7a3b-4867-82bd-7917afd836ee\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"match\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ed604e4f-7d50-45ec-8b64-77763bb3dadb\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"99e0c3e9-330f-4951-849f-3bdc0c7fc0f7\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"ae30f597-3757-402b-8f35-9dec026008b4\",\"processType\":\"None\"}],\"exposedAs\":[\"StartableService\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Start ODC Request\",\"declaredType\":\"globalUserTask\",\"id\":\"1.71509bdd-238b-4c2e-8aa7-65107cae0d0b\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.dd46ba9a-cb92-44c1-87aa-ab62d8648d68\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.e21b51c9-84fa-4940-89fc-251e2b57048c\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.93c5c002-7ac4-4283-83ee-63b8662f9223\",\"epvProcessLinkId\":\"ee84ccbb-7aff-4875-8255-7fec04e89a38\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7\",\"epvProcessLinkId\":\"533fed92-cfb1-4607-8948-69ebf68802a0\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{}],\"outputSet\":[{}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = {};\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = {};\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = [];\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = [];\\nautoObject.FcCollections.listOfAccounts[0] = {};\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = [];\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = {};\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.Drawer = {};\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = {};\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = {};\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = {};\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = {};\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.714f799b-ef4f-429f-8681-00f13ef31a6e\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"1.d45f0bec-8983-4b01-b96c-12f8dedbee67\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.714f799b-ef4f-429f-8681-00f13ef31a6e", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "29f379a8-1d64-49b9-9dd7-57e5d0378efc", "versionId": "809c4734-8119-42fa-b257-16556fe3cbdb"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.dd46ba9a-cb92-44c1-87aa-ab62d8648d68", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4a8d5bd2-4502-4ee4-94a2-9a25f8e210df", "versionId": "dfdb55be-efa7-4860-935e-5eb098567a60"}], "processVariable": [{"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b55e1f67-35bb-42a9-8d21-5b5b38125a41", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c2dcf8a5-ed57-41cd-8721-ae84c3f19ac0", "versionId": "ffce1e31-6abd-49b6-ab2a-385bca7c4ea8"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.33b74951-3a81-4990-84c1-bb3ba8966a0d", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1876f4b2-6a1c-4629-b19b-533872ddebfc", "versionId": "9840fe21-21e0-4e05-ba49-1a5e70a626ad"}, {"name": "errorPnlVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.63e83afb-9835-499c-8299-c3b291102257", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5bf5a6fd-f9db-490c-a5aa-5a6d28b86606", "versionId": "653a324d-2b64-4d11-b13d-1031225e5482"}, {"name": "instanceId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.*************-4dc0-884e-4892e15a8920", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4d37e80d-d652-4e13-bd34-8d00c2b7650c", "versionId": "7d3af0d4-e209-4c6e-aa7a-c5077ac58497"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.bb085204-fd0e-4f4e-8c8b-48551c29f91b", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "51e8f9da-bbb0-457f-bf06-9b62beb01476", "versionId": "0514cb3e-32bc-407d-b2c6-58b9c8b730ba"}, {"name": "taskId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.11e3f0c2-08d7-400a-8282-6c97dbe66b03", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7716c2ee-dfe7-4550-8d13-1f06c5334beb", "versionId": "c4866900-3626-497d-bd09-733c90df178b"}, {"name": "parentrequestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c2f5f445-9eae-42f2-849f-469114746d3c", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "10fe1076-c9be-4f98-b853-c6a860c0e3b1", "versionId": "2e55f85c-ae47-4fbd-bb6d-e7bfd8717afe"}, {"name": "testData", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c16cf93c-6c6f-42da-8b0c-f00c5234f32a", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "57a7a910-11a0-4f9d-9da4-e259a4ea3cff", "versionId": "452fb726-1839-4f61-99fe-725297da8469"}, {"name": "routingDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6aa633ed-3cc2-48ad-8f5f-e012837727c1", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "7e0f5168-39e7-4381-9fba-d97e853a1fc5", "versionId": "dbafa54c-c4e7-4326-bc12-6bd068547d8f"}, {"name": "role", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.55ac1d60-8e92-42b8-8c49-a14ee97f0a37", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "e2232033-5479-4363-b6dd-be8fe991641d", "versionId": "370952aa-84f7-4693-b1e1-91c43d8c5997"}, {"name": "actionConditins", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.39593f81-2758-44ae-8a82-012904210d53", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "8315886a-e9f3-4e62-a8c7-3a0e475d1f7d", "versionId": "e6bd67db-fd01-4510-8739-e143f029ac64"}, {"name": "parentIsFound", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a7e91e91-005a-448f-8a37-381b5f34cbea", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "12", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5a93f888-a0d4-44b7-9659-897a74816de9", "versionId": "f8afe623-ad54-466c-b032-090b8f03cd17"}, {"name": "messageOFParentODC", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8d8ce351-eee6-4234-8cbc-b63fbf9f605b", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "13", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5b62c0f4-c6b1-415e-afa2-57770902cebc", "versionId": "71dc91cc-669f-4bfd-be54-66eca9bb9030"}, {"name": "requestNature", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0155092e-203d-49f7-8ec8-a4ff6b57262b", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "14", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fbf90fe5-84bf-44fa-b35b-4deaaa1f005d", "versionId": "d3ed1e7d-fd4d-4ef2-932d-4799dd2df4bf"}, {"name": "requestType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ba713ab7-2969-4da8-832d-47a0802687d4", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "15", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9abbc6ec-ac28-4c22-857b-94daf12a9b30", "versionId": "5cbdc55a-8bc1-4035-98f6-1fd285988987"}, {"name": "customerInfo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d2ecf21b-a1ce-4213-8ff4-3199f9b301cf", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "16", "isArrayOf": "false", "isTransient": "false", "classId": "/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "08e0c1d9-8d2d-4d89-b3be-7c8a51345631", "versionId": "1c0671d0-af40-4ad0-9c25-f41ad7180502"}, {"name": "match", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ed604e4f-7d50-45ec-8b64-77763bb3dadb", "description": {"isNull": "true"}, "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "namespace": "2", "seq": "17", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "05e1988a-a608-46a5-9a4f-4939be9a30c8", "versionId": "90f51199-df5d-413f-a54d-f299ea68cb38"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.57d2b1c1-9e2a-4263-8908-d5a175901be5", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Retrieve request and customer data", "tWComponentName": "SubProcess", "tWComponentId": "3012.5cc7fd8f-74e6-4e3e-beaf-c863bf3eaa8a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a9fddf040a0e47ab:-5b64a0cf:18a6eb46484:7e7a", "versionId": "22d5abe8-e25a-4a0a-ad16-7c99a8c8c52f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5cc7fd8f-74e6-4e3e-beaf-c863bf3eaa8a", "attachedProcessRef": "/1.58febcb0-50a3-4963-8368-32c96c00d116", "guid": "0bb7830b-d56c-41f8-b2d1-b2789362e85b", "versionId": "eb14a9fc-7e59-4563-ba3f-82a6d7a4a6a0"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.511860dd-9e1f-4a02-8cd3-03a7837bb403", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Get ODC Initiators1", "tWComponentName": "SubProcess", "tWComponentId": "3012.1a08e996-6e7a-4a0b-9766-dd6ba583a0ee", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a9fddf040a0e47ab:-5b64a0cf:18a7e1fab29:-70d7", "versionId": "3fda8de5-820f-4ce9-9cce-cec6ebc99cbe", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.1a08e996-6e7a-4a0b-9766-dd6ba583a0ee", "attachedProcessRef": "/1.76de764f-7233-4b6f-8130-202e1d0049ac", "guid": "5ec996f9-c8aa-493a-9ff0-6850fd5fb031", "versionId": "cfdf7943-f9ed-4af1-a347-0b1be9df1528"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Initiate ODC Process", "tWComponentName": "SubProcess", "tWComponentId": "3012.5928389d-0e02-4b67-8bf9-cc37d67c4355", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76cd", "versionId": "417d9176-0b89-48fa-aea7-1cc28c418742", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5928389d-0e02-4b67-8bf9-cc37d67c4355", "attachedProcessRef": "/1.2302fe8e-614d-43eb-91b6-2956193bc993", "guid": "84c2f4ef-d310-4530-8685-d9f54dd04d15", "versionId": "c6b1297a-6524-4771-8ffb-ecaeb4cf9cca"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ce1e4337-d25d-4259-9c75-6c6c95dfe359", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.f99bf59b-637e-4e87-863c-64a594eb0e95", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a0405b515:100", "versionId": "51653a6b-5ba6-4007-a3a6-00a473405e20", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.f99bf59b-637e-4e87-863c-64a594eb0e95", "haltProcess": "false", "guid": "42cf3284-6aaf-4223-a5ef-decd1e5cdf01", "versionId": "77065635-43c7-4843-a206-e78185e8dfe4"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Init ODC Request", "tWComponentName": "SubProcess", "tWComponentId": "3012.9807e62f-0db1-414c-b4c2-aa52bc233023", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac6fe7417:-4e6b", "versionId": "563dbb40-93eb-45e3-8d6d-b46534aba6a1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.9807e62f-0db1-414c-b4c2-aa52bc233023", "attachedProcessRef": "/1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "guid": "22ba07a6-bb38-4182-9f72-844e81cb4386", "versionId": "e2c66d5d-8663-41c8-9adc-3443d4bc9080"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1c64304e-e98e-438b-84a4-48e212ff86ff", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Get customer Accounts", "tWComponentName": "SubProcess", "tWComponentId": "3012.577833d1-373a-46e6-a5de-68af221c7648", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a0405b515:110", "versionId": "684c1d96-6e20-4c06-9358-c9bc30beba4a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.577833d1-373a-46e6-a5de-68af221c7648", "attachedProcessRef": "/1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f", "guid": "91389945-8dd9-40e3-a67a-3fc09cdd7502", "versionId": "11371bd7-4d35-49f4-853b-d07e2a1431cf"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7e66d523-7bdb-481f-ab21-e5c56f7694e4", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.4a3547ad-50f0-420d-be73-e39dfdb50fa7", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a0405b515:101", "versionId": "cc94e8c5-7837-41dc-bdbe-19141ea6ab7e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "name": "Get customer info", "tWComponentName": "SubProcess", "tWComponentId": "3012.249e4554-bd74-45e7-a75a-617e55016642", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c400b33e5f41dfd9:-18f49999:18a0405b515:111", "versionId": "ce8d6206-31fb-4fd5-9e35-4b59f8b01720", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.249e4554-bd74-45e7-a75a-617e55016642", "attachedProcessRef": "/1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "guid": "43114cd3-bdf4-47bc-ae61-0154dc66fd40", "versionId": "82b6787a-6a54-45ad-897c-74251a85e8e2"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.16fcc6f2-91aa-444e-9cf0-3c9d6b444b54", "epvId": "/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "guid": "6a606102-aaf7-4caf-b160-9d7902f554d5", "versionId": "4e967fe9-3173-4274-86dc-62277bb89d39"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.bbca8863-92f9-47cd-800d-85c7fd245649", "epvId": "/21.93c5c002-7ac4-4283-83ee-63b8662f9223", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "guid": "82f2aa63-82d6-45e0-af8e-9890b0f445bb", "versionId": "bbaf332d-4575-4e92-965a-a6be2da6affe"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.050b1bfc-7723-4d47-b642-ee7004b43866", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "guid": "fa830515-9fb5-4352-b98f-2f9cda1299e9", "versionId": "3a2e5e43-d7f4-4c48-8fe6-cf8d59a77cbd"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "1.d45f0bec-8983-4b01-b96c-12f8dedbee67", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:globalUserTask": {"implementation": "##unspecified", "name": "Start ODC Request", "id": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:userTaskImplementation": {"processType": "None", "isClosed": "false", "id": "ae30f597-3757-402b-8f35-9dec026008b4", "ns16:startEvent": {"name": "Start", "id": "764a39a0-e969-4dc7-8ff9-dfc1e2d6eee8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "-143", "y": "180", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.04dd44c6-4272-406e-8aac-2a58512438f3"}, "ns16:sequenceFlow": [{"sourceRef": "764a39a0-e969-4dc7-8ff9-dfc1e2d6eee8", "targetRef": "2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7", "name": "To Init ODC Request", "id": "2027.04dd44c6-4272-406e-8aac-2a58512438f3", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.1c64304e-e98e-438b-84a4-48e212ff86ff", "targetRef": "2025.6f1b5374-e9f7-4cd9-8652-96019a4c97eb", "name": "To add list of accounts to BO", "id": "2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536", "targetRef": "2025.1c64304e-e98e-438b-84a4-48e212ff86ff", "name": "To Get customer Accounts", "id": "2027.********-cc55-441d-85f9-fc7347b56e9c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96", "targetRef": "2025.5ff03e32-c758-4d31-87e6-e3a3b9b18baa", "name": "To Get customer info", "id": "2027.583eb4f6-332a-4717-8998-1ba5434caf93", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "e596643e-21ac-410e-87ff-7cc707dcf103", "ns3:coachEventPath": "Start_New_Request_CV1/retrieveCustomerBtn"}}}, {"sourceRef": "2025.********-7a3b-4867-82bd-7917afd836ee", "targetRef": "2025.cab3c66e-b183-4fe1-8c49-00cea1480619", "name": "To Validation", "id": "2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96", "targetRef": "2025.c7059a1d-7148-45b9-8c72-dbb49859b70a", "name": "To Validation", "id": "2027.07ff7019-86de-42d0-812f-07403c4e6137", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightBottom", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "451e14b0-e6bf-4f3f-809f-dc6632067888", "ns3:coachEventPath": "Start_New_Request_CV1/retrieveRequestBtn"}}}, {"sourceRef": "2025.cab3c66e-b183-4fe1-8c49-00cea1480619", "targetRef": "2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff", "name": "To Valid?", "id": "2027.5ed17938-f539-4de9-859e-409c61a836e2", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.c7059a1d-7148-45b9-8c72-dbb49859b70a", "targetRef": "2025.f8a83829-62ea-4e8e-83f2-9b3378975433", "name": "To Valid?", "id": "2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.5ff03e32-c758-4d31-87e6-e3a3b9b18baa", "targetRef": "2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a", "name": "To Valid?", "id": "2027.24c62e6d-f2d2-41b4-852c-75662821c37f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.6f1b5374-e9f7-4cd9-8652-96019a4c97eb", "targetRef": "2025.ae9d3422-ed8b-496b-855a-a5ac9c1793c9", "name": "To Stay on page", "id": "2027.d8207bd6-bf8a-47c8-8663-e8035d29a909", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff", "targetRef": "2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6", "name": "Yes", "id": "2027.3770419b-abc2-4c23-8f28-fa27b5f1d122", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff", "targetRef": "2025.e339591e-f753-463c-8eec-88f5fab4bcc6", "name": "No", "id": "2027.fdeea0f0-045f-4724-8021-655bd3da3904", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6", "targetRef": "2025.27ea4332-6690-4601-88e4-44f33eda9ec1", "name": "To Is Successful?", "id": "2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.adb831df-fa3a-4a39-80d3-634e6d74a0cf", "targetRef": "2025.bc42e81e-a5b4-4f64-805a-67b338e3e04f", "name": "Yes", "id": "2027.c63ff4b7-1199-4175-8459-96274f810734", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.27ea4332-6690-4601-88e4-44f33eda9ec1", "targetRef": "2025.adb831df-fa3a-4a39-80d3-634e6d74a0cf", "name": "Yes", "id": "2027.1db69037-6f65-4698-8ae1-1d626c0e547a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.isSuccessful\t  ==\t  ", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.27ea4332-6690-4601-88e4-44f33eda9ec1", "targetRef": "2025.2d1d2ec7-ab7a-45dd-82c8-68e9bfece5cc", "name": "No", "id": "2027.5d67e7f8-dbc4-4a42-8d5e-0737f7d03a62", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.isSuccessful\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.f8a83829-62ea-4e8e-83f2-9b3378975433", "targetRef": "2025.57d2b1c1-9e2a-4263-8908-d5a175901be5", "name": "Yes", "id": "2027.2dfd22ad-e1e6-4169-8141-a6fa665dae6d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "leftCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.f8a83829-62ea-4e8e-83f2-9b3378975433", "targetRef": "2025.f3eab63c-273f-4365-81f9-d46a38fd63fc", "name": "No", "id": "2027.f2b4d906-4421-4932-83a6-4caacafcd6da", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  !=\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.57d2b1c1-9e2a-4263-8908-d5a175901be5", "targetRef": "2025.f3eab63c-273f-4365-81f9-d46a38fd63fc", "name": "To Stay on page 3", "id": "2027.04cb5201-2bb1-490a-83b3-3939d688fa9d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightBottom", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.55b4a008-52de-4c7d-8ba0-eef90baf6e45", "targetRef": "2025.c8d210f8-1ebe-4a55-889d-88fd8af66479", "name": "To Exp Handling", "id": "2027.266dbba4-42b1-47ed-8511-9923ff05bc91", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.0864acdc-858d-4f64-8b57-3a4d2ce31604", "targetRef": "2025.c8d210f8-1ebe-4a55-889d-88fd8af66479", "name": "To Exp Handling", "id": "2027.33cad873-9cec-4aa0-8a17-290acb5feefb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c8d210f8-1ebe-4a55-889d-88fd8af66479", "targetRef": "2025.ae9d3422-ed8b-496b-855a-a5ac9c1793c9", "name": "To Stay on page", "id": "2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.83804793-3887-45b9-86ba-debf601e5a61", "targetRef": "2025.8831f70c-3d21-41c8-8332-e16c1391865b", "name": "To Client-Side Script 1", "id": "2027.9315199b-0bda-4103-8748-a73f2c698641", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.8831f70c-3d21-41c8-8332-e16c1391865b", "targetRef": "2025.f3eab63c-273f-4365-81f9-d46a38fd63fc", "name": "To Stay on page 3", "id": "2027.047323b2-b2be-4496-8a49-edf981e8d2f2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.511860dd-9e1f-4a02-8cd3-03a7837bb403", "targetRef": "2025.a251a684-eb50-4960-8385-1975a1f4ab72", "name": "To Init Request Header", "id": "2027.fa8f7885-4309-4b35-8734-0d5d280d1af8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.a251a684-eb50-4960-8385-1975a1f4ab72", "targetRef": "2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96", "name": "To New Request Coach", "id": "2027.c0303047-c7de-4d2f-8917-5f1dd710d133", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a", "targetRef": "2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536", "name": "YES", "id": "2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a", "targetRef": "2025.84e7c76f-c916-45ec-8118-19f96e8b4e2d", "name": "NO", "id": "2027.8c4524f7-79d1-4606-8cc7-2bb22ed9d2f7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  !=\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.8eee1217-c861-46a5-8225-7129e237bdf0", "targetRef": "2025.27ea4332-6690-4601-88e4-44f33eda9ec1", "name": "To Is Successful?", "id": "2027.86a06a2f-7003-4159-8106-9c6fb96f9e10", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7", "targetRef": "2025.511860dd-9e1f-4a02-8cd3-03a7837bb403", "name": "To Init", "id": "2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96", "targetRef": "2025.********-7a3b-4867-82bd-7917afd836ee", "name": "To Exclusive Gateway", "id": "2027.3e87bb72-e5f8-4ba1-822b-a872aedeab52", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightTop", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "4819d209-8f61-46d3-82a0-ed4be9a68e08", "ns3:coachEventPath": "Start_New_Request_CV1/createRequestBtn"}}}, {"sourceRef": "2025.********-7a3b-4867-82bd-7917afd836ee", "targetRef": "2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96", "name": "To New Request Coach", "id": "2027.e57880dc-aa52-4d4d-84b3-13f7208fb67c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topRight", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.match\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}], "ns3:formTask": {"isHeritageCoach": "false", "cachePage": "false", "commonLayoutArea": "0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "New Request Coach", "id": "2025.6881d7a5-341d-4fb4-81c3-3dba7e8f2f96", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "353", "y": "158", "width": "95", "height": "70"}, "ns3:validationStayOnPagePaths": "okbutton", "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": "if(tw.local.odcRequest.CustomerInfo.customerType !=\"\" && tw.local.odcRequest.CustomerInfo.customerType == \"C\")\r\r\n{\r\r\n\ttw.local.odcRequest.CustomerInfo.customerType = \"Corporate\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.odcRequest.CustomerInfo.customerType = \"Individual\";\r\r\n}\r\r\n\r\r\ntw.local.requestNature = tw.local.odcRequest.requestNature.value;\r\r\ntw.local.requestType = tw.local.odcRequest.requestType.value;\r\r\ntw.local.customerInfo = tw.local.odcRequest.CustomerInfo;"}, "ns16:incoming": ["2027.c0303047-c7de-4d2f-8917-5f1dd710d133", "2027.e57880dc-aa52-4d4d-84b3-13f7208fb67c"], "ns16:outgoing": ["2027.583eb4f6-332a-4717-8998-1ba5434caf93", "2027.07ff7019-86de-42d0-812f-07403c4e6137", "2027.3e87bb72-e5f8-4ba1-822b-a872aedeab52"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": [{"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "d3d803d3-a2a5-4826-8fce-30dde22474be", "ns19:layoutItemId": "Header_View1", "ns19:configData": [{"ns19:id": "884f8eeb-388b-48a5-8e78-1c3d1172eb0f", "ns19:optionName": "@label", "ns19:value": "Header <PERSON>"}, {"ns19:id": "5518afd4-7fa9-4e77-8f1f-0deb46272fcd", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "d1446490-b00d-4c4e-8e79-64de082d716c", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.55afafe5-9321-40fd-9b9a-cbbd556a8005", "ns19:binding": "tw.local.odcRequest.appInfo"}, {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:G<PERSON><PERSON><PERSON>outContainer", "version": "8550", "ns19:id": "211aa44e-8ccb-4d4b-871b-05f53de2d3cc", "ns19:layoutItemId": "GridLayoutContainer4", "ns19:configData": {"ns19:id": "3f31b643-8cdf-418d-8c91-851e86b1446d", "ns19:optionName": "@horizontalSpan", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":12}]}"}, "ns19:direction": "VERTICAL", "ns19:gridContainerLayoutItems": {"xsi:type": "ns19:G<PERSON><PERSON><PERSON>outContainer", "version": "8550", "ns19:id": "e6526719-c765-4a39-82a6-7b2787a4f5e4", "ns19:layoutItemId": "GridLayoutContainer5", "ns19:configData": {"ns19:id": "eab53643-a395-4189-8bd5-6e457563ff7a", "ns19:optionName": "@horizontalSpan", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":12}]}"}, "ns19:direction": "HORIZONTAL", "ns19:gridContainerLayoutItems": [{"xsi:type": "ns19:G<PERSON><PERSON>ayoutCell", "version": "8550", "ns19:id": "7e5faa96-f792-421c-8144-6331d3c3c06a", "ns19:layoutItemId": "GridLayoutCell4", "ns19:configData": {"ns19:id": "f377ec63-03b5-4646-892b-3ff3521df2b1", "ns19:optionName": "@horizontalSpan", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":1},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}"}, "ns19:direction": "VERTICAL"}, {"xsi:type": "ns19:G<PERSON><PERSON>ayoutCell", "version": "8550", "ns19:id": "4dc70e2d-4555-4ab5-872a-d29feeb509d9", "ns19:layoutItemId": "GridLayoutCell5", "ns19:configData": {"ns19:id": "75f1e920-d092-4d79-865b-3aa63848783d", "ns19:optionName": "@horizontalSpan", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":10},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}"}, "ns19:direction": "VERTICAL", "ns19:gridCellLayoutItems": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "fd9040a8-1ded-401d-8bb0-a457f9355a5e", "ns19:layoutItemId": "Start_New_Request_CV1", "ns19:configData": [{"ns19:id": "7a9c9090-ee4d-4adb-8779-df4784fd3ebb", "ns19:optionName": "@label", "ns19:value": "Start New Request CV"}, {"ns19:id": "d44b50cf-a6aa-45ec-821c-57414e035a75", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "7f162bc6-f399-4f85-8581-0392c74f32e2", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "55a2dbae-f630-4ec9-8b2a-3ad17c6ce482", "ns19:optionName": "errorMessgae", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "c3bd52e9-b91c-460d-8c62-3b711985c44d", "ns19:optionName": "parentrequestTypeVIS", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "88a6ff2a-4b9f-4e19-8522-baacbb6778cb", "ns19:optionName": "errorPnlVis", "ns19:value": "tw.local.errorPnlVis", "ns19:valueType": "dynamic"}, {"ns19:id": "8f1f2138-c95f-46ae-826f-3988f548777c", "ns19:optionName": "conditions", "ns19:value": "tw.local.conditions", "ns19:valueType": "dynamic"}, {"ns19:id": "59e091f6-2919-4be2-8389-5c066e071669", "ns19:optionName": "userConditions", "ns19:value": "tw.local.actionConditins", "ns19:valueType": "dynamic"}, {"ns19:id": "3c20018b-e9f6-43e5-8327-0e0de1495db7", "ns19:optionName": "role", "ns19:value": "tw.local.role", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.7c4993bc-f7d5-4689-96c6-89c5b908583f", "ns19:binding": "tw.local.odcRequest"}}, {"xsi:type": "ns19:G<PERSON><PERSON>ayoutCell", "version": "8550", "ns19:id": "7d3e5d66-ae94-47d7-8937-42b58317bb49", "ns19:layoutItemId": "GridLayoutCell6", "ns19:configData": {"ns19:id": "54e064bd-04f9-4ba6-86f7-c62d8dcbdd9f", "ns19:optionName": "@horizontalSpan", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":1},{\"deviceConfigID\":\"SmallID\",\"value\":12}]}"}, "ns19:direction": "VERTICAL"}]}}]}}}}, "ns16:callActivity": [{"calledElement": "1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf", "name": "Get customer Accounts", "id": "2025.1c64304e-e98e-438b-84a4-48e212ff86ff", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "740", "y": "-71", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.********-cc55-441d-85f9-fc7347b56e9c", "ns16:outgoing": "2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0bc98ce8-10aa-460a-8857-8a8600dafc90", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.208f75bb-611b-4393-83d6-d60470c4a6a4", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.FinancialDetailsBR.listOfAccounts", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.d2e5a15a-ea53-4793-9e93-29af5bd80b13"}}}, {"ns16:sourceRef": "2055.7fdee937-d555-46b6-88d2-46c40e165c27", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, {"calledElement": "1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.********-cc55-441d-85f9-fc7347b56e9c", "name": "Get customer info", "id": "2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "621", "y": "-71", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.error={};\r\r\n", "ns3:postAssignmentScript": "\r\r\nif (!!tw.local.error && tw.local.error.errorText!=null)\r\r\n\talert( tw.local.error.errorText );\r\r\n\r\r\ntw.local.odcRequest.customerName = tw.local.odcRequest.CustomerInfo.customerName;"}, "ns16:incoming": "2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39", "ns16:outgoing": "2027.********-cc55-441d-85f9-fc7347b56e9c", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.373a4a6c-181b-43d5-8f0e-8b400049f72e", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.CustomerInfo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a"}}}, {"ns16:sourceRef": "2055.08a7970f-b648-4b7d-8d40-df8769fba145", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.2302fe8e-614d-43eb-91b6-2956193bc993", "default": "2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe", "name": "Initiate ODC Process", "id": "2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "879", "y": "98", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": " tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\r\r\n tw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name;\r\r\n tw.local.odcRequest.BasicDetails.parentRequestNo = tw.local.odcRequest.parentRequestNo;\r\r\n\r\r\n "}, "ns16:incoming": "2027.3770419b-abc2-4c23-8f28-fa27b5f1d122", "ns16:outgoing": "2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.value", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.82e3268d-9082-40bf-8a48-16f0710ff117", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf", "ns16:assignment": {"ns16:from": {"_": "tw.local.routingDetails", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"}}}, {"ns16:targetRef": "2055.17c166a1-1274-4247-89a3-e21805af343c", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.de6250d6-ea05-49bc-896c-eada027a9562", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644", "ns16:assignment": {"ns16:to": {"_": "tw.local.instanceId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.743a63fc-5839-4d37-8d0e-a582fda8ee91", "ns16:assignment": {"ns16:to": {"_": "tw.local.taskId", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.192ba003-65ef-451c-89ea-378afe19f644", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.bdae3091-5a11-4be9-8bab-5c185829c1d3", "ns16:assignment": {"ns16:to": {"_": "tw.local.testData", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:sourceRef": "2055.d6436a9f-5a30-4f42-8b03-224e2373a5a4", "ns16:assignment": {"ns16:to": {"_": "tw.local.routingDetails", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"}}}, {"ns16:sourceRef": "2055.5368acb7-69e7-470f-8f37-b60b75384c47", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, {"calledElement": "1.58febcb0-50a3-4963-8368-32c96c00d116", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.04cb5201-2bb1-490a-83b3-3939d688fa9d", "name": "Retrieve request and customer data", "id": "2025.57d2b1c1-9e2a-4263-8908-d5a175901be5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "480", "y": "406", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true", "ns3:postAssignmentScript": "if (tw.local.parentIsFound == false) {\r\r\n\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.odcRequest.parentRequestNo\", tw.local.messageOFParentODC);\r\r\n\ttw.local.match = false;\r\r\n}\r\r\n\r\r\n  tw.local.odcRequest.requestNature.value=tw.local.requestNature;\r\r\n  tw.local.odcRequest.requestType.value = tw.local.requestType;\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n"}, "ns16:incoming": "2027.2dfd22ad-e1e6-4169-8141-a6fa665dae6d", "ns16:outgoing": "2027.04cb5201-2bb1-490a-83b3-3939d688fa9d", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.a6cf9e03-c07b-4bb0-81a8-c74e6c8f64da", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.CustomerInfo.cif", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.d9a8520f-f8b4-408f-8cab-eafb8f688056", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.67ed8128-22da-46f1-894a-ab580c23f58f", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.6448783e-44be-4081-88f8-e6d08fe0c107", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.f7fc5ea8-49a6-40b1-8b25-250a8fdd25fa", "ns16:assignment": {"ns16:to": {"_": "tw.local.parentIsFound", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.b1f93375-04e2-4a6a-8e23-a158a2320eb6", "ns16:assignment": {"ns16:to": {"_": "tw.local.messageOFParentODC", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.58299858-8daf-4093-89ff-ec58ea61013c", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.013d14bf-4f05-44e3-89dd-360f5ad5ceb8", "ns16:assignment": {"ns16:to": {"_": "tw.local.match", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, {"calledElement": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "default": "2027.fa8f7885-4309-4b35-8734-0d5d280d1af8", "name": "Get ODC Initiators1", "id": "2025.511860dd-9e1f-4a02-8cd3-03a7837bb403", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "84", "y": "158", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": "tw.local.error={};\r\r\ntw.local.routingDetails ={};"}, "ns16:incoming": "2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d", "ns16:outgoing": "2027.fa8f7885-4309-4b35-8734-0d5d280d1af8", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.e791ee53-6e73-4c61-a509-f183adf32d40", "ns16:assignment": {"ns16:from": {"_": "tw.system.user.fullName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.a17679eb-ad20-4778-b1c1-06b7dc04228f", "ns16:assignment": {"ns16:to": {"_": "tw.local.routingDetails.hubCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.4e8f9aa5-7f46-45cc-b0ee-47bfd2137cde", "ns16:assignment": {"ns16:to": {"_": "tw.local.routingDetails.branchCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.ea1a2239-6be8-4443-a29e-1a1d65d70dda", "ns16:assignment": {"ns16:to": {"_": "tw.local.routingDetails.branchName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.c9bdd6b8-1830-4f27-8a5f-dafc6532eb02", "ns16:assignment": {"ns16:to": {"_": "tw.local.routingDetails.hubName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.8b261e83-fd81-4f5c-aee4-4011dfc21d9f", "ns16:assignment": {"ns16:to": {"_": "tw.local.routingDetails.branchSeq", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.194ea2a4-0d33-4fd5-8ddb-eb734d149fdb", "ns16:assignment": {"ns16:to": {"_": "tw.local.routingDetails.initiatorUser", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.d0ef6455-5fd6-4815-82bc-b93bd42e3532", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.6cab4049-7518-47ae-8460-bd5b20f589c3", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}, {"calledElement": "1.67c66f5d-27ec-4ffa-988d-6c8b6e4ee91e", "default": "2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d", "name": "Init ODC Request", "id": "2025.8218dde1-2bb0-4af4-8c42-524bfc5974b7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "-95", "y": "158", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.error={};"}, "ns16:incoming": "2027.04dd44c6-4272-406e-8aac-2a58512438f3", "ns16:outgoing": "2027.b615ad8c-d4c2-493d-8c70-ad7dc611139d", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5c2788e7-980b-4bb0-83bd-e96fb95996a5", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.1f86e5e6-0e7e-46f9-8822-6ec3fb31b2de", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.247d12a8-cf76-495c-8cd4-700f32286023", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.83939b7e-949f-45c0-8ff8-5f11d818d9a6", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.d8207bd6-bf8a-47c8-8663-e8035d29a909", "name": "add list of accounts to BO", "id": "2025.6f1b5374-e9f7-4cd9-8652-96019a4c97eb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "853", "y": "-71", "width": "95", "height": "70"}, "ns3:postAssignmentScript": ""}, "ns16:incoming": "2027.f4149dd0-143a-4cf4-8146-02c2f29fdccf", "ns16:outgoing": "2027.d8207bd6-bf8a-47c8-8663-e8035d29a909", "ns16:script": "if(!!tw.local.odcRequest.FinancialDetailsBR){\r\r\n\r\r\n  if(!tw.local.odcRequest.FcCollections)\r\r\n      tw.local.odcRequest.FcCollections = {};\r\r\n      \r\r\n  tw.local.odcRequest.FcCollections.listOfAccounts = [];\r\r\n\t\r\r\n\tfor(var i=0;i<tw.local.odcRequest.FinancialDetailsBR.listOfAccounts.length;i++)\r\r\n\t{\r\r\n\t     \r\r\n\t\ttw.local.odcRequest.FcCollections.listOfAccounts[i] = {};\r\r\n\t\ttw.local.odcRequest.FcCollections.listOfAccounts[i]= tw.local.odcRequest.FinancialDetailsBR.listOfAccounts[i];\r\r\n\t}\r\r\n} \r\r\n"}, {"scriptFormat": "text/x-javascript", "default": "2027.c0303047-c7de-4d2f-8917-5f1dd710d133", "name": "Init Request Header", "id": "2025.a251a684-eb50-4960-8385-1975a1f4ab72", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "220", "y": "158", "width": "95", "height": "70"}}, "ns16:incoming": "2027.fa8f7885-4309-4b35-8734-0d5d280d1af8", "ns16:outgoing": "2027.c0303047-c7de-4d2f-8917-5f1dd710d133", "ns16:script": "tw.local.isSuccessful=false;\r\r\ntw.local.error = {};\r\r\ntw.local.errorPnlVis= \"NONE\";\r\r\n\r\r\ntw.local.odcRequest={};\r\r\ntw.local.odcRequest.appInfo = {};\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\n\r\r\ntw.local.odcRequest.appInfo.branch = {};\r\r\n\r\r\n\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\n\ttw.local.odcRequest.appInfo.requestName = \"Outward Documentary Collection\";\r\r\n\ttw.local.odcRequest.appInfo.stepName =  \"Start New ODC Request\";\r\r\n\ttw.local.odcRequest.appInfo.status =\"New \";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"New \";\r\r\n\tif( tw.local.routingDetails.hubCode !=null)\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.appInfo.branch.name = \"HUB \"   + tw.local.routingDetails.hubCode;\r\r\n\t\ttw.local.odcRequest.appInfo.branch.value =  \"HUB \" + tw.local.routingDetails.hubCode;\r\r\n\t\t\r\r\n\t\ttw.local.role = \"hub\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.odcRequest.appInfo.branch.name  =  tw.local.routingDetails.branchName;\r\r\n\t\ttw.local.odcRequest.appInfo.branch.value =  tw.local.routingDetails.branchName;\r\r\n\t\ttw.local.role = \"branch\";\r\r\n\t}\r\r\n\t\r\r\ntw.local.actionConditins =\"new\";\r\r\n"}, {"scriptFormat": "text/x-javascript", "default": "2027.5ed17938-f539-4de9-859e-409c61a836e2", "name": "Validation", "id": "2025.cab3c66e-b183-4fe1-8c49-00cea1480619", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "599", "y": "94", "width": "95", "height": "70", "color": "#95D087"}}, "ns16:incoming": "2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5", "ns16:outgoing": "2027.5ed17938-f539-4de9-859e-409c61a836e2", "ns16:script": " tw.local.errorMessage =\"\";\r\r\nvar mandatoryTriggered = false;\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length < len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------START NEW REQUEST VALIDATION-----------------------------------------\r\r\n \r\r\nmandatory(tw.local.odcRequest.requestNature.value , \"tw.local.odcRequest.requestNature\");\r\r\nmandatory(tw.local.odcRequest.requestType.value , \"tw.local.odcRequest.requestType\");\r\r\nmandatory(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\");\r\r\nminLength(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\" , 8 , \"CIF can't be empty and must be 8 digits\" , \"CIF can't be empty and must be 8 digits\")\r\r\nmaxLength(tw.local.odcRequest.cif ,tw.local.odcRequest.cif , 8 , tw.resource.ValidationMessages.MaxLength8 ,\"CIF: \" + tw.resource.ValidationMessages.MaxLength8);\r\r\n\r\r\nif(tw.local.odcRequest.customerName == \"\" || tw.local.odcRequest.customerName == null)\r\r\n{\r\r\n\taddError(tw.local.odcRequest.customerName , \"Customer Data must be retrieved\" , \"Customer Data must be retrieved\");\r\r\n}\r\r\n \r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPnlVis =\"EDITABLE\": tw.local.errorPnlVis =\"NONE\";"}, {"scriptFormat": "text/x-javascript", "default": "2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e", "name": "Validation", "id": "2025.c7059a1d-7148-45b9-8c72-dbb49859b70a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "593", "y": "283", "width": "95", "height": "70", "color": "#95D087"}}, "ns16:incoming": "2027.07ff7019-86de-42d0-812f-07403c4e6137", "ns16:outgoing": "2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e", "ns16:script": "if(tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest )\r\r\n{\t\r\r\n\tif(tw.local.odcRequest.parentRequestNo == null || tw.local.odcRequest.parentRequestNo == \"\"){\r\r\n\t\ttw.system.coachValidation.addValidationError(\"tw.local.odcRequest.parentRequestNo\", \"Mandatory Field\");\r\r\n\t\ttw.local.parentrequestTypeVIS= \"Editable\";\r\r\n\t}\r\r\n\telse if(tw.local.odcRequest.parentRequestNo.length !=14)\r\r\n\t{\r\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.odcRequest.parentRequestNo\", \"Parent Request number length is 14 digits\");\r\r\n\t}\r\r\n//\ttw.local.parentrequestTypeVIS= \"Editable\";\r\r\n\r\r\n}"}, {"scriptFormat": "text/x-javascript", "default": "2027.24c62e6d-f2d2-41b4-852c-75662821c37f", "name": "Validate CIF", "id": "2025.5ff03e32-c758-4d31-87e6-e3a3b9b18baa", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "420", "y": "-71", "width": "95", "height": "70", "color": "#95D087"}, "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": ""}, "ns16:incoming": "2027.583eb4f6-332a-4717-8998-1ba5434caf93", "ns16:outgoing": "2027.24c62e6d-f2d2-41b4-852c-75662821c37f", "ns16:script": " tw.local.errorMessage =\"\";\r\r\nvar mandatoryTriggered = false;\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n/*\r\r\n* =========================================================================================================\r\r\n*  \r\r\n* Add a coach validation error \r\r\n* \t\t\r\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n*\r\r\n* =========================================================================================================\r\r\n*/\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n\r\r\n/*\r\r\n* =================================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is less than given length\r\r\n*\t\r\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =================================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length < len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n/*\r\r\n* =======================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the string length is greater than given length\r\r\n*\t\r\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n*\r\r\n* =======================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field != null && field != undefined && field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n/*\r\r\n* ==================================================================================================================\r\r\n*\r\r\n* Add a coach validation error if the field is null 'Mandatory'\r\r\n*\t\r\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n*\r\r\n* ==================================================================================================================\r\r\n*/\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n\r\r\n//-------------------------------------------------START NEW REQUEST VALIDATION-----------------------------------------\r\r\n \r\r\n//mandatory(tw.local.odcRequest.requestNature.value , \"tw.local.odcRequest.requestNature\");\r\r\n//mandatory(tw.local.odcRequest.requestType.value , \"tw.local.odcRequest.requestType\");\r\r\nmandatory(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\");\r\r\nminLength(tw.local.odcRequest.cif , \"tw.local.odcRequest.cif\" , 8 , \"CIF can't be empty and must be 8 digits\" , \"CIF can't be empty and must be 8 digits\")\r\r\nmaxLength(tw.local.odcRequest.cif ,tw.local.odcRequest.cif , 8 , tw.resource.ValidationMessages.MaxLength8 ,\"CIF: \" + tw.resource.ValidationMessages.MaxLength8);\r\r\n \r\r\ntw.local.errorMessage!=null ?  tw.local.errorPnlVis =\"EDITABLE\": tw.local.errorPnlVis =\"NONE\";"}, {"scriptFormat": "text/x-javascript", "default": "2027.c63ff4b7-1199-4175-8459-96274f810734", "name": "Log", "id": "2025.adb831df-fa3a-4a39-80d3-634e6d74a0cf", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1133", "y": "95", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.1db69037-6f65-4698-8ae1-1d626c0e547a", "ns16:outgoing": "2027.c63ff4b7-1199-4175-8459-96274f810734", "ns16:script": "console.log(\"********************************* ODC PROCESS STARTED SUCCESSFULLY **********************************************\");"}, {"scriptFormat": "text/x-javascript", "default": "2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576", "name": "Exp Handling", "id": "2025.c8d210f8-1ebe-4a55-889d-88fd8af66479", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "739", "y": "27", "width": "95", "height": "70", "color": "#FF7782"}}, "ns16:incoming": ["2027.266dbba4-42b1-47ed-8511-9923ff05bc91", "2027.33cad873-9cec-4aa0-8a17-290acb5feefb"], "ns16:outgoing": "2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576", "ns16:script": "tw.local.errorMessage =\"Can Not Retrieve Customer Data, Please Contact Administrator.\";\r\r\ntw.local.errorPnlVis=\"Editable\";"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.047323b2-b2be-4496-8a49-edf981e8d2f2", "name": "Exp Handling", "id": "2025.8831f70c-3d21-41c8-8332-e16c1391865b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "748", "y": "519", "width": "95", "height": "70", "color": "#FF7782"}}, "ns16:incoming": "2027.9315199b-0bda-4103-8748-a73f2c698641", "ns16:outgoing": "2027.047323b2-b2be-4496-8a49-edf981e8d2f2", "ns16:script": "tw.local.errorMessage=\"Can Not Retrieve Request Data, Please Contact Administrator.\";\r\r\ntw.local.errorPnlVis=\"Editable\";"}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.b55e1f67-35bb-42a9-8d21-5b5b38125a41"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.33b74951-3a81-4990-84c1-bb3ba8966a0d"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorPnlVis", "id": "2056.63e83afb-9835-499c-8299-c3b291102257"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "instanceId", "id": "2056.*************-4dc0-884e-4892e15a8920"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.bb085204-fd0e-4f4e-8c8b-48551c29f91b"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "taskId", "id": "2056.11e3f0c2-08d7-400a-8282-6c97dbe66b03"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentrequestTypeVIS", "id": "2056.c2f5f445-9eae-42f2-849f-469114746d3c"}, {"itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "name": "testData", "id": "2056.c16cf93c-6c6f-42da-8b0c-f00c5234f32a"}, {"itemSubjectRef": "itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727", "isCollection": "false", "name": "routingDetails", "id": "2056.6aa633ed-3cc2-48ad-8f5f-e012837727c1", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.hubCode = \"\";\r\nautoObject.branchCode = \"\";\r\nautoObject.initiatorUser = \"\";\r\nautoObject.branchName = \"\";\r\nautoObject.hubName = \"\";\r\nautoObject.branchSeq = \"\";\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "role", "id": "2056.55ac1d60-8e92-42b8-8c49-a14ee97f0a37"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "actionConditins", "id": "2056.39593f81-2758-44ae-8a82-012904210d53"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "parentIsFound", "id": "2056.a7e91e91-005a-448f-8a37-381b5f34cbea"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "messageOFParentODC", "id": "2056.8d8ce351-eee6-4234-8cbc-b63fbf9f605b"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestNature", "id": "2056.0155092e-203d-49f7-8ec8-a4ff6b57262b"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestType", "id": "2056.ba713ab7-2969-4da8-832d-47a0802687d4"}, {"itemSubjectRef": "itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "isCollection": "false", "name": "customerInfo", "id": "2056.d2ecf21b-a1ce-4213-8ff4-3199f9b301cf"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "match", "id": "2056.ed604e4f-7d50-45ec-8b64-77763bb3dadb"}], "ns16:intermediateThrowEvent": [{"name": "Stay on page", "id": "2025.ae9d3422-ed8b-496b-855a-a5ac9c1793c9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "949", "y": "-47", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.d8207bd6-bf8a-47c8-8663-e8035d29a909", "2027.3f23a84e-9f23-4480-8bd5-7f773ffb1576"], "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.e339591e-f753-463c-8eec-88f5fab4bcc6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "731", "y": "233", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.fdeea0f0-045f-4724-8021-655bd3da3904", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 2", "id": "2025.2d1d2ec7-ab7a-45dd-82c8-68e9bfece5cc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1051", "y": "200", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:preAssignmentScript": "alert(\"Can Not Intiate New Process, Please Contact Administrator.\");\r\r\nconsole.log(\"********************************* ODC PROCESS CAN NOT START **********************************************\");"}, "ns16:incoming": "2027.5d67e7f8-dbc4-4a42-8d5e-0737f7d03a62", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 3", "id": "2025.f3eab63c-273f-4365-81f9-d46a38fd63fc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "676", "y": "375", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.f2b4d906-4421-4932-83a6-4caacafcd6da", "2027.047323b2-b2be-4496-8a49-edf981e8d2f2", "2027.04cb5201-2bb1-490a-83b3-3939d688fa9d"], "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 4", "id": "2025.84e7c76f-c916-45ec-8118-19f96e8b4e2d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "546", "y": "6", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.8c4524f7-79d1-4606-8cc7-2bb22ed9d2f7", "ns3:stayOnPageEventDefinition": ""}], "ns16:exclusiveGateway": [{"default": "2027.fdeea0f0-045f-4724-8021-655bd3da3904", "name": "Valid?", "id": "2025.1abc2d1e-0507-43e2-87a8-36d08d6a57ff", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "714", "y": "113", "width": "32", "height": "32"}}, "ns16:incoming": "2027.5ed17938-f539-4de9-859e-409c61a836e2", "ns16:outgoing": ["2027.3770419b-abc2-4c23-8f28-fa27b5f1d122", "2027.fdeea0f0-045f-4724-8021-655bd3da3904"]}, {"default": "2027.1db69037-6f65-4698-8ae1-1d626c0e547a", "name": "Is Successful?", "id": "2025.27ea4332-6690-4601-88e4-44f33eda9ec1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1022", "y": "117", "width": "32", "height": "32"}}, "ns16:incoming": ["2027.d0cc8b4a-2c3c-4e2b-8c15-bc619f2ae4fe", "2027.86a06a2f-7003-4159-8106-9c6fb96f9e10"], "ns16:outgoing": ["2027.1db69037-6f65-4698-8ae1-1d626c0e547a", "2027.5d67e7f8-dbc4-4a42-8d5e-0737f7d03a62"]}, {"default": "2027.f2b4d906-4421-4932-83a6-4caacafcd6da", "name": "Valid?", "id": "2025.f8a83829-62ea-4e8e-83f2-9b3378975433", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "624", "y": "374", "width": "32", "height": "32"}}, "ns16:incoming": "2027.58517fb8-0bc2-4e64-8c1a-803fe8eaf75e", "ns16:outgoing": ["2027.2dfd22ad-e1e6-4169-8141-a6fa665dae6d", "2027.f2b4d906-4421-4932-83a6-4caacafcd6da"]}, {"default": "2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39", "name": "Valid?", "id": "2025.b7dd172c-ff00-48be-80f2-d2b0d2dd6c7a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "526", "y": "-52", "width": "32", "height": "32"}}, "ns16:incoming": "2027.24c62e6d-f2d2-41b4-852c-75662821c37f", "ns16:outgoing": ["2027.a76c20ca-3b92-4c9c-8136-fb9b76364e39", "2027.8c4524f7-79d1-4606-8cc7-2bb22ed9d2f7"]}, {"default": "2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5", "name": "Exclusive Gateway", "id": "2025.********-7a3b-4867-82bd-7917afd836ee", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "536", "y": "152", "width": "32", "height": "32"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": "if(tw.local.match == false)\r\r\n{\r\r\ntw.system.coachValidation.addValidationError(\"tw.local.\", message)\r\r\n\r\r\n}"}, "ns16:incoming": "2027.3e87bb72-e5f8-4ba1-822b-a872aedeab52", "ns16:outgoing": ["2027.cc910e28-17db-4e54-88f7-ade7c5adb9f5", "2027.e57880dc-aa52-4d4d-84b3-13f7208fb67c"]}], "ns16:endEvent": {"name": "End 1", "id": "2025.bc42e81e-a5b4-4f64-805a-67b338e3e04f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1266", "y": "118", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.c63ff4b7-1199-4175-8459-96274f810734"}, "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "2025.8fbc8233-4e66-45a6-8f28-63fdea1cd536", "parallelMultiple": "false", "name": "Error", "id": "2025.55b4a008-52de-4c7d-8ba0-eef90baf6e45", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "656", "y": "-13", "width": "24", "height": "24"}, "ns3:default": "2027.266dbba4-42b1-47ed-8511-9923ff05bc91"}, "ns16:outgoing": "2027.266dbba4-42b1-47ed-8511-9923ff05bc91", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "2025.4e7aa27c-54bd-4822-87e1-703a9affa393"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "2025.1c64304e-e98e-438b-84a4-48e212ff86ff", "parallelMultiple": "false", "name": "Error 1", "id": "2025.0864acdc-858d-4f64-8b57-3a4d2ce31604", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "775", "y": "-13", "width": "24", "height": "24"}, "ns3:default": "2027.33cad873-9cec-4aa0-8a17-290acb5feefb"}, "ns16:outgoing": "2027.33cad873-9cec-4aa0-8a17-290acb5feefb", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "2025.8eb8c012-df86-42fb-8810-d83245f739a4"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "2025.57d2b1c1-9e2a-4263-8908-d5a175901be5", "parallelMultiple": "false", "name": "Error 2", "id": "2025.83804793-3887-45b9-86ba-debf601e5a61", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "515", "y": "464", "width": "24", "height": "24"}, "ns3:default": "2027.9315199b-0bda-4103-8748-a73f2c698641"}, "ns16:outgoing": "2027.9315199b-0bda-4103-8748-a73f2c698641", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "2025.b5867aa6-89f1-46b8-8750-1eb640536d5d"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "2025.a4e2b99e-2e8e-4ef3-8cea-f02de90d84b6", "parallelMultiple": "false", "name": "Error 3", "id": "2025.8eee1217-c861-46a5-8225-7129e237bdf0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "914", "y": "156", "width": "24", "height": "24"}, "ns3:default": "2027.86a06a2f-7003-4159-8106-9c6fb96f9e10"}, "ns16:outgoing": "2027.86a06a2f-7003-4159-8106-9c6fb96f9e10", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "2025.53d618c7-1a55-4157-8a9b-f2260fee8e9e"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}], "ns3:htmlHeaderTag": {"id": "99e0c3e9-330f-4951-849f-3bdc0c7fc0f7", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "StartableService", "ns3:participantRef": "24.e7977935-dba2-437e-9671-4ec41d29e437"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.93c5c002-7ac4-4283-83ee-63b8662f9223", "epvProcessLinkId": "ee84ccbb-7aff-4875-8255-7fec04e89a38"}, {"epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "epvProcessLinkId": "533fed92-cfb1-4607-8948-69ebf68802a0"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.e21b51c9-84fa-4940-89fc-251e2b57048c"}}}, "ns16:dataInput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.714f799b-ef4f-429f-8681-00f13ef31a6e", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = {};\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = [];\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = [];\r\nautoObject.FcCollections.listOfAccounts[0] = {};\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = [];\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = {};\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.Drawer = {};\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = {};\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = {};\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = {};\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = {};\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "useDefault": "false"}}}, "ns16:dataOutput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.dd46ba9a-cb92-44c1-87aa-ab62d8648d68"}, "ns16:inputSet": "", "ns16:outputSet": ""}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.93be0009-f18b-4bd3-85d5-151812d02724", "processId": "1.71509bdd-238b-4c2e-8aa7-65107cae0d0b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.7e66d523-7bdb-481f-ab21-e5c56f7694e4", "2025.7e66d523-7bdb-481f-ab21-e5c56f7694e4"], "endStateId": "Out", "toProcessItemId": ["2025.ce1e4337-d25d-4259-9c75-6c6c95dfe359", "2025.ce1e4337-d25d-4259-9c75-6c6c95dfe359"], "guid": "6b1fdf22-57be-4040-847b-f88dcf3bc863", "versionId": "a606e510-e376-49e9-9213-479e0fad2bf4", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}