{"id": "1.f3735333-84b5-461b-9811-0817300dfb0b", "versionId": "51adad51-fef8-40a1-bfc8-cdc9146187c5", "name": "Get bank BIC codes", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.f3735333-84b5-461b-9811-0817300dfb0b", "name": "Get bank BIC codes", "lastModified": "*************", "lastModifiedBy": "abdelrahman.saleh", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.7ae56528-be45-461b-8366-bd4f74202552", "2025.7ae56528-be45-461b-8366-bd4f74202552"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5805", "versionId": "51adad51-fef8-40a1-bfc8-cdc9146187c5", "dependencySummary": "<dependencySummary id=\"bpdid:415d794a2c221205:3dfd662b:18a375ad0dc:-2e00\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"b74db939-5f25-4f31-860a-2afc7b2a9d64\"},{\"incoming\":[\"e03acc50-4b43-462b-809c-d797923ccf73\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5803\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"1a9523ef-4729-404d-88fc-10eaebdff708\"},{\"targetRef\":\"7ae56528-be45-461b-8366-bd4f74202552\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To get list of bic codes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d\",\"sourceRef\":\"b74db939-5f25-4f31-860a-2afc7b2a9d64\"},{\"startQuantity\":1,\"outgoing\":[\"acae3377-def0-4e0c-8bb9-0dd367e18a45\"],\"incoming\":[\"2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":162,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"get list of bic codes\",\"dataInputAssociation\":[{\"targetRef\":\"2055.4ad7fd40-8451-480a-840d-e4350d23f2ba\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.data\"]}}]},{\"targetRef\":\"2055.1094baed-e916-4772-8bd8-91a41441f7ad\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.prefix\"]}}]},{\"targetRef\":\"2055.*************-4d10-8b61-5e94d3ad31bd\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"ODC creation and amendment\\\"\"]}}]},{\"targetRef\":\"2055.a5891acc-745c-47e0-825a-7e040254d8ec\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user_id\"]}}]},{\"targetRef\":\"2055.034a46fd-24a2-4243-84ce-38fb46ffc959\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.currentProcessInstanceID\"]}}]},{\"targetRef\":\"2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.21defa96-4307-47f8-8cb7-e0bc7234481b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"7ae56528-be45-461b-8366-bd4f74202552\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.listOfValues\"]}}],\"sourceRef\":[\"2055.bb8dd17c-590d-440f-8545-364fdb26328e\"]}],\"calledElement\":\"1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62\"},{\"targetRef\":\"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Map output\",\"declaredType\":\"sequenceFlow\",\"id\":\"acae3377-def0-4e0c-8bb9-0dd367e18a45\",\"sourceRef\":\"7ae56528-be45-461b-8366-bd4f74202552\"},{\"startQuantity\":1,\"outgoing\":[\"e03acc50-4b43-462b-809c-d797923ccf73\"],\"incoming\":[\"acae3377-def0-4e0c-8bb9-0dd367e18a45\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":415,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Map output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.results = new tw.object.listOf.String();\\r\\nif(tw.local.listOfValues != null)\\r\\n{\\r\\n\\tfor(var i=0;i<tw.local.listOfValues.listLength;i++)\\r\\n\\t{\\r\\n\\t\\ttw.local.results[i] = \\\"\\\";\\r\\n\\t\\ttw.local.results[i] = tw.local.listOfValues[i];\\r\\n\\t}\\r\\n}\\r\\n\"]}},{\"targetRef\":\"1a9523ef-4729-404d-88fc-10eaebdff708\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e03acc50-4b43-462b-809c-d797923ccf73\",\"sourceRef\":\"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject[0] = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"listOfValues\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.f5dee66e-6b9e-46be-8d29-874648095e3d\"},{\"incoming\":[\"82ba0295-2ba9-470d-81b6-ef511e423068\",\"42c654ba-fcee-4940-8ff4-36237aea2653\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"abf8ccbb-47b9-49bf-8345-125ebddc5e72\",\"otherAttributes\":{\"eventImplId\":\"95fe0dd6-856c-4b5a-8b7f-8cd74a17a168\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":290,\"y\":214,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"62081ca6-1b8d-4c67-81a8-1cf00f284a2a\"},{\"parallelMultiple\":false,\"outgoing\":[\"42c654ba-fcee-4940-8ff4-36237aea2653\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"a724f10c-ddb3-4b91-8565-083f4cac09e8\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"f49a74c3-b8fb-495e-82bb-4f9de383efb3\",\"otherAttributes\":{\"eventImplId\":\"bb2aefb4-85e6-45ba-8761-2b73e2f887c0\"}}],\"attachedToRef\":\"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":450,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"e80a291b-dd2b-47af-8304-9b4cb2195ff8\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"82ba0295-2ba9-470d-81b6-ef511e423068\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"337db7fe-9920-42d9-8a37-a580e02e1a26\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"d391214b-9104-43a2-83ab-6d225d047c3f\",\"otherAttributes\":{\"eventImplId\":\"676dd3d3-749f-4e9d-8eb8-7e3ac66d3978\"}}],\"attachedToRef\":\"7ae56528-be45-461b-8366-bd4f74202552\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":171,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f\",\"outputSet\":{}},{\"targetRef\":\"62081ca6-1b8d-4c67-81a8-1cf00f284a2a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"82ba0295-2ba9-470d-81b6-ef511e423068\",\"sourceRef\":\"dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f\"},{\"targetRef\":\"62081ca6-1b8d-4c67-81a8-1cf00f284a2a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"42c654ba-fcee-4940-8ff4-36237aea2653\",\"sourceRef\":\"e80a291b-dd2b-47af-8304-9b4cb2195ff8\"}],\"laneSet\":[{\"id\":\"7c93cb9a-9d31-4de8-8cb1-63da23bec364\",\"lane\":[{\"flowNodeRef\":[\"b74db939-5f25-4f31-860a-2afc7b2a9d64\",\"1a9523ef-4729-404d-88fc-10eaebdff708\",\"7ae56528-be45-461b-8366-bd4f74202552\",\"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d\",\"62081ca6-1b8d-4c67-81a8-1cf00f284a2a\",\"e80a291b-dd2b-47af-8304-9b4cb2195ff8\",\"dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"c9cc53cd-ec4b-460c-8b79-e8fd70bbeb8f\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get bank BIC codes\",\"declaredType\":\"process\",\"id\":\"1.f3735333-84b5-461b-9811-0817300dfb0b\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"results\",\"isCollection\":true,\"id\":\"2055.5c07015a-cecb-4b9d-8228-49a177850bf6\"}],\"inputSet\":[{}],\"outputSet\":[{}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"********\\\"\"}]},\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"data\",\"isCollection\":false,\"id\":\"2055.a7371130-64dd-40de-8d61-bedd5c7a412b\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a7371130-64dd-40de-8d61-bedd5c7a412b", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "1", "hasDefault": "true", "defaultValue": "\"********\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "f69b94ae-1be9-46e3-bb44-b823a8495ab9", "versionId": "6da7e77d-e344-43c1-9758-7775dabdfa64"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5c07015a-cecb-4b9d-8228-49a177850bf6", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "parameterType": "2", "isArrayOf": "true", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "dfe007d2-c0f9-4852-9ad2-ddb490f285ce", "versionId": "e02902ba-5e25-41c9-be2f-1e11c74d57bf"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.880b4f86-b85c-4e62-9cc8-7e3dacb5c162", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "parameterType": "3", "isArrayOf": "false", "classId": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "698dd17e-55ef-448b-a199-8956d7d12572", "versionId": "41fbc600-bd3f-44da-8f26-5706aa14ad8e"}], "processVariable": {"name": "listOfValues", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f5dee66e-6b9e-46be-8d29-874648095e3d", "description": {"isNull": "true"}, "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject[0] = \"\";\r\nautoObject", "guid": "14049fe2-64ff-4678-8368-3ed5067a1444", "versionId": "eda03136-db1a-4008-b372-40f664e54508"}, "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1a9523ef-4729-404d-88fc-10eaebdff708", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.b24cbe5a-322c-4722-960b-5a596ca09a05", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5803", "versionId": "0d36980a-9cf4-4a55-a329-f4be6eca1dc5", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.b24cbe5a-322c-4722-960b-5a596ca09a05", "haltProcess": "false", "guid": "d9a37e16-c361-4c8d-b7ce-74a1b9741532", "versionId": "10a31c12-344d-4612-94dc-cfc61cbba707"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.47b14728-7d8a-4424-ac2d-bf47f0b8e353", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5685", "versionId": "5e041cd7-7448-4df5-a5af-79685077c003", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "290", "y": "214", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.47b14728-7d8a-4424-ac2d-bf47f0b8e353", "message": "", "faultStyle": "1", "guid": "4d7d52d8-c7b3-4263-8881-fd115773655e", "versionId": "dd83b077-2724-4689-b06a-48d7c2211816", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.65b86ba3-cf43-4c2e-a922-6980e5623c7c", "processParameterId": "2055.880b4f86-b85c-4e62-9cc8-7e3dacb5c162", "parameterMappingParentId": "3007.47b14728-7d8a-4424-ac2d-bf47f0b8e353", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "isList": "false", "isInput": "false", "guid": "cb3ebf65-2e76-4f41-8bcc-57a31bf91578", "versionId": "2ea9a718-0dc0-487f-aca3-747a46dbfa4c", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.7ae56528-be45-461b-8366-bd4f74202552", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "name": "get list of bic codes", "tWComponentName": "SubProcess", "tWComponentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "guid": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-57ec", "versionId": "5f96e2be-3f3e-4418-8f79-3cdfb6ab7be5", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "162", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomLeft", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5685", "errorHandlerItemId": "2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62", "guid": "d4651a6e-5408-4be3-8c0f-f26bca980328", "versionId": "b67b89b9-a1ea-404e-921e-fee2e3c4cdb0", "parameterMapping": [{"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.be44b871-28e8-4456-b6ce-d954f67034b6", "processParameterId": "2055.a5891acc-745c-47e0-825a-7e040254d8ec", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "tw.system.user_id", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "65238cad-15af-4c9c-ab90-184db627a864", "versionId": "04449fe1-6dc0-4899-ac84-61c14fae956e", "description": {"isNull": "true"}}, {"name": "bicCodes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.01afab78-98f5-4c93-a3dc-261ef476f104", "processParameterId": "2055.bb8dd17c-590d-440f-8545-364fdb26328e", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "tw.local.listOfValues", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "true", "isInput": "false", "guid": "2d0301cf-e4ae-4cd7-94a5-6056d0d10f1a", "versionId": "10f84cef-b2b1-4704-ba06-a61635e250ad", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c84aa355-7219-4d80-8a1a-b0e2798dca78", "processParameterId": "2055.1094baed-e916-4772-8bd8-91a41441f7ad", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "b8aa884a-dacf-48bf-bb48-d37a80f64c17", "versionId": "2196b854-1655-4266-8609-889656d7d6bd", "description": {"isNull": "true"}}, {"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.bc90b3a3-c22d-4b56-84d6-d9810cb3e55f", "processParameterId": "2055.21defa96-4307-47f8-8cb7-e0bc7234481b", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "124694e4-7a67-4649-af81-3fe8e3e2a9ee", "versionId": "431a8c25-e265-47ae-8b5e-6eb60738cd7f", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a867490b-b8ab-436f-8a78-3b84e92099a9", "processParameterId": "2055.034a46fd-24a2-4243-84ce-38fb46ffc959", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "tw.system.currentProcessInstanceID", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "8bff6d4e-f2a5-40c7-a8f3-49f13b87fb7a", "versionId": "46dceaff-f1f2-4ff6-9adb-2244e074d7dd", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c65b35e6-9d91-4540-9757-76ad0c0531b7", "processParameterId": "2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "08af194b-eecc-4273-986f-163ab6bac521", "versionId": "4ed072f3-de2d-49d7-8354-3bb6d54bf8f7", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5517de03-88db-4006-8c2c-be7aad7d6f76", "processParameterId": "2055.3dfd9149-fb93-49c0-8d6c-3bb6a31c868b", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "b7708845-90f4-43f1-b58f-6804841812d0", "versionId": "64f08c15-4199-46ec-a459-00d6f18ac3e8", "description": {"isNull": "true"}}, {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d0d0d033-27f9-4e6d-88db-642dc106a9bf", "processParameterId": "2055.*************-4d10-8b61-5e94d3ad31bd", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "\"ODC creation and amendment\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "67ba4dec-a4d5-4ef3-a44e-512dce8b0ac0", "versionId": "7291425c-dcbc-4c5b-98e3-5d7c134669de", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a3274fa2-445d-4279-8e2f-747c085794c7", "processParameterId": "2055.390182d1-c178-4449-8178-edd289314ab2", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "84e932df-f9b8-4c4e-83b1-8532c151860a", "versionId": "832c8d37-b8c5-48ab-868f-fac6c8dc9d45", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.8c4d93f3-86d0-4e23-b2a6-450042d50b59", "processParameterId": "2055.9a271b13-fe7a-4bc9-8573-99028a4ff122", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "da11a6de-8cda-4ddb-aa25-d89eca41ad5e", "versionId": "a9329376-d373-4c51-a4e3-c0189472d514", "description": {"isNull": "true"}}, {"name": "customerNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.87ed2962-4611-45bb-85c5-ab5beda7b038", "processParameterId": "2055.4ad7fd40-8451-480a-840d-e4350d23f2ba", "parameterMappingParentId": "3012.ea885079-dccb-4176-8201-1fbb0ace9852", "useDefault": "false", "value": "tw.local.data", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "d507806e-8fcb-419c-ab6e-8b7e6204e9e4", "versionId": "f69f2b52-134b-45f1-9b07-3cc700f15a31", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "name": "Map output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.cfa93689-0513-4dc7-8192-6ca2aebfea48", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "guid": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-57ed", "versionId": "e46eae10-8f34-4426-a7e8-36fa6b1cfe1f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "415", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5685", "errorHandlerItemId": "2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.cfa93689-0513-4dc7-8192-6ca2aebfea48", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.results = new tw.object.listOf.String();\r\r\nif(tw.local.listOfValues != null)\r\r\n{\r\r\n\tfor(var i=0;i<tw.local.listOfValues.listLength;i++)\r\r\n\t{\r\r\n\t\ttw.local.results[i] = \"\";\r\r\n\t\ttw.local.results[i] = tw.local.listOfValues[i];\r\r\n\t}\r\r\n}\r\r\n", "isRule": "false", "guid": "3813dc94-6b4d-46fe-915c-acec7c59a1f0", "versionId": "9ab8b2f3-1871-46ca-8c68-49298c01bf00"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get bank BIC codes", "id": "1.f3735333-84b5-461b-9811-0817300dfb0b", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "data", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.a7371130-64dd-40de-8d61-bedd5c7a412b", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"********\"", "useDefault": "true"}}}, "ns16:dataOutput": {"name": "results", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "true", "id": "2055.5c07015a-cecb-4b9d-8228-49a177850bf6"}, "ns16:inputSet": "", "ns16:outputSet": ""}, "ns16:laneSet": {"id": "7c93cb9a-9d31-4de8-8cb1-63da23bec364", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "c9cc53cd-ec4b-460c-8b79-e8fd70bbeb8f", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["b74db939-5f25-4f31-860a-2afc7b2a9d64", "1a9523ef-4729-404d-88fc-10eaebdff708", "7ae56528-be45-461b-8366-bd4f74202552", "ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "e80a291b-dd2b-47af-8304-9b4cb2195ff8", "dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "b74db939-5f25-4f31-860a-2afc7b2a9d64", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d"}, "ns16:endEvent": [{"name": "End", "id": "1a9523ef-4729-404d-88fc-10eaebdff708", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5803"}, "ns16:incoming": "e03acc50-4b43-462b-809c-d797923ccf73"}, {"name": "End Event", "id": "62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "290", "y": "214", "width": "24", "height": "24"}}, "ns16:incoming": ["82ba0295-2ba9-470d-81b6-ef511e423068", "42c654ba-fcee-4940-8ff4-36237aea2653"], "ns16:errorEventDefinition": {"id": "abf8ccbb-47b9-49bf-8345-125ebddc5e72", "eventImplId": "95fe0dd6-856c-4b5a-8b7f-8cd74a17a168", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "b74db939-5f25-4f31-860a-2afc7b2a9d64", "targetRef": "7ae56528-be45-461b-8366-bd4f74202552", "name": "To get list of bic codes", "id": "2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "7ae56528-be45-461b-8366-bd4f74202552", "targetRef": "ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "name": "To Map output", "id": "acae3377-def0-4e0c-8bb9-0dd367e18a45", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a"}}, {"sourceRef": "ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "targetRef": "1a9523ef-4729-404d-88fc-10eaebdff708", "name": "To End", "id": "e03acc50-4b43-462b-809c-d797923ccf73", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f", "targetRef": "62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "name": "To End Event", "id": "82ba0295-2ba9-470d-81b6-ef511e423068", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "e80a291b-dd2b-47af-8304-9b4cb2195ff8", "targetRef": "62081ca6-1b8d-4c67-81a8-1cf00f284a2a", "name": "To End Event", "id": "42c654ba-fcee-4940-8ff4-36237aea2653", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:callActivity": {"calledElement": "1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "get list of bic codes", "id": "7ae56528-be45-461b-8366-bd4f74202552", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "162", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d", "ns16:outgoing": "acae3377-def0-4e0c-8bb9-0dd367e18a45", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.4ad7fd40-8451-480a-840d-e4350d23f2ba", "ns16:assignment": {"ns16:from": {"_": "tw.local.data", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.1094baed-e916-4772-8bd8-91a41441f7ad", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.*************-4d10-8b61-5e94d3ad31bd", "ns16:assignment": {"ns16:from": {"_": "\"ODC creation and amendment\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.a5891acc-745c-47e0-825a-7e040254d8ec", "ns16:assignment": {"ns16:from": {"_": "tw.system.user_id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.034a46fd-24a2-4243-84ce-38fb46ffc959", "ns16:assignment": {"ns16:from": {"_": "tw.system.currentProcessInstanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.21defa96-4307-47f8-8cb7-e0bc7234481b", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.bb8dd17c-590d-440f-8545-364fdb26328e", "ns16:assignment": {"ns16:to": {"_": "tw.local.listOfValues", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Map output", "id": "ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "415", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "acae3377-def0-4e0c-8bb9-0dd367e18a45", "ns16:outgoing": "e03acc50-4b43-462b-809c-d797923ccf73", "ns16:script": "tw.local.results = new tw.object.listOf.String();\r\r\nif(tw.local.listOfValues != null)\r\r\n{\r\r\n\tfor(var i=0;i<tw.local.listOfValues.listLength;i++)\r\r\n\t{\r\r\n\t\ttw.local.results[i] = \"\";\r\r\n\t\ttw.local.results[i] = tw.local.listOfValues[i];\r\r\n\t}\r\r\n}\r\r\n"}, "ns16:dataObject": {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "listOfValues", "id": "2056.f5dee66e-6b9e-46be-8d29-874648095e3d", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject[0] = \"\";\r\nautoObject", "useDefault": "true"}}}, "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "parallelMultiple": "false", "name": "Error", "id": "e80a291b-dd2b-47af-8304-9b4cb2195ff8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "450", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "42c654ba-fcee-4940-8ff4-36237aea2653", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "a724f10c-ddb3-4b91-8565-083f4cac09e8"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "f49a74c3-b8fb-495e-82bb-4f9de383efb3", "eventImplId": "bb2aefb4-85e6-45ba-8761-2b73e2f887c0", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "7ae56528-be45-461b-8366-bd4f74202552", "parallelMultiple": "false", "name": "Error1", "id": "dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "171", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "82ba0295-2ba9-470d-81b6-ef511e423068", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "337db7fe-9920-42d9-8a37-a580e02e1a26"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "d391214b-9104-43a2-83ab-6d225d047c3f", "eventImplId": "676dd3d3-749f-4e9d-8eb8-7e3ac66d3978", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To Map output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.acae3377-def0-4e0c-8bb9-0dd367e18a45", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.7ae56528-be45-461b-8366-bd4f74202552", "2025.7ae56528-be45-461b-8366-bd4f74202552"], "endStateId": "guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a", "toProcessItemId": ["2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d"], "guid": "fb506d55-d85f-4816-8b49-11d74df6e7f0", "versionId": "8d117563-29ee-4a5a-b4f7-6f824d5f9590", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e03acc50-4b43-462b-809c-d797923ccf73", "processId": "1.f3735333-84b5-461b-9811-0817300dfb0b", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d", "2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d"], "endStateId": "Out", "toProcessItemId": ["2025.1a9523ef-4729-404d-88fc-10eaebdff708", "2025.1a9523ef-4729-404d-88fc-10eaebdff708"], "guid": "ecaa523a-1321-4ba8-8042-c3624b92710d", "versionId": "fd66e52b-2604-4316-aba5-087ed7a9cb36", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}