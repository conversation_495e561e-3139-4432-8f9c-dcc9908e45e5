{"id": "64.0f40f56d-733f-4bd5-916c-92ae7dccbb10", "versionId": "af64a198-833a-4315-bc6b-a2056724e2f5", "name": "Contract Creation CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "contractCreation", "configOptions": ["contractCreationVis", "contractStage", "bpmRequestNumber", "currency", "nbeCollectableAmount", "todayDate"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.testSave = function(){\r\r\n\tvar saveBtn = bpmext.ui.getView(\"/DC_Templete1/saveState\");\r\r\n\tconsole.dir(saveBtn);\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\nthis.getViewByPath = function (path) {\r\r\n\treturn bpmext.ui.getView(path) || null;\r\r\n};\r\r\n\r\r\nthis.getFieldById = function (path) {\r\r\n\treturn this.ui.get(path) || null;\r\r\n};"}]}, "hasDetails": true}