<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.7276be8c-00f4-4766-949c-bcd033b050c3" name="Trade Compliance">
        <lastModified>1700596998643</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.35d64c8d-b9be-4b4d-b426-52b3d023705d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>63f01549-b889-4af5-aaeb-0faea1ec8f6f</guid>
        <versionId>86e4fd32-c430-438d-a46b-870d97e311a2</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.f0b0fe9f-384e-4473-91b0-464179607ecc"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":188,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"826774b1-f9a8-4693-9cfe-1ad66c37dd65"},{"targetRef":"2025.335301a8-53b9-4997-b289-0e7f6704abba","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"Start To Coach","declaredType":"sequenceFlow","id":"2027.f0b0fe9f-384e-4473-91b0-464179607ecc","sourceRef":"826774b1-f9a8-4693-9cfe-1ad66c37dd65"},{"startQuantity":1,"outgoing":["2027.4bf97862-f836-4e06-9bfc-81c838970a74"],"incoming":["2027.f0b0fe9f-384e-4473-91b0-464179607ecc"],"default":"2027.4bf97862-f836-4e06-9bfc-81c838970a74","extensionElements":{"nodeVisualInfo":[{"width":95,"x":243,"y":165,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Initialization Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.335301a8-53b9-4997-b289-0e7f6704abba","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.stepLog = {};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT07; \r\n\r\n tw.local.odcRequest.appInfo.stepName= tw.epv.ScreenNames.CACT07;\r\n tw.local.odcRequest.appInfo.appID = tw.system.processInstance.id;\r\n \r\n tw.local.actionConditions = {};\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT07;\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;\r\n\r\n\/*Visibilty Conditions*\/\r\n\/*Basic Details CV Visibility*\/\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\n\ttw.local.parentRequestNoVIS = \"READONLY\";\r\n}\t\r\nelse{\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\n}\t\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\n}\r\nelse{\r\ntw.local.contractStageVIS = \"NONE\";\r\n}\r\nif(tw.local.fromTradeFo == true)\r\n{\r\n\ttw.local.tradefoVis = \"Editable\";\r\n\ttw.local.exehubMkrVis = \"None\";\r\n}\r\nelse\r\n{\r\n\ttw.local.tradefoVis = \"None\";\r\n\ttw.local.exehubMkrVis = \"Editable\";\r\n}\r\n "]}},{"startQuantity":1,"outgoing":["2027.02254107-cf62-4386-b74b-6666a37e9ea6"],"incoming":["2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1"],"default":"2027.02254107-cf62-4386-b74b-6666a37e9ea6","extensionElements":{"nodeVisualInfo":[{"width":95,"x":801,"y":148,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status and substatus","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.5e719de4-0f51-4f78-875d-e8b72a537cae","scriptFormat":"text\/x-javascript","script":{"content":["\r\nif(tw.local.compApprovalInit == tw.epv.userRole.CACT03)\r\n{\r\n\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.returnToMaker;\r\n\ttw.local.odcRequest.appInfo.status =\" In Approval \";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Waiting Trade Fo  Maker Approval\";\r\n}\r\nelse\r\n{\r\n\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.approveRequest;\r\n\t\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing\";\r\n}"]}},{"targetRef":"2025.19273870-c44a-41f8-83e4-94bd44345007","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Trade Compliance","declaredType":"sequenceFlow","id":"2027.4bf97862-f836-4e06-9bfc-81c838970a74","sourceRef":"2025.335301a8-53b9-4997-b289-0e7f6704abba"},{"targetRef":"2025.6a84d426-165c-459f-8ceb-d6006c6ad20d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.02254107-cf62-4386-b74b-6666a37e9ea6","sourceRef":"2025.5e719de4-0f51-4f78-875d-e8b72a537cae"},{"outgoing":["2027.18112dba-24b4-45b9-998c-931522e4f8da"],"incoming":["2027.638a3b48-b2b8-425b-a512-66d9cf9a605b"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.18112dba-24b4-45b9-998c-931522e4f8da"],"nodeVisualInfo":[{"width":24,"x":387,"y":85,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2"},{"targetRef":"2025.6125901b-f21b-438b-9845-846e04b2e62d","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"1f95c90f-8b19-4dfc-9081-1fefb92374d6","coachEventPath":"ODC_Creation_Template1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightTop","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Required Documents","declaredType":"sequenceFlow","id":"2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1","sourceRef":"2025.19273870-c44a-41f8-83e4-94bd44345007"},{"targetRef":"2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"d15989d6-b5f4-4534-b936-4e6cb29120ac","coachEventPath":"ODC_Creation_Template1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.638a3b48-b2b8-425b-a512-66d9cf9a605b","sourceRef":"2025.19273870-c44a-41f8-83e4-94bd44345007"},{"targetRef":"2025.19273870-c44a-41f8-83e4-94bd44345007","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Trade Compliance","declaredType":"sequenceFlow","id":"2027.18112dba-24b4-45b9-998c-931522e4f8da","sourceRef":"2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2"},{"outgoing":["2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1","2027.39733aac-7ee5-4bc2-8ae3-11facf422a16"],"incoming":["2027.718ac925-7d5c-4692-9a49-8441c94e6b80"],"default":"2027.39733aac-7ee5-4bc2-8ae3-11facf422a16","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":64,"x":709,"y":169,"declaredType":"TNodeVisualInfo","height":51}],"preAssignmentScript":[]},"name":"Have Errors","declaredType":"exclusiveGateway","id":"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c"},{"startQuantity":1,"outgoing":["2027.718ac925-7d5c-4692-9a49-8441c94e6b80"],"incoming":["2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1"],"default":"2027.718ac925-7d5c-4692-9a49-8441c94e6b80","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":560,"y":150,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.6125901b-f21b-438b-9845-846e04b2e62d","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false;\r\n \r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\n\t\r\n\/\/-------------------------------------------------Trade Fo and compliance comment validation ------------------------------\r\nif(tw.local.odcRequest.complianceApproval == true)\r\nmandatory(tw.local.odcRequest.compcheckerComment, \"tw.local.odcRequest.compcheckerComment\");\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\t\r\n\t"]}},{"targetRef":"2025.5e719de4-0f51-4f78-875d-e8b72a537cae","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1","sourceRef":"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c"},{"targetRef":"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Have Errors","declaredType":"sequenceFlow","id":"2027.718ac925-7d5c-4692-9a49-8441c94e6b80","sourceRef":"2025.6125901b-f21b-438b-9845-846e04b2e62d"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"errorExist","isCollection":false,"declaredType":"dataObject","id":"2056.3cb681c2-3b0b-4631-b001-0a2c882b9f06"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.3039db6f-7017-4dac-804b-2c33c08ae807"},{"itemSubjectRef":"itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e","name":"documentsTypesSelected","isCollection":true,"declaredType":"dataObject","id":"2056.f250f757-7c1c-4824-a52b-c7a99c4fa5cc"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"validation","isCollection":false,"declaredType":"dataObject","id":"2056.d5955721-5c73-4eea-a4b2-ff7863f4acbd"},{"outgoing":["2027.638a3b48-b2b8-425b-a512-66d9cf9a605b","2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1"],"incoming":["2027.18112dba-24b4-45b9-998c-931522e4f8da","2027.4bf97862-f836-4e06-9bfc-81c838970a74"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":368,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Basic_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cc84854c-2026-4a3e-80ef-81384fc0003e","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"82d1a57b-8783-4495-8acb-f6ff9e5e94c6","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c51f9a43-e803-4766-8f79-f850b6aea066","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"785affde-d6ca-4733-87f4-432d7648950d","optionName":"flexCubeContractNoVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"30568523-626a-4a48-8346-58e2b18fc3e1","optionName":"basicDetailsCVVIS","value":"Readonly"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0d4bc528-b1fe-49b5-8769-a397d283f428","optionName":"parentRequestNoVis","value":"tw.local.parentRequestNoVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"910fd496-ea6a-469c-87ef-205956544fd7","optionName":"contractStageVIS","value":"tw.local.contractStageVIS"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5a46ae9d-72a1-40b3-894b-5963f3933430","version":"8550"},{"layoutItemId":"Customer_Information_cv1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f617bcdb-7f12-4bbe-806e-52125ae8974b","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6390e972-5962-48c0-8d54-27af1f5ba356","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b9a3ba97-842f-4845-853d-dbf1617e4e40","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9f9b6ee9-718e-449e-807b-1d3c8bf1b714","optionName":"customerInfoVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"759f2a5b-d9d2-48f5-8d9d-c4e30842d018","optionName":"listsVIS","value":"Readonly"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f1d938c2-a9be-41d5-8d97-c2cc5622a13d","version":"8550"},{"layoutItemId":"Financial_Details_Branch_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"19e099d3-c648-42a4-878d-f530ee4296df","optionName":"@label","value":"Financial Details Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3947c921-f3b8-4e49-82aa-a310e7375aac","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fe5afcb7-8388-4935-83bd-f6dcf7773ace","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1cb9f512-1af3-4b1f-8baa-62fb6df1e619","optionName":"financialDetailsCVVis","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8f214fca-6653-4a44-86b5-78bf839c7ca6","optionName":"currencyDocAmountVIS","value":"Readonly"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"7bcb31e8-e1e8-4427-8dce-03cf8df12623","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b348381-cd08-4a0a-83f2-ea0b8b1b898b","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"837dcd17-775c-4efe-841c-fb80fb394370","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"20aaf5df-642a-437f-8625-da456863acd4","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"91f8c0f7-58f8-41d2-88ce-e383830089a6","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ddb36141-5cd7-4bb0-8413-978aa7a3017e","optionName":"@label","value":"DC History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b5136aad-7b4e-4ad3-8296-3a79e9a668c0","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b9993545-2227-48e4-8ef6-41caa27b65fd","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f7bb56ab-c9b5-480b-845d-2ad7594ec4c9","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"c0c066c9-2294-4106-87b2-6d8b3112e468"}],"layoutItemId":"Tabs1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"01a07a4f-74ed-4008-89f6-b6f941f1dc79","optionName":"@label","value":"Tabs"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f84296a9-6d7d-4ddb-807d-e6eaae1ef446","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"59ebdb84-1589-4bcd-8d1e-f6823a463245","optionName":"@width","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"99%\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f6c61f00-858b-4520-8711-5a487139e947","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"6c7ce844-c89c-4e15-8f09-59f794ee9606"}],"layoutItemId":"ODC_Creation_Template1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"006ab33a-a3b8-4ee5-8305-1441e543a98b","optionName":"@label","value":"ODC Creation Template"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"194e54fe-1947-4dca-8ede-93af1121fbf7","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f080343e-1909-41f1-8a4b-b9271db352fd","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9d93c440-8136-4604-81b3-a47ad834a6f1","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"98f98ca8-116e-4dd0-84d8-971a786f1cdb","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"eeae2370-a75b-455e-82d5-0e36ada37cb0","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bb0aadf6-6aa3-4cee-8acf-171607e7d06f","optionName":"complianceApprovalVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"baa4fe56-12fe-476a-88a1-6501dcd00dcd","optionName":"terminateReasonVIS","value":"None"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9b629cbc-23a4-4a03-8270-6455bdf3d818","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"59672ff6-0244-4b90-818c-97d590125c47","optionName":"actionConditions","value":"tw.local.actionConditions"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e7e763f6-3093-4aab-8f5e-ca10fecfb022","optionName":"approvalComment","value":"tw.local.odcRequest.approvalComment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"94da15a9-9325-4e3a-8962-c67d096def46","optionName":"approvalCommentVIS","value":"Editable"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"325c9ce6-6791-4935-8626-f3e7cc54881b","optionName":"exeHubMkrCommentVis","value":"Readonly"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d469bb0d-fb91-4c51-8dd6-69a491f4892f","optionName":"tradeFoCommentVis","value":"Readonly"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"dd0b42b4-0097-4c7c-8ed4-dec6711a0f9e","optionName":"tradeFoComment","value":"tw.local.odcRequest.tradeFoComment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bbb5c4d8-3490-4be7-8d15-658463baf634","optionName":"exeHubMkrComment","value":"tw.local.odcRequest.exeHubMkrComment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"22b5585c-4b30-4885-8c05-000ba283130b","optionName":"compcheckerComment","value":"tw.local.odcRequest.compcheckerComment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b65420f1-12d6-4e0c-8bbe-765ea773067c","optionName":"compcheckerCommentVIS","value":"Editable"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"804e9295-a6d8-4814-8c75-ded5d13cda77","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Trade Compliance","isForCompensation":false,"completionQuantity":1,"id":"2025.19273870-c44a-41f8-83e4-94bd44345007"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNoVIS","isCollection":false,"declaredType":"dataObject","id":"2056.ccc1eddc-ac3e-4636-845d-d8899c8f3629"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"contractStageVIS","isCollection":false,"declaredType":"dataObject","id":"2056.a96defa1-4538-4300-87f3-ecf7b0b7cec4"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.screenName = \"\";\nautoObject.userRole = \"\";\nautoObject.complianceApproval = false;\nautoObject.lastStepAction = \"\";\nautoObject.subStatus = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.49e16d32-2f3b-40ab-8e9b-b210917ef3a1"},{"incoming":["2027.39733aac-7ee5-4bc2-8ae3-11facf422a16"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":705,"y":38,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.26bb7a03-2343-47a3-85a9-81c05ecb5530"},{"targetRef":"2025.26bb7a03-2343-47a3-85a9-81c05ecb5530","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.39733aac-7ee5-4bc2-8ae3-11facf422a16","sourceRef":"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c"},{"startQuantity":1,"outgoing":["2027.5038ce81-b484-4111-8dd7-c85220b46cb3"],"incoming":["2027.02254107-cf62-4386-b74b-6666a37e9ea6"],"default":"2027.5038ce81-b484-4111-8dd7-c85220b46cb3","extensionElements":{"nodeVisualInfo":[{"width":95,"x":912,"y":148,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Update request state and status in DB","dataInputAssociation":[{"targetRef":"2055.a84d91ec-e620-4417-85c4-7dd7db58ff31","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}]},{"targetRef":"2055.c5194a72-2de4-483f-823c-47d5b98b572c","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.status"]}}]},{"targetRef":"2055.3974caa7-9f10-4f46-8275-17080a25476e","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.BasicDetails.requestState"]}}]},{"targetRef":"2055.b05449cf-c459-4405-809c-888b00e3e968","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.subStatus"]}}]},{"targetRef":"2055.b537f107-9e83-46e0-8979-6fe5796c7a7d","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.stepName"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.6a84d426-165c-459f-8ceb-d6006c6ad20d","calledElement":"1.2cab04cd-6063-4a13-b148-ec9788e07bf4"},{"targetRef":"2025.509f906a-c362-44e5-81e5-0364f769022b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.5038ce81-b484-4111-8dd7-c85220b46cb3","sourceRef":"2025.6a84d426-165c-459f-8ceb-d6006c6ad20d"},{"outgoing":["2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881"],"incoming":["2027.5038ce81-b484-4111-8dd7-c85220b46cb3"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":1036,"y":148,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881","name":"Audit ODC Request","dataInputAssociation":[{"targetRef":"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.509f906a-c362-44e5-81e5-0364f769022b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.254cf8eb-2743-4c53-8c52-e51c8c22884e"]}],"calledElement":"1.7ee96dd0-834b-44cb-af41-b21585627e49"},{"incoming":["2027.59fef372-1bac-477d-8a36-085033334685"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1476,"y":170,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}],"preAssignmentScript":[]},"name":"End","declaredType":"endEvent","id":"2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff"},{"outgoing":["2027.734d781b-620a-4a45-8c30-281afce1b505"],"incoming":["2027.4f838403-693d-4f4a-8df1-27d6875ec647"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":1225,"y":149,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.734d781b-620a-4a45-8c30-281afce1b505","name":"Audit Request History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.epv.userRole.CACT07"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"outgoing":["2027.59fef372-1bac-477d-8a36-085033334685","2027.d8848e36-792f-45dd-881c-90030cb18e31"],"incoming":["2027.734d781b-620a-4a45-8c30-281afce1b505"],"default":"2027.d8848e36-792f-45dd-881c-90030cb18e31","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1343,"y":168,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Audited","declaredType":"exclusiveGateway","id":"2025.d17dee7b-c083-4da1-8247-34274a828f10"},{"outgoing":["2027.4f838403-693d-4f4a-8df1-27d6875ec647","2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc"],"incoming":["2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881"],"default":"2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1135,"y":168,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Audited?","declaredType":"exclusiveGateway","id":"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852"},{"incoming":["2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc","2027.d8848e36-792f-45dd-881c-90030cb18e31"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1223,"y":247,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0"},{"targetRef":"2025.d17dee7b-c083-4da1-8247-34274a828f10","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Audited","declaredType":"sequenceFlow","id":"2027.734d781b-620a-4a45-8c30-281afce1b505","sourceRef":"2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a"},{"targetRef":"2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.59fef372-1bac-477d-8a36-085033334685","sourceRef":"2025.d17dee7b-c083-4da1-8247-34274a828f10"},{"targetRef":"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Audited?","declaredType":"sequenceFlow","id":"2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881","sourceRef":"2025.509f906a-c362-44e5-81e5-0364f769022b"},{"targetRef":"2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.4f838403-693d-4f4a-8df1-27d6875ec647","sourceRef":"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852"},{"targetRef":"2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc","sourceRef":"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852"},{"targetRef":"2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.d8848e36-792f-45dd-881c-90030cb18e31","sourceRef":"2025.d17dee7b-c083-4da1-8247-34274a828f10"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"tradefoVis","isCollection":false,"declaredType":"dataObject","id":"2056.bb63e567-928d-4798-8a5d-62378b7e95f5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"exehubMkrVis","isCollection":false,"declaredType":"dataObject","id":"2056.5d310b3c-3f82-443d-813d-6d276cfc9944"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"1008d2fb-8e09-4963-8860-a910eda74e70","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"7f504969-a9fd-4c9a-864d-f8d53c95e0fc","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Trade Compliance","declaredType":"globalUserTask","id":"1.7276be8c-00f4-4766-949c-bcd033b050c3","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.0f59046c-cb7e-4393-bec7-d27739cecd93"},{"itemSubjectRef":"itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e","name":"attachment","isCollection":true,"id":"2055.f89a842a-9d83-45bf-adeb-2837b1beb056"},{"itemSubjectRef":"itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49","name":"complianceComments","isCollection":true,"id":"2055.36778ac9-0d81-4996-991e-f909c6f6aa5a"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.5afdc518-a04e-4a57-8dbe-c5eeb28f9e3e"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"104d3792-ae24-4ba9-8c92-9945eea81cb8","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"249442ab-2ec7-4bfa-850c-96de06de549a","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"8b48a36e-e195-47ae-8108-60b7fb639e57","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"616db28e-55e0-4f9e-8b37-f39f1ccded64","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = {};\nautoObject.requestNature = {};\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = {};\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new Date();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = {};\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = {};\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = {};\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.BasicDetails = {};\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = {};\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = {};\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = {};\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = {};\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = [];\nautoObject.BasicDetails.Bills[0] = {};\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\nautoObject.BasicDetails.Invoice = [];\nautoObject.BasicDetails.Invoice[0] = {};\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\nautoObject.GeneratedDocumentInfo = {};\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = [];\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.FinancialDetailsBR = {};\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = {};\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = {};\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = [];\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = {};\nautoObject.FcCollections.currency = {};\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new Date();\nautoObject.FcCollections.ToDate = new Date();\nautoObject.FcCollections.accountNo = {};\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = [];\nautoObject.FcCollections.retrievedTransactions[0] = {};\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = [];\nautoObject.FcCollections.selectedTransactions[0] = {};\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = [];\nautoObject.FcCollections.listOfAccounts[0] = {};\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = {};\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new Date();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = {};\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.MultiTenorDates = [];\nautoObject.MultiTenorDates[0] = {};\nautoObject.MultiTenorDates[0].date = new Date();\nautoObject.MultiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = {};\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = {};\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = {};\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new Date();\nautoObject.ProductShipmentDetails.shipmentMethod = {};\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = {};\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = {};\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ContractCreation = {};\nautoObject.ContractCreation.productCode = {};\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new Date();\nautoObject.ContractCreation.valueDate = new Date();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new Date();\nautoObject.Parties = {};\nautoObject.Parties.Drawer = {};\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = {};\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = {};\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = {};\nautoObject.Parties.partyTypes.partyCIF = 0;\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.ChargesAndCommissions = [];\nautoObject.ChargesAndCommissions[0] = {};\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].rate = 0.0;\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].minimumAmount = 0.0;\nautoObject.ContractLiquidation = {};\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new Date();\nautoObject.ContractLiquidation.creditValueDate = new Date();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = {};\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAmount = {};\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = [];\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = {};\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = {};\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = [];\nautoObject.attachmentDetails.attachment[0] = {};\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.initiator = \"\";\nautoObject.complianceComments = [];\nautoObject.complianceComments[0] = {};\nautoObject.complianceComments[0].startTime = new Date();\nautoObject.complianceComments[0].endTime = new Date();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = [];\nautoObject.History[0] = {};\nautoObject.History[0].startTime = new Date();\nautoObject.History[0].endTime = new Date();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = {};\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.6102a850-faf2-401e-9c7a-d8f3a6215da0"},{"itemSubjectRef":"itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e","name":"attachment","isCollection":true,"id":"2055.6c11359f-601f-4a8a-adab-80428b282316"},{"itemSubjectRef":"itm.12.b698dbfb-84da-40a5-9db3-676815055e65","name":"ECMproperties","isCollection":false,"id":"2055.3502bdff-abe8-4815-b2bb-71d90d6236ce"},{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"folderId","isCollection":false,"id":"2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"compApprovalInit","isCollection":false,"id":"2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"fromTradeFo","isCollection":false,"id":"2055.dd027994-1915-4bca-8452-c0b7021d8794"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"1.e1c94f86-a87e-4418-8c6b-8725c6ccccea"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6102a850-faf2-401e-9c7a-d8f3a6215da0</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>def18e39-b298-4c2a-adb8-31ad18dca927</guid>
            <versionId>775527d3-b5e0-4904-acbb-f2ef7326f873</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6c11359f-601f-4a8a-adab-80428b282316</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.07383e61-26ca-4c95-9b61-00fbdbc9735e</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b6701c74-76bf-4173-9f00-306ed7a06d07</guid>
            <versionId>e6a773fa-ab7e-478a-b39c-fbb920dffba0</versionId>
        </processParameter>
        <processParameter name="ECMproperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3502bdff-abe8-4815-b2bb-71d90d6236ce</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.b698dbfb-84da-40a5-9db3-676815055e65</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c24e2dbe-b5bb-432c-b807-74187b8a3909</guid>
            <versionId>a982fe26-8c17-49c8-99a0-43355b42f83d</versionId>
        </processParameter>
        <processParameter name="folderId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a598b96d-de6b-4d9e-944b-5ced3080e6c1</guid>
            <versionId>8450aadc-d751-4742-9b84-26e2920292c8</versionId>
        </processParameter>
        <processParameter name="compApprovalInit">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>59800dc3-0f46-4b86-8069-779c2d30f6af</guid>
            <versionId>4fbf8206-d563-4c70-8419-************</versionId>
        </processParameter>
        <processParameter name="fromTradeFo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dd027994-1915-4bca-8452-c0b7021d8794</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7791eea8-b8b1-4980-b712-994b62cd8854</guid>
            <versionId>99f26644-dd12-454b-a885-9a580054ba00</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0f59046c-cb7e-4393-bec7-d27739cecd93</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>daf07137-a150-4df3-a27e-c0b8b74991d8</guid>
            <versionId>c0606dc6-00ac-4d5b-8d67-7f4b59a7e68b</versionId>
        </processParameter>
        <processParameter name="attachment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f89a842a-9d83-45bf-adeb-2837b1beb056</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.07383e61-26ca-4c95-9b61-00fbdbc9735e</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9d832f8e-4cfc-4d6d-aa6c-f7d84aba6a5a</guid>
            <versionId>938f1161-e080-45cc-b3a3-2eb18df54b3d</versionId>
        </processParameter>
        <processParameter name="complianceComments">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.36778ac9-0d81-4996-991e-f909c6f6aa5a</processParameterId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.e4654440-58a7-47b2-8f98-3eaa9cccad49</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>699cc9d9-de14-4311-8ea0-95915fd4b004</guid>
            <versionId>770a50a5-593f-4dab-a7e4-5ba75dc1621d</versionId>
        </processParameter>
        <processVariable name="errorExist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3cb681c2-3b0b-4631-b001-0a2c882b9f06</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0aa13414-eb43-4f14-8233-90a6a0d2855f</guid>
            <versionId>f62df95c-56b6-4095-909a-53d0f480983d</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3039db6f-7017-4dac-804b-2c33c08ae807</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ebd25995-7b96-46e4-9a48-77b105110041</guid>
            <versionId>13f17e74-9c0b-461b-a058-7dfc18c9fea0</versionId>
        </processVariable>
        <processVariable name="documentsTypesSelected">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f250f757-7c1c-4824-a52b-c7a99c4fa5cc</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.07383e61-26ca-4c95-9b61-00fbdbc9735e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c6855578-4e30-483e-816d-7c503e1f46de</guid>
            <versionId>695ae450-c8da-47f7-828a-374f40370ecd</versionId>
        </processVariable>
        <processVariable name="validation">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d5955721-5c73-4eea-a4b2-ff7863f4acbd</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>07e8df0f-6daa-44af-bde9-97557cb319da</guid>
            <versionId>fdd3b65a-4ec6-451b-a38e-05bd1316654d</versionId>
        </processVariable>
        <processVariable name="parentRequestNoVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ccc1eddc-ac3e-4636-845d-d8899c8f3629</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bda77c28-9213-4175-aaff-abed762c173a</guid>
            <versionId>7a518011-fa43-4b90-b3c1-e75fb5694dbb</versionId>
        </processVariable>
        <processVariable name="contractStageVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a96defa1-4538-4300-87f3-ecf7b0b7cec4</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>716adec0-02e6-4fd6-9cff-735d04395434</guid>
            <versionId>6c3e7b6f-29a1-4823-af3b-82472cca1ef1</versionId>
        </processVariable>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.49e16d32-2f3b-40ab-8e9b-b210917ef3a1</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c9f66afc-b1de-41b8-85c7-8ed1859a2122</guid>
            <versionId>5deac724-d8af-4a66-97fa-699d7e7de6cc</versionId>
        </processVariable>
        <processVariable name="tradefoVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bb63e567-928d-4798-8a5d-62378b7e95f5</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d8587cf6-5948-4bb3-9ba7-bcf77c097f78</guid>
            <versionId>340ca6c8-99e7-44a3-82f4-925641c84625</versionId>
        </processVariable>
        <processVariable name="exehubMkrVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5d310b3c-3f82-443d-813d-6d276cfc9944</processVariableId>
            <description isNull="true" />
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d3d50f0f-17c8-4db9-bc14-eafbfc15207c</guid>
            <versionId>d21037fe-9b3b-49e7-8653-dbbd0b8ed6d8</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.509f906a-c362-44e5-81e5-0364f769022b</processItemId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <name>Audit ODC Request</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4e2ab6a7-bb10-424e-96f6-6a78f917d368</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:358c</guid>
            <versionId>1da744ca-be21-41ca-bc41-8f4ed36fcfa7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4e2ab6a7-bb10-424e-96f6-6a78f917d368</subProcessId>
                <attachedProcessRef>/1.7ee96dd0-834b-44cb-af41-b21585627e49</attachedProcessRef>
                <guid>74098f67-fbd5-47f9-8dc1-bc7c5c00bb1c</guid>
                <versionId>d67b7c40-fc01-4e3c-9201-ea5802b6e18c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.35d64c8d-b9be-4b4d-b426-52b3d023705d</processItemId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.08b2bc37-4ef2-4b7d-a7a5-acb27a4384c2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c3e5ad11373681e2:14b3f188:1896fd422e2:6199</guid>
            <versionId>a8a6e863-679e-4023-b99c-6dec9fb17f08</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a</processItemId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <name>Audit Request History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.288bee4c-ccd1-4142-8c22-b1f006579e4a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:358d</guid>
            <versionId>b50696ce-2ff1-4e7c-a22d-12677d57a272</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.288bee4c-ccd1-4142-8c22-b1f006579e4a</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>73b39987-d316-4719-9d9d-c1d2a3c827a2</guid>
                <versionId>2a0bf44f-4ce8-4a7e-aa38-26572d54e5c2</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f85a0204-3ac0-433d-95ae-699891512ca9</processItemId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.e86921f2-ca34-4973-9ae6-681cd4481d3c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:c3e5ad11373681e2:14b3f188:1896fd422e2:619a</guid>
            <versionId>d723eb32-288e-4336-8067-adc1675dc532</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.e86921f2-ca34-4973-9ae6-681cd4481d3c</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>ebb3195d-02e9-42f0-9a86-75473dcef6e9</guid>
                <versionId>621e70d6-23ed-4144-b04e-26d0f07f4a84</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6a84d426-165c-459f-8ceb-d6006c6ad20d</processItemId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <name>Update request state and status in DB</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d8f087aa-00df-4de1-8cd8-c2f6a6dc1b05</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aff098473ecd546d:1d42df0a:18b1b2b8841:-775c</guid>
            <versionId>e25a4065-dfe2-4821-bbc0-6cab4dceb9f1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d8f087aa-00df-4de1-8cd8-c2f6a6dc1b05</subProcessId>
                <attachedProcessRef>/1.2cab04cd-6063-4a13-b148-ec9788e07bf4</attachedProcessRef>
                <guid>39cf13e4-fb84-4d2f-9fb4-ec214de9fd84</guid>
                <versionId>b748777a-dd0a-43b0-9541-dbaf03bb4cd5</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.7e94a1be-b7aa-4d2b-95ff-f20af88548e8</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <guid>d009b1b5-2c7b-4ad0-beca-7aa8e502d430</guid>
            <versionId>846cf66f-cda8-41b3-bdf8-1e6d39111a5d</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.b1b6919b-6072-4258-94af-************</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <guid>416d3f97-c58b-4085-a21a-e01feb0bd0d0</guid>
            <versionId>8ea604fd-aca0-429c-8daf-6d9dad4dcb8f</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.1e59223b-a3d8-44e2-a32e-deca2cf021b6</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <guid>2e6165fd-a16f-4143-b887-d33ad99892b4</guid>
            <versionId>98b90e71-da67-472b-95fa-d74204e79e06</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.b06ca682-1256-4f73-bd2d-9d567ff93be8</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <guid>2000bd14-c858-4d53-96ce-95f8deda6c5d</guid>
            <versionId>9929532f-40d6-471f-813f-1925aa849c6f</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.f6e0acea-3d75-44c9-b5cd-e086fef2108f</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <guid>6ad117ad-f00b-4934-a2f1-73e55fde0dec</guid>
            <versionId>69e80b1d-8d1f-4644-8526-49e7736a608c</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.35d64c8d-b9be-4b4d-b426-52b3d023705d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="1.e1c94f86-a87e-4418-8c6b-8725c6ccccea" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:globalUserTask implementation="##unspecified" name="Trade Compliance" id="1.7276be8c-00f4-4766-949c-bcd033b050c3">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation processType="None" isClosed="false" id="7f504969-a9fd-4c9a-864d-f8d53c95e0fc">
                            
                            
                            <ns16:startEvent name="Start" id="826774b1-f9a8-4693-9cfe-1ad66c37dd65">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="50" y="188" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.f0b0fe9f-384e-4473-91b0-464179607ecc</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="826774b1-f9a8-4693-9cfe-1ad66c37dd65" targetRef="2025.335301a8-53b9-4997-b289-0e7f6704abba" name="Start To Coach" id="2027.f0b0fe9f-384e-4473-91b0-464179607ecc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.4bf97862-f836-4e06-9bfc-81c838970a74" name="Initialization Script" id="2025.335301a8-53b9-4997-b289-0e7f6704abba">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="243" y="165" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.f0b0fe9f-384e-4473-91b0-464179607ecc</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.4bf97862-f836-4e06-9bfc-81c838970a74</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.odcRequest.stepLog = {};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT07; &#xD;
&#xD;
 tw.local.odcRequest.appInfo.stepName= tw.epv.ScreenNames.CACT07;&#xD;
 tw.local.odcRequest.appInfo.appID = tw.system.processInstance.id;&#xD;
 &#xD;
 tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;&#xD;
tw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT07;&#xD;
tw.local.actionConditions.userRole= tw.local.odcRequest.initiator;&#xD;
tw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;&#xD;
&#xD;
/*Visibilty Conditions*/&#xD;
/*Basic Details CV Visibility*/&#xD;
///////////////////////////////&#xD;
if(tw.local.odcRequest.requestNature.value == "update"){&#xD;
	tw.local.parentRequestNoVIS = "READONLY";&#xD;
}	&#xD;
else{&#xD;
	tw.local.parentRequestNoVIS = "None";&#xD;
}	&#xD;
if(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){&#xD;
	tw.local.contractStageVIS = "READONLY";&#xD;
}&#xD;
else{&#xD;
tw.local.contractStageVIS = "NONE";&#xD;
}&#xD;
if(tw.local.fromTradeFo == true)&#xD;
{&#xD;
	tw.local.tradefoVis = "Editable";&#xD;
	tw.local.exehubMkrVis = "None";&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.tradefoVis = "None";&#xD;
	tw.local.exehubMkrVis = "Editable";&#xD;
}&#xD;
 </ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.02254107-cf62-4386-b74b-6666a37e9ea6" name="Set Status and substatus" id="2025.5e719de4-0f51-4f78-875d-e8b72a537cae">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="801" y="148" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.02254107-cf62-4386-b74b-6666a37e9ea6</ns16:outgoing>
                                
                                
                                <ns16:script>&#xD;
if(tw.local.compApprovalInit == tw.epv.userRole.CACT03)&#xD;
{&#xD;
	tw.local.odcRequest.stepLog.action = tw.epv.CreationActions.returnToMaker;&#xD;
	tw.local.odcRequest.appInfo.status =" In Approval ";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Waiting Trade Fo  Maker Approval";&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.odcRequest.stepLog.action = tw.epv.CreationActions.approveRequest;&#xD;
		tw.local.odcRequest.appInfo.status    = "In Execution";&#xD;
	tw.local.odcRequest.appInfo.subStatus = "Pending Execution Hub Processing";&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.335301a8-53b9-4997-b289-0e7f6704abba" targetRef="2025.19273870-c44a-41f8-83e4-94bd44345007" name="To Trade Compliance" id="2027.4bf97862-f836-4e06-9bfc-81c838970a74">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5e719de4-0f51-4f78-875d-e8b72a537cae" targetRef="2025.6a84d426-165c-459f-8ceb-d6006c6ad20d" name="To End" id="2027.02254107-cf62-4386-b74b-6666a37e9ea6">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="387" y="85" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.18112dba-24b4-45b9-998c-931522e4f8da</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.638a3b48-b2b8-425b-a512-66d9cf9a605b</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.18112dba-24b4-45b9-998c-931522e4f8da</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.19273870-c44a-41f8-83e4-94bd44345007" targetRef="2025.6125901b-f21b-438b-9845-846e04b2e62d" name="To Get Required Documents" id="2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightTop</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="1f95c90f-8b19-4dfc-9081-1fefb92374d6">
                                        
                                        
                                        <ns3:coachEventPath>ODC_Creation_Template1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.19273870-c44a-41f8-83e4-94bd44345007" targetRef="2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2" name="To Postpone" id="2027.638a3b48-b2b8-425b-a512-66d9cf9a605b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="d15989d6-b5f4-4534-b936-4e6cb29120ac">
                                        
                                        
                                        <ns3:coachEventPath>ODC_Creation_Template1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2" targetRef="2025.19273870-c44a-41f8-83e4-94bd44345007" name="To Trade Compliance" id="2027.18112dba-24b4-45b9-998c-931522e4f8da">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.39733aac-7ee5-4bc2-8ae3-11facf422a16" gatewayDirection="Unspecified" name="Have Errors" id="2025.3d21978a-5764-41b9-bf92-a51bcfc0783c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="709" y="169" width="64" height="51" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.718ac925-7d5c-4692-9a49-8441c94e6b80</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.39733aac-7ee5-4bc2-8ae3-11facf422a16</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.718ac925-7d5c-4692-9a49-8441c94e6b80" name="validation" id="2025.6125901b-f21b-438b-9845-846e04b2e62d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="560" y="150" width="95" height="70" color="#95D087" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.718ac925-7d5c-4692-9a49-8441c94e6b80</ns16:outgoing>
                                
                                
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false;&#xD;
 &#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)&#xD;
	mandatory(tw.local.odcRequest.stepLog.returnReason, "tw.local.odcRequest.stepLog.returnReason");&#xD;
	&#xD;
//-------------------------------------------------Trade Fo and compliance comment validation ------------------------------&#xD;
if(tw.local.odcRequest.complianceApproval == true)&#xD;
mandatory(tw.local.odcRequest.compcheckerComment, "tw.local.odcRequest.compcheckerComment");&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
	&#xD;
	</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.3d21978a-5764-41b9-bf92-a51bcfc0783c" targetRef="2025.5e719de4-0f51-4f78-875d-e8b72a537cae" name="no" id="2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6125901b-f21b-438b-9845-846e04b2e62d" targetRef="2025.3d21978a-5764-41b9-bf92-a51bcfc0783c" name="To Have Errors" id="2027.718ac925-7d5c-4692-9a49-8441c94e6b80">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="errorExist" id="2056.3cb681c2-3b0b-4631-b001-0a2c882b9f06" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.3039db6f-7017-4dac-804b-2c33c08ae807" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e" isCollection="true" name="documentsTypesSelected" id="2056.f250f757-7c1c-4824-a52b-c7a99c4fa5cc" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="validation" id="2056.d5955721-5c73-4eea-a4b2-ff7863f4acbd" />
                            
                            
                            <ns3:formTask name="Trade Compliance" id="2025.19273870-c44a-41f8-83e4-94bd44345007">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="368" y="165" width="95" height="70" />
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.18112dba-24b4-45b9-998c-931522e4f8da</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.4bf97862-f836-4e06-9bfc-81c838970a74</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.638a3b48-b2b8-425b-a512-66d9cf9a605b</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>804e9295-a6d8-4814-8c75-ded5d13cda77</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>ODC_Creation_Template1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>006ab33a-a3b8-4ee5-8305-1441e543a98b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>ODC Creation Template</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>194e54fe-1947-4dca-8ede-93af1121fbf7</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>f080343e-1909-41f1-8a4b-b9271db352fd</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9d93c440-8136-4604-81b3-a47ad834a6f1</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>98f98ca8-116e-4dd0-84d8-971a786f1cdb</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>eeae2370-a75b-455e-82d5-0e36ada37cb0</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>bb0aadf6-6aa3-4cee-8acf-171607e7d06f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>baa4fe56-12fe-476a-88a1-6501dcd00dcd</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9b629cbc-23a4-4a03-8270-6455bdf3d818</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>59672ff6-0244-4b90-818c-97d590125c47</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>e7e763f6-3093-4aab-8f5e-ca10fecfb022</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvalComment</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.approvalComment</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>94da15a9-9325-4e3a-8962-c67d096def46</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Editable</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>325c9ce6-6791-4935-8626-f3e7cc54881b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Readonly</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>static</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>d469bb0d-fb91-4c51-8dd6-69a491f4892f</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Readonly</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>static</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>dd0b42b4-0097-4c7c-8ed4-dec6711a0f9e</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tradeFoComment</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.tradeFoComment</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>bbb5c4d8-3490-4be7-8d15-658463baf634</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>exeHubMkrComment</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.exeHubMkrComment</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>22b5585c-4b30-4885-8c05-000ba283130b</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>compcheckerComment</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.compcheckerComment</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>b65420f1-12d6-4e0c-8bbe-765ea773067c</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>compcheckerCommentVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>Editable</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>6c7ce844-c89c-4e15-8f09-59f794ee9606</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>f6c61f00-858b-4520-8711-5a487139e947</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tabs1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>01a07a4f-74ed-4008-89f6-b6f941f1dc79</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tabs</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>f84296a9-6d7d-4ddb-807d-e6eaae1ef446</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>59ebdb84-1589-4bcd-8d1e-f6823a463245</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@width</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"99%"}]}</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>c0c066c9-2294-4106-87b2-6d8b3112e468</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>5a46ae9d-72a1-40b3-894b-5963f3933430</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>cc84854c-2026-4a3e-80ef-81384fc0003e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>82d1a57b-8783-4495-8acb-f6ff9e5e94c6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c51f9a43-e803-4766-8f79-f850b6aea066</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>785affde-d6ca-4733-87f4-432d7648950d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>30568523-626a-4a48-8346-58e2b18fc3e1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0d4bc528-b1fe-49b5-8769-a397d283f428</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.parentRequestNoVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>910fd496-ea6a-469c-87ef-205956544fd7</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.contractStageVIS</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>f1d938c2-a9be-41d5-8d97-c2cc5622a13d</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information_cv1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f617bcdb-7f12-4bbe-806e-52125ae8974b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6390e972-5962-48c0-8d54-27af1f5ba356</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b9a3ba97-842f-4845-853d-dbf1617e4e40</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9f9b6ee9-718e-449e-807b-1d3c8bf1b714</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>759f2a5b-d9d2-48f5-8d9d-c4e30842d018</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>7bcb31e8-e1e8-4427-8dce-03cf8df12623</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details_Branch_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>19e099d3-c648-42a4-878d-f530ee4296df</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3947c921-f3b8-4e49-82aa-a310e7375aac</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>fe5afcb7-8388-4935-83bd-f6dcf7773ace</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1cb9f512-1af3-4b1f-8baa-62fb6df1e619</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8f214fca-6653-4a44-86b5-78bf839c7ca6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>91f8c0f7-58f8-41d2-88ce-e383830089a6</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4b348381-cd08-4a0a-83f2-ea0b8b1b898b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>837dcd17-775c-4efe-841c-fb80fb394370</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>20aaf5df-642a-437f-8625-da456863acd4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>f7bb56ab-c9b5-480b-845d-2ad7594ec4c9</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ddb36141-5cd7-4bb0-8413-978aa7a3017e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>DC History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b5136aad-7b4e-4ad3-8296-3a79e9a668c0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b9993545-2227-48e4-8ef6-41caa27b65fd</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentRequestNoVIS" id="2056.ccc1eddc-ac3e-4636-845d-d8899c8f3629" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="contractStageVIS" id="2056.a96defa1-4538-4300-87f3-ecf7b0b7cec4" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.49e16d32-2f3b-40ab-8e9b-b210917ef3a1">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.screenName = "";
autoObject.userRole = "";
autoObject.complianceApproval = false;
autoObject.lastStepAction = "";
autoObject.subStatus = "";
autoObject</ns3:defaultValue>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:dataObject>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.26bb7a03-2343-47a3-85a9-81c05ecb5530">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="705" y="38" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.39733aac-7ee5-4bc2-8ae3-11facf422a16</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.3d21978a-5764-41b9-bf92-a51bcfc0783c" targetRef="2025.26bb7a03-2343-47a3-85a9-81c05ecb5530" name="no" id="2027.39733aac-7ee5-4bc2-8ae3-11facf422a16">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.2cab04cd-6063-4a13-b148-ec9788e07bf4" default="2027.5038ce81-b484-4111-8dd7-c85220b46cb3" name="Update request state and status in DB" id="2025.6a84d426-165c-459f-8ceb-d6006c6ad20d">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="912" y="148" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.02254107-cf62-4386-b74b-6666a37e9ea6</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.5038ce81-b484-4111-8dd7-c85220b46cb3</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.a84d91ec-e620-4417-85c4-7dd7db58ff31</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.c5194a72-2de4-483f-823c-47d5b98b572c</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.status</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.3974caa7-9f10-4f46-8275-17080a25476e</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.BasicDetails.requestState</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.b05449cf-c459-4405-809c-888b00e3e968</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.subStatus</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.b537f107-9e83-46e0-8979-6fe5796c7a7d</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.stepName</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.6a84d426-165c-459f-8ceb-d6006c6ad20d" targetRef="2025.509f906a-c362-44e5-81e5-0364f769022b" name="To End" id="2027.5038ce81-b484-4111-8dd7-c85220b46cb3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:callActivity calledElement="1.7ee96dd0-834b-44cb-af41-b21585627e49" default="2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881" name="Audit ODC Request" id="2025.509f906a-c362-44e5-81e5-0364f769022b">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1036" y="148" width="95" height="70" />
                                    
                                    
                                    <ns3:mode>InvokeService</ns3:mode>
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5038ce81-b484-4111-8dd7-c85220b46cb3</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.afad40c5-a38b-475c-8154-b4dabd94b6fe</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.254cf8eb-2743-4c53-8c52-e51c8c22884e</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:endEvent name="End" id="2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1476" y="170" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.59fef372-1bac-477d-8a36-085033334685</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.734d781b-620a-4a45-8c30-281afce1b505" name="Audit Request History" id="2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1225" y="149" width="95" height="70" />
                                    
                                    
                                    <ns3:mode>InvokeService</ns3:mode>
                                    
                                    
                                    <ns3:autoMap>true</ns3:autoMap>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.4f838403-693d-4f4a-8df1-27d6875ec647</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.734d781b-620a-4a45-8c30-281afce1b505</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.epv.userRole.CACT07</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:exclusiveGateway default="2027.d8848e36-792f-45dd-881c-90030cb18e31" name="Audited" id="2025.d17dee7b-c083-4da1-8247-34274a828f10">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1343" y="168" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.734d781b-620a-4a45-8c30-281afce1b505</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.59fef372-1bac-477d-8a36-085033334685</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.d8848e36-792f-45dd-881c-90030cb18e31</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:exclusiveGateway default="2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc" name="Audited?" id="2025.8b4a5787-28e3-49d2-896d-ef1528ec5852">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1135" y="168" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.4f838403-693d-4f4a-8df1-27d6875ec647</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1223" y="247" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.d8848e36-792f-45dd-881c-90030cb18e31</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a" targetRef="2025.d17dee7b-c083-4da1-8247-34274a828f10" name="To Audited" id="2027.734d781b-620a-4a45-8c30-281afce1b505">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d17dee7b-c083-4da1-8247-34274a828f10" targetRef="2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff" name="Yes" id="2027.59fef372-1bac-477d-8a36-085033334685">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.509f906a-c362-44e5-81e5-0364f769022b" targetRef="2025.8b4a5787-28e3-49d2-896d-ef1528ec5852" name="To Audited?" id="2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8b4a5787-28e3-49d2-896d-ef1528ec5852" targetRef="2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a" name="Yes" id="2027.4f838403-693d-4f4a-8df1-27d6875ec647">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.8b4a5787-28e3-49d2-896d-ef1528ec5852" targetRef="2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0" name="No" id="2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.d17dee7b-c083-4da1-8247-34274a828f10" targetRef="2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0" name="No" id="2027.d8848e36-792f-45dd-881c-90030cb18e31">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="tradefoVis" id="2056.bb63e567-928d-4798-8a5d-62378b7e95f5" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="exehubMkrVis" id="2056.5d310b3c-3f82-443d-813d-6d276cfc9944" />
                            
                            
                            <ns3:htmlHeaderTag id="1008d2fb-8e09-4963-8860-a910eda74e70">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="104d3792-ae24-4ba9-8c92-9945eea81cb8" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="249442ab-2ec7-4bfa-850c-96de06de549a" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="8b48a36e-e195-47ae-8108-60b7fb639e57" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="616db28e-55e0-4f9e-8b37-f39f1ccded64" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.5afdc518-a04e-4a57-8dbe-c5eeb28f9e3e</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.6102a850-faf2-401e-9c7a-d8f3a6215da0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = {};
autoObject.requestNature = {};
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = {};
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new Date();
autoObject.ImporterName = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = {};
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = {};
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = {};
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.BasicDetails = {};
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = {};
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = {};
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = {};
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = {};
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = [];
autoObject.BasicDetails.Bills[0] = {};
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();
autoObject.BasicDetails.Invoice = [];
autoObject.BasicDetails.Invoice[0] = {};
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new Date();
autoObject.GeneratedDocumentInfo = {};
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = [];
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = [];
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.Instructions = [];
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = "";
autoObject.GeneratedDocumentInfo.specialInstructions = [];
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.FinancialDetailsBR = {};
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = {};
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new Date();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = {};
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = [];
autoObject.FinancialDetailsBR.listOfAccounts[0] = {};
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = {};
autoObject.FcCollections.currency = {};
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new Date();
autoObject.FcCollections.ToDate = new Date();
autoObject.FcCollections.accountNo = {};
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = [];
autoObject.FcCollections.retrievedTransactions[0] = {};
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = [];
autoObject.FcCollections.selectedTransactions[0] = {};
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new Date();
autoObject.FcCollections.selectedTransactions[0].valueDate = new Date();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = [];
autoObject.FcCollections.listOfAccounts[0] = {};
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = {};
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new Date();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = {};
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.MultiTenorDates = [];
autoObject.MultiTenorDates[0] = {};
autoObject.MultiTenorDates[0].date = new Date();
autoObject.MultiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = {};
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = {};
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = {};
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = {};
autoObject.ProductShipmentDetails.CBECommodityClassification = {};
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new Date();
autoObject.ProductShipmentDetails.shipmentMethod = {};
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = {};
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = {};
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ContractCreation = {};
autoObject.ContractCreation.productCode = {};
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new Date();
autoObject.ContractCreation.valueDate = new Date();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new Date();
autoObject.Parties = {};
autoObject.Parties.Drawer = {};
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = {};
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = {};
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = {};
autoObject.Parties.partyTypes.partyCIF = 0;
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.ChargesAndCommissions = [];
autoObject.ChargesAndCommissions[0] = {};
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = {};
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = {};
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAmount = {};
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].rate = 0.0;
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].minimumAmount = 0.0;
autoObject.ContractLiquidation = {};
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new Date();
autoObject.ContractLiquidation.creditValueDate = new Date();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = {};
autoObject.ContractLiquidation.creditedAccount.accountClass = {};
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = {};
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAmount = {};
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = [];
autoObject.actions[0] = "";
autoObject.attachmentDetails = {};
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = {};
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = [];
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = [];
autoObject.attachmentDetails.attachment[0] = {};
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.initiator = "";
autoObject.complianceComments = [];
autoObject.complianceComments[0] = {};
autoObject.complianceComments[0].startTime = new Date();
autoObject.complianceComments[0].endTime = new Date();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = [];
autoObject.History[0] = {};
autoObject.History[0].startTime = new Date();
autoObject.History[0].endTime = new Date();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = {};
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="attachment" itemSubjectRef="itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e" isCollection="true" id="2055.6c11359f-601f-4a8a-adab-80428b282316" />
                        
                        
                        <ns16:dataInput name="ECMproperties" itemSubjectRef="itm.12.b698dbfb-84da-40a5-9db3-676815055e65" isCollection="false" id="2055.3502bdff-abe8-4815-b2bb-71d90d6236ce" />
                        
                        
                        <ns16:dataInput name="folderId" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7" />
                        
                        
                        <ns16:dataInput name="compApprovalInit" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c" />
                        
                        
                        <ns16:dataInput name="fromTradeFo" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.dd027994-1915-4bca-8452-c0b7021d8794" />
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.0f59046c-cb7e-4393-bec7-d27739cecd93" />
                        
                        
                        <ns16:dataOutput name="attachment" itemSubjectRef="itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e" isCollection="true" id="2055.f89a842a-9d83-45bf-adeb-2837b1beb056" />
                        
                        
                        <ns16:dataOutput name="complianceComments" itemSubjectRef="itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49" isCollection="true" id="2055.36778ac9-0d81-4996-991e-f909c6f6aa5a" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.81a70b0f-bcbf-48ca-a9d3-8bcd43c03ad8</processLinkId>
            <processId>1.7276be8c-00f4-4766-949c-bcd033b050c3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.35d64c8d-b9be-4b4d-b426-52b3d023705d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.f85a0204-3ac0-433d-95ae-699891512ca9</toProcessItemId>
            <guid>b131f68f-830c-4087-a2bd-8b9f9f8adcea</guid>
            <versionId>dc030826-afff-4603-9d31-63e8e3e26262</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.35d64c8d-b9be-4b4d-b426-52b3d023705d</fromProcessItemId>
            <toProcessItemId>2025.f85a0204-3ac0-433d-95ae-699891512ca9</toProcessItemId>
        </link>
    </process>
</teamworks>

