<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.c923d7fa-ce48-4122-8c3e-afbd6e8e5917" name="Parties">
        <lastModified>1690464764376</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <classId>12.c923d7fa-ce48-4122-8c3e-afbd6e8e5917</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>false</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.c923d7fa-ce48-4122-8c3e-afbd6e8e5917</externalId>
        <dependencySummary isNull="true" />
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/NBEODCR","complexType":[{"annotation":{"documentation":[{}],"appinfo":[{"shared":[false],"advancedProperties":[{}],"shadow":[false]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["drawer"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"drawer","maxOccurs":"unbounded","type":"{http:\/\/NBEODCR}PartyDetails","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.3e75c63e-2df3-4510-8895-56d6203d0609"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["Drawee"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"Drawee","maxOccurs":"unbounded","type":"{http:\/\/NBEODCR}PartyDetails","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.3e75c63e-2df3-4510-8895-56d6203d0609"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["collectingBank"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"collectingBank","maxOccurs":"unbounded","type":"{http:\/\/NBEODCR}CollectingBank","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.b80eface-d932-480c-ab0a-f072d7a8a5db"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["accounteeCaseInNeed"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"accounteeCaseInNeed","maxOccurs":"unbounded","type":"{http:\/\/NBEODCR}PartyDetails","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.3e75c63e-2df3-4510-8895-56d6203d0609"}}]},"name":"Parties"}],"id":"_12.c923d7fa-ce48-4122-8c3e-afbd6e8e5917"}</jsonData>
        <description isNull="true" />
        <guid>guid:b0773951799faebc:1cdfe656:1898795260f:37c2</guid>
        <versionId>7897d0b4-94c4-4df5-a5b7-7907a1cfb653</versionId>
        <definition>
            <property>
                <name>drawer</name>
                <description isNull="true" />
                <classRef>/12.3e75c63e-2df3-4510-8895-56d6203d0609</classRef>
                <arrayProperty>true</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>Drawee</name>
                <description isNull="true" />
                <classRef>/12.3e75c63e-2df3-4510-8895-56d6203d0609</classRef>
                <arrayProperty>true</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>collectingBank</name>
                <description isNull="true" />
                <classRef>/12.b80eface-d932-480c-ab0a-f072d7a8a5db</classRef>
                <arrayProperty>true</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>accounteeCaseInNeed</name>
                <description isNull="true" />
                <classRef>/12.3e75c63e-2df3-4510-8895-56d6203d0609</classRef>
                <arrayProperty>true</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="Parties">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name isNull="true" />
                <namespace isNull="true" />
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

