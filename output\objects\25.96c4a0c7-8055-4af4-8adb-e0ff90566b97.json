{"id": "25.96c4a0c7-8055-4af4-8adb-e0ff90566b97", "versionId": "3252646c-337c-4ca2-a04a-6f0942cdc552", "name": "", "type": "bpd", "typeName": "Business Process Definition", "details": {}, "_fullObjectData": {"teamworks": {"bpd": {"id": "25.96c4a0c7-8055-4af4-8adb-e0ff90566b97", "name": "Trade Compliance Unified subprocess", "lastModified": "1739179939907", "lastModifiedBy": "bawa<PERSON><PERSON>", "bpdId": "25.96c4a0c7-8055-4af4-8adb-e0ff90566b97", "isTrackingEnabled": "true", "isSpcEnabled": "false", "restrictedName": {"isNull": "true"}, "isCriticalPathEnabled": "false", "participantRef": {"isNull": "true"}, "businessDataParticipantRef": {"isNull": "true"}, "perfMetricParticipantRef": {"isNull": "true"}, "ownerTeamParticipantRef": {"isNull": "true"}, "timeScheduleType": {"isNull": "true"}, "timeScheduleName": {"isNull": "true"}, "timeScheduleExpression": {"isNull": "true"}, "holidayScheduleType": {"isNull": "true"}, "holidayScheduleName": {"isNull": "true"}, "holidayScheduleExpression": {"isNull": "true"}, "timezoneType": {"isNull": "true"}, "timezone": {"isNull": "true"}, "timezoneExpression": {"isNull": "true"}, "internalName": {"isNull": "true"}, "description": "", "type": "2", "rootBpdId": "25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9", "parentBpdId": "25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9", "parentFlowObjectId": "96c4a0c7-8055-4af4-8adb-e0ff90566b97", "xmlData": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "dependencySummary": {"isNull": "true"}, "jsonData": {"isNull": "true"}, "migrationData": {"isNull": "true"}, "rwfData": {"isNull": "true"}, "rwfStatus": {"isNull": "true"}, "templateId": {"isNull": "true"}, "externalId": {"isNull": "true"}, "guid": "guid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-ca5", "versionId": "3252646c-337c-4ca2-a04a-6f0942cdc552", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "BusinessProcessDiagram": {"id": "bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-142", "metadata": {"entry": {"key": "SAP_META.SHOULDRECOVER", "value": "no"}}, "name": ["Trade Compliance Unified subprocess", "Trade Compliance Unified subprocess"], "documentation": "", "dimension": {"size": {"w": "0", "h": "0"}}, "author": "bawa<PERSON><PERSON>", "isTrackingEnabled": "true", "isCriticalPathEnabled": "false", "isSpcEnabled": "false", "isDueDateEnabled": "true", "isAtRiskCalcEnabled": "true", "creationDate": "1691052736153", "modificationDate": "1739179939907", "metricSettings": {"itemType": "2"}, "instanceNameExpression": "\"Trade Compliance Unified subprocess:\" + tw.system.process.instanceId", "dueDateType": "1", "dueDateTime": "1", "dueDateTimeResolution": "2", "dueDateTimeTOD": "00:00", "officeIntegration": {"sharePointParentSiteDisabled": "true", "sharePointParentSiteName": "<#= tw.system.process.name #>", "sharePointParentSiteTemplate": "ParentSiteTemplate.stp", "sharePointWorkspaceSiteName": "<#= tw.system.process.name #> <#= tw.system.process.instanceId #>", "sharePointWorkspaceSiteDescription": "This site has been automatically generated for managing collaborations and documents \r\nfor the Lombardi TeamWorks process instance: <#= tw.system.process.name #> <#= tw.system.process.instanceId #>\r\n\r\nTeamWorks Link:  http://NBEdevBAW:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=<#= tw.system.process.instanceId #>\r\n\r\n", "sharePointWorkspaceSiteTemplate": "WorkspaceSiteTemplate.stp", "sharePointLCID": "1033"}, "timeScheduleType": "0", "holidayScheduleType": "0", "timezoneType": "0", "executionProfile": "default", "isSBOSyncEnabled": "false", "allowContentOperations": "false", "isLegacyCaseMigrated": "false", "hasCaseObjectParams": "false", "defaultPool": {"BpmnObjectId": {"id": "4f8f2faa-bc76-45fe-86e2-fd76b446f7c5"}}, "defaultInstanceUI": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9cf"}, "ownerTeamInstanceUI": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9d0"}, "simulationScenario": {"id": "bpdid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-c2c", "name": "<PERSON><PERSON><PERSON>", "simNumInstances": "100", "simMinutesBetween": "30", "maxInstances": "100", "useMaxInstances": "true", "continueFromReal": "false", "useParticipantCalendars": "false", "useDuration": "false", "duration": "86400", "startTime": "1691052736319"}, "flow": [{"id": "853aa405-4ef8-4ca4-89a1-6264a3ae8c29", "connectionType": "SequenceFlow", "name": "To Escalation email service", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c0"}}}, {"id": "700a2c9b-fbcb-49f8-8691-dd9afe661086", "connectionType": "SequenceFlow", "name": "To End Event2", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8bf"}}}, {"id": "a74def41-38fa-4044-872e-87314182eafc", "connectionType": "SequenceFlow", "name": "To ACT07- Review ODC By Compliance", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8be"}}}, {"id": "324570fb-767a-41e6-82b1-4caf0784ec32", "connectionType": "SequenceFlow", "name": "To Subprocess End", "documentation": "", "nameVisible": "false", "metricSettings": {"itemType": "32"}, "connection": {"lineType": "0", "condition": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8bd"}}}], "pool": {"id": "4f8f2faa-bc76-45fe-86e2-fd76b446f7c5", "name": "Pool", "documentation": "", "dimension": {"size": {"w": "3000", "h": "400"}}, "autoTrackingEnabled": "false", "lane": [{"id": "dee7eb59-f491-4346-a98b-08a5b3b848cb", "name": "Compliance", "height": "200", "laneColor": "0", "systemLane": "false", "attachedParticipant": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b09bd69-9498-4f11-beaf-41aba731a014", "flowObject": [{"id": "bc337dcc-6688-472a-8487-e99cca9b9d71", "componentType": "Activity", "name": "ACT07- Review ODC By Compliance", "position": {"location": {"x": "224", "y": "38"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "1", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "1", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "narrative": "", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.7276be8c-00f4-4766-949c-bcd033b050c3", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "Number(tw.epv.ODCCreationSLA.CACT07)", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "subject": " Review Request by Trade Compliance  – مراجعة طلب تحصيل مستندى تصدير من إدارة الالتزام ", "narrative": "", "forceSend": "true", "timeSchedule": "NBEWork", "timeScheduleType": "0", "timeZone": "Africa/Cairo", "timeZoneType": "0", "holidaySchedule": "NBEHoliday", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9ca", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.ODCRequest", "parameterId": "2055.6102a850-faf2-401e-9c7a-d8f3a6215da0"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c9", "name": "ECMproperties", "classId": "/12.b698dbfb-84da-40a5-9db3-676815055e65", "input": "true", "value": "tw.local.ODCRequest.attachmentDetails.ecmProperties", "parameterId": "2055.3502bdff-abe8-4815-b2bb-71d90d6236ce"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c8", "name": "folderId", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "input": "true", "value": "tw.local.ODCRequest.attachmentDetails.folderID", "parameterId": "2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c7", "name": "attachment", "classId": "/12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "array": "true", "input": "true", "value": "tw.local.ODCRequest.attachmentDetails.attachment", "parameterId": "2055.6c11359f-601f-4a8a-adab-80428b282316"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c6", "name": "compApprovalInit", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "value": "tw.local.compApprovalInit", "parameterId": "2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c5", "name": "fromTradeFo", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "input": "true", "value": "tw.local.fromTradeFo", "parameterId": "2055.dd027994-1915-4bca-8452-c0b7021d8794"}], "outputActivityParameterMapping": [{"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c4", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "value": "tw.local.ODCRequest", "parameterId": "2055.0f59046c-cb7e-4393-bec7-d27739cecd93"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c3", "name": "complianceComments", "classId": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "array": "true", "input": "true", "value": "tw.local.ODCRequest.complianceComments", "parameterId": "2055.36778ac9-0d81-4996-991e-f909c6f6aa5a"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c2", "name": "attachment", "classId": "/12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "array": "true", "input": "true", "value": "tw.local.ODCRequest.attachmentDetails.attachment", "parameterId": "2055.f89a842a-9d83-45bf-adeb-2837b1beb056"}], "laneFilter": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c1", "serviceType": "1", "teamRef": "c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b09bd69-9498-4f11-beaf-41aba731a014"}, "teamFilter": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9c0", "serviceType": "1"}}}, "inputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b5", "positionId": "leftCenter", "input": "true", "flow": {"ref": "a74def41-38fa-4044-872e-87314182eafc"}}, "outputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b4", "positionId": "rightCenter", "flow": {"ref": "324570fb-767a-41e6-82b1-4caf0784ec32"}}, "attachedEvent": {"id": "f3fc74cf-9590-49b2-8297-07492e9ce8f3", "componentType": "Event", "name": "Error", "documentation": "", "position": {"location": {"x": "0", "y": "0"}}, "positionId": "bottomCenter", "dropIconUrl": "0", "colorInput": "Color", "component": {"nameVisible": "true", "eventType": "3", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "false", "EventAction": {"id": "364c3569-8bef-4100-8997-cc68e719b1ca", "actionType": "2", "actionSubType": "0", "EventActionImplementation": {"id": "b6a28059-e9a1-48d4-8661-e1d2216f08fd", "dateType": "1", "relativeDirection": "1", "relativeTime": "0", "relativeTimeResolution": "0", "toleranceInterval": "0", "toleranceIntervalResolution": "0", "UseCalendar": "false"}}}, "outputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b2", "positionId": "bottomCenter", "flow": {"ref": "853aa405-4ef8-4ca4-89a1-6264a3ae8c29"}}, "assignment": [{"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9ba", "assignTime": "2", "to": "tw.system.step.task.id", "from": "tw.local.taskID"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b9", "assignTime": "2", "to": "\"\"", "from": "tw.local.mailTo"}]}}, {"id": "90571470-899e-44db-b0da-bb2842927ca3", "componentType": "Event", "name": "Subprocess Start", "documentation": "", "position": {"location": {"x": "25", "y": "80"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "eventType": "1", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "outputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b6", "positionId": "rightCenter", "flow": {"ref": "a74def41-38fa-4044-872e-87314182eafc"}}}, {"id": "75ad6090-c994-431b-90d3-803dc6b15c5a", "componentType": "Event", "name": "Subprocess End", "documentation": "", "position": {"location": {"x": "650", "y": "80"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b3", "positionId": "leftCenter", "input": "true", "flow": {"ref": "324570fb-767a-41e6-82b1-4caf0784ec32"}}}]}, {"id": "1a0244d0-d147-40ea-84e4-d4ff72885a2e", "name": "system", "height": "200", "laneColor": "0", "systemLane": "false", "attachedParticipant": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "flowObject": [{"id": "726c29b6-ca92-4806-8dc0-3cea258f1845", "componentType": "Activity", "name": "Escalation email service", "documentation": "Escalation mail service: <div>   this service is running when the task has delay to be done </div>", "position": {"location": {"x": "482", "y": "42"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"loopType": "0", "loopMaximum": "1", "startQuantity": "1", "isAutoflowable": "false", "MIOrdering": "1", "MIFlowCondition": "0", "cancelRemainingInstances": "false", "metricSettings": {"itemType": "4"}, "implementationType": "1", "isConditional": "false", "conditionScript": "", "bpmnTaskType": "3", "activityOptionType": "REQUIRED", "activityExecutionType": "NONE", "activityExecutionTypePreviousValue": "AUTOMATIC", "isHidden": "false", "isRepeatable": "false", "transactionalBehavior": "0", "isRobotTask": "false", "implementation": {"attachedActivityId": "/1.d7acf968-6740-4e52-b037-2049466eeeb2", "sendToType": "1", "taskRouting": "0", "dueDateType": "1", "dueDateTime": "1", "dueDateTimeResolution": "1", "dueDateTimeTOD": "00:00", "priorityType": "0", "priority": "30.30", "forceSend": "true", "timeSchedule": "(use default)", "timeScheduleType": "0", "timeZone": "(use default)", "timeZoneType": "0", "holidaySchedule": "(use default)", "holidayScheduleType": "0", "inputActivityParameterMapping": [{"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9be", "name": "odcRequest", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "input": "true", "useDefault": "true", "value": "tw.local.ODCRequest", "parameterId": "2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f"}, {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9bd", "name": "taskId", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "input": "true", "useDefault": "true", "value": "tw.local.taskID", "parameterId": "2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1"}], "laneFilter": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9bc", "serviceType": "1", "teamRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b"}, "teamFilter": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9bb", "serviceType": "1"}}}, "inputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b1", "positionId": "leftCenter", "input": "true", "flow": {"ref": "853aa405-4ef8-4ca4-89a1-6264a3ae8c29"}}, "outputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9b0", "positionId": "rightCenter", "flow": {"ref": "700a2c9b-fbcb-49f8-8691-dd9afe661086"}}}, {"id": "2285ed84-ab47-40b1-8b3e-5545fc5020f8", "componentType": "Event", "name": "End Event2", "documentation": "", "position": {"location": {"x": "662", "y": "65"}}, "dropIconUrl": "0", "colorInput": "#A5B7CD", "component": {"nameVisible": "true", "eventType": "2", "cancelActivity": "true", "repeatable": "false", "doCloseTask": "true"}, "inputPort": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9af", "positionId": "leftCenter", "input": "true", "flow": {"ref": "700a2c9b-fbcb-49f8-8691-dd9afe661086"}}}]}], "epv": {"id": "bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9cd", "epvId": "/21.dbbaa047-8f02-4397-b1b5-41f11b0256b3"}}}}}}}