{"id": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "versionId": "d3e91fab-eaa6-42c0-bc2b-1993a9797adb", "name": "Get Actions By ScreenName 2", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "name": "Get Actions By ScreenName 2", "lastModified": "1698882749973", "lastModifiedBy": "heba", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.eb1dae19-eaa0-4a23-8299-7a66ae48caec", "2025.eb1dae19-eaa0-4a23-8299-7a66ae48caec"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.09e628df-d4ba-408b-b61b-83722a082cca", "2025.09e628df-d4ba-408b-b61b-83722a082cca"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "d6d7ce05-5518-4080-96fc-a876a00d5fa5", "versionId": "d3e91fab-eaa6-42c0-bc2b-1993a9797adb", "dependencySummary": "<dependencySummary id=\"bpdid:ca84402e5fd92838:-7c46c9a3:18b8d18087b:-73f8\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"3ddcc5e2-55a4-430b-9670-486f052fdded\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":99,\"y\":160,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"41718139-7b6e-4468-8396-607a3e22495d\"},{\"incoming\":[\"cc96dfc2-7cef-4dac-8a48-29833afae4e5\",\"a1807b0c-f648-4a25-8cb9-cc96d09d8874\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":590,\"y\":160,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-599a\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"73414ba8-e14a-4008-86f8-697d960a169b\"},{\"incoming\":[\"7472591e-b20b-469e-b2e3-600ef9df1153\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"369b8f67-4716-4689-ab61-c981bee6529f\",\"otherAttributes\":{\"eventImplId\":\"237c7b4c-47cb-4452-8547-9ae3d510e70f\"}}],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":24,\"x\":901,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"preAssignmentScript\":[\"log.info(\\\"*====================== ODC =========================*\\\");\\r\\nlog.info(\\\"[Get Actions By ScreenName -> Log Error ]- START\\\");\\r\\nlog.info(\\\"Process Instance ID:\\\" +tw.system.currentProcessInstanceID + \\\" Error Message: \\\" + tw.system.error.toString(true));\\r\\n\\r\\ntw.local.error = new tw.object.AjaxError();\\r\\nlog.info(\\\"[Get Actions By ScreenName -> Log Error ]- END\\\");\\r\\nlog.info(\\\"*=======================================================*\\\");\\r\\n\\r\\ntw.local.error.errorText = tw.local.errorMsg;\"]},\"name\":\"End Event\",\"dataInputAssociation\":[{\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}]}],\"declaredType\":\"endEvent\",\"id\":\"09e628df-d4ba-408b-b61b-83722a082cca\"},{\"parallelMultiple\":false,\"outgoing\":[\"7472591e-b20b-469e-b2e3-600ef9df1153\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"58280f62-59c6-4438-bcce-8489143934f8\",\"otherAttributes\":{\"eventImplId\":\"23e3545e-6881-4f89-8aaa-9fa396b010c9\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":898,\"y\":96,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"d253d430-6035-48df-b73f-ee2133921481\"},{\"targetRef\":\"09e628df-d4ba-408b-b61b-83722a082cca\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"7472591e-b20b-469e-b2e3-600ef9df1153\",\"sourceRef\":\"d253d430-6035-48df-b73f-ee2133921481\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"action\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.*************-468e-a3d2-49a84496ce7b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"screenName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9d473f4e-fbce-4dd8-876b-73fe1e1e76fd\"},{\"targetRef\":\"eb1dae19-eaa0-4a23-8299-7a66ae48caec\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exclusive Gateway\",\"declaredType\":\"sequenceFlow\",\"id\":\"3ddcc5e2-55a4-430b-9670-486f052fdded\",\"sourceRef\":\"41718139-7b6e-4468-8396-607a3e22495d\"},{\"startQuantity\":1,\"outgoing\":[\"cc96dfc2-7cef-4dac-8a48-29833afae4e5\"],\"incoming\":[\"ff3e0f0a-2d00-4de3-816d-e666551b9332\"],\"extensionElements\":{\"postAssignmentScript\":[\"tw.local.results = tw.local.actions;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":432,\"y\":137,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Action List\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"f64b170f-b2e6-479e-8608-f1058c6779f1\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\ntw.local.actions =[];\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*------------------------------------ Set Action List For Create ODC Process -----------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n \\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT01;\\r\\n\\/\\/Set Actions for Act01\\t\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n\\tif(tw.local.conditions.userRole == tw.epv.userRole.hub)\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequestToHubDirectory+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\\\"\\\";\\r\\n\\t}\\r\\n\\telse if(tw.local.conditions.userRole == tw.epv.userRole.branch)\\r\\n\\t{\\r\\n\\t \\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\\\"\\\";\\r\\n\\t}\\r\\n}\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT02 + \\\"\\\";\\r\\n\\/\\/Set Actions for Act02\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n\\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\\r\\n\\t{\\r\\n\\t \\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\\\"\\\";\\r\\n\\t\\t\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\t}\\r\\n }\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT03;\\r\\n\\/\\/Set Actions for Act03\\r\\nif(tw.local.conditions.screenName == tw.local.screenName) \\r\\n{\\r\\n\\tif(tw.local.conditions.complianceApproval){\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\\\"\\\";\\r\\n\\t}\\r\\n\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\\\"\\\"; \\r\\n\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT04;\\r\\n\\/\\/Set Actions for Act04\\r\\n\\r\\nif(tw.local.conditions.screenName == tw.local.screenName)\\r\\n{\\r\\n\\tif(tw.local.conditions.complianceApproval == true)\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\\\"\\\";\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\tif(tw.local.conditions.requestType == tw.epv.RequestType.Create)\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.createContract+\\\"\\\";\\r\\n\\t\\r\\n\\tif(tw.local.conditions.requestType == tw.epv.RequestType.Amendment)\\r\\n\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.amendContract+\\\"\\\";\\t\\r\\n\\t\\r\\n\\tif(tw.local.conditions.requestType == tw.epv.RequestType.Recreate)\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.recreateContract+\\\"\\\";\\r\\n\\t\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.terminateRequest+\\\"\\\";\\r\\n\\t}\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT05;\\r\\n\\/\\/Set Actions for Act05\\r\\nif(tw.local.conditions.screenName == tw.local.screenName)\\r\\n{\\r\\n\\tif(tw.local.conditions.complianceApproval == true)\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t}\\r\\n\\r\\n\\telse if(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToTradeFo)\\r\\n\\t{\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t\\r\\n\\t}\\r\\n\\telse if(tw.local.conditions.lastStepAction == tw.epv.CreationActions.terminateRequest)\\r\\n\\t{\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t\\r\\n\\t}\\r\\n\\telse \\r\\n\\t{\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.authorize+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t\\r\\n\\t}\\r\\n}\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT06;\\r\\n\\/\\/Set Actions for Act06\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.completeRequest+\\\"\\\";\\r\\n}\\r\\n\\/\\/Set Actions for ACT07 \\/\\/ Subprocess unified trade  FO subprocess\\r\\n\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT07;\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n\\r\\n\\ttw.local.actions[tw.local.actions.listLength] =tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*------------------------------------ Set Action List For Collection ODC Process ---------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/\\/screen01\\r\\nif(tw.local.conditions.screenName == (tw.epv.Col_ScreenNames.ODCCol01+\\\"\\\") ){\\r\\n\\t\\r\\n\\tif( tw.local.conditions.isLiquidated)\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.submitLiq+\\\"\\\";\\r\\n\\telse\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.createLiq+\\\"\\\";\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.cancel+\\\"\\\";\\r\\n}\\r\\n\\/\\/screen02\\r\\nif(tw.local.conditions.screenName == (tw.epv.Col_ScreenNames.ODCCol02+\\\"\\\") ){\\r\\n\\t\\r\\n\\tif(tw.local.conditions.subStatus==\\\"Pending Execution Hub Liquidation Review\\\")\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.authorize+\\\"\\\";\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.returnToMaker+\\\"\\\";\\r\\n\\r\\n\\tif(tw.local.conditions.subStatus==\\\"Pending Cancelation Confirmation\\\")\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.cancel+\\\"\\\";\\r\\n}\\r\\n\\/************************************************************************************************************************\\r\\n\\/*------------------------------------ Set Action List For Reversal ODC Process ---------------------------------------\\r\\n************************************************************************************************************************\\/ \\r\\ntw.local.screenName = tw.epv.ScreenNames.RACT01;\\r\\n\\/\\/Set Actions for Act01\\t\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n\\t\\r\\n\\t \\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\\\"\\\";\\r\\n}\\t\\r\\n\\/\\/\\/Set actions for reversal  act2\\r\\nif(tw.local.conditions.screenName == tw.epv.ScreenNames.RACT02 )\\r\\n{\\r\\n\\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\\r\\n\\t{\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\\\"\\\";\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\t}\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\/************************************************************************************************************************\\r\\n\\/*------------------------------------ Set Action List For closure ODC Process ---------------------------------------\\r\\n************************************************************************************************************************\\/ \\r\\ntw.local.screenName = tw.epv.ScreenNames.closureACT01;\\r\\nif(tw.local.screenName == tw.local.conditions.screenName)\\r\\n{\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.submitRequest+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\\\"\\\";\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.closureACT02+\\\"\\\";\\r\\nif(tw.local.screenName == tw.local.conditions.screenName)\\r\\n{\\r\\n\\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\\r\\n\\t{\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\\\"\\\";\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\t}\\r\\n}\\r\\ntw.local.screenName = tw.epv.ScreenNames.closureACT03;\\r\\nif(tw.local.screenName == tw.local.conditions.screenName)\\r\\n{\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\t\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.terminateRequest+\\\"\\\";\\r\\n}\\r\\ntw.local.screenName = tw.epv.ScreenNames.closureACT04;\\r\\nif(tw.local.screenName == tw.local.conditions.screenName)\\r\\n{\\r\\n\\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.terminateRequest)\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.terminateRequest+\\\"\\\";\\r\\n\\t}\\r\\n\\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToInitiator)\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\\\"\\\";\\r\\n\\t}\\r\\n\\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.approveRequest)\\r\\n\\t{\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t}\\r\\n\\t\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.closureACT05+\\\"\\\";\\r\\nif(tw.local.screenName == tw.local.conditions.screenName)\\r\\n{\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToTradeFo+\\\"\\\";\\r\\n}\\r\\ntw.local.screenName = tw.epv.ScreenNames.closureACT06;\\r\\nif(tw.local.screenName == tw.local.conditions.screenName)\\r\\n{\\r\\n\\t\\r\\n\\t\\r\\n\\t\\r\\n\\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToTradeFo)\\r\\n\\t{\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToTradeFo+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\\\"\\\";\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\\\"\\\";\\r\\n\\t}\\r\\n}\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\/****************************************************************************\\/\\r\\n\\r\\ntw.local.results = tw.local.actions;\"]}},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject[0] = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"actions\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.a170470d-a4b8-455a-8a83-a95e87a15113\"},{\"targetRef\":\"f64b170f-b2e6-479e-8608-f1058c6779f1\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"ff3e0f0a-2d00-4de3-816d-e666551b9332\",\"sourceRef\":\"*************-43f2-8589-85728a5dc2f7\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.ActionConditions();\\nautoObject.screenName = \\\"\\\";\\nautoObject.userRole = \\\"\\\";\\nautoObject.complianceApproval = false;\\nautoObject.lastStepAction = \\\"\\\";\\nautoObject.subStatus = \\\"\\\";\\nautoObject.requestType = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"conditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b42fa23f-7837-4f83-89d3-000b7e778d9f\"},{\"startQuantity\":1,\"outgoing\":[\"ff3e0f0a-2d00-4de3-816d-e666551b9332\"],\"incoming\":[\"6eaadde4-352f-41eb-8ab6-78629af8518a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":305,\"y\":137,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Set Conditions\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"*************-43f2-8589-85728a5dc2f7\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"try {\\r\\nvar jsonObj = JSON.parse(tw.local.data);\\r\\n\\ttw.local.conditions.screenName = jsonObj.screenName;\\r\\n\\ttw.local.conditions.userRole   = jsonObj.userRole;\\r\\n\\ttw.local.conditions.lastStepAction= jsonObj.lastStepAction;\\r\\n\\ttw.local.conditions.complianceApproval= jsonObj.complianceApproval;\\r\\n\\ttw.local.conditions.subStatus= jsonObj.subStatus;\\r\\n\\ttw.local.conditions.requestType =jsonObj.requestType;\\r\\n\\ttw.local.conditions.isLiquidated =jsonObj.isLiquidated;\\r\\n\\t\\t\\r\\n} catch (err) {\\r\\n\\tlog.info(\\\"ODC: Get Action By ScreenName Service: no data found in conditions list \\\")\\r\\n}\\r\\n\"]}},{\"targetRef\":\"73414ba8-e14a-4008-86f8-697d960a169b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"cc96dfc2-7cef-4dac-8a48-29833afae4e5\",\"sourceRef\":\"f64b170f-b2e6-479e-8608-f1058c6779f1\"},{\"outgoing\":[\"6eaadde4-352f-41eb-8ab6-78629af8518a\",\"a1807b0c-f648-4a25-8cb9-cc96d09d8874\"],\"incoming\":[\"3ddcc5e2-55a4-430b-9670-486f052fdded\"],\"default\":\"6eaadde4-352f-41eb-8ab6-78629af8518a\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":141,\"y\":156,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"eb1dae19-eaa0-4a23-8299-7a66ae48caec\"},{\"targetRef\":\"*************-43f2-8589-85728a5dc2f7\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Set Action List\",\"declaredType\":\"sequenceFlow\",\"id\":\"6eaadde4-352f-41eb-8ab6-78629af8518a\",\"sourceRef\":\"eb1dae19-eaa0-4a23-8299-7a66ae48caec\"},{\"targetRef\":\"73414ba8-e14a-4008-86f8-697d960a169b\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"!tw.local.data\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"a1807b0c-f648-4a25-8cb9-cc96d09d8874\",\"sourceRef\":\"eb1dae19-eaa0-4a23-8299-7a66ae48caec\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.62be6d5b-855f-4503-8287-e00a19b848fc\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b56bc1a1-1124-4135-8472-d86060e909cb\"}],\"laneSet\":[{\"id\":\"19ff87d9-a521-48dc-8e53-589a83bbdd2e\",\"lane\":[{\"flowNodeRef\":[\"41718139-7b6e-4468-8396-607a3e22495d\",\"73414ba8-e14a-4008-86f8-697d960a169b\",\"d253d430-6035-48df-b73f-ee2133921481\",\"09e628df-d4ba-408b-b61b-83722a082cca\",\"f64b170f-b2e6-479e-8608-f1058c6779f1\",\"*************-43f2-8589-85728a5dc2f7\",\"eb1dae19-eaa0-4a23-8299-7a66ae48caec\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"889e124f-9763-4244-840e-6a155992b850\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get Actions By ScreenName 2\",\"declaredType\":\"process\",\"id\":\"1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"results\",\"isCollection\":true,\"id\":\"2055.23dbb134-6c18-443b-b36d-def23540d7d8\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"4b8a14cf-8ce2-4f5e-89be-8226e9c5c412\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"8ed638eb-816d-4ded-8a8a-844a44c309de\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"13be8547-da6c-4d7c-877d-371d69fa51e9\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd\",\"epvProcessLinkId\":\"44984d48-595a-46b7-8699-7b3617b84590\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.e22cd1cb-4788-4edd-beb4-825e8f27f335\",\"epvProcessLinkId\":\"6e975c75-3cac-43a7-881c-ab14875e7cc7\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"7cc8a0b7-c4a1-4a64-8e01-c9ec98541625\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.dab3e84e-2056-4fcc-8ee5-0748f03cbf1d\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.23dbb134-6c18-443b-b36d-def23540d7d8\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"null\"}]},\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"documentation\":[{\"content\":[\"\\/\\/&quot;screenName,userRole,complianceApproval,lastAction&quot;\"],\"textFormat\":\"text\\/plain\"}],\"name\":\"data\",\"isCollection\":false,\"id\":\"2055.dab3e84e-2056-4fcc-8ee5-0748f03cbf1d\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.dab3e84e-2056-4fcc-8ee5-0748f03cbf1d", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": "//&quot;screenName,userRole,complianceApproval,lastAction&quot;", "guid": "bbc9ba6e-2b24-4e05-b305-bab87765775f", "versionId": "a49ae21d-81c6-4e3d-ad77-cbe4e30d02e3"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.23dbb134-6c18-443b-b36d-def23540d7d8", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "parameterType": "2", "isArrayOf": "true", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "52abd12e-7881-4abf-9f0c-d35a0417ffe8", "versionId": "615fd21d-13f5-492b-85a5-fa2b03b607e7"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c8e8471e-59b8-436e-a269-994fa6a65c31", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "parameterType": "3", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9c3bec94-7484-4213-830f-584f43b7f936", "versionId": "f9353dc9-725b-44bc-b9d9-e8b404613ad8"}], "processVariable": [{"name": "action", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.*************-468e-a3d2-49a84496ce7b", "description": {"isNull": "true"}, "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a2161dc7-1e0d-4413-af2c-f98e4ced40b5", "versionId": "71a408ab-366e-4eaa-b049-640519c77dbe"}, {"name": "screenName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9d473f4e-fbce-4dd8-876b-73fe1e1e76fd", "description": {"isNull": "true"}, "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "201dac3c-e0a5-4ce2-8cca-fa4b5cf494b1", "versionId": "2c5f9867-2eaf-427a-a314-2893ec4dc8fc"}, {"name": "actions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a170470d-a4b8-455a-8a83-a95e87a15113", "description": {"isNull": "true"}, "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0d71fffd-d5f2-42ec-8af1-1d3ca4b3b634", "versionId": "45a526be-f15d-4ba9-bd83-be911a2aea84"}, {"name": "conditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b42fa23f-7837-4f83-89d3-000b7e778d9f", "description": {"isNull": "true"}, "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.ActionConditions();\r\nautoObject.screenName = \"\";\r\nautoObject.userRole = \"\";\r\nautoObject.complianceApproval = false;\r\nautoObject.lastStepAction = \"\";\r\nautoObject.subStatus = \"\";\r\nautoObject.requestType = \"\";\r\nautoObject", "guid": "a08ae660-2bf1-47e0-80e0-82802a5abf89", "versionId": "586ced87-0197-4271-8c94-ecd8cf1b2cc9"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.62be6d5b-855f-4503-8287-e00a19b848fc", "description": {"isNull": "true"}, "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "44d764ec-91c3-4dd4-ad27-bd8916f4315e", "versionId": "52778fbe-71bc-49e2-a7c2-586254e72f68"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b56bc1a1-1124-4135-8472-d86060e909cb", "description": {"isNull": "true"}, "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "31955c66-bfe0-4c5f-9547-ee8dbe4ebe5c", "versionId": "acdbccc0-41eb-4f2b-9e0a-3d911dbe610f"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f64b170f-b2e6-479e-8608-f1058c6779f1", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "name": "Set Action List", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.929f5759-00bc-4bbb-88d8-e01414437ee7", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-5846", "versionId": "3b5aca9f-46b0-479e-a233-65c4737cef53", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.ad59c169-a144-4d1f-989b-86ed118d8af0", "processItemId": "2025.f64b170f-b2e6-479e-8608-f1058c6779f1", "location": "2", "script": "tw.local.results = tw.local.actions;", "guid": "e54b51a4-ec21-4977-9be2-18072a94a206", "versionId": "e745db02-5e4b-4c29-9ab1-e0213af73a64"}, "layoutData": {"x": "432", "y": "137", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.929f5759-00bc-4bbb-88d8-e01414437ee7", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\ntw.local.actions =[];\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For Create ODC Process -----------------------------------------\r\r\n************************************************************************************************************************/\r\r\n \r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT01;\r\r\n//Set Actions for Act01\t\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\tif(tw.local.conditions.userRole == tw.epv.userRole.hub)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequestToHubDirectory+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n\telse if(tw.local.conditions.userRole == tw.epv.userRole.branch)\r\r\n\t{\r\r\n\t \ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT02 + \"\";\r\r\n//Set Actions for Act02\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\r\r\n\t{\r\r\n\t \ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t\t\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n }\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT03;\r\r\n//Set Actions for Act03\r\r\nif(tw.local.conditions.screenName == tw.local.screenName) \r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval){\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\"\";\r\r\n\t}\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\"\"; \r\r\n\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT04;\r\r\n//Set Actions for Act04\r\r\n\r\r\nif(tw.local.conditions.screenName == tw.local.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval == true)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\tif(tw.local.conditions.requestType == tw.epv.RequestType.Create)\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.createContract+\"\";\r\r\n\t\r\r\n\tif(tw.local.conditions.requestType == tw.epv.RequestType.Amendment)\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.amendContract+\"\";\t\r\r\n\t\r\r\n\tif(tw.local.conditions.requestType == tw.epv.RequestType.Recreate)\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.recreateContract+\"\";\r\r\n\t\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT05;\r\r\n//Set Actions for Act05\r\r\nif(tw.local.conditions.screenName == tw.local.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval == true)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n\r\r\n\telse if(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToTradeFo)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n\t}\r\r\n\telse if(tw.local.conditions.lastStepAction == tw.epv.CreationActions.terminateRequest)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n\t}\r\r\n\telse \r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.authorize+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n\t}\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT06;\r\r\n//Set Actions for Act06\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.completeRequest+\"\";\r\r\n}\r\r\n//Set Actions for ACT07 // Subprocess unified trade  FO subprocess\r\r\n\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT07;\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\r\r\n\ttw.local.actions[tw.local.actions.listLength] =tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For Collection ODC Process ---------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//screen01\r\r\nif(tw.local.conditions.screenName == (tw.epv.Col_ScreenNames.ODCCol01+\"\") ){\r\r\n\t\r\r\n\tif( tw.local.conditions.isLiquidated)\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.submitLiq+\"\";\r\r\n\telse\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.createLiq+\"\";\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.cancel+\"\";\r\r\n}\r\r\n//screen02\r\r\nif(tw.local.conditions.screenName == (tw.epv.Col_ScreenNames.ODCCol02+\"\") ){\r\r\n\t\r\r\n\tif(tw.local.conditions.subStatus==\"Pending Execution Hub Liquidation Review\")\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.authorize+\"\";\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.returnToMaker+\"\";\r\r\n\r\r\n\tif(tw.local.conditions.subStatus==\"Pending Cancelation Confirmation\")\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.cancel+\"\";\r\r\n}\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For Reversal ODC Process ---------------------------------------\r\r\n************************************************************************************************************************/ \r\r\ntw.local.screenName = tw.epv.ScreenNames.RACT01;\r\r\n//Set Actions for Act01\t\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\t\r\r\n\t \ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n}\t\r\r\n///Set actions for reversal  act2\r\r\nif(tw.local.conditions.screenName == tw.epv.ScreenNames.RACT02 )\r\r\n{\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For closure ODC Process ---------------------------------------\r\r\n************************************************************************************************************************/ \r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT01;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.submitRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\"\";\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT02+\"\";\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT03;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.terminateRequest+\"\";\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT04;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.terminateRequest)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\t}\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToInitiator)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.approveRequest)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n\t\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT05+\"\";\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT06;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\t\r\r\n\t\r\r\n\t\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToTradeFo)\r\r\n\t{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n/****************************************************************************/\r\r\n\r\r\ntw.local.results = tw.local.actions;", "isRule": "false", "guid": "7466c497-64ff-44dc-aaae-daac53e62c91", "versionId": "4307fbf7-0ca8-43db-8a36-011eeade360c"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.73414ba8-e14a-4008-86f8-697d960a169b", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.cfbeb335-c84a-49f3-b785-4232d0647dd1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-599a", "versionId": "811fce79-69aa-43c0-9968-265987f31e05", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "590", "y": "160", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.cfbeb335-c84a-49f3-b785-4232d0647dd1", "haltProcess": "false", "guid": "0d01303a-bbde-498a-a758-4db02a450e02", "versionId": "aa66735e-7342-4ae2-851e-ef9b3b8b6454"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.*************-43f2-8589-85728a5dc2f7", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "name": "Set Conditions", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.f728c544-ac57-4275-b505-2a92760f8b10", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189daab98ba:9ef", "versionId": "8bf3cbf1-69aa-47e3-aa93-5df353e250cc", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.0daea4e9-34d1-4029-9f05-53305133326a", "processItemId": "2025.*************-43f2-8589-85728a5dc2f7", "location": "1", "script": {"isNull": "true"}, "guid": "9e281826-769e-4af7-9a7e-c36624745888", "versionId": "040daddb-3b98-470b-9731-ca8e6ae14c9d"}, "layoutData": {"x": "305", "y": "137", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.f728c544-ac57-4275-b505-2a92760f8b10", "scriptTypeId": "2", "isActive": "true", "script": "try {\r\r\nvar jsonObj = JSON.parse(tw.local.data);\r\r\n\ttw.local.conditions.screenName = jsonObj.screenName;\r\r\n\ttw.local.conditions.userRole   = jsonObj.userRole;\r\r\n\ttw.local.conditions.lastStepAction= jsonObj.lastStepAction;\r\r\n\ttw.local.conditions.complianceApproval= jsonObj.complianceApproval;\r\r\n\ttw.local.conditions.subStatus= jsonObj.subStatus;\r\r\n\ttw.local.conditions.requestType =jsonObj.requestType;\r\r\n\ttw.local.conditions.isLiquidated =jsonObj.isLiquidated;\r\r\n\t\t\r\r\n} catch (err) {\r\r\n\tlog.info(\"ODC: Get Action By ScreenName Service: no data found in conditions list \")\r\r\n}\r\r\n", "isRule": "false", "guid": "f1e09235-51de-4fe2-a7af-5c7cbaaec2bb", "versionId": "30f48c9e-8d73-4634-b5b1-ad5eb80083a7"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.09e628df-d4ba-408b-b61b-83722a082cca", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.8104e087-5794-4cf1-9ff3-6099660db851", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-599c", "versionId": "a387176c-4c80-493f-8d2d-794fbe0b5938", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.47878b29-90c1-4ebc-8d6e-1be36a3f1a70", "processItemId": "2025.09e628df-d4ba-408b-b61b-83722a082cca", "location": "1", "script": "log.info(\"*====================== ODC =========================*\");\r\r\nlog.info(\"[Get Actions By ScreenName -> Log Error ]- START\");\r\r\nlog.info(\"Process Instance ID:\" +tw.system.currentProcessInstanceID + \" Error Message: \" + tw.system.error.toString(true));\r\r\n\r\r\ntw.local.error = new tw.object.AjaxError();\r\r\nlog.info(\"[Get Actions By ScreenName -> Log Error ]- END\");\r\r\nlog.info(\"*=======================================================*\");\r\r\n\r\r\ntw.local.error.errorText = tw.local.errorMsg;", "guid": "ea22dd12-5163-42b2-8690-47dd5cc5cbd7", "versionId": "4637bfd3-2bc7-4c1b-955c-d7e14167342c"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.2d3e513c-ce69-495e-bad0-b750ac7e2029", "processItemId": "2025.09e628df-d4ba-408b-b61b-83722a082cca", "location": "2", "script": {"isNull": "true"}, "guid": "40ee45c2-4ca3-4b5a-a771-47672082348f", "versionId": "6428b672-f349-45c6-8c2b-7e2f8bc0d8c1"}], "layoutData": {"x": "901", "y": "175", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.8104e087-5794-4cf1-9ff3-6099660db851", "message": "", "faultStyle": "1", "guid": "8b9107c4-9b34-48b4-98b6-e54e024e9a78", "versionId": "29a3d086-ab83-4be5-9792-f0a9e9b03e40", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4e020207-5f9e-4bb5-9455-182318d5bd5e", "processParameterId": "2055.c8e8471e-59b8-436e-a269-994fa6a65c31", "parameterMappingParentId": "3007.8104e087-5794-4cf1-9ff3-6099660db851", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "aa12552d-135c-4a1c-8221-a97645fb00ae", "versionId": "f373ef82-2af6-4ecb-99a2-15c4da8db773", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.eb1dae19-eaa0-4a23-8299-7a66ae48caec", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "name": "Exclusive Gateway", "tWComponentName": "Switch", "tWComponentId": "3013.aa0ca8c4-de99-4c08-a915-1d8afe097dc2", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189defc53f4:-4f74", "versionId": "cf8c29ee-aa4f-4c94-81e7-d5c50a129548", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "141", "y": "156", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.aa0ca8c4-de99-4c08-a915-1d8afe097dc2", "guid": "ceb15461-6044-4236-90b8-3bf5aa578f38", "versionId": "815c1a38-abe1-4091-af14-4f005700e98a", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.49ad04ec-82fa-47ba-9760-4f1e6756b04a", "switchId": "3013.aa0ca8c4-de99-4c08-a915-1d8afe097dc2", "seq": "1", "endStateId": "guid:ca84402e5fd92838:-7c46c9a3:18b8d18087b:-73f9", "condition": "!tw.local.data", "guid": "d9a16814-3104-4c1d-bbdb-883b41443149", "versionId": "508e03c0-b684-4e49-b164-6331fc6ae4a1"}}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.eb01d8a3-564e-4e3c-8be2-fa94a07c621d", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "guid": "dc0961e8-aa65-40c9-8621-e1662404da63", "versionId": "64f1d5ec-da68-4b01-8eb8-8c483e49dc0e"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.a6d33c9c-16b1-4a7e-9ef2-cbd4589d3d5a", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "guid": "8af47388-d50d-4741-bab3-af14cbf40019", "versionId": "6eb34355-1058-4523-b02e-14081c0af3e2"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.a1660f5a-f695-441a-b91a-723db57f9d64", "epvId": "/21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "guid": "07f31473-fbf5-4de6-8a3c-5985cd364747", "versionId": "85f62c5d-224d-4804-bbbe-bb35c4304ac3"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.0d3892fb-de22-4569-8118-f5f11751214c", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "guid": "df189315-1300-4bd9-bedd-92ccdd4d8e54", "versionId": "9ee75647-e861-4578-a86d-528b6ace751e"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.9c795761-7331-4386-83ea-fb60600640d1", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "guid": "91de6462-**************-2bfd1207370a", "versionId": "e128e986-516d-4313-b369-296aa01789ac"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.465ed70b-072e-4eec-a413-a496e41e1e43", "epvId": "/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "guid": "e400e55c-96df-4a35-befa-be20e5038854", "versionId": "e86aa8d1-d7bb-485d-9179-eae58c9ad468"}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "99", "y": "160", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "898", "y": "96", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Actions By ScreenName 2", "id": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "4b8a14cf-8ce2-4f5e-89be-8226e9c5c412"}, {"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "8ed638eb-816d-4ded-8a8a-844a44c309de"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "13be8547-da6c-4d7c-877d-371d69fa51e9"}, {"epvId": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "epvProcessLinkId": "44984d48-595a-46b7-8699-7b3617b84590"}, {"epvId": "21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "epvProcessLinkId": "6e975c75-3cac-43a7-881c-ab14875e7cc7"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "7cc8a0b7-c4a1-4a64-8e01-c9ec98541625"}]}}, "ns16:dataInput": {"name": "data", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.dab3e84e-2056-4fcc-8ee5-0748f03cbf1d", "ns16:documentation": {"_": "//&quot;screenName,userRole,complianceApproval,lastAction&quot;", "textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:defaultValue": {"_": "null", "useDefault": "false"}}}, "ns16:dataOutput": {"name": "results", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "true", "id": "2055.23dbb134-6c18-443b-b36d-def23540d7d8"}, "ns16:inputSet": {"ns16:dataInputRefs": "2055.dab3e84e-2056-4fcc-8ee5-0748f03cbf1d"}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.23dbb134-6c18-443b-b36d-def23540d7d8"}}, "ns16:laneSet": {"id": "19ff87d9-a521-48dc-8e53-589a83bbdd2e", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "889e124f-9763-4244-840e-6a155992b850", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["41718139-7b6e-4468-8396-607a3e22495d", "73414ba8-e14a-4008-86f8-697d960a169b", "d253d430-6035-48df-b73f-ee2133921481", "09e628df-d4ba-408b-b61b-83722a082cca", "f64b170f-b2e6-479e-8608-f1058c6779f1", "*************-43f2-8589-85728a5dc2f7", "eb1dae19-eaa0-4a23-8299-7a66ae48caec"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "41718139-7b6e-4468-8396-607a3e22495d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "99", "y": "160", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "3ddcc5e2-55a4-430b-9670-486f052fdded"}, "ns16:endEvent": [{"name": "End", "id": "73414ba8-e14a-4008-86f8-697d960a169b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "590", "y": "160", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:-599a"}, "ns16:incoming": ["cc96dfc2-7cef-4dac-8a48-29833afae4e5", "a1807b0c-f648-4a25-8cb9-cc96d09d8874"]}, {"name": "End Event", "id": "09e628df-d4ba-408b-b61b-83722a082cca", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "901", "y": "175", "width": "24", "height": "24"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": "log.info(\"*====================== ODC =========================*\");\r\r\nlog.info(\"[Get Actions By ScreenName -> Log Error ]- START\");\r\r\nlog.info(\"Process Instance ID:\" +tw.system.currentProcessInstanceID + \" Error Message: \" + tw.system.error.toString(true));\r\r\n\r\r\ntw.local.error = new tw.object.AjaxError();\r\r\nlog.info(\"[Get Actions By ScreenName -> Log Error ]- END\");\r\r\nlog.info(\"*=======================================================*\");\r\r\n\r\r\ntw.local.error.errorText = tw.local.errorMsg;"}, "ns16:incoming": "7472591e-b20b-469e-b2e3-600ef9df1153", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:errorEventDefinition": {"id": "369b8f67-4716-4689-ab61-c981bee6529f", "eventImplId": "237c7b4c-47cb-4452-8547-9ae3d510e70f", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:intermediateCatchEvent": {"parallelMultiple": "false", "name": "Error Event", "id": "d253d430-6035-48df-b73f-ee2133921481", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "898", "y": "96", "width": "24", "height": "24"}}, "ns16:outgoing": "7472591e-b20b-469e-b2e3-600ef9df1153", "ns16:errorEventDefinition": {"id": "58280f62-59c6-4438-bcce-8489143934f8", "eventImplId": "23e3545e-6881-4f89-8aaa-9fa396b010c9", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:sequenceFlow": [{"sourceRef": "d253d430-6035-48df-b73f-ee2133921481", "targetRef": "09e628df-d4ba-408b-b61b-83722a082cca", "name": "To End Event", "id": "7472591e-b20b-469e-b2e3-600ef9df1153", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "41718139-7b6e-4468-8396-607a3e22495d", "targetRef": "eb1dae19-eaa0-4a23-8299-7a66ae48caec", "name": "To Exclusive Gateway", "id": "3ddcc5e2-55a4-430b-9670-486f052fdded", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "*************-43f2-8589-85728a5dc2f7", "targetRef": "f64b170f-b2e6-479e-8608-f1058c6779f1", "name": "To End", "id": "ff3e0f0a-2d00-4de3-816d-e666551b9332", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "f64b170f-b2e6-479e-8608-f1058c6779f1", "targetRef": "73414ba8-e14a-4008-86f8-697d960a169b", "name": "To End", "id": "cc96dfc2-7cef-4dac-8a48-29833afae4e5", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "eb1dae19-eaa0-4a23-8299-7a66ae48caec", "targetRef": "*************-43f2-8589-85728a5dc2f7", "name": "To Set Action List", "id": "6eaadde4-352f-41eb-8ab6-78629af8518a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "eb1dae19-eaa0-4a23-8299-7a66ae48caec", "targetRef": "73414ba8-e14a-4008-86f8-697d960a169b", "name": "To End", "id": "a1807b0c-f648-4a25-8cb9-cc96d09d8874", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "!tw.local.data", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "action", "id": "2056.*************-468e-a3d2-49a84496ce7b"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "screenName", "id": "2056.9d473f4e-fbce-4dd8-876b-73fe1e1e76fd"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "actions", "id": "2056.a170470d-a4b8-455a-8a83-a95e87a15113", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject[0] = \"\";\r\nautoObject", "useDefault": "false"}}}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "conditions", "id": "2056.b42fa23f-7837-4f83-89d3-000b7e778d9f", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ActionConditions();\r\nautoObject.screenName = \"\";\r\nautoObject.userRole = \"\";\r\nautoObject.complianceApproval = false;\r\nautoObject.lastStepAction = \"\";\r\nautoObject.subStatus = \"\";\r\nautoObject.requestType = \"\";\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMsg", "id": "2056.62be6d5b-855f-4503-8287-e00a19b848fc"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.b56bc1a1-1124-4135-8472-d86060e909cb"}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Set Action List", "id": "f64b170f-b2e6-479e-8608-f1058c6779f1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "432", "y": "137", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "tw.local.results = tw.local.actions;"}, "ns16:incoming": "ff3e0f0a-2d00-4de3-816d-e666551b9332", "ns16:outgoing": "cc96dfc2-7cef-4dac-8a48-29833afae4e5", "ns16:script": "\r\r\ntw.local.actions =[];\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For Create ODC Process -----------------------------------------\r\r\n************************************************************************************************************************/\r\r\n \r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT01;\r\r\n//Set Actions for Act01\t\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\tif(tw.local.conditions.userRole == tw.epv.userRole.hub)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequestToHubDirectory+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n\telse if(tw.local.conditions.userRole == tw.epv.userRole.branch)\r\r\n\t{\r\r\n\t \ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT02 + \"\";\r\r\n//Set Actions for Act02\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\r\r\n\t{\r\r\n\t \ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t\t\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n }\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT03;\r\r\n//Set Actions for Act03\r\r\nif(tw.local.conditions.screenName == tw.local.screenName) \r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval){\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\"\";\r\r\n\t}\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\"\"; \r\r\n\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT04;\r\r\n//Set Actions for Act04\r\r\n\r\r\nif(tw.local.conditions.screenName == tw.local.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval == true)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\tif(tw.local.conditions.requestType == tw.epv.RequestType.Create)\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.createContract+\"\";\r\r\n\t\r\r\n\tif(tw.local.conditions.requestType == tw.epv.RequestType.Amendment)\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.amendContract+\"\";\t\r\r\n\t\r\r\n\tif(tw.local.conditions.requestType == tw.epv.RequestType.Recreate)\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.recreateContract+\"\";\r\r\n\t\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\t}\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT05;\r\r\n//Set Actions for Act05\r\r\nif(tw.local.conditions.screenName == tw.local.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval == true)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.obtainApprovals+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n\r\r\n\telse if(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToTradeFo)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n\t}\r\r\n\telse if(tw.local.conditions.lastStepAction == tw.epv.CreationActions.terminateRequest)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n\t}\r\r\n\telse \r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.authorize+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n\t}\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT06;\r\r\n//Set Actions for Act06\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.completeRequest+\"\";\r\r\n}\r\r\n//Set Actions for ACT07 // Subprocess unified trade  FO subprocess\r\r\n\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT07;\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\r\r\n\ttw.local.actions[tw.local.actions.listLength] =tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For Collection ODC Process ---------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//screen01\r\r\nif(tw.local.conditions.screenName == (tw.epv.Col_ScreenNames.ODCCol01+\"\") ){\r\r\n\t\r\r\n\tif( tw.local.conditions.isLiquidated)\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.submitLiq+\"\";\r\r\n\telse\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.createLiq+\"\";\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.cancel+\"\";\r\r\n}\r\r\n//screen02\r\r\nif(tw.local.conditions.screenName == (tw.epv.Col_ScreenNames.ODCCol02+\"\") ){\r\r\n\t\r\r\n\tif(tw.local.conditions.subStatus==\"Pending Execution Hub Liquidation Review\")\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.authorize+\"\";\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.returnToMaker+\"\";\r\r\n\r\r\n\tif(tw.local.conditions.subStatus==\"Pending Cancelation Confirmation\")\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.Col_Actions.cancel+\"\";\r\r\n}\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For Reversal ODC Process ---------------------------------------\r\r\n************************************************************************************************************************/ \r\r\ntw.local.screenName = tw.epv.ScreenNames.RACT01;\r\r\n//Set Actions for Act01\t\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\t\r\r\n\t \ttw.local.actions[tw.local.actions.listLength]=  tw.epv.CreationActions.submitRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.epv.CreationActions.cancelRequest+\"\";\r\r\n}\t\r\r\n///Set actions for reversal  act2\r\r\nif(tw.local.conditions.screenName == tw.epv.ScreenNames.RACT02 )\r\r\n{\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n/************************************************************************************************************************\r\r\n/*------------------------------------ Set Action List For closure ODC Process ---------------------------------------\r\r\n************************************************************************************************************************/ \r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT01;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.submitRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\"\";\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT02+\"\";\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.cancelRequest)\r\r\n\t{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.cancelRequest+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT03;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.terminateRequest+\"\";\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT04;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.terminateRequest)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.terminateRequest+\"\";\r\r\n\t}\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToInitiator)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToInitiator+\"\";\r\r\n\t}\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.approveRequest)\r\r\n\t{\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\t\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n\t\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT05+\"\";\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.closureACT06;\r\r\nif(tw.local.screenName == tw.local.conditions.screenName)\r\r\n{\r\r\n\t\r\r\n\t\r\r\n\t\r\r\n\tif(tw.local.conditions.lastStepAction == tw.epv.CreationActions.returnToTradeFo)\r\r\n\t{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToTradeFo+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.approveRequest+\"\";\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.epv.CreationActions.returnToMaker+\"\";\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n/****************************************************************************/\r\r\n\r\r\ntw.local.results = tw.local.actions;"}, {"scriptFormat": "text/x-javascript", "name": "Set Conditions", "id": "*************-43f2-8589-85728a5dc2f7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "305", "y": "137", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "6eaadde4-352f-41eb-8ab6-78629af8518a", "ns16:outgoing": "ff3e0f0a-2d00-4de3-816d-e666551b9332", "ns16:script": "try {\r\r\nvar jsonObj = JSON.parse(tw.local.data);\r\r\n\ttw.local.conditions.screenName = jsonObj.screenName;\r\r\n\ttw.local.conditions.userRole   = jsonObj.userRole;\r\r\n\ttw.local.conditions.lastStepAction= jsonObj.lastStepAction;\r\r\n\ttw.local.conditions.complianceApproval= jsonObj.complianceApproval;\r\r\n\ttw.local.conditions.subStatus= jsonObj.subStatus;\r\r\n\ttw.local.conditions.requestType =jsonObj.requestType;\r\r\n\ttw.local.conditions.isLiquidated =jsonObj.isLiquidated;\r\r\n\t\t\r\r\n} catch (err) {\r\r\n\tlog.info(\"ODC: Get Action By ScreenName Service: no data found in conditions list \")\r\r\n}\r\r\n"}], "ns16:exclusiveGateway": {"default": "6eaadde4-352f-41eb-8ab6-78629af8518a", "name": "Exclusive Gateway", "id": "eb1dae19-eaa0-4a23-8299-7a66ae48caec", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "141", "y": "156", "width": "32", "height": "32"}}, "ns16:incoming": "3ddcc5e2-55a4-430b-9670-486f052fdded", "ns16:outgoing": ["6eaadde4-352f-41eb-8ab6-78629af8518a", "a1807b0c-f648-4a25-8cb9-cc96d09d8874"]}}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ff3e0f0a-2d00-4de3-816d-e666551b9332", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.*************-43f2-8589-85728a5dc2f7", "2025.*************-43f2-8589-85728a5dc2f7"], "endStateId": "Out", "toProcessItemId": ["2025.f64b170f-b2e6-479e-8608-f1058c6779f1", "2025.f64b170f-b2e6-479e-8608-f1058c6779f1"], "guid": "879d35e6-687a-4c12-9155-5fd2d34719f5", "versionId": "33eaf710-a643-406e-bdb2-1521340fe615", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Set Action List", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6eaadde4-352f-41eb-8ab6-78629af8518a", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.eb1dae19-eaa0-4a23-8299-7a66ae48caec", "2025.eb1dae19-eaa0-4a23-8299-7a66ae48caec"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.*************-43f2-8589-85728a5dc2f7", "2025.*************-43f2-8589-85728a5dc2f7"], "guid": "5e01f583-5b76-478d-8be4-24ded5acb4e1", "versionId": "*************-4fea-9282-f3cf81f6c458", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a1807b0c-f648-4a25-8cb9-cc96d09d8874", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.eb1dae19-eaa0-4a23-8299-7a66ae48caec", "2025.eb1dae19-eaa0-4a23-8299-7a66ae48caec"], "endStateId": "guid:ca84402e5fd92838:-7c46c9a3:18b8d18087b:-73f9", "toProcessItemId": ["2025.73414ba8-e14a-4008-86f8-697d960a169b", "2025.73414ba8-e14a-4008-86f8-697d960a169b"], "guid": "620baef6-f6d7-4dc8-9dfd-38d160dfcda5", "versionId": "786cf957-b2a7-4b35-b624-e61c81df6afd", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.cc96dfc2-7cef-4dac-8a48-29833afae4e5", "processId": "1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f64b170f-b2e6-479e-8608-f1058c6779f1", "2025.f64b170f-b2e6-479e-8608-f1058c6779f1"], "endStateId": "Out", "toProcessItemId": ["2025.73414ba8-e14a-4008-86f8-697d960a169b", "2025.73414ba8-e14a-4008-86f8-697d960a169b"], "guid": "d0fba949-1d1b-4301-8938-cfed3cee09b5", "versionId": "b1346a0a-d5b3-4ad5-be2e-f52381d12d93", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}