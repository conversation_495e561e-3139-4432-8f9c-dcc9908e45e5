<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.7c4993bc-f7d5-4689-96c6-89c5b908583f" name="Start New Request CV">
        <lastModified>1699252783156</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;7a521660-b968-4e9a-8098-d6878e2d936f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;startNewRequestPnl&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7865aced-8b88-46fb-8afb-95b24d99dbd9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Start a New Request&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23baaa89-76b5-4e31-8481-ba632892b275&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f388646-dd29-4f35-80ec-da05b0b455ba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;196c87b4-0527-4300-8d1c-648207d5f4bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;41410fac-5ffa-4423-84d0-ef54844344de&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;2879d34a-db6a-4c19-83ec-8d416d572bcb&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6836807a-cc3c-4508-83ec-e48f06233053&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5400fb63-c1f8-4b27-82a9-67ae5bf169b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a064df72-0f65-41e9-8fc6-e201e4e97a88&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e44398a-ff5c-412b-862b-0ff0baf45ea5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;472c8954-1ec4-4dc4-8798-d41ce26dd756&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;57ae24f1-45b1-4f08-815f-0626d4068321&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a051d4ea-**************-3905daefb7f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;51d609fa-c28c-4293-8fed-11d9e40dccb2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;630d13f1-a66c-4a42-86f0-6327dd12ec5b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e5cb126a-fd7f-4c93-876e-751f9f11dbea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;85a9d59b-d0fe-4ed2-8809-635a92e74b74&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;92204a1e-97c8-447c-8a49-15b2ff4ac024&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestNature&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cadcbd9a-a340-431d-8dda-3a94d4367806&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2c2c2d12-0304-47a0-83d8-d359b24e3e65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Request Nature طبيعة طلب العميل&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;09638071-ccee-4cef-8102-7dc9a9ced288&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f461f4f9-7dc3-4a40-8c2a-0bebbce3237a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;836f716d-58eb-461f-8efe-5be3eb4b2f95&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0b4c1c8d-29f8-4880-8fd7-0d5a7ce9d3b5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da915fe2-8e81-4972-8b69-e79e3c3e7453&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5ccf2be3-16c8-4840-80ab-0e354e70f410&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.d034d01a-267a-423b-a582-7444da255e09&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;46f4a357-9193-4296-8cc5-9017e4231f52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;767a0a25-0fc6-4284-8fa9-fd2c9c3fb6f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;${requestType}.setAction(); 

view.showParentRequestNo();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ab338278-8a26-48bc-8bb3-c7358f459d7d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.showParentRequestNo();
view.showCreateBtn();
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81b31eb4-9710-4190-82f5-21e825dd3ea0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"New","value":"New Request اصدر جديد"},{"name":"Update","value":"Update Request تعديل"}]&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.ODCRequest.requestNature&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c5be64d4-c3ee-4a99-85d2-f45977d134e4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestType&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;306118fb-4d66-4aa9-b419-27bea9694b43&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;43183922-6e9e-4cb4-81e8-34d82df8a405&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Request Type نوع الطلب&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b4c019b1-68e2-47e3-8795-f177b09ec57f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c57a5a24-fa52-491f-88b4-10efab06d633&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d7e4631-c07a-4fdd-8b5a-81ace54ca07c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3c60fe76-1012-4184-8fec-2a67a1c7a75c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.9e31ab59-ee74-4591-92f7-292855228c1b&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;633add29-5d4a-4d06-8717-3f1249a95547&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f3d1be3-f910-497f-8ac9-bbd91fccb1f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"value","optionDisplayProperty":"name"}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a109d463-807b-4ee8-8a78-ca1cb4973f8a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.userConditions&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5fbccea-9547-4a2f-8993-3192dad0318c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;[{"name":"Amend","value":"ODC Amendment"},{"name":"ReCreate","value":"ODC Re-Create"},{"name":"Collection","value":"ODC Collection"},{"name":"Reversal","value":"ODC Reversal"},{"name":"Closure","value":"ODC Closure"}]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5ab5c13a-18b0-4c3a-8ce0-0dbd787b63f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
view.showCreateBtn();

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;07d82648-3e69-459c-8d54-a0a9af5c81f8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
me.setAction = function()
{
      var role = view.context.options.role.get("value");
//      console.log("role===="+role);
      var requestNature =${requestNature}.getData().value;
//      console.log("requestnature===="+requestNature);
      var concat = requestNature +"_"+role ;
//      console.log("concat===="+concat);
	view.context.options.userConditions.set("value", concat);
	concat="";
};

me.setAction();

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.ODCRequest.requestType&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8913bb94-2078-4320-81a9-ede3239653cd&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9622ffc8-341c-4d03-8355-0b22c4c23587&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;90bbccce-cebb-4735-8ddf-b7c7e2488322&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;989a978e-f779-451b-88a0-f7ced887cb4e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e599392-51e1-4e81-8243-d6fb6c056d1f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;529fddd5-3d6c-4d30-8fd8-7da0999fa036&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;fd98122f-aae7-44c2-8b33-c4819c726022&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CIFNO&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5c01e370-22ee-4604-affb-f33fc17d6d6d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;627f3eaa-84f1-42f5-830b-c5eb8fd45430&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;CIF Number&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff2ca2c7-47aa-426e-8a7b-ffc51844f60b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;939385a3-16fb-473a-80f5-6ccb450e0366&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97d791cd-3af4-4901-8a14-9a8e7eb4d15c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length !=8)
{
	me.setvalid(false,"Cif No Length is 8");
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ba2e888c-bbe0-437f-8052-a3377762f17a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
view.showCreateBtn();

var cifValue = me.getData();
if(cifValue==null || cifValue=="")
{
	${retrieveCustomerBtn}.setVisible(false,true);
}	
else
{
	${retrieveCustomerBtn}.setVisible(true,true);
}
${customerName}.setData("");

&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e78afdf7-d958-441d-8673-b4b1abc82e29&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@className&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af7880e6-8f9c-4b5b-8fcd-461e9d06827a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
view.showCreateBtn();
//show.showCustomerDetails();

var cifValue = me.getData();
if(cifValue==null || cifValue==""){
	${retrieveCustomerBtn}.setVisible(false,true);
}	
else{
	${retrieveCustomerBtn}.setVisible(true,true);
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.ODCRequest.cif&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3b72914a-71cb-4a54-814d-84d70f0efabf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;customerName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0e134240-ffea-4bfd-8f00-651d326eb765&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Customer Name اسم العميل&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fff71f65-02a4-439e-892c-c6681e4e21ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b28f0b8-bf70-4854-87c2-6b2751829892&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d89c0ae-7fa1-4b94-8691-58e9fd74a988&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.ODCRequest.customerName&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e3afd333-4502-4a07-8541-f6c78a9ed615&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrieveCustomerBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71294c7d-c5f2-447c-8260-670d149cffc6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Retrieve Customer عرض بيانات العميل&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;12e9ea68-bd18-4976-87c2-eb90bd4c26cf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;46d11f38-25b3-4bbc-86ce-67eb01fce765&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b9d07847-6425-4167-855f-29111db1fe59&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ed2426e-f578-4162-8b7e-2ea1ac2439a4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;510d7676-e12b-430d-89dd-29b34697c39c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e1d623cd-3b04-44d1-8c31-cdcd3d56f074&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae326d2e-153f-47c3-8d0a-4b8c6fdb400f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;956ab4c4-8f9c-4490-85bd-bf1e824dcd30&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;732dc6c9-4f2d-44b8-80d7-1a59e5bd2d74&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8aa379dd-b9a3-4af1-8ff4-66e01e3e1b42&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ec87df2-9f8f-42ac-8c0e-e93a9588466c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;72ea5daf-a4f7-4690-8454-ed777c76e86c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;86330f72-3906-4b8a-82e2-93db376352b9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"59%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ddf96607-cb8b-4299-8877-c5dc5412d8cf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@margin&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.retrieveCustomer&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c74ee49f-1144-4fd6-8eab-d411461fe113&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;updateRequestTypeSection&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a8de1e60-3261-4fb9-82a4-f37029faca72&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8f32dd08-7e96-4fb0-81db-98c0dce2f667&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bab471ef-3340-46aa-822e-de57eab5dcf7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da972200-d578-4b75-86f7-36f299a19a74&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;9fc19a77-7302-4cd2-8a1c-50ed3d5579a8&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;ca65940b-84d4-4599-8dbb-ee866f35fdcc&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ff7523a-cfa5-4dbe-8c61-059863fa9fa0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8858f12f-8796-40d8-83ee-cd18eff9c1d7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7b98482f-5339-438e-8b3f-7c671c93eca9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4067b49-39eb-447d-858e-1950d855fb48&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;0341c9fc-d29f-417c-8e0f-64df2a25468d&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3554b829-d315-482f-81c1-7d6f9b2f3e33&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;parentRequestNo&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;83053a7a-d42e-440e-bac7-b2ba124587eb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b2dd36f0-f0cc-45b2-84ea-da2d0578c142&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Parent Request Number رقم الطلب   الذى يتم تعديله&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;37ac46cc-de3e-45d9-88c5-65c22ec887f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6837f09c-e6d1-4540-853f-75778a8ec2b2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6fbfac0e-22f7-47b6-830c-5d1ce1c80c00&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.parentrequestTypeVIS&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;31c5198e-8f21-460e-87f7-06b22484f698&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//if(potential.length !=14)
//{
//	me.setvalid(false,"Parent Request Number Length is 14");
//}
//else
//{
//	me.setvalid(true);
//}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1328081b-9c99-4c96-8c84-b9266dcccbdb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
view.showCreateBtn();

var parentRequestNo = me.getData();
if(parentRequestNo == null || parentRequestNo == "")
{
	${retrieveRequestBtn}.setVisible(false,true);
}
else
{
	${retrieveRequestBtn}.setVisible(true,true);
}	

${importerName}.setData("");
	
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;007ff5a7-5b89-491c-84b5-10de8fce75c1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
view.showCreateBtn();

var parentRequestNo = me.getData();
if(parentRequestNo == null || parentRequestNo == "")
{
	${retrieveRequestBtn}.setVisible(false,true);
}
else
{
	${retrieveRequestBtn}.setVisible(true,true);
}	


	
&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.ODCRequest.parentRequestNo&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0097d21b-1eba-48f8-8d53-ec867b6b8e71&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;importerName&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d4860fe5-722f-47bb-8084-35a41870e264&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Importer Name اسم المستورد&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f0ea9350-3d56-4fab-88fa-076acd2a7f11&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23e76d2c-baf4-46a8-85a6-9759b9ad626d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;add821fe-e135-49d4-823e-60fdd2f6d003&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.ODCRequest.ImporterName&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;12ecfea5-a8f2-4470-894f-7ea6002d0e83&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;29e78d81-7654-40bf-85e3-80347a379c93&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b48f8316-db4c-402b-89e8-3aecb230aee1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ab245c01-aab1-4d67-8dd7-ab78fa3051da&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2b72fe1c-6fe8-44c3-8e28-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;6bd32aaf-8a5a-47fc-840f-aaebfceafbc5&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e9447e1e-b28b-42b5-82b3-2cb2dfa83adf&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;requestDate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;61e9bb79-39d6-410f-87df-885f32babd85&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Request Date تاريخ الطلب&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1bfc731d-63d5-4a95-82d8-5b15ff0c42dc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;902e553b-a8ca-4f8f-82d1-c7f405a58adf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cd6aadb9-3683-4f39-894d-bcc50da90344&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fdeb3b51-a25a-4aa1-8068-a75f56a29609&lt;/ns2:id&gt;&lt;ns2:optionName&gt;includeTimePicker&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c00463e1-5afc-4e15-8847-5ee589007c77&lt;/ns2:id&gt;&lt;ns2:optionName&gt;enableCalendarIcon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.54643ff2-8363-4976-bb5e-d4eb1094cca3&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.ODCRequest.requestDate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;6dbd1023-4d97-4443-8758-402ef8244105&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cecc3803-e5c3-43a6-8b70-ace11d0eff89&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;89ac4a3f-34b1-4140-88ee-fa2beaca40ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;57e1722b-4275-4ffe-8379-9b6d1f54b7fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;fb8b960c-4dd2-4247-82fd-fe0929a53fad&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0946ddd0-68d1-4861-8065-12e3ed225f2b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9efa22c7-854d-41ed-857c-269d683e5970&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f7d42195-ccb0-4ea6-8ea4-0e6027e83573&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9d29bd68-cd9a-40ee-8747-25af281522c5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ef185d1-b101-4653-8541-2a75aee59c35&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"55%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;f044b46d-8810-4222-8dd3-5396e89f1227&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1deea231-b181-4689-81da-cd9bfe8a505d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b3238058-21f2-48d8-888f-c4f45d1ac128&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e882bd6-815d-4b05-8797-72d01df6c656&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c33cca24-08e1-427d-8efd-e1cab535b526&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"44%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;74e52d05-f13d-4dce-8cbd-71d2fcb53ec2&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;e0d5c089-42a2-48c5-816f-c346a19bd1c4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;retrieveRequestBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0f81ad5e-3073-4f3c-8fdb-fa80e466d357&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Retrieve Request عرض بيانات الطلب&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1877474-242a-44c3-8dd8-39906da0aa08&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;970d116e-fb22-4003-8d44-a8a2c03fe00c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;095bafa3-5ff9-4090-8d01-f3b72bd7eff2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;101f7bdc-e343-4cd0-8a83-d773bb0fd425&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;619c8494-50e6-4aa2-8bad-1a956a87ea83&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a4680b6-7583-4caa-885e-024f4b2eac4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ac598e5-47db-4ba2-8c07-65e2edcdb24c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b7489fc5-93b5-4647-82c2-734383fad2e2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;744483a1-9ec6-447e-8e18-de29fc8cb138&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92e6e350-3f94-4cc9-871b-63d5216bc464&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b6c65ad8-0a63-4236-854b-17b32ac8028b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.retrieveRequestBtn&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;65d9e59e-08b8-497e-8879-1aaa2eca963c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;createRequestBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a2abf968-aa9e-464d-8b71-7d067d2d9ab9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Create Request     إنشاء الطلب&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;41ba3b6f-9084-4d58-8948-33308b6efca5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1c335bbc-f6af-40c3-857d-087f14f88a6e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7980f370-5351-4c7c-8419-5f9df310da88&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c1aa19ad-6839-4eb5-811b-9911f11f5170&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ccae539-2780-4d34-88d8-5a8748f25e8f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bba8b5b7-1ad5-45d3-81dc-61a1dedc716b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56a2f7f3-ed09-40ca-8e5d-51eb4f0195d8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f94c3bf0-8a4b-4478-87d0-34c90fbd5524&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f9e28ef-0622-46cc-87d0-318ca47230af&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4c59b17-4739-410b-8403-39ef459f022d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;10eb5b1d-a0e1-46bd-8bd6-b8b7f46fd97e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.createRequestBtn&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;84513474-1fa7-4c03-83ab-7810dd8e5ca3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;000e4e9c-6a7b-496f-8ef8-93894caefd10&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ac614c9c-8013-4ec1-8d16-e4827a93292f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;102a7a0f-2aca-4e80-8de3-43eeed267614&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;c99d28eb-daa6-4162-8cf5-4fdf98ee277d&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;146a8910-5c6b-4701-82f9-9715296f2f6f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2ed30b8a-243f-4c47-8081-ead49c8acfb0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Error Description / تفاصيل الاخطاء&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e2deb03d-d710-4152-8e4d-f457de41ca2e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;62a8f145-81b1-4e7a-8e43-0dfaa27aca7b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8527fd30-43af-4fa8-8eee-0270936a045c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;d570b320-6e8d-4da6-84cf-76bed863f0a4&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5cbd3a8f-ac6b-4242-82ca-3524121113b9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Output_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a40577a9-4811-47fc-8323-f65ef2c910f3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Display text&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f3ab1184-3bdb-4675-84ff-1ab9a73cd810&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;295b6235-a9dd-4c4f-8d8b-cd5a6889d5d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc0b8b01-dbdb-46d4-88fc-009eb76c7068&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;G&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b3c7ee97-e6b7-4047-8181-a529cf8753d1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e244edd8-0e66-4847-8382-175fb47671f3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;weightStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;76bd45f8-1593-4cb3-8b53-1fd0ffcbbec2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowHTML&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.errorMessgae&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction></loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction></changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>45cae5bc-57d4-4d7e-974a-ee5507a198b2</guid>
        <versionId>3e728fac-930b-4161-9fb2-c7fcac0ddd33</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="ODCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.3d3d7c61-303d-4902-afe9-4f4939fc5b1d</coachViewBindingTypeId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>73929c55-3275-439f-a898-1fdaac5c7ece</guid>
            <versionId>880f9522-ec5e-4de5-8089-8efe11dc12a5</versionId>
        </bindingType>
        <configOption name="retrieveRequestBtn">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.093ca349-804e-4c7d-ad4c-a1f5fa34903d</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>ea66cb46-e59d-486c-b88b-d67af09103e9</guid>
            <versionId>ac6afa2a-5ad6-469e-9dc4-977975b582d3</versionId>
        </configOption>
        <configOption name="createRequestBtn">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.69094bc5-8873-473f-97ed-44a8d96c68f2</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>ba74ff9f-e695-472f-b463-d7e820ca555e</guid>
            <versionId>47ba309d-e1c7-4fc3-a780-83f65343b8be</versionId>
        </configOption>
        <configOption name="errorMessgae">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.bc043e6d-48e4-47ca-a2aa-8f49bdd88995</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>d4141247-9b37-48b3-bbd0-00313fe11a92</guid>
            <versionId>bfb0f319-3b0e-4a64-a50f-56e65240b1f4</versionId>
        </configOption>
        <configOption name="retrieveCustomer">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.7af3bfb3-e9aa-4e6c-8c92-6cfe8f839812</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>8e0cc42b-8a09-4b34-b305-854e14bdc207</guid>
            <versionId>38ed6cfc-750d-4a46-a99e-0c1af451154a</versionId>
        </configOption>
        <configOption name="errorPnlVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.aa61d4ab-5a1e-45e1-9a83-d34319a2c5c5</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>f6c7319e-2b50-4469-ae5c-55f3a0ae3117</guid>
            <versionId>a6ff4bb3-ea1e-4b4e-a1e6-f13a9331bcc1</versionId>
        </configOption>
        <configOption name="parentrequestTypeVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.908d52ff-223b-44ee-81f7-63c6b30dc46d</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>5f6a37c6-e08b-42ee-bbf1-26d6c35f2ef0</guid>
            <versionId>01568fda-1c99-4dbd-8bfd-31446a8caeda</versionId>
        </configOption>
        <configOption name="userConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.da1bdc8f-1cb9-4ac2-b3f3-2e72f830694d</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>5c898ce1-4311-4604-84af-bf68e8ec91d8</guid>
            <versionId>ced3eff5-c08a-41c9-8870-1598cce4f06f</versionId>
        </configOption>
        <configOption name="role">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.76a5d8a9-96e3-4d1d-9bb6-56a5756e141c</coachViewConfigOptionId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>7630b0e7-453c-475b-bcdf-627382fea760</guid>
            <versionId>95e970be-8d27-4867-b5aa-25851adc0e94</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.371e8a33-6051-4bc9-8cb6-18df3569ba78</coachViewInlineScriptId>
            <coachViewId>64.7c4993bc-f7d5-4689-96c6-89c5b908583f</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>///show parent request number if request nature is update request&#xD;
this.showParentRequestNo = function(){&#xD;
	if(this.context.binding.get("value").get("requestNature").get("value") == "update"){&#xD;
		//this.context.options.parentrequestTypeVIS.set("Editable",  "NONE");&#xD;
		this.ui.get("updateRequestTypeSection").setVisible(true,true);&#xD;
	}&#xD;
	else{&#xD;
		//this.context.options.parentrequestTypeVIS.set("value",  "NONE");&#xD;
		this.ui.get("updateRequestTypeSection").setVisible(false,true);&#xD;
	}&#xD;
}&#xD;
// hide create contract if request nature , request type , cif no and parent request no is empty&#xD;
this.showCreateBtn = function(){&#xD;
	if(this.context.binding.get("value").get("requestNature").get("value") == "" ||this.context.binding.get("value").get("requestType").get("value") == "" ||this.context.binding.get("value").get("cif") == "" ||&#xD;
	(this.context.binding.get("value").get("requestNature").get("value") == "update" &amp;&amp; this.context.binding.get("value").get("parentRequestNo") == "" ))&#xD;
	{&#xD;
	 	this.ui.get("createRequestBtn").setVisible(false,true);&#xD;
	}&#xD;
	else{&#xD;
		this.ui.get("createRequestBtn").setVisible(true,true);	&#xD;
	}&#xD;
}&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>b0d78bf2-e8b8-4aba-958f-93029c735ba5</guid>
            <versionId>8082865d-7e9e-45a7-8621-7a6250ebd782</versionId>
        </inlineScript>
    </coachView>
</teamworks>

