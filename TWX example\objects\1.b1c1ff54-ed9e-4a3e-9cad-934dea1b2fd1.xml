<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1" name="Act01 - Create or Amend ODC Request">
        <lastModified>1738479947887</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.136e3662-e6aa-49a8-a4ba-e15111c65f8c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>true</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:debeaea024f39647:-512ef1a:189a6650968:-3e35</guid>
        <versionId>1bd76b05-5153-462d-993b-a53d8e782b07</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"mobileReady":[true],"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"isInterrupting":true,"extensionElements":{"default":["2027.2a66b38e-078c-4f17-8aa4-0a0eac8bf103"],"nodeVisualInfo":[{"color":"#F8F8F8","width":31,"x":-306,"y":105,"declaredType":"TNodeVisualInfo","height":43}]},"name":"Start","declaredType":"startEvent","id":"e4c26469-3f1e-49fd-a9c2-f1feeabcfc0a"},{"outgoing":["2027.a628a18e-a57f-484d-890e-d714a0873119","2027.e3d77e41-7957-42f1-8f6f-79ab32bbe009"],"incoming":["2027.245a4797-cab9-41b8-8610-6e51dc5b7da1","2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17","2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d","2027.a4212e56-9bb4-41c2-812b-e4dd08de0584"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":716,"y":81,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":["if(tw.local.odcRequest.requestType.value == \"amend\" || tw.local.odcRequest.requestType.value == \"recreate\" )\r\n{\r\n\ttw.local.requestTypeVIS = \"Editable\";\r\n}\r\nelse\r\n{\r\n\ttw.local.requestTypeVIS = \"None\";\r\n}\r\n\r\ntw.local.odcRequest.GeneratedDocumentInfo.customerName = tw.local.odcRequest.CustomerInfo.customerName;\r\n"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"0","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f138f6a9-e27a-413e-86bc-ddeb6d979ad2","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"569dfbbb-7e7c-408a-85bd-ad9a4c1499af","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cffdb4cc-b87d-4e77-8a7d-191bf3c42b63","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cf8fe0fe-833e-4540-81c3-fc3a726e1141","optionName":"parentRequestNoVis","value":"tw.local.parentRequestNoVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3a6322f6-09c5-497b-805b-7e27e6bc339e","optionName":"basicDetailsVIS","value":"\"\""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ec0e40a1-ca1c-40d2-8f62-1ed2f3c656ed","optionName":"contractStageVIS","value":"tw.local.contractStageVIS"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c23af930-1ce8-4908-88f9-88ac5c34df90","optionName":"basicDetailsCVVIS","value":"EDITABLE"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ac6c8e7b-88cd-4e55-85ef-18a2da5b604e","optionName":"flexCubeContractNoVIS","value":"\"NONE\""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"205311e8-ef63-4e75-855b-ffd4ae3fb6c8","optionName":"multiTenorDatesVIS","value":""}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"7f199f4d-c7a9-497a-89b4-15228c4256c4","version":"8550"},{"layoutItemId":"1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"346cb219-0124-426d-8fc0-9cf5e483c354","optionName":"@label","value":"Letter Generation"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e9cd7085-34e9-4873-8116-6eb83f5b1115","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8836366c-f207-4a8a-8551-ba9e6a36955a","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"*************-460d-8074-bbc271c821ab","optionName":"conditionType","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f5d1a577-f9c5-4ea2-8012-9f986d88aaa8","optionName":"deliveryTerms","value":"tw.local.deliveryterms"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f636975b-b7ff-43ac-8288-d0e6d89434bb","optionName":"paymentTerms","value":"tw.local.paymentTerms"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d2058fd9-48ac-494b-86c3-4a38d789e833","optionName":"specialInstructions","value":"tw.local.specialInstructions"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a143445a-3219-4a4b-8fb3-376bc3a7a733","optionName":"instructions","value":"tw.local.instructions"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5198a1c6-bc6a-4a28-8a70-e98f496dddbe","optionName":"requestTypeVIS","value":"tw.local.requestTypeVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2bba2dfd-9ed6-4d93-833f-8c8d3c86a0ea","optionName":"regeneratedRemittanceLetterTitleVIS","value":"tw.local.regeneratedRemittanceLetterTitleVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ca5f40a2-5930-4d7d-89ba-ad5562aeea04","optionName":"DocumentGenerationVIS","value":"\"Editable\""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"247b338c-33ff-4721-85f5-8ec47f25ce72","optionName":"requestType","value":"tw.local.odcRequest.requestType.value"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"80a2bbbc-dbbd-4899-86fe-42fb927eaade","optionName":"remittanceLetterButton","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a2615514-9766-45b3-8155-5a28b4b58605","optionName":"documentGenerationVIS","value":"Editable"}],"viewUUID":"64.1d99aba8-195f-4eee-ba8c-a47926bc21e2","binding":"tw.local.odcRequest.GeneratedDocumentInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"bf13348a-57c9-46f5-8b74-cba703b6aade","version":"8550"},{"layoutItemId":"2","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b7335dc-565a-4874-87fb-d6b5faf147d6","optionName":"@label","value":"Customer Info"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"12f7655f-8be9-42c7-82d8-fed7984dc3c2","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1c4ab8ed-1743-4726-8da8-92ad4490ee1c","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b05651b8-8166-49a6-8300-b6a4121d49e0","optionName":"tableName","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a423d4a2-8e36-4218-8a9e-************","optionName":"customerInfoVIS","value":"\"READONLY\""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8ef808e8-bd99-40a2-856f-034cce91cbcc","optionName":"listsVIS","value":"None"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a1bb5a88-33a1-4a0c-8264-48202758152d","version":"8550"},{"layoutItemId":"3","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"53c207e1-4e34-4e9f-826f-313f99a55eab","optionName":"@label","value":"Financial Details - Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ffcc6dc8-d25a-4f68-8c9e-7cc29724f213","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"69ba7164-e143-463f-87cb-765e8ed61190","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2ac167ab-139c-4e82-8982-1be1519e5b17","optionName":"financialDetailsVis","value":"\"DEFAULT\""},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"48b8a314-f7cd-4fdc-8b97-d249cc0a4340","optionName":"fcCollectionVis","value":""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"001351ad-d960-4e19-874b-471ca812464c","optionName":"today","value":"tw.local.today"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fa17c193-55ca-4708-81de-e7bc461e5cc4","optionName":"currencyDocAmountVIS","value":"tw.local.currencyListVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"738443dc-44d3-417c-8323-2008b8a95b9e","optionName":"financialDetailsCVVis","value":"EDITABLE"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"7021f345-19f1-4c0c-8103-01315252d575","version":"8550"},{"layoutItemId":"4","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"783cb246-2d9d-4ddb-87cc-33e490a2fc35","optionName":"@label","value":"FC Collections"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4e21faf2-ce42-4a21-864e-80ccdda2e9eb","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8ed0576a-89e4-4fb5-82b5-206b1af02b43","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7a11c958-c6f9-4723-84b1-4d9e8873a852","optionName":"customerCif","value":"tw.local.cifNo"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"65b80165-a269-4645-8e75-95d3e98164cb","optionName":"requestCurrency","value":"tw.local.odcRequest.FinancialDetailsBR.currency.value"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9ba6bb97-fa1d-4e04-8f56-561deb3288fc","optionName":"collectionCurrencyVIS","value":"tw.local.collectionCurrencyVis"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"31eb5f93-225d-40ac-8868-e8570a290020","optionName":"activityType","value":"write"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d8723785-5dcd-44a3-85bf-7ec05c7e09c8","optionName":"FCVIS","value":"EDITABLE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a63ef53-90d0-4ac8-8b35-2943edd8700c","optionName":"retrieveBtnVis","value":"EDITABLE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"217adb6f-29be-47ca-81af-b6f24094b431","optionName":"negotiatedExchangeRateVIS","value":"EDITABLE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0ac01877-2952-4ac6-8ba1-9f10792459f7","optionName":"addBtnVIS","value":"EDITABLE"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","binding":"tw.local.odcRequest.FcCollections","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"223910ad-acc4-4fa9-8355-84dbb81e27ef","version":"8550"},{"layoutItemId":"5","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"96edc205-324f-4941-8ed6-d3141ae5980c","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"deb225d5-aae7-42dd-8f40-cc96b236244f","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"97a68835-5a68-4cff-8473-7353d6e553d1","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f9a9504a-7a38-472b-87cb-53a821448c19","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ba0afa83-c17b-4711-809a-d517506418c3","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"805e3641-ff9d-465f-8495-6c162f6d5548","optionName":"canCreate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ca254d23-7913-4f38-8588-ebad134dc21f","optionName":"canDelete","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"daeb4758-8372-4c95-8ae0-19183e3b71b5","optionName":"visible","value":"true"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e93a070b-6e7b-4bd6-87fa-00a1425057f7","optionName":"remittanceLetterPath","value":"tw.local.odcRequest.folderPath"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f5bd2e4d-**************-3776041b1aca","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"79075267-9dc5-419b-87e4-da66913467b6","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a61917c1-a7fa-4b21-8a1c-b6f74de9e120","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"97492741-0a9b-43a0-8bfd-950ca85d4d72","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"634af355-b29a-47bf-8a77-9c97fb0d2fa7","optionName":"@visibility","value":""},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"37c0fa32-54f8-489f-8eac-d81ba3badb33","optionName":"@visibility.script","value":"{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"26835533-4b1b-4359-83d8-************","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"4f59508d-9e4b-4845-8f49-4b3cbefe2edf"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2f8fccd3-9879-4e3e-8e48-17d3c6e63e01","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"29c98562-024e-47da-845a-3abfd51a7b46","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"462b95ce-b660-4c4a-8bfc-70f72c026223","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5601885e-a6bf-4bc1-8bec-2a7630731cba","optionName":"colorStyle","value":"P"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d11694b0-3d1e-4acd-84e7-fc714fa2474c","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a5dbc7b0-f9ef-410b-892e-9db2f622ea96","optionName":"tabsStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f883482a-d226-4e45-8ef1-1188a8ded128","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"9c799fe1-8ec9-407f-842e-c0edaeb25c88"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"181464ab-fb2d-416a-81cc-ecc576909a18","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"142337c6-2374-4a83-8447-c556028e0e09","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c890191c-653a-4b5e-8503-6e1133efe5e7","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"366b9b64-0ed4-43c6-88a6-5d2039676b3e","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"48937872-ef9b-4a24-8922-9ffcf4bf8c84","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d26f50e7-e132-4f11-82b0-3da304589c0f","optionName":"buttonName","value":"Submit"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6ced93af-e593-48f3-87e2-313d6a562130","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7dc8b8c7-0732-4d7d-82c2-6450dc15b713","optionName":"approvals","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4de1b979-652e-4708-8395-ccec614834a4","optionName":"complianceApprovalVis","value":"\"NONE\""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d0cd978a-f34a-4fd7-8364-2441474fe56e","optionName":"complianceApproval","value":"tw.local.odcRequest.complianceApproval"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b1d2a4e8-de09-48c3-81cf-731c0b483469","optionName":"errorPanelVIS","value":"tw.local.errorPanelVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6693a28e-f36c-46e2-8dd5-30709b363f8b","optionName":"errorMsg","value":"tw.local.errorMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c5a7d979-7b6a-47cf-8ab0-c268d987ba50","optionName":"terminateReasonVIS","value":"\"NONE\""},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4fbc1a7b-33f6-44ec-8896-4b290ab2d7a3","optionName":"data","value":"tw.local.data"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f5836a08-2c88-46a9-82be-8f179b3305ca","optionName":"actionConditions","value":"tw.local.actionConditions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0421a764-efbb-47b0-89a8-4e64c9f4a3ff","optionName":"returnReasonVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"94b3d09f-52c9-4a2d-805a-e1cc00e616e4","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"de615e4d-3640-48c7-866c-7ddcb180b103","optionName":"tradeFoCommentVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3a1b431b-2243-4153-871a-03fe69e6b559","optionName":"exeHubMkrCommentVis","value":"None"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c3e2dbec-c577-4a5b-80ab-6eac468b21a6","optionName":"tradeFoComment","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f531a602-b360-41cb-8aa7-fc59fb0f5245","optionName":"compcheckerCommentVIS","value":"None"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"4329d93d-28af-4675-8b80-5ee05652e2ca","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Create \/ Amend Request","isForCompensation":false,"completionQuantity":1,"id":"2025.46e58a58-6a17-44bd-807b-f31643801a7a"},{"incoming":["2027.bb576e8e-dc9f-4992-8532-24b61b983a46"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1961,"y":114,"declaredType":"TNodeVisualInfo","height":43}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}],"preAssignmentScript":[]},"name":"End","declaredType":"endEvent","id":"dd400abd-e346-4579-a741-c91be71e13ba"},{"outgoing":["2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17"],"incoming":["2027.a628a18e-a57f-484d-890e-d714a0873119"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17"],"nodeVisualInfo":[{"width":24,"x":782,"y":231,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.8c295691-a3bf-4224-856e-edd0e9ed866b"},{"targetRef":"2025.8c295691-a3bf-4224-856e-edd0e9ed866b","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"ffa16bac-cf6c-4f20-8117-ea23e246de18","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.a628a18e-a57f-484d-890e-d714a0873119","sourceRef":"2025.46e58a58-6a17-44bd-807b-f31643801a7a"},{"targetRef":"2025.e33b4f1b-810b-4ca7-8114-f5c67f3512fe","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"5719c255-a02c-4b8b-814e-900c3221eac6","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.e3d77e41-7957-42f1-8f6f-79ab32bbe009","sourceRef":"2025.46e58a58-6a17-44bd-807b-f31643801a7a"},{"startQuantity":1,"outgoing":["2027.9daf5d03-785b-48a9-820d-f68f1863a784"],"incoming":["2027.e3d77e41-7957-42f1-8f6f-79ab32bbe009"],"default":"2027.9daf5d03-785b-48a9-820d-f68f1863a784","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":910,"y":157,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.e33b4f1b-810b-4ca7-8114-f5c67f3512fe","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false; \r\n var choice = false;\r\n var tempLength = 0 ;\r\n tw.local.invalidTabs = [];\r\ntw.system.coachValidation.clearValidationErrors();\r\n\r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/debugger;\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)\r\n\tmandatory(tw.local.odcRequest.stepLog.comment, \"tw.local.odcRequest.stepLog.comment\");\r\n\r\nif(true){\r\n\t\r\n\/\/-------------------------------------------------BASIC DETAILS VALIDATION -----------------------------------------\r\nmandatory(tw.local.odcRequest.BasicDetails.exportPurpose.name , \"tw.local.odcRequest.BasicDetails.exportPurpose\");\r\nmandatory(tw.local.odcRequest.BasicDetails.paymentTerms.name , \"tw.local.odcRequest.BasicDetails.paymentTerms\");\r\nmandatory(tw.local.odcRequest.BasicDetails.productCategory.name , \"tw.local.odcRequest.BasicDetails.productCategory\");\r\nmandatory(tw.local.odcRequest.BasicDetails.commodityDescription , \"tw.local.odcRequest.BasicDetails.commodityDescription\");\r\nif(tw.local.odcRequest.BasicDetails.Invoice.length==0){\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Invoice , \"tw.local.odcRequest.BasicDetails.Invoice\");\r\n\/\/\/\/\taddError(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo , \"There should be at least one or more invoices\" , tw.resource.ValidationMessages.MandatoryFields, true);\r\n\ttw.local.errorMessage += \"&lt;li&gt;\"+\"Fill in at least one entry in invoices\"+\"&lt;\/li&gt;\";\t\r\n\t}\r\n\telse{\r\n\tfor(var i=0;i&lt;tw.local.odcRequest.BasicDetails.Invoice.length ; i++){\r\n\t\tmandatory(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate , \"tw.local.odcRequest.BasicDetails.Invoice[\"+i+\"].invoiceDate\");\r\n\t\tmandatory(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo , \"tw.local.odcRequest.BasicDetails.Invoice[\"+i+\"].invoiceNo\");\r\n\t\tmaxLength(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo , \"tw.local.odcRequest.BasicDetails.Invoice[\"+i+\"].invoiceNo\", 20 , \"Shouldn't be more than 20 character\" , \"invoice Number: \" + \"Shouldn't be more than 20 character\" );\r\n\t\t}\r\n\t} \r\nif(tw.local.odcRequest.BasicDetails.Bills.length==0 ){\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef , \"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\");\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate , \"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate\");\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Bills , \"tw.local.odcRequest.BasicDetails.Bills\");\r\n\ttw.local.errorMessage += \"&lt;li&gt;\"+\"Fill in at least one entry in Bills\"+\"&lt;\/li&gt;\";\r\n\t}\r\n\telse{\r\n\tfor(var i=0;i&lt;tw.local.odcRequest.BasicDetails.Bills.length ; i++){\t\r\n\t\tmandatory(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate , \"tw.local.odcRequest.BasicDetails.Bills[\"+i+\"].billOfLadingDate\");\r\n\t\tmandatory(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef , \"tw.local.odcRequest.BasicDetails.Bills[\"+i+\"].billOfLadingRef\"); \r\n\t\tmaxLength(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef , \"tw.local.odcRequest.BasicDetails.Bills[\"+i+\"].billOfLadingRef\" ,  20 , \"Shouldn't be more than 20 character\" , \"Bill Of lading Ref : \" + \"Shouldn't be more than 20 character\" );\r\n\t\t}\r\n\t}\t\r\n\/\/if(tw.local.odcRequest.BasicDetails.Invoice.length &gt; 0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate == \"\" || tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate == null))\r\n\/\/if(tw.local.odcRequest.BasicDetails.Invoice.length &gt; 0){\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate , \"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate\");\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo , \"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo\");\r\n\/\/\t}\r\n\/\/\/\/if(tw.local.odcRequest.BasicDetails.Invoice.length &gt; 0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo == \"\" || tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo == null))\r\n\/\/\t{\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo , \"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo\");\r\n\/\/\t}\r\n\t\r\n\/\/if(tw.local.odcRequest.BasicDetails.Bills.length &gt;0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate == \"\" || tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate == null))\r\n\/\/if(tw.local.odcRequest.BasicDetails.Bills.length &gt; 0){\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate , \"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate\");\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef , \"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\");\r\n\/\/\t}\t\r\n\/\/if(tw.local.odcRequest.BasicDetails.Bills.length &gt;0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef == \"\" || tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef == null))\r\n\/\/\t{\r\n\/\/\tmandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef , \"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\");\r\n\/\/\t}\r\n\r\nmandatory(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name , \"tw.local.odcRequest.BasicDetails.CountryOfOrigin\");\r\nmaxLength(tw.local.odcRequest.BasicDetails.flexCubeContractNo , \"tw.local.odcRequest.BasicDetails.flexCubeContractNo\", 16 ,  tw.resource.ValidationMessages.MaxLength16 , \"Flex Cube Contract Number: \" +tw.resource.ValidationMessages.MaxLength16);\r\nmaxLength(tw.local.odcRequest.BasicDetails.commodityDescription , \"tw.local.odcRequest.BasicDetails.commodityDescription\", 160 , \"Shouldn't be more than 160 character\" , \"Commodity Description: \" + \"Shouldn't be more than 160 character\" );\r\n\r\n\/\/for (var i=0 ;i&lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++){\r\n\/\/\tmaxLength(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo , \"tw.local.odcRequest.BasicDetails.Invoice[\"+i+\"].invoiceNo\", 20 , \"Shouldn't be more than 20 character\" , \"invoice Number: \" + \"Shouldn't be more than 20 character\" );\r\n\/\/\t}\r\n\/\/\t\r\n\/\/for(var i=0; i&lt;tw.local.odcRequest.BasicDetails.Bills.length ;i++)\r\n\/\/\tmaxLength(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef , \"tw.local.odcRequest.BasicDetails.Bills[\"+i+\"i].billOfLadingRef\" ,  20 , \"Shouldn't be more than 20 character\" , \"Bill Of lading Ref : \" + \"Shouldn't be more than 20 character\" );\r\n\r\nvalidateTab(0,\"Basic Details\");\r\n\/\/-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------\t\r\nmandatory(tw.local.odcRequest.FinancialDetailsBR.documentAmount , \"tw.local.odcRequest.FinancialDetailsBR.documentAmount\");\r\nmandatory(tw.local.odcRequest.FinancialDetailsBR.currency.name , \"tw.local.odcRequest.FinancialDetailsBR.currency\");\r\nmandatory(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate , \"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate\");\r\n\/\/mandatory(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced , \"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\");\r\nif(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0.0)\r\n{\r\n\tmandatory(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value , \"tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount\");\r\n\tmandatory(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value , \"tw.local.odcRequest.FinancialDetailsBR.collectionAccount\");\r\n}\r\nminLength(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\", 2 ,\"Shouldn't be less than 14 character\",\"Amount Advanced: Shouldn't be less than 2 character\");\r\nmaxLength(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced , \"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\", 14 , \"Shouldn't be more than 14 character\" , \"Amount Advanced:\" + \"Shouldn't be more than 14 character\" );\r\nvalidateTab(3,\"Financial Details - Branch\");\r\n\/\/-----------------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------------\t\r\n\/\/This change according to mosallam meeting 12-11-2023\r\n\/\/mandatory(tw.local.odcRequest.FcCollections.currency.name , \"tw.local.odcRequest.FcCollections.currency\");\r\n\/\/mandatory(tw.local.odcRequest.FcCollections.negotiatedExchangeRate , \"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\");\r\n\/\/mandatory(tw.local.odcRequest.FcCollections.fromDate , \"tw.local.odcRequest.FcCollections.fromDate\");\r\n\/\/mandatory(tw.local.odcRequest.FcCollections.ToDate , \"tw.local.odcRequest.FcCollections.ToDate\");\r\n\/\/mandatory(tw.local.odcRequest.FcCollections.accountNo.value , \"tw.local.odcRequest.FcCollections.accountNo\");\r\n\/\/sumDates(tw.local.odcRequest.FcCollections.fromDate, tw.local.odcRequest.FcCollections.ToDate , \"tw.local.odcRequest.FcCollections.ToDate\" ,\"Search Transactions To Date must be bigger than Search Transactions From Date\" , \"Search Transactions To Date must be bigger than Search Transactions From Date\" );\r\nif(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0.0)\r\n{\r\n\tif(tw.local.odcRequest.FcCollections!= null &amp;&amp; (tw.local.odcRequest.FcCollections.totalAllocatedAmount == 0 || tw.local.odcRequest.FcCollections.totalAllocatedAmount != tw.local.odcRequest.FinancialDetailsBR.amountAdvanced))\r\n\t{\r\n\t\tchoice = confirm(\"Total allocated amount should be equal to the advance amount\");\r\n\t}\r\n\r\nif(tw.local.odcRequest.FcCollections != null &amp;&amp; tw.local.odcRequest.FcCollections.negotiatedExchangeRate != null)\r\n{\r\n\tminLength(tw.local.odcRequest.FcCollections.negotiatedExchangeRate,\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\", 6 ,\"Shouldn't be less than 6 character\",\" Negotiated Exchange Rate: Shouldn't be less than 6 character\");\r\n\tmaxLength(tw.local.odcRequest.FcCollections.negotiatedExchangeRate , \"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\", 10 , \"Shouldn't be more than 10 character\" , \" Negotiated Exchange Rate:\" + \"Shouldn't be more than 10 character\" );\r\n}\r\n}\r\nif(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced == 0.0 &amp;&amp; tw.local.odcRequest.FcCollections.selectedTransactions.length &gt;0)\r\n{\r\n\ttw.local.errorMessage += \"&lt;li&gt;\"+\"No Transactions should be retrieved advanced amount equals zero\"+\"&lt;\/li&gt;\";\t\r\n\ttw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.retrievedTransactions\", \"Mandatory Field can't left blank\");\r\n\t\r\n}\r\nif(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0.0)\r\n{\r\n\t\r\n\tfor(var i=0; i&lt;tw.local.odcRequest.FcCollections.selectedTransactions.length;i++)\r\n\t{\r\n\t var totalTransAmount = tw.local.odcRequest.FcCollections.selectedTransactions[i].existAmount + tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest;\r\n\t\r\n\t if(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount &lt; totalTransAmount)\r\n\t {\r\n\t \t\ttw.local.errorMessage += \"&lt;li&gt;\"+\"Amount Allocated for currenct request and amount linked in bpm shoudn't be greater than transaction amount\"+\"&lt;\/li&gt;\";\t\r\n\t \t\ttw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.selectedTransactions[\"+i+\"].amountAllocatedForCurrencyRequest\", \"Amount Allocated for currenct request and amount linked in bpm shoudn't be greater than transaction amount\");\r\n\t \t\t\r\n\t }\r\n\t \r\n\t}\r\n\r\nif(tw.local.odcRequest.FcCollections.fromDate &gt;= tw.local.odcRequest.FcCollections.ToDate)\r\n{\r\n\ttw.local.errorMessage += \"&lt;li&gt;\"+\" Search Transactions From Date shouln't be greater than Search Transactions To Date\"+\"&lt;\/li&gt;\";\t\r\n\t tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.fromDate\", \"Search Transactions From Date shouln't be greater than Search Transactions To Date\");\r\n\t tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.ToDate\", \"Search Transactions From Date shouln't be greater than Search Transactions To Date\");\r\n}\r\nif(tw.local.odcRequest.FcCollections.selectedTransactions.length !=0)\r\n{\r\n for (var i = 0; i &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; i++) \r\n  {\r\n      for (var j = 0; j &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; j++) \r\n      {\r\n          if (i != j) \r\n          {\r\n              if (tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber == tw.local.odcRequest.FcCollections.selectedTransactions[j].referenceNumber) \r\n              {\r\n\/\/                 alert(\"This Tranaction Reference Number have been added before\");\r\n                 tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.selectedTransactions[\"+i+\"].referenceNumber\", \"This Tranaction Reference Number have been added before\");\r\n                  \r\n              }\r\n            \r\n              \r\n          }\r\n            \r\n      }\r\n  }\r\n\t\t\t    \r\n\r\n}\r\n}\r\nvalidateTab(4,\"Flexcube Collecions\");\r\n\/\/minLength(tw.local.odcRequest.FcCollections.accountNo.value,\"tw.local.odcRequest.FcCollections.accountNo\", 19 ,\"Shouldn't be less than 19 character\",\"Account Number: Shouldn't be less than 19 character\");\r\n}\/\/end of action coond\r\n\r\nconsole.log(tw.system.coachValidation.validationErrors);\r\n\r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- Validation Functions ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/*\r\n* =========================================================================================================\r\n*  \r\n* Add a coach validation error \r\n* \t\t\r\n* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\n*\r\n* =========================================================================================================\r\n*\/\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\n\/*\r\n* =================================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is less than given length\r\n*\t\r\n* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =================================================================================================================================\r\n*\/\r\n\r\nfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\/*\r\n* =======================================================================================================================\r\n*\r\n* Add a coach validation error if the string length is greater than given length\r\n*\t\r\n* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\n*\r\n* =======================================================================================================================\r\n*\/\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\/*\r\n* ==================================================================================================================\r\n*\r\n* Add a coach validation error if the field is null 'Mandatory'\r\n*\t\r\n* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\n*\r\n* ==================================================================================================================\r\n*\/\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (field &lt; 0)\r\n\t\t\t\t{\r\n\t\t\t\t\tvar msg= \"Invalid Value, This field can not be negative value.\";\r\n\t\t\t\t\taddError(fieldName , msg , msg , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\/*\r\n* ==============================================================================================================================\r\n*\r\n* Add a coach validation error if the fromDate bigger than toDate\r\n*\t\r\n* EX:\tsumDates(fomDate , toDate , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')\r\n*\r\n* =============================================================================================================================\r\n*\/\r\n\r\nfunction sumDates(fromDate , toDate , fieldName , controlMessage , validationMessage)\r\n{\r\n\tif(toDate - fromDate == 0)\r\n\t{\r\n\t \treturn true;\r\n\t}\r\n\taddError(fieldName , controlMessage , validationMessage);\r\n\treturn false;\r\n}\r\n\r\n\/\/=================================================================================\r\nfunction checkNegativeValue(field , fieldName)\r\n{\r\n   if (field &lt; 0){\r\n   var msg= \"Invalid Value, This field can not be negative value.\";\r\n   addError(fieldName , msg , msg , true);\r\n   mandatoryTriggered = true;\r\n   return false;\r\n   }\r\n   return true;\r\n}\r\n\/\/=================================================================================\r\nfunction validateTab(index , tabName)\r\n{\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength)\r\n\t{\r\n\t\tif (tw.local.errorMessage.length == 0) {\r\n\t\t\ttw.local.errorMessage += \"&lt;p&gt;\" + \"Please complete fields in the following tabs:\" + \"&lt;\/p&gt;\";\r\n\t\t}\r\n\t\ttw.local.errorMessage += \"&lt;li&gt;\" + tabName + \"&lt;\/li&gt;\";\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length ;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\t\r\n}\r\n\r\n\t\r\n\/\/=================================================================================\t\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";\r\n\t"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.7dc1b5f9-1298-45b1-8518-63942881213e"},{"targetRef":"2025.1db417ca-6523-481d-81bf-55b229eb7dda","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.9daf5d03-785b-48a9-820d-f68f1863a784","sourceRef":"2025.e33b4f1b-810b-4ca7-8114-f5c67f3512fe"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorPanelVIS","isCollection":false,"declaredType":"dataObject","id":"2056.8290a647-ed4e-4117-847f-46d7262f7b9e"},{"startQuantity":1,"outgoing":["2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66"],"incoming":["2027.fcce1260-f60c-46c8-8768-f0db6fef23fd"],"default":"2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66","extensionElements":{"nodeVisualInfo":[{"width":95,"x":-103,"y":81,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.error = {};"]},"name":"update BO value from first coach","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.1b2e8257-1e18-497e-8d78-851099ae9415","scriptFormat":"text\/x-javascript","script":{"content":["\/\/\/\/\/\/\/\/\/\/\/\/\/\/initializing basic details CV\r\nif(!tw.local.odcRequest.BasicDetails.requestNature)\r\n tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\r\ntw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name;\r\ntw.local.odcRequest.BasicDetails.parentRequestNo = tw.local.odcRequest.parentRequestNo;\r\ntw.local.odcRequest.BasicDetails.contractStage = \"Final\";\r\n\r\n\r\n\r\n\/\/\/\/\/\/ initializing Customer CV\r\nif(!tw.local.odcRequest.CustomerInfo) \r\n      tw.local.odcRequest.CustomerInfo = {};\r\nif (!tw.local.odcRequest.CustomerInfo.cif)\r\n\ttw.local.odcRequest.CustomerInfo.cif = tw.local.odcRequest.cif;\r\nif (!tw.local.odcRequest.CustomerInfo.customerName)\r\n\ttw.local.odcRequest.CustomerInfo.customerName = tw.local.odcRequest.customerName;\r\nif (tw.local.odcRequest.GeneratedDocumentInfo == null) {\r\n\ttw.local.odcRequest.GeneratedDocumentInfo = {};\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"001\";\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"Second Mail\";\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = true;\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.customerName = tw.local.odcRequest.CustomerInfo.customerName;\r\n\r\n\r\n}\r\nif (tw.local.odcRequest.CustomerInfo.addressLine1 == null) {\r\n\ttw.local.odcRequest.CustomerInfo.addressLine1 = \"\";\r\n\ttw.local.odcRequest.CustomerInfo.addressLine2 = \"\";\r\n\ttw.local.odcRequest.CustomerInfo.addressLine3 = \"\";\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + \" \" + tw.local.odcRequest.CustomerInfo.addressLine2 + \" \" + tw.local.odcRequest.CustomerInfo.addressLine3;\r\n}\r\nelse if (tw.local.odcRequest.CustomerInfo.addressLine2 == null) {\r\n\ttw.local.odcRequest.CustomerInfo.addressLine2 = \"\";\r\n\ttw.local.odcRequest.CustomerInfo.addressLine3 = \"\";\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + \" \" + tw.local.odcRequest.CustomerInfo.addressLine2 + \" \" + tw.local.odcRequest.CustomerInfo.addressLine3;\r\n}\r\nelse if (tw.local.odcRequest.CustomerInfo.addressLine3 == null) {\r\n\ttw.local.odcRequest.CustomerInfo.addressLine3 = \"\";\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + \" \" + tw.local.odcRequest.CustomerInfo.addressLine2 + \" \" + tw.local.odcRequest.CustomerInfo.addressLine3;\r\n}\r\nelse {\r\n\ttw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + tw.local.odcRequest.CustomerInfo.addressLine2 + tw.local.odcRequest.CustomerInfo.addressLine3;\r\n}\r\nif (tw.local.odcRequest.CustomerInfo.customerType != \"\" &amp;&amp; tw.local.odcRequest.CustomerInfo.customerType == \"C\") {\r\n\ttw.local.odcRequest.CustomerInfo.customerType = \"Corporate\";\r\n}\r\nelse {\r\n\ttw.local.odcRequest.CustomerInfo.customerType = \"Individual\";\r\n}\r\n\/\/tw.local.odcRequest.BasicDetails.Bills[0] = {};\r\n\/\/tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\n\/\/tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\n\r\n\/\/tw.local.odcRequest.BasicDetails.Invoice[0] = {};\r\n\/\/tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new Date();\r\n\/\/tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"\";\r\ntw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;\r\ntw.local.deliveryterms = tw.epv.TermsAndConditions.deliveryTerms;\r\ntw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;\r\ntw.local.instructions = tw.epv.TermsAndConditions.instructions;\r\n\r\ntw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Draft;\r\n\r\ntw.local.odcRequest.stepLog = {};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT01;\r\n\r\ntw.local.actionConditions = {};\r\ntw.local.actionConditions.complianceApproval = false;\r\ntw.local.actionConditions.screenName = tw.epv.ScreenNames.CACT01;\r\n\r\ntw.local.actionConditions.userRole = tw.local.initiator;\r\ntw.local.odcRequest.initiator = tw.local.initiator;\/\/Branch or hub\r\ntw.local.actionConditions.lastStepAction = \"\";\r\ntw.local.actionConditions.requestType = tw.local.odcRequest.requestType.value;\r\n\r\nvar date = new Date();\r\ntw.local.odcRequest.appInfo.requestDate = date.getDate() + '\/' + (date.getMonth() + 1) + '\/' + date.getFullYear();\r\ntw.local.odcRequest.appInfo.branch = {};\r\ntw.local.odcRequest.appInfo.branch.name = tw.local.branchName;\r\ntw.local.odcRequest.appInfo.branch.value = tw.local.branchCode;\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName + \"( \" + tw.system.user.name + \")\";\/\/initiator user name\r\ntw.local.odcRequest.appInfo.requestName = tw.epv.ProcessDetails.processName;\r\ntw.local.odcRequest.appInfo.requestType = tw.epv.ProcessDetails.suffixName;\r\ntw.local.odcRequest.appInfo.status = \"Initiated\";\r\ntw.local.odcRequest.appInfo.subStatus = \"Initiated\";\r\ntw.local.odcRequest.appInfo.appRef = \"\";\r\ntw.local.odcRequest.appInfo.appID = \"\";\r\n\/\/tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;\r\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.CACT01;\r\nif (tw.local.branchCode != \"\" &amp;&amp; tw.local.branchCode != null) {\r\n\ttw.local.code = tw.local.branchCode;\r\n\ttw.local.role = tw.epv.userRole.branch;\r\n}\r\nelse {\r\n\ttw.local.code = tw.local.hubCode;\r\n\ttw.local.role = tw.epv.userRole.hub;\r\n\ttw.local.odcRequest.appInfo.branch.name = \"HUB \" + tw.local.hubCode;\r\n\ttw.local.odcRequest.appInfo.branch.value = \"HUB \" + tw.local.hubCode;\r\n}\r\n\r\ntw.local.errorPanelVIS = \"NONE\";\r\n"]}},{"outgoing":["2027.fab42a6b-ed96-48f1-80ea-4d8fc49b39f3","2027.36d368c9-8fe5-4613-857d-6b78975ca83b"],"incoming":["2027.9daf5d03-785b-48a9-820d-f68f1863a784"],"default":"2027.36d368c9-8fe5-4613-857d-6b78975ca83b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1047,"y":258,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.1db417ca-6523-481d-81bf-55b229eb7dda"},{"targetRef":"2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.fab42a6b-ed96-48f1-80ea-4d8fc49b39f3","sourceRef":"2025.1db417ca-6523-481d-81bf-55b229eb7dda"},{"incoming":["2027.36d368c9-8fe5-4613-857d-6b78975ca83b"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1058,"y":346,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 2","declaredType":"intermediateThrowEvent","id":"2025.bad324e2-7784-4a4e-8230-d7de0a2dc2bf"},{"targetRef":"2025.bad324e2-7784-4a4e-8230-d7de0a2dc2bf","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.36d368c9-8fe5-4613-857d-6b78975ca83b","sourceRef":"2025.1db417ca-6523-481d-81bf-55b229eb7dda"},{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"tableName","isCollection":false,"declaredType":"dataObject","id":"2056.58e85f42-1971-4d20-81ca-0860aeb5ab8c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.935f2a35-5377-476b-8d6c-2e3a779389f3"},{"outgoing":["2027.245a4797-cab9-41b8-8610-6e51dc5b7da1"],"incoming":["2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4"],"extensionElements":{"postAssignmentScript":["tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = tw.local.odcRequest.folderPath;\r\n\r\ntw.local.odcRequest.attachmentDetails.folderID = tw.local.odcRequest.folderID;\r\n "],"nodeVisualInfo":[{"width":95,"x":585,"y":81,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.245a4797-cab9-41b8-8610-6e51dc5b7da1","name":"Create ECM Folder","dataInputAssociation":[{"targetRef":"2055.4156964b-1c67-40bc-8f62-3804c71cf908","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.e2ce0eed-342c-4942-8214-83e964b550e5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.code"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.7e5a7e9c-39e6-4ab6-84a7-c7c6e834b6ea","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderID"]}}],"sourceRef":["2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}],"sourceRef":["2055.5f955245-0538-4e40-80a6-12f45c3102f3"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.214c7268-80d0-444d-8702-dd0d5462dbe7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.676e3a06-e2cc-4855-84d6-6f82a350500a"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderPath"]}}],"sourceRef":["2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7"]}],"calledElement":"1.46b984a3-b4ad-405a-abd3-8631f907efe4"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.3440f870-ff23-4c8b-807b-82f6195cb0ac"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.4f575929-abd6-495f-8279-27ad84b9e83a"},{"targetRef":"2025.819f5c1b-594a-44a2-8a30-d73903741b3b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To is update?","declaredType":"sequenceFlow","id":"2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66","sourceRef":"2025.1b2e8257-1e18-497e-8d78-851099ae9415"},{"targetRef":"2025.46e58a58-6a17-44bd-807b-f31643801a7a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Create \/ Amend Request","declaredType":"sequenceFlow","id":"2027.245a4797-cab9-41b8-8610-6e51dc5b7da1","sourceRef":"2025.7e5a7e9c-39e6-4ab6-84a7-c7c6e834b6ea"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cifNo","isCollection":false,"declaredType":"dataObject","id":"2056.636f1fc7-df9d-4c8a-8ac1-a08cf8fd89bb"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"code","isCollection":false,"declaredType":"dataObject","id":"2056.d13f9520-dab1-4199-89c0-4fc470136286"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"paymentTerms","isCollection":false,"declaredType":"dataObject","id":"2056.c0fc0852-5a22-4c78-8338-2f33e3d59575"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"deliveryterms","isCollection":false,"declaredType":"dataObject","id":"2056.b0f66c87-0d0d-4fac-8b46-e60d9f9f3a53"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instructions","isCollection":false,"declaredType":"dataObject","id":"2056.2b50d167-9dee-4c17-891b-999f5a634f7d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"specialInstructions","isCollection":false,"declaredType":"dataObject","id":"2056.dd12b9c9-3ac9-49c7-891c-57fde4c88f8e"},{"outgoing":["2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d"],"incoming":["2027.4e805889-60ae-4790-8410-c2c59cca283f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1072,"y":-2,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d","name":"Get account transactions","dataInputAssociation":[{"targetRef":"2055.4b2027cd-0272-4e63-8d83-8e29bae003ca","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.fromDate"]}}]},{"targetRef":"2055.2566eac7-c31f-48ea-8a24-9b38b1022610","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.ToDate"]}}]},{"targetRef":"2055.c480ed60-61cd-4824-8a4f-181e6b5ad541","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.accountNo.value"]}}]},{"targetRef":"2055.676facb9-f340-4be1-8aec-d2bdee4233af","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.accountNo.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.28b35608-231d-45e7-823b-f4f3e2c86f67","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.retrievedTransactions"]}}],"sourceRef":["2055.863e4f64-1adc-44a3-8b7c-f14f92f00d66"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.1fb10221-c28b-436a-846e-60bdaa692291"]}],"calledElement":"1.e48b8940-fb79-4255-9301-1ce59d8cfa3b"},{"startQuantity":1,"outgoing":["2027.7b4d33ce-7300-4150-8abf-dee5da66eba7"],"incoming":["2027.9054de94-cc5b-4018-8c9d-4a9ac615462c","2027.6a90d411-7682-4e7a-8d71-30028735cfd9","2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e"],"default":"2027.7b4d33ce-7300-4150-8abf-dee5da66eba7","extensionElements":{"nodeVisualInfo":[{"width":95,"x":201,"y":81,"declaredType":"TNodeVisualInfo","height":70}]},"name":"visibility conditions","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.125eab48-4fda-4887-841c-c11355ab579c","scriptFormat":"text\/x-javascript","script":{"content":["\/*Basic Details CV Visibility*\/\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\n\ttw.local.parentRequestNoVIS = \"READONLY\";\r\n\ttw.local.currencyListVIS = \"READONLY\";\r\n}\t\r\nelse{\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\n\ttw.local.currencyListVIS = \"Editable\";\r\n}\t\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\n}\r\nelse{\r\ntw.local.contractStageVIS = \"NONE\";\r\n}\r\n\/*Document Generation Section*\/\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nif(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){\r\n\ttw.local.requestTypeVIS = \"EDITABLE\";\r\n}\r\nelse{\r\n\ttw.local.requestTypeVIS = \"NONE\";\t\r\n}\r\ntw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name  ;\r\ntw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name  ;"]}},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNoVIS","isCollection":false,"declaredType":"dataObject","id":"2056.c4c3b157-8024-436e-8ab5-59fcdbd737c2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"retrieveBtnVIS","isCollection":false,"declaredType":"dataObject","id":"2056.6c865b7b-c862-4323-8c5c-97510fd0ef97"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"createBtnVis","isCollection":false,"declaredType":"dataObject","id":"2056.d662f048-a8f8-4cc5-8df0-04861cd25913"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"flexCubeContractNoVIS","isCollection":false,"declaredType":"dataObject","id":"2056.ec0d54ed-2198-461b-87e3-6cf4fe46fad0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"contractStageVIS","isCollection":false,"declaredType":"dataObject","id":"2056.2d2655f2-55d8-4bd4-836c-8f176c739995"},{"targetRef":"2025.0aa24670-c849-405c-87f0-2ceaba6a718b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Attachments list","declaredType":"sequenceFlow","id":"2027.7b4d33ce-7300-4150-8abf-dee5da66eba7","sourceRef":"2025.125eab48-4fda-4887-841c-c11355ab579c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestTypeVIS","isCollection":false,"declaredType":"dataObject","id":"2056.01606556-d295-4b99-88bf-d896554ab43e"},{"targetRef":"2025.46e58a58-6a17-44bd-807b-f31643801a7a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomRight","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Create \/ Amend Request","declaredType":"sequenceFlow","id":"2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17","sourceRef":"2025.8c295691-a3bf-4224-856e-edd0e9ed866b"},{"startQuantity":1,"outgoing":["2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98"],"incoming":["2027.f417b728-8cd1-4be3-8b8f-d4666914c55c"],"default":"2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98","extensionElements":{"nodeVisualInfo":[{"width":95,"x":446,"y":-13,"declaredType":"TNodeVisualInfo","height":70}]},"name":"add a row to selected transaction list","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.fb51b0af-8183-424c-8dea-eaeee10e3cda","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.collectionCurrencyVis = \"READONLY\";\r\n\r\n\r\nvar selectedTransactionsIndices  = [];\r\nselectedTransactionsIndices = tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelectedIndices;\r\n\r\nif(tw.local.odcRequest.FcCollections.selectedTransactions.length &gt; 0) {\r\n   var oldSelectedTransactions =  tw.local.odcRequest.FcCollections.selectedTransactions; \r\n   tw.local.odcRequest.FcCollections.selectedTransactions =  [];\r\n   for(var i=0;i&lt;selectedTransactionsIndices.length;i++)\r\n    {\r\n        \r\n       tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length] = tw.local.odcRequest.FcCollections.retrievedTransactions[selectedTransactionsIndices[i]];\r\n      \r\n      for (var j =0 ;j&lt;oldSelectedTransactions.length;j++){\r\n\r\n\r\n         if(tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length-1].referenceNumber ==oldSelectedTransactions[j].referenceNumber ){\r\n             tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length-1].amountAllocatedForCurrencyRequest = oldSelectedTransactions[j].amountAllocatedForCurrencyRequest;\r\n             tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length-1].allocatedAmountInRequestCurrency = oldSelectedTransactions[j].allocatedAmountInRequestCurrency;\r\n      }\r\n    }\r\n   }\r\n     \r\n}      \r\nelse{\r\n   tw.local.odcRequest.FcCollections.selectedTransactions =  [];\r\n   for(var i=0;i&lt;selectedTransactionsIndices.length;i++)\r\n    {\r\n        \r\n       tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length] = tw.local.odcRequest.FcCollections.retrievedTransactions[selectedTransactionsIndices[i]];\r\n       \r\n     }\r\n }    \r\n\/\/------\r\n\/\/-----------------------------------------------\r\n\r\n\/\/var array = \"\";\r\n\/\/var subarray = \"\";\r\n\/\/\r\n\/\/ array = tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelectedIndices;\r\n\/\/\r\n\/\/\r\n\/\/\r\n\/\/for(var i=0;i&lt;array.length;i++)\r\n\/\/{\r\n\/\/\r\n\/\/\tsubarray = array.slice(i,i+1);\r\n\/\/\t\r\n\/\/\t\tif(tw.local.odcRequest.FcCollections.selectedTransactions != null &amp;&amp; tw.local.odcRequest.FcCollections.selectedTransactions.length == 0)\r\n\/\/\t\t{\r\n\/\/\t\ttw.local.odcRequest.FcCollections.selectedTransactions[i] = {};\r\n\/\/\t\ttw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].accountNo;\r\n\/\/\t\ttw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].referenceNumber;\r\n\/\/\t\ttw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].postingDate;\r\n\/\/\t\ttw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].transactionAmount;\r\n\/\/\t\ttw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].valueDate;\r\n\/\/\t\ttw.local.odcRequest.FcCollections.selectedTransactions[i].existAmount = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].existAmount;\r\n\/\/\t\t}\r\n\/\/\t\telse\r\n\/\/\t\t{\r\n\/\/\/\/\t\t\t        for (var i = 0; i &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; i++) \r\n\/\/\/\/\t\t\t        {\r\n\/\/\/\/\t\t\t            for (var j = 0; j &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; j++) \r\n\/\/\/\/\t\t\t            {\r\n\/\/\/\/\t\t\t                if (i != j) \r\n\/\/\/\/\t\t\t                {\r\n\/\/\/\/\t\t\t                    if (tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber == tw.local.odcRequest.FcCollections.selectedTransactions[j].referenceNumber) \r\n\/\/\/\/\t\t\t                    {\r\n\/\/\/\/\/\/\t\t\t                       alert(\"This Tranaction Reference Number have been added before\");\r\n\/\/\/\/\t\t\t                       tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.selectedTransactions[\"+i+\"].referenceNumber\", \"This Tranaction Reference Number have been added before\");\r\n\/\/\/\/\t\t\t                        \r\n\/\/\/\/\t\t\t                    }\r\n\/\/\/\/\t\t\t                  \r\n\/\/\/\/\t\t\t                    \r\n\/\/\/\/\t\t\t                }\r\n\/\/\/\/\t\t\t                  \r\n\/\/\/\/\t\t\t            }\r\n\/\/\/\/\t\t\t        }\r\n\/\/\/\/\t\t\t         \r\n\/\/\t\t\t                     tw.local.odcRequest.FcCollections.selectedTransactions.push(tw.local.odcRequest.FcCollections.retrievedTransactions[subarray]);\r\n\/\/\t\t\t                    \r\n\/\/\t\t\t      \r\n\/\/\t\t\t    }\r\n\/\/}\r\n\r\n\r\n\r\n"]}},{"targetRef":"2025.c809aba7-3270-4571-8a29-c5c009a87243","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create \/ Amend Request","declaredType":"sequenceFlow","id":"2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98","sourceRef":"2025.fb51b0af-8183-424c-8dea-eaeee10e3cda"},{"outgoing":["2027.f417b728-8cd1-4be3-8b8f-d4666914c55c","2027.7170da1b-ba91-424e-833a-b411ef3b412d"],"default":"2027.f417b728-8cd1-4be3-8b8f-d4666914c55c","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":645,"y":-67,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":[]},"name":"rows selected?","declaredType":"exclusiveGateway","id":"2025.2889d0ab-cf30-46b0-8919-9c1d896beb5e"},{"targetRef":"2025.fb51b0af-8183-424c-8dea-eaeee10e3cda","conditionExpression":{"declaredType":"TFormalExpression","content":["!!tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelected"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"rows selected","declaredType":"sequenceFlow","id":"2027.f417b728-8cd1-4be3-8b8f-d4666914c55c","sourceRef":"2025.2889d0ab-cf30-46b0-8919-9c1d896beb5e"},{"startQuantity":1,"outgoing":["2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d"],"incoming":["2027.7170da1b-ba91-424e-833a-b411ef3b412d"],"default":"2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d","extensionElements":{"nodeVisualInfo":[{"width":95,"x":740,"y":-150,"declaredType":"TNodeVisualInfo","height":70}]},"name":"display error message","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a99010f5-f4f0-4a56-8e2d-3f0655e71cfe","scriptFormat":"text\/x-javascript","script":{"content":["\/\/ \r\n\/\/ tw.local.errorMessage += \"&lt;li&gt;\"+\"Select one or more Transaction\"+\"&lt;\/li&gt;\";\t\r\nalert(\"Select one or more Transaction\");"]}},{"targetRef":"2025.a99010f5-f4f0-4a56-8e2d-3f0655e71cfe","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelectedIndices.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.7170da1b-ba91-424e-833a-b411ef3b412d","sourceRef":"2025.2889d0ab-cf30-46b0-8919-9c1d896beb5e"},{"targetRef":"2025.46e58a58-6a17-44bd-807b-f31643801a7a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create \/ Amend Request","declaredType":"sequenceFlow","id":"2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d","sourceRef":"2025.a99010f5-f4f0-4a56-8e2d-3f0655e71cfe"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"collectionCurrencyVis","isCollection":false,"declaredType":"dataObject","id":"2056.7c986a7b-8a80-42bc-868c-22410aeddcad"},{"startQuantity":1,"outgoing":["2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb"],"default":"2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb","extensionElements":{"postAssignmentScript":["tw.local.odcRequest.FcCollections.negotiatedExchangeRate = tw.local.odcRequest.FcCollections.standardExchangeRate;\r\ntw.local.cifCurrency = tw.local.odcRequest.CustomerInfo.cif +\"_\"+ tw.local.odcRequest.FcCollections.currency.name;\r\nconsole.log(tw.local.odcRequest.FcCollections.standardExchangeRate);"],"nodeVisualInfo":[{"width":95,"x":660,"y":248,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Get exchange rate","dataInputAssociation":[{"targetRef":"2055.4f75807d-191a-4553-809f-630208b0d737","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FinancialDetailsBR.currency.name"]}}]},{"targetRef":"2055.cc3906a0-dd9d-4580-8485-14caef94ac8d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.currency.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.30c2df0f-2ec6-4f9b-85d3-219773f46111","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.standardExchangeRate"]}}],"sourceRef":["2055.a1cc30d0-50bc-4feb-8427-9f8aafe9ef96"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.7e0973d8-72ed-4b76-81df-4cda85422d2c"]}],"calledElement":"1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f"},{"startQuantity":1,"outgoing":["2027.99a0385c-a950-49d3-88a0-1a3042dd4586"],"incoming":["2027.3bb12c6a-ecf3-4a7d-858c-8925b89ebc23","2027.9a43cba3-9c21-4454-8d9c-4a57a6c8102e","2027.fab42a6b-ed96-48f1-80ea-4d8fc49b39f3"],"default":"2027.99a0385c-a950-49d3-88a0-1a3042dd4586","extensionElements":{"nodeVisualInfo":[{"width":95,"x":1421,"y":172,"declaredType":"TNodeVisualInfo","height":70}]},"name":"setting status and sub status","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.submitRequest){\r\n\ttw.local.odcRequest.appInfo.status    = \"Initiated\";\r\n\tif(tw.local.role == tw.epv.userRole.branch){\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Branch Compliance Initiation Review\";\r\n\t}\r\n\telse if(tw.local.role == tw.epv.userRole.hub){\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Hub Compliance Initiation Review\";\r\n\t}\r\n}\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequestToHubDirectory){\r\n\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing\";\r\n}\r\nelse if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest){\r\n\ttw.local.odcRequest.appInfo.status    = \"Initiated\";\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Cancelation Confirmation\";\r\n}\r\n\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequestToHubDirectory)\r\n{\r\n\ttw.local.odcRequest.FinancialDetailsFO.executionHub.value = tw.local.hubCode;\r\n}\r\n\/\/\/\/\/\/\/\/\/\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)\r\n{\r\n\ttw.local.lastAction = tw.epv.CreationActions.cancelRequest;\r\n}"]}},{"targetRef":"2025.769b40fb-8c10-42e7-82ab-ee90fe3eed81","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.99a0385c-a950-49d3-88a0-1a3042dd4586","sourceRef":"2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91"},{"outgoing":["2027.2f58c309-16be-4828-88a5-42a22d7c66a6"],"incoming":["2027.99a0385c-a950-49d3-88a0-1a3042dd4586"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1540,"y":92,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.2f58c309-16be-4828-88a5-42a22d7c66a6","name":"Audit Request History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.role"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.769b40fb-8c10-42e7-82ab-ee90fe3eed81","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.a617c560-c740-484e-89de-0931088cdc6c"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"targetRef":"2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Audited?","declaredType":"sequenceFlow","id":"2027.2f58c309-16be-4828-88a5-42a22d7c66a6","sourceRef":"2025.769b40fb-8c10-42e7-82ab-ee90fe3eed81"},{"outgoing":["2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4"],"incoming":["2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":445,"y":81,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4","name":"Set ECM default properties","dataInputAssociation":[{"targetRef":"2055.399c7a58-00b5-4451-9813-41c0b9652088","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.cif"]}}]},{"targetRef":"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}]},{"targetRef":"2055.25394215-074f-4b79-8e84-9a96d32cc83b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.e6de2c56-d47d-4541-8874-43156e2b1a04","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.0261e8ad-a540-4682-88c5-87dff3eab23c"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails"]}}],"sourceRef":["2055.7d269650-ee48-4101-80db-2807cf921562"]}],"calledElement":"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"conditions","isCollection":false,"declaredType":"dataObject","id":"2056.25df8083-a36e-46f4-8f74-c21352472afc"},{"startQuantity":1,"outgoing":["2027.f03be96c-9d23-4354-8ddc-b8c195291e0b"],"default":"2027.f03be96c-9d23-4354-8ddc-b8c195291e0b","extensionElements":{"nodeVisualInfo":[{"width":95,"x":868,"y":-52,"declaredType":"TNodeVisualInfo","height":70}]},"name":"calculate total allocated amount ","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.add865fd-cfd5-4045-841f-9e4c95d9423c","scriptFormat":"text\/x-javascript","script":{"content":["var sum = 0;\r\nif(tw.local.odcRequest.FcCollections.selectedTransactions.length != 0)\r\n{\r\n\r\n\tfor(var i=0;i&lt;tw.local.odcRequest.FcCollections.selectedTransactions.length;i++)\r\n\t{ \r\n\t      tw.local.odcRequest.FcCollections.selectedTransactions[i].allocatedAmountInRequestCurrency = tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest * tw.local.odcRequest.FcCollections.negotiatedExchangeRate; \r\n\t\tsum += tw.local.odcRequest.FcCollections.selectedTransactions[i].allocatedAmountInRequestCurrency;\r\n\t\t\r\n\t}\r\n\ttw.local.odcRequest.FcCollections.totalAllocatedAmount = sum;\r\n}"]}},{"incoming":["2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d","2027.f03be96c-9d23-4354-8ddc-b8c195291e0b","2027.14334ed8-4d74-46d3-8646-fc7e2595281d"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1229,"y":20,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 6","declaredType":"intermediateThrowEvent","id":"2025.1459d000-61ea-441b-8c7e-25136085e9fa"},{"targetRef":"2025.1459d000-61ea-441b-8c7e-25136085e9fa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page 6","declaredType":"sequenceFlow","id":"2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d","sourceRef":"2025.28b35608-231d-45e7-823b-f4f3e2c86f67"},{"targetRef":"2025.1459d000-61ea-441b-8c7e-25136085e9fa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page 6","declaredType":"sequenceFlow","id":"2027.f03be96c-9d23-4354-8ddc-b8c195291e0b","sourceRef":"2025.add865fd-cfd5-4045-841f-9e4c95d9423c"},{"incoming":["2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb","2027.285ce338-e84a-4dce-89dc-0853289ffc52"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":664,"y":340,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 7","declaredType":"intermediateThrowEvent","id":"2025.d12bae84-1eca-4b47-85d9-59ff8d89281d"},{"targetRef":"2025.d12bae84-1eca-4b47-85d9-59ff8d89281d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page 7","declaredType":"sequenceFlow","id":"2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb","sourceRef":"2025.30c2df0f-2ec6-4f9b-85d3-219773f46111"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"new Date()"}]},"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"today","isCollection":false,"declaredType":"dataObject","id":"2056.539b1ec1-0a89-447a-81ea-f3f12bda1101"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cifCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.fb2c23ad-6257-4292-82aa-e1975e8254dc"},{"outgoing":["2027.9054de94-cc5b-4018-8c9d-4a9ac615462c"],"incoming":["2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098"],"extensionElements":{"postAssignmentScript":["tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;"],"nodeVisualInfo":[{"width":95,"x":81,"y":81,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.9054de94-cc5b-4018-8c9d-4a9ac615462c","name":"Generate request No","dataInputAssociation":[{"targetRef":"2055.d063d99f-aaa6-427d-ac13-2ac9946acbda","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.code"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.2b5c0a5f-7672-46bd-8a52-37ae501d7d58","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}],"sourceRef":["2055.3bdd8527-404f-4ac0-bd04-07931e8d167e"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.9a8f8a58-0e47-413f-b430-fb905024fce6"]}],"calledElement":"1.8e583b1e-1719-4e19-a6ce-6f41202527d4"},{"targetRef":"2025.125eab48-4fda-4887-841c-c11355ab579c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Attachment","declaredType":"sequenceFlow","id":"2027.9054de94-cc5b-4018-8c9d-4a9ac615462c","sourceRef":"2025.2b5c0a5f-7672-46bd-8a52-37ae501d7d58"},{"outgoing":["2027.5e6497c2-313c-40f1-81c5-137dd55f746b"],"incoming":["2027.2c918199-35a7-45e1-8eb6-096f6ada59e3"],"extensionElements":{"postAssignmentScript":["console.log(\"******************************************************\");\r\nconsole.log(\"SQL=  \"+tw.local.sqlTemp);\r\nconsole.log(\"******************************************************\");"],"nodeVisualInfo":[{"width":95,"x":1741,"y":92,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.5e6497c2-313c-40f1-81c5-137dd55f746b","name":"Audit request Data","dataInputAssociation":[{"targetRef":"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.3ed54d76-c867-4a7b-8891-68bc17aea4ee","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.254cf8eb-2743-4c53-8c52-e51c8c22884e"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sqlTemp"]}}],"sourceRef":["2055.e6f312e7-4580-4d58-8a10-d7720006d8bf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestID"]}}],"sourceRef":["2055.59dc474f-3e47-40ee-8737-ad21d25eb436"]}],"calledElement":"1.7ee96dd0-834b-44cb-af41-b21585627e49"},{"targetRef":"2025.1d5b803f-b012-4bd6-82e3-6fffae922c98","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.5e6497c2-313c-40f1-81c5-137dd55f746b","sourceRef":"2025.3ed54d76-c867-4a7b-8891-68bc17aea4ee"},{"outgoing":["2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098","2027.b3442983-6e1e-4389-8f15-576414d3da2b"],"incoming":["2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66"],"default":"2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":32,"x":0,"y":100,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is update?","declaredType":"exclusiveGateway","id":"2025.819f5c1b-594a-44a2-8a30-d73903741b3b"},{"targetRef":"2025.2b5c0a5f-7672-46bd-8a52-37ae501d7d58","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098","sourceRef":"2025.819f5c1b-594a-44a2-8a30-d73903741b3b"},{"targetRef":"2025.5b11ce9d-63dc-45ab-8334-76f7216f2803","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNature.name\t  ==\t  tw.epv.RequestNature.UpdateRequest"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Retrieve Parent Request Number","declaredType":"sequenceFlow","id":"2027.b3442983-6e1e-4389-8f15-576414d3da2b","sourceRef":"2025.819f5c1b-594a-44a2-8a30-d73903741b3b"},{"outgoing":["2027.6a90d411-7682-4e7a-8d71-30028735cfd9"],"incoming":["2027.b3442983-6e1e-4389-8f15-576414d3da2b"],"extensionElements":{"postAssignmentScript":["tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;"],"nodeVisualInfo":[{"width":95,"x":85,"y":-9,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.6a90d411-7682-4e7a-8d71-30028735cfd9","name":"Retrieve Parent Request Number","dataInputAssociation":[{"targetRef":"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.parentRequestNo"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.5b11ce9d-63dc-45ab-8334-76f7216f2803","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}],"sourceRef":["2055.29fabc80-90b8-4cad-81e3-1c319c6f595a"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.4ec4b61d-5cd9-43b6-827c-c1801162373f"]}],"calledElement":"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546"},{"targetRef":"2025.125eab48-4fda-4887-841c-c11355ab579c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.6a90d411-7682-4e7a-8d71-30028735cfd9","sourceRef":"2025.5b11ce9d-63dc-45ab-8334-76f7216f2803"},{"outgoing":["2027.fcce1260-f60c-46c8-8768-f0db6fef23fd","2027.367d7cf7-1d8b-4fdf-882c-796796f01226"],"default":"2027.367d7cf7-1d8b-4fdf-882c-796796f01226","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":-245,"y":101,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Is FirstTime","declaredType":"exclusiveGateway","id":"2025.d4d6d8cb-2e86-4941-844e-63bcb70d177b"},{"targetRef":"2025.1b2e8257-1e18-497e-8d78-851099ae9415","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime\t  ==\t  true"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.fcce1260-f60c-46c8-8768-f0db6fef23fd","sourceRef":"2025.d4d6d8cb-2e86-4941-844e-63bcb70d177b"},{"targetRef":"2025.55db037b-3a91-4884-8d92-bd45588aef5f","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.367d7cf7-1d8b-4fdf-882c-796796f01226","sourceRef":"2025.d4d6d8cb-2e86-4941-844e-63bcb70d177b"},{"startQuantity":1,"outgoing":["2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e"],"incoming":["2027.367d7cf7-1d8b-4fdf-882c-796796f01226"],"default":"2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e","extensionElements":{"nodeVisualInfo":[{"width":95,"x":-103,"y":185,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Update BO Values","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.55db037b-3a91-4884-8d92-bd45588aef5f","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.stepLog ={};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT01;\r\n\r\ntw.local.actionConditions = {};\r\ntw.local.actionConditions.complianceApproval= false;\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT01;\r\ntw.local.actionConditions.userRole= tw.local.initiator;\r\ntw.local.actionConditions.lastStepAction=\"\";\r\ntw.local.actionConditions.requestType = tw.local.odcRequest.requestType.value;\r\ntw.local.odcRequest.stepLog.action =\"\";\r\n\r\ntw.local.odcRequest.initiator = tw.local.initiator;\/\/Branch or hub\r\n\/\/tw.local.odcRequest.appInfo.instanceID = tw.system.processInstance.id; \r\n\ttw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT01;\r\n\tif(tw.local.branchCode != \"\" &amp;&amp; tw.local.branchCode != null)\r\n\t{\r\n\t\ttw.local.role = tw.epv.userRole.branch;\r\n\t}\r\n\telse\r\n\t{\r\n\t\ttw.local.role =tw.epv.userRole.hub;\r\n\t}\r\n\r\ntw.local.odcRequest.appInfo.instanceID = tw.local.odcRequest.requestNo;"]}},{"targetRef":"2025.125eab48-4fda-4887-841c-c11355ab579c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To is update?","declaredType":"sequenceFlow","id":"2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e","sourceRef":"2025.55db037b-3a91-4884-8d92-bd45588aef5f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"currencyListVIS","isCollection":false,"declaredType":"dataObject","id":"2056.76107c34-e108-4643-8da1-34f54054e499"},{"outgoing":["2027.bb576e8e-dc9f-4992-8532-24b61b983a46","2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6"],"incoming":["2027.5e6497c2-313c-40f1-81c5-137dd55f746b"],"default":"2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1852,"y":111,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.1d5b803f-b012-4bd6-82e3-6fffae922c98"},{"targetRef":"dd400abd-e346-4579-a741-c91be71e13ba","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.bb576e8e-dc9f-4992-8532-24b61b983a46","sourceRef":"2025.1d5b803f-b012-4bd6-82e3-6fffae922c98"},{"incoming":["2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6","2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1828,"y":212,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.90b9f7d5-9708-485d-85bc-48249004dc25"},{"targetRef":"2025.90b9f7d5-9708-485d-85bc-48249004dc25","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.errorMessage\t  !=\t  null"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6","sourceRef":"2025.1d5b803f-b012-4bd6-82e3-6fffae922c98"},{"outgoing":["2027.2c918199-35a7-45e1-8eb6-096f6ada59e3","2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f"],"incoming":["2027.2f58c309-16be-4828-88a5-42a22d7c66a6"],"default":"2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1649,"y":112,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Audited?","declaredType":"exclusiveGateway","id":"2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9"},{"targetRef":"2025.3ed54d76-c867-4a7b-8891-68bc17aea4ee","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.2c918199-35a7-45e1-8eb6-096f6ada59e3","sourceRef":"2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9"},{"targetRef":"2025.90b9f7d5-9708-485d-85bc-48249004dc25","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f","sourceRef":"2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sqlTemp","isCollection":false,"declaredType":"dataObject","id":"2056.0d50691a-03ec-493f-82d0-a3b808faa7b3"},{"startQuantity":1,"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":359,"y":-121,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"name":"Service","dataInputAssociation":[{"targetRef":"2055.f43a0726-0192-4d4c-942e-83e973ee5015","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails.folderID"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.df00826b-75d0-4379-84fc-7d1c724e3e46","calledElement":"1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7"},{"startQuantity":1,"outgoing":["2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418"],"incoming":["2027.7b4d33ce-7300-4150-8abf-dee5da66eba7"],"default":"2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418","extensionElements":{"nodeVisualInfo":[{"width":95,"x":314,"y":81,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Attachments list","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.0aa24670-c849-405c-87f0-2ceaba6a718b","scriptFormat":"text\/x-javascript","script":{"content":["\/\/var index= 0;\r\n\r\n\/\/if(!tw.local.odcRequest.attachmentDetails)\r\n\/\/  tw.local.odcRequest.attachmentDetails = {};\r\n\/\/\r\n\/\/if (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {   \r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment = []; \t\r\n\t\t\r\n\/\/\tsetProps(\"AIRWAY BILL\", \"AIRWAY BILL\", \"\" ); \r\n\/\/\tsetProps(\"TRUCK CONSIGNMENT NOTE\",\"TRUCK CONSIGNMENT NOTE\", \"\"); \r\n\/\/\tsetProps(\"N\/N BILL OF LADING\",\"N\/N BILL OF LADING\",\"\"); \r\n\/\/\tsetProps(\"COURIER \/ POST RECEIPT\", \"COURIER \/ POST RECEIPT\" , \"\" ); \r\n\/\/\tsetProps(\"PACKING LIST\",\"PACKING LIST\",\"\"); \r\n\/\/\tsetProps(\"CERTIFICATE OF ORIGIN\",\"CERTIFICATE OF ORIGIN\", \"\" );\r\n\/\/\tsetProps(\"CERTIFICATE OF ANALYSIS\", \"CERTIFICATE OF ANALYSIS\", \"\");\r\n\/\/\tsetProps(\"INSURANCE POLICY \/ CERTIFICATE\", \"INSURANCE POLICY \/ CERTIFICATE\",\"\");\r\n\/\/\tsetProps(\"BENEFECIARY DECLARATION\", \"BENEFECIARY DECLARATION\", \"\");\r\n\/\/\tsetProps(\"NON RADIOACTIVE CERTIFICATE\", \"NON RADIOACTIVE CERTIFICATE\",\"\");\r\n\/\/\tsetProps(\"PHYTOSANITARY CERTIFICATE\", \"PHYTOSANITARY CERTIFICATE\",\"\");\r\n\/\/\tsetProps(\"CERTIFICATE OF ANALYSIS\",\"Bill of exchange\/draft\", \"\u0627\u0644\u0643\u0645\u0628\u064a\u0627\u0644\u0629\" );\r\n\/\/\tsetProps(\"HEALTH CERTIFICATE\",\"HEALTH CERTIFICATE\", \"\");\r\n\/\/\tsetProps(\"INSPECTION CERTIFICATE\", \"INSPECTION CERTIFICATE\",  \"\");\r\n\/\/\tsetProps(\"WARRANTY CERTIFICATE\", \"WARRANTY CERTIFICATE\",\"\");\r\n\/\/\tsetProps( \"TEST CERTIFICATE\",\"TEST CERTIFICATE\", \"\");\r\n\/\/\r\n\/\/\ttw.local.odcRequest.attachmentDetails.ecmProperties = {};\r\n\/\/\ttw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\n\/\/\ttw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};\r\n\/\/}\t\r\n\/\/\r\n\/\/function setProps(name, desc, arabicName){\t\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index]= {};\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].name= name;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].description= desc;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].arabicName= arabicName;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].numOfOriginals= 0;\r\n\/\/\ttw.local.odcRequest.attachmentDetails.attachment[index].numOfCopies= 0;\r\n\/\/\tindex++;\t\r\n\/\/}\r\n\/\/\r\n"]}},{"targetRef":"2025.e6de2c56-d47d-4541-8874-43156e2b1a04","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set ECM default properties","declaredType":"sequenceFlow","id":"2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418","sourceRef":"2025.0aa24670-c849-405c-87f0-2ceaba6a718b"},{"outgoing":["2027.7115e13c-7593-4314-8f4b-f49796567d59"],"incoming":["2027.d5b1e172-ea40-4e43-847c-6417d46aaa14"],"extensionElements":{"postAssignmentScript":["if(tw.local.docValMsg!= null || tw.local.docValMsg!=undefined){\r\n\ttw.local.errorMessage+= tw.local.docValMsg;\r\n\ttw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";\r\n\r\n}"],"nodeVisualInfo":[{"width":95,"x":1200,"y":165,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.errorMessage= \"\";\r\n\r\nvar list_attachments = tw.local.odcRequest.attachmentDetails.attachment;\r\nvar list_selAttachments = list_attachments.listAllSelectedIndices || []; \r\nvar isChecked_CustRequest = false;\r\n\r\nfor( var i=0; i&lt;list_selAttachments.length; i++)\r\n{\r\n\tvar index= list_selAttachments[i];\r\n\t\r\n\tif( list_attachments[index].name == \"Customer Request\" ) {\r\n\t\tisChecked_CustRequest = true;\r\n\t\tbreak;\r\n\t}\r\n}\r\n\r\nif( isChecked_CustRequest == false)    \r\n\ttw.local.errorMessage += \"&lt;li&gt;\"+ \"Please Check 'Customer Request' Document.\" +\"&lt;\/li&gt;\";\r\n\t\r\ntw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";"]},"declaredType":"callActivity","startQuantity":1,"default":"2027.7115e13c-7593-4314-8f4b-f49796567d59","name":"Validate Mandatory Docs","dataInputAssociation":[{"targetRef":"2055.1bf8ade2-bd6b-4af2-87c8-1b269002413f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestType.value"]}}]},{"targetRef":"2055.f43a0726-0192-4d4c-942e-83e973ee5015","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails.folderID"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.2bc8a62f-795f-4fd1-81a0-ea668ed25f2c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.validateDocMsg"]}}],"sourceRef":["2055.c965f53e-7627-46ac-8f53-b8a0e4d9e4fb"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.docValMsg"]}}],"sourceRef":["2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b"]}],"calledElement":"1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7"},{"targetRef":"2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.7115e13c-7593-4314-8f4b-f49796567d59","sourceRef":"2025.2bc8a62f-795f-4fd1-81a0-ea668ed25f2c"},{"outgoing":["2027.3bb12c6a-ecf3-4a7d-858c-8925b89ebc23","2027.c7e218da-97a5-4c47-8687-c48d630d6dc4"],"incoming":["2027.7115e13c-7593-4314-8f4b-f49796567d59"],"default":"2027.c7e218da-97a5-4c47-8687-c48d630d6dc4","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":32,"x":1335,"y":188,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2"},{"targetRef":"2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.3bb12c6a-ecf3-4a7d-858c-8925b89ebc23","sourceRef":"2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2"},{"incoming":["2027.c7e218da-97a5-4c47-8687-c48d630d6dc4"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1320,"y":345,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}],"preAssignmentScript":["\/\/console.log(\"logg error msg= \"+ tw.local.errorMessage);\r\ntw.local.errorMessage!=null ?  tw.local.errorPanelVIS =\"EDITABLE\": tw.local.errorPanelVIS =\"NONE\";"]},"name":"Stay on page 3","declaredType":"intermediateThrowEvent","id":"2025.06fec280-6dce-4af7-8731-033834d10d5c"},{"targetRef":"2025.06fec280-6dce-4af7-8731-033834d10d5c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.c7e218da-97a5-4c47-8687-c48d630d6dc4","sourceRef":"2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validateDocMsg","isCollection":false,"declaredType":"dataObject","id":"2056.316e2023-7ea1-4df1-84bd-cc961bfbecc3"},{"targetRef":"2025.7e5a7e9c-39e6-4ab6-84a7-c7c6e834b6ea","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create ECM Folder","declaredType":"sequenceFlow","id":"2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4","sourceRef":"2025.e6de2c56-d47d-4541-8874-43156e2b1a04"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"docValMsg","isCollection":false,"declaredType":"dataObject","id":"2056.9dc8b867-8bad-4166-808b-e4ad0f33dd88"},{"outgoing":["2027.d5b1e172-ea40-4e43-847c-6417d46aaa14","2027.9a43cba3-9c21-4454-8d9c-4a57a6c8102e"],"default":"2027.d5b1e172-ea40-4e43-847c-6417d46aaa14","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1107,"y":182,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Action?","declaredType":"exclusiveGateway","id":"2025.0986e69a-6cb9-44dd-82b2-a42f1e0fac00"},{"targetRef":"2025.2bc8a62f-795f-4fd1-81a0-ea668ed25f2c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"submit","declaredType":"sequenceFlow","id":"2027.d5b1e172-ea40-4e43-847c-6417d46aaa14","sourceRef":"2025.0986e69a-6cb9-44dd-82b2-a42f1e0fac00"},{"targetRef":"2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"cancel","declaredType":"sequenceFlow","id":"2027.9a43cba3-9c21-4454-8d9c-4a57a6c8102e","sourceRef":"2025.0986e69a-6cb9-44dd-82b2-a42f1e0fac00"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"choice","isCollection":false,"declaredType":"dataObject","id":"2056.76f7b38e-ea7a-43bd-84e6-b1e004009a2e"},{"startQuantity":1,"outgoing":["2027.4513a1a9-7623-4051-81c6-3465578f5640"],"default":"2027.4513a1a9-7623-4051-81c6-3465578f5640","extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":910,"y":53,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a0837de4-4431-4576-8ecd-6c743b723d43","scriptFormat":"text\/x-javascript","script":{"content":["if(!tw.local.odcRequest.FcCollections.fromDate &amp;&amp; !tw.local.odcRequest.FcCollections.ToDate)\t\r\n\tif(tw.local.odcRequest.FcCollections.fromDate &gt;= tw.local.odcRequest.FcCollections.ToDate)\r\n\t{\r\n\t\ttw.local.errorMessage += \"&lt;li&gt;\"+\" Search Transactions From Date shouln't be greater than Search Transactions To Date\"+\"&lt;\/li&gt;\";\t\r\n\t\t tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.fromDate\", \"Search Transactions From Date shouln't be greater than Search Transactions To Date\");\r\n\t\t tw.system.coachValidation.addValidationError(\"tw.local.odcRequest.FcCollections.ToDate\", \"Search Transactions From Date shouln't be greater than Search Transactions To Date\");\r\n\t}"]}},{"targetRef":"2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.4513a1a9-7623-4051-81c6-3465578f5640","sourceRef":"2025.a0837de4-4431-4576-8ecd-6c743b723d43"},{"outgoing":["2027.14334ed8-4d74-46d3-8646-fc7e2595281d","2027.4e805889-60ae-4790-8410-c2c59cca283f"],"incoming":["2027.4513a1a9-7623-4051-81c6-3465578f5640"],"default":"2027.14334ed8-4d74-46d3-8646-fc7e2595281d","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1041,"y":89,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273"},{"targetRef":"2025.1459d000-61ea-441b-8c7e-25136085e9fa","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Stay on page 6","declaredType":"sequenceFlow","id":"2027.14334ed8-4d74-46d3-8646-fc7e2595281d","sourceRef":"2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273"},{"targetRef":"2025.28b35608-231d-45e7-823b-f4f3e2c86f67","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Get account transactions","declaredType":"sequenceFlow","id":"2027.4e805889-60ae-4790-8410-c2c59cca283f","sourceRef":"2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273"},{"outgoing":["2027.a4212e56-9bb4-41c2-812b-e4dd08de0584","2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4"],"incoming":["2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98"],"default":"2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":372,"y":5,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway 1","declaredType":"exclusiveGateway","id":"2025.c809aba7-3270-4571-8a29-c5c009a87243"},{"targetRef":"2025.46e58a58-6a17-44bd-807b-f31643801a7a","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Create \/ Amend Request","declaredType":"sequenceFlow","id":"2027.a4212e56-9bb4-41c2-812b-e4dd08de0584","sourceRef":"2025.c809aba7-3270-4571-8a29-c5c009a87243"},{"incoming":["2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":255,"y":-8,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 4","declaredType":"intermediateThrowEvent","id":"2025.4634743f-b927-43ef-8dc9-0d4512fa9fec"},{"targetRef":"2025.4634743f-b927-43ef-8dc9-0d4512fa9fec","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page 4","declaredType":"sequenceFlow","id":"2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4","sourceRef":"2025.c809aba7-3270-4571-8a29-c5c009a87243"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"oldSelectedIndices","isCollection":true,"declaredType":"dataObject","id":"2056.4ab1f720-7e58-4e17-8640-c3834348b0a4"},{"startQuantity":1,"outgoing":["2027.285ce338-e84a-4dce-89dc-0853289ffc52"],"default":"2027.285ce338-e84a-4dce-89dc-0853289ffc52","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":414,"y":185,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Get Account Numbers","dataInputAssociation":[{"targetRef":"2055.0bc98ce8-10aa-460a-8857-8a8600dafc90","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.CustomerInfo.cif"]}}]},{"targetRef":"2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.currency.value"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.97508aa7-a872-4e8f-8926-162c835ea004","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.odcRequest.FcCollections.listOfAccounts"]}}],"sourceRef":["2055.208f75bb-611b-4393-83d6-d60470c4a6a4"]}],"calledElement":"1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f"},{"targetRef":"2025.d12bae84-1eca-4b47-85d9-59ff8d89281d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Stay on page 7","declaredType":"sequenceFlow","id":"2027.285ce338-e84a-4dce-89dc-0853289ffc52","sourceRef":"2025.97508aa7-a872-4e8f-8926-162c835ea004"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.4bdd7c1f-1123-46cb-88c0-927d2d1e2423"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"validationMsg","isCollection":false,"declaredType":"dataObject","id":"2056.384aa163-2cab-40ca-8350-af8f35164f95"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"ef16a6ed-4dbc-4c15-9de7-b885ea6d3eb3","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"8f44955b-6062-4a44-bbb3-5f15c9404677","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"Act01 - Create or Amend ODC Request","declaredType":"globalUserTask","id":"1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.c6e44d18-bb22-407c-8c94-33e273a84088"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.1ae8623e-a0d0-455e-8199-92f720530682"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"regeneratedRemittanceLetterTitleVIS","isCollection":false,"id":"2055.17b2fc8d-62c3-4cbc-8046-1daaee9f0658"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.4371b561-70e9-4ac1-898e-91948aadac07"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"role","isCollection":false,"id":"2055.42d6ab5b-c60b-4768-89d1-cabaa3f9dd78"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.1583433b-7213-455b-8bfb-f438e5f671d8"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"envProcessLinks":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEnvProcessLinks","envProcessLinkRef":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEnvProcessLinkRef","envProcessLinkId":"e82e0b4d-3a0d-4e3a-8171-4be6eb6409e0","envId":"2094.63e43d67-f3a4-4489-8560-5c6a837d5dab"},{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEnvProcessLinkRef","envProcessLinkId":"5b10ea2d-86dc-4be5-87c3-af547cfa971b","envId":"2094.6592f70d-f558-4606-8b84-18b641c868fe"}]}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"6d73953a-7ab6-4eb3-86d1-3a621b59928e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"3daf28b6-a1ea-429d-864b-51546a24d9fb","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.93c5c002-7ac4-4283-83ee-63b8662f9223","epvProcessLinkId":"411e58bc-2e05-4e97-8c75-48a63a398250","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"499195eb-f3f5-4d40-8279-7ef02448ec2b","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.769dc134-1d15-4dd4-a967-c5f61cf352dc","epvProcessLinkId":"93922f64-3a85-4073-864a-a37ed98751ea","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"588e65bb-4b8f-4ba8-89d5-75f52b0b3d12","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.062854b5-6513-4da8-84ab-0126f90e550d","epvProcessLinkId":"6a7a416e-c510-48fc-84bb-0037adb307b8","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"89ea6690-4305-4b7e-8825-14250a80df23","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"_b8cc2321-d8b1-4647-83c1-b912d1889625"}],"outputSet":[{"id":"_4619ef27-6b4c-4f9d-aa0d-6c6c1269cdbd"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.initiator = \"\";\nautoObject.requestNature = {};\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = {};\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new Date();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = {};\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = {};\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = {};\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = {};\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = {};\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = {};\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = {};\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = {};\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = [];\nautoObject.BasicDetails.Bills[0] = {};\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\nautoObject.BasicDetails.Invoice = [];\nautoObject.BasicDetails.Invoice[0] = {};\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\nautoObject.GeneratedDocumentInfo = {};\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = [];\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = {};\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = {};\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = {};\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = [];\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = {};\nautoObject.FcCollections.currency = {};\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new Date();\nautoObject.FcCollections.ToDate = new Date();\nautoObject.FcCollections.accountNo = {};\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = [];\nautoObject.FcCollections.retrievedTransactions[0] = {};\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = [];\nautoObject.FcCollections.selectedTransactions[0] = {};\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = [];\nautoObject.FcCollections.listOfAccounts[0] = {};\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = {};\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new Date();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = {};\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = [];\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = {};\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = {};\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = {};\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new Date();\nautoObject.ProductShipmentDetails.shipmentMethod = {};\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = {};\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = {};\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = {};\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = {};\nautoObject.ContractCreation.productCode = {};\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new Date();\nautoObject.ContractCreation.valueDate = new Date();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new Date();\nautoObject.Parties = {};\nautoObject.Parties.Drawer = {};\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = {};\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = {};\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = {};\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = {};\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.Parties.caseInNeed = {};\nautoObject.Parties.caseInNeed.partyCIF = \"\";\nautoObject.Parties.caseInNeed.partyId = \"\";\nautoObject.Parties.caseInNeed.partyName = \"\";\nautoObject.Parties.caseInNeed.country = \"\";\nautoObject.Parties.caseInNeed.language = \"\";\nautoObject.Parties.caseInNeed.refrence = \"\";\nautoObject.Parties.caseInNeed.address1 = \"\";\nautoObject.Parties.caseInNeed.address2 = \"\";\nautoObject.Parties.caseInNeed.address3 = \"\";\nautoObject.Parties.caseInNeed.partyType = {};\nautoObject.Parties.caseInNeed.partyType.name = \"\";\nautoObject.Parties.caseInNeed.partyType.value = \"\";\nautoObject.ChargesAndCommissions = [];\nautoObject.ChargesAndCommissions[0] = {};\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ChargesAndCommissions[0].isGLFound = false;\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\nautoObject.ContractLiquidation = {};\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new Date();\nautoObject.ContractLiquidation.creditValueDate = new Date();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.debitedAccountName = \"\";\nautoObject.ContractLiquidation.creditedAccount = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = {};\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = {};\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = [];\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = {};\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = {};\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = [];\nautoObject.attachmentDetails.attachment[0] = {};\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = [];\nautoObject.complianceComments[0] = {};\nautoObject.complianceComments[0].startTime = new Date();\nautoObject.complianceComments[0].endTime = new Date();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = [];\nautoObject.History[0] = {};\nautoObject.History[0].startTime = new Date();\nautoObject.History[0].endTime = new Date();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = {};\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject.folderPath = \"\";\nautoObject.templateDocID = \"\";\nautoObject.requestID = 0;\nautoObject.customerAndPartyAccountList = [];\nautoObject.customerAndPartyAccountList[0] = {};\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\nautoObject.customerAndPartyAccountList[0].frozen = false;\nautoObject.customerAndPartyAccountList[0].dormant = false;\nautoObject.customerAndPartyAccountList[0].noDebit = false;\nautoObject.customerAndPartyAccountList[0].noCredit = false;\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\nautoObject.tradeFoComment = \"\";\nautoObject.exeHubMkrComment = \"\";\nautoObject.compcheckerComment = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.52b23fff-ad18-4c9c-8d4b-da25fa353d74"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"hubCode","isCollection":false,"id":"2055.3db632ba-2dff-49ab-81f3-ecc3512ed61a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchCode","isCollection":false,"id":"2055.e60856e2-88c3-449a-8b95-682aa6f088ab"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"branchName","isCollection":false,"id":"2055.c17f6b1c-420e-4316-8602-278ac2e5f98d"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isFirstTime","isCollection":false,"id":"2055.262e9fc1-8a4f-4bee-81b6-3ebbcd392f6d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"initiator","isCollection":false,"id":"2055.df6d7638-3ff3-45a9-817e-e243fd7e7807"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.5e731f35-27b7-4ff1-852c-7479e82b9115"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"85f7c5b8-9c66-4f73-9714-a117d7bd6e77"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.52b23fff-ad18-4c9c-8d4b-da25fa353d74</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>092e7665-b6e4-4104-9720-b99a77c7b49d</guid>
            <versionId>5cf0537a-6561-4a54-b0bd-5d8c69109342</versionId>
        </processParameter>
        <processParameter name="hubCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3db632ba-2dff-49ab-81f3-ecc3512ed61a</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9054b425-b23d-4a44-8836-9bfe79fa134b</guid>
            <versionId>6fff81ed-7b42-4057-85af-eb01cad5b541</versionId>
        </processParameter>
        <processParameter name="branchCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.e60856e2-88c3-449a-8b95-682aa6f088ab</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7fb3b087-8fad-427f-ada8-ff476101a96e</guid>
            <versionId>51e85739-2b2a-4168-8293-ac0fc720c092</versionId>
        </processParameter>
        <processParameter name="branchName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c17f6b1c-420e-4316-8602-278ac2e5f98d</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d1b649a3-375d-4456-8d5f-f33f48f4a5d3</guid>
            <versionId>2a17139f-c5e9-4f5e-895b-bd3d5947316e</versionId>
        </processParameter>
        <processParameter name="isFirstTime">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.262e9fc1-8a4f-4bee-81b6-3ebbcd392f6d</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0b280b49-c4d8-4a6b-9c02-bfd892e49478</guid>
            <versionId>3fe7c355-d01a-4ef7-b442-f3e88286af23</versionId>
        </processParameter>
        <processParameter name="initiator">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.df6d7638-3ff3-45a9-817e-e243fd7e7807</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1624845d-b39e-4261-bf0f-a5b33f0404e1</guid>
            <versionId>c64c0a1e-42d7-4ec9-a679-412cb854b495</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5e731f35-27b7-4ff1-852c-7479e82b9115</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>829690c8-5815-4b60-871b-206d82b233a5</guid>
            <versionId>8fa4a349-8526-4c74-b11d-1391ac87567b</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c6e44d18-bb22-407c-8c94-33e273a84088</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d6ed7579-1651-42bd-ab9f-3dc3fe103c0b</guid>
            <versionId>84ce809c-4d80-475a-8288-b74079c9644e</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1ae8623e-a0d0-455e-8199-92f720530682</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a02007c6-20ab-4e28-92b5-828e6d5eef30</guid>
            <versionId>e720bb19-37ce-446f-9461-f1eb7f94b841</versionId>
        </processParameter>
        <processParameter name="regeneratedRemittanceLetterTitleVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.17b2fc8d-62c3-4cbc-8046-1daaee9f0658</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1d61a937-6c3c-4171-8323-59b8891b4684</guid>
            <versionId>f816b5e2-369e-4a0e-94b1-096b1be54914</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4371b561-70e9-4ac1-898e-91948aadac07</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>11</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3984ab02-a0f3-44ad-836a-5d2cb0dd78d3</guid>
            <versionId>236dcc62-56b5-4a75-99e8-57bb2ce9d37c</versionId>
        </processParameter>
        <processParameter name="role">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.42d6ab5b-c60b-4768-89d1-cabaa3f9dd78</processParameterId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>12</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f6746057-ea62-4399-9b09-35f14bcd75d5</guid>
            <versionId>afe8b062-ae44-427b-87ff-dce84df55f15</versionId>
        </processParameter>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7dc1b5f9-1298-45b1-8518-63942881213e</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0cf008b8-c539-4e31-a6a1-0176bce245bc</guid>
            <versionId>5bd57ace-a811-439f-8e9e-b0cb41154839</versionId>
        </processVariable>
        <processVariable name="errorPanelVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8290a647-ed4e-4117-847f-46d7262f7b9e</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5289cc43-d595-4ed0-a195-596d03c82cf6</guid>
            <versionId>24d9e5ba-286d-49fb-8265-6d3bf02a947d</versionId>
        </processVariable>
        <processVariable name="tableName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.58e85f42-1971-4d20-81ca-0860aeb5ab8c</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fe61123a-12b1-4335-ba93-87bd1ec56116</guid>
            <versionId>118676d7-08ff-4d41-95d4-e4a14a7c2ee0</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.935f2a35-5377-476b-8d6c-2e3a779389f3</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e90b2618-1770-4ddb-b255-9b574b64e8b2</guid>
            <versionId>1e9e571e-65fa-4891-aed4-1a62c67c1deb</versionId>
        </processVariable>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3440f870-ff23-4c8b-807b-82f6195cb0ac</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c6dfbbb3-7f67-4c80-a6e6-3f3f2a1e95a6</guid>
            <versionId>9e31e595-8990-41ed-9cab-818a0d7cef96</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4f575929-abd6-495f-8279-27ad84b9e83a</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c6370a90-09b4-4dc5-b83e-f38203ea3845</guid>
            <versionId>4cc695cf-293a-4606-a710-60b342e4b398</versionId>
        </processVariable>
        <processVariable name="cifNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.636f1fc7-df9d-4c8a-8ac1-a08cf8fd89bb</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a82036c8-2547-48dd-b785-8827d744b781</guid>
            <versionId>369044b9-8079-4936-a9f4-9a7dc59ee221</versionId>
        </processVariable>
        <processVariable name="code">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d13f9520-dab1-4199-89c0-4fc470136286</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>69c89cf1-9b6c-44f4-82ac-a3bdd21805b9</guid>
            <versionId>9b8da88a-5fb3-4ea5-9f48-3ed4b2f19289</versionId>
        </processVariable>
        <processVariable name="paymentTerms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c0fc0852-5a22-4c78-8338-2f33e3d59575</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4027af72-17b3-4f76-8e05-613df22e54a9</guid>
            <versionId>19c3d639-3330-4564-9ebd-36b6f0ea3ab4</versionId>
        </processVariable>
        <processVariable name="deliveryterms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b0f66c87-0d0d-4fac-8b46-e60d9f9f3a53</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>68917f2e-40e1-460f-9c4b-c82511ce03f6</guid>
            <versionId>d4e31e1f-b9fe-486b-b265-95b4e6784802</versionId>
        </processVariable>
        <processVariable name="instructions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2b50d167-9dee-4c17-891b-999f5a634f7d</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>59b0aff6-320a-47e0-9b99-054d067365e3</guid>
            <versionId>e6deddc5-9d8e-418e-b3e3-2e69cfe8904c</versionId>
        </processVariable>
        <processVariable name="specialInstructions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.dd12b9c9-3ac9-49c7-891c-57fde4c88f8e</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fbaa3642-5dcf-4c14-9cdc-98aa3310c356</guid>
            <versionId>7e3dfeef-0555-4b41-9433-d13e34d15700</versionId>
        </processVariable>
        <processVariable name="parentRequestNoVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c4c3b157-8024-436e-8ab5-59fcdbd737c2</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>530d312e-1628-4425-b0c6-ae1de661ca2c</guid>
            <versionId>032cff29-d9e4-45f8-bf0d-6ba55a27eaaa</versionId>
        </processVariable>
        <processVariable name="retrieveBtnVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6c865b7b-c862-4323-8c5c-97510fd0ef97</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9f2adee9-5d37-48b9-b6d3-bc17a2958cd9</guid>
            <versionId>0e5919c6-694b-4655-829b-b0589d8aac3f</versionId>
        </processVariable>
        <processVariable name="createBtnVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d662f048-a8f8-4cc5-8df0-04861cd25913</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>56b2b3f7-bfd1-41b3-a34b-6794eb01c5d8</guid>
            <versionId>2e210812-a2cb-4231-be68-e9fec5441f2c</versionId>
        </processVariable>
        <processVariable name="flexCubeContractNoVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ec0d54ed-2198-461b-87e3-6cf4fe46fad0</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ae3f4c1f-f013-4beb-8cec-6660982d866a</guid>
            <versionId>c09e3cce-b4d0-4af5-9ba6-c8fac9777d47</versionId>
        </processVariable>
        <processVariable name="contractStageVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2d2655f2-55d8-4bd4-836c-8f176c739995</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>7c4c7eb2-0367-4caa-ad7b-df2741f5d008</guid>
            <versionId>2b8bed13-0ff2-4de4-b28c-6e0acf6731c0</versionId>
        </processVariable>
        <processVariable name="requestTypeVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.01606556-d295-4b99-88bf-d896554ab43e</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>abde7df5-8649-48a8-9e30-2c8e3e6beeb7</guid>
            <versionId>77840da3-a3a3-4b85-ad4c-88648fff0933</versionId>
        </processVariable>
        <processVariable name="collectionCurrencyVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7c986a7b-8a80-42bc-868c-22410aeddcad</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>57cffc8d-9a65-4020-acbd-1b6f819863da</guid>
            <versionId>35e5fd69-9e62-4347-9c4e-ffba610d6135</versionId>
        </processVariable>
        <processVariable name="conditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.25df8083-a36e-46f4-8f74-c21352472afc</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>200cd836-525f-453b-bd73-d36db915199b</guid>
            <versionId>601e4c24-8f52-4102-9302-98d8af2da1b0</versionId>
        </processVariable>
        <processVariable name="today">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.539b1ec1-0a89-447a-81ea-f3f12bda1101</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>99c2a0a1-eb70-40f0-856b-9e7d1a86699f</guid>
            <versionId>26e0cdb4-4e69-4214-b47a-bc9e8fafd0a1</versionId>
        </processVariable>
        <processVariable name="cifCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fb2c23ad-6257-4292-82aa-e1975e8254dc</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3092f6ae-8229-45a8-8f7a-f9de2b25e296</guid>
            <versionId>8ee38b4e-14a3-470f-ad7d-bcd60b0ae9bf</versionId>
        </processVariable>
        <processVariable name="currencyListVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.76107c34-e108-4643-8da1-34f54054e499</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>da6d1a39-2533-49c5-9dce-a895a8a50d27</guid>
            <versionId>6e30943f-ad5a-4ae3-8c0d-448cdd834915</versionId>
        </processVariable>
        <processVariable name="sqlTemp">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0d50691a-03ec-493f-82d0-a3b808faa7b3</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>664c9182-e175-4923-b902-bc33ac65477e</guid>
            <versionId>62cf6922-5364-4d54-a2b7-00134dfb022c</versionId>
        </processVariable>
        <processVariable name="validateDocMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.316e2023-7ea1-4df1-84bd-cc961bfbecc3</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>02f0488d-4e6b-4c9d-a980-fce66cc7e6b8</guid>
            <versionId>a07ea3b1-b537-48c7-ab2f-4b8675bc0111</versionId>
        </processVariable>
        <processVariable name="docValMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9dc8b867-8bad-4166-808b-e4ad0f33dd88</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e40efe1f-5592-4ae8-bcd9-c198a68a3cbe</guid>
            <versionId>0a6aa02b-3ca5-45a5-92c3-3d40af5de338</versionId>
        </processVariable>
        <processVariable name="choice">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.76f7b38e-ea7a-43bd-84e6-b1e004009a2e</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>29ebe7a5-7bc9-4df3-98d4-91fc238b43a9</guid>
            <versionId>09725427-586a-49d0-a041-268a261302a6</versionId>
        </processVariable>
        <processVariable name="oldSelectedIndices">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4ab1f720-7e58-4e17-8640-c3834348b0a4</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6c55c925-5921-415d-9352-fd0ccc8b5b51</guid>
            <versionId>7a9386af-bcb0-4523-a8ac-5d637b30b99d</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4bdd7c1f-1123-46cb-88c0-927d2d1e2423</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>29</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>85b98bc2-3965-4776-b2d4-f8ae811f3e05</guid>
            <versionId>aa95d853-f5a7-41f8-80db-8e4c02b44378</versionId>
        </processVariable>
        <processVariable name="validationMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.384aa163-2cab-40ca-8350-af8f35164f95</processVariableId>
            <description isNull="true" />
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <namespace>2</namespace>
            <seq>30</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a30179a4-807f-48f3-9e5e-b95c4203c940</guid>
            <versionId>dbbca18d-b871-4be0-af18-69fe222455a3</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.30c2df0f-2ec6-4f9b-85d3-219773f46111</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Get exchange rate</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.02884352-7555-4bae-bba9-60130271481f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a23e9995f:2b43</guid>
            <versionId>26936085-124c-4ae5-bd15-a6648feb9253</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.02884352-7555-4bae-bba9-60130271481f</subProcessId>
                <attachedProcessRef>/1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</attachedProcessRef>
                <guid>9c9051c3-d839-4cec-a3d7-f0e2ba882273</guid>
                <versionId>7cbcd8ed-cbb3-41e8-8e58-d27e6536d98d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.136e3662-e6aa-49a8-a4ba-e15111c65f8c</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.ee0c58f6-3ee2-4f61-af08-33e26828882b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:debeaea024f39647:-512ef1a:189a6650968:-3e33</guid>
            <versionId>3fd68a94-12e4-410a-ba32-5b96961a4a6a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2bc8a62f-795f-4fd1-81a0-ea668ed25f2c</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Validate Mandatory Docs</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e36d6582-**************-e1b15319f8f5</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:665</guid>
            <versionId>7a8a627a-e3cc-420e-9b15-c01e0cb5153d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e36d6582-**************-e1b15319f8f5</subProcessId>
                <attachedProcessRef>/1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</attachedProcessRef>
                <guid>b06c9bc8-2fb6-4689-bdba-2350067663b0</guid>
                <versionId>********-0bb6-45dc-bbb1-0368149902d7</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.97508aa7-a872-4e8f-8926-162c835ea004</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Get Account Numbers</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.a5d534f3-17fa-49f4-919f-eddd9c5a00e2</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:2b1528dec8afbe86:-********:18be81b4797:-77c8</guid>
            <versionId>97f627af-e548-41a7-a781-7be1258ca886</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.a5d534f3-17fa-49f4-919f-eddd9c5a00e2</subProcessId>
                <attachedProcessRef>/1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</attachedProcessRef>
                <guid>26c33267-963f-44ea-b936-77eacc2d271a</guid>
                <versionId>140b14ce-fe7a-4300-a0d7-da8c60226462</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.18417b70-6cda-48e4-99fe-9db5c676caf7</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ff40fb7b-1eee-4a99-a7fb-93cdfd5a80dc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:debeaea024f39647:-512ef1a:189a6650968:-3e34</guid>
            <versionId>9a554b21-e439-476b-a05a-1bc412e30035</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ff40fb7b-1eee-4a99-a7fb-93cdfd5a80dc</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>18443f4b-c923-4247-9ffe-11e3ade7b832</guid>
                <versionId>fbc87d1a-1f1a-43b6-ad94-f2d19300cd15</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7e5a7e9c-39e6-4ab6-84a7-c7c6e834b6ea</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Create ECM Folder</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.8d2ddce0-1328-4824-8371-3f3bc2e100a0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:83e1efe624431d49:-7084ad56:189daab98ba:4944</guid>
            <versionId>b3b7ea9a-3631-4f39-a40a-6837b6951dac</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.8d2ddce0-1328-4824-8371-3f3bc2e100a0</subProcessId>
                <attachedProcessRef>/1.46b984a3-b4ad-405a-abd3-8631f907efe4</attachedProcessRef>
                <guid>59f014f3-90fc-4620-911f-d74ae613f1b1</guid>
                <versionId>fd6a74c8-8ef2-436d-91f1-bb68699a51d3</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3ed54d76-c867-4a7b-8891-68bc17aea4ee</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Audit request Data</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.734c9bf2-6eaf-4f17-befb-94d59c23769e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:54cd</guid>
            <versionId>b42605aa-644c-4875-8015-574022371e87</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.734c9bf2-6eaf-4f17-befb-94d59c23769e</subProcessId>
                <attachedProcessRef>/1.7ee96dd0-834b-44cb-af41-b21585627e49</attachedProcessRef>
                <guid>d31b4b8a-d5dc-4c23-bf39-e4857dc7a8c5</guid>
                <versionId>4545d7fd-5157-4738-a7d7-6c5b6968c9ed</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2b5c0a5f-7672-46bd-8a52-37ae501d7d58</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Generate request No</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4309fd12-d4c4-4464-81ae-86bc542637c7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:549f</guid>
            <versionId>b5bbe4d6-b38a-4334-b802-1f7a9146f280</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4309fd12-d4c4-4464-81ae-86bc542637c7</subProcessId>
                <attachedProcessRef>/1.8e583b1e-1719-4e19-a6ce-6f41202527d4</attachedProcessRef>
                <guid>0efeaa70-48a2-45f5-bf81-d579f555be9e</guid>
                <versionId>8562ed58-74d4-41ee-8b06-c713f2aeb3ea</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5b11ce9d-63dc-45ab-8334-76f7216f2803</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Retrieve Parent Request Number</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.6fade0ed-f63f-4d9f-bfcb-dcad8e8f38f7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aff098473ecd546d:1d42df0a:18b1b2b8841:29d8</guid>
            <versionId>bae02338-fedf-4b60-aaee-cad73548618c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.6fade0ed-f63f-4d9f-bfcb-dcad8e8f38f7</subProcessId>
                <attachedProcessRef>/1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</attachedProcessRef>
                <guid>12829280-239e-42a4-85d6-e4bfc5608371</guid>
                <versionId>ca01db53-e325-4790-8d52-5d8a49d1a2dd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.769b40fb-8c10-42e7-82ab-ee90fe3eed81</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Audit Request History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c90ef413-a3c1-4f14-b77d-54522c25a45b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a31a47ae9:6717</guid>
            <versionId>bd4dbd59-8b2d-4a60-bd9b-da86ac712d47</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c90ef413-a3c1-4f14-b77d-54522c25a45b</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>94867b16-92eb-4c47-a568-24758103e6d9</guid>
                <versionId>80dcb177-f9d6-44e2-b9a5-46c979278f3d</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df00826b-75d0-4379-84fc-7d1c724e3e46</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Service</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.274b6603-9ddb-4d5f-9bbe-7da2b5c06b2c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:52</guid>
            <versionId>c402d23f-f6df-456e-b63c-d7ca72b25a27</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.274b6603-9ddb-4d5f-9bbe-7da2b5c06b2c</subProcessId>
                <attachedProcessRef>/1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7</attachedProcessRef>
                <guid>17bfb9d1-5bcf-4d64-95df-ea92951168d3</guid>
                <versionId>ed51d3a7-fbfa-4020-876a-a3e4a53f7ce4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.e6de2c56-d47d-4541-8874-43156e2b1a04</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Set ECM default properties</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.cbbe3532-03e5-4aad-b85b-497102b65431</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a3676f5a2:5a27</guid>
            <versionId>d43cdd93-a7b5-4432-aceb-4524969a51db</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.cbbe3532-03e5-4aad-b85b-497102b65431</subProcessId>
                <attachedProcessRef>/1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</attachedProcessRef>
                <guid>b6da0c97-1d48-43f8-82f7-b3b2be5cca20</guid>
                <versionId>c641e39e-edf4-41b9-bda1-5206aefd7be5</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.28b35608-231d-45e7-823b-f4f3e2c86f67</processItemId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <name>Get account transactions</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.a1fe63fb-004e-43b8-9faa-28ead60c97f7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:6ad7eb4224455a46:11f23e39:189eae0bd83:-2a8b</guid>
            <versionId>e4f67c79-9c94-44dc-b5f8-73473cac4638</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.a1fe63fb-004e-43b8-9faa-28ead60c97f7</subProcessId>
                <attachedProcessRef>/1.e48b8940-fb79-4255-9301-1ce59d8cfa3b</attachedProcessRef>
                <guid>81606b86-5e0d-4cfb-aac9-51e3f46cdf75</guid>
                <versionId>4210b00c-652c-43ae-ad59-a7eaf7a33a3f</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.a0f8f03f-a058-4c38-ac15-c1a6c29f2eb4</epvProcessLinkId>
            <epvId>/21.93c5c002-7ac4-4283-83ee-63b8662f9223</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>bc4e4366-5cca-458c-976a-ceb2e4e1436b</guid>
            <versionId>076aed09-6187-4d2a-b006-66a24486b6c1</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f7ef5046-c836-45bd-972b-2f9573b36ff5</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>fe0152ba-e13e-4461-95f6-3e93197ac805</guid>
            <versionId>094521bd-0b36-440f-a0c8-08f5336dbf07</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.da484534-356a-4ce1-9721-eeabbf088a48</epvProcessLinkId>
            <epvId>/21.769dc134-1d15-4dd4-a967-c5f61cf352dc</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>497472e6-62f1-402f-a87b-8af3871de340</guid>
            <versionId>2f536aa9-530f-44e4-b390-4cd4dff6d58c</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.e9aa77d4-0869-4bbf-b6a8-7a0cc0a3b83c</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>16435a00-1f43-4482-9379-3c5e3c5e77c2</guid>
            <versionId>35478777-3c3c-40b7-8a33-cebbfcf92e84</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.3bb95b9c-8432-46fb-8dda-7151aee534b0</epvProcessLinkId>
            <epvId>/21.062854b5-6513-4da8-84ab-0126f90e550d</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>bec0ccfe-25f1-493d-963c-1e721422e4e8</guid>
            <versionId>591d235c-8c79-4180-b7cc-8fcea919c699</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.a7945168-156a-4d69-8d25-b4c26f1cfbc0</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>d5308d79-23f7-4b10-9c29-98d8ba19e060</guid>
            <versionId>5969818b-f6e9-49d6-a1b0-a415a7320224</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.282f6e05-115b-4c91-ad41-a9562e078f8e</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>58bd6b56-3294-4936-b0f3-107daf259452</guid>
            <versionId>86d7ce7d-ad36-4da4-8375-874ab40a4079</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.d4b569de-e70f-45d0-b049-9df0dc40b508</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>bca5070b-03f9-4778-8ff6-3ea9b643bdb3</guid>
            <versionId>f3469516-99f4-4529-a7c6-d8dc0dbb806c</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.22ebad11-661c-4e6c-83cb-ac996fd85d8a</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <guid>0a484720-1022-43b3-8240-65bf5dcf5571</guid>
            <versionId>f52c30ad-541c-4dca-8f19-3465685db37f</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.136e3662-e6aa-49a8-a4ba-e15111c65f8c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="85f7c5b8-9c66-4f73-9714-a117d7bd6e77" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns16:globalUserTask name="Act01 - Create or Amend ODC Request" id="1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1">
                    <ns16:documentation />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation id="8f44955b-6062-4a44-bbb3-5f15c9404677">
                            <ns16:startEvent name="Start" id="e4c26469-3f1e-49fd-a9c2-f1feeabcfc0a">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="-306" y="105" width="31" height="43" color="#F8F8F8" />
                                    <ns3:default>2027.2a66b38e-078c-4f17-8aa4-0a0eac8bf103</ns3:default>
                                </ns16:extensionElements>
                            </ns16:startEvent>
                            <ns3:formTask name="Create / Amend Request" id="2025.46e58a58-6a17-44bd-807b-f31643801a7a">
                                <ns16:extensionElements>
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns13:nodeVisualInfo x="716" y="81" width="95" height="70" />
                                    <ns3:postAssignmentScript />
                                    <ns3:preAssignmentScript>if(tw.local.odcRequest.requestType.value == "amend" || tw.local.odcRequest.requestType.value == "recreate" )&#xD;
{&#xD;
	tw.local.requestTypeVIS = "Editable";&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.requestTypeVIS = "None";&#xD;
}&#xD;
&#xD;
tw.local.odcRequest.GeneratedDocumentInfo.customerName = tw.local.odcRequest.CustomerInfo.customerName;&#xD;
</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.245a4797-cab9-41b8-8610-6e51dc5b7da1</ns16:incoming>
                                <ns16:incoming>2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17</ns16:incoming>
                                <ns16:incoming>2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d</ns16:incoming>
                                <ns16:incoming>2027.a4212e56-9bb4-41c2-812b-e4dd08de0584</ns16:incoming>
                                <ns16:outgoing>2027.a628a18e-a57f-484d-890e-d714a0873119</ns16:outgoing>
                                <ns16:outgoing>2027.e3d77e41-7957-42f1-8f6f-79ab32bbe009</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>4329d93d-28af-4675-8b80-5ee05652e2ca</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>181464ab-fb2d-416a-81cc-ecc576909a18</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>142337c6-2374-4a83-8447-c556028e0e09</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c890191c-653a-4b5e-8503-6e1133efe5e7</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>366b9b64-0ed4-43c6-88a6-5d2039676b3e</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>48937872-ef9b-4a24-8922-9ffcf4bf8c84</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d26f50e7-e132-4f11-82b0-3da304589c0f</ns19:id>
                                                    <ns19:optionName>buttonName</ns19:optionName>
                                                    <ns19:value>Submit</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6ced93af-e593-48f3-87e2-313d6a562130</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>7dc8b8c7-0732-4d7d-82c2-6450dc15b713</ns19:id>
                                                    <ns19:optionName>approvals</ns19:optionName>
                                                    <ns19:value />
                                                    <ns19:valueType>static</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>4de1b979-652e-4708-8395-ccec614834a4</ns19:id>
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    <ns19:value>"NONE"</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d0cd978a-f34a-4fd7-8364-2441474fe56e</ns19:id>
                                                    <ns19:optionName>complianceApproval</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.complianceApproval</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b1d2a4e8-de09-48c3-81cf-731c0b483469</ns19:id>
                                                    <ns19:optionName>errorPanelVIS</ns19:optionName>
                                                    <ns19:value>tw.local.errorPanelVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6693a28e-f36c-46e2-8dd5-30709b363f8b</ns19:id>
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c5a7d979-7b6a-47cf-8ab0-c268d987ba50</ns19:id>
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    <ns19:value>"NONE"</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>4fbc1a7b-33f6-44ec-8896-4b290ab2d7a3</ns19:id>
                                                    <ns19:optionName>data</ns19:optionName>
                                                    <ns19:value>tw.local.data</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>f5836a08-2c88-46a9-82be-8f179b3305ca</ns19:id>
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>0421a764-efbb-47b0-89a8-4e64c9f4a3ff</ns19:id>
                                                    <ns19:optionName>returnReasonVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>94b3d09f-52c9-4a2d-805a-e1cc00e616e4</ns19:id>
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>de615e4d-3640-48c7-866c-7ddcb180b103</ns19:id>
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>3a1b431b-2243-4153-871a-03fe69e6b559</ns19:id>
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c3e2dbec-c577-4a5b-80ab-6eac468b21a6</ns19:id>
                                                    <ns19:optionName>tradeFoComment</ns19:optionName>
                                                    <ns19:value />
                                                    <ns19:valueType>static</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>f531a602-b360-41cb-8aa7-fc59fb0f5245</ns19:id>
                                                    <ns19:optionName>compcheckerCommentVIS</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>9c799fe1-8ec9-407f-842e-c0edaeb25c88</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>f883482a-d226-4e45-8ef1-1188a8ded128</ns19:id>
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>2f8fccd3-9879-4e3e-8e48-17d3c6e63e01</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Tab section</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>29c98562-024e-47da-845a-3abfd51a7b46</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>462b95ce-b660-4c4a-8bfc-70f72c026223</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>5601885e-a6bf-4bc1-8bec-2a7630731cba</ns19:id>
                                                            <ns19:optionName>colorStyle</ns19:optionName>
                                                            <ns19:value>P</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>d11694b0-3d1e-4acd-84e7-fc714fa2474c</ns19:id>
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>a5dbc7b0-f9ef-410b-892e-9db2f622ea96</ns19:id>
                                                            <ns19:optionName>tabsStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"S"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>4f59508d-9e4b-4845-8f49-4b3cbefe2edf</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>7f199f4d-c7a9-497a-89b4-15228c4256c4</ns19:id>
                                                                <ns19:layoutItemId>0</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>f138f6a9-e27a-413e-86bc-ddeb6d979ad2</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>569dfbbb-7e7c-408a-85bd-ad9a4c1499af</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cffdb4cc-b87d-4e77-8a7d-191bf3c42b63</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cf8fe0fe-833e-4540-81c3-fc3a726e1141</ns19:id>
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    <ns19:value>tw.local.parentRequestNoVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3a6322f6-09c5-497b-805b-7e27e6bc339e</ns19:id>
                                                                    <ns19:optionName>basicDetailsVIS</ns19:optionName>
                                                                    <ns19:value>""</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ec0e40a1-ca1c-40d2-8f62-1ed2f3c656ed</ns19:id>
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    <ns19:value>tw.local.contractStageVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>c23af930-1ce8-4908-88f9-88ac5c34df90</ns19:id>
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    <ns19:value>EDITABLE</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ac6c8e7b-88cd-4e55-85ef-18a2da5b604e</ns19:id>
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    <ns19:value>"NONE"</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>205311e8-ef63-4e75-855b-ffd4ae3fb6c8</ns19:id>
                                                                    <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>bf13348a-57c9-46f5-8b74-cba703b6aade</ns19:id>
                                                                <ns19:layoutItemId>1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>346cb219-0124-426d-8fc0-9cf5e483c354</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Letter Generation</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e9cd7085-34e9-4873-8116-6eb83f5b1115</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8836366c-f207-4a8a-8551-ba9e6a36955a</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>*************-460d-8074-bbc271c821ab</ns19:id>
                                                                    <ns19:optionName>conditionType</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f5d1a577-f9c5-4ea2-8012-9f986d88aaa8</ns19:id>
                                                                    <ns19:optionName>deliveryTerms</ns19:optionName>
                                                                    <ns19:value>tw.local.deliveryterms</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f636975b-b7ff-43ac-8288-d0e6d89434bb</ns19:id>
                                                                    <ns19:optionName>paymentTerms</ns19:optionName>
                                                                    <ns19:value>tw.local.paymentTerms</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d2058fd9-48ac-494b-86c3-4a38d789e833</ns19:id>
                                                                    <ns19:optionName>specialInstructions</ns19:optionName>
                                                                    <ns19:value>tw.local.specialInstructions</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a143445a-3219-4a4b-8fb3-376bc3a7a733</ns19:id>
                                                                    <ns19:optionName>instructions</ns19:optionName>
                                                                    <ns19:value>tw.local.instructions</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5198a1c6-bc6a-4a28-8a70-e98f496dddbe</ns19:id>
                                                                    <ns19:optionName>requestTypeVIS</ns19:optionName>
                                                                    <ns19:value>tw.local.requestTypeVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2bba2dfd-9ed6-4d93-833f-8c8d3c86a0ea</ns19:id>
                                                                    <ns19:optionName>regeneratedRemittanceLetterTitleVIS</ns19:optionName>
                                                                    <ns19:value>tw.local.regeneratedRemittanceLetterTitleVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ca5f40a2-5930-4d7d-89ba-ad5562aeea04</ns19:id>
                                                                    <ns19:optionName>DocumentGenerationVIS</ns19:optionName>
                                                                    <ns19:value>"Editable"</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>247b338c-33ff-4721-85f5-8ec47f25ce72</ns19:id>
                                                                    <ns19:optionName>requestType</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.requestType.value</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>80a2bbbc-dbbd-4899-86fe-42fb927eaade</ns19:id>
                                                                    <ns19:optionName>remittanceLetterButton</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a2615514-9766-45b3-8155-5a28b4b58605</ns19:id>
                                                                    <ns19:optionName>documentGenerationVIS</ns19:optionName>
                                                                    <ns19:value>Editable</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.1d99aba8-195f-4eee-ba8c-a47926bc21e2</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.GeneratedDocumentInfo</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>a1bb5a88-33a1-4a0c-8264-48202758152d</ns19:id>
                                                                <ns19:layoutItemId>2</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>4b7335dc-565a-4874-87fb-d6b5faf147d6</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Customer Info</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>12f7655f-8be9-42c7-82d8-fed7984dc3c2</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1c4ab8ed-1743-4726-8da8-92ad4490ee1c</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>b05651b8-8166-49a6-8300-b6a4121d49e0</ns19:id>
                                                                    <ns19:optionName>tableName</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a423d4a2-8e36-4218-8a9e-************</ns19:id>
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    <ns19:value>"READONLY"</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8ef808e8-bd99-40a2-856f-034cce91cbcc</ns19:id>
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>7021f345-19f1-4c0c-8103-01315252d575</ns19:id>
                                                                <ns19:layoutItemId>3</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>53c207e1-4e34-4e9f-826f-313f99a55eab</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Financial Details - Branch</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ffcc6dc8-d25a-4f68-8c9e-7cc29724f213</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>69ba7164-e143-463f-87cb-765e8ed61190</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2ac167ab-139c-4e82-8982-1be1519e5b17</ns19:id>
                                                                    <ns19:optionName>financialDetailsVis</ns19:optionName>
                                                                    <ns19:value>"DEFAULT"</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>48b8a314-f7cd-4fdc-8b97-d249cc0a4340</ns19:id>
                                                                    <ns19:optionName>fcCollectionVis</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>001351ad-d960-4e19-874b-471ca812464c</ns19:id>
                                                                    <ns19:optionName>today</ns19:optionName>
                                                                    <ns19:value>tw.local.today</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>fa17c193-55ca-4708-81de-e7bc461e5cc4</ns19:id>
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    <ns19:value>tw.local.currencyListVIS</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>738443dc-44d3-417c-8323-2008b8a95b9e</ns19:id>
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    <ns19:value>EDITABLE</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>223910ad-acc4-4fa9-8355-84dbb81e27ef</ns19:id>
                                                                <ns19:layoutItemId>4</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>783cb246-2d9d-4ddb-87cc-33e490a2fc35</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>FC Collections</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4e21faf2-ce42-4a21-864e-80ccdda2e9eb</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8ed0576a-89e4-4fb5-82b5-206b1af02b43</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7a11c958-c6f9-4723-84b1-4d9e8873a852</ns19:id>
                                                                    <ns19:optionName>customerCif</ns19:optionName>
                                                                    <ns19:value>tw.local.cifNo</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>65b80165-a269-4645-8e75-95d3e98164cb</ns19:id>
                                                                    <ns19:optionName>requestCurrency</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.FinancialDetailsBR.currency.value</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9ba6bb97-fa1d-4e04-8f56-561deb3288fc</ns19:id>
                                                                    <ns19:optionName>collectionCurrencyVIS</ns19:optionName>
                                                                    <ns19:value>tw.local.collectionCurrencyVis</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>31eb5f93-225d-40ac-8868-e8570a290020</ns19:id>
                                                                    <ns19:optionName>activityType</ns19:optionName>
                                                                    <ns19:value>write</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d8723785-5dcd-44a3-85bf-7ec05c7e09c8</ns19:id>
                                                                    <ns19:optionName>FCVIS</ns19:optionName>
                                                                    <ns19:value>EDITABLE</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4a63ef53-90d0-4ac8-8b35-2943edd8700c</ns19:id>
                                                                    <ns19:optionName>retrieveBtnVis</ns19:optionName>
                                                                    <ns19:value>EDITABLE</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>217adb6f-29be-47ca-81af-b6f24094b431</ns19:id>
                                                                    <ns19:optionName>negotiatedExchangeRateVIS</ns19:optionName>
                                                                    <ns19:value>EDITABLE</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>0ac01877-2952-4ac6-8ba1-9f10792459f7</ns19:id>
                                                                    <ns19:optionName>addBtnVIS</ns19:optionName>
                                                                    <ns19:value>EDITABLE</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.FcCollections</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>f5bd2e4d-**************-3776041b1aca</ns19:id>
                                                                <ns19:layoutItemId>5</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>96edc205-324f-4941-8ed6-d3141ae5980c</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Attachment</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>deb225d5-aae7-42dd-8f40-cc96b236244f</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>97a68835-5a68-4cff-8473-7353d6e553d1</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>f9a9504a-7a38-472b-87cb-53a821448c19</ns19:id>
                                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ba0afa83-c17b-4711-809a-d517506418c3</ns19:id>
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>805e3641-ff9d-465f-8495-6c162f6d5548</ns19:id>
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>ca254d23-7913-4f38-8588-ebad134dc21f</ns19:id>
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>daeb4758-8372-4c95-8ae0-19183e3b71b5</ns19:id>
                                                                    <ns19:optionName>visible</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e93a070b-6e7b-4bd6-87fa-00a1425057f7</ns19:id>
                                                                    <ns19:optionName>remittanceLetterPath</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.folderPath</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>26835533-4b1b-4359-83d8-************</ns19:id>
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>79075267-9dc5-419b-87e4-da66913467b6</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>History</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>a61917c1-a7fa-4b21-8a1c-b6f74de9e120</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>97492741-0a9b-43a0-8bfd-950ca85d4d72</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>634af355-b29a-47bf-8a77-9c97fb0d2fa7</ns19:id>
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    <ns19:value />
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>37c0fa32-54f8-489f-8eac-d81ba3badb33</ns19:id>
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    <ns19:value>{"@class":"com.ibm.bpm.coachNG.visibility.VisibilityRules","rules":[{"@class":"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule","var_conditions":[{"timeInMs":null,"var":"tw.local.isFirstTime","operand":true,"operator":0}],"action":"NONE"}],"defaultAction":"DEFAULT"}</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:endEvent name="End" id="dd400abd-e346-4579-a741-c91be71e13ba">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1961" y="114" width="24" height="43" color="#F8F8F8" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.bb576e8e-dc9f-4992-8532-24b61b983a46</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.8c295691-a3bf-4224-856e-edd0e9ed866b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="782" y="231" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:default>2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17</ns3:default>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.a628a18e-a57f-484d-890e-d714a0873119</ns16:incoming>
                                <ns16:outgoing>2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.46e58a58-6a17-44bd-807b-f31643801a7a" targetRef="2025.8c295691-a3bf-4224-856e-edd0e9ed866b" name="To Stay on page" id="2027.a628a18e-a57f-484d-890e-d714a0873119">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomRight</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="ffa16bac-cf6c-4f20-8117-ea23e246de18">
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.46e58a58-6a17-44bd-807b-f31643801a7a" targetRef="2025.e33b4f1b-810b-4ca7-8114-f5c67f3512fe" name="To End" id="2027.e3d77e41-7957-42f1-8f6f-79ab32bbe009">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightBottom</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="5719c255-a02c-4b8b-814e-900c3221eac6">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.9daf5d03-785b-48a9-820d-f68f1863a784" name="Validation" id="2025.e33b4f1b-810b-4ca7-8114-f5c67f3512fe">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="910" y="157" width="95" height="70" color="#95D087" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.e3d77e41-7957-42f1-8f6f-79ab32bbe009</ns16:incoming>
                                <ns16:outgoing>2027.9daf5d03-785b-48a9-820d-f68f1863a784</ns16:outgoing>
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false; &#xD;
 var choice = false;&#xD;
 var tempLength = 0 ;&#xD;
 tw.local.invalidTabs = [];&#xD;
tw.system.coachValidation.clearValidationErrors();&#xD;
&#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//debugger;&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)&#xD;
	mandatory(tw.local.odcRequest.stepLog.comment, "tw.local.odcRequest.stepLog.comment");&#xD;
&#xD;
if(true){&#xD;
	&#xD;
//-------------------------------------------------BASIC DETAILS VALIDATION -----------------------------------------&#xD;
mandatory(tw.local.odcRequest.BasicDetails.exportPurpose.name , "tw.local.odcRequest.BasicDetails.exportPurpose");&#xD;
mandatory(tw.local.odcRequest.BasicDetails.paymentTerms.name , "tw.local.odcRequest.BasicDetails.paymentTerms");&#xD;
mandatory(tw.local.odcRequest.BasicDetails.productCategory.name , "tw.local.odcRequest.BasicDetails.productCategory");&#xD;
mandatory(tw.local.odcRequest.BasicDetails.commodityDescription , "tw.local.odcRequest.BasicDetails.commodityDescription");&#xD;
if(tw.local.odcRequest.BasicDetails.Invoice.length==0){&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Invoice , "tw.local.odcRequest.BasicDetails.Invoice");&#xD;
////	addError(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo , "There should be at least one or more invoices" , tw.resource.ValidationMessages.MandatoryFields, true);&#xD;
	tw.local.errorMessage += "&lt;li&gt;"+"Fill in at least one entry in invoices"+"&lt;/li&gt;";	&#xD;
	}&#xD;
	else{&#xD;
	for(var i=0;i&lt;tw.local.odcRequest.BasicDetails.Invoice.length ; i++){&#xD;
		mandatory(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate , "tw.local.odcRequest.BasicDetails.Invoice["+i+"].invoiceDate");&#xD;
		mandatory(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo , "tw.local.odcRequest.BasicDetails.Invoice["+i+"].invoiceNo");&#xD;
		maxLength(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo , "tw.local.odcRequest.BasicDetails.Invoice["+i+"].invoiceNo", 20 , "Shouldn't be more than 20 character" , "invoice Number: " + "Shouldn't be more than 20 character" );&#xD;
		}&#xD;
	} &#xD;
if(tw.local.odcRequest.BasicDetails.Bills.length==0 ){&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef , "tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef");&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate , "tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate");&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Bills , "tw.local.odcRequest.BasicDetails.Bills");&#xD;
	tw.local.errorMessage += "&lt;li&gt;"+"Fill in at least one entry in Bills"+"&lt;/li&gt;";&#xD;
	}&#xD;
	else{&#xD;
	for(var i=0;i&lt;tw.local.odcRequest.BasicDetails.Bills.length ; i++){	&#xD;
		mandatory(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate , "tw.local.odcRequest.BasicDetails.Bills["+i+"].billOfLadingDate");&#xD;
		mandatory(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef , "tw.local.odcRequest.BasicDetails.Bills["+i+"].billOfLadingRef"); &#xD;
		maxLength(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef , "tw.local.odcRequest.BasicDetails.Bills["+i+"].billOfLadingRef" ,  20 , "Shouldn't be more than 20 character" , "Bill Of lading Ref : " + "Shouldn't be more than 20 character" );&#xD;
		}&#xD;
	}	&#xD;
//if(tw.local.odcRequest.BasicDetails.Invoice.length &gt; 0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate == "" || tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate == null))&#xD;
//if(tw.local.odcRequest.BasicDetails.Invoice.length &gt; 0){&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate , "tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate");&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo , "tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo");&#xD;
//	}&#xD;
////if(tw.local.odcRequest.BasicDetails.Invoice.length &gt; 0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo == "" || tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo == null))&#xD;
//	{&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo , "tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo");&#xD;
//	}&#xD;
	&#xD;
//if(tw.local.odcRequest.BasicDetails.Bills.length &gt;0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate == "" || tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate == null))&#xD;
//if(tw.local.odcRequest.BasicDetails.Bills.length &gt; 0){&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate , "tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate");&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef , "tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef");&#xD;
//	}	&#xD;
//if(tw.local.odcRequest.BasicDetails.Bills.length &gt;0 &amp;&amp; (tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef == "" || tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef == null))&#xD;
//	{&#xD;
//	mandatory(tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef , "tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef");&#xD;
//	}&#xD;
&#xD;
mandatory(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name , "tw.local.odcRequest.BasicDetails.CountryOfOrigin");&#xD;
maxLength(tw.local.odcRequest.BasicDetails.flexCubeContractNo , "tw.local.odcRequest.BasicDetails.flexCubeContractNo", 16 ,  tw.resource.ValidationMessages.MaxLength16 , "Flex Cube Contract Number: " +tw.resource.ValidationMessages.MaxLength16);&#xD;
maxLength(tw.local.odcRequest.BasicDetails.commodityDescription , "tw.local.odcRequest.BasicDetails.commodityDescription", 160 , "Shouldn't be more than 160 character" , "Commodity Description: " + "Shouldn't be more than 160 character" );&#xD;
&#xD;
//for (var i=0 ;i&lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++){&#xD;
//	maxLength(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo , "tw.local.odcRequest.BasicDetails.Invoice["+i+"].invoiceNo", 20 , "Shouldn't be more than 20 character" , "invoice Number: " + "Shouldn't be more than 20 character" );&#xD;
//	}&#xD;
//	&#xD;
//for(var i=0; i&lt;tw.local.odcRequest.BasicDetails.Bills.length ;i++)&#xD;
//	maxLength(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef , "tw.local.odcRequest.BasicDetails.Bills["+i+"i].billOfLadingRef" ,  20 , "Shouldn't be more than 20 character" , "Bill Of lading Ref : " + "Shouldn't be more than 20 character" );&#xD;
&#xD;
validateTab(0,"Basic Details");&#xD;
//-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------	&#xD;
mandatory(tw.local.odcRequest.FinancialDetailsBR.documentAmount , "tw.local.odcRequest.FinancialDetailsBR.documentAmount");&#xD;
mandatory(tw.local.odcRequest.FinancialDetailsBR.currency.name , "tw.local.odcRequest.FinancialDetailsBR.currency");&#xD;
mandatory(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate , "tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate");&#xD;
//mandatory(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced , "tw.local.odcRequest.FinancialDetailsBR.amountAdvanced");&#xD;
if(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0.0)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value , "tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount");&#xD;
	mandatory(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value , "tw.local.odcRequest.FinancialDetailsBR.collectionAccount");&#xD;
}&#xD;
minLength(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced,"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced", 2 ,"Shouldn't be less than 14 character","Amount Advanced: Shouldn't be less than 2 character");&#xD;
maxLength(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced , "tw.local.odcRequest.FinancialDetailsBR.amountAdvanced", 14 , "Shouldn't be more than 14 character" , "Amount Advanced:" + "Shouldn't be more than 14 character" );&#xD;
validateTab(3,"Financial Details - Branch");&#xD;
//-----------------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------------	&#xD;
//This change according to mosallam meeting 12-11-2023&#xD;
//mandatory(tw.local.odcRequest.FcCollections.currency.name , "tw.local.odcRequest.FcCollections.currency");&#xD;
//mandatory(tw.local.odcRequest.FcCollections.negotiatedExchangeRate , "tw.local.odcRequest.FcCollections.negotiatedExchangeRate");&#xD;
//mandatory(tw.local.odcRequest.FcCollections.fromDate , "tw.local.odcRequest.FcCollections.fromDate");&#xD;
//mandatory(tw.local.odcRequest.FcCollections.ToDate , "tw.local.odcRequest.FcCollections.ToDate");&#xD;
//mandatory(tw.local.odcRequest.FcCollections.accountNo.value , "tw.local.odcRequest.FcCollections.accountNo");&#xD;
//sumDates(tw.local.odcRequest.FcCollections.fromDate, tw.local.odcRequest.FcCollections.ToDate , "tw.local.odcRequest.FcCollections.ToDate" ,"Search Transactions To Date must be bigger than Search Transactions From Date" , "Search Transactions To Date must be bigger than Search Transactions From Date" );&#xD;
if(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0.0)&#xD;
{&#xD;
	if(tw.local.odcRequest.FcCollections!= null &amp;&amp; (tw.local.odcRequest.FcCollections.totalAllocatedAmount == 0 || tw.local.odcRequest.FcCollections.totalAllocatedAmount != tw.local.odcRequest.FinancialDetailsBR.amountAdvanced))&#xD;
	{&#xD;
		choice = confirm("Total allocated amount should be equal to the advance amount");&#xD;
	}&#xD;
&#xD;
if(tw.local.odcRequest.FcCollections != null &amp;&amp; tw.local.odcRequest.FcCollections.negotiatedExchangeRate != null)&#xD;
{&#xD;
	minLength(tw.local.odcRequest.FcCollections.negotiatedExchangeRate,"tw.local.odcRequest.FcCollections.negotiatedExchangeRate", 6 ,"Shouldn't be less than 6 character"," Negotiated Exchange Rate: Shouldn't be less than 6 character");&#xD;
	maxLength(tw.local.odcRequest.FcCollections.negotiatedExchangeRate , "tw.local.odcRequest.FcCollections.negotiatedExchangeRate", 10 , "Shouldn't be more than 10 character" , " Negotiated Exchange Rate:" + "Shouldn't be more than 10 character" );&#xD;
}&#xD;
}&#xD;
if(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced == 0.0 &amp;&amp; tw.local.odcRequest.FcCollections.selectedTransactions.length &gt;0)&#xD;
{&#xD;
	tw.local.errorMessage += "&lt;li&gt;"+"No Transactions should be retrieved advanced amount equals zero"+"&lt;/li&gt;";	&#xD;
	tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.retrievedTransactions", "Mandatory Field can't left blank");&#xD;
	&#xD;
}&#xD;
if(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0.0)&#xD;
{&#xD;
	&#xD;
	for(var i=0; i&lt;tw.local.odcRequest.FcCollections.selectedTransactions.length;i++)&#xD;
	{&#xD;
	 var totalTransAmount = tw.local.odcRequest.FcCollections.selectedTransactions[i].existAmount + tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest;&#xD;
	&#xD;
	 if(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount &lt; totalTransAmount)&#xD;
	 {&#xD;
	 		tw.local.errorMessage += "&lt;li&gt;"+"Amount Allocated for currenct request and amount linked in bpm shoudn't be greater than transaction amount"+"&lt;/li&gt;";	&#xD;
	 		tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.selectedTransactions["+i+"].amountAllocatedForCurrencyRequest", "Amount Allocated for currenct request and amount linked in bpm shoudn't be greater than transaction amount");&#xD;
	 		&#xD;
	 }&#xD;
	 &#xD;
	}&#xD;
&#xD;
if(tw.local.odcRequest.FcCollections.fromDate &gt;= tw.local.odcRequest.FcCollections.ToDate)&#xD;
{&#xD;
	tw.local.errorMessage += "&lt;li&gt;"+" Search Transactions From Date shouln't be greater than Search Transactions To Date"+"&lt;/li&gt;";	&#xD;
	 tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.fromDate", "Search Transactions From Date shouln't be greater than Search Transactions To Date");&#xD;
	 tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.ToDate", "Search Transactions From Date shouln't be greater than Search Transactions To Date");&#xD;
}&#xD;
if(tw.local.odcRequest.FcCollections.selectedTransactions.length !=0)&#xD;
{&#xD;
 for (var i = 0; i &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; i++) &#xD;
  {&#xD;
      for (var j = 0; j &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; j++) &#xD;
      {&#xD;
          if (i != j) &#xD;
          {&#xD;
              if (tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber == tw.local.odcRequest.FcCollections.selectedTransactions[j].referenceNumber) &#xD;
              {&#xD;
//                 alert("This Tranaction Reference Number have been added before");&#xD;
                 tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.selectedTransactions["+i+"].referenceNumber", "This Tranaction Reference Number have been added before");&#xD;
                  &#xD;
              }&#xD;
            &#xD;
              &#xD;
          }&#xD;
            &#xD;
      }&#xD;
  }&#xD;
			    &#xD;
&#xD;
}&#xD;
}&#xD;
validateTab(4,"Flexcube Collecions");&#xD;
//minLength(tw.local.odcRequest.FcCollections.accountNo.value,"tw.local.odcRequest.FcCollections.accountNo", 19 ,"Shouldn't be less than 19 character","Account Number: Shouldn't be less than 19 character");&#xD;
}//end of action coond&#xD;
&#xD;
console.log(tw.system.coachValidation.validationErrors);&#xD;
&#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- Validation Functions ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
/*&#xD;
* =========================================================================================================&#xD;
*  &#xD;
* Add a coach validation error &#xD;
* 		&#xD;
* EX:	addError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');&#xD;
*&#xD;
* =========================================================================================================&#xD;
*/&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
/*&#xD;
* =================================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is less than given length&#xD;
*	&#xD;
* EX:	minLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =================================================================================================================================&#xD;
*/&#xD;
&#xD;
function minLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
/*&#xD;
* =======================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the string length is greater than given length&#xD;
*	&#xD;
* EX:	maxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =======================================================================================================================&#xD;
*/&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
/*&#xD;
* ==================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the field is null 'Mandatory'&#xD;
*	&#xD;
* EX:	notNull(tw.local.name , 'tw.local.name')&#xD;
*&#xD;
* ==================================================================================================================&#xD;
*/&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				if (field &lt; 0)&#xD;
				{&#xD;
					var msg= "Invalid Value, This field can not be negative value.";&#xD;
					addError(fieldName , msg , msg , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
/*&#xD;
* ==============================================================================================================================&#xD;
*&#xD;
* Add a coach validation error if the fromDate bigger than toDate&#xD;
*	&#xD;
* EX:	sumDates(fomDate , toDate , 'tw.local.date' , 'this is a control validation message!' , 'this is a validation message!')&#xD;
*&#xD;
* =============================================================================================================================&#xD;
*/&#xD;
&#xD;
function sumDates(fromDate , toDate , fieldName , controlMessage , validationMessage)&#xD;
{&#xD;
	if(toDate - fromDate == 0)&#xD;
	{&#xD;
	 	return true;&#xD;
	}&#xD;
	addError(fieldName , controlMessage , validationMessage);&#xD;
	return false;&#xD;
}&#xD;
&#xD;
//=================================================================================&#xD;
function checkNegativeValue(field , fieldName)&#xD;
{&#xD;
   if (field &lt; 0){&#xD;
   var msg= "Invalid Value, This field can not be negative value.";&#xD;
   addError(fieldName , msg , msg , true);&#xD;
   mandatoryTriggered = true;&#xD;
   return false;&#xD;
   }&#xD;
   return true;&#xD;
}&#xD;
//=================================================================================&#xD;
function validateTab(index , tabName)&#xD;
{&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength)&#xD;
	{&#xD;
		if (tw.local.errorMessage.length == 0) {&#xD;
			tw.local.errorMessage += "&lt;p&gt;" + "Please complete fields in the following tabs:" + "&lt;/p&gt;";&#xD;
		}&#xD;
		tw.local.errorMessage += "&lt;li&gt;" + tabName + "&lt;/li&gt;";&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length ;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}	&#xD;
}&#xD;
&#xD;
	&#xD;
//=================================================================================	&#xD;
	tw.local.errorMessage!=null ?  tw.local.errorPanelVIS ="EDITABLE": tw.local.errorPanelVIS ="NONE";&#xD;
	</ns16:script>
                            </ns16:scriptTask>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.7dc1b5f9-1298-45b1-8518-63942881213e" />
                            <ns16:sequenceFlow sourceRef="2025.e33b4f1b-810b-4ca7-8114-f5c67f3512fe" targetRef="2025.1db417ca-6523-481d-81bf-55b229eb7dda" name="To End" id="2027.9daf5d03-785b-48a9-820d-f68f1863a784">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorPanelVIS" id="2056.8290a647-ed4e-4117-847f-46d7262f7b9e" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66" name="update BO value from first coach" id="2025.1b2e8257-1e18-497e-8d78-851099ae9415">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="-103" y="81" width="95" height="70" />
                                    <ns3:preAssignmentScript>tw.local.error = {};</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.fcce1260-f60c-46c8-8768-f0db6fef23fd</ns16:incoming>
                                <ns16:outgoing>2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66</ns16:outgoing>
                                <ns16:script>//////////////initializing basic details CV&#xD;
if(!tw.local.odcRequest.BasicDetails.requestNature)&#xD;
 tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;&#xD;
tw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name;&#xD;
tw.local.odcRequest.BasicDetails.parentRequestNo = tw.local.odcRequest.parentRequestNo;&#xD;
tw.local.odcRequest.BasicDetails.contractStage = "Final";&#xD;
&#xD;
&#xD;
&#xD;
////// initializing Customer CV&#xD;
if(!tw.local.odcRequest.CustomerInfo) &#xD;
      tw.local.odcRequest.CustomerInfo = {};&#xD;
if (!tw.local.odcRequest.CustomerInfo.cif)&#xD;
	tw.local.odcRequest.CustomerInfo.cif = tw.local.odcRequest.cif;&#xD;
if (!tw.local.odcRequest.CustomerInfo.customerName)&#xD;
	tw.local.odcRequest.CustomerInfo.customerName = tw.local.odcRequest.customerName;&#xD;
if (tw.local.odcRequest.GeneratedDocumentInfo == null) {&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo = {};&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption = {};&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.name = "001";&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetterOption.value = "Second Mail";&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.regenerateRemLetter = true;&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.customerName = tw.local.odcRequest.CustomerInfo.customerName;&#xD;
&#xD;
&#xD;
}&#xD;
if (tw.local.odcRequest.CustomerInfo.addressLine1 == null) {&#xD;
	tw.local.odcRequest.CustomerInfo.addressLine1 = "";&#xD;
	tw.local.odcRequest.CustomerInfo.addressLine2 = "";&#xD;
	tw.local.odcRequest.CustomerInfo.addressLine3 = "";&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + " " + tw.local.odcRequest.CustomerInfo.addressLine2 + " " + tw.local.odcRequest.CustomerInfo.addressLine3;&#xD;
}&#xD;
else if (tw.local.odcRequest.CustomerInfo.addressLine2 == null) {&#xD;
	tw.local.odcRequest.CustomerInfo.addressLine2 = "";&#xD;
	tw.local.odcRequest.CustomerInfo.addressLine3 = "";&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + " " + tw.local.odcRequest.CustomerInfo.addressLine2 + " " + tw.local.odcRequest.CustomerInfo.addressLine3;&#xD;
}&#xD;
else if (tw.local.odcRequest.CustomerInfo.addressLine3 == null) {&#xD;
	tw.local.odcRequest.CustomerInfo.addressLine3 = "";&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + " " + tw.local.odcRequest.CustomerInfo.addressLine2 + " " + tw.local.odcRequest.CustomerInfo.addressLine3;&#xD;
}&#xD;
else {&#xD;
	tw.local.odcRequest.GeneratedDocumentInfo.customerAddress = tw.local.odcRequest.CustomerInfo.addressLine1 + tw.local.odcRequest.CustomerInfo.addressLine2 + tw.local.odcRequest.CustomerInfo.addressLine3;&#xD;
}&#xD;
if (tw.local.odcRequest.CustomerInfo.customerType != "" &amp;&amp; tw.local.odcRequest.CustomerInfo.customerType == "C") {&#xD;
	tw.local.odcRequest.CustomerInfo.customerType = "Corporate";&#xD;
}&#xD;
else {&#xD;
	tw.local.odcRequest.CustomerInfo.customerType = "Individual";&#xD;
}&#xD;
//tw.local.odcRequest.BasicDetails.Bills[0] = {};&#xD;
//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new Date();&#xD;
//tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = "";&#xD;
&#xD;
//tw.local.odcRequest.BasicDetails.Invoice[0] = {};&#xD;
//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new Date();&#xD;
//tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = "";&#xD;
tw.local.paymentTerms = tw.epv.TermsAndConditions.paymentTerms;&#xD;
tw.local.deliveryterms = tw.epv.TermsAndConditions.deliveryTerms;&#xD;
tw.local.specialInstructions = tw.epv.TermsAndConditions.specialInstructions;&#xD;
tw.local.instructions = tw.epv.TermsAndConditions.instructions;&#xD;
&#xD;
tw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Draft;&#xD;
&#xD;
tw.local.odcRequest.stepLog = {};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT01;&#xD;
&#xD;
tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.complianceApproval = false;&#xD;
tw.local.actionConditions.screenName = tw.epv.ScreenNames.CACT01;&#xD;
&#xD;
tw.local.actionConditions.userRole = tw.local.initiator;&#xD;
tw.local.odcRequest.initiator = tw.local.initiator;//Branch or hub&#xD;
tw.local.actionConditions.lastStepAction = "";&#xD;
tw.local.actionConditions.requestType = tw.local.odcRequest.requestType.value;&#xD;
&#xD;
var date = new Date();&#xD;
tw.local.odcRequest.appInfo.requestDate = date.getDate() + '/' + (date.getMonth() + 1) + '/' + date.getFullYear();&#xD;
tw.local.odcRequest.appInfo.branch = {};&#xD;
tw.local.odcRequest.appInfo.branch.name = tw.local.branchName;&#xD;
tw.local.odcRequest.appInfo.branch.value = tw.local.branchCode;&#xD;
tw.local.odcRequest.appInfo.initiator = tw.system.user.fullName + "( " + tw.system.user.name + ")";//initiator user name&#xD;
tw.local.odcRequest.appInfo.requestName = tw.epv.ProcessDetails.processName;&#xD;
tw.local.odcRequest.appInfo.requestType = tw.epv.ProcessDetails.suffixName;&#xD;
tw.local.odcRequest.appInfo.status = "Initiated";&#xD;
tw.local.odcRequest.appInfo.subStatus = "Initiated";&#xD;
tw.local.odcRequest.appInfo.appRef = "";&#xD;
tw.local.odcRequest.appInfo.appID = "";&#xD;
//tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;&#xD;
tw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.CACT01;&#xD;
if (tw.local.branchCode != "" &amp;&amp; tw.local.branchCode != null) {&#xD;
	tw.local.code = tw.local.branchCode;&#xD;
	tw.local.role = tw.epv.userRole.branch;&#xD;
}&#xD;
else {&#xD;
	tw.local.code = tw.local.hubCode;&#xD;
	tw.local.role = tw.epv.userRole.hub;&#xD;
	tw.local.odcRequest.appInfo.branch.name = "HUB " + tw.local.hubCode;&#xD;
	tw.local.odcRequest.appInfo.branch.value = "HUB " + tw.local.hubCode;&#xD;
}&#xD;
&#xD;
tw.local.errorPanelVIS = "NONE";&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:exclusiveGateway default="2027.36d368c9-8fe5-4613-857d-6b78975ca83b" name="Valid?" id="2025.1db417ca-6523-481d-81bf-55b229eb7dda">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1047" y="258" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.9daf5d03-785b-48a9-820d-f68f1863a784</ns16:incoming>
                                <ns16:outgoing>2027.fab42a6b-ed96-48f1-80ea-4d8fc49b39f3</ns16:outgoing>
                                <ns16:outgoing>2027.36d368c9-8fe5-4613-857d-6b78975ca83b</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.1db417ca-6523-481d-81bf-55b229eb7dda" targetRef="2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91" name="yes" id="2027.fab42a6b-ed96-48f1-80ea-4d8fc49b39f3">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 2" id="2025.bad324e2-7784-4a4e-8230-d7de0a2dc2bf">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1058" y="346" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.36d368c9-8fe5-4613-857d-6b78975ca83b</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.1db417ca-6523-481d-81bf-55b229eb7dda" targetRef="2025.bad324e2-7784-4a4e-8230-d7de0a2dc2bf" name="No" id="2027.36d368c9-8fe5-4613-857d-6b78975ca83b">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" name="tableName" id="2056.58e85f42-1971-4d20-81ca-0860aeb5ab8c" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.935f2a35-5377-476b-8d6c-2e3a779389f3" />
                            <ns16:callActivity calledElement="1.46b984a3-b4ad-405a-abd3-8631f907efe4" default="2027.245a4797-cab9-41b8-8610-6e51dc5b7da1" name="Create ECM Folder" id="2025.7e5a7e9c-39e6-4ab6-84a7-c7c6e834b6ea">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="585" y="81" width="95" height="70" />
                                    <ns3:postAssignmentScript>tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath = tw.local.odcRequest.folderPath;&#xD;
&#xD;
tw.local.odcRequest.attachmentDetails.folderID = tw.local.odcRequest.folderID;&#xD;
 </ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4</ns16:incoming>
                                <ns16:outgoing>2027.245a4797-cab9-41b8-8610-6e51dc5b7da1</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4156964b-1c67-40bc-8f62-3804c71cf908</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.e2ce0eed-342c-4942-8214-83e964b550e5</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.code</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.folderID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.5f955245-0538-4e40-80a6-12f45c3102f3</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.214c7268-80d0-444d-8702-dd0d5462dbe7</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.676e3a06-e2cc-4855-84d6-6f82a350500a</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.folderPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.3440f870-ff23-4c8b-807b-82f6195cb0ac" />
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.4f575929-abd6-495f-8279-27ad84b9e83a" />
                            <ns16:sequenceFlow sourceRef="2025.1b2e8257-1e18-497e-8d78-851099ae9415" targetRef="2025.819f5c1b-594a-44a2-8a30-d73903741b3b" name="To is update?" id="2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.7e5a7e9c-39e6-4ab6-84a7-c7c6e834b6ea" targetRef="2025.46e58a58-6a17-44bd-807b-f31643801a7a" name="To Create / Amend Request" id="2027.245a4797-cab9-41b8-8610-6e51dc5b7da1">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="cifNo" id="2056.636f1fc7-df9d-4c8a-8ac1-a08cf8fd89bb" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="code" id="2056.d13f9520-dab1-4199-89c0-4fc470136286" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="paymentTerms" id="2056.c0fc0852-5a22-4c78-8338-2f33e3d59575" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="deliveryterms" id="2056.b0f66c87-0d0d-4fac-8b46-e60d9f9f3a53" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instructions" id="2056.2b50d167-9dee-4c17-891b-999f5a634f7d" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="specialInstructions" id="2056.dd12b9c9-3ac9-49c7-891c-57fde4c88f8e" />
                            <ns16:callActivity calledElement="1.e48b8940-fb79-4255-9301-1ce59d8cfa3b" default="2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d" name="Get account transactions" id="2025.28b35608-231d-45e7-823b-f4f3e2c86f67">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1072" y="-2" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4e805889-60ae-4790-8410-c2c59cca283f</ns16:incoming>
                                <ns16:outgoing>2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4b2027cd-0272-4e63-8d83-8e29bae003ca</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be">tw.local.odcRequest.FcCollections.fromDate</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.2566eac7-c31f-48ea-8a24-9b38b1022610</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be">tw.local.odcRequest.FcCollections.ToDate</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.c480ed60-61cd-4824-8a4f-181e6b5ad541</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.FcCollections.accountNo.value</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.676facb9-f340-4be1-8aec-d2bdee4233af</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.FcCollections.accountNo.name</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.863e4f64-1adc-44a3-8b7c-f14f92f00d66</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134">tw.local.odcRequest.FcCollections.retrievedTransactions</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.1fb10221-c28b-436a-846e-60bdaa692291</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.7b4d33ce-7300-4150-8abf-dee5da66eba7" name="visibility conditions" id="2025.125eab48-4fda-4887-841c-c11355ab579c">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="201" y="81" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.9054de94-cc5b-4018-8c9d-4a9ac615462c</ns16:incoming>
                                <ns16:incoming>2027.6a90d411-7682-4e7a-8d71-30028735cfd9</ns16:incoming>
                                <ns16:incoming>2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e</ns16:incoming>
                                <ns16:outgoing>2027.7b4d33ce-7300-4150-8abf-dee5da66eba7</ns16:outgoing>
                                <ns16:script>/*Basic Details CV Visibility*/&#xD;
///////////////////////////////&#xD;
if(tw.local.odcRequest.requestNature.value == "update"){&#xD;
	tw.local.parentRequestNoVIS = "READONLY";&#xD;
	tw.local.currencyListVIS = "READONLY";&#xD;
}	&#xD;
else{&#xD;
	tw.local.parentRequestNoVIS = "None";&#xD;
	tw.local.currencyListVIS = "Editable";&#xD;
}	&#xD;
if(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){&#xD;
	tw.local.contractStageVIS = "READONLY";&#xD;
}&#xD;
else{&#xD;
tw.local.contractStageVIS = "NONE";&#xD;
}&#xD;
/*Document Generation Section*/&#xD;
///////////////////////////////&#xD;
if(tw.local.odcRequest.requestType.value == tw.epv.RequestType.Amendment || tw.local.odcRequest.requestType.value == tw.epv.RequestType.Recreate){&#xD;
	tw.local.requestTypeVIS = "EDITABLE";&#xD;
}&#xD;
else{&#xD;
	tw.local.requestTypeVIS = "NONE";	&#xD;
}&#xD;
tw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name  ;&#xD;
tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name  ;</ns16:script>
                            </ns16:scriptTask>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentRequestNoVIS" id="2056.c4c3b157-8024-436e-8ab5-59fcdbd737c2">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="retrieveBtnVIS" id="2056.6c865b7b-c862-4323-8c5c-97510fd0ef97" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="createBtnVis" id="2056.d662f048-a8f8-4cc5-8df0-04861cd25913" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="flexCubeContractNoVIS" id="2056.ec0d54ed-2198-461b-87e3-6cf4fe46fad0" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="contractStageVIS" id="2056.2d2655f2-55d8-4bd4-836c-8f176c739995" />
                            <ns16:sequenceFlow sourceRef="2025.125eab48-4fda-4887-841c-c11355ab579c" targetRef="2025.0aa24670-c849-405c-87f0-2ceaba6a718b" name="To Set Attachments list" id="2027.7b4d33ce-7300-4150-8abf-dee5da66eba7">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestTypeVIS" id="2056.01606556-d295-4b99-88bf-d896554ab43e" />
                            <ns16:sequenceFlow sourceRef="2025.8c295691-a3bf-4224-856e-edd0e9ed866b" targetRef="2025.46e58a58-6a17-44bd-807b-f31643801a7a" name="To Create / Amend Request" id="2027.78ad5dec-d04a-44a0-8e9f-d80c75679a17">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98" name="add a row to selected transaction list" id="2025.fb51b0af-8183-424c-8dea-eaeee10e3cda">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="446" y="-13" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.f417b728-8cd1-4be3-8b8f-d4666914c55c</ns16:incoming>
                                <ns16:outgoing>2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98</ns16:outgoing>
                                <ns16:script>tw.local.collectionCurrencyVis = "READONLY";&#xD;
&#xD;
&#xD;
var selectedTransactionsIndices  = [];&#xD;
selectedTransactionsIndices = tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelectedIndices;&#xD;
&#xD;
if(tw.local.odcRequest.FcCollections.selectedTransactions.length &gt; 0) {&#xD;
   var oldSelectedTransactions =  tw.local.odcRequest.FcCollections.selectedTransactions; &#xD;
   tw.local.odcRequest.FcCollections.selectedTransactions =  [];&#xD;
   for(var i=0;i&lt;selectedTransactionsIndices.length;i++)&#xD;
    {&#xD;
        &#xD;
       tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length] = tw.local.odcRequest.FcCollections.retrievedTransactions[selectedTransactionsIndices[i]];&#xD;
      &#xD;
      for (var j =0 ;j&lt;oldSelectedTransactions.length;j++){&#xD;
&#xD;
&#xD;
         if(tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length-1].referenceNumber ==oldSelectedTransactions[j].referenceNumber ){&#xD;
             tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length-1].amountAllocatedForCurrencyRequest = oldSelectedTransactions[j].amountAllocatedForCurrencyRequest;&#xD;
             tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length-1].allocatedAmountInRequestCurrency = oldSelectedTransactions[j].allocatedAmountInRequestCurrency;&#xD;
      }&#xD;
    }&#xD;
   }&#xD;
     &#xD;
}      &#xD;
else{&#xD;
   tw.local.odcRequest.FcCollections.selectedTransactions =  [];&#xD;
   for(var i=0;i&lt;selectedTransactionsIndices.length;i++)&#xD;
    {&#xD;
        &#xD;
       tw.local.odcRequest.FcCollections.selectedTransactions[tw.local.odcRequest.FcCollections.selectedTransactions.length] = tw.local.odcRequest.FcCollections.retrievedTransactions[selectedTransactionsIndices[i]];&#xD;
       &#xD;
     }&#xD;
 }    &#xD;
//------&#xD;
//-----------------------------------------------&#xD;
&#xD;
//var array = "";&#xD;
//var subarray = "";&#xD;
//&#xD;
// array = tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelectedIndices;&#xD;
//&#xD;
//&#xD;
//&#xD;
//for(var i=0;i&lt;array.length;i++)&#xD;
//{&#xD;
//&#xD;
//	subarray = array.slice(i,i+1);&#xD;
//	&#xD;
//		if(tw.local.odcRequest.FcCollections.selectedTransactions != null &amp;&amp; tw.local.odcRequest.FcCollections.selectedTransactions.length == 0)&#xD;
//		{&#xD;
//		tw.local.odcRequest.FcCollections.selectedTransactions[i] = {};&#xD;
//		tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].accountNo;&#xD;
//		tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].referenceNumber;&#xD;
//		tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].postingDate;&#xD;
//		tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].transactionAmount;&#xD;
//		tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].valueDate;&#xD;
//		tw.local.odcRequest.FcCollections.selectedTransactions[i].existAmount = tw.local.odcRequest.FcCollections.retrievedTransactions[subarray].existAmount;&#xD;
//		}&#xD;
//		else&#xD;
//		{&#xD;
////			        for (var i = 0; i &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; i++) &#xD;
////			        {&#xD;
////			            for (var j = 0; j &lt; tw.local.odcRequest.FcCollections.selectedTransactions.length; j++) &#xD;
////			            {&#xD;
////			                if (i != j) &#xD;
////			                {&#xD;
////			                    if (tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber == tw.local.odcRequest.FcCollections.selectedTransactions[j].referenceNumber) &#xD;
////			                    {&#xD;
//////			                       alert("This Tranaction Reference Number have been added before");&#xD;
////			                       tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.selectedTransactions["+i+"].referenceNumber", "This Tranaction Reference Number have been added before");&#xD;
////			                        &#xD;
////			                    }&#xD;
////			                  &#xD;
////			                    &#xD;
////			                }&#xD;
////			                  &#xD;
////			            }&#xD;
////			        }&#xD;
////			         &#xD;
//			                     tw.local.odcRequest.FcCollections.selectedTransactions.push(tw.local.odcRequest.FcCollections.retrievedTransactions[subarray]);&#xD;
//			                    &#xD;
//			      &#xD;
//			    }&#xD;
//}&#xD;
&#xD;
&#xD;
&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.fb51b0af-8183-424c-8dea-eaeee10e3cda" targetRef="2025.c809aba7-3270-4571-8a29-c5c009a87243" name="To Create / Amend Request" id="2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.f417b728-8cd1-4be3-8b8f-d4666914c55c" name="rows selected?" id="2025.2889d0ab-cf30-46b0-8919-9c1d896beb5e">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="645" y="-67" width="32" height="32" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.f417b728-8cd1-4be3-8b8f-d4666914c55c</ns16:outgoing>
                                <ns16:outgoing>2027.7170da1b-ba91-424e-833a-b411ef3b412d</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.2889d0ab-cf30-46b0-8919-9c1d896beb5e" targetRef="2025.fb51b0af-8183-424c-8dea-eaeee10e3cda" name="rows selected" id="2027.f417b728-8cd1-4be3-8b8f-d4666914c55c">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">!!tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelected</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d" name="display error message" id="2025.a99010f5-f4f0-4a56-8e2d-3f0655e71cfe">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="740" y="-150" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7170da1b-ba91-424e-833a-b411ef3b412d</ns16:incoming>
                                <ns16:outgoing>2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d</ns16:outgoing>
                                <ns16:script>// &#xD;
// tw.local.errorMessage += "&lt;li&gt;"+"Select one or more Transaction"+"&lt;/li&gt;";	&#xD;
alert("Select one or more Transaction");</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.2889d0ab-cf30-46b0-8919-9c1d896beb5e" targetRef="2025.a99010f5-f4f0-4a56-8e2d-3f0655e71cfe" name="No" id="2027.7170da1b-ba91-424e-833a-b411ef3b412d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.odcRequest.FcCollections.retrievedTransactions.listAllSelectedIndices.length	  ==	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.a99010f5-f4f0-4a56-8e2d-3f0655e71cfe" targetRef="2025.46e58a58-6a17-44bd-807b-f31643801a7a" name="To Create / Amend Request" id="2027.4e8708c5-25c4-4108-86f0-82fbf5f8178d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="collectionCurrencyVis" id="2056.7c986a7b-8a80-42bc-868c-22410aeddcad" />
                            <ns16:callActivity calledElement="1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f" default="2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb" name="Get exchange rate" id="2025.30c2df0f-2ec6-4f9b-85d3-219773f46111">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="660" y="248" width="95" height="70" />
                                    <ns3:postAssignmentScript>tw.local.odcRequest.FcCollections.negotiatedExchangeRate = tw.local.odcRequest.FcCollections.standardExchangeRate;&#xD;
tw.local.cifCurrency = tw.local.odcRequest.CustomerInfo.cif +"_"+ tw.local.odcRequest.FcCollections.currency.name;&#xD;
console.log(tw.local.odcRequest.FcCollections.standardExchangeRate);</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4f75807d-191a-4553-809f-630208b0d737</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.FinancialDetailsBR.currency.name</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.cc3906a0-dd9d-4580-8485-14caef94ac8d</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.FcCollections.currency.name</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.a1cc30d0-50bc-4feb-8427-9f8aafe9ef96</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.536b2aa5-a30f-4eca-87fa-3a28066753ee">tw.local.odcRequest.FcCollections.standardExchangeRate</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.7e0973d8-72ed-4b76-81df-4cda85422d2c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.99a0385c-a950-49d3-88a0-1a3042dd4586" name="setting status and sub status" id="2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1421" y="172" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.3bb12c6a-ecf3-4a7d-858c-8925b89ebc23</ns16:incoming>
                                <ns16:incoming>2027.9a43cba3-9c21-4454-8d9c-4a57a6c8102e</ns16:incoming>
                                <ns16:incoming>2027.fab42a6b-ed96-48f1-80ea-4d8fc49b39f3</ns16:incoming>
                                <ns16:outgoing>2027.99a0385c-a950-49d3-88a0-1a3042dd4586</ns16:outgoing>
                                <ns16:script>if(tw.local.odcRequest.stepLog.action      == tw.epv.CreationActions.submitRequest){&#xD;
	tw.local.odcRequest.appInfo.status    = "Initiated";&#xD;
	if(tw.local.role == tw.epv.userRole.branch){&#xD;
	tw.local.odcRequest.appInfo.subStatus = "Pending Branch Compliance Initiation Review";&#xD;
	}&#xD;
	else if(tw.local.role == tw.epv.userRole.hub){&#xD;
	tw.local.odcRequest.appInfo.subStatus = "Pending Hub Compliance Initiation Review";&#xD;
	}&#xD;
}&#xD;
else if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequestToHubDirectory){&#xD;
	tw.local.odcRequest.appInfo.status    = "In Execution";&#xD;
	tw.local.odcRequest.appInfo.subStatus = "Pending Execution Hub Processing";&#xD;
}&#xD;
else if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest){&#xD;
	tw.local.odcRequest.appInfo.status    = "Initiated";&#xD;
	tw.local.odcRequest.appInfo.subStatus = "Pending Cancelation Confirmation";&#xD;
}&#xD;
&#xD;
/////////////&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequestToHubDirectory)&#xD;
{&#xD;
	tw.local.odcRequest.FinancialDetailsFO.executionHub.value = tw.local.hubCode;&#xD;
}&#xD;
/////////&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)&#xD;
{&#xD;
	tw.local.lastAction = tw.epv.CreationActions.cancelRequest;&#xD;
}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91" targetRef="2025.769b40fb-8c10-42e7-82ab-ee90fe3eed81" name="To End" id="2027.99a0385c-a950-49d3-88a0-1a3042dd4586">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" default="2027.2f58c309-16be-4828-88a5-42a22d7c66a6" name="Audit Request History" id="2025.769b40fb-8c10-42e7-82ab-ee90fe3eed81">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1540" y="92" width="95" height="70" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.99a0385c-a950-49d3-88a0-1a3042dd4586</ns16:incoming>
                                <ns16:outgoing>2027.2f58c309-16be-4828-88a5-42a22d7c66a6</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.role</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.a617c560-c740-484e-89de-0931088cdc6c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.769b40fb-8c10-42e7-82ab-ee90fe3eed81" targetRef="2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9" name="To Audited?" id="2027.2f58c309-16be-4828-88a5-42a22d7c66a6">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8" default="2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4" name="Set ECM default properties" id="2025.e6de2c56-d47d-4541-8874-43156e2b1a04">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="445" y="81" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418</ns16:incoming>
                                <ns16:outgoing>2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.399c7a58-00b5-4451-9813-41c0b9652088</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.cif</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.c28023fb-b45e-4b63-ae36-97e6df6421bc</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.25394215-074f-4b79-8e84-9a96d32cc83b</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.0261e8ad-a540-4682-88c5-87dff3eab23c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.7d269650-ee48-4101-80db-2807cf921562</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9">tw.local.odcRequest.attachmentDetails</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="conditions" id="2056.25df8083-a36e-46f4-8f74-c21352472afc" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.f03be96c-9d23-4354-8ddc-b8c195291e0b" name="calculate total allocated amount " id="2025.add865fd-cfd5-4045-841f-9e4c95d9423c">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="868" y="-52" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.f03be96c-9d23-4354-8ddc-b8c195291e0b</ns16:outgoing>
                                <ns16:script>var sum = 0;&#xD;
if(tw.local.odcRequest.FcCollections.selectedTransactions.length != 0)&#xD;
{&#xD;
&#xD;
	for(var i=0;i&lt;tw.local.odcRequest.FcCollections.selectedTransactions.length;i++)&#xD;
	{ &#xD;
	      tw.local.odcRequest.FcCollections.selectedTransactions[i].allocatedAmountInRequestCurrency = tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest * tw.local.odcRequest.FcCollections.negotiatedExchangeRate; &#xD;
		sum += tw.local.odcRequest.FcCollections.selectedTransactions[i].allocatedAmountInRequestCurrency;&#xD;
		&#xD;
	}&#xD;
	tw.local.odcRequest.FcCollections.totalAllocatedAmount = sum;&#xD;
}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:intermediateThrowEvent name="Stay on page 6" id="2025.1459d000-61ea-441b-8c7e-25136085e9fa">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1229" y="20" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d</ns16:incoming>
                                <ns16:incoming>2027.f03be96c-9d23-4354-8ddc-b8c195291e0b</ns16:incoming>
                                <ns16:incoming>2027.14334ed8-4d74-46d3-8646-fc7e2595281d</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.28b35608-231d-45e7-823b-f4f3e2c86f67" targetRef="2025.1459d000-61ea-441b-8c7e-25136085e9fa" name="To Stay on page 6" id="2027.398c07bf-77ed-49f6-88bb-45ed01bbb79d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.add865fd-cfd5-4045-841f-9e4c95d9423c" targetRef="2025.1459d000-61ea-441b-8c7e-25136085e9fa" name="To Stay on page 6" id="2027.f03be96c-9d23-4354-8ddc-b8c195291e0b">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 7" id="2025.d12bae84-1eca-4b47-85d9-59ff8d89281d">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="664" y="340" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb</ns16:incoming>
                                <ns16:incoming>2027.285ce338-e84a-4dce-89dc-0853289ffc52</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.30c2df0f-2ec6-4f9b-85d3-219773f46111" targetRef="2025.d12bae84-1eca-4b47-85d9-59ff8d89281d" name="To Stay on page 7" id="2027.1be27e7c-9f36-4ee4-88a9-ac385a4286bb">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="today" id="2056.539b1ec1-0a89-447a-81ea-f3f12bda1101">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">new Date()</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="cifCurrency" id="2056.fb2c23ad-6257-4292-82aa-e1975e8254dc" />
                            <ns16:callActivity calledElement="1.8e583b1e-1719-4e19-a6ce-6f41202527d4" default="2027.9054de94-cc5b-4018-8c9d-4a9ac615462c" name="Generate request No" id="2025.2b5c0a5f-7672-46bd-8a52-37ae501d7d58">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="81" y="81" width="95" height="70" />
                                    <ns3:postAssignmentScript>tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098</ns16:incoming>
                                <ns16:outgoing>2027.9054de94-cc5b-4018-8c9d-4a9ac615462c</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.d063d99f-aaa6-427d-ac13-2ac9946acbda</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.code</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.3bdd8527-404f-4ac0-bd04-07931e8d167e</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.9a8f8a58-0e47-413f-b430-fb905024fce6</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.2b5c0a5f-7672-46bd-8a52-37ae501d7d58" targetRef="2025.125eab48-4fda-4887-841c-c11355ab579c" name="To Attachment" id="2027.9054de94-cc5b-4018-8c9d-4a9ac615462c">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.7ee96dd0-834b-44cb-af41-b21585627e49" default="2027.5e6497c2-313c-40f1-81c5-137dd55f746b" name="Audit request Data" id="2025.3ed54d76-c867-4a7b-8891-68bc17aea4ee">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1741" y="92" width="95" height="70" />
                                    <ns3:preAssignmentScript />
                                    <ns3:postAssignmentScript>console.log("******************************************************");&#xD;
console.log("SQL=  "+tw.local.sqlTemp);&#xD;
console.log("******************************************************");</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.2c918199-35a7-45e1-8eb6-096f6ada59e3</ns16:incoming>
                                <ns16:outgoing>2027.5e6497c2-313c-40f1-81c5-137dd55f746b</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.afad40c5-a38b-475c-8154-b4dabd94b6fe</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.254cf8eb-2743-4c53-8c52-e51c8c22884e</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.e6f312e7-4580-4d58-8a10-d7720006d8bf</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sqlTemp</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.59dc474f-3e47-40ee-8737-ad21d25eb436</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.3ed54d76-c867-4a7b-8891-68bc17aea4ee" targetRef="2025.1d5b803f-b012-4bd6-82e3-6fffae922c98" name="To Valid?" id="2027.5e6497c2-313c-40f1-81c5-137dd55f746b">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098" name="is update?" id="2025.819f5c1b-594a-44a2-8a30-d73903741b3b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="0" y="100" width="32" height="32" />
                                    <ns3:postAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.3a0e191c-d74d-48fe-8855-5caadc7bdf66</ns16:incoming>
                                <ns16:outgoing>2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098</ns16:outgoing>
                                <ns16:outgoing>2027.b3442983-6e1e-4389-8f15-576414d3da2b</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.819f5c1b-594a-44a2-8a30-d73903741b3b" targetRef="2025.2b5c0a5f-7672-46bd-8a52-37ae501d7d58" name="No" id="2027.e12f6e70-6bf7-4713-8f41-9c2bb8923098">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.819f5c1b-594a-44a2-8a30-d73903741b3b" targetRef="2025.5b11ce9d-63dc-45ab-8334-76f7216f2803" name="To Retrieve Parent Request Number" id="2027.b3442983-6e1e-4389-8f15-576414d3da2b">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.odcRequest.requestNature.name	  ==	  tw.epv.RequestNature.UpdateRequest</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.509c6ef8-f489-4ab8-bcb8-45c4531aa546" default="2027.6a90d411-7682-4e7a-8d71-30028735cfd9" name="Retrieve Parent Request Number" id="2025.5b11ce9d-63dc-45ab-8334-76f7216f2803">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="85" y="-9" width="95" height="70" />
                                    <ns3:postAssignmentScript>tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.b3442983-6e1e-4389-8f15-576414d3da2b</ns16:incoming>
                                <ns16:outgoing>2027.6a90d411-7682-4e7a-8d71-30028735cfd9</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.e1ca7465-4084-405d-8ea6-ab3f3762de92</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.parentRequestNo</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.29fabc80-90b8-4cad-81e3-1c319c6f595a</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.4ec4b61d-5cd9-43b6-827c-c1801162373f</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.5b11ce9d-63dc-45ab-8334-76f7216f2803" targetRef="2025.125eab48-4fda-4887-841c-c11355ab579c" name="Yes" id="2027.6a90d411-7682-4e7a-8d71-30028735cfd9">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.367d7cf7-1d8b-4fdf-882c-796796f01226" name="Is FirstTime" id="2025.d4d6d8cb-2e86-4941-844e-63bcb70d177b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="-245" y="101" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.fcce1260-f60c-46c8-8768-f0db6fef23fd</ns16:outgoing>
                                <ns16:outgoing>2027.367d7cf7-1d8b-4fdf-882c-796796f01226</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.d4d6d8cb-2e86-4941-844e-63bcb70d177b" targetRef="2025.1b2e8257-1e18-497e-8d78-851099ae9415" name="Yes" id="2027.fcce1260-f60c-46c8-8768-f0db6fef23fd">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isFirstTime	  ==	  true</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.d4d6d8cb-2e86-4941-844e-63bcb70d177b" targetRef="2025.55db037b-3a91-4884-8d92-bd45588aef5f" name="No" id="2027.367d7cf7-1d8b-4fdf-882c-796796f01226">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isFirstTime</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e" name="Update BO Values" id="2025.55db037b-3a91-4884-8d92-bd45588aef5f">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="-103" y="185" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.367d7cf7-1d8b-4fdf-882c-796796f01226</ns16:incoming>
                                <ns16:outgoing>2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e</ns16:outgoing>
                                <ns16:script>tw.local.odcRequest.stepLog ={};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT01;&#xD;
&#xD;
tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.complianceApproval= false;&#xD;
tw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT01;&#xD;
tw.local.actionConditions.userRole= tw.local.initiator;&#xD;
tw.local.actionConditions.lastStepAction="";&#xD;
tw.local.actionConditions.requestType = tw.local.odcRequest.requestType.value;&#xD;
tw.local.odcRequest.stepLog.action ="";&#xD;
&#xD;
tw.local.odcRequest.initiator = tw.local.initiator;//Branch or hub&#xD;
//tw.local.odcRequest.appInfo.instanceID = tw.system.processInstance.id; &#xD;
	tw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.CACT01;&#xD;
	if(tw.local.branchCode != "" &amp;&amp; tw.local.branchCode != null)&#xD;
	{&#xD;
		tw.local.role = tw.epv.userRole.branch;&#xD;
	}&#xD;
	else&#xD;
	{&#xD;
		tw.local.role =tw.epv.userRole.hub;&#xD;
	}&#xD;
&#xD;
tw.local.odcRequest.appInfo.instanceID = tw.local.odcRequest.requestNo;</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.55db037b-3a91-4884-8d92-bd45588aef5f" targetRef="2025.125eab48-4fda-4887-841c-c11355ab579c" name="To is update?" id="2027.fbc07419-bbc4-4de7-84d7-bbe9901df93e">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="currencyListVIS" id="2056.76107c34-e108-4643-8da1-34f54054e499" />
                            <ns16:exclusiveGateway default="2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6" name="Valid?" id="2025.1d5b803f-b012-4bd6-82e3-6fffae922c98">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1852" y="111" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.5e6497c2-313c-40f1-81c5-137dd55f746b</ns16:incoming>
                                <ns16:outgoing>2027.bb576e8e-dc9f-4992-8532-24b61b983a46</ns16:outgoing>
                                <ns16:outgoing>2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.1d5b803f-b012-4bd6-82e3-6fffae922c98" targetRef="dd400abd-e346-4579-a741-c91be71e13ba" name="Yes" id="2027.bb576e8e-dc9f-4992-8532-24b61b983a46">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.90b9f7d5-9708-485d-85bc-48249004dc25">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1828" y="212" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6</ns16:incoming>
                                <ns16:incoming>2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.1d5b803f-b012-4bd6-82e3-6fffae922c98" targetRef="2025.90b9f7d5-9708-485d-85bc-48249004dc25" name="No" id="2027.0733cf4f-691a-42b0-87a8-ec53d5895ff6">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.errorMessage	  !=	  null</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f" name="Audited?" id="2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1649" y="112" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.2f58c309-16be-4828-88a5-42a22d7c66a6</ns16:incoming>
                                <ns16:outgoing>2027.2c918199-35a7-45e1-8eb6-096f6ada59e3</ns16:outgoing>
                                <ns16:outgoing>2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9" targetRef="2025.3ed54d76-c867-4a7b-8891-68bc17aea4ee" name="Yes" id="2027.2c918199-35a7-45e1-8eb6-096f6ada59e3">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.f7cbf856-04cc-44eb-8980-c5f5e9a1b5c9" targetRef="2025.90b9f7d5-9708-485d-85bc-48249004dc25" name="No" id="2027.b1dd6411-ab5b-496b-85c7-391e012e7e7f">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sqlTemp" id="2056.0d50691a-03ec-493f-82d0-a3b808faa7b3" />
                            <ns16:callActivity calledElement="1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Service" id="2025.df00826b-75d0-4379-84fc-7d1c724e3e46">
                                <ns16:extensionElements>
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns13:nodeVisualInfo x="359" y="-121" width="95" height="70" />
                                    <ns3:autoMap>true</ns3:autoMap>
                                </ns16:extensionElements>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.f43a0726-0192-4d4c-942e-83e973ee5015</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.attachmentDetails.folderID</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                            </ns16:callActivity>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418" name="Set Attachments list" id="2025.0aa24670-c849-405c-87f0-2ceaba6a718b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="314" y="81" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7b4d33ce-7300-4150-8abf-dee5da66eba7</ns16:incoming>
                                <ns16:outgoing>2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418</ns16:outgoing>
                                <ns16:script>//var index= 0;&#xD;
&#xD;
//if(!tw.local.odcRequest.attachmentDetails)&#xD;
//  tw.local.odcRequest.attachmentDetails = {};&#xD;
//&#xD;
//if (tw.local.odcRequest.attachmentDetails.attachment == null || tw.local.odcRequest.attachmentDetails.attachment == undefined) {   &#xD;
//	tw.local.odcRequest.attachmentDetails.attachment = []; 	&#xD;
		&#xD;
//	setProps("AIRWAY BILL", "AIRWAY BILL", "" ); &#xD;
//	setProps("TRUCK CONSIGNMENT NOTE","TRUCK CONSIGNMENT NOTE", ""); &#xD;
//	setProps("N/N BILL OF LADING","N/N BILL OF LADING",""); &#xD;
//	setProps("COURIER / POST RECEIPT", "COURIER / POST RECEIPT" , "" ); &#xD;
//	setProps("PACKING LIST","PACKING LIST",""); &#xD;
//	setProps("CERTIFICATE OF ORIGIN","CERTIFICATE OF ORIGIN", "" );&#xD;
//	setProps("CERTIFICATE OF ANALYSIS", "CERTIFICATE OF ANALYSIS", "");&#xD;
//	setProps("INSURANCE POLICY / CERTIFICATE", "INSURANCE POLICY / CERTIFICATE","");&#xD;
//	setProps("BENEFECIARY DECLARATION", "BENEFECIARY DECLARATION", "");&#xD;
//	setProps("NON RADIOACTIVE CERTIFICATE", "NON RADIOACTIVE CERTIFICATE","");&#xD;
//	setProps("PHYTOSANITARY CERTIFICATE", "PHYTOSANITARY CERTIFICATE","");&#xD;
//	setProps("CERTIFICATE OF ANALYSIS","Bill of exchange/draft", "الكمبيالة" );&#xD;
//	setProps("HEALTH CERTIFICATE","HEALTH CERTIFICATE", "");&#xD;
//	setProps("INSPECTION CERTIFICATE", "INSPECTION CERTIFICATE",  "");&#xD;
//	setProps("WARRANTY CERTIFICATE", "WARRANTY CERTIFICATE","");&#xD;
//	setProps( "TEST CERTIFICATE","TEST CERTIFICATE", "");&#xD;
//&#xD;
//	tw.local.odcRequest.attachmentDetails.ecmProperties = {};&#xD;
//	tw.local.odcRequest.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
//	tw.local.odcRequest.attachmentDetails.ecmProperties.defaultProperties = {};&#xD;
//}	&#xD;
//&#xD;
//function setProps(name, desc, arabicName){	&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index]= {};&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].name= name;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].description= desc;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].arabicName= arabicName;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].numOfOriginals= 0;&#xD;
//	tw.local.odcRequest.attachmentDetails.attachment[index].numOfCopies= 0;&#xD;
//	index++;	&#xD;
//}&#xD;
//&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.0aa24670-c849-405c-87f0-2ceaba6a718b" targetRef="2025.e6de2c56-d47d-4541-8874-43156e2b1a04" name="To Set ECM default properties" id="2027.4dd8e2a2-bbfc-4b63-8c2e-22f43bba6418">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.e246ff72-d6b4-4fbc-9ffb-3381a50a9ac7" default="2027.7115e13c-7593-4314-8f4b-f49796567d59" name="Validate Mandatory Docs" id="2025.2bc8a62f-795f-4fd1-81a0-ea668ed25f2c">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1200" y="165" width="95" height="70" />
                                    <ns3:preAssignmentScript>tw.local.errorMessage= "";&#xD;
&#xD;
var list_attachments = tw.local.odcRequest.attachmentDetails.attachment;&#xD;
var list_selAttachments = list_attachments.listAllSelectedIndices || []; &#xD;
var isChecked_CustRequest = false;&#xD;
&#xD;
for( var i=0; i&lt;list_selAttachments.length; i++)&#xD;
{&#xD;
	var index= list_selAttachments[i];&#xD;
	&#xD;
	if( list_attachments[index].name == "Customer Request" ) {&#xD;
		isChecked_CustRequest = true;&#xD;
		break;&#xD;
	}&#xD;
}&#xD;
&#xD;
if( isChecked_CustRequest == false)    &#xD;
	tw.local.errorMessage += "&lt;li&gt;"+ "Please Check 'Customer Request' Document." +"&lt;/li&gt;";&#xD;
	&#xD;
tw.local.errorMessage!=null ?  tw.local.errorPanelVIS ="EDITABLE": tw.local.errorPanelVIS ="NONE";</ns3:preAssignmentScript>
                                    <ns3:postAssignmentScript>if(tw.local.docValMsg!= null || tw.local.docValMsg!=undefined){&#xD;
	tw.local.errorMessage+= tw.local.docValMsg;&#xD;
	tw.local.errorMessage!=null ?  tw.local.errorPanelVIS ="EDITABLE": tw.local.errorPanelVIS ="NONE";&#xD;
&#xD;
}</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.d5b1e172-ea40-4e43-847c-6417d46aaa14</ns16:incoming>
                                <ns16:outgoing>2027.7115e13c-7593-4314-8f4b-f49796567d59</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.1bf8ade2-bd6b-4af2-87c8-1b269002413f</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestType.value</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.f43a0726-0192-4d4c-942e-83e973ee5015</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.attachmentDetails.folderID</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.c965f53e-7627-46ac-8f53-b8a0e4d9e4fb</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.validateDocMsg</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.46a6e2d9-ec27-448f-897f-8913cdfdb06b</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.docValMsg</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.2bc8a62f-795f-4fd1-81a0-ea668ed25f2c" targetRef="2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2" name="To Valid?" id="2027.7115e13c-7593-4314-8f4b-f49796567d59">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.c7e218da-97a5-4c47-8687-c48d630d6dc4" name="Valid?" id="2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1335" y="188" width="32" height="32" />
                                    <ns3:postAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7115e13c-7593-4314-8f4b-f49796567d59</ns16:incoming>
                                <ns16:outgoing>2027.3bb12c6a-ecf3-4a7d-858c-8925b89ebc23</ns16:outgoing>
                                <ns16:outgoing>2027.c7e218da-97a5-4c47-8687-c48d630d6dc4</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2" targetRef="2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91" name="Yes" id="2027.3bb12c6a-ecf3-4a7d-858c-8925b89ebc23">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 3" id="2025.06fec280-6dce-4af7-8731-033834d10d5c">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1320" y="345" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:preAssignmentScript>//console.log("logg error msg= "+ tw.local.errorMessage);&#xD;
tw.local.errorMessage!=null ?  tw.local.errorPanelVIS ="EDITABLE": tw.local.errorPanelVIS ="NONE";</ns3:preAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.c7e218da-97a5-4c47-8687-c48d630d6dc4</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.d37cc783-7fb2-4b0b-871f-3c97e9ec3ce2" targetRef="2025.06fec280-6dce-4af7-8731-033834d10d5c" name="No" id="2027.c7e218da-97a5-4c47-8687-c48d630d6dc4">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validateDocMsg" id="2056.316e2023-7ea1-4df1-84bd-cc961bfbecc3" />
                            <ns16:sequenceFlow sourceRef="2025.e6de2c56-d47d-4541-8874-43156e2b1a04" targetRef="2025.7e5a7e9c-39e6-4ab6-84a7-c7c6e834b6ea" name="To Create ECM Folder" id="2027.d14ba9dc-31fe-4a4f-8208-ab85f9cd2dd4">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="docValMsg" id="2056.9dc8b867-8bad-4166-808b-e4ad0f33dd88" />
                            <ns16:exclusiveGateway default="2027.d5b1e172-ea40-4e43-847c-6417d46aaa14" name="Action?" id="2025.0986e69a-6cb9-44dd-82b2-a42f1e0fac00">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1107" y="182" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.d5b1e172-ea40-4e43-847c-6417d46aaa14</ns16:outgoing>
                                <ns16:outgoing>2027.9a43cba3-9c21-4454-8d9c-4a57a6c8102e</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.0986e69a-6cb9-44dd-82b2-a42f1e0fac00" targetRef="2025.2bc8a62f-795f-4fd1-81a0-ea668ed25f2c" name="submit" id="2027.d5b1e172-ea40-4e43-847c-6417d46aaa14">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.0986e69a-6cb9-44dd-82b2-a42f1e0fac00" targetRef="2025.921aeefd-1fd5-4512-802f-bb1bd75a9f91" name="cancel" id="2027.9a43cba3-9c21-4454-8d9c-4a57a6c8102e">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.cancelRequest</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="choice" id="2056.76f7b38e-ea7a-43bd-84e6-b1e004009a2e" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.4513a1a9-7623-4051-81c6-3465578f5640" name="validation" id="2025.a0837de4-4431-4576-8ecd-6c743b723d43">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="910" y="53" width="95" height="70" color="#95D087" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.4513a1a9-7623-4051-81c6-3465578f5640</ns16:outgoing>
                                <ns16:script>if(!tw.local.odcRequest.FcCollections.fromDate &amp;&amp; !tw.local.odcRequest.FcCollections.ToDate)	&#xD;
	if(tw.local.odcRequest.FcCollections.fromDate &gt;= tw.local.odcRequest.FcCollections.ToDate)&#xD;
	{&#xD;
		tw.local.errorMessage += "&lt;li&gt;"+" Search Transactions From Date shouln't be greater than Search Transactions To Date"+"&lt;/li&gt;";	&#xD;
		 tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.fromDate", "Search Transactions From Date shouln't be greater than Search Transactions To Date");&#xD;
		 tw.system.coachValidation.addValidationError("tw.local.odcRequest.FcCollections.ToDate", "Search Transactions From Date shouln't be greater than Search Transactions To Date");&#xD;
	}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.a0837de4-4431-4576-8ecd-6c743b723d43" targetRef="2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273" name="To Valid?" id="2027.4513a1a9-7623-4051-81c6-3465578f5640">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightBottom</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.14334ed8-4d74-46d3-8646-fc7e2595281d" name="Exclusive Gateway" id="2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1041" y="89" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.4513a1a9-7623-4051-81c6-3465578f5640</ns16:incoming>
                                <ns16:outgoing>2027.14334ed8-4d74-46d3-8646-fc7e2595281d</ns16:outgoing>
                                <ns16:outgoing>2027.4e805889-60ae-4790-8410-c2c59cca283f</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273" targetRef="2025.1459d000-61ea-441b-8c7e-25136085e9fa" name="To Stay on page 6" id="2027.14334ed8-4d74-46d3-8646-fc7e2595281d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.b20e04f9-ce5b-4ecb-853f-6b1202b76273" targetRef="2025.28b35608-231d-45e7-823b-f4f3e2c86f67" name="To Get account transactions" id="2027.4e805889-60ae-4790-8410-c2c59cca283f">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4" name="Exclusive Gateway 1" id="2025.c809aba7-3270-4571-8a29-c5c009a87243">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="372" y="5" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.0e1910ef-87b4-4e67-8c18-22ebb79b2f98</ns16:incoming>
                                <ns16:outgoing>2027.a4212e56-9bb4-41c2-812b-e4dd08de0584</ns16:outgoing>
                                <ns16:outgoing>2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.c809aba7-3270-4571-8a29-c5c009a87243" targetRef="2025.46e58a58-6a17-44bd-807b-f31643801a7a" name="To Create / Amend Request" id="2027.a4212e56-9bb4-41c2-812b-e4dd08de0584">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 4" id="2025.4634743f-b927-43ef-8dc9-0d4512fa9fec">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="255" y="-8" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.c809aba7-3270-4571-8a29-c5c009a87243" targetRef="2025.4634743f-b927-43ef-8dc9-0d4512fa9fec" name="To Stay on page 4" id="2027.824b12c1-27cc-4cd3-82a5-c94ed74575e4">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="oldSelectedIndices" id="2056.4ab1f720-7e58-4e17-8640-c3834348b0a4" />
                            <ns16:callActivity calledElement="1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f" default="2027.285ce338-e84a-4dce-89dc-0853289ffc52" name="Get Account Numbers" id="2025.97508aa7-a872-4e8f-8926-162c835ea004">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="414" y="185" width="95" height="70" />
                                    <ns3:postAssignmentScript />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.285ce338-e84a-4dce-89dc-0853289ffc52</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.0bc98ce8-10aa-460a-8857-8a8600dafc90</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.CustomerInfo.cif</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.FcCollections.currency.value</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.208f75bb-611b-4393-83d6-d60470c4a6a4</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.odcRequest.FcCollections.listOfAccounts</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.97508aa7-a872-4e8f-8926-162c835ea004" targetRef="2025.d12bae84-1eca-4b47-85d9-59ff8d89281d" name="To Stay on page 7" id="2027.285ce338-e84a-4dce-89dc-0853289ffc52">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.4bdd7c1f-1123-46cb-88c0-927d2d1e2423" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="validationMsg" id="2056.384aa163-2cab-40ca-8350-af8f35164f95" />
                            <ns3:htmlHeaderTag id="ef16a6ed-4dbc-4c15-9de7-b885ea6d3eb3">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:mobileReady>true</ns3:mobileReady>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="6d73953a-7ab6-4eb3-86d1-3a621b59928e" />
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="3daf28b6-a1ea-429d-864b-51546a24d9fb" />
                                <ns3:epvProcessLinkRef epvId="21.93c5c002-7ac4-4283-83ee-63b8662f9223" epvProcessLinkId="411e58bc-2e05-4e97-8c75-48a63a398250" />
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="499195eb-f3f5-4d40-8279-7ef02448ec2b" />
                                <ns3:epvProcessLinkRef epvId="21.769dc134-1d15-4dd4-a967-c5f61cf352dc" epvProcessLinkId="93922f64-3a85-4073-864a-a37ed98751ea" />
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="588e65bb-4b8f-4ba8-89d5-75f52b0b3d12" />
                                <ns3:epvProcessLinkRef epvId="21.062854b5-6513-4da8-84ab-0126f90e550d" epvProcessLinkId="6a7a416e-c510-48fc-84bb-0037adb307b8" />
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="89ea6690-4305-4b7e-8825-14250a80df23" />
                            </ns3:epvProcessLinks>
                            <ns3:localizationResourceLinks>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    <ns3:id>69.1583433b-7213-455b-8bfb-f438e5f671d8</ns3:id>
                                </ns3:resourceRef>
                            </ns3:localizationResourceLinks>
                            <ns3:envProcessLinks>
                                <ns3:envProcessLinkRef envId="2094.63e43d67-f3a4-4489-8560-5c6a837d5dab" envProcessLinkId="e82e0b4d-3a0d-4e3a-8171-4be6eb6409e0" />
                                <ns3:envProcessLinkRef envId="2094.6592f70d-f558-4606-8b84-18b641c868fe" envProcessLinkId="5b10ea2d-86dc-4be5-87c3-af547cfa971b" />
                            </ns3:envProcessLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.52b23fff-ad18-4c9c-8d4b-da25fa353d74">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.initiator = "";
autoObject.requestNature = {};
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = {};
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new Date();
autoObject.ImporterName = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = {};
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = {};
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = {};
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = {};
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = {};
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = {};
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = {};
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = {};
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = [];
autoObject.BasicDetails.Bills[0] = {};
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();
autoObject.BasicDetails.Invoice = [];
autoObject.BasicDetails.Invoice[0] = {};
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new Date();
autoObject.GeneratedDocumentInfo = {};
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = [];
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = [];
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = [];
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = [];
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = [];
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = {};
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = [];
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = {};
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = {};
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new Date();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = {};
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = [];
autoObject.FinancialDetailsBR.listOfAccounts[0] = {};
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = {};
autoObject.FcCollections.currency = {};
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new Date();
autoObject.FcCollections.ToDate = new Date();
autoObject.FcCollections.accountNo = {};
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = [];
autoObject.FcCollections.retrievedTransactions[0] = {};
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = [];
autoObject.FcCollections.selectedTransactions[0] = {};
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new Date();
autoObject.FcCollections.selectedTransactions[0].valueDate = new Date();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = [];
autoObject.FcCollections.listOfAccounts[0] = {};
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = {};
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new Date();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = {};
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = [];
autoObject.FinancialDetailsFO.multiTenorDates[0] = {};
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = {};
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = {};
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = {};
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = {};
autoObject.ProductShipmentDetails.CBECommodityClassification = {};
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new Date();
autoObject.ProductShipmentDetails.shipmentMethod = {};
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = {};
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = {};
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = {};
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = {};
autoObject.ContractCreation.productCode = {};
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new Date();
autoObject.ContractCreation.valueDate = new Date();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new Date();
autoObject.Parties = {};
autoObject.Parties.Drawer = {};
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = {};
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = {};
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = {};
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = {};
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.Parties.caseInNeed = {};
autoObject.Parties.caseInNeed.partyCIF = "";
autoObject.Parties.caseInNeed.partyId = "";
autoObject.Parties.caseInNeed.partyName = "";
autoObject.Parties.caseInNeed.country = "";
autoObject.Parties.caseInNeed.language = "";
autoObject.Parties.caseInNeed.refrence = "";
autoObject.Parties.caseInNeed.address1 = "";
autoObject.Parties.caseInNeed.address2 = "";
autoObject.Parties.caseInNeed.address3 = "";
autoObject.Parties.caseInNeed.partyType = {};
autoObject.Parties.caseInNeed.partyType.name = "";
autoObject.Parties.caseInNeed.partyType.value = "";
autoObject.ChargesAndCommissions = [];
autoObject.ChargesAndCommissions[0] = {};
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = {};
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = {};
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = {};
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ChargesAndCommissions[0].isGLFound = false;
autoObject.ChargesAndCommissions[0].glVerifyMSG = "";
autoObject.ContractLiquidation = {};
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new Date();
autoObject.ContractLiquidation.creditValueDate = new Date();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.debitedAccountName = "";
autoObject.ContractLiquidation.creditedAccount = {};
autoObject.ContractLiquidation.creditedAccount.accountClass = {};
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = {};
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = {};
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = [];
autoObject.actions[0] = "";
autoObject.attachmentDetails = {};
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = {};
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = [];
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = [];
autoObject.attachmentDetails.attachment[0] = {};
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = [];
autoObject.complianceComments[0] = {};
autoObject.complianceComments[0].startTime = new Date();
autoObject.complianceComments[0].endTime = new Date();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = [];
autoObject.History[0] = {};
autoObject.History[0].startTime = new Date();
autoObject.History[0].endTime = new Date();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = {};
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject.folderPath = "";
autoObject.templateDocID = "";
autoObject.requestID = 0;
autoObject.customerAndPartyAccountList = [];
autoObject.customerAndPartyAccountList[0] = {};
autoObject.customerAndPartyAccountList[0].accountNO = "";
autoObject.customerAndPartyAccountList[0].currencyCode = "";
autoObject.customerAndPartyAccountList[0].branchCode = "";
autoObject.customerAndPartyAccountList[0].balance = 0.0;
autoObject.customerAndPartyAccountList[0].typeCode = "";
autoObject.customerAndPartyAccountList[0].customerName = "";
autoObject.customerAndPartyAccountList[0].customerNo = "";
autoObject.customerAndPartyAccountList[0].frozen = false;
autoObject.customerAndPartyAccountList[0].dormant = false;
autoObject.customerAndPartyAccountList[0].noDebit = false;
autoObject.customerAndPartyAccountList[0].noCredit = false;
autoObject.customerAndPartyAccountList[0].postingAllowed = false;
autoObject.customerAndPartyAccountList[0].ibanAccountNumber = "";
autoObject.customerAndPartyAccountList[0].accountClassCode = "";
autoObject.customerAndPartyAccountList[0].balanceType = "";
autoObject.customerAndPartyAccountList[0].accountStatus = "";
autoObject.tradeFoComment = "";
autoObject.exeHubMkrComment = "";
autoObject.compcheckerComment = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="hubCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.3db632ba-2dff-49ab-81f3-ecc3512ed61a" />
                        <ns16:dataInput name="branchCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.e60856e2-88c3-449a-8b95-682aa6f088ab">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false" />
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="branchName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.c17f6b1c-420e-4316-8602-278ac2e5f98d">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false" />
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="isFirstTime" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.262e9fc1-8a4f-4bee-81b6-3ebbcd392f6d" />
                        <ns16:dataInput name="initiator" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.df6d7638-3ff3-45a9-817e-e243fd7e7807" />
                        <ns16:dataInput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5e731f35-27b7-4ff1-852c-7479e82b9115" />
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.c6e44d18-bb22-407c-8c94-33e273a84088" />
                        <ns16:dataOutput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1ae8623e-a0d0-455e-8199-92f720530682" />
                        <ns16:dataOutput name="regeneratedRemittanceLetterTitleVIS" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.17b2fc8d-62c3-4cbc-8046-1daaee9f0658" />
                        <ns16:dataOutput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.4371b561-70e9-4ac1-898e-91948aadac07" />
                        <ns16:dataOutput name="role" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.42d6ab5b-c60b-4768-89d1-cabaa3f9dd78" />
                        <ns16:inputSet id="_b8cc2321-d8b1-4647-83c1-b912d1889625" />
                        <ns16:outputSet id="_4619ef27-6b4c-4f9d-aa0d-6c6c1269cdbd" />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2b5d1a52-9dc7-4451-86ee-a3dd2a41deed</processLinkId>
            <processId>1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.136e3662-e6aa-49a8-a4ba-e15111c65f8c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.18417b70-6cda-48e4-99fe-9db5c676caf7</toProcessItemId>
            <guid>f0783dab-91a7-461b-aac9-712ebc31712c</guid>
            <versionId>05d0495f-cc5f-4667-8b9d-ab99adef0a16</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.136e3662-e6aa-49a8-a4ba-e15111c65f8c</fromProcessItemId>
            <toProcessItemId>2025.18417b70-6cda-48e4-99fe-9db5c676caf7</toProcessItemId>
        </link>
    </process>
</teamworks>

