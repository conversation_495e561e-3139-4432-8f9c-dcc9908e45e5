{"id": "24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89", "versionId": "f8d25f3b-0b03-40df-beee-9a37fd159eaa", "name": "Trade FO Makers", "type": "participant", "typeName": "Participant", "details": {}, "_fullObjectData": {"teamworks": {"participant": {"id": "24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89", "name": "Trade FO Makers", "lastModified": "1691144750823", "lastModifiedBy": "heba", "participantId": "24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89", "participantDefinition": {"isNull": "true"}, "simulationGroupSize": "2", "capacityType": "1", "definitionType": "3", "percentAvailable": {"isNull": "true"}, "percentEfficiency": {"isNull": "true"}, "cost": "10.00", "currencyCode": {"isNull": "true"}, "image": {"isNull": "true"}, "serviceMembersRef": {"isNull": "true"}, "managersRef": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"team\":[{\"members\":{\"Users\":[{\"name\":\"heba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"abdelrahman.saleh\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"},{\"name\":\"somaia\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}],\"UserGroups\":[{\"name\":\"BPM_ODC_Trade_FO_MKR\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails\",\"type\":\"StandardMembers\"}]},\"documentation\":[{\"content\":[],\"textFormat\":\"text\\/plain\"}],\"name\":\"Trade FO Makers\",\"declaredType\":\"resource\",\"id\":\"24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89\"}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.w3.org\\/1999\\/XPath\",\"id\":\"24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89\"}", "externalId": {"isNull": "true"}, "description": "", "guid": "guid:d694a63221635d5b:6baf87c4:18969a9a6e2:7534", "versionId": "f8d25f3b-0b03-40df-beee-9a37fd159eaa", "standardMembers": {"standardMember": [{"type": "Group", "name": "BPM_ODC_Trade_FO_MKR"}, {"type": "User", "name": "heba"}, {"type": "User", "name": "abdelrahman.saleh"}, {"type": "User", "name": "so<PERSON>ia"}]}, "teamAssignments": ""}}}}