{"id": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "versionId": "f4a258c2-bb9f-42ca-ada7-e28ce1a91934", "name": "Get ODC Initiators", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "Get ODC Initiators", "lastModified": "1699519391009", "lastModifiedBy": "heba", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "2025.63dbb119-bcd4-49f5-89fc-4dcc2921fa84"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.fe313612-9b33-4c13-87ad-5285d86baac9", "2025.fe313612-9b33-4c13-87ad-5285d86baac9"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "c6478844-e599-4b93-ba63-d7db05ed0c33", "versionId": "f4a258c2-bb9f-42ca-ada7-e28ce1a91934", "dependencySummary": "<dependencySummary id=\"bpdid:651a1a6abf396537:64776e00:18baeba64af:23da\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.18e79798-a7b5-4b8a-a8fc-b693b2eaae8a\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"59b88717-b722-44e0-a890-bebb1eeb60f2\"},{\"incoming\":[\"2b442d23-db71-47f4-ab53-e7e8cc63841a\",\"67c295de-959f-45fb-b360-389cf10f5907\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":610,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-555e\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"feb8d983-4091-4d77-a2d2-5c8d9b703b34\"},{\"targetRef\":\"63dbb119-bcd4-49f5-89fc-4dcc2921fa84\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get initiator group\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.18e79798-a7b5-4b8a-a8fc-b693b2eaae8a\",\"sourceRef\":\"59b88717-b722-44e0-a890-bebb1eeb60f2\"},{\"startQuantity\":1,\"outgoing\":[\"78b58382-6346-4e2d-a0a8-d878f0636a03\"],\"incoming\":[\"2027.18e79798-a7b5-4b8a-a8fc-b693b2eaae8a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":130,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Get initiator group\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"63dbb119-bcd4-49f5-89fc-4dcc2921fa84\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.userTeams = new tw.object.listOf.String();\\r\\n\\r\\nfor(var i=0;i<tw.system.org.findUserByName(tw.local.loggedInUser).roles.listLength;i++)\\r\\n{\\r\\n\\ttw.local.userTeams[i] = tw.system.org.findUserByName(tw.local.loggedInUser).roles[i].name;\\r\\n}\\r\\nlog.info(\\\"all roles: \\\"+tw.local.userTeams);\\r\\n\\r\\nfor(i=0;i<tw.local.userTeams.listLength;i++)\\r\\n{\\r\\n\\tif(tw.local.userTeams[i] != \\\"\\\" && tw.local.userTeams[i] != null)\\r\\n\\t{\\r\\n\\t\\r\\n\\t\\r\\n\\tvar newString = tw.local.userTeams[i].substring(0,2);\\r\\n\\tif(newString == \\\"BR\\\")\\r\\n\\t{\\r\\n\\t\\r\\n\\t\\ttw.local.initiator = tw.epv.userRole.branch;\\r\\n\\t\\ttw.local.branchCode = tw.local.userTeams[i];\\r\\n\\t\\ttw.local.branchSeq = tw.local.userTeams[i].substring(2,5);\\r\\n\\t\\tlog.info(\\\"Branch Code#: \\\"+tw.local.branchCode);\\r\\n\\t\\tlog.info(\\\"Branch seq#: \\\"+tw.local.branchSeq);\\r\\n\\t\\tbreak;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\r\\n\\t\\t\\r\\n\\t\\ttw.local.initiator = tw.epv.userRole.hub;\\r\\n\\t\\tvar splittedArray = tw.local.userTeams[i].split('_',4);\\r\\n\\t\\tif(splittedArray[0] == \\\"BPM\\\" && (splittedArray[1] == \\\"ODC\\\" || splittedArray[1] == \\\"IDC\\\" )&& splittedArray[2] == \\\"HUB\\\")\\r\\n\\t\\t{\\r\\n\\t\\t\\ttw.local.hubCode = splittedArray[3];\\r\\n\\t\\t\\tlog.info(\\\"split array #: \\\"+splittedArray);\\r\\n\\t\\t\\tbreak;\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\t}\\r\\n}\\r\\n\\r\\n\\r\\n\\/\\/tw.system.org.findUserByName(tw.system.user_loginName).roles\"]}},{\"targetRef\":\"4082a5f8-e4fb-405f-8f86-8ef4d9f304a3\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To is Branch?\",\"declaredType\":\"sequenceFlow\",\"id\":\"78b58382-6346-4e2d-a0a8-d878f0636a03\",\"sourceRef\":\"63dbb119-bcd4-49f5-89fc-4dcc2921fa84\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"userTeams\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.6ff9c4f6-29c1-4149-a414-b72cfb3aa0a9\"},{\"outgoing\":[\"bfb5040f-7412-4fe4-8d92-ce779e6aaadd\",\"7e4d08ca-c1d5-4365-a897-1ef6db46b5c3\"],\"incoming\":[\"78b58382-6346-4e2d-a0a8-d878f0636a03\"],\"default\":\"7e4d08ca-c1d5-4365-a897-1ef6db46b5c3\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":338,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"is Branch?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"4082a5f8-e4fb-405f-8f86-8ef4d9f304a3\"},{\"targetRef\":\"31b1cc7b-1684-4bec-875a-6591c4110d93\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.branchCode != \\\"\\\" && tw.local.branchCode != null\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"bfb5040f-7412-4fe4-8d92-ce779e6aaadd\",\"sourceRef\":\"4082a5f8-e4fb-405f-8f86-8ef4d9f304a3\"},{\"startQuantity\":1,\"outgoing\":[\"67c295de-959f-45fb-b360-389cf10f5907\"],\"incoming\":[\"bfb5040f-7412-4fe4-8d92-ce779e6aaadd\"],\"extensionElements\":{\"postAssignmentScript\":[\"tw.local.branchName = tw.local.BranchNameValue.name;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":442,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Get Branch Name\",\"dataInputAssociation\":[{\"targetRef\":\"2055.264287fd-743d-4580-9bb3-4f10fd434c0e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"BPM.NBE_SME_LG_BRANCHES\\\"\"]}}]},{\"targetRef\":\"2055.402fde1a-d77f-4ba9-a7df-945c4617e3b0\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.branchSeq\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"31b1cc7b-1684-4bec-875a-6591c4110d93\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.BranchNameValue\"]}}],\"sourceRef\":[\"2055.64f4ba7a-4d36-4697-830d-c1951a7875bd\"]}],\"calledElement\":\"1.a32c22ed-dfe4-4915-9281-b9c0c9d67d0a\"},{\"targetRef\":\"feb8d983-4091-4d77-a2d2-5c8d9b703b34\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:e30c377f7882db6a:324bd248:186d6576310:-2b5a\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"67c295de-959f-45fb-b360-389cf10f5907\",\"sourceRef\":\"31b1cc7b-1684-4bec-875a-6591c4110d93\"},{\"startQuantity\":1,\"outgoing\":[\"2b442d23-db71-47f4-ab53-e7e8cc63841a\"],\"incoming\":[\"7e4d08ca-c1d5-4365-a897-1ef6db46b5c3\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":442,\"y\":184,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[],\"activityType\":[\"CalledProcess\"]},\"name\":\"Get List of Hubs\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e35a516a-98ca-4df7-9910-13acf06e8ec1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.hubCode\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"6efc1691-399a-4e59-8865-f7580738ce70\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.hubName\"]}}],\"sourceRef\":[\"2055.61031ca4-05c6-4f29-8bef-4b9f624e36b7\"]}],\"calledElement\":\"1.09d158a9-e4e4-4987-827f-4a4923c76843\"},{\"targetRef\":\"6efc1691-399a-4e59-8865-f7580738ce70\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"7e4d08ca-c1d5-4365-a897-1ef6db46b5c3\",\"sourceRef\":\"4082a5f8-e4fb-405f-8f86-8ef4d9f304a3\"},{\"targetRef\":\"feb8d983-4091-4d77-a2d2-5c8d9b703b34\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-1434\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Get Hub by code\",\"declaredType\":\"sequenceFlow\",\"id\":\"2b442d23-db71-47f4-ab53-e7e8cc63841a\",\"sourceRef\":\"6efc1691-399a-4e59-8865-f7580738ce70\"},{\"itemSubjectRef\":\"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"name\":\"BranchNameValue\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.140aa34e-c967-41b6-9ad4-3ef4da33798d\"},{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"listofHubs\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.9409ac42-0efe-489c-848a-210f24184128\"},{\"parallelMultiple\":false,\"outgoing\":[\"c5fa2031-e4dc-4945-810b-56eda5391dbd\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"83424229-acba-4b7f-83f1-7b981ec464a3\",\"otherAttributes\":{\"eventImplId\":\"c58c9271-b0cf-48d7-8682-bd879835093c\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":708,\"y\":234,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"ee5e1181-4351-406a-87ce-e7d508f47218\"},{\"startQuantity\":1,\"outgoing\":[\"6cd1babe-54c6-41be-81e5-bf33e3c99a2d\"],\"incoming\":[\"c5fa2031-e4dc-4945-810b-56eda5391dbd\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":840,\"y\":211,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Linked Service Flow\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Get ODC Initiators\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"fe313612-9b33-4c13-87ad-5285d86baac9\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"fe313612-9b33-4c13-87ad-5285d86baac9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Linked Service Flow\",\"declaredType\":\"sequenceFlow\",\"id\":\"c5fa2031-e4dc-4945-810b-56eda5391dbd\",\"sourceRef\":\"ee5e1181-4351-406a-87ce-e7d508f47218\"},{\"incoming\":[\"6cd1babe-54c6-41be-81e5-bf33e3c99a2d\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"798d89c9-71b2-4f5b-8d5f-808a2b44802f\",\"otherAttributes\":{\"eventImplId\":\"2ea49891-4b73-429d-8e10-c817b659c126\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":983,\"y\":234,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"bfb76acf-630e-48d5-8629-eed50b637bbc\"},{\"targetRef\":\"bfb76acf-630e-48d5-8629-eed50b637bbc\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"6cd1babe-54c6-41be-81e5-bf33e3c99a2d\",\"sourceRef\":\"fe313612-9b33-4c13-87ad-5285d86baac9\"}],\"laneSet\":[{\"id\":\"df8b3c2c-d02d-4fb7-90fa-568fbeeed889\",\"lane\":[{\"flowNodeRef\":[\"59b88717-b722-44e0-a890-bebb1eeb60f2\",\"feb8d983-4091-4d77-a2d2-5c8d9b703b34\",\"63dbb119-bcd4-49f5-89fc-4dcc2921fa84\",\"4082a5f8-e4fb-405f-8f86-8ef4d9f304a3\",\"31b1cc7b-1684-4bec-875a-6591c4110d93\",\"6efc1691-399a-4e59-8865-f7580738ce70\",\"ee5e1181-4351-406a-87ce-e7d508f47218\",\"fe313612-9b33-4c13-87ad-5285d86baac9\",\"bfb76acf-630e-48d5-8629-eed50b637bbc\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"47af6768-fba9-4a1a-8952-77b69be47876\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get ODC Initiators\",\"declaredType\":\"process\",\"id\":\"1.76de764f-7233-4b6f-8130-202e1d0049ac\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"hubCode\",\"isCollection\":false,\"id\":\"2055.a17679eb-ad20-4778-b1c1-06b7dc04228f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"branchCode\",\"isCollection\":false,\"id\":\"2055.4e8f9aa5-7f46-45cc-b0ee-47bfd2137cde\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"branchName\",\"isCollection\":false,\"id\":\"2055.ea1a2239-6be8-4443-a29e-1a1d65d70dda\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"hubName\",\"isCollection\":false,\"id\":\"2055.c9bdd6b8-1830-4f27-8a5f-dafc6532eb02\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"branchSeq\",\"isCollection\":false,\"id\":\"2055.8b261e83-fd81-4f5c-aee4-4011dfc21d9f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"initiator\",\"isCollection\":false,\"id\":\"2055.194ea2a4-0d33-4fd5-8ddb-eb734d149fdb\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.d0ef6455-5fd6-4815-82bc-b93bd42e3532\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"id\":\"2055.6cab4049-7518-47ae-8460-bd5b20f589c3\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"1078c7cc-6381-451c-812e-5de5eee70418\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.e791ee53-6e73-4c61-a509-f183adf32d40\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.a17679eb-ad20-4778-b1c1-06b7dc04228f\",\"2055.4e8f9aa5-7f46-45cc-b0ee-47bfd2137cde\",\"2055.ea1a2239-6be8-4443-a29e-1a1d65d70dda\",\"2055.c9bdd6b8-1830-4f27-8a5f-dafc6532eb02\",\"2055.8b261e83-fd81-4f5c-aee4-4011dfc21d9f\",\"2055.194ea2a4-0d33-4fd5-8ddb-eb734d149fdb\",\"2055.d0ef6455-5fd6-4815-82bc-b93bd42e3532\",\"2055.6cab4049-7518-47ae-8460-bd5b20f589c3\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\/\\/\\\"odcbranchmkr\\\"\\r\\n\\\"idchubexemkr07\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"loggedInUser\",\"isCollection\":false,\"id\":\"2055.e791ee53-6e73-4c61-a509-f183adf32d40\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "loggedInUser", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e791ee53-6e73-4c61-a509-f183adf32d40", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "//\"odcbranchmkr\"\r\r\n\"idchubexemkr07\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "15003b63-d15b-4bfc-9795-c7877e3f2e63", "versionId": "79e10e78-25f2-4220-855c-0bf5867ce799"}, {"name": "hubCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a17679eb-ad20-4778-b1c1-06b7dc04228f", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "257b1dd3-ee3c-44e8-965f-3ad82c17a2ea", "versionId": "c8e1beb8-9c29-4739-b60c-8bee74c5d859"}, {"name": "branchCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4e8f9aa5-7f46-45cc-b0ee-47bfd2137cde", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "0a70bb72-6729-4929-abca-a7d5a50194ca", "versionId": "797173eb-e450-4fcd-9d52-fd7a3b26e779"}, {"name": "branchName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ea1a2239-6be8-4443-a29e-1a1d65d70dda", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7bb07ecf-0457-41a5-a0cc-61f7cbca11ad", "versionId": "64153822-1bf2-413a-b198-de0ab<PERSON>deb9d"}, {"name": "hubName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.c9bdd6b8-1830-4f27-8a5f-dafc6532eb02", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "6f820953-06aa-47df-9e2e-e131221167a5", "versionId": "f885f50f-d2d6-4440-9673-2bde51df7025"}, {"name": "branchSeq", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.8b261e83-fd81-4f5c-aee4-4011dfc21d9f", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "990333bc-e631-4d1c-92cd-3f7220fe2a3b", "versionId": "055f972e-8607-48fe-806d-6aff81ecaaeb"}, {"name": "initiator", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.194ea2a4-0d33-4fd5-8ddb-eb734d149fdb", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "7", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "e9aaaa30-6904-443f-a3bc-1f410c8e2dcc", "versionId": "ce2547f0-d697-4d56-8762-003616e8ec78"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.705ec081-25e4-4c27-b500-cd18a3989267", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "3", "isArrayOf": "false", "classId": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3709ad6e-cb7b-43d9-975a-fe5955a867b7", "versionId": "ca162d6c-1b0a-44b9-9624-87965a64ced6"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d0ef6455-5fd6-4815-82bc-b93bd42e3532", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "0fa6aba8-a673-4522-85f2-cfdb84e5b51c", "versionId": "f2005d68-b397-4723-88e0-5479ceacade1"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6cab4049-7518-47ae-8460-bd5b20f589c3", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "9", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3a00d4d5-9385-4951-9f84-34f7af3c9c19", "versionId": "df526597-3cb8-47b8-bfa9-c5a726f8ce60"}], "processVariable": [{"name": "userTeams", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6ff9c4f6-29c1-4149-a414-b72cfb3aa0a9", "description": {"isNull": "true"}, "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bba398d5-cd43-449a-9c47-c55b3ef4ceb1", "versionId": "89e621b4-80c2-423a-ad38-422ad9cb948a"}, {"name": "BranchNameValue", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.140aa34e-c967-41b6-9ad4-3ef4da33798d", "description": {"isNull": "true"}, "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d21cfe8a-afef-4edc-886a-1334f46f637e", "versionId": "8e951e50-9904-4bd5-8619-d11e806be414"}, {"name": "listofHubs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9409ac42-0efe-489c-848a-210f24184128", "description": {"isNull": "true"}, "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c11b1c04-a29f-4058-b061-d61efbabfd0b", "versionId": "d20b53be-38c2-4261-aa02-02f7208be3c2"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6efc1691-399a-4e59-8865-f7580738ce70", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "Get List of Hubs", "tWComponentName": "SubProcess", "tWComponentId": "3012.9d6dc00a-b508-4de4-8f07-cae20741ab24", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5560", "versionId": "0dd7a559-1be7-4625-9b09-e2b31559a86c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.05abd9eb-2c8a-4e7e-a7bc-53b648b04fc8", "processItemId": "2025.6efc1691-399a-4e59-8865-f7580738ce70", "location": "1", "script": {"isNull": "true"}, "guid": "91db0702-f3c1-4225-9327-dd54ddcc3b85", "versionId": "d5985c8c-138c-463d-bec1-a57ace7b224a"}, "layoutData": {"x": "442", "y": "184", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.9d6dc00a-b508-4de4-8f07-cae20741ab24", "attachedProcessRef": "/1.09d158a9-e4e4-4987-827f-4a4923c76843", "guid": "0f5c915d-6944-49a9-9886-f976db18e915", "versionId": "59ee7fc8-f62c-4ebf-aaa5-68b5d017f807", "parameterMapping": [{"name": "hubName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6288dfce-74a9-4df4-a449-063a0e0a68f6", "processParameterId": "2055.61031ca4-05c6-4f29-8bef-4b9f624e36b7", "parameterMappingParentId": "3012.9d6dc00a-b508-4de4-8f07-cae20741ab24", "useDefault": "false", "value": "tw.local.hubName", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "564a2fa5-8d2a-4f9b-bfac-1a5d93072a97", "versionId": "e129dd4b-57c5-4697-b2ef-37adc4b7c17f", "description": {"isNull": "true"}}, {"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.dc1b5960-b8c7-48ef-8980-b65e950f5de8", "processParameterId": "2055.e35a516a-98ca-4df7-9910-13acf06e8ec1", "parameterMappingParentId": "3012.9d6dc00a-b508-4de4-8f07-cae20741ab24", "useDefault": "false", "value": "tw.local.hubCode", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isList": "false", "isInput": "true", "guid": "5de9a48e-3f27-4d9a-ab9f-c5d5f388e8a8", "versionId": "f69d4f19-386c-4338-bd04-be01266018cb", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bfb76acf-630e-48d5-8629-eed50b637bbc", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.9547df92-254d-4e0f-affa-4abbd3dbbcfb", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:651a1a6abf396537:64776e00:18baeba64af:22cc", "versionId": "0f110edc-7a24-4d16-bde4-70177ef3b2af", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "983", "y": "234", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.9547df92-254d-4e0f-affa-4abbd3dbbcfb", "message": "", "faultStyle": "1", "guid": "3a63b195-91ed-48fb-a72c-5d8e0517c32b", "versionId": "ad905f94-a54c-40e6-b8ad-0d965dd4315f", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.cba9b212-ca76-4f6b-be6f-44ff8591b55a", "processParameterId": "2055.705ec081-25e4-4c27-b500-cd18a3989267", "parameterMappingParentId": "3007.9547df92-254d-4e0f-affa-4abbd3dbbcfb", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "isList": "false", "isInput": "false", "guid": "9ba56be2-c872-4541-aab1-a52b27d5c638", "versionId": "52dbda84-3c18-4b80-a011-68dfa5f739c7", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "is <PERSON>?", "tWComponentName": "Switch", "tWComponentId": "3013.c37505db-1506-4e63-8226-520e0299c593", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5561", "versionId": "3afd335d-5865-4c03-8821-184ebf602b9c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "338", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.c37505db-1506-4e63-8226-520e0299c593", "guid": "76f46713-393f-439f-8de1-9ebc8c66cf90", "versionId": "eb5916c2-939b-48a2-9889-5d7884ce8593", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.84a56d46-1eb0-4ae7-9aa9-d63222e8765e", "switchId": "3013.c37505db-1506-4e63-8226-520e0299c593", "seq": "1", "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:23d9", "condition": "tw.local.branchCode != \"\" && tw.local.branchCode != null", "guid": "8a26c92b-58b8-4cd8-b579-ceb47f22fe44", "versionId": "6f583936-a23c-484e-92d6-3ae9497b6eea"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.feb8d983-4091-4d77-a2d2-5c8d9b703b34", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.f23f43fa-7b41-4456-9d23-291ad8f93b6a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-555e", "versionId": "3f0da131-d70b-4595-9131-d7df6d423774", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "610", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.f23f43fa-7b41-4456-9d23-291ad8f93b6a", "haltProcess": "false", "guid": "e8dbe16f-8e60-40f3-967f-cfde2695d983", "versionId": "13bd8075-39e1-499d-a8ec-6cbed4456aed"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "Get initiator group", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.0c72003e-0fbb-4166-9e9b-a21162e9e0be", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-5562", "versionId": "887420fa-5e4a-43ee-879b-b391f3354b6d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.edc1b425-d367-4513-b81f-e4367d9eaf5c", "processItemId": "2025.63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "location": "1", "script": {"isNull": "true"}, "guid": "b0248b5f-0859-41af-b27a-cffea2307fae", "versionId": "ba6ca852-a30c-46fa-ab07-cb49b38abeee"}, "layoutData": {"x": "130", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.0c72003e-0fbb-4166-9e9b-a21162e9e0be", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.userTeams = new tw.object.listOf.String();\r\r\n\r\r\nfor(var i=0;i<tw.system.org.findUserByName(tw.local.loggedInUser).roles.listLength;i++)\r\r\n{\r\r\n\ttw.local.userTeams[i] = tw.system.org.findUserByName(tw.local.loggedInUser).roles[i].name;\r\r\n}\r\r\nlog.info(\"all roles: \"+tw.local.userTeams);\r\r\n\r\r\nfor(i=0;i<tw.local.userTeams.listLength;i++)\r\r\n{\r\r\n\tif(tw.local.userTeams[i] != \"\" && tw.local.userTeams[i] != null)\r\r\n\t{\r\r\n\t\r\r\n\t\r\r\n\tvar newString = tw.local.userTeams[i].substring(0,2);\r\r\n\tif(newString == \"BR\")\r\r\n\t{\r\r\n\t\r\r\n\t\ttw.local.initiator = tw.epv.userRole.branch;\r\r\n\t\ttw.local.branchCode = tw.local.userTeams[i];\r\r\n\t\ttw.local.branchSeq = tw.local.userTeams[i].substring(2,5);\r\r\n\t\tlog.info(\"Branch Code#: \"+tw.local.branchCode);\r\r\n\t\tlog.info(\"Branch seq#: \"+tw.local.branchSeq);\r\r\n\t\tbreak;\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\t\r\r\n\t\ttw.local.initiator = tw.epv.userRole.hub;\r\r\n\t\tvar splittedArray = tw.local.userTeams[i].split('_',4);\r\r\n\t\tif(splittedArray[0] == \"BPM\" && (splittedArray[1] == \"ODC\" || splittedArray[1] == \"IDC\" )&& splittedArray[2] == \"HUB\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.hubCode = splittedArray[3];\r\r\n\t\t\tlog.info(\"split array #: \"+splittedArray);\r\r\n\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n//tw.system.org.findUserByName(tw.system.user_loginName).roles", "isRule": "false", "guid": "132b0b20-a9fe-4784-9ff6-8731272b35fd", "versionId": "ecbe61d0-05a5-4531-be23-f44be506e652"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.31b1cc7b-1684-4bec-875a-6591c4110d93", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "Get Branch Name", "tWComponentName": "SubProcess", "tWComponentId": "3012.1e751711-ff6d-429b-89e0-c994d08225b7", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-555f", "versionId": "ecc736cc-2df6-4a82-9564-19fc1ff6ac93", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.65a74535-e24b-406b-98e3-3793cab08847", "processItemId": "2025.31b1cc7b-1684-4bec-875a-6591c4110d93", "location": "2", "script": "tw.local.branchName = tw.local.BranchNameValue.name;", "guid": "93af0815-bc4f-4957-ad8e-ea5b2d890cfd", "versionId": "a4e2e5a4-0cc1-43b2-a401-f469bfa84485"}, "layoutData": {"x": "442", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.1e751711-ff6d-429b-89e0-c994d08225b7", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.a32c22ed-dfe4-4915-9281-b9c0c9d67d0a", "guid": "c4dc5bb9-fc40-4161-aa8a-bcc1fc3bc60b", "versionId": "cbd315a2-b240-4780-b908-b208293a8673", "parameterMapping": [{"name": "<PERSON><PERSON><PERSON>", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.10315f16-4d73-4451-bc38-3e6e411dc1f0", "processParameterId": "2055.64f4ba7a-4d36-4697-830d-c1951a7875bd", "parameterMappingParentId": "3012.1e751711-ff6d-429b-89e0-c994d08225b7", "useDefault": "false", "value": "tw.local.BranchNameValue", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "isList": "false", "isInput": "false", "guid": "c00e7107-4861-45f5-b9d0-0decfa4ce086", "versionId": "3869a90c-0d8c-4b8e-95de-1f15c1f294d3", "description": {"isNull": "true"}}, {"name": "tableName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b4530f22-7227-41df-99e5-00c3e36c9fb2", "processParameterId": "2055.264287fd-743d-4580-9bb3-4f10fd434c0e", "parameterMappingParentId": "3012.1e751711-ff6d-429b-89e0-c994d08225b7", "useDefault": "false", "value": "\"BPM.NBE_SME_LG_BRANCHES\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "5f0149fe-4bc7-4fd9-b973-feb231cff86a", "versionId": "c7190b59-070d-4fdb-a3e8-27a6d5071f9e", "description": {"isNull": "true"}}, {"name": "code", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f695034e-cebc-4301-9d0a-4621f3e54d50", "processParameterId": "2055.402fde1a-d77f-4ba9-a7df-945c4617e3b0", "parameterMappingParentId": "3012.1e751711-ff6d-429b-89e0-c994d08225b7", "useDefault": "false", "value": "tw.local.branchSeq", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "1a545657-d161-4f01-bae4-042c343e0d63", "versionId": "e69691dd-f871-4878-9d44-bbe770dc3531", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fe313612-9b33-4c13-87ad-5285d86baac9", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "name": "Linked Service Flow", "tWComponentName": "SubProcess", "tWComponentId": "3012.c7c876e2-c0c4-42b8-b28c-636cd8eba1fc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:651a1a6abf396537:64776e00:18baeba64af:22ca", "versionId": "f1b76208-3aaf-4a2a-ab3c-8fab83788da2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "840", "y": "211", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.c7c876e2-c0c4-42b8-b28c-636cd8eba1fc", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "5e8b267d-19bc-47ec-b22c-730d7aa953c5", "versionId": "560124af-55ff-498f-b984-b67b70f36211", "parameterMapping": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0a4ed7bf-59e0-426e-bf39-0bf7ffce0b55", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.c7c876e2-c0c4-42b8-b28c-636cd8eba1fc", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "4dded8cd-2889-4b47-a7fe-d81b00f201de", "versionId": "01ca6501-5ae2-4889-b394-216da9462b58", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0ec2cb74-a661-423f-b1ad-b7f22a7b14cf", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.c7c876e2-c0c4-42b8-b28c-636cd8eba1fc", "useDefault": "false", "value": "\"Get ODC Initiators\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "f293fa52-85b2-4d07-b669-c81135029a9a", "versionId": "5ddfa7c1-2bdf-4e82-90a7-9c3e8716122d", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6bd8bcc3-0e6a-48ac-bc52-04857742386e", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.c7c876e2-c0c4-42b8-b28c-636cd8eba1fc", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "7e4d2be3-1a1d-4137-8de0-ec9c39a1d07e", "versionId": "d5e64166-5c6e-46d7-bb53-abff4c828973", "description": {"isNull": "true"}}]}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.58236559-46c4-4d77-9d87-fb8890b8ad28", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "guid": "67f51e1d-de5f-4dd7-ba4b-da52af3d38fc", "versionId": "7113a66b-4caf-484e-a9bc-f6861cea2951"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "708", "y": "234", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get ODC Initiators", "id": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:localizationResourceLinks": "", "ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "1078c7cc-6381-451c-812e-5de5eee70418"}}}, "ns16:dataInput": {"name": "loggedInUser", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.e791ee53-6e73-4c61-a509-f183adf32d40", "ns16:extensionElements": {"ns3:defaultValue": {"_": "//\"odcbranchmkr\"\r\r\n\"idchubexemkr07\"", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "hubCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a17679eb-ad20-4778-b1c1-06b7dc04228f"}, {"name": "branchCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.4e8f9aa5-7f46-45cc-b0ee-47bfd2137cde"}, {"name": "branchName", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.ea1a2239-6be8-4443-a29e-1a1d65d70dda"}, {"name": "hubName", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.c9bdd6b8-1830-4f27-8a5f-dafc6532eb02"}, {"name": "branchSeq", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.8b261e83-fd81-4f5c-aee4-4011dfc21d9f"}, {"name": "initiator", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.194ea2a4-0d33-4fd5-8ddb-eb734d149fdb"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.d0ef6455-5fd6-4815-82bc-b93bd42e3532"}, {"name": "isSuccessful", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.6cab4049-7518-47ae-8460-bd5b20f589c3"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.e791ee53-6e73-4c61-a509-f183adf32d40"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.a17679eb-ad20-4778-b1c1-06b7dc04228f", "2055.4e8f9aa5-7f46-45cc-b0ee-47bfd2137cde", "2055.ea1a2239-6be8-4443-a29e-1a1d65d70dda", "2055.c9bdd6b8-1830-4f27-8a5f-dafc6532eb02", "2055.8b261e83-fd81-4f5c-aee4-4011dfc21d9f", "2055.194ea2a4-0d33-4fd5-8ddb-eb734d149fdb", "2055.d0ef6455-5fd6-4815-82bc-b93bd42e3532", "2055.6cab4049-7518-47ae-8460-bd5b20f589c3"]}}, "ns16:laneSet": {"id": "df8b3c2c-d02d-4fb7-90fa-568fbeeed889", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "47af6768-fba9-4a1a-8952-77b69be47876", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["59b88717-b722-44e0-a890-bebb1eeb60f2", "feb8d983-4091-4d77-a2d2-5c8d9b703b34", "63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "31b1cc7b-1684-4bec-875a-6591c4110d93", "6efc1691-399a-4e59-8865-f7580738ce70", "ee5e1181-4351-406a-87ce-e7d508f47218", "fe313612-9b33-4c13-87ad-5285d86baac9", "bfb76acf-630e-48d5-8629-eed50b637bbc"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "59b88717-b722-44e0-a890-bebb1eeb60f2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.18e79798-a7b5-4b8a-a8fc-b693b2eaae8a"}, "ns16:endEvent": [{"name": "End", "id": "feb8d983-4091-4d77-a2d2-5c8d9b703b34", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "610", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-555e"}, "ns16:incoming": ["2b442d23-db71-47f4-ab53-e7e8cc63841a", "67c295de-959f-45fb-b360-389cf10f5907"]}, {"name": "End Event", "id": "bfb76acf-630e-48d5-8629-eed50b637bbc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "983", "y": "234", "width": "24", "height": "24"}}, "ns16:incoming": "6cd1babe-54c6-41be-81e5-bf33e3c99a2d", "ns16:errorEventDefinition": {"id": "798d89c9-71b2-4f5b-8d5f-808a2b44802f", "eventImplId": "2ea49891-4b73-429d-8e10-c817b659c126", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "59b88717-b722-44e0-a890-bebb1eeb60f2", "targetRef": "63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "name": "To Get initiator group", "id": "2027.18e79798-a7b5-4b8a-a8fc-b693b2eaae8a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "targetRef": "4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "name": "To is Branch?", "id": "78b58382-6346-4e2d-a0a8-d878f0636a03", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "targetRef": "31b1cc7b-1684-4bec-875a-6591c4110d93", "name": "Yes", "id": "bfb5040f-7412-4fe4-8d92-ce779e6aaadd", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.branchCode != \"\" && tw.local.branchCode != null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "31b1cc7b-1684-4bec-875a-6591c4110d93", "targetRef": "feb8d983-4091-4d77-a2d2-5c8d9b703b34", "name": "To End", "id": "67c295de-959f-45fb-b360-389cf10f5907", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:e30c377f7882db6a:324bd248:186d6576310:-2b5a"}}, {"sourceRef": "4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "targetRef": "6efc1691-399a-4e59-8865-f7580738ce70", "name": "No", "id": "7e4d08ca-c1d5-4365-a897-1ef6db46b5c3", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "6efc1691-399a-4e59-8865-f7580738ce70", "targetRef": "feb8d983-4091-4d77-a2d2-5c8d9b703b34", "name": "To Get Hub by code", "id": "2b442d23-db71-47f4-ab53-e7e8cc63841a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-1434"}}, {"sourceRef": "ee5e1181-4351-406a-87ce-e7d508f47218", "targetRef": "fe313612-9b33-4c13-87ad-5285d86baac9", "name": "To Linked Service Flow", "id": "c5fa2031-e4dc-4945-810b-56eda5391dbd", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fe313612-9b33-4c13-87ad-5285d86baac9", "targetRef": "bfb76acf-630e-48d5-8629-eed50b637bbc", "name": "To End Event", "id": "6cd1babe-54c6-41be-81e5-bf33e3c99a2d", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Get initiator group", "id": "63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "130", "y": "57", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.18e79798-a7b5-4b8a-a8fc-b693b2eaae8a", "ns16:outgoing": "78b58382-6346-4e2d-a0a8-d878f0636a03", "ns16:script": "tw.local.userTeams = new tw.object.listOf.String();\r\r\n\r\r\nfor(var i=0;i<tw.system.org.findUserByName(tw.local.loggedInUser).roles.listLength;i++)\r\r\n{\r\r\n\ttw.local.userTeams[i] = tw.system.org.findUserByName(tw.local.loggedInUser).roles[i].name;\r\r\n}\r\r\nlog.info(\"all roles: \"+tw.local.userTeams);\r\r\n\r\r\nfor(i=0;i<tw.local.userTeams.listLength;i++)\r\r\n{\r\r\n\tif(tw.local.userTeams[i] != \"\" && tw.local.userTeams[i] != null)\r\r\n\t{\r\r\n\t\r\r\n\t\r\r\n\tvar newString = tw.local.userTeams[i].substring(0,2);\r\r\n\tif(newString == \"BR\")\r\r\n\t{\r\r\n\t\r\r\n\t\ttw.local.initiator = tw.epv.userRole.branch;\r\r\n\t\ttw.local.branchCode = tw.local.userTeams[i];\r\r\n\t\ttw.local.branchSeq = tw.local.userTeams[i].substring(2,5);\r\r\n\t\tlog.info(\"Branch Code#: \"+tw.local.branchCode);\r\r\n\t\tlog.info(\"Branch seq#: \"+tw.local.branchSeq);\r\r\n\t\tbreak;\r\r\n\t}\r\r\n\telse\r\r\n\t{\r\r\n\t\t\r\r\n\t\ttw.local.initiator = tw.epv.userRole.hub;\r\r\n\t\tvar splittedArray = tw.local.userTeams[i].split('_',4);\r\r\n\t\tif(splittedArray[0] == \"BPM\" && (splittedArray[1] == \"ODC\" || splittedArray[1] == \"IDC\" )&& splittedArray[2] == \"HUB\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.hubCode = splittedArray[3];\r\r\n\t\t\tlog.info(\"split array #: \"+splittedArray);\r\r\n\t\t\tbreak;\r\r\n\t\t}\r\r\n\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n\r\r\n//tw.system.org.findUserByName(tw.system.user_loginName).roles"}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "userTeams", "id": "2056.6ff9c4f6-29c1-4149-a414-b72cfb3aa0a9"}, {"itemSubjectRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "isCollection": "false", "name": "BranchNameValue", "id": "2056.140aa34e-c967-41b6-9ad4-3ef4da33798d"}, {"itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "true", "name": "listofHubs", "id": "2056.9409ac42-0efe-489c-848a-210f24184128"}], "ns16:exclusiveGateway": {"default": "7e4d08ca-c1d5-4365-a897-1ef6db46b5c3", "name": "is <PERSON>?", "id": "4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "338", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "78b58382-6346-4e2d-a0a8-d878f0636a03", "ns16:outgoing": ["bfb5040f-7412-4fe4-8d92-ce779e6aaadd", "7e4d08ca-c1d5-4365-a897-1ef6db46b5c3"]}, "ns16:callActivity": [{"calledElement": "1.a32c22ed-dfe4-4915-9281-b9c0c9d67d0a", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Get Branch Name", "id": "31b1cc7b-1684-4bec-875a-6591c4110d93", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "442", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:postAssignmentScript": "tw.local.branchName = tw.local.BranchNameValue.name;"}, "ns16:incoming": "bfb5040f-7412-4fe4-8d92-ce779e6aaadd", "ns16:outgoing": "67c295de-959f-45fb-b360-389cf10f5907", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.264287fd-743d-4580-9bb3-4f10fd434c0e", "ns16:assignment": {"ns16:from": {"_": "\"BPM.NBE_SME_LG_BRANCHES\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.402fde1a-d77f-4ba9-a7df-945c4617e3b0", "ns16:assignment": {"ns16:from": {"_": "tw.local.branchSeq", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.64f4ba7a-4d36-4697-830d-c1951a7875bd", "ns16:assignment": {"ns16:to": {"_": "tw.local.BranchNameValue", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13"}}}}, {"calledElement": "1.09d158a9-e4e4-4987-827f-4a4923c76843", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Get List of Hubs", "id": "6efc1691-399a-4e59-8865-f7580738ce70", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "442", "y": "184", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:preAssignmentScript": ""}, "ns16:incoming": "7e4d08ca-c1d5-4365-a897-1ef6db46b5c3", "ns16:outgoing": "2b442d23-db71-47f4-ab53-e7e8cc63841a", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.e35a516a-98ca-4df7-9910-13acf06e8ec1", "ns16:assignment": {"ns16:from": {"_": "tw.local.hubCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.61031ca4-05c6-4f29-8bef-4b9f624e36b7", "ns16:assignment": {"ns16:to": {"_": "tw.local.hubName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "name": "Linked Service Flow", "id": "fe313612-9b33-4c13-87ad-5285d86baac9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "840", "y": "211", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "c5fa2031-e4dc-4945-810b-56eda5391dbd", "ns16:outgoing": "6cd1babe-54c6-41be-81e5-bf33e3c99a2d", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Get ODC Initiators\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}]}], "ns16:intermediateCatchEvent": {"name": "Error Event", "id": "ee5e1181-4351-406a-87ce-e7d508f47218", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "708", "y": "234", "width": "24", "height": "24"}}, "ns16:outgoing": "c5fa2031-e4dc-4945-810b-56eda5391dbd", "ns16:errorEventDefinition": {"id": "83424229-acba-4b7f-83f1-7b981ec464a3", "eventImplId": "c58c9271-b0cf-48d7-8682-bd879835093c", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": [{"name": "To is Branch?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.78b58382-6346-4e2d-a0a8-d878f0636a03", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.63dbb119-bcd4-49f5-89fc-4dcc2921fa84", "2025.63dbb119-bcd4-49f5-89fc-4dcc2921fa84"], "endStateId": "Out", "toProcessItemId": ["2025.4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "2025.4082a5f8-e4fb-405f-8f86-8ef4d9f304a3"], "guid": "9a2b7c24-3dda-49fc-bd83-a00d419b2ae4", "versionId": "120c3d45-7a0d-4f81-8e61-a640385de01d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.67c295de-959f-45fb-b360-389cf10f5907", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.31b1cc7b-1684-4bec-875a-6591c4110d93", "2025.31b1cc7b-1684-4bec-875a-6591c4110d93"], "endStateId": "guid:e30c377f7882db6a:324bd248:186d6576310:-2b5a", "toProcessItemId": ["2025.feb8d983-4091-4d77-a2d2-5c8d9b703b34", "2025.feb8d983-4091-4d77-a2d2-5c8d9b703b34"], "guid": "9885fc40-9b0d-47e7-9427-70f8a9d011c4", "versionId": "3de5ef45-6e69-4a63-9d2a-01263a0195d2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get Hub by code", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.2b442d23-db71-47f4-ab53-e7e8cc63841a", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.6efc1691-399a-4e59-8865-f7580738ce70", "2025.6efc1691-399a-4e59-8865-f7580738ce70"], "endStateId": "guid:d7a662a99ec79f4c:-60dc945f:189c1d215cf:-1434", "toProcessItemId": ["2025.feb8d983-4091-4d77-a2d2-5c8d9b703b34", "2025.feb8d983-4091-4d77-a2d2-5c8d9b703b34"], "guid": "bd365f38-a781-495b-b4f3-a11a568d8cf0", "versionId": "4918afe0-bc72-43d0-ba86-c9c6f94ff9a0", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "No", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7e4d08ca-c1d5-4365-a897-1ef6db46b5c3", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "2025.4082a5f8-e4fb-405f-8f86-8ef4d9f304a3"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.6efc1691-399a-4e59-8865-f7580738ce70", "2025.6efc1691-399a-4e59-8865-f7580738ce70"], "guid": "27e5aa7b-9d48-4b52-be39-8d56f9516573", "versionId": "595c144c-7b6b-44fd-88a4-dec697d5293c", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End Event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6cd1babe-54c6-41be-81e5-bf33e3c99a2d", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.fe313612-9b33-4c13-87ad-5285d86baac9", "2025.fe313612-9b33-4c13-87ad-5285d86baac9"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.bfb76acf-630e-48d5-8629-eed50b637bbc", "2025.bfb76acf-630e-48d5-8629-eed50b637bbc"], "guid": "e3bfdd39-a7a4-4057-8f96-a43fff363620", "versionId": "855d0a55-d306-40ac-83a9-15dfe2641980", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.bfb5040f-7412-4fe4-8d92-ce779e6aaadd", "processId": "1.76de764f-7233-4b6f-8130-202e1d0049ac", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4082a5f8-e4fb-405f-8f86-8ef4d9f304a3", "2025.4082a5f8-e4fb-405f-8f86-8ef4d9f304a3"], "endStateId": "guid:651a1a6abf396537:64776e00:18baeba64af:23d9", "toProcessItemId": ["2025.31b1cc7b-1684-4bec-875a-6591c4110d93", "2025.31b1cc7b-1684-4bec-875a-6591c4110d93"], "guid": "286e4130-353e-42a6-aea2-1ec5608cc619", "versionId": "f8c52518-e0ee-43b1-8d1e-12fa90a3b168", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}