<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <participant id="24.55c1fe56-ce36-4b4b-87bd-380022cde382" name="Trade compliance Maker">
        <lastModified>1691144885665</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <participantId>24.55c1fe56-ce36-4b4b-87bd-380022cde382</participantId>
        <participantDefinition isNull="true" />
        <simulationGroupSize>2</simulationGroupSize>
        <capacityType>1</capacityType>
        <definitionType>3</definitionType>
        <percentAvailable isNull="true" />
        <percentEfficiency isNull="true" />
        <cost>10.00</cost>
        <currencyCode isNull="true" />
        <image isNull="true" />
        <serviceMembersRef isNull="true" />
        <managersRef isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"team":[{"members":{"Users":[{"name":"heba","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"somaia","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"abdelrahman.saleh","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}],"UserGroups":[{"name":"BPM_ODC_Trade_Compliance_MKR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails","type":"StandardMembers"}]},"documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"Trade compliance Maker","declaredType":"resource","id":"24.55c1fe56-ce36-4b4b-87bd-380022cde382"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.w3.org\/1999\/XPath","id":"24.55c1fe56-ce36-4b4b-87bd-380022cde382"}</jsonData>
        <externalId isNull="true" />
        <description></description>
        <guid>guid:d694a63221635d5b:6baf87c4:1896d4865c2:74f1</guid>
        <versionId>8b8cf17f-db58-4284-86c7-f2ad044d9901</versionId>
        <standardMembers>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_Trade_Compliance_MKR</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>heba</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>somaia</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>abdelrahman.saleh</name>
            </standardMember>
        </standardMembers>
        <teamAssignments />
    </participant>
</teamworks>

