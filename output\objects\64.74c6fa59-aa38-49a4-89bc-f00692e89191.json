{"id": "64.74c6fa59-aa38-49a4-89bc-f00692e89191", "versionId": "83f0eba5-cd01-40ef-b913-38fd871d516c", "name": "Charges And Commissions CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "chargesAndCommission", "configOptions": ["chargesAndCommVis", "btnVis", "amountCollectableByNBE", "addchargeBtn", "exchangeRate"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//console.log(\"outside vis function \"+this.context.options.addchargeBtn.get(\"value\"));\r\r\n\r\r\n//this.chargesSectionVIS = function()\r\r\n//{\r\r\n//\tvar addChargeBtn= this.context.options.addchargeBtn.get(\"value\") ;\r\r\n//\tconsole.log(\"inside vis function \"+addChargeBtn);\r\r\n//\t\r\r\n//\tif(addChargeBtn == false)\r\r\n//\t\tthis.ui.get(\"chargesAndCommVis\").setVisible(false,true);\r\r\n//\telse\r\r\n//debugger;\r\r\n//\t\tthis.ui.get(\"chargesVIS\").setVisible(true,true);\r\r\n//\t\tthis.ui.get(\"chargesVIS\").setVisible(true,true);\r\r\n//}\r\r\n\r\r\n//this.calculateDefaultAmount = function(index)\r\r\n//{console.log(\"inside  calculateDefaultAmount function\");\r\r\n//\tvar Index      = index.ui.getIndex();\r\r\n//\tvar rateType   = this.ui.get(\"rateType[\"+index+\"]\").getData();\r\r\n//\tvar flatAmount = this.ui.get(\"flatAmount[\"+index+\"]\").getData();\r\r\n//\tvar rate       = this.ui.get(\"rate[\"+index+\"]\").getData();\r\r\n//\t\r\r\n//\tconsole.log(\"index >>\"+Index);\r\r\n//\tconsole.log(\"rate type >>\"+rateType);\r\r\n//\tconsole.log(\"flat amount >>\"+flatAmount);\r\r\n//\t\r\r\n//\tif(this.ui.get(\"rateType[\"+Index+\"]\").getData() == \"flat amount\")\r\r\n//\t{\r\r\n//\t\tthis.ui.get(\"defaultAmount[\"+Index+\"]\").setData(flatAmount);\r\r\n//\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setData(flatAmount);\r\r\n//\t}\r\r\n//\telse if(this.ui.get(\"rateType[\"+Index+\"]\").getData() == \"fixed rate\")\r\r\n//\t{\r\r\n//\t\tvar result = (rate/100)* this.context.options.amountCollectableByNBE;\r\r\n//\t\t\r\r\n//\t\tthis.ui.get(\"defaultAmount[\"+Index+\"]\").setData(result);\r\r\n//\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setData(result)\r\r\n//\t\t\r\r\n//\t}\r\r\n//\t\r\r\n//}\r\r\n//this.accountTypes = function (value){\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (value.getData() == \"Customer Account\"){\r\r\n//\t\tthis.ui.get(\"customerAccountNo[\"+index+\"]\").setEnabled(true);\r\r\n//\t\t\r\r\n//       \tthis.ui.get(\"accountBranchCode[\"+index+\"]\").setEnabled(false);\r\r\n//       \tthis.ui.get(\"accountCurrency[\"+index+\"]\").setEnabled(false);\r\r\n//\t\tthis.ui.get(\"glAccountNo[\"+index+\"]\").setEnabled(false);\r\r\n//    \t      this.ui.get(\"glAccountNo[\"+index+\"]\").setData(\"\");\r\r\n//    \t\t\r\r\n//\t}else if (value.getData() == \"GL Account\"){\r\r\n//\t\tthis.ui.get(\"customerAccountNo[\"+index+\"]\").setEnabled(false);\r\r\n//\t\t  this.ui.get(\"customerAccountNo[\"+index+\"]\").setData(\"\");\r\r\n//\t\t  \r\r\n//       \tthis.ui.get(\"accountBranchCode[\"+index+\"]\").setEnabled(true);\r\r\n//       \tthis.ui.get(\"accountCurrency[\"+index+\"]\").setEnabled(true);\r\r\n//\t\tthis.ui.get(\"glAccountNo[\"+index+\"]\").setEnabled(true);\r\r\n//    \t    \r\r\n//    }\r\r\n//}\r\r\n//\r\r\n//this.calculateDebitAmount = function (value){\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tvar changeAmount = this.ui.get(\"changeAmount[\"+index+\"]\").getData();\r\r\n//\tvar negRate = this.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").getData();\r\r\n//\tvar result = changeAmount * negRate;\r\r\n//\tthis.ui.get(\"debitedamount[\"+index+\"]\").setData(result);\r\r\n//}"}]}, "hasDetails": true}