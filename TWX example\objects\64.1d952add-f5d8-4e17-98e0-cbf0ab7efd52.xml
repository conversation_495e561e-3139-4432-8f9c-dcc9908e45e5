<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52" name="Charges And Commissions CV 2">
        <lastModified>1722491012436</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;6c9965da-68d8-488f-858b-c7632f474de4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b071d055-c471-42b6-827c-fb2ce2179f88&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Applied Commissions / Charges&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fa5c1bf4-4d73-48aa-8d63-32f4549d6fa9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bc66be4b-d03d-4471-85af-34533c1dcb53&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9dac7457-f188-4623-80df-8939eab165d7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5967affe-43cd-4c81-81ef-c756a5cbcaaf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.455e44ab-b77b-4337-b3f9-435e234fb569&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;4f941797-0aa6-4676-838a-91edf0c21093&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;53ace8eb-89ac-49fe-8763-b4ba1f30c223&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout7&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5adfcf85-6f4f-4a55-80f7-b48a65485ef5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 7&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a61f4167-d404-4c2e-8fb3-a25b49235585&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e485f31-3664-47e2-8522-2d5a1179ce64&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f0bfc96a-81d7-4b08-8018-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.commissionSectionVIS&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission[]&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;b2c372ff-03de-48ef-8f6d-8d1c3f946806&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;43424280-21a0-4750-8fa8-5dfa3d965b07&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;chargesAndCommission&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b6e09029-7f19-4be8-824c-de574463bfb2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.businessData.chargesAndCommission.currentItem.description&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a963b81e-0c49-49c2-82bc-3ccf7956136f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9eb3bc4a-d941-487d-8845-8e5fd9d26cd1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;18e0b511-d5b1-4517-88b1-2a0bc8c26dab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;058cc9d3-da64-49ae-8fbc-84809634b3a7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;904a6f4a-4d13-4363-8896-03ab5f668c35&lt;/ns2:id&gt;&lt;ns2:optionName&gt;initiallyCollapsed&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;cf621baa-9259-4f44-88e0-f44b22446791&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b8b98a80-0f41-441c-8687-abc2e6f0017e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;59dae1d3-6b03-49a8-8302-ea835bf9b287&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb83e7c6-9a72-4414-8e79-7e5f0ca9ffd1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;30ef7c84-f047-4f98-850d-6f0bf136c088&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9a2ea8ae-ba4d-4223-88bf-c9f1c160ad47&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;41634280-0a30-45bd-8243-7700b0b7a22c&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;edccb848-393d-41bb-8789-27e681374f3f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7b86e9ba-f94b-4207-856f-2a2fb3c835bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;42b65ac8-79b9-4711-8973-e644396116b3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e37c1f86-26ca-4ff9-84c6-0ea295d1c04f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c1db5512-3181-4e0a-8be9-94eae8d7593f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;d6ec2c21-b219-4af5-8260-cb0de5946b6b&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;69b482a2-474d-4849-83d7-aa9a1cef8670&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;component&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;708f669f-5edd-45e7-9a8e-b68ed78014db&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6570be34-5eea-4c4e-8051-2197858ca656&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Component&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;33e5f88c-354c-4b06-8231-5629a608a902&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;914249d9-36c7-41ea-8e52-ce338c586009&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.component&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;851986d6-c42d-4980-89db-50112d02cd41&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;rateType&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3668268f-f8e9-4aa6-b99f-fd89a2e94565&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;35ab16a2-2806-4061-8f87-a584cc182477&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Rate Type&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;58848896-15f7-4302-8925-326817755cf7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e2ac37f4-088e-4e05-8ca7-27cd34740660&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;998020a3-5f1f-45e1-8041-88edbd67f2a4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.rateTypeVis(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75a85937-4132-46a0-814a-256b8fee629f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.rateTypeVis(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.rateType&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c30b436e-f3c0-423e-83dd-010612b002a5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;defaultPercentage&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;af7bed4d-1994-4621-8211-bf243be836b7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f5fdeea9-3ca8-4bb7-8693-8d9ffb5baf82&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Default Percentage&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d9902048-4d22-44df-8ec4-e3d42aa02c32&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b99feeda-8178-40f7-8101-1c93e028f220&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;823f4f1e-c042-428e-88c5-e3759c248b49&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.defaultPercentage&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;bc3ddc97-9448-4b1d-8c70-87c88ab9d7a5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;defaultAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e61f6c6e-8fc8-4218-a9f1-cc8dd99a9d40&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4bf2b01a-72f8-4767-894f-99602ad95da2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Default Amount&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e166b285-7ae2-4562-8e96-024a39bc6dfb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32eeef16-1e5e-49bf-804e-43bdb2281025&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;beca0346-f852-47f5-81bc-f1310e01ccda&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;var rateType= ${rateType}.getData();

if(rateType== "Flat Amount")
	${changeAmount}.setData(me.getData());//if flat amount&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e23de8ba-8949-4fba-83ed-03d1f7bf1bfa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.defaultAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;81b6faee-deed-40c1-8972-b94c961ca363&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;waiver&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc7c3346-c328-4532-8c36-7426bb94cac9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Waiver&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;45b1e1c4-7b37-430a-8a90-24c824ca0c0a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;47461993-5ce4-4782-8913-c13af5b61915&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;97fd9058-6a44-4a3b-85da-7f901ef8d282&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b5d9564b-01af-4a23-8c4f-19109bd63f4c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.waiver&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4cf3c942-4f7d-444d-829f-3d827b1c73ed&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1075cd53-6049-4216-86e8-970da2ce8868&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8e2f85b4-336f-4e3f-80c6-cdb339ec62c2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0b605a04-2a91-4630-881a-0ff31e03d9d9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e41c83b2-2623-4969-8cc1-e16972363b58&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;d6509911-b748-49b4-81c3-f8c16421dba5&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2fac58b0-a6fc-4cd2-8c24-edea9572089f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;description&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f722544e-ef78-40e2-8509-aed11afe2ab0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;068cd7df-087b-4137-809c-d05c8e7aad4d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Description&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;be334621-03fb-4667-8b50-038da29119b4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e31d6bb-5b9d-48b6-8ee2-ce56a334dbac&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.description&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;0214920f-d419-48ad-8585-6047a96a6311&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;defaultCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e04fed56-2de3-4051-969d-cdff11b2253f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e172a97b-9333-43b6-8dfe-6a7f9393209a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Default Currency&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1292e399-bbcb-4e75-8a23-2b2439ddb58c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b1444cb9-e555-4a51-8b61-831e697290ae&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9019a022-791d-4b88-8e00-68cef3142208&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3509804f-ea8f-4bc8-8227-dcfb114036b3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.2f93c4b5-368e-4a13-aff6-b12926260bb3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3cb5c856-ac04-4589-8a97-4fd49f8f3492&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.odcLookupsTable.Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.defaultCurrency.value&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;991985f5-046a-4078-8f78-b2a4cc66920e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;changePercentage&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5eace80d-3423-41f0-afc0-0e47f0f39eba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0ad68a13-0c9a-4594-8c93-6b75a7b6602f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Change Percentage&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c5ecc3bd-198d-4af7-8c72-40202a1e31ff&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92815e0e-75a8-4392-8dd1-b83554d0f5b9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9ce36d90-6755-4eeb-8b83-b1604cb756c0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.amountValidation(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.changePercentage&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4ff91b91-b1c3-478f-890d-fa54c32d03ee&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;changeAmount&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0a556781-90cb-4a05-990d-d3925b480d5d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7991b8e7-b0fc-4614-8cdd-28cb13cc4e1f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Change Amount&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c4f0faf7-98f8-4c9f-8916-592a3fa868dc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;82532b04-7efe-462d-8090-dd38802a773c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;caf6b808-dcc8-442d-8007-1aec8195307a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validatePositive(me);
view.calculateDebitedAmount(me.ui.getIndex());

var value = me.getData();
me.setData(Number(value.toFixed(2)));


if(${rateType}.getData()=="" &amp;amp;&amp;amp; me.getData()&amp;lt;0)
	return 0;
	&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;351863d8-c1d8-48a1-865a-4f256a17bf9b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;numericFormatting&lt;/ns2:optionName&gt;&lt;ns2:value&gt;auto&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.changeAmount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;85bfb105-8126-444c-853c-5503a39ebf4f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;calculateChangeAmount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f6b9eda0-d6f2-4d07-8e69-333c282b7c81&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Calculate Change Amount&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;797bfbde-f31e-4344-8540-913e4dad3757&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fd33d54b-fb0d-4297-81e6-fc4f449df53c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;30233caa-0f70-44f7-842b-2af69f08fba8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.40a7d34d-1192-436a-ba5a-0360265d3261&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9691a49b-c735-419d-8915-88918d097ea6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d287888d-3ba1-42c8-85f4-8739ecd2a145&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;
view.setChangeAmnt();&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4dba5704-bd1d-4d87-8725-4e0f9bb3d7a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCINVOKE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.calculatedChangeAmnt&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d5e73efa-fdad-4eaa-8318-35f6a31db51b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;debitAccounts&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f521f70a-8164-4dae-8c17-81d09e12ff06&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Debited Accounts&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;281be054-36bc-4d01-8e46-efb590687972&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1ad9cc69-a0d1-4054-8fc1-80cb1de4e6c5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;666c42ce-0f33-4da5-8b1a-0a0499075dfa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;eabf4899-e52c-4001-8ce4-d3a66059fab3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;initiallyCollapsed&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e001b155-b0db-4394-82ba-36a487e3f66b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;b9048adb-db04-4fe3-811f-e986816efff6&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;c717ff79-99d8-4095-831d-9cdbb2543231&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c0a47fa-fd6d-4666-819a-0ec1a0e89578&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b032bfe2-5c4c-4499-8d6c-27a215fe52e9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1278113-1d20-4753-8937-f54a268eb845&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a6bf04de-b947-4530-8294-a65a47c3b998&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;5b07cbc8-289e-4cd1-8131-1a2573b41274&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3770c4d7-538f-4e3f-809c-b0c8d23fc502&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3d2995df-bace-48ab-8a82-095a5927d792&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c60a576b-8f2a-4c20-821d-87b9e4047dde&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;feb04f51-bc65-434d-8445-f7cd1b6cbe06&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;138bcfb1-3b05-42c2-8aee-28c469196a31&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;b76e641c-1ff6-4a90-8409-73795a2548a3&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;93ad861f-e8d2-485f-8f70-b1482cb1ee36&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Single_Select1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cff53078-e2b3-4403-83ce-855dfffdcd3d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Account Class (Customer / GL)&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e60a4534-8e9d-412e-81c3-2672d238261a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;655d95cd-1914-4f5c-885b-28c81bf27435&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a2c11eec-7a18-437e-8905-82a3fb468c10&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4d1c0fa5-31c2-472a-86aa-5d74d374f848&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.2f93c4b5-368e-4a13-aff6-b12926260bb3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7e656a51-784a-4dbd-80e3-e7cdb4e1dc58&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.odcLookupsTable.AccountClass&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3019dacf-c9ed-455b-89d2-0760fae216f5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.accountNumVis(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;23b53ebd-9b7a-4de6-88b5-af40680787d7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8f5b71ff-4d30-4754-891e-cfd66ed91e15&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a4950278-9cf4-4d44-88f4-44817a1d3b3c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.accountNumVis(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAccount.accountClass&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;77309e1e-440a-4f08-8df0-eb4b998a6d00&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;customerAccountNo&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f1239f3-81f3-49bc-aaf9-e45305a768a9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e554f9c2-09c4-4aea-8110-5f5b1d4242ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Account Number&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f9d8254b-eb92-401e-8617-f9e8ba55a36f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;72d2e74e-6322-4859-8775-4acd42b6d0ad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5958adf3-96e5-4fa6-8c57-37c739764307&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;10c35bfb-8e98-4bfa-854f-cef6ab5a4f81&lt;/ns2:id&gt;&lt;ns2:optionName&gt;staticList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;98a6d341-f513-4f0f-8456-dcf39c38b8fa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.fillAccountData(me);
//${GetRateButton1}.click();
//console.log(me.ui.getIndex());&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e9b5c8e-4a22-4aa5-8578-ea636f467180&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d2b68da-5353-4378-83ef-093b146c5b09&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.chargesCustomerAccountList[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;17981b80-63d3-44c0-8f03-e976b193943a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"accountNO","optionDisplayProperty":"accountNO"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6378b02d-9b58-4e3e-843c-12986907008c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"accountNO","optionDisplayProperty":"accountNO"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAccount.customerAccountNo&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;63a9c4d0-c48e-4b5b-8f46-b1d086ea381a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GLSection1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;30688ff3-5bcc-41da-81d5-d314e233aeb0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;GL Section&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6085bcb-9e68-4d34-8a1a-9ca4c273bcec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;193931e2-50c0-4f17-8a78-58a764d65e5c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;c39f0152-d65e-4a8d-8ca5-5a1294c1b6bc&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;4d233470-9f5b-4d1b-8f89-657b8ca986f8&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;glAccountNo&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8eefea63-f7af-4be6-87c3-e7d0ed5aafad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6af44d13-5bfc-4f7b-8b66-c51415ca89ea&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;GL Account Number&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;42ed4dd8-b64d-4fc2-8f79-ee77b71fd19c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a7b49e1a-e0a3-4185-8431-cae6f06daa49&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"DEFAULT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;debdbdde-0937-4e97-88bc-775eec0e393b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 9){
	me.setValid(false , "must be 9 characters");
//	me.setData("");
	return false;
}else{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c593600d-63c2-43cf-8a95-be796ce5632b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.resetGLButton(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAccount.glAccountNo&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;eb896b10-1afd-4025-80d0-2da37a65e480&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;verifyGLButton&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ce062b29-0f90-4177-8ab3-f8b606229726&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Verify GL&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;101d9319-6ed1-42f1-8e27-1a2145d0c070&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;03e74710-5b53-4e3a-84cc-ffd87c81a926&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1b1dc3a6-d490-4d66-84aa-55167e6f8ddc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0d92386e-1aff-48d2-8e57-6272674daea7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;33970af5-fa6c-4f5d-8f23-198d07dd2be5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.executeGL(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c922a983-a585-42d6-8c55-0ae165ca13b8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;34b6549a-c55d-4728-8173-4581911aaaca&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9eec007d-6354-4ad9-8e5c-358767690474&lt;/ns2:id&gt;&lt;ns2:optionName&gt;expression&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ec0f5af-1122-4df1-8f89-70e0f6321c11&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;79b7fec0-0869-490f-810a-ca615f35942c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;preventMultipleClicks&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d4572d84-1de0-458e-8874-94234ffbe514&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;verifiedText&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;883b703f-c459-4e07-89ef-6c1e6724b81c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Verified text&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0fe02ae2-65eb-4a1a-8948-6686520f583d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;296acaa4-d94d-4544-8143-c7c275e5757b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;50c77068-9da7-4f71-86dd-b7b7f53f02fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9929494-4d73-4023-82c4-dfccab80cfc8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;weightStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;M&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fd4ab0a8-ec3f-4f75-8bd0-ead54f5cc7b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;textAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"C"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d3b417ce-8afe-4a2c-8739-93bca45a7f53&lt;/ns2:id&gt;&lt;ns2:optionName&gt;labelPlacement&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"T"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e728ed88-2e83-48db-8889-26a2766c2b80&lt;/ns2:id&gt;&lt;ns2:optionName&gt;textWrap&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"N"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3f54d5a3-d3ab-4d35-81c1-545aa991ed77&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"L"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;108ffcf1-38ce-4d03-8488-e0992f1b22e6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;labelWeightNormal&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":false}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;192e08a5-dedb-49d1-866d-e46320f27e10&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@margin&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"0px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ad2032f7-04ca-4112-8cfe-b58a8999d9c3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"30px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e6f31807-1f2e-48e0-82f3-d2d6707439fb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"20%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.glVerifyMSG&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;60d7e19c-c102-42d6-83ad-b4445eaf6838&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;accountCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8b4deaa2-d77f-47dd-b33d-7bf83460cc70&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f591d64-9ada-4156-8200-1eeff9cac9c2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Account Currency&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5ee42c4a-3147-4d2c-8750-66512fe5deb3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32ab848f-fc05-40d1-8937-d20fd6d937c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7f58b512-3c15-436c-8c61-39fecceb1299&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a205fb46-b8f9-4abd-87b6-bcda6230235a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.2f93c4b5-368e-4a13-aff6-b12926260bb3&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8fdfca90-693b-4b4e-8684-4d80e6176b53&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.resource.odcLookupsTable.Currency&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;194752bc-e935-4547-8ed5-a39da7d826e0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setExchangeRate(me);
//view.NeoExRateVis(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;125d1598-0467-46d1-87a7-53636c3bd59f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;59f24924-453d-46e1-8dcb-7922f7021a7f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"optionValueProperty":"name","optionDisplayProperty":"value"}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAccount.currency.value&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2c0e31db-8da9-4f42-8d54-c059db3f59b7&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;75ec1a47-ea19-4889-8f8f-1f001a2c61df&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 4&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40fd3c8a-2141-40f7-8d57-b019d8e7a329&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1fb3c354-b58e-40e5-81dc-6fb76454a340&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7cbfa79f-ab1c-4858-84b0-2c0633184a99&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;8cad7bbb-a275-4902-8a1f-8009d5b72abb&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d87bab08-5ab3-4e71-8a65-e991214f7e63&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;accountBranchCode&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ebc729b4-cfb1-4dff-b949-fd349b13fa02&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;67ee0a19-c5ae-4306-8bcf-ad939804526c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Account Branch Code&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6edf9d3a-4e11-4f71-8af9-c054155418a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;27fed48d-9fb3-4ec0-83d7-5677e09747f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_INPUT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(potential.length &amp;gt; 3)
{
	me.setValid(false , "max lenght 3 Digits");
	return false;
}
else
{
	me.setValid(true);
	return true;
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4fb0e785-fc20-463d-86f0-e98a25e9f35e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;regExp&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4fa442b9-9efc-4f52-89de-19518ef0a4d3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validateDigits(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAccount.branchCode&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;3aaa6633-731a-4ae2-8d61-e4c99f898d83&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;balanceSign&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dd9b8e04-cbb0-4e9b-beef-06aca009be21&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;70e0a784-2d6d-4984-843d-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Balance Sign&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32ea5d7d-b1d7-4d96-83f6-c961e7b440cb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;********-d275-4a51-8ac4-6cc182188775&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAccount.balanceSign&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;416be139-913e-4611-8e2f-5adc81ae6d5d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;accountBalance&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;798f77fe-7fc8-40bb-a7c2-fb1eeefe9e0d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;12fc8e5a-687c-41f6-8943-120757036e8c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Account Balance&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5e5bbce0-0bfb-4605-8bac-ac9c38848651&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f1a3b98f-ec97-4d38-89a7-43a9f4fd63a3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;03306e63-fe0b-4051-88cb-0b8b5ee3b930&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validateOverDraft(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAccount.balance&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;7587bc3c-c2cf-4c1b-8bb7-94b953aa46f5&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CheckexistingGLAccount1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;df5826ce-432a-4d9f-8dde-3383231b867b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Check existing GL Account&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f856ff0d-da98-4203-81f7-c4a6afcef609&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;802628bc-273e-4411-8629-89e235744488&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f8118136-1b76-43ac-8e85-63dbe60265e7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.2f8e9d68-4adb-42dd-a9f6-634969db03dc&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9903f0e3-8859-41ee-846c-98ea111577fa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0434e8e2-a7a4-4f65-8dbf-11bcc3615e32&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validGlAccount(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0bac456a-a9ba-40c2-86f8-ceeb0fcbbd85&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;console.dir(error);
view.AjaxErrorHandling(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;819cb011-6aa5-45c9-8921-b3e4f24adf03&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.isGLFound&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a174fcd1-66fc-4d95-862c-10a3354c238b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetRateButton1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1605876a-6739-4688-8ea9-44ffd350af75&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Button&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f5c3e450-03bd-484a-8853-145c2c9ee5ee&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0e8ef7f4-e40c-418c-84a0-f9bc0fdb8dbe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;20858185-986a-4da6-86c3-255ccc1b6685&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8e0b27b3-9f18-484e-8858-18f254e09147&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;debitAmounts&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b279ff1d-8028-4ecd-8c13-a81ca02c0e38&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Debited Amounts&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;034e5b3e-43e9-47c1-891f-0333ea1a7ae1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;931fb940-ae60-43ae-8b9e-532d3da0e670&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e3b0c19-f227-4c6d-850e-c132abc8b759&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0a663d82-8beb-4e82-8e05-9fdf1fdb1df4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;initiallyCollapsed&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d2615f8b-6e87-415e-8204-b8ddd5cc50d2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;c6d159aa-9f4b-47a6-8357-d5a7e7887e67&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;23c1e08f-52a5-4bd2-895d-6dd013bca3b0&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Horizontal_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;804ac415-8214-4623-8c5f-6afcc437c674&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Horizontal layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0263d774-d9ee-403f-8c44-0fafd302420d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;68ae756f-6170-4fb5-859c-46d850cadea8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8d522587-8573-4795-8ee2-17ad601cc877&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.44f463cc-615b-43d0-834f-c398a82e0363&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;cc0dbc26-8271-42bb-8302-c63a73bd2745&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d4d611a5-4f54-4f6d-8a55-5983d3d57d8d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c23e5a97-62d9-48e6-8274-64c59b380077&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 5&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6d29de68-2db6-47b7-8209-9a170fb3240d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da74cecf-e9e9-4078-8c95-8bfe13f2a66d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cddd604e-4463-4ee1-869b-8a3e81daa4a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;8ccf5356-290f-46c0-8c46-5db339c226d9&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1a38e289-8b96-4a2a-8a71-e517c3d6dea4&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;standardExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3ad33ce6-392a-4fca-8211-d4e90bbbb560&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9d44a489-969a-4b90-8798-687829da2b11&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Standard Exchange Rate&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;acf10365-34cf-4f63-87a0-885f782d23f6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1d6af485-3ace-4165-810e-36ad77c9f478&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7f13708e-2bd2-46b0-847a-bb01ab993aa8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;numericFormatting&lt;/ns2:optionName&gt;&lt;ns2:value&gt;auto&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAmount.standardExRate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;a58ec1a1-ee25-4771-8f12-67c9cc48651a&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;negotiatedExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;604b841e-3b66-4381-b5dc-f55d65be9ac0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d6268d6a-b08c-4355-8dd5-beb63ecc2967&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Negotiated Exchange Rate&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;021e5602-7340-4a9f-8d6f-d1186a8df3c9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7279c42e-f13c-4670-81b7-df45ed06de7b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.calculateDebitedAmount(me.ui.getIndex());

var value = me.getData();
me.setData(Number(value.toFixed(6)));&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;512db637-a7ac-4741-8143-4ccf7fbb2a92&lt;/ns2:id&gt;&lt;ns2:optionName&gt;numericFormatting&lt;/ns2:optionName&gt;&lt;ns2:value&gt;auto&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAmount.negotiatedExRate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;31a9355b-e623-4fbd-8c69-b137ae88be09&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetExchangeRate&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;575a856b-fd53-4e09-873b-937af96e8e26&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Exchange Rate&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b1182ea-7ea5-4639-8496-4622721fb9f0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;69ca65de-ac6e-4f6f-872e-426584a729bf&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9e54fbdb-1df8-4e0e-8d69-063dc2a0cc84&lt;/ns2:id&gt;&lt;ns2:optionName&gt;attachedService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e78ad170-fcb7-43b3-8bf1-8361d7d93d01&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;82657dfc-193f-4e99-87d8-259904f5ae94&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCINVOKE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7c6c485e-80cc-4df0-85a6-2e52058436a1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCRESULT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.setExServiceResults(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bd50e4da-f21c-4695-8f88-bce1ae25d6c7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_SVCERROR&lt;/ns2:optionName&gt;&lt;ns2:value&gt;alert(error.errorText);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;91e2e95a-f44b-433e-8a5e-6e7f76c641b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;busyIndicator&lt;/ns2:optionName&gt;&lt;ns2:value&gt;C&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.exRate&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;49c7a5d4-6722-4fa6-88d4-fb0cc8b92322&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Vertical_Layout6&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;67d3274f-a959-460c-8fc6-7b4270311f6d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Vertical layout 6&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0e328623-e561-4936-8160-64545b3fb6fe&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e1bedaf7-e02b-4ab5-8112-f9417c061e7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;624cd1d7-3fab-4677-8392-************&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@width&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"49%"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;5b5eb59c-16f4-47a4-830a-1fbd2a45b75f&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8e3bb984-d6bb-48d7-8c25-fa090cb3f30c&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;debitedAmountInCurrency&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;26caa661-567c-42ac-a124-f0488cdfdef9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b181afc2-1ce6-4343-8d55-a2a03cebedba&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Debited Amount in Account Currency&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b520c4e9-aa89-4446-8ebd-2342701610c8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5447ba4-7dcc-4a51-8324-84f7b352f83b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"READONLY"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f676d93f-44f7-4448-8bbc-29d24c4520a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalSep&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;92cc5dca-93c0-49ac-81f9-01c18e8ef1f9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;decimalPlaces&lt;/ns2:optionName&gt;&lt;ns2:value&gt;2&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4674a294-9cb2-4f68-8763-d45bf70a6339&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;view.validateOverDraft(me);&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.chargesAndCommission.currentItem.debitedAmount.amountInAccount&lt;/ns2:binding&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;a4e23b88-7944-4fad-8b49-1e8d0b4b21d2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GetChargesBtn&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6f9b41ff-2283-4a8c-89a7-810949f28345&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Get Charges on load&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c0d01e81-a6a0-425f-80b8-91ad92078b08&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;747760ad-e7c2-4c03-84e6-5e4edd0e131c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;da01ab07-4d13-49a1-8a97-7d20ffda20f5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction>if (!this.context.options.isChecker.get("value")) {&#xD;
	this.ui.get("GetChargesBtn").click();&#xD;
}else{&#xD;
	this.ui.get("Vertical_Layout7").setEnabled(false);&#xD;
}&#xD;
</loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>8c6bee91-6d2e-4d72-8e55-b5778e8a447c</guid>
        <versionId>a72c1fc9-ee22-4196-9904-eb4eb8bfe774</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="chargesAndCommission">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.e46aab16-396d-4e0a-8c98-3fb57d9ebff9</coachViewBindingTypeId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>true</isList>
            <classId>/12.e3018bad-b453-4bf5-96fd-09a5141cd061</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>3d422ff1-cacf-41d2-89e4-be2a62379fc9</guid>
            <versionId>c3823455-517d-41ff-8c44-59fc3a01ef9b</versionId>
        </bindingType>
        <configOption name="amountCollectableByNBE">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.dd0dcfea-27bf-4938-b09e-dce93eab2c4f</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>ee60f882-f307-46ee-8eda-e665e1d5b36b</guid>
            <versionId>c34f2c4b-7320-48f2-9859-0c1a6c5fc308</versionId>
        </configOption>
        <configOption name="accountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.42a31739-3ecc-4aad-9483-601fa0824b69</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>24a61b90-1fef-4912-84f4-bbfa24170ccd</guid>
            <versionId>023dfd73-bd7d-4de8-878f-732eca5c2bbc</versionId>
        </configOption>
        <configOption name="accountNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e2633861-4d6c-4bbb-8fcf-97ee09356181</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>94de94df-4eeb-4972-a862-48b7fe352b1c</guid>
            <versionId>f4e3b5a0-2b62-4f5e-a31f-0f8f09a2f9c3</versionId>
        </configOption>
        <configOption name="index">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.41a4de21-1c0a-4f6b-b886-de346e1dcfde</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>276828a9-4dee-41f2-acad-7d763f4bcb69</guid>
            <versionId>aa744b70-6ed8-4926-90d4-7d84787f15c9</versionId>
        </configOption>
        <configOption name="commissionSectionVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.d1979495-a1aa-4dd0-b095-6a3ffe295d02</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>707b5974-94b6-4502-ad1a-0fac108ec949</guid>
            <versionId>c2b08c5b-6089-4880-8c02-2277b039e245</versionId>
        </configOption>
        <configOption name="calculatedChangeAmnt">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.909953e4-47eb-49ff-b272-3dea9aba0579</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>4c8b2fa2-b7fe-4456-91d1-6e6a36ebdf51</guid>
            <versionId>80f7471b-0ee9-4672-b85a-badea0c1478a</versionId>
        </configOption>
        <configOption name="chargesCustomerAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.5a10ded8-708f-43e4-b930-e95598598668</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>7f2c8fd7-365b-46bd-bc83-2bcc8743779d</guid>
            <versionId>03f9639f-1cba-451e-a79d-36cc8966204b</versionId>
        </configOption>
        <configOption name="exRate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.e133be5b-fac1-482c-8c96-05499f37051d</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>7c132345-6251-4908-bab9-747930bb5fbb</guid>
            <versionId>55ab67d5-d3cf-4720-87e9-d28b2b16df99</versionId>
        </configOption>
        <configOption name="isChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.9d6f4fa4-f407-48dd-8925-9fbfb28cbcc1</coachViewConfigOptionId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>e99ba1cc-a6f5-422e-92af-10a91523a99b</guid>
            <versionId>dce2d4a7-d7de-4753-9807-84e3f79bffc7</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.74c38ba8-106c-458f-9cfe-9bd2ef8b3211</coachViewInlineScriptId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>//On Change Amount and Negotiated Exchange Rate - calculate Debited Amount&#xD;
this.calculateDebitedAmount = function(index){&#xD;
	var chargeAmount = this.context.binding.get("value").get(index).get("changeAmount");&#xD;
	var NegotiableRate = this.context.binding.get("value").get(index).get("debitedAmount").get("negotiatedExRate");&#xD;
	var debitedAmount = chargeAmount * NegotiableRate;&#xD;
	&#xD;
	this.context.binding.get("value").get(index).get("debitedAmount").set("amountInAccount", debitedAmount);&#xD;
}&#xD;
&#xD;
//On Account Class - Set vis for GL and Customer account&#xD;
//this.accountNumVis = function(value){&#xD;
//&#xD;
//	var index = value.ui.getIndex();&#xD;
//	if (this.context.binding.get("value").get(index).get("debitedAccount").get("accountClass").get("name") == "001") {&#xD;
//	    //Customer&#xD;
//	    if (!this.context.options.isChecker.get("value")) {&#xD;
//	        this.ui.get("customerAccountNo[" + index + "]").setVisible(true, true);&#xD;
//	&#xD;
//	        this.ui.get("verifyGLButton[" + index + "]").setVisible(false, true);&#xD;
//	        this.ui.get("glAccountNo[" + index + "]").setVisible(false, true);&#xD;
//	        this.ui.get("GLSection1[" + index + "]").setVisible(false, true);&#xD;
//	&#xD;
//	        this.ui.get("glAccountNo[" + index + "]").setData("");&#xD;
//	    }else{&#xD;
//	        this.ui.get("customerAccountNo[" + index + "]").setVisible(true, true);&#xD;
//	        this.ui.get("customerAccountNo[" + index + "]").setEnabled(false);&#xD;
//	&#xD;
//	        this.ui.get("verifyGLButton[" + index + "]").setVisible(false, true);&#xD;
//	        this.ui.get("glAccountNo[" + index + "]").setVisible(false, true);&#xD;
//	        this.ui.get("GLSection1[" + index + "]").setVisible(false, true);&#xD;
//	&#xD;
//	    }&#xD;
//	&#xD;
//	} else {&#xD;
//	    //GL&#xD;
//	    if (!this.context.options.isChecker.get("value")) {&#xD;
//	&#xD;
//	        this.ui.get("customerAccountNo[" + index + "]").setVisible(false, true);&#xD;
//	&#xD;
//	        this.ui.get("glAccountNo[" + index + "]").setVisible(true, true);&#xD;
//	        this.ui.get("GLSection1[" + index + "]").setVisible(true, true);&#xD;
//	        this.ui.get("verifyGLButton[" + index + "]").setVisible(true, true);&#xD;
//	        this.ui.get("customerAccountNo[" + index + "]").setData(null);&#xD;
//	&#xD;
//	        //GL account cant be overDrafted or not&#xD;
//	        this.context.binding.get("value").get(index).get("debitedAccount").set("isOverDraft", null);&#xD;
//	    }else{&#xD;
//	        this.ui.get("customerAccountNo[" + index + "]").setVisible(false, true);&#xD;
//	&#xD;
//	        this.ui.get("glAccountNo[" + index + "]").setVisible(true, true);&#xD;
//	        this.ui.get("glAccountNo[" + index + "]").setEnabled(false);&#xD;
//	        this.ui.get("GLSection1[" + index + "]").setVisible(true, true);&#xD;
//	        this.ui.get("GLSection1[" + index + "]").setEnabled(false);&#xD;
//	        this.ui.get("verifyGLButton[" + index + "]").setVisible(true, true);&#xD;
//	        this.ui.get("verifyGLButton[" + index + "]").setEnabled(false);&#xD;
//	    }&#xD;
//	}&#xD;
//&#xD;
//}&#xD;
&#xD;
//On Rate Type - Set Vis for Flat amount and fixed rate&#xD;
this.rateTypeVis = function(value){&#xD;
	var index = value.ui.getIndex();&#xD;
	var rateType= value.getData();&#xD;
	if(rateType!=null &amp;&amp; rateType == "Flat Amount"){&#xD;
&#xD;
		this.ui.get("defaultPercentage["+index+"]").setVisible(false,true);&#xD;
		this.ui.get("changePercentage["+index+"]").setVisible(false,true);&#xD;
&#xD;
		this.ui.get("defaultPercentage["+index+"]").setData(0);&#xD;
		this.ui.get("changePercentage["+index+"]").setData(0);&#xD;
	}&#xD;
	else if(rateType!=null &amp;&amp; rateType == "Fixed Rate"){&#xD;
		this.ui.get("defaultPercentage["+index+"]").setVisible(true,true);&#xD;
		this.ui.get("changePercentage["+index+"]").setVisible(true,true);&#xD;
		this.ui.get("changeAmount["+index+"]").setEnabled(false);&#xD;
	}&#xD;
}&#xD;
&#xD;
//====================================================================================================&#xD;
//Generic Validate Positive amount (accepts 0) (Change Amount)&#xD;
this.validatePositive = function(value){&#xD;
	if (value.getData() &lt; 0) {&#xD;
		value.setValid(false,"Must be &gt;= 0");&#xD;
//		value.setData(0.0);&#xD;
	}else{&#xD;
		value.setValid(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
//Generic Validate Digits Only (Account Branch Code)&#xD;
this.validateDigits = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	if(isNaN(Number(value.getData()))){&#xD;
		value.setData("");&#xD;
		value.setValid(false ,"must be digits");&#xD;
		return false;&#xD;
	}else&#xD;
	{&#xD;
		value.setValid(true);&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
//On Change of Change Percentage - Validate Change percentage before calculate change amount&#xD;
this.amountValidation = function (value) {&#xD;
	var index = value.ui.getIndex();&#xD;
	if (this.context.binding.get("value").get(index).get("rateType") =="Fixed Rate") {		&#xD;
		if (value.getData() &lt; 0 || value.getData() &gt; 100){&#xD;
			value.setData(0);&#xD;
			value.setValid(false, "Must be &gt;= 0 and &lt; 100");&#xD;
		}else{&#xD;
			value.setValid(true);&#xD;
			this.calcChangeAmnt(value);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
var newIndex;&#xD;
//call from amountValidation function - Calculate change amount service call&#xD;
this.calcChangeAmnt = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
&#xD;
	var requestAmnt = this.context.options.amountCollectableByNBE.get("value");&#xD;
	var changePercentage = this.context.binding.get("value").get(index).get("changePercentage");&#xD;
	&#xD;
	var concatedDefault = requestAmnt + "," + changePercentage;&#xD;
	this.ui.get("calculateChangeAmount1["+index+"]").execute(concatedDefault);&#xD;
	newIndex = index;&#xD;
}&#xD;
&#xD;
//On result of calculateChangeAmount1 - Map Service call ouput&#xD;
this.setChangeAmnt = function(){&#xD;
	var index = newIndex;&#xD;
	var changeAmount = this.context.options.calculatedChangeAmnt.get("value");&#xD;
	if(!!changeAmount){&#xD;
		this.context.binding.get("value").get(index).set("changeAmount",changeAmount);&#xD;
	}else{&#xD;
		this.context.binding.get("value").get(index).set("changeAmount",0);&#xD;
	}&#xD;
} &#xD;
&#xD;
//On change of Account Num - Set Account Info for each customer Account&#xD;
this.fillAccountData = function (value) {&#xD;
	var index = value.ui.getIndex();&#xD;
&#xD;
	for (var i = 0; i &lt; this.context.options.chargesCustomerAccountList.get("value").length(); i++) {&#xD;
		if (this.context.options.chargesCustomerAccountList.get("value").get(i).get("accountNO") == value.getData()) {&#xD;
		&#xD;
			//Set isOverDraft&#xD;
			var commClassCode = this.context.options.chargesCustomerAccountList.get("value").get(i).get("accountClassCode");&#xD;
			var code = commClassCode.substring(0, 1);&#xD;
&#xD;
			if (code == "O" || code == "D") {&#xD;
				this.context.binding.get("value").get(index).get("debitedAccount").set("isOverDraft", true);&#xD;
			}else{&#xD;
				this.context.binding.get("value").get(index).get("debitedAccount").set("isOverDraft", false);&#xD;
			}&#xD;
			&#xD;
&#xD;
			//Set Account Info&#xD;
			this.context.binding.get("value").get(index).get("debitedAccount").set("branchCode", this.context.options.chargesCustomerAccountList.get("value").get(i).get("branchCode"));&#xD;
			this.context.binding.get("value").get(index).get("debitedAccount").set("currency", {});&#xD;
			this.ui.get("accountCurrency["+index+"]").setData(this.context.options.chargesCustomerAccountList.get("value").get(i).get("currencyCode"));&#xD;
//			this.context.binding.get("value").get(index).get("debitedAccount").get("currency").set("vale", this.context.options.chargesCustomerAccountList.get("value").get(i).get("currencyCode"));&#xD;
//			this.context.binding.get("value").get(index).get("debitedAccount").set("currency",this.context.options.chargesCustomerAccountList.get("value").get(i).get("currencyCode"));&#xD;
			this.context.binding.get("value").get(index).get("debitedAccount").set("balance", this.context.options.chargesCustomerAccountList.get("value").get(i).get("balance"));&#xD;
			this.context.binding.get("value").get(index).get("debitedAccount").set("balanceSign", this.context.options.chargesCustomerAccountList.get("value").get(i).get("balanceType"));&#xD;
			&#xD;
			this.context.options.index.set("value", index);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
//On change of GL Account Num - Reset Data on GL account change&#xD;
this.resetGLButton = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	this.context.binding.get("value").get(index).set("isGLFound",false);&#xD;
	this.context.binding.get("value").get(index).set("glVerifyMSG","");&#xD;
}&#xD;
&#xD;
//On click of validate GL Btn - Call A service call to validate GL&#xD;
this.executeGL = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
//	value.setEnabled(false);&#xD;
&#xD;
	var data = this.context.binding.get("value").get(index).get("debitedAccount").get("glAccountNo");&#xD;
	this.ui.get("CheckexistingGLAccount1["+index+"]").execute(data);&#xD;
}&#xD;
&#xD;
//On result of Validate GL - Map the result of GL service call&#xD;
this.validGlAccount = function (value){&#xD;
	record = value.ui.getIndex();&#xD;
	if (!this.context.binding.get("value").get(record).get("isGLFound") ) {&#xD;
		this.ui.get("glAccountNo["+record+"]").setValid(false, "Account Not Found!");&#xD;
		this.context.binding.get("value").get(record).set("glVerifyMSG","");&#xD;
	}else{&#xD;
		this.ui.get("glAccountNo["+record+"]").setValid(true);&#xD;
		this.context.binding.get("value").get(record).set("glVerifyMSG","Verified");&#xD;
	}&#xD;
}&#xD;
&#xD;
//On change of Account Cry - Apply vis and Call A service to get exchange rate between Default Cry and Account Cry&#xD;
this.setExchangeRate = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
	var cryCode = value.getData();&#xD;
//	var cryCode = this.context.binding.get("value").get(index).get("debitedAccount").get("currency").get("value");&#xD;
	if(cryCode != "" &amp;&amp; cryCode != null &amp;&amp; cryCode != undefined){&#xD;
		if (this.ui.get("defaultCurrency["+index+"]").getData() == cryCode) {&#xD;
			&#xD;
			this.ui.get("standardExchangeRate["+index+"]").setData(1.0);&#xD;
			this.ui.get("negotiatedExchangeRate["+index+"]").setData(1.0);&#xD;
			this.ui.get("negotiatedExchangeRate["+index+"]").setEnabled(false);&#xD;
		}else{&#xD;
//			this.ui.get("NegotiatedExchangeRate["+index+"]").setEnabled(false);&#xD;
			var defaultCurrency = this.ui.get("defaultCurrency["+index+"]").getData();&#xD;
			var accountCurrency = cryCode;&#xD;
&#xD;
			concatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:"TRANSFER" , sType:"S"};&#xD;
			var inputCurr = JSON.stringify(concatedCurrency);&#xD;
			this.ui.get("GetExchangeRate["+index+"]").execute(inputCurr);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
&#xD;
//On Result of get exchange rate&#xD;
this.setExServiceResults = function (value){&#xD;
	var index = value.ui.getIndex();&#xD;
&#xD;
	var rate = this.context.options.exRate.get("value");&#xD;
	this.ui.get("standardExchangeRate["+index+"]").setData(Number(rate.toFixed(6)));&#xD;
	this.ui.get("negotiatedExchangeRate["+index+"]").setData(Number(rate.toFixed(6)));&#xD;
	this.ui.get("negotiatedExchangeRate["+index+"]").setEnabled(true);&#xD;
}&#xD;
&#xD;
//On Debited Amount in Account Currency and Account Balance- validate is over Draft&#xD;
this.validateOverDraft = function (value) {&#xD;
	var index = value.ui.getIndex();&#xD;
	var accountBalance = this.ui.get("accountBalance["+index+"]").getData();&#xD;
	var debitedAmount = this.ui.get("debitedAmountInCurrency["+index+"]").getData();&#xD;
	&#xD;
	//Need to add &amp;&amp;accountClass is Customer Account--&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&gt;&#xD;
	if (debitedAmount &gt; accountBalance) {&#xD;
		if (this.context.binding.get("value").get(index).get("debitedAccount").get("isOverDraft") == true) {&#xD;
			this.ui.get("debitedAmountInCurrency["+index+"]").setValid(false,"WARNING: Must be &lt; Account Balance");&#xD;
		}else if (this.context.binding.get("value").get(index).get("debitedAccount").get("isOverDraft") == false){&#xD;
			this.ui.get("debitedAmountInCurrency["+index+"]").setValid(false,"Error: Must be &lt; Account Balance");&#xD;
		}&#xD;
	}else{&#xD;
		this.ui.get("debitedAmountInCurrency["+index+"]").setValid(true);&#xD;
	}&#xD;
}&#xD;
&#xD;
// Reusable function to set visibility and enabled state&#xD;
this.setUIVisibilityAndState = function (ui, index, config) {&#xD;
    for (var i = 0; i &lt; config.length; i++) {&#xD;
		var item = config[i];&#xD;
		ui.get(item.name + "[" + index + "]").setVisible(item.visible, true);&#xD;
		if (item.enabled !== undefined) {&#xD;
			ui.get(item.name + "[" + index + "]").setEnabled(item.enabled);&#xD;
		}&#xD;
		if (item.data !== undefined) {&#xD;
			ui.get(item.name + "[" + index + "]").setData(item.data);&#xD;
		}&#xD;
    }&#xD;
};&#xD;
&#xD;
// Handle Customer Account&#xD;
this.handleCustomerAccount = function (index, isChecker) {&#xD;
    var config = [&#xD;
        { name: "balanceSign", visible: true },&#xD;
        { name: "accountBalance", visible: true },&#xD;
        { name: "customerAccountNo", visible: true, enabled: !isChecker },&#xD;
        { name: "verifyGLButton", visible: false },&#xD;
        { name: "glAccountNo", visible: false },&#xD;
        { name: "GLSection1", visible: false },&#xD;
        { name: "accountCurrency", enabled: false }&#xD;
    ];&#xD;
&#xD;
    if (!isChecker) {&#xD;
        config.push({ name: "glAccountNo", data: "" });&#xD;
    } else {&#xD;
        config.push({ name: "customerAccountNo", enabled: false });&#xD;
    }&#xD;
&#xD;
    this.setUIVisibilityAndState(this.ui, index, config);&#xD;
    this.ui.get("accountBranchCode[" + index + "]").setEnabled(false);&#xD;
};&#xD;
&#xD;
// Handle GL Account&#xD;
this.handleGLAccount = function (index, isChecker) {&#xD;
    var config = [&#xD;
        { name: "balanceSign", visible: false },&#xD;
        { name: "accountBalance", visible: false },&#xD;
        { name: "customerAccountNo", visible: false },&#xD;
        { name: "glAccountNo", visible: true, enabled: !isChecker },&#xD;
        { name: "GLSection1", visible: true, enabled: !isChecker },&#xD;
        { name: "verifyGLButton", visible: true, enabled: !isChecker },&#xD;
        { name: "accountCurrency", enabled: !isChecker },&#xD;
        { name: "accountBranchCode", enabled: !isChecker }&#xD;
    ];&#xD;
&#xD;
    if (!isChecker) {&#xD;
        config.push({ name: "customerAccountNo", data: null });&#xD;
        this.context.binding.get("value").get(index).get("debitedAccount").set("isOverDraft", null);&#xD;
    }&#xD;
&#xD;
    this.setUIVisibilityAndState(this.ui, index, config);&#xD;
};&#xD;
&#xD;
// Main function&#xD;
this.accountNumVis = function (value) {&#xD;
    var index = value;&#xD;
    var accountClass = this.context.binding.get("value").get(index).get("debitedAccount").get("accountClass").get("name");&#xD;
    var isChecker = this.context.options.isChecker.get("value");&#xD;
&#xD;
    if (accountClass === "001") {&#xD;
        // Customer&#xD;
        this.handleCustomerAccount(index, isChecker);&#xD;
    } else {&#xD;
        // GL&#xD;
        this.handleGLAccount(index, isChecker);&#xD;
    }&#xD;
};&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>4d978b13-e676-40ac-b4c8-84b9e3b14b57</guid>
            <versionId>761c0fcb-2fc8-4cb0-9ebb-671f24aa0045</versionId>
        </inlineScript>
        <inlineScript name="Inline CSS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.a06fbc97-7feb-4160-9284-1212f1389ae5</coachViewInlineScriptId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <scriptType>CSS</scriptType>
            <scriptBlock>.panel-heading {&#xD;
    display: flex; /* Use flexbox for layout */&#xD;
    align-items: center; /* Center content vertically */&#xD;
    justify-content: space-between; /* Distribute space between title and controls */&#xD;
    height: 50px; /* Fixed height for the panel heading */&#xD;
    padding: 0 10px; /* Add horizontal padding */&#xD;
    box-sizing: border-box; /* Include padding and border in height calculations */&#xD;
    overflow: hidden; /* Hide any overflowing content */&#xD;
}&#xD;
&#xD;
.panel-title {&#xD;
    flex: 1; /* Allow title to grow and fill available space */&#xD;
    white-space: nowrap; /* Prevent text from wrapping */&#xD;
    overflow: hidden; /* Hide overflow text */&#xD;
    text-overflow: ellipsis; /* Add ellipsis if text overflows */&#xD;
    font-size: 14px; /* Adjust font size to make the title smaller */&#xD;
    line-height: 1; /* Adjust line height for better text fitting */&#xD;
    margin: 0; /* Ensure no extra margin affecting layout */&#xD;
}&#xD;
&#xD;
.panel-heading-controls {&#xD;
    display: flex; /* Use flexbox for layout */&#xD;
    align-items: center; /* Center content vertically */&#xD;
    margin-left: auto; /* Push controls to the right */&#xD;
}&#xD;
</scriptBlock>
            <seq>1</seq>
            <description></description>
            <guid>26bae596-96ab-4639-b699-bbb52ce8d89b</guid>
            <versionId>a0f85647-5fe3-4cc0-a949-fced5c1566a9</versionId>
        </inlineScript>
        <localization>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewLocalResId>69.6edc6057-e193-4ab2-8171-660f10c5f7fa</coachViewLocalResId>
            <coachViewId>64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52</coachViewId>
            <resourceBundleGroupId>/50.41101508-d2e4-4682-b3ef-b9b22266bb5a</resourceBundleGroupId>
            <seq>0</seq>
            <guid>d15b49ec-c960-4c8f-aedb-7c8bd83aa7ff</guid>
            <versionId>899d0ce8-edb7-4c59-bda9-de2b581ce9d0</versionId>
        </localization>
    </coachView>
</teamworks>

