<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.769a8281-bf53-46c6-a4c8-571fc192f312" name="Get charge amount">
        <lastModified>1693306559292</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac7</guid>
        <versionId>ce4d2ec4-0272-4163-b263-5eeb0cf5d121</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:415d794a2c221205:3dfd662b:18a40cdf641:-675d" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.37f043b2-11e9-4bd0-858d-63872e8023b5"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a8cd51dc-3aa4-4475-865f-9ec8390037eb"},{"incoming":["e77578d9-df76-4af6-83dc-1357a80ea9c9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"119f61bd-9f2d-4d6f-8080-df5cce2b26e3"},{"targetRef":"c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Calculate charge amount","declaredType":"sequenceFlow","id":"2027.37f043b2-11e9-4bd0-858d-63872e8023b5","sourceRef":"a8cd51dc-3aa4-4475-865f-9ec8390037eb"},{"startQuantity":1,"outgoing":["83dd2b39-0127-4988-88f7-f0943f60f1ed"],"incoming":["2027.37f043b2-11e9-4bd0-858d-63872e8023b5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":173,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Calculate charge amount","dataInputAssociation":[{"targetRef":"2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.94ff52b2-0d25-41a6-810b-8c71f86202a8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["String(tw.local.inAmount)"]}}]},{"targetRef":"2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["String(tw.local.percentage)"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.outAmount"]}}],"sourceRef":["2055.45c20ab6-7f17-473e-891c-c8352d52a7c0"]}],"calledElement":"1.c63132b4-26e8-4922-945e-0c52767485a5"},{"targetRef":"6bf1cb36-0432-4f06-8881-3d4e4339ed1f","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Map output","declaredType":"sequenceFlow","id":"83dd2b39-0127-4988-88f7-f0943f60f1ed","sourceRef":"c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5"},{"startQuantity":1,"outgoing":["e77578d9-df76-4af6-83dc-1357a80ea9c9"],"incoming":["83dd2b39-0127-4988-88f7-f0943f60f1ed"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":435,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"6bf1cb36-0432-4f06-8881-3d4e4339ed1f","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.outAmount != null &amp;&amp; tw.local.outAmount != \"\")\r\n{\r\n\ttw.local.chargeAmount = parseFloat(tw.local.outAmount);\r\n}\r\n"]}},{"targetRef":"119f61bd-9f2d-4d6f-8080-df5cce2b26e3","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e77578d9-df76-4af6-83dc-1357a80ea9c9","sourceRef":"6bf1cb36-0432-4f06-8881-3d4e4339ed1f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"outAmount","isCollection":false,"declaredType":"dataObject","id":"2056.9a9fd774-d130-4b02-8199-fdd492810dc4"},{"parallelMultiple":false,"outgoing":["4f50bf67-387c-45a7-8088-dd2fd995be02"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"02fa5b08-f8cf-4e1b-8427-e17c5230f9dd"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"5138356b-9d8f-4837-8190-76daf3c0619f","otherAttributes":{"eventImplId":"17258a8a-7070-4529-8248-5b6a4ed2b5d9"}}],"attachedToRef":"c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5","extensionElements":{"nodeVisualInfo":[{"width":24,"x":208,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"f07a50d9-ceb4-40e0-8161-d09ff7c20290","outputSet":{}},{"parallelMultiple":false,"outgoing":["e64efbbe-caf6-41ea-893b-b52bd2827b42"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"6739d0c2-4b9a-415e-87c1-1476fc27e740"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"d9e8c94f-7dcd-46b4-85b6-9c0357e9771f","otherAttributes":{"eventImplId":"72427500-9373-4d6d-84b1-798b06e5df62"}}],"attachedToRef":"6bf1cb36-0432-4f06-8881-3d4e4339ed1f","extensionElements":{"nodeVisualInfo":[{"width":24,"x":470,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"fb9f7db3-4ba4-4d6d-85c8-4a6b83cf7e9b","outputSet":{}},{"incoming":["4f50bf67-387c-45a7-8088-dd2fd995be02","e64efbbe-caf6-41ea-893b-b52bd2827b42"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"ea7eb355-fae6-4ea6-8270-38e5ff07d8cf","otherAttributes":{"eventImplId":"47a5d1b5-943c-4c59-8c1f-88bf865eb93c"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":282,"y":217,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.ajaxError"]}}]}],"declaredType":"endEvent","id":"2ea90e49-f179-48b3-80fb-c0feb6afa3ce"},{"targetRef":"2ea90e49-f179-48b3-80fb-c0feb6afa3ce","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"4f50bf67-387c-45a7-8088-dd2fd995be02","sourceRef":"f07a50d9-ceb4-40e0-8161-d09ff7c20290"},{"targetRef":"2ea90e49-f179-48b3-80fb-c0feb6afa3ce","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"e64efbbe-caf6-41ea-893b-b52bd2827b42","sourceRef":"fb9f7db3-4ba4-4d6d-85c8-4a6b83cf7e9b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();\nautoObject.errorText = \"\";\nautoObject.errorCode = \"\";\nautoObject.serviceInError = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"ajaxError","isCollection":false,"declaredType":"dataObject","id":"2056.0890f907-9416-44da-8929-6d4b35030530"}],"laneSet":[{"id":"bbe355a7-d52e-43bb-84d0-49d415586889","lane":[{"flowNodeRef":["a8cd51dc-3aa4-4475-865f-9ec8390037eb","119f61bd-9f2d-4d6f-8080-df5cce2b26e3","c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5","6bf1cb36-0432-4f06-8881-3d4e4339ed1f","f07a50d9-ceb4-40e0-8161-d09ff7c20290","fb9f7db3-4ba4-4d6d-85c8-4a6b83cf7e9b","2ea90e49-f179-48b3-80fb-c0feb6afa3ce"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"0474612b-6b8c-42f5-8e34-1f03a8ffe05a","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get charge amount","declaredType":"process","id":"1.769a8281-bf53-46c6-a4c8-571fc192f312","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"chargeAmount","isCollection":false,"id":"2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648"}],"inputSet":[{"dataInputRefs":["2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8","2055.23a7051d-b8b1-4a36-8c49-592e7028cd89"]}],"outputSet":[{"dataOutputRefs":["2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"100"}]},"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"inAmount","isCollection":false,"id":"2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"0.5"}]},"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"percentage","isCollection":false,"id":"2055.23a7051d-b8b1-4a36-8c49-592e7028cd89"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="inAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8</processParameterId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>100</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3d126880-04de-4b24-a908-144bb6e34826</guid>
            <versionId>825d2643-b023-4494-9550-5e7bbadfd0e1</versionId>
        </processParameter>
        <processParameter name="percentage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.23a7051d-b8b1-4a36-8c49-592e7028cd89</processParameterId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>0.5</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cacc5dac-8096-4946-a1f6-491428a2e4b8</guid>
            <versionId>74cfa7c7-155c-475e-8a20-cb28cb13fa61</versionId>
        </processParameter>
        <processParameter name="chargeAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648</processParameterId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>65c4cf00-975f-4b00-8ced-8fbb266326f8</guid>
            <versionId>3dbea42b-da24-4f26-8270-ee8e19819725</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b083a9e4-465b-417a-bec2-083366eef1fa</processParameterId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4bdfd137-f3d4-46c9-8679-45a2b71f1dc5</guid>
            <versionId>3c78d897-a6cf-44ea-b122-af5ffc5b01c9</versionId>
        </processParameter>
        <processVariable name="outAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9a9fd774-d130-4b02-8199-fdd492810dc4</processVariableId>
            <description isNull="true" />
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a5b4cf40-6091-40ec-942e-c6249018bf8a</guid>
            <versionId>d89b0c31-1459-4dc9-bd87-fa6df5b1cb43</versionId>
        </processVariable>
        <processVariable name="ajaxError">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0890f907-9416-44da-8929-6d4b35030530</processVariableId>
            <description isNull="true" />
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</defaultValue>
            <guid>40369fa6-fdb1-4710-a659-8a3b75f79f39</guid>
            <versionId>9ce897e0-6994-4448-97f3-c8705376ede4</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5</processItemId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <name>Calculate charge amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2ea90e49-f179-48b3-80fb-c0feb6afa3ce</errorHandlerItemId>
            <guid>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac1</guid>
            <versionId>6595bd5a-dca5-4595-89dd-c7da506c3d5a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.98a47027-c65e-4533-b9dc-35e5309db43d</processItemPrePostId>
                <processItemId>2025.c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>1cdbc3f7-a2a6-4f2a-a59e-aa95b17afeb6</guid>
                <versionId>c6def5c3-b77e-4054-962c-fc96058a3330</versionId>
            </processPrePosts>
            <layoutData x="173" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:415d794a2c221205:3dfd662b:18a3c92e735:-aa1</errorHandlerItem>
                <errorHandlerItemId>2025.2ea90e49-f179-48b3-80fb-c0feb6afa3ce</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3775e678-887d-46d6-867c-83a43f1a803a</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.c63132b4-26e8-4922-945e-0c52767485a5</attachedProcessRef>
                <guid>4fb1968c-e923-456f-af81-5b79153808dd</guid>
                <versionId>07def9a3-2168-4151-b10f-e58e246aee7d</versionId>
                <parameterMapping name="Percentage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.dddaaeb3-638b-484f-aa1a-4c23b9aba832</parameterMappingId>
                    <processParameterId>2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>String(tw.local.percentage)</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d8b49c35-2ec9-422b-a1e8-ebd63b0d90e1</guid>
                    <versionId>1010661f-4560-4f49-87d7-cbc820ca92a2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.29f3519e-3665-4c36-b86f-af3b7ded3302</parameterMappingId>
                    <processParameterId>2055.aa2d49db-103b-4ad9-8e57-95487013a0fc</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5caeb0ba-1c4f-4959-94e6-0bdf1adc6422</guid>
                    <versionId>4fa80e23-2f40-4d9d-b6d8-383aee242daa</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.286ca35c-a4f2-4876-b515-2d392a5be438</parameterMappingId>
                    <processParameterId>2055.8420386c-2f0f-431a-8b39-de0080607008</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a0566269-**************-2ae73863534a</guid>
                    <versionId>6cf0582b-85f7-4bf0-8253-efd2b35605d9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ad9d9c87-b890-47e1-934c-1703d51f0eab</parameterMappingId>
                    <processParameterId>2055.94ff52b2-0d25-41a6-810b-8c71f86202a8</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>String(tw.local.inAmount)</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c1b36639-4221-455e-b7f7-3e431498eb58</guid>
                    <versionId>76271f18-f841-4cd4-a226-3280ba606b9d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bd40a098-52fc-4609-9a47-f7aa837ad54f</parameterMappingId>
                    <processParameterId>2055.234755dd-aa6d-47c3-8a5d-f6860d21d2da</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b701f795-0ef6-4d56-972c-9adc5a433f20</guid>
                    <versionId>7f1f7b46-e6c3-49fe-8aa4-135f40b1cce5</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.16958cdc-cad2-45f4-a1ee-2608c7712a62</parameterMappingId>
                    <processParameterId>2055.8c593a04-d1d3-40d2-8288-991e270ebc48</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fa26a8e1-5051-4070-b5e3-441f8cb01cee</guid>
                    <versionId>867c759d-0505-4b44-b5c7-ab6e73966ce7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.aeb0e172-aac9-4fd1-a057-4cea19469167</parameterMappingId>
                    <processParameterId>2055.d08ff4c0-0f8d-47b1-8bd2-ff091c64a490</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>99d271a4-a64c-4d2e-8604-92adeb0e12d5</guid>
                    <versionId>9afd65a9-a06b-4f22-9f8d-75934710e3cb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.11c982b0-41b2-42d2-a137-6c6b2d55534b</parameterMappingId>
                    <processParameterId>2055.9cf99306-b011-40e1-852b-7c1366b243e7</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0c205366-9fa9-4c28-b473-0273d546a552</guid>
                    <versionId>9c174dba-097e-40b1-a607-3d4bfb3cebc1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e515a6d2-8870-4ec6-bd72-e85f935d9aa2</parameterMappingId>
                    <processParameterId>2055.03bb44c7-2ddc-40f7-8cf9-25e5eacb2c37</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>078106e2-229e-40ad-814d-838e8968058a</guid>
                    <versionId>9e4ec631-5a25-4b41-8fd6-b6840ceff0d1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9ff924be-dc7c-4245-b2a4-401a93cf7ff1</parameterMappingId>
                    <processParameterId>2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>37197276-ccea-43f4-9536-640800d84fc6</guid>
                    <versionId>a6405d38-3184-42ec-9663-d9c20113bf0a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="chargeAmount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d9d4f175-34dd-4c1b-983c-91515630d410</parameterMappingId>
                    <processParameterId>2055.45c20ab6-7f17-473e-891c-c8352d52a7c0</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.outAmount</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a5cbd66d-f394-4e57-a10f-acb755c4bc3b</guid>
                    <versionId>ba38183f-afcd-44ba-be3c-b3fe97fa8897</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f2632d13-95c5-4027-a6f3-b0bcf86f247d</parameterMappingId>
                    <processParameterId>2055.ec819699-1ccd-4c1b-8a08-e574e43b9765</processParameterId>
                    <parameterMappingParentId>3012.3775e678-887d-46d6-867c-83a43f1a803a</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>80962e9d-dacb-49c7-8251-bbfd57d9f64a</guid>
                    <versionId>cc9196a0-f2ab-4e25-8f5d-cc3ea0660eed</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6bf1cb36-0432-4f06-8881-3d4e4339ed1f</processItemId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.ffd38f4b-eef9-4362-ad6e-a8eb1d6b0ff0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.2ea90e49-f179-48b3-80fb-c0feb6afa3ce</errorHandlerItemId>
            <guid>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac0</guid>
            <versionId>67f0ef33-8cbd-4fd1-bf07-e0308f3931d0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="435" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:415d794a2c221205:3dfd662b:18a3c92e735:-aa1</errorHandlerItem>
                <errorHandlerItemId>2025.2ea90e49-f179-48b3-80fb-c0feb6afa3ce</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.ffd38f4b-eef9-4362-ad6e-a8eb1d6b0ff0</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.outAmount != null &amp;&amp; tw.local.outAmount != "")&#xD;
{&#xD;
	tw.local.chargeAmount = parseFloat(tw.local.outAmount);&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>899725e5-ebb8-43ef-872f-54fe52e1bb62</guid>
                <versionId>3fa70e1f-68f3-4963-add0-dd1ef976a25c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.119f61bd-9f2d-4d6f-8080-df5cce2b26e3</processItemId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ac148049-45ba-4425-888f-4b5f02e82dd1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5</guid>
            <versionId>aa80b1ea-1075-41a7-9b31-ed8fb3e89a34</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ac148049-45ba-4425-888f-4b5f02e82dd1</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>f0ab5a20-acf0-4ebb-aeb7-d3df70808df0</guid>
                <versionId>907ed241-38d1-48bd-af44-3f3e3bc627a0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2ea90e49-f179-48b3-80fb-c0feb6afa3ce</processItemId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.914bd6e7-a325-4a45-96ff-85c80712abc7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a3c92e735:-aa1</guid>
            <versionId>ac511c56-3cc6-433a-b4bd-3e506528acd7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="282" y="217">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.914bd6e7-a325-4a45-96ff-85c80712abc7</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>26da5b16-49c0-4757-8c5d-0cc20164596a</guid>
                <versionId>8e548afb-2da6-49a6-8358-3b843b56a75a</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.eaf13c3b-f801-4a96-b4c5-e59d6dcd7da1</parameterMappingId>
                    <processParameterId>2055.b083a9e4-465b-417a-bec2-083366eef1fa</processParameterId>
                    <parameterMappingParentId>3007.914bd6e7-a325-4a45-96ff-85c80712abc7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.ajaxError</value>
                    <classRef>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c6aef732-93d6-4c57-8002-b20b1ab38290</guid>
                    <versionId>51ab5aa9-87cd-4ff6-a58a-f4a18d5b0829</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get charge amount" id="1.769a8281-bf53-46c6-a4c8-571fc192f312" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="inAmount" itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" id="2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">100</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="percentage" itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" id="2055.23a7051d-b8b1-4a36-8c49-592e7028cd89">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">0.5</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="chargeAmount" itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" id="2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.01f1e1f8-7d3c-4d83-855c-82d38410ddd8</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.23a7051d-b8b1-4a36-8c49-592e7028cd89</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.7ab68b78-4d7f-4c2e-87f6-c360eec39648</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="bbe355a7-d52e-43bb-84d0-49d415586889">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="0474612b-6b8c-42f5-8e34-1f03a8ffe05a" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>a8cd51dc-3aa4-4475-865f-9ec8390037eb</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>119f61bd-9f2d-4d6f-8080-df5cce2b26e3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6bf1cb36-0432-4f06-8881-3d4e4339ed1f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f07a50d9-ceb4-40e0-8161-d09ff7c20290</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>fb9f7db3-4ba4-4d6d-85c8-4a6b83cf7e9b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2ea90e49-f179-48b3-80fb-c0feb6afa3ce</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="a8cd51dc-3aa4-4475-865f-9ec8390037eb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.37f043b2-11e9-4bd0-858d-63872e8023b5</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="119f61bd-9f2d-4d6f-8080-df5cce2b26e3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-ac5</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e77578d9-df76-4af6-83dc-1357a80ea9c9</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="a8cd51dc-3aa4-4475-865f-9ec8390037eb" targetRef="c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5" name="To Calculate charge amount" id="2027.37f043b2-11e9-4bd0-858d-63872e8023b5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.c63132b4-26e8-4922-945e-0c52767485a5" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Calculate charge amount" id="c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="173" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.37f043b2-11e9-4bd0-858d-63872e8023b5</ns16:incoming>
                        
                        
                        <ns16:outgoing>83dd2b39-0127-4988-88f7-f0943f60f1ed</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9ceb1d2b-0525-416e-8984-1420ff70b0e7</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94ff52b2-0d25-41a6-810b-8c71f86202a8</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">String(tw.local.inAmount)</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cd5e399a-eaf7-49a8-8ea9-ee525fa22977</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">String(tw.local.percentage)</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.45c20ab6-7f17-473e-891c-c8352d52a7c0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.outAmount</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5" targetRef="6bf1cb36-0432-4f06-8881-3d4e4339ed1f" name="To Map output" id="83dd2b39-0127-4988-88f7-f0943f60f1ed">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="6bf1cb36-0432-4f06-8881-3d4e4339ed1f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="435" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>83dd2b39-0127-4988-88f7-f0943f60f1ed</ns16:incoming>
                        
                        
                        <ns16:outgoing>e77578d9-df76-4af6-83dc-1357a80ea9c9</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.outAmount != null &amp;&amp; tw.local.outAmount != "")&#xD;
{&#xD;
	tw.local.chargeAmount = parseFloat(tw.local.outAmount);&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="6bf1cb36-0432-4f06-8881-3d4e4339ed1f" targetRef="119f61bd-9f2d-4d6f-8080-df5cce2b26e3" name="To End" id="e77578d9-df76-4af6-83dc-1357a80ea9c9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="outAmount" id="2056.9a9fd774-d130-4b02-8199-fdd492810dc4" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5" parallelMultiple="false" name="Error" id="f07a50d9-ceb4-40e0-8161-d09ff7c20290">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="208" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>4f50bf67-387c-45a7-8088-dd2fd995be02</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="02fa5b08-f8cf-4e1b-8427-e17c5230f9dd" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="5138356b-9d8f-4837-8190-76daf3c0619f" eventImplId="17258a8a-7070-4529-8248-5b6a4ed2b5d9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6bf1cb36-0432-4f06-8881-3d4e4339ed1f" parallelMultiple="false" name="Error1" id="fb9f7db3-4ba4-4d6d-85c8-4a6b83cf7e9b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="470" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e64efbbe-caf6-41ea-893b-b52bd2827b42</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="6739d0c2-4b9a-415e-87c1-1476fc27e740" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d9e8c94f-7dcd-46b4-85b6-9c0357e9771f" eventImplId="72427500-9373-4d6d-84b1-798b06e5df62">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="2ea90e49-f179-48b3-80fb-c0feb6afa3ce">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="282" y="217" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4f50bf67-387c-45a7-8088-dd2fd995be02</ns16:incoming>
                        
                        
                        <ns16:incoming>e64efbbe-caf6-41ea-893b-b52bd2827b42</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.ajaxError</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="ea7eb355-fae6-4ea6-8270-38e5ff07d8cf" eventImplId="47a5d1b5-943c-4c59-8c1f-88bf865eb93c">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f07a50d9-ceb4-40e0-8161-d09ff7c20290" targetRef="2ea90e49-f179-48b3-80fb-c0feb6afa3ce" name="To End Event" id="4f50bf67-387c-45a7-8088-dd2fd995be02">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="fb9f7db3-4ba4-4d6d-85c8-4a6b83cf7e9b" targetRef="2ea90e49-f179-48b3-80fb-c0feb6afa3ce" name="To End Event" id="e64efbbe-caf6-41ea-893b-b52bd2827b42">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="ajaxError" id="2056.0890f907-9416-44da-8929-6d4b35030530">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.toolkit.SYSBPMUI.AjaxError();
autoObject.errorText = "";
autoObject.errorCode = "";
autoObject.serviceInError = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e77578d9-df76-4af6-83dc-1357a80ea9c9</processLinkId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6bf1cb36-0432-4f06-8881-3d4e4339ed1f</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.119f61bd-9f2d-4d6f-8080-df5cce2b26e3</toProcessItemId>
            <guid>d18ecd0c-9120-4b4a-a720-d986724f82cd</guid>
            <versionId>a1b9918e-9ec0-4565-8256-45c67755b949</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6bf1cb36-0432-4f06-8881-3d4e4339ed1f</fromProcessItemId>
            <toProcessItemId>2025.119f61bd-9f2d-4d6f-8080-df5cce2b26e3</toProcessItemId>
        </link>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.83dd2b39-0127-4988-88f7-f0943f60f1ed</processLinkId>
            <processId>1.769a8281-bf53-46c6-a4c8-571fc192f312</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5</fromProcessItemId>
            <endStateId>guid:415d794a2c221205:3dfd662b:18a3c92e735:-1603</endStateId>
            <toProcessItemId>2025.6bf1cb36-0432-4f06-8881-3d4e4339ed1f</toProcessItemId>
            <guid>71ec8890-8487-4393-841f-a58246d14c13</guid>
            <versionId>d5571a88-b983-489a-99bd-05f3ccfb1d90</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.c3fdcd60-6d9f-4b83-89af-39ef46a1d0e5</fromProcessItemId>
            <toProcessItemId>2025.6bf1cb36-0432-4f06-8881-3d4e4339ed1f</toProcessItemId>
        </link>
    </process>
</teamworks>

