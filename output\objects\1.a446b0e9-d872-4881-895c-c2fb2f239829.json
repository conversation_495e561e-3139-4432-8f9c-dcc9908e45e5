{"id": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "versionId": "863323c5-c9f1-4ebb-8bb6-835644e37b23", "name": "retrieve charges and commision data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "retrieve charges and commision data", "lastModified": "1696416761520", "lastModifiedBy": "heba", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.d7e820d3-f31e-4cad-a101-af69f52fee15", "2025.d7e820d3-f31e-4cad-a101-af69f52fee15"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "fd886778-38a0-4542-ae6f-f62891f92825", "versionId": "863323c5-c9f1-4ebb-8bb6-835644e37b23", "dependencySummary": "<dependencySummary id=\"bpdid:cdc758d910e20d2d:-607ed7:18af686ab01:4709\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.6e5778ba-deba-49fb-bba9-30278c6de318\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"1ffac7db-691e-4fed-84f4-ada9e55e31e3\"},{\"incoming\":[\"e1007472-53ad-4074-b2d1-469a70da4f75\",\"0d710df0-44c9-4895-8999-9b80b8058eea\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4166\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"17d88b7a-bf19-4f4e-80bc-fe76fc9246dc\"},{\"targetRef\":\"d7e820d3-f31e-4cad-a101-af69f52fee15\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.6e5778ba-deba-49fb-bba9-30278c6de318\",\"sourceRef\":\"1ffac7db-691e-4fed-84f4-ada9e55e31e3\"},{\"startQuantity\":1,\"outgoing\":[\"d26e632a-96f7-4251-8862-b2f92a23ee7b\"],\"incoming\":[\"2027.6e5778ba-deba-49fb-bba9-30278c6de318\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":107,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Script Task\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"d7e820d3-f31e-4cad-a101-af69f52fee15\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sql = \\\"select COMPONENT , DEFAULTCURRENCY, DEFAULTAMOUNT , CHARGEAMOUNT , ACCOUNTCLASS , ACCOUNTNO , BranchCode,  \\\"\\r\\n+\\\"  currency , balance , balancesign , STANDARDEXRATE , NEGOTIATEDEXRATE , debitedamount from ODC_CHARGESCOMMISSIONS    \\\"\\r\\n+\\\"   where requesrid  = '\\\"+tw.local.requestId+\\\"' \\\";\"]}},{\"startQuantity\":1,\"outgoing\":[\"e1007472-53ad-4074-b2d1-469a70da4f75\"],\"incoming\":[\"ca1718a9-5610-47c8-b084-10e0c3f46fa9\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":477,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Mapping output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"3bf34780-13c3-4a0a-9de5-48588d919e83\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/tw.local.odcRequest = {};\\r\\ntw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\r\\n\\r\\n\\r\\nif(tw.local.sqlResults.listLength > 0)\\r\\n{\\r\\n\\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\\r\\n\\t{\\t\\r\\n\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i]  \\t\\t\\t              = new tw.object.ChargesAndCommissions();\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].component   \\t\\t        = tw.local.sqlResults[0].rows[i].data[0];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].defaultAmount                = tw.local.sqlResults[0].rows[i].data[1];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency \\t        = new tw.object.NameValuePair();\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value        = tw.local.sqlResults[0].rows[i].data[2];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount                 = tw.local.sqlResults[0].rows[i].data[3];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount               = new tw.object.AccountDetails();\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass  = new tw.object.NameValuePair();\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value   = tw.local.sqlResults[0].rows[i].data[4];\\r\\n\\t\\t\\/\\/\\/ need to check for charge and comm account type to know which field should be filled\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo    = tw.local.sqlResults[0].rows[i].data[5];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo          = tw.local.sqlResults[0].rows[i].data[5];\\r\\n\\t\\t\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode   = tw.local.sqlResults[0].rows[i].data[6];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency     = new tw.object.NameValuePair();\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value    = tw.local.sqlResults[0].rows[i].data[7];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance      = tw.local.sqlResults[0].rows[i].data[8];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount =new tw.object.AmountDetails();\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign       = tw.local.sqlResults[0].rows[i].data[9];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate     = tw.local.sqlResults[0].rows[i].data[10];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate   = tw.local.sqlResults[0].rows[i].data[11];\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount    = tw.local.sqlResults[0].rows[i].data[12];\\r\\n\\t\\t\\r\\n\\t}\\r\\n\\r\\n}\"]}},{\"startQuantity\":1,\"outgoing\":[\"ca1718a9-5610-47c8-b084-10e0c3f46fa9\"],\"incoming\":[\"d26e632a-96f7-4251-8862-b2f92a23ee7b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":291,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Linked Service Flow\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"********-897d-4de1-9d6a-19dc455219b9\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlResults\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"********-897d-4de1-9d6a-19dc455219b9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Linked Service Flow\",\"declaredType\":\"sequenceFlow\",\"id\":\"d26e632a-96f7-4251-8862-b2f92a23ee7b\",\"sourceRef\":\"d7e820d3-f31e-4cad-a101-af69f52fee15\"},{\"targetRef\":\"3bf34780-13c3-4a0a-9de5-48588d919e83\",\"extensionElements\":{\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Mapping output\",\"declaredType\":\"sequenceFlow\",\"id\":\"ca1718a9-5610-47c8-b084-10e0c3f46fa9\",\"sourceRef\":\"********-897d-4de1-9d6a-19dc455219b9\"},{\"targetRef\":\"17d88b7a-bf19-4f4e-80bc-fe76fc9246dc\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e1007472-53ad-4074-b2d1-469a70da4f75\",\"sourceRef\":\"3bf34780-13c3-4a0a-9de5-48588d919e83\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.14d60858-b96c-4d2c-af31-************\"},{\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"sqlParameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.44cbd19f-423d-4fcb-bdae-97921873266b\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"sqlResults\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.90bc53d6-b54b-4ae1-80c2-6c27b12b98ba\"},{\"parallelMultiple\":false,\"outgoing\":[\"c35bfd91-213e-4d09-865f-9428036a17a8\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"f6d1e427-d87e-4e79-80fd-b67c9d2d16ab\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"7b52b5c2-76f0-45d7-8d0d-c361959890bc\",\"otherAttributes\":{\"eventImplId\":\"8186bf52-afeb-4977-8d98-c467cd71befe\"}}],\"attachedToRef\":\"3bf34780-13c3-4a0a-9de5-48588d919e83\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":512,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"50b55d00-565a-4197-876b-cbe3124abf66\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"96ce6370-b5b8-42b2-8d1a-a09c66e03b7c\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"aa2f0d59-2dec-4731-86aa-952206dc507b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"cc59e553-931b-4dcb-81b8-213d5e7f7a19\",\"otherAttributes\":{\"eventImplId\":\"8a3b962b-44b2-4521-8f43-a2e3a0783eb7\"}}],\"attachedToRef\":\"********-897d-4de1-9d6a-19dc455219b9\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":326,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"21f15e7d-376c-436d-899d-f01e1f37bc63\",\"outputSet\":{}},{\"startQuantity\":1,\"outgoing\":[\"0d710df0-44c9-4895-8999-9b80b8058eea\"],\"incoming\":[\"96ce6370-b5b8-42b2-8d1a-a09c66e03b7c\",\"c35bfd91-213e-4d09-865f-9428036a17a8\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":453,\"y\":179,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Error Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Retrieve Charges and commisions data\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"f9fad62d-c5d1-4c5e-8659-799400d6e866\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"f9fad62d-c5d1-4c5e-8659-799400d6e866\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"96ce6370-b5b8-42b2-8d1a-a09c66e03b7c\",\"sourceRef\":\"21f15e7d-376c-436d-899d-f01e1f37bc63\"},{\"targetRef\":\"f9fad62d-c5d1-4c5e-8659-799400d6e866\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"c35bfd91-213e-4d09-865f-9428036a17a8\",\"sourceRef\":\"50b55d00-565a-4197-876b-cbe3124abf66\"},{\"targetRef\":\"17d88b7a-bf19-4f4e-80bc-fe76fc9246dc\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"0d710df0-44c9-4895-8999-9b80b8058eea\",\"sourceRef\":\"f9fad62d-c5d1-4c5e-8659-799400d6e866\"}],\"laneSet\":[{\"id\":\"dd3f9c25-4a8b-4ab8-920c-a5df8fa8a9a1\",\"lane\":[{\"flowNodeRef\":[\"1ffac7db-691e-4fed-84f4-ada9e55e31e3\",\"17d88b7a-bf19-4f4e-80bc-fe76fc9246dc\",\"d7e820d3-f31e-4cad-a101-af69f52fee15\",\"3bf34780-13c3-4a0a-9de5-48588d919e83\",\"********-897d-4de1-9d6a-19dc455219b9\",\"50b55d00-565a-4197-876b-cbe3124abf66\",\"21f15e7d-376c-436d-899d-f01e1f37bc63\",\"f9fad62d-c5d1-4c5e-8659-799400d6e866\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"91a78326-0295-455c-bf29-3ca0da271acd\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"retrieve charges and commision data\",\"declaredType\":\"process\",\"id\":\"1.a446b0e9-d872-4881-895c-c2fb2f239829\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.62e96889-79d7-4e51-9564-d2732313593f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.09d42e03-72b8-46a0-882c-e4c44d810906\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.62e96889-79d7-4e51-9564-d2732313593f\",\"2055.09d42e03-72b8-46a0-882c-e4c44d810906\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false}]},\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"requestId\",\"isCollection\":false,\"id\":\"2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "fd7a8e78-bd65-42e0-bca9-5aa509ec294a", "versionId": "46179a55-f11e-492c-89b2-954a561e5cd3"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.62e96889-79d7-4e51-9564-d2732313593f", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "cf65f11a-ff0d-466e-aca3-9ee614c985dd", "versionId": "fc27f23b-ec39-415f-8cf6-d4aea6bcb32c"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.09d42e03-72b8-46a0-882c-e4c44d810906", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "72096789-4e19-4368-84fe-22a557362d6a", "versionId": "7a1c05c6-1c4a-4ed5-a7b1-b9c68cea6a85"}], "processVariable": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.14d60858-b96c-4d2c-af31-************", "description": {"isNull": "true"}, "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9db259aa-77ca-4070-ac3c-acc27c06f401", "versionId": "05342c96-9475-4ea4-a280-410fdd01b5c3"}, {"name": "sqlParameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.44cbd19f-423d-4fcb-bdae-97921873266b", "description": {"isNull": "true"}, "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ebc2be9c-b285-4655-bf4d-1ead6e6c4d53", "versionId": "bb95a82a-d29b-4276-8b8a-56ebdaedf0fb"}, {"name": "sqlResults", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.90bc53d6-b54b-4ae1-80c2-6c27b12b98ba", "description": {"isNull": "true"}, "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "86aa7e9c-3464-4fa0-9816-5d2cb89470f1", "versionId": "b1881b13-8895-4eb7-9b63-61b0aa388c4b"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3bf34780-13c3-4a0a-9de5-48588d919e83", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "Mapping output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.9647b99f-06e6-409c-8eb8-76d59d364df2", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.f9fad62d-c5d1-4c5e-8659-799400d6e866", "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4165", "versionId": "03fa4c75-65ce-4996-8da6-e505dcef1f38", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "477", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:35f3", "errorHandlerItemId": "2025.f9fad62d-c5d1-4c5e-8659-799400d6e866", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topRight", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.9647b99f-06e6-409c-8eb8-76d59d364df2", "scriptTypeId": "2", "isActive": "true", "script": "//tw.local.odcRequest = {};\r\r\ntw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\n\r\r\n\r\r\nif(tw.local.sqlResults.listLength > 0)\r\r\n{\r\r\n\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\r\r\n\t{\t\r\r\n\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i]  \t\t\t              = new tw.object.ChargesAndCommissions();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].component   \t\t        = tw.local.sqlResults[0].rows[i].data[0];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].defaultAmount                = tw.local.sqlResults[0].rows[i].data[1];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency \t        = new tw.object.NameValuePair();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value        = tw.local.sqlResults[0].rows[i].data[2];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount                 = tw.local.sqlResults[0].rows[i].data[3];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount               = new tw.object.AccountDetails();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass  = new tw.object.NameValuePair();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value   = tw.local.sqlResults[0].rows[i].data[4];\r\r\n\t\t/// need to check for charge and comm account type to know which field should be filled\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo    = tw.local.sqlResults[0].rows[i].data[5];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo          = tw.local.sqlResults[0].rows[i].data[5];\r\r\n\t\t\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode   = tw.local.sqlResults[0].rows[i].data[6];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency     = new tw.object.NameValuePair();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value    = tw.local.sqlResults[0].rows[i].data[7];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance      = tw.local.sqlResults[0].rows[i].data[8];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount =new tw.object.AmountDetails();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign       = tw.local.sqlResults[0].rows[i].data[9];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate     = tw.local.sqlResults[0].rows[i].data[10];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate   = tw.local.sqlResults[0].rows[i].data[11];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount    = tw.local.sqlResults[0].rows[i].data[12];\r\r\n\t\t\r\r\n\t}\r\r\n\r\r\n}", "isRule": "false", "guid": "1d89e055-2f01-46c1-ad61-a488a58b1d33", "versionId": "76535f24-b7d2-484f-bae2-c9ec67677c0f"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.17d88b7a-bf19-4f4e-80bc-fe76fc9246dc", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.aba31f67-508d-4f87-84c5-60f5a3fa34dd", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4166", "versionId": "0a7c78b6-94aa-4ca0-ade2-51bc3e8084ac", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.aba31f67-508d-4f87-84c5-60f5a3fa34dd", "haltProcess": "false", "guid": "9fb6b3b1-740e-4d4c-8ea6-270e7ee6a487", "versionId": "a64415f7-1796-406f-ad06-487e9ddb5c36"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.********-897d-4de1-9d6a-19dc455219b9", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "Linked Service Flow", "tWComponentName": "SubProcess", "tWComponentId": "3012.3c6210d9-6b47-4eaf-83ad-90e8ded22af9", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.f9fad62d-c5d1-4c5e-8659-799400d6e866", "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4167", "versionId": "48d9c35b-89ba-488b-9ae2-2e7b8eca054a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "291", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:35f3", "errorHandlerItemId": "2025.f9fad62d-c5d1-4c5e-8659-799400d6e866", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.3c6210d9-6b47-4eaf-83ad-90e8ded22af9", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "9b70eb99-6482-4741-932f-f8410e5a8b0e", "versionId": "d2934c53-44d4-4407-943c-337eecd5444b", "parameterMapping": [{"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f33eb30a-f8b3-4741-b9b6-4214b909f777", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.3c6210d9-6b47-4eaf-83ad-90e8ded22af9", "useDefault": "false", "value": "tw.local.sqlResults", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "1cfb8ae9-eabf-4c94-abc3-a20ad08eb143", "versionId": "3c91ada5-1521-46a5-b81a-ac98afc40c2e", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e338404a-e130-4da3-a361-c0b292cec522", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.3c6210d9-6b47-4eaf-83ad-90e8ded22af9", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "8bad9c05-1831-4f19-a7d9-32c6799bc47b", "versionId": "4298c67a-6afa-4093-b938-14cd4590a229", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.aaeb2062-6756-49d4-abad-4ca5733dc391", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.3c6210d9-6b47-4eaf-83ad-90e8ded22af9", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "409f4ec7-5f40-41fe-9660-c1f98c49c374", "versionId": "47825583-2e97-45ce-b61c-e565ebd6c3cf", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.92fb52c1-1ac8-4a1c-b691-41434ac377c0", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.3c6210d9-6b47-4eaf-83ad-90e8ded22af9", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "7682dd51-bdf7-483a-91fc-15c7a1711053", "versionId": "d9e0fb27-3d1c-4103-9ecd-31218ab0295f", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7676a84e-5757-46a4-b7c6-3de4579be68f", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.3c6210d9-6b47-4eaf-83ad-90e8ded22af9", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "770e64da-339c-4fb8-8fba-a800ab8590e2", "versionId": "f5b5427a-34fc-423f-9e36-8c93130105d5", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f9fad62d-c5d1-4c5e-8659-799400d6e866", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "Erro<PERSON>", "tWComponentName": "SubProcess", "tWComponentId": "3012.3e1293ad-5746-40d5-93d1-667f118c6154", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:35f3", "versionId": "8b4215f5-813f-4aec-8588-2208af0fc167", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "453", "y": "179", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.3e1293ad-5746-40d5-93d1-667f118c6154", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "e4bda949-d191-4e3e-bf4d-14febcd450cd", "versionId": "e88fbfb2-3fbd-4c08-817a-632736cae9f2", "parameterMapping": [{"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3ab929d2-547c-4fa9-9d3c-fd07c789601d", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.3e1293ad-5746-40d5-93d1-667f118c6154", "useDefault": "false", "value": "\"Retrieve Charges and commisions data\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ed50034c-d10e-4db5-85af-77bc43e7485e", "versionId": "ae604282-95ba-4927-a309-7928e52b0b22", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.47452f20-22af-4dca-ba79-edc3c55747c3", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.3e1293ad-5746-40d5-93d1-667f118c6154", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "b293a8f6-d4fd-4dea-abb9-2a752605cafa", "versionId": "dbcdc721-be8c-499b-83c2-fc6475835913", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d7e820d3-f31e-4cad-a101-af69f52fee15", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "name": "Script Task", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.1de1e01b-d471-4d49-993a-b960663066fb", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4164", "versionId": "d4d460f9-811e-458d-b076-2eca216ddd23", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "107", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.1de1e01b-d471-4d49-993a-b960663066fb", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sql = \"select COMPONENT , DEFAULTCURRENCY, DEFAULTAMOUNT , CHARGEAMOUNT , ACCOUNTCLASS , ACCOUNTNO , BranchCode,  \"\r\r\n+\"  currency , balance , balancesign , STANDARDEXRATE , NEGOTIATEDEXRATE , debitedamount from ODC_CHARGESCOMMISSIONS    \"\r\r\n+\"   where requesrid  = '\"+tw.local.requestId+\"' \";", "isRule": "false", "guid": "cc5e1af8-fafd-4dc7-bfa3-06b774fd338b", "versionId": "4c638300-a9c0-4492-aded-56e5abd5a1f1"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/********/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/********/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/********/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/********/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/********/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "retrieve charges and commision data", "id": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "requestId", "itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "id": "2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5", "ns16:extensionElements": {"ns3:defaultValue": {"useDefault": "false"}}}, "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.62e96889-79d7-4e51-9564-d2732313593f"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.09d42e03-72b8-46a0-882c-e4c44d810906"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.1a526b40-e6ec-4905-9bc0-3709e3eddca5"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.62e96889-79d7-4e51-9564-d2732313593f", "2055.09d42e03-72b8-46a0-882c-e4c44d810906"]}}, "ns16:laneSet": {"id": "dd3f9c25-4a8b-4ab8-920c-a5df8fa8a9a1", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "91a78326-0295-455c-bf29-3ca0da271acd", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["1ffac7db-691e-4fed-84f4-ada9e55e31e3", "17d88b7a-bf19-4f4e-80bc-fe76fc9246dc", "d7e820d3-f31e-4cad-a101-af69f52fee15", "3bf34780-13c3-4a0a-9de5-48588d919e83", "********-897d-4de1-9d6a-19dc455219b9", "50b55d00-565a-4197-876b-cbe3124abf66", "21f15e7d-376c-436d-899d-f01e1f37bc63", "f9fad62d-c5d1-4c5e-8659-799400d6e866"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "1ffac7db-691e-4fed-84f4-ada9e55e31e3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.6e5778ba-deba-49fb-bba9-30278c6de318"}, "ns16:endEvent": {"name": "End", "id": "17d88b7a-bf19-4f4e-80bc-fe76fc9246dc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:f57c3a34823ee126:-91461cc:18aef7c4778:-4166"}, "ns16:incoming": ["e1007472-53ad-4074-b2d1-469a70da4f75", "0d710df0-44c9-4895-8999-9b80b8058eea"]}, "ns16:sequenceFlow": [{"sourceRef": "1ffac7db-691e-4fed-84f4-ada9e55e31e3", "targetRef": "d7e820d3-f31e-4cad-a101-af69f52fee15", "name": "To End", "id": "2027.6e5778ba-deba-49fb-bba9-30278c6de318", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "d7e820d3-f31e-4cad-a101-af69f52fee15", "targetRef": "********-897d-4de1-9d6a-19dc455219b9", "name": "To Linked Service Flow", "id": "d26e632a-96f7-4251-8862-b2f92a23ee7b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "********-897d-4de1-9d6a-19dc455219b9", "targetRef": "3bf34780-13c3-4a0a-9de5-48588d919e83", "name": "To Mapping output", "id": "ca1718a9-5610-47c8-b084-10e0c3f46fa9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "3bf34780-13c3-4a0a-9de5-48588d919e83", "targetRef": "17d88b7a-bf19-4f4e-80bc-fe76fc9246dc", "name": "To End", "id": "e1007472-53ad-4074-b2d1-469a70da4f75", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "21f15e7d-376c-436d-899d-f01e1f37bc63", "targetRef": "f9fad62d-c5d1-4c5e-8659-799400d6e866", "name": "To <PERSON><PERSON>r <PERSON>", "id": "96ce6370-b5b8-42b2-8d1a-a09c66e03b7c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "50b55d00-565a-4197-876b-cbe3124abf66", "targetRef": "f9fad62d-c5d1-4c5e-8659-799400d6e866", "name": "To <PERSON><PERSON>r <PERSON>", "id": "c35bfd91-213e-4d09-865f-9428036a17a8", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "f9fad62d-c5d1-4c5e-8659-799400d6e866", "targetRef": "17d88b7a-bf19-4f4e-80bc-fe76fc9246dc", "name": "To End", "id": "0d710df0-44c9-4895-8999-9b80b8058eea", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Script Task", "id": "d7e820d3-f31e-4cad-a101-af69f52fee15", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "107", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.6e5778ba-deba-49fb-bba9-30278c6de318", "ns16:outgoing": "d26e632a-96f7-4251-8862-b2f92a23ee7b", "ns16:script": "tw.local.sql = \"select COMPONENT , DEFAULTCURRENCY, DEFAULTAMOUNT , CHARGEAMOUNT , ACCOUNTCLASS , ACCOUNTNO , BranchCode,  \"\r\r\n+\"  currency , balance , balancesign , STANDARDEXRATE , NEGOTIATEDEXRATE , debitedamount from ODC_CHARGESCOMMISSIONS    \"\r\r\n+\"   where requesrid  = '\"+tw.local.requestId+\"' \";"}, {"scriptFormat": "text/x-javascript", "name": "Mapping output", "id": "3bf34780-13c3-4a0a-9de5-48588d919e83", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "477", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "ca1718a9-5610-47c8-b084-10e0c3f46fa9", "ns16:outgoing": "e1007472-53ad-4074-b2d1-469a70da4f75", "ns16:script": "//tw.local.odcRequest = {};\r\r\ntw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\n\r\r\n\r\r\nif(tw.local.sqlResults.listLength > 0)\r\r\n{\r\r\n\tfor(var i =0; i<tw.local.sqlResults[0].rows.listLength ; i++)\r\r\n\t{\t\r\r\n\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i]  \t\t\t              = new tw.object.ChargesAndCommissions();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].component   \t\t        = tw.local.sqlResults[0].rows[i].data[0];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].defaultAmount                = tw.local.sqlResults[0].rows[i].data[1];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency \t        = new tw.object.NameValuePair();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value        = tw.local.sqlResults[0].rows[i].data[2];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount                 = tw.local.sqlResults[0].rows[i].data[3];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount               = new tw.object.AccountDetails();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass  = new tw.object.NameValuePair();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value   = tw.local.sqlResults[0].rows[i].data[4];\r\r\n\t\t/// need to check for charge and comm account type to know which field should be filled\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo    = tw.local.sqlResults[0].rows[i].data[5];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo          = tw.local.sqlResults[0].rows[i].data[5];\r\r\n\t\t\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode   = tw.local.sqlResults[0].rows[i].data[6];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency     = new tw.object.NameValuePair();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value    = tw.local.sqlResults[0].rows[i].data[7];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance      = tw.local.sqlResults[0].rows[i].data[8];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount =new tw.object.AmountDetails();\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign       = tw.local.sqlResults[0].rows[i].data[9];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate     = tw.local.sqlResults[0].rows[i].data[10];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate   = tw.local.sqlResults[0].rows[i].data[11];\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount    = tw.local.sqlResults[0].rows[i].data[12];\r\r\n\t\t\r\r\n\t}\r\r\n\r\r\n}"}], "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "Linked Service Flow", "id": "********-897d-4de1-9d6a-19dc455219b9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "291", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "d26e632a-96f7-4251-8862-b2f92a23ee7b", "ns16:outgoing": "ca1718a9-5610-47c8-b084-10e0c3f46fa9", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.sqlResults", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Erro<PERSON>", "id": "f9fad62d-c5d1-4c5e-8659-799400d6e866", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "453", "y": "179", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["96ce6370-b5b8-42b2-8d1a-a09c66e03b7c", "c35bfd91-213e-4d09-865f-9428036a17a8"], "ns16:outgoing": "0d710df0-44c9-4895-8999-9b80b8058eea", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Retrieve Charges and commisions data\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.14d60858-b96c-4d2c-af31-************"}, {"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "sqlParameters", "id": "2056.44cbd19f-423d-4fcb-bdae-97921873266b"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "sqlResults", "id": "2056.90bc53d6-b54b-4ae1-80c2-6c27b12b98ba"}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "3bf34780-13c3-4a0a-9de5-48588d919e83", "parallelMultiple": "false", "name": "Error", "id": "50b55d00-565a-4197-876b-cbe3124abf66", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "512", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "c35bfd91-213e-4d09-865f-9428036a17a8", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "f6d1e427-d87e-4e79-80fd-b67c9d2d16ab"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "7b52b5c2-76f0-45d7-8d0d-c361959890bc", "eventImplId": "8186bf52-afeb-4977-8d98-c467cd71befe", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "********-897d-4de1-9d6a-19dc455219b9", "parallelMultiple": "false", "name": "Error1", "id": "21f15e7d-376c-436d-899d-f01e1f37bc63", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "326", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "96ce6370-b5b8-42b2-8d1a-a09c66e03b7c", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "aa2f0d59-2dec-4731-86aa-952206dc507b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "cc59e553-931b-4dcb-81b8-213d5e7f7a19", "eventImplId": "8a3b962b-44b2-4521-8f43-a2e3a0783eb7", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e1007472-53ad-4074-b2d1-469a70da4f75", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.3bf34780-13c3-4a0a-9de5-48588d919e83", "2025.3bf34780-13c3-4a0a-9de5-48588d919e83"], "endStateId": "Out", "toProcessItemId": ["2025.17d88b7a-bf19-4f4e-80bc-fe76fc9246dc", "2025.17d88b7a-bf19-4f4e-80bc-fe76fc9246dc"], "guid": "647dbc73-6c63-4df6-8d1d-13021e587959", "versionId": "15fe26bb-07f5-4d97-8a3f-687e83d1d717", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Mapping output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ca1718a9-5610-47c8-b084-10e0c3f46fa9", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.********-897d-4de1-9d6a-19dc455219b9", "2025.********-897d-4de1-9d6a-19dc455219b9"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.3bf34780-13c3-4a0a-9de5-48588d919e83", "2025.3bf34780-13c3-4a0a-9de5-48588d919e83"], "guid": "66dca0d4-5017-47f7-ab02-11878427b3e9", "versionId": "4b8a304a-c553-49b2-9585-0b1a310beef8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Linked Service Flow", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d26e632a-96f7-4251-8862-b2f92a23ee7b", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.d7e820d3-f31e-4cad-a101-af69f52fee15", "2025.d7e820d3-f31e-4cad-a101-af69f52fee15"], "endStateId": "Out", "toProcessItemId": ["2025.********-897d-4de1-9d6a-19dc455219b9", "2025.********-897d-4de1-9d6a-19dc455219b9"], "guid": "0d3dc80b-b6ea-4bff-8b5d-220ff9494f23", "versionId": "4eca880a-d62c-464a-802f-0501177038ac", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.0d710df0-44c9-4895-8999-9b80b8058eea", "processId": "1.a446b0e9-d872-4881-895c-c2fb2f239829", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f9fad62d-c5d1-4c5e-8659-799400d6e866", "2025.f9fad62d-c5d1-4c5e-8659-799400d6e866"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.17d88b7a-bf19-4f4e-80bc-fe76fc9246dc", "2025.17d88b7a-bf19-4f4e-80bc-fe76fc9246dc"], "guid": "9666c190-72c7-441a-98fd-39bbd98ca6c9", "versionId": "958cfb22-d372-405a-a1fd-c3d4262466b9", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}