<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.81656d33-5348-479b-a7af-5631356d9476" name="Set Status And Sub Status">
        <lastModified>1692254409289</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.262eab2a-190f-4c94-8281-94fba72ac9f9</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:25fd907f15501a6a:510e674:189fee9ec23:-5a5</guid>
        <versionId>0bd7cd63-4a1c-4d29-b8ec-8b4dc6aebb2d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:25fd907f15501a6a:510e674:189fee9ec23:-bc" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.88e38b02-8b1a-40c9-8d3b-41fdb15e218e"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"1c4b8fbc-387b-48dc-84ba-50f28af5bb9a"},{"incoming":["06f159c6-1cde-457a-8f38-998a9c482ebf"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:25fd907f15501a6a:510e674:189fee9ec23:-5a3"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"85533e76-5649-4eb3-8eba-797b674ba2bf"},{"targetRef":"262eab2a-190f-4c94-8281-94fba72ac9f9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set Status &amp; Sub Status","declaredType":"sequenceFlow","id":"2027.88e38b02-8b1a-40c9-8d3b-41fdb15e218e","sourceRef":"1c4b8fbc-387b-48dc-84ba-50f28af5bb9a"},{"startQuantity":1,"outgoing":["06f159c6-1cde-457a-8f38-998a9c482ebf"],"incoming":["2027.88e38b02-8b1a-40c9-8d3b-41fdb15e218e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":244,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set Status &amp; Sub Status","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"262eab2a-190f-4c94-8281-94fba72ac9f9","scriptFormat":"text\/x-javascript","script":{"content":["\/\/Screen Col01\r\nif((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol01+\"\") &amp;&amp;\r\n (tw.local.action== tw.epv.Col_Actions.submitLiq+\"\" || tw.local.action== tw.epv.Col_Actions.createLiq+\"\" ) )\r\n{\r\n\ttw.local.status= \"In Execution\";\r\n\ttw.local.subStatus= \"Pending Execution Hub Liquidation Review\";\r\n}\r\nif( (tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol01+\"\") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.cancel+\"\") )\r\n{\r\n\ttw.local.status= \"Initiated\";\r\n\ttw.local.subStatus= \"Pending Cancelation Confirmation\";\r\n}\r\n\/\/-------------------------------------------------------------------\r\n\/\/Screen Col02 \r\n\r\nif((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+\"\") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.authorize+\"\") )\r\n{\r\n\ttw.local.status= \"Completed\";\r\n\ttw.local.subStatus= \"Completed\";\r\n}\r\nif((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+\"\") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.returnToMaker+\"\") )\r\n{\r\n\ttw.local.status= \"In Execution\";\r\n\ttw.local.subStatus= \"Pending Execution Hub Liquidation\";\r\n} \r\nif((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+\"\") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.cancel+\"\") )\r\n{\r\n\ttw.local.status= \"Canceled\";\r\n\ttw.local.subStatus= \"Canceled\";\r\n}\r\n"]}},{"targetRef":"85533e76-5649-4eb3-8eba-797b674ba2bf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"06f159c6-1cde-457a-8f38-998a9c482ebf","sourceRef":"262eab2a-190f-4c94-8281-94fba72ac9f9"}],"laneSet":[{"id":"4fa75401-1366-4ee1-8244-3c3de3923165","lane":[{"flowNodeRef":["1c4b8fbc-387b-48dc-84ba-50f28af5bb9a","85533e76-5649-4eb3-8eba-797b674ba2bf","262eab2a-190f-4c94-8281-94fba72ac9f9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c6e2dace-7f9f-46b5-8828-f6624a03177b","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Set Status And Sub Status","declaredType":"process","id":"1.81656d33-5348-479b-a7af-5631356d9476","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"status","isCollection":false,"id":"2055.71a67d77-b802-42cf-8cf2-380a9594a9c2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"subStatus","isCollection":false,"id":"2055.ce1f6bfc-b971-4978-80c8-092f5d40c209"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.e22cd1cb-4788-4edd-beb4-825e8f27f335","epvProcessLinkId":"80460c99-9325-455f-8732-ba08342c9b60","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd","epvProcessLinkId":"cae89176-5d68-4de6-8e6b-dcaa2b9295b8","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46","2055.6010a917-3b0f-4c13-8973-51c4eede4cd9"]}],"outputSet":[{"dataOutputRefs":["2055.71a67d77-b802-42cf-8cf2-380a9594a9c2","2055.ce1f6bfc-b971-4978-80c8-092f5d40c209"]}],"dataInput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"action","isCollection":false,"id":"2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"screenName","isCollection":false,"id":"2055.6010a917-3b0f-4c13-8973-51c4eede4cd9"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46</processParameterId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>62d41688-251d-411f-9d5b-814a44c824a4</guid>
            <versionId>32899726-d86c-4f9f-8628-892801f40d9a</versionId>
        </processParameter>
        <processParameter name="screenName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6010a917-3b0f-4c13-8973-51c4eede4cd9</processParameterId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>798f0895-42bf-48a2-a3ce-57abd7aa85ea</guid>
            <versionId>2183054e-ff53-4be2-be38-3f2da043452b</versionId>
        </processParameter>
        <processParameter name="status">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.71a67d77-b802-42cf-8cf2-380a9594a9c2</processParameterId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6a937e8f-1b85-45a3-bafd-991bc775f579</guid>
            <versionId>a761e767-bf04-49ac-9eb0-f0f9203509c8</versionId>
        </processParameter>
        <processParameter name="subStatus">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ce1f6bfc-b971-4978-80c8-092f5d40c209</processParameterId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>693b548c-28ba-4b97-9e42-ad1d84317357</guid>
            <versionId>8149ca67-fa29-4ea0-95a0-f41c5519c941</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.262eab2a-190f-4c94-8281-94fba72ac9f9</processItemId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <name>Set Status &amp; Sub Status</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.5ecf9609-8bee-4e80-ab86-0c2feebc4577</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:25fd907f15501a6a:510e674:189fee9ec23:-53a</guid>
            <versionId>02db48cd-a005-41d1-b7b1-a920f024a80f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="244" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.5ecf9609-8bee-4e80-ab86-0c2feebc4577</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//Screen Col01&#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol01+"") &amp;&amp;&#xD;
 (tw.local.action== tw.epv.Col_Actions.submitLiq+"" || tw.local.action== tw.epv.Col_Actions.createLiq+"" ) )&#xD;
{&#xD;
	tw.local.status= "In Execution";&#xD;
	tw.local.subStatus= "Pending Execution Hub Liquidation Review";&#xD;
}&#xD;
if( (tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol01+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.cancel+"") )&#xD;
{&#xD;
	tw.local.status= "Initiated";&#xD;
	tw.local.subStatus= "Pending Cancelation Confirmation";&#xD;
}&#xD;
//-------------------------------------------------------------------&#xD;
//Screen Col02 &#xD;
&#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.authorize+"") )&#xD;
{&#xD;
	tw.local.status= "Completed";&#xD;
	tw.local.subStatus= "Completed";&#xD;
}&#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.returnToMaker+"") )&#xD;
{&#xD;
	tw.local.status= "In Execution";&#xD;
	tw.local.subStatus= "Pending Execution Hub Liquidation";&#xD;
} &#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.cancel+"") )&#xD;
{&#xD;
	tw.local.status= "Canceled";&#xD;
	tw.local.subStatus= "Canceled";&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>c2b1239e-d8c8-4276-93b0-d435d947531b</guid>
                <versionId>1f46f511-e113-4e84-961c-deaeafdef5fc</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.85533e76-5649-4eb3-8eba-797b674ba2bf</processItemId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.7d809877-1519-4878-ae77-adcda8d85e1b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:25fd907f15501a6a:510e674:189fee9ec23:-5a3</guid>
            <versionId>438b812b-cdf6-40c0-8b55-2b74ec7bea79</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.7d809877-1519-4878-ae77-adcda8d85e1b</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>4d2c82bb-8a98-4ada-aa82-dee6821c6bb3</guid>
                <versionId>458e2be4-4e34-49d4-b13f-a46bf0f8bbde</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.37b18dc0-ebe4-40d1-ade5-579291f1f4a8</epvProcessLinkId>
            <epvId>/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd</epvId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <guid>db34a10d-e142-4933-abc1-28e43c809af4</guid>
            <versionId>5f6bc65b-c4aa-458a-b093-40dcb1736e04</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.4da7629d-7721-4c78-9ffd-6b3a9c0c0eff</epvProcessLinkId>
            <epvId>/21.e22cd1cb-4788-4edd-beb4-825e8f27f335</epvId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <guid>207bf28b-23e9-4ada-bf65-8482be268724</guid>
            <versionId>bf7b7b09-a1a1-4d27-92e9-528cd870615a</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.262eab2a-190f-4c94-8281-94fba72ac9f9</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Set Status And Sub Status" id="1.81656d33-5348-479b-a7af-5631356d9476" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.e22cd1cb-4788-4edd-beb4-825e8f27f335" epvProcessLinkId="80460c99-9325-455f-8732-ba08342c9b60" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd" epvProcessLinkId="cae89176-5d68-4de6-8e6b-dcaa2b9295b8" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="action" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46" />
                        
                        
                        <ns16:dataInput name="screenName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.6010a917-3b0f-4c13-8973-51c4eede4cd9" />
                        
                        
                        <ns16:dataOutput name="status" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.71a67d77-b802-42cf-8cf2-380a9594a9c2" />
                        
                        
                        <ns16:dataOutput name="subStatus" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.ce1f6bfc-b971-4978-80c8-092f5d40c209" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.6010a917-3b0f-4c13-8973-51c4eede4cd9</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.71a67d77-b802-42cf-8cf2-380a9594a9c2</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.ce1f6bfc-b971-4978-80c8-092f5d40c209</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="4fa75401-1366-4ee1-8244-3c3de3923165">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c6e2dace-7f9f-46b5-8828-f6624a03177b" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>1c4b8fbc-387b-48dc-84ba-50f28af5bb9a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>85533e76-5649-4eb3-8eba-797b674ba2bf</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>262eab2a-190f-4c94-8281-94fba72ac9f9</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="1c4b8fbc-387b-48dc-84ba-50f28af5bb9a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.88e38b02-8b1a-40c9-8d3b-41fdb15e218e</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="85533e76-5649-4eb3-8eba-797b674ba2bf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:25fd907f15501a6a:510e674:189fee9ec23:-5a3</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>06f159c6-1cde-457a-8f38-998a9c482ebf</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1c4b8fbc-387b-48dc-84ba-50f28af5bb9a" targetRef="262eab2a-190f-4c94-8281-94fba72ac9f9" name="To Set Status &amp; Sub Status" id="2027.88e38b02-8b1a-40c9-8d3b-41fdb15e218e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set Status &amp; Sub Status" id="262eab2a-190f-4c94-8281-94fba72ac9f9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="244" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.88e38b02-8b1a-40c9-8d3b-41fdb15e218e</ns16:incoming>
                        
                        
                        <ns16:outgoing>06f159c6-1cde-457a-8f38-998a9c482ebf</ns16:outgoing>
                        
                        
                        <ns16:script>//Screen Col01&#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol01+"") &amp;&amp;&#xD;
 (tw.local.action== tw.epv.Col_Actions.submitLiq+"" || tw.local.action== tw.epv.Col_Actions.createLiq+"" ) )&#xD;
{&#xD;
	tw.local.status= "In Execution";&#xD;
	tw.local.subStatus= "Pending Execution Hub Liquidation Review";&#xD;
}&#xD;
if( (tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol01+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.cancel+"") )&#xD;
{&#xD;
	tw.local.status= "Initiated";&#xD;
	tw.local.subStatus= "Pending Cancelation Confirmation";&#xD;
}&#xD;
//-------------------------------------------------------------------&#xD;
//Screen Col02 &#xD;
&#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.authorize+"") )&#xD;
{&#xD;
	tw.local.status= "Completed";&#xD;
	tw.local.subStatus= "Completed";&#xD;
}&#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.returnToMaker+"") )&#xD;
{&#xD;
	tw.local.status= "In Execution";&#xD;
	tw.local.subStatus= "Pending Execution Hub Liquidation";&#xD;
} &#xD;
if((tw.local.screenName==tw.epv.Col_ScreenNames.ODCCol02+"") &amp;&amp; (tw.local.action== tw.epv.Col_Actions.cancel+"") )&#xD;
{&#xD;
	tw.local.status= "Canceled";&#xD;
	tw.local.subStatus= "Canceled";&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="262eab2a-190f-4c94-8281-94fba72ac9f9" targetRef="85533e76-5649-4eb3-8eba-797b674ba2bf" name="To End" id="06f159c6-1cde-457a-8f38-998a9c482ebf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.06f159c6-1cde-457a-8f38-998a9c482ebf</processLinkId>
            <processId>1.81656d33-5348-479b-a7af-5631356d9476</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.262eab2a-190f-4c94-8281-94fba72ac9f9</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.85533e76-5649-4eb3-8eba-797b674ba2bf</toProcessItemId>
            <guid>f246285b-0925-4e45-8330-0ee90620125b</guid>
            <versionId>02a0b51a-75be-49f2-82ce-7f6d0f347f95</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.262eab2a-190f-4c94-8281-94fba72ac9f9</fromProcessItemId>
            <toProcessItemId>2025.85533e76-5649-4eb3-8eba-797b674ba2bf</toProcessItemId>
        </link>
    </process>
</teamworks>

