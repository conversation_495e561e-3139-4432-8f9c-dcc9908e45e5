{"id": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "versionId": "2991e37c-d215-49ba-a874-3dbff01d5c0a", "name": "test view 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "test2", "inlineScripts": []}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "name": "test view 2", "lastModified": "1735137030489", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "isTemplate": "false", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>3044a8c2-2db2-4d16-8fe3-76245b438af2</ns2:id><ns2:layoutItemId>Text1</ns2:layoutItemId><ns2:configData><ns2:id>8bc5df8b-5399-40ac-bfc6-764586fb7284</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>50b8be08-2f2f-4a49-8c4b-d2a49d92c473</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Plain text</ns2:value></ns2:configData><ns2:configData><ns2:id>ee77deff-4d7b-4e7d-800e-92c6face2aef</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>5a4d9ec1-e599-428a-89bf-928caf9ac167</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>5ec4c581-c57c-476f-8436-a5dfebd2f4b5</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>//alert( me.ui.getAbsoluteName() )</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.test2</ns2:binding></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "false", "loadJsFunction": {"isNull": "true"}, "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": {"isNull": "true"}, "changeJsFunction": {"isNull": "true"}, "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-7c9f", "versionId": "2991e37c-d215-49ba-a874-3dbff01d5c0a", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "bindingType": {"name": "test2", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewBindingTypeId": "65.9534964f-8332-41ba-8496-21002edd4b54", "coachViewId": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "isList": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "0", "description": {"isNull": "true"}, "guid": "b001c2d4-35dc-4317-9ad5-6950afd815b8", "versionId": "d590815c-7ec3-4b51-8904-7b4ed72e4305"}, "inlineScript": {"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.e9dcf535-c83e-4b66-854b-3bf2bbdbb954", "coachViewId": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "scriptType": "JS", "scriptBlock": "", "seq": "0", "description": "", "guid": "97bad323-7dc9-4765-a940-864af11ecb62", "versionId": "b66e7cb3-df5a-47dd-963f-b0eb945f7279"}}}}, "hasDetails": true}