CSHS
      1. from package type = process, from id file processType = 10
      2. what to display : 
            * Variables (note: for each variable display a check box 'hasDefault' represents the hasDefault tag value):
                  Input -> processParameter has parameterType = 1
                  Output -> processParameter has parameterType = 2
                  Private -> processVariable 

            * Elemnets
                  formTask -> name
                  callActivity -> name
                  exclusiveGateway -> name
                  scriptTask -> name, the content in script tag

                  NOTE: for each elemnet of these, if the elemnet has 'preAssignmentScript' or 'postAssignmentScript' tags and they not empty, display its content script, 
                  also display a small circle on the left of the elemnt name to indicate it has preAssignmentScript or postAssignmentScript (2 circles if it has both).
      
