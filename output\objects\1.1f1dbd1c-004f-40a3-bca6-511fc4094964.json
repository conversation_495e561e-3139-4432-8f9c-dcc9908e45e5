{"id": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "versionId": "8004b27f-ff8f-461d-bd60-38d9fb4d7368", "name": "ClosureACT06 - ODC Closure Execution Review", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "lastAction", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "ODC Closure Execution Review", "id": "2025.cecafbcf-e751-4920-8ee0-320e6a9d9746", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "History", "id": "2025.14b59f4d-ad03-4bce-8913-04015763bc86", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit Closure request", "id": "2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Valid?", "id": "2025.c22ba666-0749-4ec0-87b0-0fe548210cf1", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "action?", "id": "2025.2120200f-068b-461d-8633-01832e08c39d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "audited?", "id": "2025.1e05bdba-a90d-448f-8cdd-ba170a80723e", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "audited?", "id": "2025.3264d652-8d04-497a-8596-5615a66c033d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "init Script", "id": "2025.01809a8d-b290-4226-821b-8c823584c4ea", "script": "tw.local.odcRequest.stepLog ={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step      =  tw.epv.ScreenNames.closureACT06;\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.screenName     = tw.epv.ScreenNames.closureACT06;\r\r\ntw.local.actionConditions.lastStepAction = tw.local.lastAction;\r\r\n\r\r\n\r\r\n///////////////*Initializing Request header*//////////////////////////\r\r\n//////////////////////////////////////////////////////////////////////\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT06;", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "validation script", "id": "2025.3fd32827-4598-43bf-82b2-a591ef665c30", "script": "tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n \r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\tif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n\t{\r\r\n\t\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\t\t//addError(tw.local.odcRequest.stepLog.returnReason , \"Please Enter return reason\" ,  \"Please Enter return reason\");\r\r\n\t}\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "setting status and substatus", "id": "2025.70b0325a-9f85-4400-8c84-c4075ead4e5b", "script": "if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"Completed\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Completed\";\r\r\n\t\r\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Closed\";\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending Execution Hub Closure\";\r\r\n\t\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Awaiting Trade FO Approval\";\r\r\n\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "name": "ClosureACT06 - ODC Closure Execution Review", "lastModified": "1700641355791", "lastModifiedBy": "so<PERSON>ia", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.254cec75-3c38-4b94-9949-c93c8c184a24", "2025.254cec75-3c38-4b94-9949-c93c8c184a24"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3484", "versionId": "8004b27f-ff8f-461d-bd60-38d9fb4d7368", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.e52ddd12-caed-4c43-b3c8-59738a717259\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":50,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"cd9976bc-bfc8-4675-85a1-29b35fccfb0b\"},{\"incoming\":[\"2027.292a4039-b196-4bef-80b4-be501c7eda3a\",\"2027.86dd752a-63d8-4a86-85db-06e1d326704a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1802,\"y\":197,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"5dc54b66-3463-4da8-9958-e79a15fcde28\"},{\"targetRef\":\"2025.01809a8d-b290-4226-821b-8c823584c4ea\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e52ddd12-caed-4c43-b3c8-59738a717259\",\"sourceRef\":\"cd9976bc-bfc8-4675-85a1-29b35fccfb0b\"},{\"startQuantity\":1,\"outgoing\":[\"2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf\"],\"incoming\":[\"2027.e52ddd12-caed-4c43-b3c8-59738a717259\"],\"default\":\"2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":207,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"init Script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.01809a8d-b290-4226-821b-8c823584c4ea\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.stepLog ={};\\r\\ntw.local.odcRequest.stepLog.startTime = new Date();\\r\\ntw.local.odcRequest.stepLog.step      =  tw.epv.ScreenNames.closureACT06;\\r\\n\\r\\ntw.local.actionConditions = {};\\r\\ntw.local.actionConditions.screenName     = tw.epv.ScreenNames.closureACT06;\\r\\ntw.local.actionConditions.lastStepAction = tw.local.lastAction;\\r\\n\\r\\n\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/*Initializing Request header*\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nvar date = new Date();\\r\\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\\/' +(date.getMonth() + 1) + '\\/' + date.getFullYear();\\r\\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\\\"( \\\"+ tw.system.user.name+\\\")\\\";\\r\\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT06;\\r\\n\\r\\n\"]}},{\"outgoing\":[\"2027.0d25a330-40de-489d-8ef1-08869e9e83a1\",\"2027.bb6c9551-077b-419a-8a2e-b70cd9f9c5c9\"],\"incoming\":[\"2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf\",\"2027.b3cd0cad-a415-446d-8e32-eb130e79ba68\",\"2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FFC875\",\"width\":95,\"x\":371,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Reversal_Closure_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"92806b06-b1d2-4b00-8a13-195242ca14f2\",\"optionName\":\"@label\",\"value\":\"Closure\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"222d6fd5-1c06-4cf9-8c69-bfa48c63ccf8\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5f99207f-8792-4ea8-8a7d-190456ec2916\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"95706b33-d550-402d-878d-bf6f3b9a2a32\",\"optionName\":\"closureReasonVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e1a6105b-1de8-418f-83f4-7f3f9739e1d2\",\"optionName\":\"reversalReasonVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"94d8dac1-b767-4761-892f-2cb5edbb6047\",\"optionName\":\"executionHubVIS\",\"value\":\"None\"}],\"viewUUID\":\"64.f0c268ac-0772-4735-af5b-5fc6caec30a1\",\"binding\":\"tw.local.odcRequest.ReversalReason\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"b99786df-20b4-4e5d-828b-0257a36acc62\",\"version\":\"8550\"},{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d387361d-d8b2-4098-83c8-0db19f654928\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"37ba0ef6-dc71-40e9-8b0b-d9e1d3266c28\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c7ce921f-e52b-4fe3-8d51-fadf2c6e0708\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fec0cd3f-7b50-4b53-869c-1214bbc6afac\",\"optionName\":\"parentRequestNoVis\",\"value\":\"Readonly \"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bf29fee4-c837-4799-8387-61690a5fee03\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"336c59f5-d5d7-47d9-8de2-90765a1320a4\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"89a6561c-11ab-4384-8dd7-fe8b97529eb6\",\"optionName\":\"contractStageVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0fc0e28a-7cb1-43c8-8ee4-9fbba0319ee4\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"None\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"215d83ab-f1fb-4e8f-8a1a-0495477aca59\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b7a8bb5b-368d-4a84-88df-ceb3fa786c28\",\"optionName\":\"@label\",\"value\":\"Customer Information\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"be076339-30b7-4ee9-8be8-974ababaa943\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"da05c096-902b-45e1-8071-ad9423a36a67\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b6b4f82a-1e41-4916-8580-fffaa85161c7\",\"optionName\":\"customerInfoVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"49591b69-e3b9-4e05-8ac2-1760180803c2\",\"optionName\":\"listsVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"27e95e52-46f0-4f82-892e-fd70c324a8b9\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3cef21ee-5b52-4508-8093-caae9bf875e0\",\"optionName\":\"@label\",\"value\":\"Financial Details Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2a374102-62a7-43a6-8bbf-f3c618e341a3\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d89f30d7-c719-460e-8035-877f173e18e0\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e6dc94a3-df27-4e5d-81c0-4e90e85d348d\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"51e5c190-d463-40db-892a-87d5529b0e7a\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"READONLY\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"477cd48b-88d5-4daf-8866-c1cfe3803286\",\"version\":\"8550\"},{\"layoutItemId\":\"FC_Collections_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"251952da-5cd4-473a-84bc-c5ba0f6d61ee\",\"optionName\":\"@label\",\"value\":\"FC Collections\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7fe581a8-685a-4031-878c-b3605234f73b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e3ef5e2c-749b-498a-891c-bf9c2ac90c0e\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"650bb82d-0ca5-4c86-8938-9921239b3fac\",\"optionName\":\"FCVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9c94508c-3dd3-4105-8978-************\",\"optionName\":\"retrieveBtnVis\",\"value\":\"Hidden\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d14e90da-e720-44c6-80a1-d56ddc621377\",\"optionName\":\"addBtnVIS\",\"value\":\"Hidden\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7a95524c-fbd1-44ba-8225-98860a15d667\",\"optionName\":\"customerCif\",\"value\":\"tw.local.odcRequest.cif\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9f1ff6ae-c586-44a1-871f-a1db38c6be94\",\"optionName\":\"collectionCurrencyVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"86deb812-97b3-4653-891e-09d09c2ac3e5\",\"optionName\":\"negotiatedExchangeRateVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b70ed8ed-193a-4cb5-83b0-59db4b032023\",\"optionName\":\"activityType\",\"value\":\"read\"}],\"viewUUID\":\"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe\",\"binding\":\"tw.local.odcRequest.FcCollections\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"80b44776-cb58-4232-8c80-a5cee4b9c906\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4b965675-1a6c-45be-8506-4e09f02e357e\",\"optionName\":\"@label\",\"value\":\"Attachment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d8d834fb-af06-450f-864f-0b43c0a73573\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0ce50834-30db-49f9-8d86-7ca36cac8e63\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"60dfc6c3-930c-473e-8660-05eb8a76843e\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"678a59d7-1b66-41ed-82cf-e39afa7f2ff5\",\"optionName\":\"canCreate\",\"value\":\"false\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"0a9206df-f1f0-42ce-8fb4-3a098ed67101\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"04e62dc7-3b33-4266-889e-d8ae7a7e8e53\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9064bf05-37d8-41bb-8d76-cd558715337b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"81269016-557b-4ea6-8058-f068624759ae\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d1f84ea7-aca9-443e-8bb4-78ad422dc38a\",\"optionName\":\"@visibility.script\",\"value\":\"{\\\"@class\\\":\\\"com.ibm.bpm.coachNG.visibility.VisibilityRules\\\",\\\"rules\\\":[{\\\"@class\\\":\\\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\\\",\\\"var_conditions\\\":[{\\\"timeInMs\\\":null,\\\"var\\\":\\\"tw.local.isFirstTime\\\",\\\"operand\\\":true,\\\"operator\\\":0}],\\\"action\\\":\\\"NONE\\\"}],\\\"defaultAction\\\":\\\"DEFAULT\\\"}\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"13d99976-e057-4813-8b9d-a0300cbb840e\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"75c3b7d1-82bb-4f5e-81d4-b52db7b01ea9\"}],\"layoutItemId\":\"Tab_Section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c507b959-56b6-49a3-8dfc-6fbc28bc0a67\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f708fd24-0c08-45a7-870e-a2d88587323d\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"796c60d7-4862-4169-8c66-7b5a7224554c\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"1634f23d-d5d1-4580-8572-460d587d3c91\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"6ef0273f-0149-42a0-8d18-b8c5877db716\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f9b47220-8725-4c51-8e4f-d8a16d39af88\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ef617758-fc49-48fc-8ec9-ac3bf749c20b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d30b5c55-2f00-4416-8f1d-6f662dc70c9b\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f05a0b25-6f5a-4565-894c-d02c301709ac\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"856a9278-06ed-4967-8977-eb0be60a1610\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ec4fd1c6-42e6-4c91-8ad3-30488688e29d\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6218bedc-10b6-4de8-868e-5dc8caf0da99\",\"optionName\":\"complianceApprovalVis\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cb05bb2e-1721-422d-8118-575a488cf25d\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2c2ec42b-5dfb-4e72-80ab-9fc2b98a7ce9\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5d9b5d91-ffe7-416e-861d-85283fbe4dec\",\"optionName\":\"terminateReasonVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c3a66db9-5530-46ed-8301-2d0b242548c4\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"68727bd5-f162-4632-859b-9199e2c6e08c\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ad5c6d0c-7aef-4681-8be2-1b78a4b8ab6b\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"None\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"deae7e32-348d-41bf-8ef6-92963fb0f84a\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"ODC Closure Execution Review\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.cecafbcf-e751-4920-8ee0-320e6a9d9746\"},{\"startQuantity\":1,\"outgoing\":[\"2027.4236b764-40b2-4241-8b5a-72d36adfd482\"],\"incoming\":[\"2027.0d25a330-40de-489d-8ef1-08869e9e83a1\"],\"default\":\"2027.4236b764-40b2-4241-8b5a-72d36adfd482\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":560,\"y\":173,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"validation script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.3fd32827-4598-43bf-82b2-a591ef665c30\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\" tw.local.errorMessage =\\\"\\\";\\r\\n var mandatoryTriggered = false;\\r\\n \\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/\\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\\r\\nmandatory(tw.local.odcRequest.stepLog.action, \\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\tif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\\r\\n\\t{\\r\\n\\t\\tmandatory(tw.local.odcRequest.stepLog.returnReason, \\\"tw.local.odcRequest.stepLog.returnReason\\\");\\r\\n\\t\\t\\/\\/addError(tw.local.odcRequest.stepLog.returnReason , \\\"Please Enter return reason\\\" ,  \\\"Please Enter return reason\\\");\\r\\n\\t}\\r\\n\\r\\nfunction mandatory(field , fieldName)\\r\\n{\\r\\n\\tif (field == null || field == undefined )\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\t\\t\\t\\r\\n\\t\\tswitch (typeof field)\\r\\n\\t\\t{\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\r\\n\\t\\t\\tdefault:\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\/\\/ VALIDATE DATE OBJECT\\r\\n\\t\\t\\t\\tif( field && field.getTime && isFinite(field.getTime()) ) {}\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\telse\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\t\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n{\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n{\\r\\n\\tif (field.length > len)\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\t\"]}},{\"startQuantity\":1,\"outgoing\":[\"2027.44bb52d8-554c-4998-828c-16268b3f65b9\"],\"incoming\":[\"2027.e137c27d-1643-405d-85ab-4ba5456beeed\"],\"default\":\"2027.44bb52d8-554c-4998-828c-16268b3f65b9\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":893,\"y\":174,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"setting status and substatus\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.70b0325a-9f85-4400-8c84-c4075ead4e5b\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\\r\\n{\\r\\n\\ttw.local.odcRequest.appInfo.status    =\\\"Completed\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\"Completed\\\";\\r\\n\\t\\r\\n\\ttw.local.odcRequest.BasicDetails.requestState = \\\"Closed\\\";\\r\\n}\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\\r\\n{\\r\\n\\ttw.local.odcRequest.appInfo.status    =\\\"In Execution\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\"Pending Execution Hub Closure\\\";\\r\\n\\t\\r\\n}\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo)\\r\\n{\\r\\n\\ttw.local.odcRequest.appInfo.status    =\\\"In Approval\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\"Awaiting Trade FO Approval\\\";\\r\\n\\r\\n}\"]}},{\"outgoing\":[\"2027.7d7dc6bd-d707-4987-819e-23d76cc185f4\"],\"incoming\":[\"2027.cde782f7-a9b2-46dd-88c1-6c190c43519a\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":1588,\"y\":176,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.7d7dc6bd-d707-4987-819e-23d76cc185f4\",\"name\":\"History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.userRole.RevACt02\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.14b59f4d-ad03-4bce-8913-04015763bc86\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"targetRef\":\"2025.cecafbcf-e751-4920-8ee0-320e6a9d9746\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To ODC Closure Execution Review\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf\",\"sourceRef\":\"2025.01809a8d-b290-4226-821b-8c823584c4ea\"},{\"targetRef\":\"2025.3fd32827-4598-43bf-82b2-a591ef665c30\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"63977fe7-07c8-43f5-8105-62aa6c33c6b5\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To validation script\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.0d25a330-40de-489d-8ef1-08869e9e83a1\",\"sourceRef\":\"2025.cecafbcf-e751-4920-8ee0-320e6a9d9746\"},{\"outgoing\":[\"2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de\",\"2027.e137c27d-1643-405d-85ab-4ba5456beeed\"],\"incoming\":[\"2027.4236b764-40b2-4241-8b5a-72d36adfd482\"],\"default\":\"2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":728,\"y\":192,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.c22ba666-0749-4ec0-87b0-0fe548210cf1\"},{\"targetRef\":\"2025.c22ba666-0749-4ec0-87b0-0fe548210cf1\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4236b764-40b2-4241-8b5a-72d36adfd482\",\"sourceRef\":\"2025.3fd32827-4598-43bf-82b2-a591ef665c30\"},{\"targetRef\":\"2025.cecafbcf-e751-4920-8ee0-320e6a9d9746\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"NO\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de\",\"sourceRef\":\"2025.c22ba666-0749-4ec0-87b0-0fe548210cf1\"},{\"targetRef\":\"2025.70b0325a-9f85-4400-8c84-c4075ead4e5b\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e137c27d-1643-405d-85ab-4ba5456beeed\",\"sourceRef\":\"2025.c22ba666-0749-4ec0-87b0-0fe548210cf1\"},{\"targetRef\":\"2025.2120200f-068b-461d-8633-01832e08c39d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To History\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.44bb52d8-554c-4998-828c-16268b3f65b9\",\"sourceRef\":\"2025.70b0325a-9f85-4400-8c84-c4075ead4e5b\"},{\"outgoing\":[\"2027.b3cd0cad-a415-446d-8e32-eb130e79ba68\"],\"incoming\":[\"2027.bb6c9551-077b-419a-8a2e-b70cd9f9c5c9\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.b3cd0cad-a415-446d-8e32-eb130e79ba68\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":396,\"y\":297,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.94e7ff36-d43b-4ec3-832c-3247dfbe56d1\"},{\"targetRef\":\"2025.94e7ff36-d43b-4ec3-832c-3247dfbe56d1\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"32ccada2-c51c-445c-842e-ae67216b2e40\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomLeft\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.bb6c9551-077b-419a-8a2e-b70cd9f9c5c9\",\"sourceRef\":\"2025.cecafbcf-e751-4920-8ee0-320e6a9d9746\"},{\"targetRef\":\"2025.cecafbcf-e751-4920-8ee0-320e6a9d9746\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To ODC Closure Execution Review\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b3cd0cad-a415-446d-8e32-eb130e79ba68\",\"sourceRef\":\"2025.94e7ff36-d43b-4ec3-832c-3247dfbe56d1\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.45a1a08e-d50e-46b6-8960-83f32eeb4925\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.917ed526-4725-44ba-8baf-3b9bb4fe99d0\"},{\"outgoing\":[\"2027.e464bc58-28c4-41c5-80e7-7f58bca08fe9\",\"2027.292a4039-b196-4bef-80b4-be501c7eda3a\"],\"incoming\":[\"2027.44bb52d8-554c-4998-828c-16268b3f65b9\"],\"default\":\"2027.292a4039-b196-4bef-80b4-be501c7eda3a\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1223,\"y\":193,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"action?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.2120200f-068b-461d-8633-01832e08c39d\"},{\"targetRef\":\"2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.approveRequest\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Approved\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e464bc58-28c4-41c5-80e7-7f58bca08fe9\",\"sourceRef\":\"2025.2120200f-068b-461d-8633-01832e08c39d\"},{\"outgoing\":[\"2027.48ae74aa-310e-447c-8790-40dcf57379a7\"],\"incoming\":[\"2027.e464bc58-28c4-41c5-80e7-7f58bca08fe9\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1305,\"y\":174,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.48ae74aa-310e-447c-8790-40dcf57379a7\",\"name\":\"Audit Closure request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e39bfaa6-c863-41f9-8061-0e371dff89cb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"false\"]}}]},{\"targetRef\":\"2055.5d4b901c-324e-4bea-8f10-e160a656c696\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"0\"]}}]},{\"targetRef\":\"2055.b51575f2-8ce0-48d0-8179-71d12e0440e7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]},{\"targetRef\":\"2055.84daf689-5f95-458f-8b7f-d8c08459d4c1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.20995cf3-6a12-4378-8292-51106389c796\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNature.name\"]}}]},{\"targetRef\":\"2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.name\"]}}]},{\"targetRef\":\"2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestDate\"]}}]},{\"targetRef\":\"2055.85dca7ee-4057-4dcd-878f-b924dff64190\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.328377fd-ccc9-4119-80ca-435deb518aee\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]},{\"targetRef\":\"2055.ebcd1729-7d20-4759-81b3-e98e9f554767\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}]},{\"targetRef\":\"2055.27a871f0-6893-4366-80d9-133f55bffddb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.ReversalReason.closureReason\"]}}]},{\"targetRef\":\"2055.b0be0c94-0742-4365-875f-1b01b63caf0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"\\\"\"]}}]},{\"targetRef\":\"2055.37b99722-adca-4c0b-8d6d-aa2eeae29994\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}]},{\"targetRef\":\"2055.debd9766-ed8e-45c7-8bbb-c471a2567088\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.8d379594-e94f-4a21-8222-396c4ba9b2e1\"]}],\"calledElement\":\"1.40b72dbc-5d84-4b6d-9621-4e738d6838a1\"},{\"targetRef\":\"2025.1e05bdba-a90d-448f-8cdd-ba170a80723e\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.48ae74aa-310e-447c-8790-40dcf57379a7\",\"sourceRef\":\"2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768\"},{\"targetRef\":\"5dc54b66-3463-4da8-9958-e79a15fcde28\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Returned\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.292a4039-b196-4bef-80b4-be501c7eda3a\",\"sourceRef\":\"2025.2120200f-068b-461d-8633-01832e08c39d\"},{\"outgoing\":[\"2027.cde782f7-a9b2-46dd-88c1-6c190c43519a\",\"2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e\"],\"incoming\":[\"2027.48ae74aa-310e-447c-8790-40dcf57379a7\"],\"default\":\"2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1452,\"y\":193,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.1e05bdba-a90d-448f-8cdd-ba170a80723e\"},{\"targetRef\":\"2025.14b59f4d-ad03-4bce-8913-04015763bc86\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.cde782f7-a9b2-46dd-88c1-6c190c43519a\",\"sourceRef\":\"2025.1e05bdba-a90d-448f-8cdd-ba170a80723e\"},{\"incoming\":[\"2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e\",\"2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1522,\"y\":251,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.f966d048-a295-4cef-89eb-0a240de14fcd\"},{\"targetRef\":\"2025.f966d048-a295-4cef-89eb-0a240de14fcd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e\",\"sourceRef\":\"2025.1e05bdba-a90d-448f-8cdd-ba170a80723e\"},{\"targetRef\":\"2025.3264d652-8d04-497a-8596-5615a66c033d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7d7dc6bd-d707-4987-819e-23d76cc185f4\",\"sourceRef\":\"2025.14b59f4d-ad03-4bce-8913-04015763bc86\"},{\"outgoing\":[\"2027.86dd752a-63d8-4a86-85db-06e1d326704a\",\"2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a\"],\"incoming\":[\"2027.7d7dc6bd-d707-4987-819e-23d76cc185f4\"],\"default\":\"2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1692,\"y\":194,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.3264d652-8d04-497a-8596-5615a66c033d\"},{\"targetRef\":\"5dc54b66-3463-4da8-9958-e79a15fcde28\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.86dd752a-63d8-4a86-85db-06e1d326704a\",\"sourceRef\":\"2025.3264d652-8d04-497a-8596-5615a66c033d\"},{\"targetRef\":\"2025.f966d048-a295-4cef-89eb-0a240de14fcd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a\",\"sourceRef\":\"2025.3264d652-8d04-497a-8596-5615a66c033d\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"31cb200c-4371-453a-8cc2-d6f00706a5bd\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"b00dc0b9-b54e-415b-88a0-2ae90fca7c0c\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"ClosureACT06 - ODC Closure Execution Review\",\"declaredType\":\"globalUserTask\",\"id\":\"1.1f1dbd1c-004f-40a3-bca6-511fc4094964\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.e8f79546-2c25-4f06-8077-a129808f6df9\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.27a24ea7-f13b-4d64-83c8-3b8a8226e4c1\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"3fa0783c-f5c5-452d-8a9a-8b01629f99bb\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"a3c54b71-dae1-45be-8a8f-77f6d60e6376\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"cbd65aae-833b-4777-8148-42b323677762\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"_3a34421c-853a-4609-8bd4-04ee1a4c6ba3\"}],\"outputSet\":[{\"id\":\"_ec304ed8-0239-468e-86b6-b862ddae6df5\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = {};\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = [];\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = [];\\nautoObject.FcCollections.listOfAccounts[0] = {};\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = [];\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.Drawer = {};\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = {};\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = {};\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = {};\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = {};\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.def8c2e1-d89c-4fdb-8491-8ca568d3dbc9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"lastAction\",\"isCollection\":false,\"id\":\"2055.a2de4a27-e1c1-4a5b-8df4-451dfa95ffad\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"0d995a8f-37b4-4b1a-8c1d-cf702826b2ae\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.def8c2e1-d89c-4fdb-8491-8ca568d3dbc9", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1b4c80d9-32b3-411e-a605-4c8e3aecb72f", "versionId": "f2a10eeb-592a-4309-a752-72e300603fb1"}, {"name": "lastAction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a2de4a27-e1c1-4a5b-8df4-451dfa95ffad", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "66b96c79-4bf7-4b7e-be14-5e72e85e4bb1", "versionId": "54788173-ab2c-4db4-b3d1-fb25bef4e47b"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e8f79546-2c25-4f06-8077-a129808f6df9", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4c5cd000-fad7-4404-ba8e-d4f9f8c799bd", "versionId": "acff0fa1-a2e9-4627-b3cc-a38f1c63f164"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.45a1a08e-d50e-46b6-8960-83f32eeb4925", "description": {"isNull": "true"}, "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "43040264-a100-4196-9227-a095b3b42b64", "versionId": "160694ae-83d3-4623-bb42-e5651e92edbd"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.917ed526-4725-44ba-8baf-3b9bb4fe99d0", "description": {"isNull": "true"}, "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1981406e-155b-46de-843e-a8f72cd2de00", "versionId": "6ce12182-e12f-42d5-ad32-f0bd4d22e6bb"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.14b59f4d-ad03-4bce-8913-04015763bc86", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "name": "History", "tWComponentName": "SubProcess", "tWComponentId": "3012.3a8e5224-2eca-41b3-9fe1-6a94c1736e64", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:fc53d6da47fd17d0:63ae7219:18a6cf6b670:-7546", "versionId": "1435a2e9-8e89-4a32-89c6-56095a7a3fe9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.3a8e5224-2eca-41b3-9fe1-6a94c1736e64", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "f0b5cec0-4d53-47cf-9d59-1d616a4a75f5", "versionId": "82f54dae-a85d-4be4-b381-024dd7b7080b"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.566f2aa6-f742-4ae9-b9ac-1c77961c3086", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.0ec14337-eb4c-4ccb-9165-e98609108d4f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3483", "versionId": "2b73aa36-1d12-4f77-8347-9e3a1eedac13", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.0ec14337-eb4c-4ccb-9165-e98609108d4f", "haltProcess": "false", "guid": "dfff3cd4-0470-44ae-96d5-22ac1f81b503", "versionId": "f54e8581-76b6-44c3-9e7e-020772bbf516"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.254cec75-3c38-4b94-9949-c93c8c184a24", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.c4f71be8-23cf-4611-ae37-2467209e87c2", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3482", "versionId": "9242fd92-044b-4ae7-af9f-086d5b1869c9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "name": "Audit Closure request", "tWComponentName": "SubProcess", "tWComponentId": "3012.48d0f0f2-7171-4a06-a99d-0907dd32946f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-541d", "versionId": "cc0fddaf-c5d1-4e58-87cc-a43417f071ed", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.48d0f0f2-7171-4a06-a99d-0907dd32946f", "attachedProcessRef": "/1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "guid": "14d96139-2ba7-4f46-8b69-3140ae43734d", "versionId": "a33fe173-624a-4c5b-8747-7140992180a1"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.097b52a5-52cf-496a-bf8a-e99556b61e03", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "guid": "9ea9973f-586d-4b4e-bd05-591b458848e2", "versionId": "3a33f2e1-465c-457a-8fdd-6d939b041a0b"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.236a12bc-dc12-4bc1-8e28-b087c4a83246", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "guid": "0ded89a8-8b2b-43d0-9316-35db2c68c06a", "versionId": "a46ed487-0ffd-4dfb-bd01-331cfb2660be"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.79a75f89-05e3-4716-a10b-efc597c4220a", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "guid": "ad32bdcf-5d4e-4952-9189-4a5f9aeac03d", "versionId": "fa6cd4d0-c9ac-48c2-b2dc-9581b4dc4f68"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.edf9c359-555e-416e-9b1a-549d715c0670", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "guid": "570cbb91-a907-42ba-a65c-3a814aa9df53", "versionId": "b5dc8ed4-41dc-4989-b036-7c4db863fb15"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "0d995a8f-37b4-4b1a-8c1d-cf702826b2ae", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "ClosureACT06 - ODC Closure Execution Review", "id": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "b00dc0b9-b54e-415b-88a0-2ae90fca7c0c", "ns16:startEvent": {"name": "Start", "id": "cd9976bc-bfc8-4675-85a1-29b35fccfb0b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "50", "y": "200", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.e52ddd12-caed-4c43-b3c8-59738a717259"}, "ns16:endEvent": {"name": "End", "id": "5dc54b66-3463-4da8-9958-e79a15fcde28", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1802", "y": "197", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.292a4039-b196-4bef-80b4-be501c7eda3a", "2027.86dd752a-63d8-4a86-85db-06e1d326704a"]}, "ns16:sequenceFlow": [{"sourceRef": "cd9976bc-bfc8-4675-85a1-29b35fccfb0b", "targetRef": "2025.01809a8d-b290-4226-821b-8c823584c4ea", "name": "To Coach", "id": "2027.e52ddd12-caed-4c43-b3c8-59738a717259", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}}}, {"sourceRef": "2025.01809a8d-b290-4226-821b-8c823584c4ea", "targetRef": "2025.cecafbcf-e751-4920-8ee0-320e6a9d9746", "name": "To ODC Closure Execution Review", "id": "2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.cecafbcf-e751-4920-8ee0-320e6a9d9746", "targetRef": "2025.3fd32827-4598-43bf-82b2-a591ef665c30", "name": "To validation script", "id": "2027.0d25a330-40de-489d-8ef1-08869e9e83a1", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "63977fe7-07c8-43f5-8105-62aa6c33c6b5", "ns3:coachEventPath": "DC_Templete1/submit"}}}, {"sourceRef": "2025.3fd32827-4598-43bf-82b2-a591ef665c30", "targetRef": "2025.c22ba666-0749-4ec0-87b0-0fe548210cf1", "name": "To Valid?", "id": "2027.4236b764-40b2-4241-8b5a-72d36adfd482", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c22ba666-0749-4ec0-87b0-0fe548210cf1", "targetRef": "2025.cecafbcf-e751-4920-8ee0-320e6a9d9746", "name": "NO", "id": "2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c22ba666-0749-4ec0-87b0-0fe548210cf1", "targetRef": "2025.70b0325a-9f85-4400-8c84-c4075ead4e5b", "name": "Yes", "id": "2027.e137c27d-1643-405d-85ab-4ba5456beeed", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.70b0325a-9f85-4400-8c84-c4075ead4e5b", "targetRef": "2025.2120200f-068b-461d-8633-01832e08c39d", "name": "To History", "id": "2027.44bb52d8-554c-4998-828c-16268b3f65b9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.cecafbcf-e751-4920-8ee0-320e6a9d9746", "targetRef": "2025.94e7ff36-d43b-4ec3-832c-3247dfbe56d1", "name": "To Postpone", "id": "2027.bb6c9551-077b-419a-8a2e-b70cd9f9c5c9", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomLeft", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "32ccada2-c51c-445c-842e-ae67216b2e40", "ns3:coachEventPath": "DC_Templete1/saveState"}}}, {"sourceRef": "2025.94e7ff36-d43b-4ec3-832c-3247dfbe56d1", "targetRef": "2025.cecafbcf-e751-4920-8ee0-320e6a9d9746", "name": "To ODC Closure Execution Review", "id": "2027.b3cd0cad-a415-446d-8e32-eb130e79ba68", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.2120200f-068b-461d-8633-01832e08c39d", "targetRef": "2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768", "name": "Approved", "id": "2027.e464bc58-28c4-41c5-80e7-7f58bca08fe9", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.approveRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768", "targetRef": "2025.1e05bdba-a90d-448f-8cdd-ba170a80723e", "name": "To audited?", "id": "2027.48ae74aa-310e-447c-8790-40dcf57379a7", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.2120200f-068b-461d-8633-01832e08c39d", "targetRef": "5dc54b66-3463-4da8-9958-e79a15fcde28", "name": "Returned", "id": "2027.292a4039-b196-4bef-80b4-be501c7eda3a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.1e05bdba-a90d-448f-8cdd-ba170a80723e", "targetRef": "2025.14b59f4d-ad03-4bce-8913-04015763bc86", "name": "Yes", "id": "2027.cde782f7-a9b2-46dd-88c1-6c190c43519a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1e05bdba-a90d-448f-8cdd-ba170a80723e", "targetRef": "2025.f966d048-a295-4cef-89eb-0a240de14fcd", "name": "No", "id": "2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.14b59f4d-ad03-4bce-8913-04015763bc86", "targetRef": "2025.3264d652-8d04-497a-8596-5615a66c033d", "name": "To audited?", "id": "2027.7d7dc6bd-d707-4987-819e-23d76cc185f4", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.3264d652-8d04-497a-8596-5615a66c033d", "targetRef": "5dc54b66-3463-4da8-9958-e79a15fcde28", "name": "yes", "id": "2027.86dd752a-63d8-4a86-85db-06e1d326704a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.3264d652-8d04-497a-8596-5615a66c033d", "targetRef": "2025.f966d048-a295-4cef-89eb-0a240de14fcd", "name": "no", "id": "2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf", "name": "init Script", "id": "2025.01809a8d-b290-4226-821b-8c823584c4ea", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "207", "y": "175", "width": "95", "height": "70"}}, "ns16:incoming": "2027.e52ddd12-caed-4c43-b3c8-59738a717259", "ns16:outgoing": "2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf", "ns16:script": "tw.local.odcRequest.stepLog ={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step      =  tw.epv.ScreenNames.closureACT06;\r\r\n\r\r\ntw.local.actionConditions = {};\r\r\ntw.local.actionConditions.screenName     = tw.epv.ScreenNames.closureACT06;\r\r\ntw.local.actionConditions.lastStepAction = tw.local.lastAction;\r\r\n\r\r\n\r\r\n///////////////*Initializing Request header*//////////////////////////\r\r\n//////////////////////////////////////////////////////////////////////\r\r\nvar date = new Date();\r\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();\r\r\ntw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\r\ntw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT06;\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.4236b764-40b2-4241-8b5a-72d36adfd482", "name": "validation script", "id": "2025.3fd32827-4598-43bf-82b2-a591ef665c30", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "560", "y": "173", "width": "95", "height": "70", "color": "#95D087"}}, "ns16:incoming": "2027.0d25a330-40de-489d-8ef1-08869e9e83a1", "ns16:outgoing": "2027.4236b764-40b2-4241-8b5a-72d36adfd482", "ns16:script": " tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n \r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\tif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n\t{\r\r\n\t\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\t\t//addError(tw.local.odcRequest.stepLog.returnReason , \"Please Enter return reason\" ,  \"Please Enter return reason\");\r\r\n\t}\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n{\r\r\n\tif (field.length > len)\r\r\n\t{\r\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\t"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.44bb52d8-554c-4998-828c-16268b3f65b9", "name": "setting status and substatus", "id": "2025.70b0325a-9f85-4400-8c84-c4075ead4e5b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "893", "y": "174", "width": "95", "height": "70"}}, "ns16:incoming": "2027.e137c27d-1643-405d-85ab-4ba5456beeed", "ns16:outgoing": "2027.44bb52d8-554c-4998-828c-16268b3f65b9", "ns16:script": "if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"Completed\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Completed\";\r\r\n\t\r\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Closed\";\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending Execution Hub Closure\";\r\r\n\t\r\r\n}\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToTradeFo)\r\r\n{\r\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Awaiting Trade FO Approval\";\r\r\n\r\r\n}"}], "ns3:formTask": {"isHeritageCoach": "false", "cachePage": "false", "commonLayoutArea": "0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "ODC Closure Execution Review", "id": "2025.cecafbcf-e751-4920-8ee0-320e6a9d9746", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "371", "y": "175", "width": "95", "height": "70", "color": "#FFC875"}, "ns3:validationStayOnPagePaths": "okbutton"}, "ns16:incoming": ["2027.f1a6d61f-d240-408f-8bed-3a10ae2ecaaf", "2027.b3cd0cad-a415-446d-8e32-eb130e79ba68", "2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de"], "ns16:outgoing": ["2027.0d25a330-40de-489d-8ef1-08869e9e83a1", "2027.bb6c9551-077b-419a-8a2e-b70cd9f9c5c9"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "deae7e32-348d-41bf-8ef6-92963fb0f84a", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "f9b47220-8725-4c51-8e4f-d8a16d39af88", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "ef617758-fc49-48fc-8ec9-ac3bf749c20b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "d30b5c55-2f00-4416-8f1d-6f662dc70c9b", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "f05a0b25-6f5a-4565-894c-d02c301709ac", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "856a9278-06ed-4967-8977-eb0be60a1610", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "ec4fd1c6-42e6-4c91-8ad3-30488688e29d", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "6218bedc-10b6-4de8-868e-5dc8caf0da99", "ns19:optionName": "complianceApprovalVis", "ns19:value": "NONE"}, {"ns19:id": "cb05bb2e-1721-422d-8118-575a488cf25d", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "2c2ec42b-5dfb-4e72-80ab-9fc2b98a7ce9", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "5d9b5d91-ffe7-416e-861d-85283fbe4dec", "ns19:optionName": "terminateReasonVIS", "ns19:value": "NONE"}, {"ns19:id": "c3a66db9-5530-46ed-8301-2d0b242548c4", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "68727bd5-f162-4632-859b-9199e2c6e08c", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "None"}, {"ns19:id": "ad5c6d0c-7aef-4681-8be2-1b78a4b8ab6b", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "None"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "6ef0273f-0149-42a0-8d18-b8c5877db716", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "1634f23d-d5d1-4580-8572-460d587d3c91", "ns19:layoutItemId": "Tab_Section1", "ns19:configData": [{"ns19:id": "c507b959-56b6-49a3-8dfc-6fbc28bc0a67", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "f708fd24-0c08-45a7-870e-a2d88587323d", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "796c60d7-4862-4169-8c66-7b5a7224554c", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "75c3b7d1-82bb-4f5e-81d4-b52db7b01ea9", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "b99786df-20b4-4e5d-828b-0257a36acc62", "ns19:layoutItemId": "Reversal_Closure_CV1", "ns19:configData": [{"ns19:id": "92806b06-b1d2-4b00-8a13-195242ca14f2", "ns19:optionName": "@label", "ns19:value": "Closure"}, {"ns19:id": "222d6fd5-1c06-4cf9-8c69-bfa48c63ccf8", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "5f99207f-8792-4ea8-8a7d-190456ec2916", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "95706b33-d550-402d-878d-bf6f3b9a2a32", "ns19:optionName": "closureReasonVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "e1a6105b-1de8-418f-83f4-7f3f9739e1d2", "ns19:optionName": "reversalReasonVIS", "ns19:value": "None"}, {"ns19:id": "94d8dac1-b767-4761-892f-2cb5edbb6047", "ns19:optionName": "executionHubVIS", "ns19:value": "None"}], "ns19:viewUUID": "64.f0c268ac-0772-4735-af5b-5fc6caec30a1", "ns19:binding": "tw.local.odcRequest.ReversalReason"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "215d83ab-f1fb-4e8f-8a1a-0495477aca59", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "d387361d-d8b2-4098-83c8-0db19f654928", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "37ba0ef6-dc71-40e9-8b0b-d9e1d3266c28", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "c7ce921f-e52b-4fe3-8d51-fadf2c6e0708", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "fec0cd3f-7b50-4b53-869c-1214bbc6afac", "ns19:optionName": "parentRequestNoVis", "ns19:value": "<PERSON><PERSON><PERSON> "}, {"ns19:id": "bf29fee4-c837-4799-8387-61690a5fee03", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "336c59f5-d5d7-47d9-8de2-90765a1320a4", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "None"}, {"ns19:id": "89a6561c-11ab-4384-8dd7-fe8b97529eb6", "ns19:optionName": "contractStageVIS", "ns19:value": "None"}, {"ns19:id": "0fc0e28a-7cb1-43c8-8ee4-9fbba0319ee4", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "None"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "27e95e52-46f0-4f82-892e-fd70c324a8b9", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "b7a8bb5b-368d-4a84-88df-ceb3fa786c28", "ns19:optionName": "@label", "ns19:value": "Customer Information"}, {"ns19:id": "be076339-30b7-4ee9-8be8-974ababaa943", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "da05c096-902b-45e1-8071-ad9423a36a67", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "b6b4f82a-1e41-4916-8580-fffaa85161c7", "ns19:optionName": "customerInfoVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "49591b69-e3b9-4e05-8ac2-1760180803c2", "ns19:optionName": "listsVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "477cd48b-88d5-4daf-8866-c1cfe3803286", "ns19:layoutItemId": "Financial_Details_Branch1", "ns19:configData": [{"ns19:id": "3cef21ee-5b52-4508-8093-caae9bf875e0", "ns19:optionName": "@label", "ns19:value": "Financial Details Branch"}, {"ns19:id": "2a374102-62a7-43a6-8bbf-f3c618e341a3", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "d89f30d7-c719-460e-8035-877f173e18e0", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "e6dc94a3-df27-4e5d-81c0-4e90e85d348d", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "READONLY"}, {"ns19:id": "51e5c190-d463-40db-892a-87d5529b0e7a", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "READONLY"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "80b44776-cb58-4232-8c80-a5cee4b9c906", "ns19:layoutItemId": "FC_Collections_CV1", "ns19:configData": [{"ns19:id": "251952da-5cd4-473a-84bc-c5ba0f6d61ee", "ns19:optionName": "@label", "ns19:value": "FC Collections"}, {"ns19:id": "7fe581a8-685a-4031-878c-b3605234f73b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "e3ef5e2c-749b-498a-891c-bf9c2ac90c0e", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "650bb82d-0ca5-4c86-8938-9921239b3fac", "ns19:optionName": "FCVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "9c94508c-3dd3-4105-8978-************", "ns19:optionName": "retrieveBtnVis", "ns19:value": "Hidden"}, {"ns19:id": "d14e90da-e720-44c6-80a1-d56ddc621377", "ns19:optionName": "addBtnVIS", "ns19:value": "Hidden"}, {"ns19:id": "7a95524c-fbd1-44ba-8225-98860a15d667", "ns19:optionName": "customerCif", "ns19:value": "tw.local.odcRequest.cif", "ns19:valueType": "dynamic"}, {"ns19:id": "9f1ff6ae-c586-44a1-871f-a1db38c6be94", "ns19:optionName": "collectionCurrencyVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "86deb812-97b3-4653-891e-09d09c2ac3e5", "ns19:optionName": "negotiatedExchangeRateVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "b70ed8ed-193a-4cb5-83b0-59db4b032023", "ns19:optionName": "activityType", "ns19:value": "read"}], "ns19:viewUUID": "64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe", "ns19:binding": "tw.local.odcRequest.FcCollections"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "0a9206df-f1f0-42ce-8fb4-3a098ed67101", "ns19:layoutItemId": "Attachment1", "ns19:configData": [{"ns19:id": "4b965675-1a6c-45be-8506-4e09f02e357e", "ns19:optionName": "@label", "ns19:value": "Attachment"}, {"ns19:id": "d8d834fb-af06-450f-864f-0b43c0a73573", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "0ce50834-30db-49f9-8d86-7ca36cac8e63", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "60dfc6c3-930c-473e-8660-05eb8a76843e", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "678a59d7-1b66-41ed-82cf-e39afa7f2ff5", "ns19:optionName": "canCreate", "ns19:value": "false"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "13d99976-e057-4813-8b9d-a0300cbb840e", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "04e62dc7-3b33-4266-889e-d8ae7a7e8e53", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "9064bf05-37d8-41bb-8d76-cd558715337b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "81269016-557b-4ea6-8058-f068624759ae", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "d1f84ea7-aca9-443e-8bb4-78ad422dc38a", "ns19:optionName": "@visibility.script", "ns19:value": "{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}", "ns19:valueType": "static"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:callActivity": [{"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.7d7dc6bd-d707-4987-819e-23d76cc185f4", "name": "History", "id": "2025.14b59f4d-ad03-4bce-8913-04015763bc86", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1588", "y": "176", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": "2027.cde782f7-a9b2-46dd-88c1-6c190c43519a", "ns16:outgoing": "2027.7d7dc6bd-d707-4987-819e-23d76cc185f4", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.epv.userRole.RevACt02", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "default": "2027.48ae74aa-310e-447c-8790-40dcf57379a7", "name": "Audit Closure request", "id": "2025.1a91757d-ba65-4c6a-8a2e-43ef47fa2768", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1305", "y": "174", "width": "95", "height": "70"}}, "ns16:incoming": "2027.e464bc58-28c4-41c5-80e7-7f58bca08fe9", "ns16:outgoing": "2027.48ae74aa-310e-447c-8790-40dcf57379a7", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.e39bfaa6-c863-41f9-8061-0e371dff89cb", "ns16:assignment": {"ns16:from": {"_": "false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:targetRef": "2055.5d4b901c-324e-4bea-8f10-e160a656c696", "ns16:assignment": {"ns16:from": {"_": "0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.b51575f2-8ce0-48d0-8179-71d12e0440e7", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.84daf689-5f95-458f-8b7f-d8c08459d4c1", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.20995cf3-6a12-4378-8292-51106389c796", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNature.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestDate", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}}}, {"ns16:targetRef": "2055.85dca7ee-4057-4dcd-878f-b924dff64190", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.328377fd-ccc9-4119-80ca-435deb518aee", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.ebcd1729-7d20-4759-81b3-e98e9f554767", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.27a871f0-6893-4366-80d9-133f55bffddb", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.ReversalReason.closureReason", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b0be0c94-0742-4365-875f-1b01b63caf0c", "ns16:assignment": {"ns16:from": {"_": "\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.37b99722-adca-4c0b-8d6d-aa2eeae29994", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.debd9766-ed8e-45c7-8bbb-c471a2567088", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.8d379594-e94f-4a21-8222-396c4ba9b2e1", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:exclusiveGateway": [{"default": "2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de", "name": "Valid?", "id": "2025.c22ba666-0749-4ec0-87b0-0fe548210cf1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "728", "y": "192", "width": "32", "height": "32"}}, "ns16:incoming": "2027.4236b764-40b2-4241-8b5a-72d36adfd482", "ns16:outgoing": ["2027.96ac892e-9a8e-40cd-8b5b-3c0e7e96d9de", "2027.e137c27d-1643-405d-85ab-4ba5456beeed"]}, {"default": "2027.292a4039-b196-4bef-80b4-be501c7eda3a", "name": "action?", "id": "2025.2120200f-068b-461d-8633-01832e08c39d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1223", "y": "193", "width": "32", "height": "32"}}, "ns16:incoming": "2027.44bb52d8-554c-4998-828c-16268b3f65b9", "ns16:outgoing": ["2027.e464bc58-28c4-41c5-80e7-7f58bca08fe9", "2027.292a4039-b196-4bef-80b4-be501c7eda3a"]}, {"default": "2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e", "name": "audited?", "id": "2025.1e05bdba-a90d-448f-8cdd-ba170a80723e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1452", "y": "193", "width": "32", "height": "32"}}, "ns16:incoming": "2027.48ae74aa-310e-447c-8790-40dcf57379a7", "ns16:outgoing": ["2027.cde782f7-a9b2-46dd-88c1-6c190c43519a", "2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e"]}, {"default": "2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a", "name": "audited?", "id": "2025.3264d652-8d04-497a-8596-5615a66c033d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1692", "y": "194", "width": "32", "height": "32"}}, "ns16:incoming": "2027.7d7dc6bd-d707-4987-819e-23d76cc185f4", "ns16:outgoing": ["2027.86dd752a-63d8-4a86-85db-06e1d326704a", "2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a"]}], "ns16:intermediateThrowEvent": [{"name": "Postpone", "id": "2025.94e7ff36-d43b-4ec3-832c-3247dfbe56d1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "396", "y": "297", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.b3cd0cad-a415-446d-8e32-eb130e79ba68"}, "ns16:incoming": "2027.bb6c9551-077b-419a-8a2e-b70cd9f9c5c9", "ns16:outgoing": "2027.b3cd0cad-a415-446d-8e32-eb130e79ba68", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page", "id": "2025.f966d048-a295-4cef-89eb-0a240de14fcd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1522", "y": "251", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.da3b8e7c-27d1-4e05-8b80-236d2c75fc8e", "2027.e2d4ef59-80ac-4e85-861e-00882bd76f1a"], "ns3:stayOnPageEventDefinition": ""}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.45a1a08e-d50e-46b6-8960-83f32eeb4925"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.917ed526-4725-44ba-8baf-3b9bb4fe99d0"}], "ns3:htmlHeaderTag": {"id": "31cb200c-4371-453a-8cc2-d6f00706a5bd", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "3fa0783c-f5c5-452d-8a9a-8b01629f99bb"}, {"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "a3c54b71-dae1-45be-8a8f-77f6d60e6376"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "cbd65aae-833b-4777-8148-42b323677762"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.27a24ea7-f13b-4d64-83c8-3b8a8226e4c1"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.def8c2e1-d89c-4fdb-8491-8ca568d3dbc9", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = {};\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = [];\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = [];\r\nautoObject.FcCollections.listOfAccounts[0] = {};\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = [];\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.Drawer = {};\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = {};\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = {};\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = {};\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = {};\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject", "useDefault": "true"}}}, {"name": "lastAction", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a2de4a27-e1c1-4a5b-8df4-451dfa95ffad"}], "ns16:dataOutput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.e8f79546-2c25-4f06-8077-a129808f6df9"}, "ns16:inputSet": {"id": "_3a34421c-853a-4609-8bd4-04ee1a4c6ba3"}, "ns16:outputSet": {"id": "_ec304ed8-0239-468e-86b6-b862ddae6df5"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.3494f065-bd2c-41e1-b18b-782e1f273979", "processId": "1.1f1dbd1c-004f-40a3-bca6-511fc4094964", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.254cec75-3c38-4b94-9949-c93c8c184a24", "2025.254cec75-3c38-4b94-9949-c93c8c184a24"], "endStateId": "Out", "toProcessItemId": ["2025.566f2aa6-f742-4ae9-b9ac-1c77961c3086", "2025.566f2aa6-f742-4ae9-b9ac-1c77961c3086"], "guid": "e2be170e-c652-4f95-a6c9-4b77556da868", "versionId": "d62b55d2-fc28-45ad-85b8-7303e0f301c8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}