<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.796a077f-9dd5-47e3-88a9-4908b204fdb0" name="RequestState">
        <lastModified>1692013448492</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <classId>12.796a077f-9dd5-47e3-88a9-4908b204fdb0</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>false</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.796a077f-9dd5-47e3-88a9-4908b204fdb0</externalId>
        <dependencySummary isNull="true" />
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/NBEODCR","complexType":[{"annotation":{"documentation":[{}],"appinfo":[{"shared":[false],"advancedProperties":[{}],"shadow":[false]}]},"sequence":{},"name":"RequestState"}],"id":"_12.796a077f-9dd5-47e3-88a9-4908b204fdb0"}</jsonData>
        <description isNull="true" />
        <guid>guid:b947f5711d9aa2da:-100b3077:189f3dcf80b:-3aa0</guid>
        <versionId>e24d0f34-aa04-4e0d-8685-404bed09f7b6</versionId>
        <definition>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="RequestState">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name isNull="true" />
                <namespace isNull="true" />
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

