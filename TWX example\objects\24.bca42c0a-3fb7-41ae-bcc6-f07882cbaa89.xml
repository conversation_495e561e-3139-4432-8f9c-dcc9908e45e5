<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <participant id="24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89" name="Trade FO Makers">
        <lastModified>1691144750823</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <participantId>24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89</participantId>
        <participantDefinition isNull="true" />
        <simulationGroupSize>2</simulationGroupSize>
        <capacityType>1</capacityType>
        <definitionType>3</definitionType>
        <percentAvailable isNull="true" />
        <percentEfficiency isNull="true" />
        <cost>10.00</cost>
        <currencyCode isNull="true" />
        <image isNull="true" />
        <serviceMembersRef isNull="true" />
        <managersRef isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"team":[{"members":{"Users":[{"name":"heba","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"abdelrahman.saleh","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"},{"name":"somaia","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}],"UserGroups":[{"name":"BPM_ODC_Trade_FO_MKR","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.Member"}]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmteamext.TTeamDetails","type":"StandardMembers"}]},"documentation":[{"content":[],"textFormat":"text\/plain"}],"name":"Trade FO Makers","declaredType":"resource","id":"24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.w3.org\/1999\/XPath","id":"24.bca42c0a-3fb7-41ae-bcc6-f07882cbaa89"}</jsonData>
        <externalId isNull="true" />
        <description></description>
        <guid>guid:d694a63221635d5b:6baf87c4:18969a9a6e2:7534</guid>
        <versionId>f8d25f3b-0b03-40df-beee-9a37fd159eaa</versionId>
        <standardMembers>
            <standardMember>
                <type>Group</type>
                <name>BPM_ODC_Trade_FO_MKR</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>heba</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>abdelrahman.saleh</name>
            </standardMember>
            <standardMember>
                <type>User</type>
                <name>somaia</name>
            </standardMember>
        </standardMembers>
        <teamAssignments />
    </participant>
</teamworks>

