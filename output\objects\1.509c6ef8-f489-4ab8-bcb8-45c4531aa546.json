{"id": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "versionId": "f95d146a-6c23-4ba7-85b6-bf3d0ee3d552", "name": "Retrieve Request Number", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "Retrieve Request Number", "lastModified": "1700640016701", "lastModifiedBy": "mohamed.reda", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.0b5388d2-710c-4d60-8273-fc5083f9df41", "2025.0b5388d2-710c-4d60-8273-fc5083f9df41"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3794", "versionId": "f95d146a-6c23-4ba7-85b6-bf3d0ee3d552", "dependencySummary": "<dependencySummary id=\"bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:542a\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.d485ccef-7aa5-461f-8750-141206f211b6\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":6,\"y\":74,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"ec1cbe89-1f9d-4798-858b-4facbe857909\"},{\"incoming\":[\"f56cd9b4-ec5b-4773-869a-66ff2e676749\",\"03214778-213c-410b-87ac-1aeb1579f0c7\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":555,\"y\":75,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"9a4f7402-5fc1-40f6-82b0-b18ea19b31f9\"},{\"targetRef\":\"0b5388d2-710c-4d60-8273-fc5083f9df41\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d485ccef-7aa5-461f-8750-141206f211b6\",\"sourceRef\":\"ec1cbe89-1f9d-4798-858b-4facbe857909\"},{\"startQuantity\":1,\"outgoing\":[\"5a949aee-cc6f-4dcf-8beb-c49a4a34fabb\"],\"incoming\":[\"2027.d485ccef-7aa5-461f-8750-141206f211b6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":127,\"y\":52,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Sql query\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"0b5388d2-710c-4d60-8273-fc5083f9df41\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sql = \\\"select requestNo from ODC_RequestInfo where PARENTREQUESTNO = ? order by requestNo desc;\\\"\\r\\n\\r\\ntw.local.parameters = new tw.object.listOf.SQLParameter();\\r\\ntw.local.parameters[0] = new tw.object.SQLParameter();\\r\\ntw.local.parameters[0].value = tw.local.parentRequestNumber;\"]}},{\"startQuantity\":1,\"outgoing\":[\"ad377e79-eb89-4219-83f0-19ca1836eea0\"],\"incoming\":[\"5a949aee-cc6f-4dcf-8beb-c49a4a34fabb\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":296,\"y\":53,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Sql execute statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parameters\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"a4578e04-1773-48d1-81e1-c385d8bb3b7c\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":886,\"y\":85,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"result\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"1bf6bd61-8f48-497f-8ebf-67949c77849b\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\tvar requestNumber = tw.local.results[0].rows[0].data[0];\\r\\n\\t\\/\\/ arr is array to store the splitted request number\\r\\n\\tvar arr = requestNumber.split('-');\\r\\n\\t\\/\\/ x var the incremental part of request type \\r\\n\\tvar x = parseInt(arr[1]);\\r\\nif(x == \\\"\\\")\\r\\n\\t tw.local.newRequestId = tw.local.parentRequestNumber+\\\"-01\\\";\\r\\nelse\\r\\n{\\t \\r\\n\\tx= x+1;\\r\\n\\t\\r\\n\\tif(x < 9)\\r\\n\\ttw.local.tmpvar = \\\"0\\\"+ x;\\r\\n\\telse\\r\\n\\ttw.local.tmpvar = x;\\r\\n\\t\\r\\n\\t\\r\\n\\ttw.local.newRequestId = tw.local.parentRequestNumber+'-'+tw.local.tmpvar;\\r\\n\\t\\r\\n}\\r\\n\"]}},{\"targetRef\":\"a4578e04-1773-48d1-81e1-c385d8bb3b7c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Sql execute statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"5a949aee-cc6f-4dcf-8beb-c49a4a34fabb\",\"sourceRef\":\"0b5388d2-710c-4d60-8273-fc5083f9df41\"},{\"targetRef\":\"8c10d3c7-629b-48bf-88ef-610c7c360cec\",\"extensionElements\":{\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To result\",\"declaredType\":\"sequenceFlow\",\"id\":\"ad377e79-eb89-4219-83f0-19ca1836eea0\",\"sourceRef\":\"a4578e04-1773-48d1-81e1-c385d8bb3b7c\"},{\"targetRef\":\"9a4f7402-5fc1-40f6-82b0-b18ea19b31f9\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"f56cd9b4-ec5b-4773-869a-66ff2e676749\",\"sourceRef\":\"8c10d3c7-629b-48bf-88ef-610c7c360cec\"},{\"parallelMultiple\":false,\"outgoing\":[\"0f6180ed-0fdd-4e8e-8b43-ced7c398f14e\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"c42afc2e-0b79-42bf-8f97-4bc1195ed87b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"c0f6d36d-2272-4ade-805d-6fe5c501b5ee\",\"otherAttributes\":{\"eventImplId\":\"326a6dce-8657-4ce2-8ccb-3471650944df\"}}],\"attachedToRef\":\"a4578e04-1773-48d1-81e1-c385d8bb3b7c\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":331,\"y\":111,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"7e104f2f-8557-453c-83ba-277190edbcac\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"be825c7f-339e-4b48-8f4c-67246f6be687\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"3f003487-ce71-478d-876c-1c4c9505d918\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"9ec4a605-47fa-418f-86c8-59df972271cc\",\"otherAttributes\":{\"eventImplId\":\"521505e7-454b-4300-8dde-cbf555d237fa\"}}],\"attachedToRef\":\"0b5388d2-710c-4d60-8273-fc5083f9df41\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":162,\"y\":110,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error2\",\"declaredType\":\"boundaryEvent\",\"id\":\"b5f48c1b-9e18-4afc-8b65-2ec137de13c5\",\"outputSet\":{}},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.abcd088d-e709-49e0-8fd2-41864cba3248\"},{\"startQuantity\":1,\"outgoing\":[\"03214778-213c-410b-87ac-1aeb1579f0c7\"],\"incoming\":[\"be825c7f-339e-4b48-8f4c-67246f6be687\",\"0f6180ed-0fdd-4e8e-8b43-ced7c398f14e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":295,\"y\":166,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Error handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Retrieve Request number\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"37f8f1ce-3d46-40c9-89ed-4426d7549be7\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"37f8f1ce-3d46-40c9-89ed-4426d7549be7\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"be825c7f-339e-4b48-8f4c-67246f6be687\",\"sourceRef\":\"b5f48c1b-9e18-4afc-8b65-2ec137de13c5\"},{\"targetRef\":\"9a4f7402-5fc1-40f6-82b0-b18ea19b31f9\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"03214778-213c-410b-87ac-1aeb1579f0c7\",\"sourceRef\":\"37f8f1ce-3d46-40c9-89ed-4426d7549be7\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();\\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();\\nautoObject[0].type = \\\"\\\";\\nautoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();\\nautoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();\\nautoObject[0].columns[0].catalogName = \\\"\\\";\\nautoObject[0].columns[0].columnClassName = \\\"\\\";\\nautoObject[0].columns[0].columnDisplaySize = 0;\\nautoObject[0].columns[0].columnLabel = \\\"\\\";\\nautoObject[0].columns[0].columnName = \\\"\\\";\\nautoObject[0].columns[0].columnTypeName = \\\"\\\";\\nautoObject[0].columns[0].precision = 0;\\nautoObject[0].columns[0].scale = 0;\\nautoObject[0].columns[0].schemaName = \\\"\\\";\\nautoObject[0].columns[0].tableName = \\\"\\\";\\nautoObject[0].columns[0].autoIncrement = false;\\nautoObject[0].columns[0].caseSensitive = false;\\nautoObject[0].columns[0].currency = false;\\nautoObject[0].columns[0].definitelyWritable = false;\\nautoObject[0].columns[0].nullable = 0;\\nautoObject[0].columns[0].readOnly = false;\\nautoObject[0].columns[0].searchable = false;\\nautoObject[0].columns[0].signed = false;\\nautoObject[0].columns[0].writable = false;\\nautoObject[0].columnIndexes = null;\\nautoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();\\nautoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();\\nautoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();\\nautoObject[0].rows[0].data[0] = null;\\nautoObject[0].rows[0].indexedMap = null;\\nautoObject[0].updateCount = 0;\\nautoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();\\nautoObject[0].outValues[0] = null;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"results\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.d14abeae-0693-4705-8321-ca8946b4bfa6\"},{\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"parameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.e16311e3-1714-42d0-8b05-b45a80bc6699\"},{\"targetRef\":\"37f8f1ce-3d46-40c9-89ed-4426d7549be7\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Error handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"0f6180ed-0fdd-4e8e-8b43-ced7c398f14e\",\"sourceRef\":\"7e104f2f-8557-453c-83ba-277190edbcac\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"tmpvar\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9b12ef2c-51c8-4325-80a1-8e740fcacb5d\"},{\"startQuantity\":1,\"outgoing\":[\"f56cd9b4-ec5b-4773-869a-66ff2e676749\"],\"incoming\":[\"ad377e79-eb89-4219-83f0-19ca1836eea0\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":441,\"y\":53,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"8c10d3c7-629b-48bf-88ef-610c7c360cec\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"var requestNumber =  tw.local.results[0].rows[0].data[0];\\r\\nif(requestNumber.indexOf(\\\"-\\\") == -1 )\\r\\n    tw.local.newRequestId = requestNumber + \\\"-01\\\"; \\r\\nelse {\\r\\n    var arr = requestNumber.split('-');\\r\\n    var incrementalPart =  arr[1];\\r\\n    \\r\\n    if(incrementalPart[0] == \\\"0\\\" && parseInt(incrementalPart[1]) <9 ){\\r\\n     var num = parseInt(incrementalPart[1]) + 1;\\r\\n     tw.local.newRequestId = arr[0] + \\\"-0\\\" + num; \\r\\n     }\\r\\n    else if (parseInt(incrementalPart[1]) == 9 ){\\r\\n      var num = parseInt(incrementalPart[1]) + 1;\\r\\n      tw.local.newRequestId = arr[0] + \\\"-\\\" + num;\\r\\n    }\\r\\n   else {\\r\\n      var num = parseInt(incrementalPart) + 1;\\r\\n      tw.local.newRequestId = arr[0] + \\\"-\\\" + num;\\r\\n     }\\r\\n}\"]}}],\"laneSet\":[{\"id\":\"6dfe740c-4ad3-4885-8e15-0632cf12d377\",\"lane\":[{\"flowNodeRef\":[\"ec1cbe89-1f9d-4798-858b-4facbe857909\",\"9a4f7402-5fc1-40f6-82b0-b18ea19b31f9\",\"0b5388d2-710c-4d60-8273-fc5083f9df41\",\"a4578e04-1773-48d1-81e1-c385d8bb3b7c\",\"1bf6bd61-8f48-497f-8ebf-67949c77849b\",\"7e104f2f-8557-453c-83ba-277190edbcac\",\"b5f48c1b-9e18-4afc-8b65-2ec137de13c5\",\"37f8f1ce-3d46-40c9-89ed-4426d7549be7\",\"8c10d3c7-629b-48bf-88ef-610c7c360cec\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"cc2b447a-4251-4882-874d-36323f92a7d5\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Retrieve Request Number\",\"declaredType\":\"process\",\"id\":\"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"newRequestId\",\"isCollection\":false,\"id\":\"2055.29fabc80-90b8-4cad-81e3-1c319c6f595a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"id\":\"2055.4ec4b61d-5cd9-43b6-827c-c1801162373f\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.29fabc80-90b8-4cad-81e3-1c319c6f595a\",\"2055.4ec4b61d-5cd9-43b6-827c-c1801162373f\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\/\\/\\\"00104230000146\\\"\\r\\n\\\"07704230000198\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNumber\",\"isCollection\":false,\"id\":\"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "parentRequestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e1ca7465-4084-405d-8ea6-ab3f3762de92", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "//\"00104230000146\"\r\r\n\"07704230000198\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "1bffa9d2-dc85-41b2-8e8e-0034d8e7961c", "versionId": "a9444d59-f9fb-4269-8d04-735aa2c3533e"}, {"name": "newRequestId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.29fabc80-90b8-4cad-81e3-1c319c6f595a", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "693f32ec-be1c-4b98-8cee-bdfffa8c5fac", "versionId": "8b8ea6b0-bc0d-4348-836d-75c72eeb7665"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4ec4b61d-5cd9-43b6-827c-c1801162373f", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "d1108927-dbae-424b-9b7f-b2b7eda9d536", "versionId": "b040ce0d-e185-471f-a7f3-b2967298249d"}], "processVariable": [{"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.abcd088d-e709-49e0-8fd2-41864cba3248", "description": {"isNull": "true"}, "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "697b7bb9-36bf-4401-985d-025304d190ed", "versionId": "07009dc7-0785-4885-a733-17fc73bf6640"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d14abeae-0693-4705-8321-ca8946b4bfa6", "description": {"isNull": "true"}, "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "e4d633d8-0cfe-4ab9-a054-b16ac7f9d659", "versionId": "62e1531f-3466-475f-b823-955b4c35f15e"}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.e16311e3-1714-42d0-8b05-b45a80bc6699", "description": {"isNull": "true"}, "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bebea286-0849-4745-89df-cd2f02d5638e", "versionId": "81fa92da-fd76-4bad-9539-bc90447ff67e"}, {"name": "tmpvar", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9b12ef2c-51c8-4325-80a1-8e740fcacb5d", "description": {"isNull": "true"}, "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "290f5bb5-e54b-4ee4-a967-f16778ca7edb", "versionId": "62c607c4-497c-4fa1-96bd-1d5db5eb7c96"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "Error handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3cea", "versionId": "45a350af-cd57-48cc-bdb0-f0c44c14a32c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "295", "y": "166", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "a2d7bac5-29ba-4902-8811-4d9b53c71494", "versionId": "15c61a67-be81-4624-b503-674cc3f2d40f", "parameterMapping": [{"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3dd9edbf-924a-456c-89df-c31e409cc9b2", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e", "useDefault": "false", "value": "\"Retrieve Request number\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "7dce930c-5191-4773-96e6-e89e41fd4519", "versionId": "2acf1e5a-5b0c-469f-a860-8c39283bd3f4", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ec645e07-c256-4fe3-aee2-65245704a8dd", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e", "useDefault": "false", "value": "tw.local.errorMsg", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "5c59deba-e353-4041-ac13-f0c40396a5b0", "versionId": "58d0819a-a76b-4965-a611-214ba69461f2", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.08c17bf4-d6d6-4cae-8aaa-dca60ce527ec", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.e5b22fbe-cba2-4c89-9a53-3fe08c40114e", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "853d436d-d34e-442e-9b41-d23203b21642", "versionId": "fb883f1c-cb88-4fc0-b4de-c82055b2a637", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1bf6bd61-8f48-497f-8ebf-67949c77849b", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "result", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.5bbc1aa6-36ac-4be8-9673-b0b984016af4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:386f", "versionId": "55cd9cdc-9682-455b-a473-262302858cdc", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "886", "y": "85", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.5bbc1aa6-36ac-4be8-9673-b0b984016af4", "scriptTypeId": "2", "isActive": "true", "script": "\tvar requestNumber = tw.local.results[0].rows[0].data[0];\r\r\n\t// arr is array to store the splitted request number\r\r\n\tvar arr = requestNumber.split('-');\r\r\n\t// x var the incremental part of request type \r\r\n\tvar x = parseInt(arr[1]);\r\r\nif(x == \"\")\r\r\n\t tw.local.newRequestId = tw.local.parentRequestNumber+\"-01\";\r\r\nelse\r\r\n{\t \r\r\n\tx= x+1;\r\r\n\t\r\r\n\tif(x < 9)\r\r\n\ttw.local.tmpvar = \"0\"+ x;\r\r\n\telse\r\r\n\ttw.local.tmpvar = x;\r\r\n\t\r\r\n\t\r\r\n\ttw.local.newRequestId = tw.local.parentRequestNumber+'-'+tw.local.tmpvar;\r\r\n\t\r\r\n}\r\r\n", "isRule": "false", "guid": "8f9744ef-ee03-4a90-b2f8-e5aebba29937", "versionId": "8f4f470e-8f2f-44e3-8704-a773e7b61d4a"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8c10d3c7-629b-48bf-88ef-610c7c360cec", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.9d5b0df1-daf0-4667-a8b3-7d9dd99bfc81", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ca84402e5fd92838:-7c46c9a3:18b90bc5b30:6338", "versionId": "7bca81a1-51e0-4baa-ae66-3ab78c75e7f4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "441", "y": "53", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.9d5b0df1-daf0-4667-a8b3-7d9dd99bfc81", "scriptTypeId": "2", "isActive": "true", "script": "var requestNumber =  tw.local.results[0].rows[0].data[0];\r\r\nif(requestNumber.indexOf(\"-\") == -1 )\r\r\n    tw.local.newRequestId = requestNumber + \"-01\"; \r\r\nelse {\r\r\n    var arr = requestNumber.split('-');\r\r\n    var incrementalPart =  arr[1];\r\r\n    \r\r\n    if(incrementalPart[0] == \"0\" && parseInt(incrementalPart[1]) <9 ){\r\r\n     var num = parseInt(incrementalPart[1]) + 1;\r\r\n     tw.local.newRequestId = arr[0] + \"-0\" + num; \r\r\n     }\r\r\n    else if (parseInt(incrementalPart[1]) == 9 ){\r\r\n      var num = parseInt(incrementalPart[1]) + 1;\r\r\n      tw.local.newRequestId = arr[0] + \"-\" + num;\r\r\n    }\r\r\n   else {\r\r\n      var num = parseInt(incrementalPart) + 1;\r\r\n      tw.local.newRequestId = arr[0] + \"-\" + num;\r\r\n     }\r\r\n}", "isRule": "false", "guid": "0f9c1629-479d-43c8-bab2-0d025492f631", "versionId": "a75beee5-241a-4852-9216-c7cc7852be8a"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.f954021d-5b40-47e1-8ab7-936a77ee8977", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796", "versionId": "b0213b1c-89da-4486-b92e-6e41818095bd", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "555", "y": "75", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.f954021d-5b40-47e1-8ab7-936a77ee8977", "haltProcess": "false", "guid": "bb432457-06fd-4b80-b2e2-f80be77d5a2e", "versionId": "b139f707-b9e0-4afd-954b-cd77390db004"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.0b5388d2-710c-4d60-8273-fc5083f9df41", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "Sql query", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.16eb9624-d0e3-4f29-994c-4d0da980e532", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7", "guid": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:386d", "versionId": "f647ea52-7a06-42c6-aabf-80f8e336f527", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.5442262b-69bc-4936-84ba-fd68ea2d694d", "processItemId": "2025.0b5388d2-710c-4d60-8273-fc5083f9df41", "location": "1", "script": {"isNull": "true"}, "guid": "2af1ce05-e9ea-427e-8310-a1fc157e2e8f", "versionId": "eaaeaea6-14b8-4e63-bfa4-5fe49eac08e6"}, "layoutData": {"x": "127", "y": "52", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3cea", "errorHandlerItemId": "2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.16eb9624-d0e3-4f29-994c-4d0da980e532", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sql = \"select requestNo from ODC_RequestInfo where PARENTREQUESTNO = ? order by requestNo desc;\"\r\r\n\r\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.parameters[0] = new tw.object.SQLParameter();\r\r\ntw.local.parameters[0].value = tw.local.parentRequestNumber;", "isRule": "false", "guid": "e9b761eb-39db-4aa6-833a-62d85a468e11", "versionId": "7c002fad-4ae6-4659-bf72-d6448f54251a"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "name": "Sql execute statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7", "guid": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:386e", "versionId": "f6c3370a-aa1a-462d-9573-8776b543558f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "296", "y": "53", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3cea", "errorHandlerItemId": "2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "376463c8-0ea6-4c19-8ea5-690a56760a56", "versionId": "0f5d4ed2-c9f1-4dee-bcbb-28fbc198eec6", "parameterMapping": [{"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.08e049c0-4e27-489c-9197-2a79b4517ed0", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "b3f6144d-66d5-4229-8471-96ae4d2736a6", "versionId": "01bfdc07-5ef0-43f0-848f-8d9782ce1dd8", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6a9bb069-57ca-4d6c-b449-fdafae127d45", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f", "useDefault": "false", "value": "tw.local.parameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "0ee112eb-59c3-4642-b048-8bb553ea73ec", "versionId": "6bda7fde-fd6c-4a51-bf75-1a863ddf8654", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.abce46e1-3ad8-4b5f-88f3-9212df531da1", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "07e21211-0590-4e38-97c0-9ef6b67b7ca6", "versionId": "70d7c636-c768-4344-b703-72beb96e3d47", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ed500d99-73b0-4322-b775-36ff43b31243", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "638ef13b-9d8c-4fe0-9013-97b308e446ea", "versionId": "7f1989de-4829-4d87-a1ca-cf0ed6db91a1", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a774a2c9-6df2-4f57-951d-7ef933ee125b", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.58011b9e-7e90-409b-bb7c-c67b4e9f2d5f", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "2742d7ac-7113-4787-a24e-9c65db878578", "versionId": "d1e5455a-b91f-4639-92cc-fa2edf7e58cb", "description": {"isNull": "true"}}]}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "6", "y": "74", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Retrieve Request Number", "id": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "parentRequestNumber", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.e1ca7465-4084-405d-8ea6-ab3f3762de92", "ns16:extensionElements": {"ns3:defaultValue": {"_": "//\"00104230000146\"\r\r\n\"07704230000198\"", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "newRequestId", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.29fabc80-90b8-4cad-81e3-1c319c6f595a"}, {"name": "errorMsg", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.4ec4b61d-5cd9-43b6-827c-c1801162373f"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.e1ca7465-4084-405d-8ea6-ab3f3762de92"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.29fabc80-90b8-4cad-81e3-1c319c6f595a", "2055.4ec4b61d-5cd9-43b6-827c-c1801162373f"]}}, "ns16:laneSet": {"id": "6dfe740c-4ad3-4885-8e15-0632cf12d377", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "cc2b447a-4251-4882-874d-36323f92a7d5", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["ec1cbe89-1f9d-4798-858b-4facbe857909", "9a4f7402-5fc1-40f6-82b0-b18ea19b31f9", "0b5388d2-710c-4d60-8273-fc5083f9df41", "a4578e04-1773-48d1-81e1-c385d8bb3b7c", "1bf6bd61-8f48-497f-8ebf-67949c77849b", "7e104f2f-8557-453c-83ba-277190edbcac", "b5f48c1b-9e18-4afc-8b65-2ec137de13c5", "37f8f1ce-3d46-40c9-89ed-4426d7549be7", "8c10d3c7-629b-48bf-88ef-610c7c360cec"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "ec1cbe89-1f9d-4798-858b-4facbe857909", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "6", "y": "74", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.d485ccef-7aa5-461f-8750-141206f211b6"}, "ns16:endEvent": {"name": "End", "id": "9a4f7402-5fc1-40f6-82b0-b18ea19b31f9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "555", "y": "75", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796"}, "ns16:incoming": ["f56cd9b4-ec5b-4773-869a-66ff2e676749", "03214778-213c-410b-87ac-1aeb1579f0c7"]}, "ns16:sequenceFlow": [{"sourceRef": "ec1cbe89-1f9d-4798-858b-4facbe857909", "targetRef": "0b5388d2-710c-4d60-8273-fc5083f9df41", "name": "To End", "id": "2027.d485ccef-7aa5-461f-8750-141206f211b6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "0b5388d2-710c-4d60-8273-fc5083f9df41", "targetRef": "a4578e04-1773-48d1-81e1-c385d8bb3b7c", "name": "To Sql execute statement", "id": "5a949aee-cc6f-4dcf-8beb-c49a4a34fabb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a4578e04-1773-48d1-81e1-c385d8bb3b7c", "targetRef": "8c10d3c7-629b-48bf-88ef-610c7c360cec", "name": "To result", "id": "ad377e79-eb89-4219-83f0-19ca1836eea0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "8c10d3c7-629b-48bf-88ef-610c7c360cec", "targetRef": "9a4f7402-5fc1-40f6-82b0-b18ea19b31f9", "name": "To End", "id": "f56cd9b4-ec5b-4773-869a-66ff2e676749", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "b5f48c1b-9e18-4afc-8b65-2ec137de13c5", "targetRef": "37f8f1ce-3d46-40c9-89ed-4426d7549be7", "name": "To Error handling", "id": "be825c7f-339e-4b48-8f4c-67246f6be687", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "37f8f1ce-3d46-40c9-89ed-4426d7549be7", "targetRef": "9a4f7402-5fc1-40f6-82b0-b18ea19b31f9", "name": "To End", "id": "03214778-213c-410b-87ac-1aeb1579f0c7", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"}}, {"sourceRef": "7e104f2f-8557-453c-83ba-277190edbcac", "targetRef": "37f8f1ce-3d46-40c9-89ed-4426d7549be7", "name": "To Error handling", "id": "0f6180ed-0fdd-4e8e-8b43-ced7c398f14e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Sql query", "id": "0b5388d2-710c-4d60-8273-fc5083f9df41", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "127", "y": "52", "width": "95", "height": "70"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.d485ccef-7aa5-461f-8750-141206f211b6", "ns16:outgoing": "5a949aee-cc6f-4dcf-8beb-c49a4a34fabb", "ns16:script": "tw.local.sql = \"select requestNo from ODC_RequestInfo where PARENTREQUESTNO = ? order by requestNo desc;\"\r\r\n\r\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.parameters[0] = new tw.object.SQLParameter();\r\r\ntw.local.parameters[0].value = tw.local.parentRequestNumber;"}, {"scriptFormat": "text/x-javascript", "name": "result", "id": "1bf6bd61-8f48-497f-8ebf-67949c77849b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "886", "y": "85", "width": "95", "height": "70"}}, "ns16:script": "\tvar requestNumber = tw.local.results[0].rows[0].data[0];\r\r\n\t// arr is array to store the splitted request number\r\r\n\tvar arr = requestNumber.split('-');\r\r\n\t// x var the incremental part of request type \r\r\n\tvar x = parseInt(arr[1]);\r\r\nif(x == \"\")\r\r\n\t tw.local.newRequestId = tw.local.parentRequestNumber+\"-01\";\r\r\nelse\r\r\n{\t \r\r\n\tx= x+1;\r\r\n\t\r\r\n\tif(x < 9)\r\r\n\ttw.local.tmpvar = \"0\"+ x;\r\r\n\telse\r\r\n\ttw.local.tmpvar = x;\r\r\n\t\r\r\n\t\r\r\n\ttw.local.newRequestId = tw.local.parentRequestNumber+'-'+tw.local.tmpvar;\r\r\n\t\r\r\n}\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "output", "id": "8c10d3c7-629b-48bf-88ef-610c7c360cec", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "441", "y": "53", "width": "95", "height": "70"}}, "ns16:incoming": "ad377e79-eb89-4219-83f0-19ca1836eea0", "ns16:outgoing": "f56cd9b4-ec5b-4773-869a-66ff2e676749", "ns16:script": "var requestNumber =  tw.local.results[0].rows[0].data[0];\r\r\nif(requestNumber.indexOf(\"-\") == -1 )\r\r\n    tw.local.newRequestId = requestNumber + \"-01\"; \r\r\nelse {\r\r\n    var arr = requestNumber.split('-');\r\r\n    var incrementalPart =  arr[1];\r\r\n    \r\r\n    if(incrementalPart[0] == \"0\" && parseInt(incrementalPart[1]) <9 ){\r\r\n     var num = parseInt(incrementalPart[1]) + 1;\r\r\n     tw.local.newRequestId = arr[0] + \"-0\" + num; \r\r\n     }\r\r\n    else if (parseInt(incrementalPart[1]) == 9 ){\r\r\n      var num = parseInt(incrementalPart[1]) + 1;\r\r\n      tw.local.newRequestId = arr[0] + \"-\" + num;\r\r\n    }\r\r\n   else {\r\r\n      var num = parseInt(incrementalPart) + 1;\r\r\n      tw.local.newRequestId = arr[0] + \"-\" + num;\r\r\n     }\r\r\n}"}], "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Sql execute statement", "id": "a4578e04-1773-48d1-81e1-c385d8bb3b7c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "296", "y": "53", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "5a949aee-cc6f-4dcf-8beb-c49a4a34fabb", "ns16:outgoing": "ad377e79-eb89-4219-83f0-19ca1836eea0", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.parameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Error handling", "id": "37f8f1ce-3d46-40c9-89ed-4426d7549be7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "295", "y": "166", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["be825c7f-339e-4b48-8f4c-67246f6be687", "0f6180ed-0fdd-4e8e-8b43-ced7c398f14e"], "ns16:outgoing": "03214778-213c-410b-87ac-1aeb1579f0c7", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Retrieve Request number\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "a4578e04-1773-48d1-81e1-c385d8bb3b7c", "parallelMultiple": "false", "name": "Error1", "id": "7e104f2f-8557-453c-83ba-277190edbcac", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "331", "y": "111", "width": "24", "height": "24"}}, "ns16:outgoing": "0f6180ed-0fdd-4e8e-8b43-ced7c398f14e", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "c42afc2e-0b79-42bf-8f97-4bc1195ed87b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "c0f6d36d-2272-4ade-805d-6fe5c501b5ee", "eventImplId": "326a6dce-8657-4ce2-8ccb-3471650944df", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "0b5388d2-710c-4d60-8273-fc5083f9df41", "parallelMultiple": "false", "name": "Error2", "id": "b5f48c1b-9e18-4afc-8b65-2ec137de13c5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "162", "y": "110", "width": "24", "height": "24"}}, "ns16:outgoing": "be825c7f-339e-4b48-8f4c-67246f6be687", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "3f003487-ce71-478d-876c-1c4c9505d918"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "9ec4a605-47fa-418f-86c8-59df972271cc", "eventImplId": "521505e7-454b-4300-8dde-cbf555d237fa", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.abcd088d-e709-49e0-8fd2-41864cba3248"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.d14abeae-0693-4705-8321-ca8946b4bfa6", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();\r\nautoObject[0].type = \"\";\r\nautoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();\r\nautoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();\r\nautoObject[0].columns[0].catalogName = \"\";\r\nautoObject[0].columns[0].columnClassName = \"\";\r\nautoObject[0].columns[0].columnDisplaySize = 0;\r\nautoObject[0].columns[0].columnLabel = \"\";\r\nautoObject[0].columns[0].columnName = \"\";\r\nautoObject[0].columns[0].columnTypeName = \"\";\r\nautoObject[0].columns[0].precision = 0;\r\nautoObject[0].columns[0].scale = 0;\r\nautoObject[0].columns[0].schemaName = \"\";\r\nautoObject[0].columns[0].tableName = \"\";\r\nautoObject[0].columns[0].autoIncrement = false;\r\nautoObject[0].columns[0].caseSensitive = false;\r\nautoObject[0].columns[0].currency = false;\r\nautoObject[0].columns[0].definitelyWritable = false;\r\nautoObject[0].columns[0].nullable = 0;\r\nautoObject[0].columns[0].readOnly = false;\r\nautoObject[0].columns[0].searchable = false;\r\nautoObject[0].columns[0].signed = false;\r\nautoObject[0].columns[0].writable = false;\r\nautoObject[0].columnIndexes = null;\r\nautoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();\r\nautoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();\r\nautoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();\r\nautoObject[0].rows[0].data[0] = null;\r\nautoObject[0].rows[0].indexedMap = null;\r\nautoObject[0].updateCount = 0;\r\nautoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();\r\nautoObject[0].outValues[0] = null;\r\nautoObject", "useDefault": "false"}}}, {"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "parameters", "id": "2056.e16311e3-1714-42d0-8b05-b45a80bc6699"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "tmpvar", "id": "2056.9b12ef2c-51c8-4325-80a1-8e740fcacb5d"}]}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.03214778-213c-410b-87ac-1aeb1579f0c7", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7", "2025.37f8f1ce-3d46-40c9-89ed-4426d7549be7"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9", "2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9"], "guid": "8c063a75-**************-e82c74a87ca2", "versionId": "14085622-464c-4517-b181-c4f96af87f13", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}, {"name": "To result", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ad377e79-eb89-4219-83f0-19ca1836eea0", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c", "2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.8c10d3c7-629b-48bf-88ef-610c7c360cec", "2025.8c10d3c7-629b-48bf-88ef-610c7c360cec"], "guid": "c4f62e25-e3fe-4739-8336-78569eb86907", "versionId": "795a9892-2e0a-4078-8fb8-7676f9b6fe26", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f56cd9b4-ec5b-4773-869a-66ff2e676749", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8c10d3c7-629b-48bf-88ef-610c7c360cec", "2025.8c10d3c7-629b-48bf-88ef-610c7c360cec"], "endStateId": "Out", "toProcessItemId": ["2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9", "2025.9a4f7402-5fc1-40f6-82b0-b18ea19b31f9"], "guid": "f12012eb-0340-4bfc-b6c1-e6d781827bb9", "versionId": "d5deb2ce-692f-4de4-a09e-30102981cbbb", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Sql execute statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.5a949aee-cc6f-4dcf-8beb-c49a4a34fabb", "processId": "1.509c6ef8-f489-4ab8-bcb8-45c4531aa546", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.0b5388d2-710c-4d60-8273-fc5083f9df41", "2025.0b5388d2-710c-4d60-8273-fc5083f9df41"], "endStateId": "Out", "toProcessItemId": ["2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c", "2025.a4578e04-1773-48d1-81e1-c385d8bb3b7c"], "guid": "5d93acb1-4ce5-4ccf-b9bf-d0d23d951e66", "versionId": "f5272815-b6bd-4a3e-9109-caf13c72f5e4", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}