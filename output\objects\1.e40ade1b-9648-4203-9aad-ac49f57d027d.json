{"id": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "versionId": "11b29981-5b4f-4708-b14b-a10a0c95da8b", "name": "Log FC Transactions", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "Log FC Transactions", "lastModified": "1700033891771", "lastModifiedBy": "<PERSON><PERSON>", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.a40e11d3-1311-4699-89a1-cedd867ffa99", "2025.a40e11d3-1311-4699-89a1-cedd867ffa99"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5db0", "versionId": "11b29981-5b4f-4708-b14b-a10a0c95da8b", "dependencySummary": "<dependencySummary id=\"bpdid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5700\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.bef676cb-38b6-4603-8cfb-f855f51df6c2\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"9294f675-df57-471e-8c74-************\"},{\"incoming\":[\"be348a8b-2023-451d-8b47-5aaa0f53abc8\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":709,\"y\":82,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5dae\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"324faa58-845a-49a8-8cc2-03d587c7bcbe\"},{\"targetRef\":\"a40e11d3-1311-4699-89a1-cedd867ffa99\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To update?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.bef676cb-38b6-4603-8cfb-f855f51df6c2\",\"sourceRef\":\"9294f675-df57-471e-8c74-************\"},{\"startQuantity\":1,\"outgoing\":[\"ac8f4eb9-d575-4863-820f-5317b3e03929\"],\"incoming\":[\"aabb13ea-16d8-4c74-8177-59ba78286e1f\",\"6ffae001-80c1-4540-8d0a-b724be115e87\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":458,\"y\":58,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Insert SQL\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"a2e4cae9-40e8-4fe0-8879-0cb448706b61\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"function addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\n\\r\\ntw.local.sqlStatements = new tw.object.listOf.SQLStatement();\\r\\nvar sqlStatementLen = 0;\\r\\nif(!!tw.local.odcRequest.FcCollections) {\\r\\n  if(!! tw.local.odcRequest.FcCollections.selectedTransactions){\\r\\n     \\r\\n     for (var i = 0 ;i <  tw.local.odcRequest.FcCollections.selectedTransactions.listLength;i++) {\\r\\n\\r\\n            sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n       \\r\\n            tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\r\\n      \\ttw.local.sqlStatements[sqlStatementLen].sql = \\\"INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_FCTRANSACTION\\\"\\r\\n                                                          +\\\" (ACCOUNTNO,POSTINGDATE,VALUEDATE,TRANSACTIONAMOUNT,AMOUNTINBPM, REQUESRID)\\\"\\r\\n                                                          +\\\" VALUES (?,?,?,?,?,?)\\\";\\r\\n\\t\\t\\t\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n \\t\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo  ));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate  ));\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate) );\\r\\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount ) ); \\r\\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].existAmount ) ); \\r\\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo) ); \\r\\n       } \\r\\n    } \\r\\n\\r\\n} \"]}},{\"targetRef\":\"fb4b426c-c39d-4459-8a0e-cf85bd834f08\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"ac8f4eb9-d575-4863-820f-5317b3e03929\",\"sourceRef\":\"a2e4cae9-40e8-4fe0-8879-0cb448706b61\"},{\"outgoing\":[\"aabb13ea-16d8-4c74-8177-59ba78286e1f\",\"9d82b456-**************-bc8be4931679\"],\"incoming\":[\"2027.bef676cb-38b6-4603-8cfb-f855f51df6c2\"],\"default\":\"aabb13ea-16d8-4c74-8177-59ba78286e1f\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":104,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"update?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"a40e11d3-1311-4699-89a1-cedd867ffa99\"},{\"targetRef\":\"a2e4cae9-40e8-4fe0-8879-0cb448706b61\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"aabb13ea-16d8-4c74-8177-59ba78286e1f\",\"sourceRef\":\"a40e11d3-1311-4699-89a1-cedd867ffa99\"},{\"startQuantity\":1,\"outgoing\":[\"09b27a91-6c8f-4d18-8471-4582e7ca0e5e\"],\"incoming\":[\"9d82b456-**************-bc8be4931679\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":218,\"y\":148,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\r\\n \\r\\n\"]},\"name\":\"Delete transactions data\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"3c20d966-fe63-4ba0-83a2-bfe3dd551e21\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\\r\\n\\/**-------------------------- Add SQL Parameters --------------------------**\\/\\r\\nfunction addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\n\\r\\n\\r\\nvar sqlStatementLen= 0;\\r\\n\\r\\nfunction DeleteRecordsByID(tableName){\\r\\n\\t \\r\\n\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" DELETE FROM \\\"+ tw.env.DBSchema + \\\".\\\"+ tableName + \\\" WHERE REQUESTNO= ? \\\";\\r\\n\\t\\t\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\\r\\n  \\t \\r\\n}\\r\\n\\r\\nDeleteRecordsByID (   \\\"ODC_FCTransaction\\\"   );\\r\\n\\r\\n\\r\\n \"]}},{\"itemSubjectRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"name\":\"sqlStatements\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.3df9eb39-fc60-4451-8317-22a5a2276b9a\"},{\"targetRef\":\"3c20d966-fe63-4ba0-83a2-bfe3dd551e21\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.update\\t  ==\\t  true\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Delete transactions data\",\"declaredType\":\"sequenceFlow\",\"id\":\"9d82b456-**************-bc8be4931679\",\"sourceRef\":\"a40e11d3-1311-4699-89a1-cedd867ffa99\"},{\"startQuantity\":1,\"outgoing\":[\"6ffae001-80c1-4540-8d0a-b724be115e87\"],\"incoming\":[\"09b27a91-6c8f-4d18-8471-4582e7ca0e5e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":342,\"y\":148,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Delete ODC Request Execute SQL\",\"dataInputAssociation\":[{\"targetRef\":\"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlStatements\"]}}]},{\"targetRef\":\"2055.628ceac6-aa42-426b-97c7-540674f12f38\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"SQLResult\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"e52c4730-6443-4709-82f3-15a778427c9d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.24961b69-3b9b-4311-8707-6b6dfffaf207\"]}],\"calledElement\":\"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af\"},{\"parallelMultiple\":false,\"outgoing\":[\"5ba6ff0a-2442-4f9c-8142-4d7ddcfb1873\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"124edf7c-ca1a-4ac9-8c62-af467eeedfbe\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"74924cbc-a167-4f5b-8afe-5a7ec32a7511\",\"otherAttributes\":{\"eventImplId\":\"52112ef7-7592-4dc9-8ec5-d45517f98e0d\"}}],\"attachedToRef\":\"e52c4730-6443-4709-82f3-15a778427c9d\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":377,\"y\":206,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"a91e5ea0-ddc1-46c6-8306-b60f487091cd\",\"outputSet\":{}},{\"targetRef\":\"e52c4730-6443-4709-82f3-15a778427c9d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Delete ODC Request Execute SQL\",\"declaredType\":\"sequenceFlow\",\"id\":\"09b27a91-6c8f-4d18-8471-4582e7ca0e5e\",\"sourceRef\":\"3c20d966-fe63-4ba0-83a2-bfe3dd551e21\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"results\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.70ff598f-a823-4678-89b0-208226f5522e\"},{\"startQuantity\":1,\"outgoing\":[\"be348a8b-2023-451d-8b47-5aaa0f53abc8\"],\"incoming\":[\"ac8f4eb9-d575-4863-820f-5317b3e03929\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":575,\"y\":59,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"SQL Execute Statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.628ceac6-aa42-426b-97c7-540674f12f38\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"SQLResult\\\"\"]}}]},{\"targetRef\":\"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlStatements\"]}}]},{\"targetRef\":\"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"fb4b426c-c39d-4459-8a0e-cf85bd834f08\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.24961b69-3b9b-4311-8707-6b6dfffaf207\"]}],\"calledElement\":\"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af\"},{\"targetRef\":\"a2e4cae9-40e8-4fe0-8879-0cb448706b61\",\"extensionElements\":{\"endStateId\":[\"guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Insert SQL\",\"declaredType\":\"sequenceFlow\",\"id\":\"6ffae001-80c1-4540-8d0a-b724be115e87\",\"sourceRef\":\"e52c4730-6443-4709-82f3-15a778427c9d\"},{\"targetRef\":\"324faa58-845a-49a8-8cc2-03d587c7bcbe\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"be348a8b-2023-451d-8b47-5aaa0f53abc8\",\"sourceRef\":\"fb4b426c-c39d-4459-8a0e-cf85bd834f08\"},{\"startQuantity\":1,\"outgoing\":[\"045087b8-0428-4ee8-8cf7-9119b5bf627a\"],\"incoming\":[\"5ba6ff0a-2442-4f9c-8142-4d7ddcfb1873\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":575,\"y\":201,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Log FC Transactions\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"21b80e03-8941-451c-866c-14bd2f0c42a0\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"21b80e03-8941-451c-866c-14bd2f0c42a0\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"5ba6ff0a-2442-4f9c-8142-4d7ddcfb1873\",\"sourceRef\":\"a91e5ea0-ddc1-46c6-8306-b60f487091cd\"},{\"incoming\":[\"045087b8-0428-4ee8-8cf7-9119b5bf627a\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"fa61e9e9-d793-4d46-826d-e15f5bc0141c\",\"otherAttributes\":{\"eventImplId\":\"c506fa39-d1a7-429e-8280-2c0ca59186cd\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":721,\"y\":224,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"fe17ce92-04be-42be-8877-71a5c3aec1de\"},{\"targetRef\":\"fe17ce92-04be-42be-8877-71a5c3aec1de\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"045087b8-0428-4ee8-8cf7-9119b5bf627a\",\"sourceRef\":\"21b80e03-8941-451c-866c-14bd2f0c42a0\"}],\"laneSet\":[{\"id\":\"a400793e-3fa1-4c02-81ab-e0f4dc2d7c04\",\"lane\":[{\"flowNodeRef\":[\"9294f675-df57-471e-8c74-************\",\"324faa58-845a-49a8-8cc2-03d587c7bcbe\",\"a2e4cae9-40e8-4fe0-8879-0cb448706b61\",\"a40e11d3-1311-4699-89a1-cedd867ffa99\",\"3c20d966-fe63-4ba0-83a2-bfe3dd551e21\",\"e52c4730-6443-4709-82f3-15a778427c9d\",\"a91e5ea0-ddc1-46c6-8306-b60f487091cd\",\"fb4b426c-c39d-4459-8a0e-cf85bd834f08\",\"21b80e03-8941-451c-866c-14bd2f0c42a0\",\"fe17ce92-04be-42be-8877-71a5c3aec1de\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"91ae0b58-56da-4836-8a97-975bc29d39fd\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Log FC Transactions\",\"declaredType\":\"process\",\"id\":\"1.e40ade1b-9648-4203-9aad-ac49f57d027d\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.dbbaa564-7c42-4fac-82f1-8a8ad9cd628e\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.53ace421-ea45-405b-81e5-e7b06effafca\",\"2055.3ff73486-8c6e-46ae-8bd4-a53076f701a2\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.dbbaa564-7c42-4fac-82f1-8a8ad9cd628e\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"54321\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"1234\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 5000.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 2000.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject.folderPath = \\\"\\\";\\nautoObject.templateDocID = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.53ace421-ea45-405b-81e5-e7b06effafca\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"false\"}]},\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"update\",\"isCollection\":false,\"id\":\"2055.3ff73486-8c6e-46ae-8bd4-a53076f701a2\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.53ace421-ea45-405b-81e5-e7b06effafca", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"54321\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"1234\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 5000.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 2000.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"\";\r\nautoObject.templateDocID = \"\";\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "74affcba-aa70-43e0-8c29-08a4149abb85", "versionId": "d564fbf6-34dc-49d3-ae6c-652c57f57aa3"}, {"name": "update", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.3ff73486-8c6e-46ae-8bd4-a53076f701a2", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "2", "hasDefault": "true", "defaultValue": "false", "isLocked": "false", "description": {"isNull": "true"}, "guid": "174e780f-6af2-4595-a424-64679a6f9018", "versionId": "e6f2e72f-ce9a-4bc8-ad69-ed48a9e40fb6"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.739da32f-1d76-4a8a-a3b5-d5fd77b7fd4a", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "parameterType": "3", "isArrayOf": "false", "classId": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "23df78fe-8112-44c4-83fe-707bd98fd68d", "versionId": "3837e18d-228f-4f82-a008-9414f3fb59ae"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.dbbaa564-7c42-4fac-82f1-8a8ad9cd628e", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "a339324c-5979-43e0-a714-c7e6f6700781", "versionId": "70b340ba-dd72-4f34-8025-d77bf8867f92"}], "processVariable": [{"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3df9eb39-fc60-4451-8317-22a5a2276b9a", "description": {"isNull": "true"}, "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0bb701b0-6347-42d2-8acf-c408d0927599", "versionId": "2a89af51-a578-4547-b78c-e71c18814bca"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.70ff598f-a823-4678-89b0-208226f5522e", "description": {"isNull": "true"}, "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a2e7e8c4-2631-4594-bc9e-abc314357997", "versionId": "db1c5654-1ae5-477b-b407-e9cf316a235e"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fe17ce92-04be-42be-8877-71a5c3aec1de", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.4d3cbaa0-edac-4fdf-9da4-ffbfcb4e68fe", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5cdd", "versionId": "0ed72fb6-daa0-4cb6-b050-0539174b5555", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "721", "y": "224", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.4d3cbaa0-edac-4fdf-9da4-ffbfcb4e68fe", "message": "", "faultStyle": "1", "guid": "09b9b80b-7302-4b94-ae05-4ff592e1079d", "versionId": "c9e6a651-f758-44ff-9c20-374678c9eb99", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0d873d4d-b3f0-45e0-a01c-35eae93214e5", "processParameterId": "2055.739da32f-1d76-4a8a-a3b5-d5fd77b7fd4a", "parameterMappingParentId": "3007.4d3cbaa0-edac-4fdf-9da4-ffbfcb4e68fe", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "isList": "false", "isInput": "false", "guid": "7034f663-b136-43d1-8f98-cad08b9faf65", "versionId": "6fc66776-3f4c-4d9f-a980-f8d4e63e159a", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "Delete transactions data", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.08c82074-877d-4fa4-9ad3-c91c5f1f7fa0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5d6d", "versionId": "2639a5e7-9a5f-428a-b9d3-c587f17e6f3e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.defb93ec-9ef7-4002-baf6-924f3ff12c78", "processItemId": "2025.3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "location": "1", "script": "\r\r\n \r\r\n", "guid": "b2960968-5e60-40c0-bce6-9c951f71e67d", "versionId": "8f6c6798-9ee5-4aaf-b6c7-e6b676ba06d8"}, "layoutData": {"x": "218", "y": "148", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.08c82074-877d-4fa4-9ad3-c91c5f1f7fa0", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n\r\r\n\r\r\nvar sqlStatementLen= 0;\r\r\n\r\r\nfunction DeleteRecordsByID(tableName){\r\r\n\t \r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" DELETE FROM \"+ tw.env.DBSchema + \".\"+ tableName + \" WHERE REQUESTNO= ? \";\r\r\n\t\t\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\n  \t \r\r\n}\r\r\n\r\r\nDeleteRecordsByID (   \"ODC_FCTransaction\"   );\r\r\n\r\r\n\r\r\n ", "isRule": "false", "guid": "d141d35b-aff4-4024-a2f4-d1e10542e2af", "versionId": "82af86bc-9b67-41bb-83f2-ae1510e81c82"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a40e11d3-1311-4699-89a1-cedd867ffa99", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "update?", "tWComponentName": "Switch", "tWComponentId": "3013.aa151993-81d6-4748-8f4e-ab93a5313076", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5d78", "versionId": "78751756-9c91-4d39-b655-236edc4b59f7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "104", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.aa151993-81d6-4748-8f4e-ab93a5313076", "guid": "db3f2ea2-1773-41f6-9495-59494d5ceee9", "versionId": "b411be13-b4cb-4857-aa0f-97168ca60925", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.d91f7ae2-c9fa-46b5-8a55-93759dc6d967", "switchId": "3013.aa151993-81d6-4748-8f4e-ab93a5313076", "seq": "1", "endStateId": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5701", "condition": "tw.local.update\t  ==\t  true", "guid": "cc489ca7-be74-4c63-8464-44d8c6afa5f5", "versionId": "a62728e2-51c9-46ef-93fc-04a3c6dc258a"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a2e4cae9-40e8-4fe0-8879-0cb448706b61", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "Insert SQL", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.b8a9a6ce-d37e-4b26-bf4d-e15ed881dcdd", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5d96", "versionId": "89691fdc-6470-4eff-b894-759a12c5563d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "458", "y": "58", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.b8a9a6ce-d37e-4b26-bf4d-e15ed881dcdd", "scriptTypeId": "2", "isActive": "true", "script": "function addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n\r\r\ntw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar sqlStatementLen = 0;\r\r\nif(!!tw.local.odcRequest.FcCollections) {\r\r\n  if(!! tw.local.odcRequest.FcCollections.selectedTransactions){\r\r\n     \r\r\n     for (var i = 0 ;i <  tw.local.odcRequest.FcCollections.selectedTransactions.listLength;i++) {\r\r\n\r\r\n            sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n       \r\r\n            tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n      \ttw.local.sqlStatements[sqlStatementLen].sql = \"INSERT INTO \"+ tw.env.DBSchema + \".ODC_FCTRANSACTION\"\r\r\n                                                          +\" (ACCOUNTNO,POSTINGDATE,VALUEDATE,TRANSACTIONAMOUNT,AMOUNTINBPM, REQUESRID)\"\r\r\n                                                          +\" VALUES (?,?,?,?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n \t\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo  ));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate  ));\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate) );\r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount ) ); \r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].existAmount ) ); \r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo) ); \r\r\n       } \r\r\n    } \r\r\n\r\r\n} ", "isRule": "false", "guid": "e86f1360-74bf-4728-9924-d7cd5666ff67", "versionId": "b62c153b-e837-4ad1-b381-6fafcff69033"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.324faa58-845a-49a8-8cc2-03d587c7bcbe", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.92a6a53c-dc0e-4e4e-b653-7069ea33f7c0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5dae", "versionId": "ad0507a5-5e5f-4aee-a3c7-df9b2f1d047a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "709", "y": "82", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.92a6a53c-dc0e-4e4e-b653-7069ea33f7c0", "haltProcess": "false", "guid": "fd2004b9-4ae5-4020-b820-0f1012bf64b1", "versionId": "2939e80a-4c3e-44f0-a06c-0be1bc615c52"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.21b80e03-8941-451c-866c-14bd2f0c42a0", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.c195bf53-5785-4c06-b329-7b2c1d5a5974", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5cdf", "versionId": "c642e73e-2697-41e3-9111-0bc2b7ec2490", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "575", "y": "201", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.c195bf53-5785-4c06-b329-7b2c1d5a5974", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "eff70a21-7d8d-4b61-9a95-1b96c4e398e8", "versionId": "dc308468-b644-4ee2-8ee1-5761435e2c13", "parameterMapping": [{"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ad2a0921-fdef-4769-977b-47adadd466f1", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.c195bf53-5785-4c06-b329-7b2c1d5a5974", "useDefault": "false", "value": "\"Log FC Transactions\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "fafee8aa-5fe1-468c-9527-8a665774a1f6", "versionId": "6ca63765-9077-4d26-9d65-5506c335d7b1", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e166b6b8-84df-4f45-8028-e80b7fe910fb", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.c195bf53-5785-4c06-b329-7b2c1d5a5974", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "4c4e2ff3-43c7-40ba-aa2c-892729df87ca", "versionId": "d290e93b-198e-45d3-9d73-6a295dc53c41", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.afcdce22-88d5-4378-bc78-c76a468e95b4", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.c195bf53-5785-4c06-b329-7b2c1d5a5974", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "464e1c5e-752d-4e8d-a09a-5adf572a39bb", "versionId": "e4588c40-cf0f-41fb-af26-22b65283e1c7", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e52c4730-6443-4709-82f3-15a778427c9d", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "Delete ODC Request Execute SQL", "tWComponentName": "SubProcess", "tWComponentId": "3012.8de216c2-2b87-4d91-8890-6fc67590d5bb", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.21b80e03-8941-451c-866c-14bd2f0c42a0", "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5d49", "versionId": "e83ee4ea-ec0d-479f-88f9-9dc713305127", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "342", "y": "148", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5cdf", "errorHandlerItemId": "2025.21b80e03-8941-451c-866c-14bd2f0c42a0", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.8de216c2-2b87-4d91-8890-6fc67590d5bb", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "guid": "71f49cf2-43ca-4925-afd4-a4821592b193", "versionId": "64ae4ed4-254d-4c24-b032-d091bc8fd166", "parameterMapping": [{"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.beb4e38b-4aa8-4b8e-b007-3dc511bc643e", "processParameterId": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "parameterMappingParentId": "3012.8de216c2-2b87-4d91-8890-6fc67590d5bb", "useDefault": "false", "value": "tw.local.sqlStatements", "classRef": "/12.3990d8b4-983d-4250-bd06-3600f527fed0", "isList": "true", "isInput": "true", "guid": "e1e1ed98-b7d8-4148-a42f-03e886c43fe0", "versionId": "b023b199-0a9a-42f7-b911-9314cea0aa1f", "description": {"isNull": "true"}}, {"name": "returnType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7797a78a-1af4-461d-81c8-5d42cb4c1283", "processParameterId": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "parameterMappingParentId": "3012.8de216c2-2b87-4d91-8890-6fc67590d5bb", "useDefault": "false", "value": "\"SQLResult\"", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "9add55aa-e7eb-4a08-a0b3-d2629196bde8", "versionId": "b1a9b85d-7e7f-4c13-b78e-c046ea0df52c", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f5d41466-81f7-492f-82e4-ca020d1847c6", "processParameterId": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "parameterMappingParentId": "3012.8de216c2-2b87-4d91-8890-6fc67590d5bb", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "63c3674d-3477-4396-ba00-34d703b1c75c", "versionId": "d837df19-faff-43a3-a4a5-c3cd3f493e12", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.fd156cc2-2ac9-42fa-acab-f24d2c68be59", "processParameterId": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "parameterMappingParentId": "3012.8de216c2-2b87-4d91-8890-6fc67590d5bb", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isList": "false", "isInput": "false", "guid": "00c38f90-acbc-46bf-9efc-c429319eb221", "versionId": "def3d11e-309e-4377-85d9-c2b3e2e54a1a", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fb4b426c-c39d-4459-8a0e-cf85bd834f08", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "name": "SQL Execute Statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.d9b3ca12-a1be-4f56-be59-35aadb36d8ab", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5ce6", "versionId": "febc2cfa-94c4-483b-b040-746b38031791", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "575", "y": "59", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.d9b3ca12-a1be-4f56-be59-35aadb36d8ab", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "guid": "c397d9cd-324d-491d-acd5-e8f2d5107345", "versionId": "45936f10-7f9f-4f21-9d0e-fea2e9144c29", "parameterMapping": [{"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.554342f8-17ae-4c68-b6dc-12bc8af4b28c", "processParameterId": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "parameterMappingParentId": "3012.d9b3ca12-a1be-4f56-be59-35aadb36d8ab", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isList": "false", "isInput": "false", "guid": "0cf3fca6-698e-4a37-a021-d4840e31bcfd", "versionId": "1224970c-fc24-45b8-b57d-62a575e638db", "description": {"isNull": "true"}}, {"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.12667933-6645-473a-a67c-07c6513ab33e", "processParameterId": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "parameterMappingParentId": "3012.d9b3ca12-a1be-4f56-be59-35aadb36d8ab", "useDefault": "false", "value": "tw.local.sqlStatements", "classRef": "/12.3990d8b4-983d-4250-bd06-3600f527fed0", "isList": "true", "isInput": "true", "guid": "e9057b61-701c-453b-ad8b-8962a444ec7d", "versionId": "*************-4772-8c17-43668b4280ad", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f5d62561-2bb2-4e58-88cd-90d2d48832bb", "processParameterId": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "parameterMappingParentId": "3012.d9b3ca12-a1be-4f56-be59-35aadb36d8ab", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "26b25d1d-d0b4-4f9f-b063-7293edf19c91", "versionId": "80a23b68-0ce7-4823-888e-26c2b381a331", "description": {"isNull": "true"}}, {"name": "returnType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5c6b0ee8-2599-418b-8b25-03b4bd8dd99d", "processParameterId": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "parameterMappingParentId": "3012.d9b3ca12-a1be-4f56-be59-35aadb36d8ab", "useDefault": "false", "value": "\"SQLResult\"", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "154a7c6f-a54f-42dc-967c-0a3b5a0a4881", "versionId": "e7b636c9-015f-412e-90d6-2a99a169fb1f", "description": {"isNull": "true"}}]}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Log FC Transactions", "id": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.53ace421-ea45-405b-81e5-e7b06effafca", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"54321\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"1234\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 5000.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 2000.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"\";\r\nautoObject.templateDocID = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "update", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.3ff73486-8c6e-46ae-8bd4-a53076f701a2", "ns16:extensionElements": {"ns3:defaultValue": {"_": "false", "useDefault": "true"}}}], "ns16:dataOutput": {"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.dbbaa564-7c42-4fac-82f1-8a8ad9cd628e"}, "ns16:inputSet": {"ns16:dataInputRefs": ["2055.53ace421-ea45-405b-81e5-e7b06effafca", "2055.3ff73486-8c6e-46ae-8bd4-a53076f701a2"]}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.dbbaa564-7c42-4fac-82f1-8a8ad9cd628e"}}, "ns16:laneSet": {"id": "a400793e-3fa1-4c02-81ab-e0f4dc2d7c04", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "91ae0b58-56da-4836-8a97-975bc29d39fd", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["9294f675-df57-471e-8c74-************", "324faa58-845a-49a8-8cc2-03d587c7bcbe", "a2e4cae9-40e8-4fe0-8879-0cb448706b61", "a40e11d3-1311-4699-89a1-cedd867ffa99", "3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "e52c4730-6443-4709-82f3-15a778427c9d", "a91e5ea0-ddc1-46c6-8306-b60f487091cd", "fb4b426c-c39d-4459-8a0e-cf85bd834f08", "21b80e03-8941-451c-866c-14bd2f0c42a0", "fe17ce92-04be-42be-8877-71a5c3aec1de"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "9294f675-df57-471e-8c74-************", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.bef676cb-38b6-4603-8cfb-f855f51df6c2"}, "ns16:endEvent": [{"name": "End", "id": "324faa58-845a-49a8-8cc2-03d587c7bcbe", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "709", "y": "82", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5dae"}, "ns16:incoming": "be348a8b-2023-451d-8b47-5aaa0f53abc8"}, {"name": "End Event", "id": "fe17ce92-04be-42be-8877-71a5c3aec1de", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "721", "y": "224", "width": "24", "height": "24"}}, "ns16:incoming": "045087b8-0428-4ee8-8cf7-9119b5bf627a", "ns16:errorEventDefinition": {"id": "fa61e9e9-d793-4d46-826d-e15f5bc0141c", "eventImplId": "c506fa39-d1a7-429e-8280-2c0ca59186cd", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "9294f675-df57-471e-8c74-************", "targetRef": "a40e11d3-1311-4699-89a1-cedd867ffa99", "name": "To update?", "id": "2027.bef676cb-38b6-4603-8cfb-f855f51df6c2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a2e4cae9-40e8-4fe0-8879-0cb448706b61", "targetRef": "fb4b426c-c39d-4459-8a0e-cf85bd834f08", "name": "To End", "id": "ac8f4eb9-d575-4863-820f-5317b3e03929", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "a40e11d3-1311-4699-89a1-cedd867ffa99", "targetRef": "a2e4cae9-40e8-4fe0-8879-0cb448706b61", "name": "No", "id": "aabb13ea-16d8-4c74-8177-59ba78286e1f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a40e11d3-1311-4699-89a1-cedd867ffa99", "targetRef": "3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "name": "To Delete transactions data", "id": "9d82b456-**************-bc8be4931679", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.update\t  ==\t  true", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "targetRef": "e52c4730-6443-4709-82f3-15a778427c9d", "name": "To Delete ODC Request Execute SQL", "id": "09b27a91-6c8f-4d18-8471-4582e7ca0e5e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "e52c4730-6443-4709-82f3-15a778427c9d", "targetRef": "a2e4cae9-40e8-4fe0-8879-0cb448706b61", "name": "To Insert SQL", "id": "6ffae001-80c1-4540-8d0a-b724be115e87", "ns16:extensionElements": {"ns3:endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fb4b426c-c39d-4459-8a0e-cf85bd834f08", "targetRef": "324faa58-845a-49a8-8cc2-03d587c7bcbe", "name": "To End", "id": "be348a8b-2023-451d-8b47-5aaa0f53abc8", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb"}}, {"sourceRef": "a91e5ea0-ddc1-46c6-8306-b60f487091cd", "targetRef": "21b80e03-8941-451c-866c-14bd2f0c42a0", "name": "To Exception Handling", "id": "5ba6ff0a-2442-4f9c-8142-4d7ddcfb1873", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "21b80e03-8941-451c-866c-14bd2f0c42a0", "targetRef": "fe17ce92-04be-42be-8877-71a5c3aec1de", "name": "To End Event", "id": "045087b8-0428-4ee8-8cf7-9119b5bf627a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Insert SQL", "id": "a2e4cae9-40e8-4fe0-8879-0cb448706b61", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "458", "y": "58", "width": "95", "height": "70"}}, "ns16:incoming": ["aabb13ea-16d8-4c74-8177-59ba78286e1f", "6ffae001-80c1-4540-8d0a-b724be115e87"], "ns16:outgoing": "ac8f4eb9-d575-4863-820f-5317b3e03929", "ns16:script": "function addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n\r\r\ntw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\r\nvar sqlStatementLen = 0;\r\r\nif(!!tw.local.odcRequest.FcCollections) {\r\r\n  if(!! tw.local.odcRequest.FcCollections.selectedTransactions){\r\r\n     \r\r\n     for (var i = 0 ;i <  tw.local.odcRequest.FcCollections.selectedTransactions.listLength;i++) {\r\r\n\r\r\n            sqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n       \r\r\n            tw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n      \ttw.local.sqlStatements[sqlStatementLen].sql = \"INSERT INTO \"+ tw.env.DBSchema + \".ODC_FCTRANSACTION\"\r\r\n                                                          +\" (ACCOUNTNO,POSTINGDATE,VALUEDATE,TRANSACTIONAMOUNT,AMOUNTINBPM, REQUESRID)\"\r\r\n                                                          +\" VALUES (?,?,?,?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n \t\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo  ));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate  ));\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate) );\r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount ) ); \r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].existAmount ) ); \r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo) ); \r\r\n       } \r\r\n    } \r\r\n\r\r\n} "}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Delete transactions data", "id": "3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "218", "y": "148", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "\r\r\n \r\r\n"}, "ns16:incoming": "9d82b456-**************-bc8be4931679", "ns16:outgoing": "09b27a91-6c8f-4d18-8471-4582e7ca0e5e", "ns16:script": "tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n\r\r\n\r\r\nvar sqlStatementLen= 0;\r\r\n\r\r\nfunction DeleteRecordsByID(tableName){\r\r\n\t \r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" DELETE FROM \"+ tw.env.DBSchema + \".\"+ tableName + \" WHERE REQUESTNO= ? \";\r\r\n\t\t\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\n  \t \r\r\n}\r\r\n\r\r\nDeleteRecordsByID (   \"ODC_FCTransaction\"   );\r\r\n\r\r\n\r\r\n "}], "ns16:exclusiveGateway": {"default": "aabb13ea-16d8-4c74-8177-59ba78286e1f", "name": "update?", "id": "a40e11d3-1311-4699-89a1-cedd867ffa99", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "104", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "2027.bef676cb-38b6-4603-8cfb-f855f51df6c2", "ns16:outgoing": ["aabb13ea-16d8-4c74-8177-59ba78286e1f", "9d82b456-**************-bc8be4931679"]}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0", "isCollection": "true", "name": "sqlStatements", "id": "2056.3df9eb39-fc60-4451-8317-22a5a2276b9a"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.70ff598f-a823-4678-89b0-208226f5522e"}], "ns16:callActivity": [{"calledElement": "1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Delete ODC Request Execute SQL", "id": "e52c4730-6443-4709-82f3-15a778427c9d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "342", "y": "148", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "09b27a91-6c8f-4d18-8471-4582e7ca0e5e", "ns16:outgoing": "6ffae001-80c1-4540-8d0a-b724be115e87", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlStatements", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0"}}}, {"ns16:targetRef": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "ns16:assignment": {"ns16:from": {"_": "\"SQLResult\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "SQL Execute Statement", "id": "fb4b426c-c39d-4459-8a0e-cf85bd834f08", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "575", "y": "59", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "ac8f4eb9-d575-4863-820f-5317b3e03929", "ns16:outgoing": "be348a8b-2023-451d-8b47-5aaa0f53abc8", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "ns16:assignment": {"ns16:from": {"_": "\"SQLResult\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlStatements", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0"}}}, {"ns16:targetRef": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Exception Handling", "id": "21b80e03-8941-451c-866c-14bd2f0c42a0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "575", "y": "201", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "5ba6ff0a-2442-4f9c-8142-4d7ddcfb1873", "ns16:outgoing": "045087b8-0428-4ee8-8cf7-9119b5bf627a", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Log FC Transactions\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}], "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "e52c4730-6443-4709-82f3-15a778427c9d", "parallelMultiple": "false", "name": "Error1", "id": "a91e5ea0-ddc1-46c6-8306-b60f487091cd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "377", "y": "206", "width": "24", "height": "24"}}, "ns16:outgoing": "5ba6ff0a-2442-4f9c-8142-4d7ddcfb1873", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "124edf7c-ca1a-4ac9-8c62-af467eeedfbe"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "74924cbc-a167-4f5b-8afe-5a7ec32a7511", "eventImplId": "52112ef7-7592-4dc9-8ec5-d45517f98e0d", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ac8f4eb9-d575-4863-820f-5317b3e03929", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a2e4cae9-40e8-4fe0-8879-0cb448706b61", "2025.a2e4cae9-40e8-4fe0-8879-0cb448706b61"], "endStateId": "Out", "toProcessItemId": ["2025.fb4b426c-c39d-4459-8a0e-cf85bd834f08", "2025.fb4b426c-c39d-4459-8a0e-cf85bd834f08"], "guid": "26450ff8-f9bb-4be2-b398-634df92deadb", "versionId": "35dd066f-1c0f-43a6-a3f9-3f013cd7f450", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End Event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.045087b8-0428-4ee8-8cf7-9119b5bf627a", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.21b80e03-8941-451c-866c-14bd2f0c42a0", "2025.21b80e03-8941-451c-866c-14bd2f0c42a0"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.fe17ce92-04be-42be-8877-71a5c3aec1de", "2025.fe17ce92-04be-42be-8877-71a5c3aec1de"], "guid": "d2c5f4cd-3cca-4ee4-957b-40ebcbd38ccf", "versionId": "4d6f2847-b7ba-4c28-a175-52111f7b68c1", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "No", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.aabb13ea-16d8-4c74-8177-59ba78286e1f", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a40e11d3-1311-4699-89a1-cedd867ffa99", "2025.a40e11d3-1311-4699-89a1-cedd867ffa99"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.a2e4cae9-40e8-4fe0-8879-0cb448706b61", "2025.a2e4cae9-40e8-4fe0-8879-0cb448706b61"], "guid": "56938a48-bbbc-4a64-b69a-556921baf45d", "versionId": "7c1092c8-c17b-404d-9530-b543d3e77258", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Delete transactions data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.9d82b456-**************-bc8be4931679", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a40e11d3-1311-4699-89a1-cedd867ffa99", "2025.a40e11d3-1311-4699-89a1-cedd867ffa99"], "endStateId": "guid:7e34ebd307a14eed:779a1eea:18bcfce6abc:-5701", "toProcessItemId": ["2025.3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "2025.3c20d966-fe63-4ba0-83a2-bfe3dd551e21"], "guid": "b312b8d2-a136-4897-bae6-c8631acb62de", "versionId": "84e82abe-ca92-48a0-84ac-100fa31e6fa2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Insert SQL", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6ffae001-80c1-4540-8d0a-b724be115e87", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e52c4730-6443-4709-82f3-15a778427c9d", "2025.e52c4730-6443-4709-82f3-15a778427c9d"], "endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb", "toProcessItemId": ["2025.a2e4cae9-40e8-4fe0-8879-0cb448706b61", "2025.a2e4cae9-40e8-4fe0-8879-0cb448706b61"], "guid": "108daac3-197e-45e1-b0c2-bc6237110a4e", "versionId": "da14b403-3991-4d78-ba88-c83dbf9ddb00", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To Delete ODC Request Execute SQL", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.09b27a91-6c8f-4d18-8471-4582e7ca0e5e", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.3c20d966-fe63-4ba0-83a2-bfe3dd551e21", "2025.3c20d966-fe63-4ba0-83a2-bfe3dd551e21"], "endStateId": "Out", "toProcessItemId": ["2025.e52c4730-6443-4709-82f3-15a778427c9d", "2025.e52c4730-6443-4709-82f3-15a778427c9d"], "guid": "990dd044-8a82-4e2c-b31b-b5a2f04f53a8", "versionId": "e00b4e14-8546-4fcd-aff5-7a4db0f10000", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.be348a8b-2023-451d-8b47-5aaa0f53abc8", "processId": "1.e40ade1b-9648-4203-9aad-ac49f57d027d", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.fb4b426c-c39d-4459-8a0e-cf85bd834f08", "2025.fb4b426c-c39d-4459-8a0e-cf85bd834f08"], "endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb", "toProcessItemId": ["2025.324faa58-845a-49a8-8cc2-03d587c7bcbe", "2025.324faa58-845a-49a8-8cc2-03d587c7bcbe"], "guid": "20f3c32c-51b3-4dd2-b438-4ff80e1aeecb", "versionId": "f941bef3-287f-48ef-9d38-c1f0a730f608", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}