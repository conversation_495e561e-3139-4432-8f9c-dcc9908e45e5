<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8" name="ClosureACT03 - Review ODC Closure Request by Trade FO">
        <lastModified>1700640977062</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.67d3655b-2eab-4e99-8d45-2a72717a876e</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3bca</guid>
        <versionId>e01b683e-1c97-4ca9-99a7-3b87a9627907</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.a5aace14-1797-4e2f-b7bc-594ef8a3efba"],"isInterrupting":true,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":54,"y":191,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"bcd8b201-807e-4d92-bda6-bf05e43d52ba"},{"outgoing":["2027.8fb28ff0-c8ef-4265-83af-56293088bd57","2027.1602db11-53a0-4509-803e-0cab97f6c5a8"],"incoming":["2027.925dc2b9-1dec-464d-89db-142c597a13ba","2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2"],"extensionElements":{"postAssignmentScript":["console.log(\"AfterCoach\");"],"nodeVisualInfo":[{"width":95,"x":347,"y":169,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Reversal_Closure_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d818b5b2-1abe-4e13-8241-77e122d77c14","optionName":"@label","value":"Closure"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8fd07d3e-6551-4590-8d6b-382f9417f618","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"eec8dd0f-f989-4d06-83ee-e9098277f3d0","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b01cb143-6c76-4ebc-84f7-5411fa4c3a85","optionName":"closureReasonVIS","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"42583ad9-53d6-45d7-803b-8aa3cb55f7b1","optionName":"reversalReasonVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fdad46d7-732f-41a3-8eeb-14fc98be3f7d","optionName":"executionHubVIS","value":""}],"viewUUID":"64.f0c268ac-0772-4735-af5b-5fc6caec30a1","binding":"tw.local.odcRequest.ReversalReason","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"bddc109b-0955-40d3-8a0d-08fdcb363010","version":"8550"},{"layoutItemId":"Basic_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"069fd9b7-6ab0-4367-82ec-113d07a5933f","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"32fb538f-430d-42ec-87f2-5d9941379c54","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c163794f-2bd8-4b57-8705-605b8c9b2cc0","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"83c14790-1c56-4c26-8695-0972c2a53bf0","optionName":"parentRequestNoVis","value":"Editable"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"24526380-7da2-4736-810a-03f1197ba38b","optionName":"basicDetailsCVVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"74e75b99-f8c1-4b2a-8839-bc689fc2fbbe","optionName":"multiTenorDatesVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3421ce27-9699-467a-8037-5ce50ac3ce98","optionName":"contractStageVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"42547531-12d5-40b4-84d6-c2c886898541","optionName":"flexCubeContractNoVIS","value":"None"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6f0fefc8-abef-421f-82ae-5b8cdff856ed","version":"8550"},{"layoutItemId":"Customer_Information_cv1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b6c05281-4362-460c-8ed2-2c96a2bd64be","optionName":"@label","value":"Customer Information"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b6719554-71e5-42ca-838c-e6086ae02bb1","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"39ef1d21-102c-4886-8b2a-4d45fe6bf2ca","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8c2b09f2-b97b-4b96-8e86-d077bf8a7c10","optionName":"customerInfoVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"80b93c63-0c4d-4e66-8eaf-8984abf78bd2","optionName":"listsVIS","value":"Readonly"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"cb1ac4f2-99f0-43a8-8597-84aef3b2cadf","version":"8550"},{"layoutItemId":"Financial_Details_Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f32e8951-8b4a-4642-8099-d0b100439f57","optionName":"@label","value":"Financial Details Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9755716f-cd26-4cb4-8630-fcf88c70dcce","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c54cd6de-fcee-416b-8a65-3c6561ad925a","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e23badb3-e260-44c3-8ec0-cc1d11b49bfa","optionName":"financialDetailsCVVis","value":"READONLY"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a03e5d7e-456a-4fe9-87ce-2a65c9fda8b6","optionName":"currencyDocAmountVIS","value":"READONLY"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"6e1b6516-e786-4244-8298-89165924bb78","version":"8550"},{"layoutItemId":"FC_Collections_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"01cc6391-e78a-4830-81cc-162f194c101e","optionName":"@label","value":"FC Collections"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fab4e624-c959-4455-8624-2bab4d7520c4","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"12fd4773-6447-4c77-89ee-105e3d37be01","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6253380d-bc3d-4fc6-82ce-cd992305671b","optionName":"FCVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"38ea6212-629f-4af2-8f5b-064a33bd325c","optionName":"retrieveBtnVis","value":"Hidden"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bc2f1de1-df77-42a1-81e1-410524caa434","optionName":"addBtnVIS","value":"Hidden"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"696c6f58-c2af-491e-8c6e-650f464d8b51","optionName":"customerCif","value":"tw.local.odcRequest.cif"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ee1d7b08-556f-49fa-887f-c60619d543e9","optionName":"collectionCurrencyVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6574e189-4731-4477-8e50-51d828b5bd43","optionName":"negotiatedExchangeRateVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e29fe5c4-f2d5-4191-8150-48c27674d1eb","optionName":"activityType","value":"read"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","binding":"tw.local.odcRequest.FcCollections","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"64d208f5-5b7f-45ec-8701-e611fe293d24","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"56c6c9e4-7378-4a3b-85d3-1ea0ba077f79","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1dd3c9a4-2602-4703-819a-e594abb948f8","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a7ab4bfb-4cec-4e81-8478-ec55da6660db","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0bed140b-7a94-4156-8745-4ecce9be8308","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4bf75b6a-ea02-4490-8b36-f735e40eac6a","optionName":"canCreate","value":"false"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"1261d9e9-f405-499d-8cf2-3af5381da4a1","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"330b9620-42cb-4e91-8f3d-4efdc2ed7d1e","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"*************-4d49-8118-696e5165b92e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"59dd95b3-376b-447f-8c44-13e538c5c327","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"71c1adaa-ef98-454d-8343-eb1a42aea17c","optionName":"@visibility.script","value":"{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"93d69099-1460-48c7-8a31-5b3b3915d46e","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"0707ab9b-c032-4af2-8d3e-a7d4dd70c660"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cae0eb36-8754-4c1e-8210-615f8e59f7d6","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d02ab578-4df0-4e27-87d0-7874a516289e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"49045a97-78d9-4b29-814e-7981286da143","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"e92a5b19-4db3-42a9-84a8-b3a0be34db0e","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"7df2d770-3e75-4f1c-89ba-7574be23d4f3"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8c05883e-83ae-414f-83da-b778cf77c7fe","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"10ad5ed8-4d0e-4725-85d9-97d0221893b5","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"37956140-3ebc-4a56-811f-d7101d9f6f44","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"076f2535-34c4-42b3-8736-536936cb810a","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"960b8e95-f47f-459f-8e6e-8958c4b36bfd","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"838bd19c-5964-4f82-826d-8471b1a80615","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"497bb87c-3f69-4243-8935-ad57a0b99d5a","optionName":"complianceApprovalVis","value":"NONE"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"87a4043c-d02f-4fee-8a59-e2db203fd394","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"16bc750b-f45d-4c2c-8cfe-68bc29afc0c2","optionName":"actionConditions","value":"tw.local.actionConditions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9939502d-9fc5-4311-887f-8ddd74ebec06","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b35cdea-cf59-4fcf-8348-02eec01aa9f8","optionName":"tradeFoCommentVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1ad5fc53-2437-41fd-88d0-4eac990c8bab","optionName":"exeHubMkrCommentVis","value":"None"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"425c5f57-79d3-41df-8b4a-8e01a861b5d5","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Review ODC Closure Request","isForCompensation":false,"completionQuantity":1,"id":"2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4"},{"incoming":["2027.6d7fa5f2-0239-4d28-8e0b-9be25a17a38c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1263,"y":193,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"f59e10b7-dadc-4a1c-abec-ae664cb4dc96"},{"targetRef":"2025.a0d3aad8-f463-441e-84fc-99a031a19682","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.a5aace14-1797-4e2f-b7bc-594ef8a3efba","sourceRef":"bcd8b201-807e-4d92-bda6-bf05e43d52ba"},{"startQuantity":1,"outgoing":["2027.925dc2b9-1dec-464d-89db-142c597a13ba"],"incoming":["2027.a5aace14-1797-4e2f-b7bc-594ef8a3efba"],"default":"2027.925dc2b9-1dec-464d-89db-142c597a13ba","extensionElements":{"nodeVisualInfo":[{"width":95,"x":181,"y":168,"declaredType":"TNodeVisualInfo","height":70}]},"name":"init Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.a0d3aad8-f463-441e-84fc-99a031a19682","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.stepLog ={};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step      = tw.epv.ScreenNames.closureACT03;\r\n\r\n\r\ntw.local.actionConditions = {};\r\ntw.local.actionConditions.complianceApproval= false;\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT03;\r\n\r\n\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/*Initializing Request header*\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\n\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\/\r\nvar date = new Date();\r\ntw.local.odcRequest.appInfo.requestDate =  date.getDate()+'\/' +(date.getMonth() + 1) + '\/' + date.getFullYear();\r\n\t\r\n\ttw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+\"( \"+ tw.system.user.name+\")\";\r\n\ttw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT03;\r\n\r\n"]}},{"targetRef":"2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review ODC Closure Request","declaredType":"sequenceFlow","id":"2027.925dc2b9-1dec-464d-89db-142c597a13ba","sourceRef":"2025.a0d3aad8-f463-441e-84fc-99a031a19682"},{"outgoing":["2027.5331d375-75e1-4399-8784-9cc2b9d25d17","2027.8278582a-6395-4956-8a87-bff668c04e23"],"incoming":["2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026"],"default":"2027.8278582a-6395-4956-8a87-bff668c04e23","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":667,"y":190,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Valid?","declaredType":"exclusiveGateway","id":"2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec"},{"startQuantity":1,"outgoing":["2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026"],"incoming":["2027.1602db11-53a0-4509-803e-0cab97f6c5a8"],"default":"2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026","extensionElements":{"postAssignmentScript":["console.log(\"-----------------------------------------------------------------------------------aftervalidation\");"],"nodeVisualInfo":[{"color":"#95D087","width":95,"x":531,"y":172,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.fb0098be-a5d9-4021-8fca-a4c2f1f148d7","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false;\r\n \r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\n{\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason , \"tw.local.odcRequest.stepLog.returnReason\");\r\n}\t\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)\r\n{\r\n\tmandatory(tw.local.odcRequest.stepLog.terminateReason , \"tw.local.odcRequest.stepLog.terminateReason\");\r\n}\t\r\nif(tw.local.odcRequest.ReversalReason != null &amp;&amp; (tw.local.odcRequest.ReversalReason.executionHub == null || tw.local.odcRequest.ReversalReason.executionHub.value == \"\"))\r\n{\r\n\tmandatory(tw.local.odcRequest.ReversalReason.executionHub , \"tw.local.odcRequest.ReversalReason.executionHub\");\r\n}\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\t"]}},{"startQuantity":1,"outgoing":["2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb"],"incoming":["2027.5331d375-75e1-4399-8784-9cc2b9d25d17"],"default":"2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb","extensionElements":{"nodeVisualInfo":[{"width":95,"x":790,"y":171,"declaredType":"TNodeVisualInfo","height":70}]},"name":"setting status and substatus","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.2c4425c4-adf4-41cb-8ae7-61b64048c16e","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Awaiting Trade FO Checker Approval\";\r\n\ttw.local.lastAction = tw.epv.CreationActions.approveRequest;\r\n}\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Awaiting Trade FO Checker Approval\";\r\n\t\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Final\";\r\n\ttw.local.lastAction = tw.epv.CreationActions.terminateRequest;\r\n}\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)\r\n{\r\n\ttw.local.odcRequest.appInfo.status    =\"In Approval\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Awaiting Trade FO Checker Approval\";\r\n\ttw.local.lastAction = tw.epv.CreationActions.returnToInitiator;\r\n}"]}},{"outgoing":["2027.67a28e10-bae2-47f4-8164-c3580fe56b88"],"incoming":["2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":935,"y":170,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.67a28e10-bae2-47f4-8164-c3580fe56b88","name":"History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.epv.userRole.tradeFoMkr"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.267ea466-7b0c-4ac2-87fa-2275c56babb3","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"targetRef":"2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026","sourceRef":"2025.fb0098be-a5d9-4021-8fca-a4c2f1f148d7"},{"targetRef":"2025.2c4425c4-adf4-41cb-8ae7-61b64048c16e","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.5331d375-75e1-4399-8784-9cc2b9d25d17","sourceRef":"2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec"},{"incoming":["2027.8278582a-6395-4956-8a87-bff668c04e23"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":647,"y":70,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.4083057d-e858-4022-8d99-564ae8f3171a"},{"targetRef":"2025.4083057d-e858-4022-8d99-564ae8f3171a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.8278582a-6395-4956-8a87-bff668c04e23","sourceRef":"2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec"},{"outgoing":["2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2"],"incoming":["2027.8fb28ff0-c8ef-4265-83af-56293088bd57"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2"],"nodeVisualInfo":[{"width":24,"x":362,"y":58,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.bbaaa6b4-1058-4bc3-8c17-f36fb5cb4b73"},{"targetRef":"2025.bbaaa6b4-1058-4bc3-8c17-f36fb5cb4b73","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"42424d76-caa8-42a7-87c8-24de6caed683","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.8fb28ff0-c8ef-4265-83af-56293088bd57","sourceRef":"2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4"},{"targetRef":"2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Review ODC Closure Request","declaredType":"sequenceFlow","id":"2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2","sourceRef":"2025.bbaaa6b4-1058-4bc3-8c17-f36fb5cb4b73"},{"targetRef":"2025.267ea466-7b0c-4ac2-87fa-2275c56babb3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To History","declaredType":"sequenceFlow","id":"2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb","sourceRef":"2025.2c4425c4-adf4-41cb-8ae7-61b64048c16e"},{"targetRef":"2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To audited?","declaredType":"sequenceFlow","id":"2027.67a28e10-bae2-47f4-8164-c3580fe56b88","sourceRef":"2025.267ea466-7b0c-4ac2-87fa-2275c56babb3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.6c03a761-a5b1-4fce-86e0-fc5ed6a9ef2b"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.2ae05bfe-a219-4486-8bec-f2c676fbca7e"},{"targetRef":"2025.fb0098be-a5d9-4021-8fca-a4c2f1f148d7","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"2df87f54-8b6a-4c62-8c79-f095d1289b6c","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To validation script","declaredType":"sequenceFlow","id":"2027.1602db11-53a0-4509-803e-0cab97f6c5a8","sourceRef":"2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4"},{"outgoing":["2027.6d7fa5f2-0239-4d28-8e0b-9be25a17a38c","2027.105f909b-08e4-4c07-872a-323f13284e39"],"incoming":["2027.67a28e10-bae2-47f4-8164-c3580fe56b88"],"default":"2027.105f909b-08e4-4c07-872a-323f13284e39","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1089,"y":190,"declaredType":"TNodeVisualInfo","height":32}]},"name":"audited?","declaredType":"exclusiveGateway","id":"2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243"},{"targetRef":"f59e10b7-dadc-4a1c-abec-ae664cb4dc96","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage== null || tw.local.errorMessage==\"\")"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"yes","declaredType":"sequenceFlow","id":"2027.6d7fa5f2-0239-4d28-8e0b-9be25a17a38c","sourceRef":"2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243"},{"incoming":["2027.105f909b-08e4-4c07-872a-323f13284e39"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1147,"y":278,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.bb5509c8-fd05-4a3d-81cc-1b6fdabfb1ac"},{"targetRef":"2025.bb5509c8-fd05-4a3d-81cc-1b6fdabfb1ac","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"no","declaredType":"sequenceFlow","id":"2027.105f909b-08e4-4c07-872a-323f13284e39","sourceRef":"2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"fcc8e710-2643-458d-b576-f96d68658582","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"ddc2f352-2085-4b2e-8533-36e7298d6066","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"ClosureACT03 - Review ODC Closure Request by Trade FO","declaredType":"globalUserTask","id":"1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.aa22438f-a40d-4a2b-8574-34965edd357f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.a695b666-deac-4677-831b-28362ebe5d26"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.302d0259-46dd-4e02-8735-ed03dff856dc"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"bedc76c6-a9fd-4e8f-85f4-f9455f2b8c86","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"59d54c44-5275-46f3-8db1-ab8c2f9d7160","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"72ae8e0d-9c79-4cff-8f65-a37289e36417","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"_0de3f49e-3fe0-44cd-86b7-cbc1e2b627a4"}],"outputSet":[{"id":"_a5f37951-91e2-4b0f-88e8-6d7d7e0ee1b6"}],"dataInput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.7b8524b0-bfb2-46dd-854d-9382344f5d1d"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"56d3bf09-ceb2-4457-a765-27ea44cefef4"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7b8524b0-bfb2-46dd-854d-9382344f5d1d</processParameterId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2997e0e9-aea8-444f-847f-2ea8435451ab</guid>
            <versionId>00ce3c88-0ec6-4a7e-ab2e-b4fcefcbdc86</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.aa22438f-a40d-4a2b-8574-34965edd357f</processParameterId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a1f4d5ea-90d2-42bc-acc1-d619bacc7ec2</guid>
            <versionId>22d68f4c-15f6-4027-9743-469b6ead14d2</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a695b666-deac-4677-831b-28362ebe5d26</processParameterId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>6b91a1bc-e332-408e-b964-ab578eb21135</guid>
            <versionId>a078453c-23fa-4693-9b0c-1942e33f054f</versionId>
        </processParameter>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6c03a761-a5b1-4fce-86e0-fc5ed6a9ef2b</processVariableId>
            <description isNull="true" />
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>565f8592-19a4-465e-86c8-2f7244d7bf2a</guid>
            <versionId>d6968a7c-2ef0-43e5-ba9c-930f76428685</versionId>
        </processVariable>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2ae05bfe-a219-4486-8bec-f2c676fbca7e</processVariableId>
            <description isNull="true" />
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b9d37bfc-63f8-4526-8ba4-3c1ce4276450</guid>
            <versionId>6369d4a4-be7b-47cd-8b8a-8e4e52ad5439</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ae0dacfd-d41c-4323-b2db-923639582f44</processItemId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.2775bd31-2237-4401-bfe2-99c180418301</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3bc9</guid>
            <versionId>126d3097-c1af-4329-b0c0-2440a6ef582b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.2775bd31-2237-4401-bfe2-99c180418301</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>32ee83d7-e46c-4cd4-a146-7e909cae6f4e</guid>
                <versionId>c6f5246b-ed10-4eae-9841-31f398375d3b</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.67d3655b-2eab-4e99-8d45-2a72717a876e</processItemId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.b9ce0ade-8039-4c33-be4d-51ff0cf87e1e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:5815664c4857eaa6:19ee65a1:18a6511fc28:-3bc8</guid>
            <versionId>99d82791-0241-4023-9902-44320a21bfe3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.267ea466-7b0c-4ac2-87fa-2275c56babb3</processItemId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <name>History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b00acb62-a049-4bb9-ab00-2832fc97e4ab</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:-4b14</guid>
            <versionId>fbacb42e-25e5-4878-84ed-4eaab0b2cfb3</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b00acb62-a049-4bb9-ab00-2832fc97e4ab</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>5281ae47-5fa8-4b75-b8c8-651e86ce098d</guid>
                <versionId>70905733-e7ae-4e0a-9c51-46e2572872ad</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.2134be58-8cdd-4b42-a8e8-43bc9e79769d</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <guid>20c62129-32d7-49cb-b092-74d23775c881</guid>
            <versionId>349e2309-1521-4913-9af1-412f668861b4</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.7023c152-2a52-42f0-8f77-5192c4b705d2</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <guid>098f7946-08a6-488b-b7c6-99b38471dbe6</guid>
            <versionId>789ac9b0-7972-4498-b489-64dfee3944aa</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.deb1dd1f-08c8-4ebe-b94e-617f71d950c3</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <guid>6d7b1219-9e73-4357-b9ff-4d354772a687</guid>
            <versionId>b7b68be4-a22b-48c8-a57d-e25bb07f3c02</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.6f00bcb3-7e19-4168-8d88-e15cc8288e0d</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <guid>fc3e0416-1f04-48c3-a4ed-02a84440c385</guid>
            <versionId>7e7d76d5-707a-4b55-a77a-2686053e022b</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.67d3655b-2eab-4e99-8d45-2a72717a876e</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="56d3bf09-ceb2-4457-a765-27ea44cefef4" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                
                
                <ns16:globalUserTask name="ClosureACT03 - Review ODC Closure Request by Trade FO" id="1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8">
                    
                    
                    <ns16:documentation />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:userTaskImplementation id="ddc2f352-2085-4b2e-8533-36e7298d6066">
                            
                            
                            <ns16:startEvent name="Start" id="bcd8b201-807e-4d92-bda6-bf05e43d52ba">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="54" y="191" width="24" height="24" color="#F8F8F8" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:outgoing>2027.a5aace14-1797-4e2f-b7bc-594ef8a3efba</ns16:outgoing>
                                
                            
                            </ns16:startEvent>
                            
                            
                            <ns3:formTask name="Review ODC Closure Request" id="2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    
                                    
                                    <ns13:nodeVisualInfo x="347" y="169" width="95" height="70" />
                                    
                                    
                                    <ns3:postAssignmentScript>console.log("AfterCoach");</ns3:postAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.925dc2b9-1dec-464d-89db-142c597a13ba</ns16:incoming>
                                
                                
                                <ns16:incoming>2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.8fb28ff0-c8ef-4265-83af-56293088bd57</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.1602db11-53a0-4509-803e-0cab97f6c5a8</ns16:outgoing>
                                
                                
                                <ns3:formDefinition>
                                    
                                    
                                    <ns19:coachDefinition>
                                        
                                        
                                        <ns19:layout>
                                            
                                            
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                
                                                
                                                <ns19:id>425c5f57-79d3-41df-8b4a-8e01a861b5d5</ns19:id>
                                                
                                                
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>8c05883e-83ae-414f-83da-b778cf77c7fe</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>DC Templete</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>10ad5ed8-4d0e-4725-85d9-97d0221893b5</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value />
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>37956140-3ebc-4a56-811f-d7101d9f6f44</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>SHOW</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>076f2535-34c4-42b3-8736-536936cb810a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>960b8e95-f47f-459f-8e6e-8958c4b36bfd</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>action</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>838bd19c-5964-4f82-826d-8471b1a80615</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>497bb87c-3f69-4243-8935-ad57a0b99d5a</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>87a4043c-d02f-4fee-8a59-e2db203fd394</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>16bc750b-f45d-4c2c-8cfe-68bc29afc0c2</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    
                                                    
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>9939502d-9fc5-4311-887f-8ddd74ebec06</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>NONE</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>4b35cdea-cf59-4fcf-8348-02eec01aa9f8</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:configData>
                                                    
                                                    
                                                    <ns19:id>1ad5fc53-2437-41fd-88d0-4eac990c8bab</ns19:id>
                                                    
                                                    
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    
                                                    
                                                    <ns19:value>None</ns19:value>
                                                    
                                                
                                                </ns19:configData>
                                                
                                                
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                
                                                
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                
                                                
                                                <ns19:contentBoxContrib>
                                                    
                                                    
                                                    <ns19:id>7df2d770-3e75-4f1c-89ba-7574be23d4f3</ns19:id>
                                                    
                                                    
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    
                                                    
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        
                                                        
                                                        <ns19:id>e92a5b19-4db3-42a9-84a8-b3a0be34db0e</ns19:id>
                                                        
                                                        
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>cae0eb36-8754-4c1e-8210-615f8e59f7d6</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>Tab section</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>d02ab578-4df0-4e27-87d0-7874a516289e</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value />
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:configData>
                                                            
                                                            
                                                            <ns19:id>49045a97-78d9-4b29-814e-7981286da143</ns19:id>
                                                            
                                                            
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            
                                                            
                                                            <ns19:value>SHOW</ns19:value>
                                                            
                                                        
                                                        </ns19:configData>
                                                        
                                                        
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        
                                                        
                                                        <ns19:contentBoxContrib>
                                                            
                                                            
                                                            <ns19:id>0707ab9b-c032-4af2-8d3e-a7d4dd70c660</ns19:id>
                                                            
                                                            
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>bddc109b-0955-40d3-8a0d-08fdcb363010</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Reversal_Closure_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>d818b5b2-1abe-4e13-8241-77e122d77c14</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Closure</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8fd07d3e-6551-4590-8d6b-382f9417f618</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>eec8dd0f-f989-4d06-83ee-e9098277f3d0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b01cb143-6c76-4ebc-84f7-5411fa4c3a85</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>closureReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>42583ad9-53d6-45d7-803b-8aa3cb55f7b1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>reversalReasonVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>fdad46d7-732f-41a3-8eeb-14fc98be3f7d</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>executionHubVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f0c268ac-0772-4735-af5b-5fc6caec30a1</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.ReversalReason</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>6f0fefc8-abef-421f-82ae-5b8cdff856ed</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Basic_Details_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>069fd9b7-6ab0-4367-82ec-113d07a5933f</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>32fb538f-430d-42ec-87f2-5d9941379c54</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c163794f-2bd8-4b57-8705-605b8c9b2cc0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>83c14790-1c56-4c26-8695-0972c2a53bf0</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Editable</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>24526380-7da2-4736-810a-03f1197ba38b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>74e75b99-f8c1-4b2a-8839-bc689fc2fbbe</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>3421ce27-9699-467a-8037-5ce50ac3ce98</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>42547531-12d5-40b4-84d6-c2c886898541</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>None</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>cb1ac4f2-99f0-43a8-8597-84aef3b2cadf</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Customer_Information_cv1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b6c05281-4362-460c-8ed2-2c96a2bd64be</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Customer Information</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>b6719554-71e5-42ca-838c-e6086ae02bb1</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>39ef1d21-102c-4886-8b2a-4d45fe6bf2ca</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>8c2b09f2-b97b-4b96-8e86-d077bf8a7c10</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>80b93c63-0c4d-4e66-8eaf-8984abf78bd2</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>6e1b6516-e786-4244-8298-89165924bb78</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Financial_Details_Branch1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>f32e8951-8b4a-4642-8099-d0b100439f57</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Financial Details Branch</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>9755716f-cd26-4cb4-8630-fcf88c70dcce</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>c54cd6de-fcee-416b-8a65-3c6561ad925a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e23badb3-e260-44c3-8ec0-cc1d11b49bfa</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a03e5d7e-456a-4fe9-87ce-2a65c9fda8b6</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>READONLY</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>64d208f5-5b7f-45ec-8701-e611fe293d24</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>FC_Collections_CV1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>01cc6391-e78a-4830-81cc-162f194c101e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>FC Collections</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>fab4e624-c959-4455-8624-2bab4d7520c4</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>12fd4773-6447-4c77-89ee-105e3d37be01</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6253380d-bc3d-4fc6-82ce-cd992305671b</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>FCVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>38ea6212-629f-4af2-8f5b-064a33bd325c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>retrieveBtnVis</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Hidden</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>bc2f1de1-df77-42a1-81e1-410524caa434</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>addBtnVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Hidden</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>696c6f58-c2af-491e-8c6e-650f464d8b51</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>customerCif</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.cif</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>ee1d7b08-556f-49fa-887f-c60619d543e9</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>collectionCurrencyVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>6574e189-4731-4477-8e50-51d828b5bd43</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>negotiatedExchangeRateVIS</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Readonly</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>e29fe5c4-f2d5-4191-8150-48c27674d1eb</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>activityType</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>read</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.FcCollections</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>1261d9e9-f405-499d-8cf2-3af5381da4a1</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>56c6c9e4-7378-4a3b-85d3-1ea0ba077f79</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>Attachment</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>1dd3c9a4-2602-4703-819a-e594abb948f8</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>a7ab4bfb-4cec-4e81-8478-ec55da6660db</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>0bed140b-7a94-4156-8745-4ecce9be8308</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>4bf75b6a-ea02-4490-8b36-f735e40eac6a</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>false</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                            
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                
                                                                
                                                                <ns19:id>93d69099-1460-48c7-8a31-5b3b3915d46e</ns19:id>
                                                                
                                                                
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>330b9620-42cb-4e91-8f3d-4efdc2ed7d1e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>History</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>*************-4d49-8118-696e5165b92e</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value />
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>59dd95b3-376b-447f-8c44-13e538c5c327</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>SHOW</ns19:value>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:configData>
                                                                    
                                                                    
                                                                    <ns19:id>71c1adaa-ef98-454d-8343-eb1a42aea17c</ns19:id>
                                                                    
                                                                    
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    
                                                                    
                                                                    <ns19:value>{"@class":"com.ibm.bpm.coachNG.visibility.VisibilityRules","rules":[{"@class":"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule","var_conditions":[{"timeInMs":null,"var":"tw.local.isFirstTime","operand":true,"operator":0}],"action":"NONE"}],"defaultAction":"DEFAULT"}</ns19:value>
                                                                    
                                                                    
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                    
                                                                
                                                                </ns19:configData>
                                                                
                                                                
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                
                                                                
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                                
                                                            
                                                            </ns19:contributions>
                                                            
                                                        
                                                        </ns19:contentBoxContrib>
                                                        
                                                    
                                                    </ns19:contributions>
                                                    
                                                
                                                </ns19:contentBoxContrib>
                                                
                                            
                                            </ns19:layoutItem>
                                            
                                        
                                        </ns19:layout>
                                        
                                    
                                    </ns19:coachDefinition>
                                    
                                
                                </ns3:formDefinition>
                                
                            
                            </ns3:formTask>
                            
                            
                            <ns16:endEvent name="End" id="f59e10b7-dadc-4a1c-abec-ae664cb4dc96">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1263" y="193" width="24" height="24" color="#F8F8F8" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.6d7fa5f2-0239-4d28-8e0b-9be25a17a38c</ns16:incoming>
                                
                            
                            </ns16:endEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="bcd8b201-807e-4d92-bda6-bf05e43d52ba" targetRef="2025.a0d3aad8-f463-441e-84fc-99a031a19682" name="To Coach" id="2027.a5aace14-1797-4e2f-b7bc-594ef8a3efba">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.925dc2b9-1dec-464d-89db-142c597a13ba" name="init Script" id="2025.a0d3aad8-f463-441e-84fc-99a031a19682">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="181" y="168" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.a5aace14-1797-4e2f-b7bc-594ef8a3efba</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.925dc2b9-1dec-464d-89db-142c597a13ba</ns16:outgoing>
                                
                                
                                <ns16:script>tw.local.odcRequest.stepLog ={};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step      = tw.epv.ScreenNames.closureACT03;&#xD;
&#xD;
&#xD;
tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.complianceApproval= false;&#xD;
tw.local.actionConditions.screenName= tw.epv.ScreenNames.closureACT03;&#xD;
&#xD;
&#xD;
///////////////*Initializing Request header*//////////////////////////&#xD;
//////////////////////////////////////////////////////////////////////&#xD;
var date = new Date();&#xD;
tw.local.odcRequest.appInfo.requestDate =  date.getDate()+'/' +(date.getMonth() + 1) + '/' + date.getFullYear();&#xD;
	&#xD;
	tw.local.odcRequest.appInfo.initiator = tw.system.user.fullName+"( "+ tw.system.user.name+")";&#xD;
	tw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.closureACT03;&#xD;
&#xD;
</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.a0d3aad8-f463-441e-84fc-99a031a19682" targetRef="2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4" name="To Review ODC Closure Request" id="2027.925dc2b9-1dec-464d-89db-142c597a13ba">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.8278582a-6395-4956-8a87-bff668c04e23" name="Valid?" id="2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="667" y="190" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.5331d375-75e1-4399-8784-9cc2b9d25d17</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.8278582a-6395-4956-8a87-bff668c04e23</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026" name="validation script" id="2025.fb0098be-a5d9-4021-8fca-a4c2f1f148d7">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="531" y="172" width="95" height="70" color="#95D087" />
                                    
                                    
                                    <ns3:postAssignmentScript>console.log("-----------------------------------------------------------------------------------aftervalidation");</ns3:postAssignmentScript>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.1602db11-53a0-4509-803e-0cab97f6c5a8</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026</ns16:outgoing>
                                
                                
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false;&#xD;
 &#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.stepLog.returnReason , "tw.local.odcRequest.stepLog.returnReason");&#xD;
}	&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.stepLog.terminateReason , "tw.local.odcRequest.stepLog.terminateReason");&#xD;
}	&#xD;
if(tw.local.odcRequest.ReversalReason != null &amp;&amp; (tw.local.odcRequest.ReversalReason.executionHub == null || tw.local.odcRequest.ReversalReason.executionHub.value == ""))&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.ReversalReason.executionHub , "tw.local.odcRequest.ReversalReason.executionHub");&#xD;
}&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
	</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb" name="setting status and substatus" id="2025.2c4425c4-adf4-41cb-8ae7-61b64048c16e">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="790" y="171" width="95" height="70" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.5331d375-75e1-4399-8784-9cc2b9d25d17</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb</ns16:outgoing>
                                
                                
                                <ns16:script>if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.approveRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="In Approval";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Awaiting Trade FO Checker Approval";&#xD;
	tw.local.lastAction = tw.epv.CreationActions.approveRequest;&#xD;
}&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.terminateRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="In Approval";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Awaiting Trade FO Checker Approval";&#xD;
	&#xD;
	tw.local.odcRequest.BasicDetails.requestState = "Final";&#xD;
	tw.local.lastAction = tw.epv.CreationActions.terminateRequest;&#xD;
}&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToInitiator)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status    ="In Approval";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Awaiting Trade FO Checker Approval";&#xD;
	tw.local.lastAction = tw.epv.CreationActions.returnToInitiator;&#xD;
}</ns16:script>
                                
                            
                            </ns16:scriptTask>
                            
                            
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" default="2027.67a28e10-bae2-47f4-8164-c3580fe56b88" name="History" id="2025.267ea466-7b0c-4ac2-87fa-2275c56babb3">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="935" y="170" width="95" height="70" />
                                    
                                    
                                    <ns3:preAssignmentScript />
                                    
                                    
                                    <ns3:postAssignmentScript />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.67a28e10-bae2-47f4-8164-c3580fe56b88</ns16:outgoing>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataInputAssociation>
                                    
                                    
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.epv.userRole.tradeFoMkr</ns16:from>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataInputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                                
                                <ns16:dataOutputAssociation>
                                    
                                    
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    
                                    
                                    <ns16:assignment>
                                        
                                        
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                        
                                    
                                    </ns16:assignment>
                                    
                                
                                </ns16:dataOutputAssociation>
                                
                            
                            </ns16:callActivity>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.fb0098be-a5d9-4021-8fca-a4c2f1f148d7" targetRef="2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec" name="To Valid?" id="2027.cf0d4429-2b69-4cbf-814e-e4327a2aa026">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec" targetRef="2025.2c4425c4-adf4-41cb-8ae7-61b64048c16e" name="Yes" id="2027.5331d375-75e1-4399-8784-9cc2b9d25d17">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.4083057d-e858-4022-8d99-564ae8f3171a">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="647" y="70" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8278582a-6395-4956-8a87-bff668c04e23</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.77c4456e-cf47-4dc3-8aea-7c44a3f7adec" targetRef="2025.4083057d-e858-4022-8d99-564ae8f3171a" name="No" id="2027.8278582a-6395-4956-8a87-bff668c04e23">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.bbaaa6b4-1058-4bc3-8c17-f36fb5cb4b73">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="362" y="58" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                    
                                    <ns3:default>2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2</ns3:default>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.8fb28ff0-c8ef-4265-83af-56293088bd57</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2</ns16:outgoing>
                                
                                
                                <ns3:postponeTaskEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4" targetRef="2025.bbaaa6b4-1058-4bc3-8c17-f36fb5cb4b73" name="To Postpone" id="2027.8fb28ff0-c8ef-4265-83af-56293088bd57">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>topRight</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="42424d76-caa8-42a7-87c8-24de6caed683">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.bbaaa6b4-1058-4bc3-8c17-f36fb5cb4b73" targetRef="2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4" name="To Review ODC Closure Request" id="2027.b620e9aa-af85-4c52-85ec-7e58cb0f40a2">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.2c4425c4-adf4-41cb-8ae7-61b64048c16e" targetRef="2025.267ea466-7b0c-4ac2-87fa-2275c56babb3" name="To History" id="2027.21f056a4-eb33-4edd-80d0-c2f4ae6927eb">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.267ea466-7b0c-4ac2-87fa-2275c56babb3" targetRef="2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243" name="To audited?" id="2027.67a28e10-bae2-47f4-8164-c3580fe56b88">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.6c03a761-a5b1-4fce-86e0-fc5ed6a9ef2b" />
                            
                            
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.2ae05bfe-a219-4486-8bec-f2c676fbca7e" />
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.66ac6bd0-6748-4103-8a3e-cb4bbb878ff4" targetRef="2025.fb0098be-a5d9-4021-8fca-a4c2f1f148d7" name="To validation script" id="2027.1602db11-53a0-4509-803e-0cab97f6c5a8">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>false</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                    
                                    <ns3:coachEventBinding id="2df87f54-8b6a-4c62-8c79-f095d1289b6c">
                                        
                                        
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                        
                                    
                                    </ns3:coachEventBinding>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:exclusiveGateway default="2027.105f909b-08e4-4c07-872a-323f13284e39" name="audited?" id="2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1089" y="190" width="32" height="32" />
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.67a28e10-bae2-47f4-8164-c3580fe56b88</ns16:incoming>
                                
                                
                                <ns16:outgoing>2027.6d7fa5f2-0239-4d28-8e0b-9be25a17a38c</ns16:outgoing>
                                
                                
                                <ns16:outgoing>2027.105f909b-08e4-4c07-872a-323f13284e39</ns16:outgoing>
                                
                            
                            </ns16:exclusiveGateway>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243" targetRef="f59e10b7-dadc-4a1c-abec-ae664cb4dc96" name="yes" id="2027.6d7fa5f2-0239-4d28-8e0b-9be25a17a38c">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage== null || tw.local.errorMessage=="")</ns16:conditionExpression>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.bb5509c8-fd05-4a3d-81cc-1b6fdabfb1ac">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:nodeVisualInfo x="1147" y="278" width="24" height="24" />
                                    
                                    
                                    <ns3:navigationInstructions>
                                        
                                        
                                        <ns3:targetType>Default</ns3:targetType>
                                        
                                    
                                    </ns3:navigationInstructions>
                                    
                                
                                </ns16:extensionElements>
                                
                                
                                <ns16:incoming>2027.105f909b-08e4-4c07-872a-323f13284e39</ns16:incoming>
                                
                                
                                <ns3:stayOnPageEventDefinition />
                                
                            
                            </ns16:intermediateThrowEvent>
                            
                            
                            <ns16:sequenceFlow sourceRef="2025.64f3658b-b58f-4daf-8f2c-ab7cfb7b7243" targetRef="2025.bb5509c8-fd05-4a3d-81cc-1b6fdabfb1ac" name="no" id="2027.105f909b-08e4-4c07-872a-323f13284e39">
                                
                                
                                <ns16:extensionElements>
                                    
                                    
                                    <ns13:linkVisualInfo>
                                        
                                        
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        
                                        
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        
                                        
                                        <ns13:showLabel>true</ns13:showLabel>
                                        
                                        
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        
                                        
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        
                                        
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        
                                    
                                    </ns13:linkVisualInfo>
                                    
                                
                                </ns16:extensionElements>
                                
                            
                            </ns16:sequenceFlow>
                            
                            
                            <ns3:htmlHeaderTag id="fcc8e710-2643-458d-b576-f96d68658582">
                                
                                
                                <ns3:tagName>viewport</ns3:tagName>
                                
                                
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                
                                
                                <ns3:enabled>true</ns3:enabled>
                                
                            
                            </ns3:htmlHeaderTag>
                            
                        
                        </ns3:userTaskImplementation>
                        
                        
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks>
                                
                                
                                <ns3:resourceRef>
                                    
                                    
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    
                                    
                                    <ns3:id>69.302d0259-46dd-4e02-8735-ed03dff856dc</ns3:id>
                                    
                                
                                </ns3:resourceRef>
                                
                            
                            </ns3:localizationResourceLinks>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="bedc76c6-a9fd-4e8f-85f4-f9455f2b8c86" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="59d54c44-5275-46f3-8db1-ab8c2f9d7160" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="72ae8e0d-9c79-4cff-8f65-a37289e36417" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.7b8524b0-bfb2-46dd-854d-9382344f5d1d" />
                        
                        
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.aa22438f-a40d-4a2b-8574-34965edd357f" />
                        
                        
                        <ns16:dataOutput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a695b666-deac-4677-831b-28362ebe5d26" />
                        
                        
                        <ns16:inputSet id="_0de3f49e-3fe0-44cd-86b7-cbc1e2b627a4" />
                        
                        
                        <ns16:outputSet id="_a5f37951-91e2-4b0f-88e8-6d7d7e0ee1b6" />
                        
                    
                    </ns16:ioSpecification>
                    
                
                </ns16:globalUserTask>
                
            
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.dc86791c-0432-4d03-8dbc-5d653fcc78db</processLinkId>
            <processId>1.4d1b706e-dacc-4a2d-94de-2a07343a4ea8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.67d3655b-2eab-4e99-8d45-2a72717a876e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.ae0dacfd-d41c-4323-b2db-923639582f44</toProcessItemId>
            <guid>66848bc4-a500-498b-872c-a0a5e23979ea</guid>
            <versionId>682e51bb-775a-40a7-805c-863ae4595297</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.67d3655b-2eab-4e99-8d45-2a72717a876e</fromProcessItemId>
            <toProcessItemId>2025.ae0dacfd-d41c-4323-b2db-923639582f44</toProcessItemId>
        </link>
    </process>
</teamworks>

