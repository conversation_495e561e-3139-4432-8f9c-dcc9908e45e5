{"id": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "versionId": "1382d1e5-4d3e-48a1-9c56-e2c1342710c9", "name": "Audit Create Data", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Audit Create Data", "lastModified": "1700199190493", "lastModifiedBy": "<PERSON><PERSON>", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b", "2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "2025.fcc15744-4366-40c0-863f-44d3fbef78dd"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de3", "versionId": "1382d1e5-4d3e-48a1-9c56-e2c1342710c9", "dependencySummary": "<dependencySummary id=\"bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:33eb\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"1004085d-935b-4f0b-8b7d-00f0e577a11a\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":99,\"y\":126,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"081c9314-33e1-4935-8aa2-fd628f60e06c\"},{\"incoming\":[\"4a775d57-f5a3-421f-89c6-8f0cb17b3efa\",\"cdaf90c4-7974-40d9-8c80-2cd09e52dc77\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1555,\"y\":79,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de5\"],\"preAssignmentScript\":[\"log.info(\\\"ODC -- ProcessInstance : \\\"+tw.local.odcRequest.appInfo.instanceID +\\\" - ServiceName : AUDIT CREATE DATA : END\\\");\\r\\n\\r\\ntw.local.reqID= tw.local.requestID;\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"646302c3-7d88-4f2d-8840-dec30757da05\"},{\"startQuantity\":1,\"outgoing\":[\"b3af8797-0b4e-411f-804d-fc11e6ac8cbb\"],\"incoming\":[\"c27dcbbc-8a95-4f89-8f2f-45efe37c2fad\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":838,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"log.info(\\\"ODC -- ProcessInstance : \\\"+tw.local.odcRequest.appInfo.instanceID  +\\\" - ServiceName : AUDIT CREATE DATA : START--Insert Path\\\");\\r\\n\\r\\n\\r\\n\\r\\n\"]},\"name\":\"Insert Request_info & Retrieve Request ID\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"1988ef7c-d47c-41ca-8f40-ab05373c7958\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\n\\/**-------------------------- Add SQL Parameters --------------------------**\\/\\r\\nfunction addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\n\\/***********************************************************************************\\/\\r\\n\\ttw.local.sql=\\\" SELECT ID FROM FINAL TABLE ( \\\"\\r\\n\\t\\t\\t+ \\\" INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_RequestInfo ( \\\"\\r\\n               \\t+ \\\" requestNo, requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,               \\\"  \\r\\n\\t\\t\\t+ \\\" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \\t\\t\\t \\\"  \\r\\n\\t\\t\\t+ \\\" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \\\"  \\r\\n\\t\\t\\t+ \\\" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \\\"  \\r\\n\\t\\t\\t+ \\\" amountDefAvalization, collectableAmount, outstandingAmount, maturityDate, maturityDays, financeApprovalNo, hubCode,  \\\"  \\r\\n\\t\\t\\t+ \\\" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \\t\\t\\t\\t\\t  \\t\\t\\t \\\"  \\r\\n\\t\\t\\t+ \\\"   CBEClassification, shipmentMethod, shipmentDate, \\t\\t\\t\\t\\t\\t \\t\\t\\t\\t\\t \\\"  \\r\\n\\t\\t\\t+ \\\" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \\t\\t\\t \\\"  \\r\\n\\t\\t\\t+ \\\" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \\t,ISLIQUIDATED,\\t \\\" \\r\\n\\t\\t\\t+\\\"  BPMInstanceNumber, currentStepName, substatus,\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t \\\"\\r\\n\\t\\t\\t+\\\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \\\"\\r\\n\\t\\t\\t+ \\\" ) VALUES\\\" \\r\\n\\t\\t\\t+ \\\" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\\\"\\r\\n\\t\\t\\t+\\\",?,?,?,?,?,?,?)\\\"\\r\\n\\t\\t\\t+ \\\" )\\\";\\r\\n\\r\\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\\\"0\\\")); \\r\\n \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \\r\\n  \\r\\n\\/\\/\\/\\r\\n\\r\\n\\/\\/CHARGESACCOUNT\\r\\n\\/\\/TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \\r\\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \\r\\n\\r\\n\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"]}},{\"targetRef\":\"6b290809-0a1a-487e-8fcf-899da748dc05\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To SQL Execute Statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"b3af8797-0b4e-411f-804d-fc11e6ac8cbb\",\"sourceRef\":\"1988ef7c-d47c-41ca-8f40-ab05373c7958\"},{\"startQuantity\":1,\"outgoing\":[\"4deb808a-8acb-44c0-818d-8f2dbea1d4cb\"],\"incoming\":[\"b3af8797-0b4e-411f-804d-fc11e6ac8cbb\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":955,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"SQL Execute Statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"1\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"6b290809-0a1a-487e-8fcf-899da748dc05\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"29ef6559-8a76-4830-84f5-e12eee5f40a1\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Get Request ID\",\"declaredType\":\"sequenceFlow\",\"id\":\"4deb808a-8acb-44c0-818d-8f2dbea1d4cb\",\"sourceRef\":\"6b290809-0a1a-487e-8fcf-899da748dc05\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLParameter();\\nautoObject[0].value = null;\\nautoObject[0].type = \\\"\\\";\\nautoObject[0].mode = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"sqlParameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.c7095e57-5f5f-49e1-89d1-7aa304a64524\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.116007eb-077a-4059-8f3b-1ff2f6837c3d\"},{\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"results\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.cb80769f-3387-4e8b-8b5e-82a6dfd69a8e\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"0\"}]},\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"requestID\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.63cfedee-d6e5-4a3c-8e47-d9d6176aaf3f\"},{\"startQuantity\":1,\"outgoing\":[\"4a775d57-f5a3-421f-89c6-8f0cb17b3efa\"],\"incoming\":[\"aeebf34e-e506-4186-8d36-a9ad0378f4f6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1427,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Insert ODC Request Execute SQL\",\"dataInputAssociation\":[{\"targetRef\":\"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlStatements\"]}}]},{\"targetRef\":\"2055.628ceac6-aa42-426b-97c7-540674f12f38\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"SQLResult\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.24961b69-3b9b-4311-8707-6b6dfffaf207\"]}],\"calledElement\":\"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();\\nautoObject[0].sql = \\\"\\\";\\nautoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\nautoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\\nautoObject[0].parameters[0].value = null;\\nautoObject[0].parameters[0].type = \\\"\\\";\\nautoObject[0].parameters[0].mode = \\\"\\\";\\nautoObject[0].maxRows = 0;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"name\":\"sqlStatements\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.ce409362-d245-4476-8bac-bdafde3da38f\"},{\"targetRef\":\"646302c3-7d88-4f2d-8840-dec30757da05\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"4a775d57-f5a3-421f-89c6-8f0cb17b3efa\",\"sourceRef\":\"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81\"},{\"startQuantity\":1,\"outgoing\":[\"aeebf34e-e506-4186-8d36-a9ad0378f4f6\"],\"incoming\":[\"f0ce710d-dacd-48c1-87a4-b97b7d2ef860\",\"3f77b7cb-8b99-48b5-844b-6fdfa82c5b42\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":1313,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Insert ODC Data\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"4f8c5959-0089-4977-8ac8-802b96f20d99\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\ntw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\\r\\n\\r\\n\\/**-------------------------- Add SQL Parameters --------------------------**\\/\\r\\nfunction addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\n\\/***********************************************************************************\\/\\r\\n\\r\\n\\/**-----------------------------------------------------------------------**\\/\\r\\n\\/**-------------------------- Log All ODC Data ---------------------------**\\/\\r\\n\\/**-----------------------------------------------------------------------**\\/\\r\\n\\r\\nvar i = 0;\\r\\nvar sqlStatementLen= 0;\\r\\n\\r\\n\\/**-------------------------- Log Invoice Data --------------------------**\\/\\r\\n\\r\\nwhile (i < tw.local.odcRequest.BasicDetails.Invoice.listLength) {\\r\\n\\t\\r\\n\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_Invoice ( \\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" requesrID, invoiceNo , invoiceDate\\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" ) VALUES\\\" + \\\" (?,?,?)\\\";\\r\\n\\t\\t\\t\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.requestID));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate));\\r\\n\\t\\r\\n    i++;\\r\\n}\\r\\n \\/**-------------------------- Log Bills Data --------------------------**\\/\\r\\ni = 0;\\r\\n\\r\\nwhile (i < tw.local.odcRequest.BasicDetails.Bills.listLength) {\\r\\n\\t\\r\\n\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_Bills ( \\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" requesrID, billOfLadingRef, billOfLadingDate\\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" ) VALUES\\\" + \\\" (?,?,?)\\\";\\r\\n\\t\\t\\t\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate));\\r\\n\\r\\n    i++;\\r\\n}\\r\\n \\/**-------------------------- Log Parties Data --------------------------**\\/\\r\\n\\r\\n\\/**-------------------------- Set SQL query to add party record --------------------------**\\/\\r\\nfunction AuditParty(requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address){\\r\\n\\r\\n\\tif(partyId != null || partyId != \\\"\\\"){\\r\\n\\t\\t\\r\\n\\t\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_PartiesInfo ( \\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" requesrID, partyId, partyType, cif, partyName,Country,Language,\\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  +\\\"  Reference, Address1, Address2, Address3, Media, Address\\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" ) VALUES\\\" + \\\" (?,?,?,?,?,?,?,?,?,?,?,?,?) \\\";\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(requesrID));\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyId));\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyType));\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(cif));\\t\\t\\r\\n\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyName));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(country));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(language));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(reference));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address1));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address2));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address3));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(media));\\t\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address));\\t\\r\\n\\r\\n\\t\\treturn true;\\r\\n\\t}\\r\\n\\telse{\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n}\\r\\n\\/\\/Params: requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address\\r\\n\\/\\/Audit Drawer \\r\\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawer.partyId , \\\"Drawer\\\", \\\"\\\" , tw.local.odcRequest.Parties.Drawer.partyName, \\r\\n tw.local.odcRequest.Parties.Drawer.country, tw.local.odcRequest.Parties.Drawer.Language, tw.local.odcRequest.Parties.Drawer.Reference,\\r\\n tw.local.odcRequest.Parties.Drawer.address1, tw.local.odcRequest.Parties.Drawer.address2, tw.local.odcRequest.Parties.Drawer.address3,\\\"\\\",\\\"\\\");\\r\\n\\r\\n\\/\\/Audit Drawee\\r\\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawee.partyId , \\\"Drawee\\\", \\\"\\\", tw.local.odcRequest.Parties.Drawee.partyName,\\r\\ntw.local.odcRequest.Parties.Drawee.country, tw.local.odcRequest.Parties.Drawee.Language, tw.local.odcRequest.Parties.Drawee.Reference,\\r\\ntw.local.odcRequest.Parties.Drawee.address1, tw.local.odcRequest.Parties.Drawee.address2, tw.local.odcRequest.Parties.Drawee.address3,\\\"\\\",\\\"\\\" );\\r\\n\\r\\n\\/\\/Audit collecting Bank\\r\\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.collectingBank.id , \\\"collecting Bank\\\",tw.local.odcRequest.Parties.collectingBank.cif,\\r\\ntw.local.odcRequest.Parties.collectingBank.name, tw.local.odcRequest.Parties.collectingBank.country, tw.local.odcRequest.Parties.collectingBank.language,\\r\\ntw.local.odcRequest.Parties.collectingBank.reference, tw.local.odcRequest.Parties.collectingBank.address1, tw.local.odcRequest.Parties.collectingBank.address2,\\r\\ntw.local.odcRequest.Parties.collectingBank.address3, tw.local.odcRequest.Parties.collectingBank.media, tw.local.odcRequest.Parties.collectingBank.address);\\r\\n\\r\\n\\/\\/Audit Accountee\\/ Case In Need\\r\\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.partyTypes.partyId, tw.local.odcRequest.Parties.partyTypes.partyType.value, tw.local.odcRequest.Parties.partyTypes.partyCIF,\\r\\ntw.local.odcRequest.Parties.partyTypes.partyName, tw.local.odcRequest.Parties.partyTypes.country, tw.local.odcRequest.Parties.partyTypes.language, tw.local.odcRequest.Parties.partyTypes.refrence,\\r\\ntw.local.odcRequest.Parties.partyTypes.address1, tw.local.odcRequest.Parties.partyTypes.address2, tw.local.odcRequest.Parties.partyTypes.address3, \\\"\\\", \\\"\\\");\\r\\n\\r\\n \\/**-------------------------- Log Charges And Commissions Data --------------------------**\\/\\r\\n\\r\\ni = 0;\\r\\n\\r\\nwhile (i < tw.local.odcRequest.ChargesAndCommissions.listLength) {\\r\\n\\t\\r\\n\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_CHARGESCOMMISSIONS ( \\\" \\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" requesrID, defaultCurrency, defaultAmount, chargeAmount, waiver, accountClass,\\\"\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" accountNo, branchCode, currency, balance, balanceSign, standardExRate,\\\"   \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\"negotiatedExRate, debitedAmount, COMPONENT\\\"\\t\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" ) VALUES\\\" + \\\" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)\\\";\\r\\n\\t\\t\\t\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultAmount));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount));\\r\\n\\t\\r\\n\\tif(tw.local.odcRequest.ChargesAndCommissions[i].waiver)\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\\\"1\\\"));\\r\\n\\telse\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\\\"0\\\"));\\r\\n\\t\\r\\n\\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount)\\r\\n\\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount ={};\\r\\n\\t    \\r\\n\\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass)\\r\\n\\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass ={};\\r\\n\\t    \\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value));\\r\\n\\t\\r\\n    \\r\\n\\tvar accountClass= tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value;\\r\\n\\t\\r\\n\\tif(accountClass == \\\"GL Account\\\"){\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo));\\r\\n\\t}\\r\\n\\t\\/\\/Customer Account\\r\\n\\telse{\\r\\n\\t\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo));\\r\\n\\t}\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode));\\r\\n\\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency)\\r\\n\\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency = {};\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign));\\r\\n\\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount)\\r\\n\\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount = {};\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].component));\\r\\n\\t\\r\\n    i++;\\r\\n}\\r\\n \\/**-------------------------- Log FlexCube Transactions Data --------------------------**\\/ \\r\\ni = 0;\\r\\n\\r\\nwhile (i < tw.local.odcRequest.FcCollections.selectedTransactions.listLength) {\\r\\n\\t\\r\\n\\r\\n\\t\\r\\n\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_FCTransaction ( \\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" requesrID,accountNo, postingDate, valueDate, transactionAmount, AMOUNTFORCURRENTREQUEST, TRANSACTIONREFNO \\\"\\t\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" ) VALUES\\\" + \\\" (?,?,?,?,?,?,?)\\\";\\r\\n\\t\\t\\t\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest));\\r\\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber));\\r\\n    i++;\\r\\n}\\r\\n \\/**-------------------------- Log Multi Tenor dates Data --------------------------**\\/\\r\\ni = 0;\\r\\n\\r\\nwhile (i < tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.listLength) {\\r\\n\\t\\r\\n\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" INSERT INTO \\\"+ tw.env.DBSchema + \\\".ODC_MultiTenorDates ( \\\" \\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" requesrID, installmentDate, installmentAmount \\\"\\t\\r\\n\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t  + \\\" ) VALUES\\\" + \\\" (?,?,?)\\\";\\r\\n\\t\\t\\t\\r\\n \\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date));\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount));\\r\\n\\r\\n    i++;\\r\\n}\\r\\n     \"]}},{\"targetRef\":\"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Insert ODC Request Execute SQL\",\"declaredType\":\"sequenceFlow\",\"id\":\"aeebf34e-e506-4186-8d36-a9ad0378f4f6\",\"sourceRef\":\"4f8c5959-0089-4977-8ac8-802b96f20d99\"},{\"startQuantity\":1,\"outgoing\":[\"cdaf90c4-7974-40d9-8c80-2cd09e52dc77\"],\"incoming\":[\"fd084925-7d7f-49d7-8989-1e1357be3d46\",\"7919967e-0eb7-4dc7-804f-4b4f47f29532\",\"87a6fb28-a343-4a33-812e-e6eb0a6463f0\",\"6371f870-49c9-4241-87f2-2d5a5de4eb27\",\"ef61f92e-9713-4cb1-8aef-8065c9fa7428\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":1458,\"y\":203,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Audit Create Data\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"fcc15744-4366-40c0-863f-44d3fbef78dd\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"646302c3-7d88-4f2d-8840-dec30757da05\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"cdaf90c4-7974-40d9-8c80-2cd09e52dc77\",\"sourceRef\":\"fcc15744-4366-40c0-863f-44d3fbef78dd\"},{\"startQuantity\":1,\"outgoing\":[\"c9f833af-b3c3-453e-8b46-df827d4264eb\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FFE14F\",\"width\":95,\"x\":207,\"y\":187,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"log.info(\\\"ODC -- ProcessInstance : \\\"+33311 +\\\" - ServiceName : AUDIT CREATE DATA : START\\\");\\r\\n \\r\\n\"]},\"name\":\"Test Data\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"c32dfa16-a852-4585-896b-47642ffee8bd\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\" tw.local.odcRequest.appInfo.instanceID= \\\"33311\\\";\\r\\n tw.local.odcRequest.requestNo= \\\"12345678911111_03\\\";\\r\\n tw.local.odcRequest.requestNature.name= \\\"new\\\";\\r\\n tw.local.odcRequest.requestType.name = \\\"collect \\\"\\r\\n tw.local.odcRequest.requestDate= new TWDate();\\r\\n tw.local.odcRequest.BasicDetails.requestState=\\\"Final\\\";\\r\\n tw.local.odcRequest.appInfo.status=\\\"Completed\\\";\\r\\n tw.local.odcRequest.parentRequestNo=\\\"12345678911111\\\";\\r\\n tw.local.odcRequest.BasicDetails.flexCubeContractNo=\\\"11112222\\\";\\r\\n tw.local.odcRequest.BasicDetails.contractStage=\\\"stage3\\\";\\r\\n tw.local.odcRequest.BasicDetails.exportPurpose.name=\\\"exportP\\\";\\r\\n tw.local.odcRequest.BasicDetails.paymentTerms.name= \\\"PT\\\";\\r\\n tw.local.odcRequest.BasicDetails.productCategory.name= \\\"PCpc\\\";\\r\\n tw.local.odcRequest.BasicDetails.commodityDescription= \\\"CD11\\\";\\r\\n tw.local.odcRequest.CustomerInfo.cif= \\\"1234\\\";\\r\\n tw.local.odcRequest.CustomerInfo.customerName=\\\"smsma\\\";\\r\\n tw.local.odcRequest.ContractCreation.baseDate= new TWDate();\\r\\n tw.local.odcRequest.ContractCreation.valueDate = new TWDate();\\r\\n tw.local.odcRequest.ContractCreation.tenorDays= 3;\\r\\n tw.local.odcRequest.ContractCreation.transitDays= 4;\\r\\n tw.local.odcRequest.ContractCreation.maturityDate = new TWDate();\\r\\n tw.local.odcRequest.ContractCreation.userReference= \\\"1223231\\\";\\r\\n \\/**********************************************************************************\\/\\r\\n tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\\r\\n tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\\r\\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \\\"bill 03\\\";\\r\\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\r\\n tw.local.odcRequest.BasicDetails.Bills[1] = new tw.object.Bills();\\r\\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingRef = \\\"Bill 04\\\";\\r\\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingDate = new TWDate();\\r\\n \\r\\n tw.local.odcRequest.BasicDetails.Bills[2] = new tw.object.Bills();\\r\\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingRef = \\\"Bill 05\\\";\\r\\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingDate = new TWDate();\\r\\n \\r\\n tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\r\\n tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\\r\\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \\\"Inv 11\\\";\\r\\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\r\\n\\/\\/ tw.local.odcRequest.BasicDetails.Invoice[1] = new tw.object.Invoice();\\r\\n\\/\\/ tw.local.odcRequest.BasicDetails.Invoice[1].invoiceNo = \\\"Inv 08\\\";\\r\\n\\/\\/  tw.local.odcRequest.BasicDetails.Invoice[1].invoiceDate = new TWDate();\\r\\n \\r\\n \\/******************************************************************************************\\/\\r\\n\\r\\n tw.local.odcRequest.Parties = new tw.object.odcParties();\\r\\n tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\\r\\n tw.local.odcRequest.Parties.Drawer.partyId = \\\"00000000\\\";\\r\\n tw.local.odcRequest.Parties.Drawer.partyName = \\\"Party01\\\";\\r\\n tw.local.odcRequest.Parties.Drawer.country = \\\"Egp\\\";\\r\\n tw.local.odcRequest.Parties.Drawer.Language = \\\"EN\\\";\\r\\n tw.local.odcRequest.Parties.Drawer.Reference = \\\"1234\\\";\\r\\n tw.local.odcRequest.Parties.Drawer.address1 = \\\"add1\\\";\\r\\n tw.local.odcRequest.Parties.Drawer.address2 = \\\"add2\\\";\\r\\n tw.local.odcRequest.Parties.Drawer.address3 = \\\"add3\\\";\\r\\n tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\\r\\n tw.local.odcRequest.Parties.Drawee.partyId = \\\"********\\\";\\r\\n tw.local.odcRequest.Parties.Drawee.partyName = \\\"Party02\\\";\\r\\n tw.local.odcRequest.Parties.Drawee.country = \\\"EUR\\\";\\r\\n tw.local.odcRequest.Parties.Drawee.Language = \\\"arabic\\\";\\r\\n tw.local.odcRequest.Parties.Drawee.Reference = \\\"ref1\\\";\\r\\n tw.local.odcRequest.Parties.Drawee.address1 = \\\"address1\\\";\\r\\n tw.local.odcRequest.Parties.Drawee.address2 = \\\"address2\\\";\\r\\n tw.local.odcRequest.Parties.Drawee.address3 = \\\"address3\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\\r\\n tw.local.odcRequest.Parties.collectingBank.id = \\\"********\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.name = \\\"Party03\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.country = \\\"Egypt\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.language = \\\"french\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.reference = \\\"ref2\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.address1 = \\\"address1\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.address2 = \\\"address2\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.address3 = \\\"address3\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.cif = \\\"********\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.media = \\\"swift\\\";\\r\\n tw.local.odcRequest.Parties.collectingBank.address = \\\"address\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\\r\\n tw.local.odcRequest.Parties.partyTypes.partyCIF = \\\"********\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.partyId = \\\"********\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.partyName = \\\"Party04\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.country = \\\"italy\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.language = \\\"italian\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.refrence = \\\"reference0\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.address1 = \\\"add11\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.address2 = \\\"add22\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.address3 = \\\"add33\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.Parties.partyTypes.partyType.name = \\\"Case In Need\\\";\\r\\n tw.local.odcRequest.Parties.partyTypes.partyType.value = \\\"Case In Need\\\";\\r\\n\\/\\/ ------------------------------------------------------------------------------\\r\\n tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\r\\n tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].component = \\\"component\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \\\"EGP\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \\\"EGP\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 11.0;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].waiver = false;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"GL Account\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"1111\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"111\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"055\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"EGP\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"EGP\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balance = 200;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"D\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.isOverDraft = true;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.standardExRate = 990.0;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 990.0;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.amountInAccount = 99.9;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].rateType = \\\"\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].description = \\\"\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].defaultPercentage = 99.9;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].changePercentage = 99.9;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].defaultAmount = 99.1;\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\r\\n tw.local.odcRequest.ChargesAndCommissions[0].flatAmount = 900.0;\\r\\n \\/******************************************************************************************\\/\\r\\n\\r\\n tw.local.odcRequest.FcCollections = new tw.object.FCCollections();\\r\\n tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.FcCollections.currency.name = \\\"\\\";\\r\\n tw.local.odcRequest.FcCollections.currency.value = \\\"Egypt\\\";\\r\\n tw.local.odcRequest.FcCollections.standardExchangeRate = 119.0;\\r\\n tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 99.9;\\r\\n tw.local.odcRequest.FcCollections.fromDate = new TWDate();\\r\\n tw.local.odcRequest.FcCollections.ToDate = new TWDate();\\r\\n tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.FcCollections.accountNo.name = \\\"\\\";\\r\\n tw.local.odcRequest.FcCollections.accountNo.value = \\\"11999\\\";\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \\\"22222\\\";\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \\\"11199\\\";\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 111.0;\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 109.0;\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \\\"Egypt\\\";\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 190.0;\\r\\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency =99.0;\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 0.0;\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\r\\n tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\r\\n tw.local.odcRequest.FcCollections.isReversed = false;\\r\\n tw.local.odcRequest.FcCollections.usedAmount = 999.0;\\r\\n tw.local.odcRequest.FcCollections.calculatedAmount = 119.0;\\r\\n tw.local.odcRequest.FcCollections.totalAllocatedAmount = 999.9;\\r\\n tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\r\\n tw.local.odcRequest.FcCollections.listOfAccounts[0].name = \\\"11119\\\";\\r\\n tw.local.odcRequest.FcCollections.listOfAccounts[0].value = \\\"11119\\\";\\r\\n \\/******************************************************************************************\\/\\r\\n\\r\\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\r\\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\r\\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\r\\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 100.0; \\r\\n\\r\\ntw.local.odcRequest.appInfo.instanceID= \\\"1234\\\";\\r\\ntw.local.odcRequest.appInfo.stepName=\\\"Create Act06\\\";\\r\\ntw.local.odcRequest.appInfo.subStatus=\\\"Completed\\\";\\r\\n\"]}},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"new TWDate()\"}]},\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"date\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.f401dcad-832c-452f-823c-6fca66361cee\"},{\"targetRef\":\"*************-4299-8059-f22e8c1ef414\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To update?\",\"declaredType\":\"sequenceFlow\",\"id\":\"7197dad2-4b36-4cc6-8e17-52094b099c7c\",\"sourceRef\":\"9f4eb6e8-ca0b-4b3f-8785-678607e11258\"},{\"startQuantity\":1,\"outgoing\":[\"13e4407a-2000-4320-81ff-e1245b2f17b5\"],\"incoming\":[\"55643b29-ffc3-4ee6-89ee-92865f61e092\"],\"extensionElements\":{\"postAssignmentScript\":[\"tw.local.sqlOut= tw.local.sql;\\r\\ntw.local.reqID= tw.local.requestID;\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":832,\"y\":173,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"log.info(\\\"ODC -- ProcessInstance : \\\"+tw.local.odcRequest.appInfo.instanceID  +\\\" - ServiceName : AUDIT CREATE DATA : START--Update Path\\\");\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"]},\"name\":\"Update Request Info\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"8895b0f8-2ec0-49f3-8848-9ddc17cb68e2\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\n\\/**-------------------------- Add SQL Parameters --------------------------**\\/\\r\\nfunction addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\n\\/*********************************************************************************************************************************************\\/\\r\\n\\t\\r\\ntw.local.sql= \\\"UPDATE \\\"+ tw.env.DBSchema +  \\\".ODC_RequestInfo SET (\\\"\\r\\n\\t\\t+ \\\" requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,         \\t\\t       \\\"  \\r\\n\\t\\t+ \\\" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \\t\\t\\t \\\"  \\r\\n\\t\\t+ \\\" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \\\"  \\r\\n\\t\\t+ \\\" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \\\"  \\r\\n\\t\\t+ \\\" amountDefAvalization, collectableAmount,\\\"\\r\\n\\t\\t+ \\\"outstandingAmount, maturityDate, maturityDays,\\\"\\r\\n\\t\\t+ \\\"financeApprovalNo,\\\"\\r\\n\\t\\t+\\\"hubCode,  \\\"  \\r\\n\\t\\t+ \\\" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \\t\\t\\t\\t\\t  \\t\\t\\t \\\"  \\r\\n\\t\\t+ \\\" CBEClassification, shipmentMethod, shipmentDate, \\t\\t\\t\\t\\t\\t \\t\\t\\t\\t\\t\\t \\\"  \\r\\n\\t\\t+ \\\" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \\t\\t\\t \\\"  \\r\\n\\t\\t+ \\\" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \\t,ISLIQUIDATED ,\\t \\\" \\r\\n\\t\\t+ \\\" BPMInstanceNumber, currentStepName, substatus, \\t\\t\\t\\t\\t\\t\\t\\t\\t\\t\\t \\t\\\"\\r\\n\\t\\t+\\\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \\\"\\r\\n\\t\\t+ \\\" ) \\\"\\r\\n\\t\\t+\\\" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\\\"\\r\\n\\t\\t+\\\",?,?,?,?,?,?,?,?,?\\\"\\r\\n\\t\\t+\\\")\\\"\\r\\n\\t\\t+\\\" WHERE requestNo = ? \\\";\\r\\n\\t\\t\\r\\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\\\"0\\\")); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \\r\\n\\/\\/\\r\\n\\/\\/TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \\r\\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\\r\\n\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"6878fe71-cf06-4406-817b-c4ec9fb17b03\"],\"incoming\":[\"13e4407a-2000-4320-81ff-e1245b2f17b5\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":963,\"y\":173,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"SQL Update Request Info\",\"dataInputAssociation\":[{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]},{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"c5082aee-343a-452e-8b33-35b492b4251b\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"c5082aee-343a-452e-8b33-35b492b4251b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To SQL Update Request Info\",\"declaredType\":\"sequenceFlow\",\"id\":\"13e4407a-2000-4320-81ff-e1245b2f17b5\",\"sourceRef\":\"8895b0f8-2ec0-49f3-8848-9ddc17cb68e2\"},{\"startQuantity\":1,\"outgoing\":[\"f4368575-7433-403f-8e26-387c7f93456b\"],\"incoming\":[\"6878fe71-cf06-4406-817b-c4ec9fb17b03\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1080,\"y\":173,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\r\\n \\r\\n\"]},\"name\":\"Delete ODC Data\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"3b354799-bd42-4854-868d-e0629c388301\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\\r\\n\\/**-------------------------- Add SQL Parameters --------------------------**\\/\\r\\nfunction addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\n\\/********************************************************************************\\/\\r\\n\\r\\n\\/**-------------------------------------------------------------------------**\\/\\r\\n\\/**-------------------------- DELETE All ODC Data ---------------------------**\\/\\r\\n\\/**-------------------------------------------------------------------------**\\/\\r\\n\\r\\nvar sqlStatementLen= 0;\\r\\n\\/**************************************************************************************************************************************************************\\/\\r\\n\\/**------------------------------This function is used to delete multiple records from table using the requestId---------------------------------------------**\\/\\r\\n\\/**----------------------------------------------- tableName= the name of the table in the DB ---------------------------------------------------------------**\\/\\r\\n\\/**************************************************************************************************************************************************************\\/\\r\\nfunction DeleteRecordsByID(tableName){\\r\\n\\t \\r\\n\\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\\r\\n\\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\\r\\n\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].sql = \\\" DELETE FROM \\\"+ tw.env.DBSchema + \\\".\\\"+ tableName + \\\" WHERE REQUESRID= ? \\\";\\r\\n\\t\\t\\t\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\r\\n\\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\\r\\n  \\t \\r\\n}\\r\\n\\/**************************************************************************************************************************************************************\\/\\r\\n\\r\\n\\/**-------------------------- Delete Data from Tables by request id --------------------------**\\/\\r\\n\\r\\n\\tDeleteRecordsByID (      \\\"ODC_Invoice\\\"      );\\r\\n\\tDeleteRecordsByID (      \\\"ODC_Bills\\\"        );\\r\\n\\tDeleteRecordsByID (   \\\"ODC_PartiesInfo\\\"     );\\t\\r\\n\\tDeleteRecordsByID ( \\\"ODC_CHARGESCOMMISSIONS\\\");\\r\\n\\tDeleteRecordsByID (   \\\"ODC_FCTransaction\\\"   );\\r\\n\\tDeleteRecordsByID ( \\\"ODC_MultiTenorDates\\\"   );\\t\\r\\n\\r\\n \"]}},{\"targetRef\":\"3b354799-bd42-4854-868d-e0629c388301\",\"extensionElements\":{\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Delete ODC Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"6878fe71-cf06-4406-817b-c4ec9fb17b03\",\"sourceRef\":\"c5082aee-343a-452e-8b33-35b492b4251b\"},{\"targetRef\":\"548669f1-7641-4c40-8867-17f51c191b64\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Execute SQL Statements\",\"declaredType\":\"sequenceFlow\",\"id\":\"f4368575-7433-403f-8e26-387c7f93456b\",\"sourceRef\":\"3b354799-bd42-4854-868d-e0629c388301\"},{\"startQuantity\":1,\"outgoing\":[\"f0ce710d-dacd-48c1-87a4-b97b7d2ef860\"],\"incoming\":[\"f4368575-7433-403f-8e26-387c7f93456b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1190,\"y\":173,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Delete ODC Request Execute SQL\",\"dataInputAssociation\":[{\"targetRef\":\"2055.c007e1f5-b9cb-4b02-bf74-bc982112aade\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlStatements\"]}}]},{\"targetRef\":\"2055.628ceac6-aa42-426b-97c7-540674f12f38\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"SQLResult\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"548669f1-7641-4c40-8867-17f51c191b64\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.24961b69-3b9b-4311-8707-6b6dfffaf207\"]}],\"calledElement\":\"1.89ee1e72-5c6b-44ed-9e55-158a6cb613af\"},{\"targetRef\":\"4f8c5959-0089-4977-8ac8-802b96f20d99\",\"extensionElements\":{\"endStateId\":[\"guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Insert ODC Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"f0ce710d-dacd-48c1-87a4-b97b7d2ef860\",\"sourceRef\":\"548669f1-7641-4c40-8867-17f51c191b64\"},{\"startQuantity\":1,\"outgoing\":[\"3f77b7cb-8b99-48b5-844b-6fdfa82c5b42\"],\"incoming\":[\"4deb808a-8acb-44c0-818d-8f2dbea1d4cb\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1073,\"y\":56,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Get Request ID\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"29ef6559-8a76-4830-84f5-e12eee5f40a1\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.requestID= tw.local.results[0].rows[0].data[0];\\r\\n\"]}},{\"targetRef\":\"4f8c5959-0089-4977-8ac8-802b96f20d99\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Insert Invoice Data\",\"declaredType\":\"sequenceFlow\",\"id\":\"3f77b7cb-8b99-48b5-844b-6fdfa82c5b42\",\"sourceRef\":\"29ef6559-8a76-4830-84f5-e12eee5f40a1\"},{\"startQuantity\":1,\"outgoing\":[\"b85ff298-f81c-4996-807b-ca607cbe39e9\"],\"incoming\":[\"c9f833af-b3c3-453e-8b46-df827d4264eb\",\"f71d7316-fa39-41e3-8bd4-1dcd87381d2d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":354,\"y\":103,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"log.info(\\\"ODC -- ProcessInstance : \\\"+tw.local.odcRequest.appInfo.instanceID  +\\\" - ServiceName : AUDIT CREATE DATA : START\\\");\\r\\n \\r\\n\\r\\n\\r\\n\\r\\n\"]},\"name\":\"Search for requestNo\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/**-------------------------- Add SQL Parameters --------------------------**\\/\\r\\nfunction addSQLParameter(value){\\r\\n\\tif(value == null && typeof value == \\\"string\\\"){\\r\\n\\t\\tvalue= \\\"\\\"; \\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"number\\\"){\\r\\n\\t\\tvalue= 0;\\r\\n\\t}\\r\\n\\telse if(value == null && typeof value == \\\"boolean\\\"){\\r\\n\\t\\tvalue = \\\"0\\\";\\r\\n\\t}\\r\\n var parameter = new tw.object.SQLParameter();  \\r\\n   parameter.value = value;\\r\\n   parameter.mode = \\\"IN\\\";\\r\\nreturn parameter;\\r\\n}\\r\\n\\/***********************************************************************************\\/\\r\\n\\ttw.local.sql=\\\"SELECT ID FROM \\\"+ tw.env.DBSchema + \\\".ODC_REQUESTINFO WHERE REQUESTNO = ? \\\"\\r\\n\\t\\t\\r\\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\\r\\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\\r\\n\\r\\n\\r\\n\"]}},{\"startQuantity\":1,\"outgoing\":[\"7197dad2-4b36-4cc6-8e17-52094b099c7c\"],\"incoming\":[\"b85ff298-f81c-4996-807b-ca607cbe39e9\"],\"extensionElements\":{\"postAssignmentScript\":[\"\\r\\nif (!!tw.local.results && tw.local.results.listLength > 0 )\\r\\n\\ttw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;\\r\\n\\t\\r\\n\\t \"],\"nodeVisualInfo\":[{\"width\":95,\"x\":479,\"y\":103,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Get ID of Request SQL Execute Statement\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"1\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlParameters\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"9f4eb6e8-ca0b-4b3f-8785-678607e11258\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.results\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"9f4eb6e8-ca0b-4b3f-8785-678607e11258\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get ID of Request SQL Execute Statement\",\"declaredType\":\"sequenceFlow\",\"id\":\"b85ff298-f81c-4996-807b-ca607cbe39e9\",\"sourceRef\":\"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b\"},{\"outgoing\":[\"c27dcbbc-8a95-4f89-8f2f-45efe37c2fad\",\"55643b29-ffc3-4ee6-89ee-92865f61e092\"],\"incoming\":[\"7197dad2-4b36-4cc6-8e17-52094b099c7c\"],\"default\":\"c27dcbbc-8a95-4f89-8f2f-45efe37c2fad\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":32,\"x\":682,\"y\":122,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"update?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"*************-4299-8059-f22e8c1ef414\"},{\"targetRef\":\"1988ef7c-d47c-41ca-8f40-ab05373c7958\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"c27dcbbc-8a95-4f89-8f2f-45efe37c2fad\",\"sourceRef\":\"*************-4299-8059-f22e8c1ef414\"},{\"targetRef\":\"8895b0f8-2ec0-49f3-8848-9ddc17cb68e2\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestID\\t  >\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"55643b29-ffc3-4ee6-89ee-92865f61e092\",\"sourceRef\":\"*************-4299-8059-f22e8c1ef414\"},{\"targetRef\":\"2f5bf898-59ef-4548-80f2-bfecfcd3fc4b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Init\",\"declaredType\":\"sequenceFlow\",\"id\":\"1004085d-935b-4f0b-8b7d-00f0e577a11a\",\"sourceRef\":\"081c9314-33e1-4935-8aa2-fd628f60e06c\"},{\"targetRef\":\"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Search for requestNo\",\"declaredType\":\"sequenceFlow\",\"id\":\"c9f833af-b3c3-453e-8b46-df827d4264eb\",\"sourceRef\":\"c32dfa16-a852-4585-896b-47642ffee8bd\"},{\"parallelMultiple\":false,\"outgoing\":[\"fd084925-7d7f-49d7-8989-1e1357be3d46\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"582948a9-cdc3-457a-84e4-8902e477907a\",\"otherAttributes\":{\"eventImplId\":\"30d16f80-5b70-4f1f-8634-12dbbf5fd5c4\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1384,\"y\":226,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"17ed215d-500f-49f1-8c31-7bd7cfcd3e67\"},{\"targetRef\":\"fcc15744-4366-40c0-863f-44d3fbef78dd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"fd084925-7d7f-49d7-8989-1e1357be3d46\",\"sourceRef\":\"17ed215d-500f-49f1-8c31-7bd7cfcd3e67\"},{\"startQuantity\":1,\"outgoing\":[\"f71d7316-fa39-41e3-8bd4-1dcd87381d2d\"],\"incoming\":[\"1004085d-935b-4f0b-8b7d-00f0e577a11a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":178,\"y\":103,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Init\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2f5bf898-59ef-4548-80f2-bfecfcd3fc4b\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nvar requestType= tw.epv.RequestType.Create+\\\"\\\";\\r\\nif (tw.local.odcRequest.requestType.value == requestType){\\r\\n\\ttw.local.odcRequest.parentRequestNo= tw.local.odcRequest.requestNo;\\r\\n\\ttw.local.odcRequest.BasicDetails.parentRequestNo= tw.local.odcRequest.requestNo;\\r\\n\\t\\r\\n\\t}\\r\\n\\r\\n\"]}},{\"targetRef\":\"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exclusive Gateway\",\"declaredType\":\"sequenceFlow\",\"id\":\"f71d7316-fa39-41e3-8bd4-1dcd87381d2d\",\"sourceRef\":\"2f5bf898-59ef-4548-80f2-bfecfcd3fc4b\"},{\"parallelMultiple\":false,\"outgoing\":[\"6371f870-49c9-4241-87f2-2d5a5de4eb27\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"1c246876-97df-422c-841d-f03f1aff1e32\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"794653c5-6b68-4d7c-81e9-7f67f750847b\",\"otherAttributes\":{\"eventImplId\":\"6052246d-788e-4bdf-8abf-44441276f2a0\"}}],\"attachedToRef\":\"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1488,\"y\":114,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"15dbe67a-a7c9-4140-8add-4e119dc15301\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"87a6fb28-a343-4a33-812e-e6eb0a6463f0\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"124edf7c-ca1a-4ac9-8c62-af467eeedfbe\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"70012514-bb4c-468c-89fb-4965e328da43\",\"otherAttributes\":{\"eventImplId\":\"26862378-5ff7-4f34-8c29-6a0a3b1bd431\"}}],\"attachedToRef\":\"548669f1-7641-4c40-8867-17f51c191b64\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1225,\"y\":231,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"4f33d610-6ca6-4031-8084-0666bcc26181\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"7919967e-0eb7-4dc7-804f-4b4f47f29532\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"49982f30-6256-48ef-82dc-48b29d350b1b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"513b40ec-e25c-43e4-8e50-a73ad4502c30\",\"otherAttributes\":{\"eventImplId\":\"7cf5af0d-181d-4961-878f-f7a6f08ef2be\"}}],\"attachedToRef\":\"c5082aee-343a-452e-8b33-35b492b4251b\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":972,\"y\":231,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error2\",\"declaredType\":\"boundaryEvent\",\"id\":\"a48629c7-9082-4589-8d93-8b735c1dcc82\",\"outputSet\":{}},{\"targetRef\":\"fcc15744-4366-40c0-863f-44d3fbef78dd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"7919967e-0eb7-4dc7-804f-4b4f47f29532\",\"sourceRef\":\"a48629c7-9082-4589-8d93-8b735c1dcc82\"},{\"targetRef\":\"fcc15744-4366-40c0-863f-44d3fbef78dd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"87a6fb28-a343-4a33-812e-e6eb0a6463f0\",\"sourceRef\":\"4f33d610-6ca6-4031-8084-0666bcc26181\"},{\"targetRef\":\"fcc15744-4366-40c0-863f-44d3fbef78dd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"6371f870-49c9-4241-87f2-2d5a5de4eb27\",\"sourceRef\":\"15dbe67a-a7c9-4140-8add-4e119dc15301\"},{\"parallelMultiple\":false,\"outgoing\":[\"ef61f92e-9713-4cb1-8aef-8065c9fa7428\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"92c496b5-54cd-4f7d-8297-2bc9367cd880\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"6aed0f61-ac99-4f37-8726-1ba2504371d7\",\"otherAttributes\":{\"eventImplId\":\"f042136e-6141-4382-89eb-4cf18cec5008\"}}],\"attachedToRef\":\"6b290809-0a1a-487e-8fcf-899da748dc05\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":990,\"y\":114,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error3\",\"declaredType\":\"boundaryEvent\",\"id\":\"bf43fdb4-37f4-4208-80a8-16c178b90bd9\",\"outputSet\":{}},{\"targetRef\":\"fcc15744-4366-40c0-863f-44d3fbef78dd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"ef61f92e-9713-4cb1-8aef-8065c9fa7428\",\"sourceRef\":\"bf43fdb4-37f4-4208-80a8-16c178b90bd9\"}],\"laneSet\":[{\"id\":\"c0d17625-3a14-4a88-8ea7-d9e8d453b9c1\",\"lane\":[{\"flowNodeRef\":[\"081c9314-33e1-4935-8aa2-fd628f60e06c\",\"646302c3-7d88-4f2d-8840-dec30757da05\",\"1988ef7c-d47c-41ca-8f40-ab05373c7958\",\"6b290809-0a1a-487e-8fcf-899da748dc05\",\"6d3651c3-0746-4c2c-807b-a4f4d7ac6d81\",\"4f8c5959-0089-4977-8ac8-802b96f20d99\",\"fcc15744-4366-40c0-863f-44d3fbef78dd\",\"c32dfa16-a852-4585-896b-47642ffee8bd\",\"8895b0f8-2ec0-49f3-8848-9ddc17cb68e2\",\"c5082aee-343a-452e-8b33-35b492b4251b\",\"3b354799-bd42-4854-868d-e0629c388301\",\"548669f1-7641-4c40-8867-17f51c191b64\",\"29ef6559-8a76-4830-84f5-e12eee5f40a1\",\"ec4e3ae8-5b80-40c3-8718-4361f96b6b4b\",\"9f4eb6e8-ca0b-4b3f-8785-678607e11258\",\"*************-4299-8059-f22e8c1ef414\",\"17ed215d-500f-49f1-8c31-7bd7cfcd3e67\",\"2f5bf898-59ef-4548-80f2-bfecfcd3fc4b\",\"15dbe67a-a7c9-4140-8add-4e119dc15301\",\"4f33d610-6ca6-4031-8084-0666bcc26181\",\"a48629c7-9082-4589-8d93-8b735c1dcc82\",\"bf43fdb4-37f4-4208-80a8-16c178b90bd9\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"5e7ccb09-8e69-4a08-833a-ede6a123e618\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Audit Create Data\",\"declaredType\":\"process\",\"id\":\"1.7ee96dd0-834b-44cb-af41-b21585627e49\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"id\":\"2055.254cf8eb-2743-4c53-8c52-e51c8c22884e\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"reqID\",\"isCollection\":false,\"id\":\"2055.59dc474f-3e47-40ee-8737-ad21d25eb436\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sqlOut\",\"isCollection\":false,\"id\":\"2055.0d27ff21-d378-41ac-801e-065cf08cc7a7\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"e9b2480e-1992-4de1-8261-e524f562a11d\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.254cf8eb-2743-4c53-8c52-e51c8c22884e\",\"2055.59dc474f-3e47-40ee-8737-ad21d25eb436\",\"2055.0d27ff21-d378-41ac-801e-065cf08cc7a7\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"create\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"1111222224444\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.afad40c5-a38b-475c-8154-b4dabd94b6fe", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "921a8d09-0241-40aa-b26d-56763bde4083", "versionId": "d6430302-e92c-4838-bf40-17fde8631b82"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.254cf8eb-2743-4c53-8c52-e51c8c22884e", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "27b88540-64e1-412f-8f1d-6eb9f65153e2", "versionId": "bbcbe250-9db3-4cc0-863b-611911d5396b"}, {"name": "reqID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.59dc474f-3e47-40ee-8737-ad21d25eb436", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "880debf0-3945-4cf4-8c93-8073f2226151", "versionId": "0dfb194f-01b9-4735-b744-649f299af4f6"}, {"name": "sqlOut", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0d27ff21-d378-41ac-801e-065cf08cc7a7", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4150cb3c-a546-4bfb-92f7-428814f5b3dc", "versionId": "bd51019a-aa17-4340-96aa-797d701e9c41"}], "processVariable": [{"name": "sqlParameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c7095e57-5f5f-49e1-89d1-7aa304a64524", "description": {"isNull": "true"}, "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "dbaa3cf7-1305-44d5-901a-90ac61268cb8", "versionId": "8225c37a-f39c-430d-b7c0-55948a35ec4c"}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.116007eb-077a-4059-8f3b-1ff2f6837c3d", "description": {"isNull": "true"}, "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d06a24c1-4c06-4c7d-8369-d3d82ac05902", "versionId": "78fb4085-cc44-4e81-9446-179285baee98"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.cb80769f-3387-4e8b-8b5e-82a6dfd69a8e", "description": {"isNull": "true"}, "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bc152c6c-785e-4f2c-9602-a31cc72ade0a", "versionId": "b5dc6e07-09bb-46c0-964c-733f02d6f4f4"}, {"name": "requestID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.63cfedee-d6e5-4a3c-8e47-d9d6176aaf3f", "description": {"isNull": "true"}, "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "true", "defaultValue": "0", "guid": "cb65610b-6282-4160-a247-7c40704d9008", "versionId": "a0b7dca5-f374-4a69-964b-8fa1a9ef60c5"}, {"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ce409362-d245-4476-8bac-bdafde3da38f", "description": {"isNull": "true"}, "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "namespace": "2", "seq": "5", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f5b6523e-a12d-41b1-b726-39e05fd58a03", "versionId": "74a7e0d3-6457-4b71-92b4-ac9abc9186f3"}, {"name": "date", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f401dcad-832c-452f-823c-6fca66361cee", "description": {"isNull": "true"}, "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "true", "defaultValue": "new TWDate()", "guid": "03238a08-35d9-40e4-8033-2eadaf16c4c9", "versionId": "3984438d-6e2d-4a88-8309-1470bbcd956d"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.548669f1-7641-4c40-8867-17f51c191b64", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Delete ODC Request Execute SQL", "tWComponentName": "SubProcess", "tWComponentId": "3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:251d", "versionId": "16bbc3a5-8862-42d6-8e43-c3f73feac4b5", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1190", "y": "173", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "bottomLeft", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "guid": "1207fcbe-a39d-4fe6-8b27-791fa2ab30d8", "versionId": "1e899175-4a77-47af-b23c-32345d563e25", "parameterMapping": [{"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c1a7d243-b5a4-4277-a424-7d5ceaeca375", "processParameterId": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "parameterMappingParentId": "3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isList": "false", "isInput": "false", "guid": "c3dd68b0-6841-4ee7-816f-40a99110ad67", "versionId": "1f8f2d1a-46c3-407e-9e8c-9719d69fa249", "description": {"isNull": "true"}}, {"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.04338fe6-619b-4803-aa52-12e8a1552b44", "processParameterId": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "parameterMappingParentId": "3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f", "useDefault": "false", "value": "tw.local.sqlStatements", "classRef": "/12.3990d8b4-983d-4250-bd06-3600f527fed0", "isList": "true", "isInput": "true", "guid": "dec739ce-1440-41d8-8f85-c0cdb3841ac8", "versionId": "505a685f-2149-446c-9840-01c63f243913", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f265f013-d7af-45ef-b9d7-3884a43624c3", "processParameterId": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "parameterMappingParentId": "3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "008b0565-bd1a-4bde-ae77-356768f633a8", "versionId": "84309312-9c2c-4235-8519-cce0263d11fe", "description": {"isNull": "true"}}, {"name": "returnType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.58ec74bc-9acd-467c-9184-3b6744d65def", "processParameterId": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "parameterMappingParentId": "3012.6059ae5d-ffb4-4c0c-9cd2-61380f28224f", "useDefault": "false", "value": "\"SQLResult\"", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "7442c9d1-317f-46a3-a142-68710efebb02", "versionId": "c1589603-37de-474d-a907-825de74cd773", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Insert ODC Request Execute SQL", "tWComponentName": "SubProcess", "tWComponentId": "3012.312838e0-7389-43f9-906e-3991ac664612", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "guid": "guid:68332a9f3202b062:612a65b7:18aa4a1e041:-6f4a", "versionId": "263947c3-e524-4f71-b73d-f463a4bb4f47", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1427", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomRight", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.312838e0-7389-43f9-906e-3991ac664612", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "guid": "84f882c4-7f81-4a85-80bd-9c871ba45113", "versionId": "9389ae06-dc55-41ea-845b-f21982d82cb3", "parameterMapping": [{"name": "sqlStatements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.bdf4cc12-e06c-45c3-9b25-ae3e00035879", "processParameterId": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "parameterMappingParentId": "3012.312838e0-7389-43f9-906e-3991ac664612", "useDefault": "false", "value": "tw.local.sqlStatements", "classRef": "/12.3990d8b4-983d-4250-bd06-3600f527fed0", "isList": "true", "isInput": "true", "guid": "b0926685-d4a9-4879-895e-5193134e644a", "versionId": "39cb94cf-29d6-43e8-a41a-9f287b7341d6", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.460e6f9e-a88f-4cbb-9df7-a8c8cbe22d49", "processParameterId": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "parameterMappingParentId": "3012.312838e0-7389-43f9-906e-3991ac664612", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "d41c8b2d-a27a-4480-a62b-1d200f2b146e", "versionId": "4964e24c-b88c-42a6-9f12-f4619ad3e743", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.320c370b-5eaf-4a5b-b4f0-4148bb1e8114", "processParameterId": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "parameterMappingParentId": "3012.312838e0-7389-43f9-906e-3991ac664612", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isList": "false", "isInput": "false", "guid": "8203d21f-9463-48ee-9465-a4f6a9e2525f", "versionId": "c5e9916e-7e30-4499-b8c3-3760664c18d3", "description": {"isNull": "true"}}, {"name": "returnType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.093d176d-cf7c-4429-bffe-6807fc1cfe62", "processParameterId": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "parameterMappingParentId": "3012.312838e0-7389-43f9-906e-3991ac664612", "useDefault": "false", "value": "\"SQLResult\"", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "58f08d48-1493-451d-9375-5509b8e4e349", "versionId": "d825bbb8-9d26-420b-9034-5f84c8664be4", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6b290809-0a1a-487e-8fcf-899da748dc05", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "SQL Execute Statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "guid": "guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3ed7", "versionId": "292b72ce-9bda-4e00-ade4-12644a6395ea", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.78478636-bda8-4ac6-b938-81ea06531fdd", "processItemId": "2025.6b290809-0a1a-487e-8fcf-899da748dc05", "location": "2", "script": {"isNull": "true"}, "guid": "de60251b-c3b5-4f54-9d8d-2e0b2047a841", "versionId": "37300624-d646-4be5-97be-7204b01d9f1f"}, "layoutData": {"x": "955", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error3", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topLeft", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "db52592a-d54c-4ccf-b560-356f5a179e3d", "versionId": "245a3991-1cb2-492b-b329-4410a437a936", "parameterMapping": [{"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.fb975ad6-d66e-465c-a2c5-73ef259e5901", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "c9c70372-3d68-44c5-8381-88273cf4de52", "versionId": "2281673d-173c-4b62-bc8e-a3f4c79ed386", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0e3ad9a5-7556-4eb9-9879-9412b1cc03d3", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "900a540a-1b9f-4ddc-992f-8e026ebc39b1", "versionId": "62680f46-2a65-4b24-a6dc-a8f41fa96063", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.64db4079-b70c-4a32-aa14-411e21ea0582", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4", "useDefault": "false", "value": "1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "0c5531dd-8860-4e88-acc5-e2ac082465a4", "versionId": "a7559674-b92e-41ad-a227-0e7f15e7f743", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.49a039e8-747a-4c67-85a8-93f01dec0593", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "09d0a855-c388-4877-a2ca-431acbd3d45c", "versionId": "cbca44c5-c1d4-4749-bdfd-f3b9251c9d24", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.fa1e329b-8689-4fb7-8c00-665f2d063676", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.2502c658-bfe3-4e71-ac71-d984f8a7feb4", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "8a002454-d9e6-48e9-ba38-31de99482d34", "versionId": "e3d23ec5-a7a1-4dfd-b6b9-c6405824b85a", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Update Request Info", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.7abba84e-a437-4e81-89c8-46808854585a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:-6cb8", "versionId": "333bab8e-3229-4792-a471-8bc539a02cc4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.cf11e8c2-ff27-4a6c-857d-1f9370278d1c", "processItemId": "2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "location": "1", "script": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START--Update Path\");\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n", "guid": "fe7aa395-99c7-4211-9af6-87cf69cb97e9", "versionId": "c7a7d6d6-5511-450e-b420-2e362d183347"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.cc61e741-33e3-4772-add8-8211b632258e", "processItemId": "2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "location": "2", "script": "tw.local.sqlOut= tw.local.sql;\r\r\ntw.local.reqID= tw.local.requestID;", "guid": "ebe44ac5-1fe5-4635-b160-672fa5729cd7", "versionId": "e0b1be3b-aae4-4295-bbd4-2793068f7b34"}], "layoutData": {"x": "832", "y": "173", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.7abba84e-a437-4e81-89c8-46808854585a", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/*********************************************************************************************************************************************/\r\r\n\t\r\r\ntw.local.sql= \"UPDATE \"+ tw.env.DBSchema +  \".ODC_RequestInfo SET (\"\r\r\n\t\t+ \" requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,         \t\t       \"  \r\r\n\t\t+ \" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \t\t\t \"  \r\r\n\t\t+ \" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \"  \r\r\n\t\t+ \" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \"  \r\r\n\t\t+ \" amountDefAvalization, collectableAmount,\"\r\r\n\t\t+ \"outstandingAmount, maturityDate, maturityDays,\"\r\r\n\t\t+ \"financeApprovalNo,\"\r\r\n\t\t+\"hubCode,  \"  \r\r\n\t\t+ \" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \t\t\t\t\t  \t\t\t \"  \r\r\n\t\t+ \" CBEClassification, shipmentMethod, shipmentDate, \t\t\t\t\t\t \t\t\t\t\t\t \"  \r\r\n\t\t+ \" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \t\t\t \"  \r\r\n\t\t+ \" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \t,ISLIQUIDATED ,\t \" \r\r\n\t\t+ \" BPMInstanceNumber, currentStepName, substatus, \t\t\t\t\t\t\t\t\t\t\t \t\"\r\r\n\t\t+\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \"\r\r\n\t\t+ \" ) \"\r\r\n\t\t+\" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\"\r\r\n\t\t+\",?,?,?,?,?,?,?,?,?\"\r\r\n\t\t+\")\"\r\r\n\t\t+\" WHERE requestNo = ? \";\r\r\n\t\t\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\"0\")); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \r\r\n//\r\r\n//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \r\r\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\n\r\r\n", "isRule": "false", "guid": "cfb2a556-37b4-439b-9643-8bbb789d400b", "versionId": "2c2ca018-47ab-4a32-ad57-35505cf776a4"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.646302c3-7d88-4f2d-8840-dec30757da05", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.a85aecba-3c78-4608-bb87-660ff73006a1", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de5", "versionId": "3b6924a8-7359-41a4-b099-145f4d74cf54", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.31c7a11e-57d5-4b01-a565-77d45fed6255", "processItemId": "2025.646302c3-7d88-4f2d-8840-dec30757da05", "location": "1", "script": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID +\" - ServiceName : AUDIT CREATE DATA : END\");\r\r\n\r\r\ntw.local.reqID= tw.local.requestID;", "guid": "ebe498f9-997b-4d33-a952-16a22e1d9ab8", "versionId": "ea659bbb-80bc-4bdc-b2c3-c65ef27cdeac"}, "layoutData": {"x": "1555", "y": "79", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.a85aecba-3c78-4608-bb87-660ff73006a1", "haltProcess": "false", "guid": "5c135035-7fd2-4ffb-a40b-55d7c6f4a498", "versionId": "7d37599a-3c4b-4be8-8b14-d3a42dd91570"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d", "versionId": "5a7ec6b3-ab73-4f92-9841-ea4269131755", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "1458", "y": "203", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "028f2c63-7a09-42c9-942d-caef7c65c110", "versionId": "76197891-eb54-44a8-a52e-0ef62626aa41", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.eb7090af-c3bf-4e33-aad7-54510e6ec9b9", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "0fac2513-fd5b-4fc5-a22b-81142edd6546", "versionId": "35c7f1d2-3dc0-45a7-89b9-fc70fe204515", "description": {"isNull": "true"}}, {"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.08a09a5c-0bee-40bf-82b4-777fbe2c5d57", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946", "useDefault": "false", "value": "\"Audit Create Data\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "9473c360-bd00-47f7-bd91-9e008ef5e988", "versionId": "6fe8e39a-015a-486b-9bc8-753557e8e5cf", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d3d40c54-231f-49e4-8763-e1e7d39ba90f", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.4e002975-bb4a-4fa0-b11e-cd08fa0c3946", "useDefault": "false", "value": "tw.local.errorMessage", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "*************-45d5-8d18-8259bcf69443", "versionId": "b9a51297-2741-48f6-ab13-d4c3e50e40f2", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c32dfa16-a852-4585-896b-47642ffee8bd", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Test Data", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.2579a8e0-0063-4d22-96c4-b8858061415c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:266f6f4955d8489f:7b0b9b81:18b5b81b903:5e4b", "versionId": "89e597f7-1efb-4dc9-8cd0-3f80e41d48e4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.ebdedfcf-91d9-463d-9350-c20785f9965e", "processItemId": "2025.c32dfa16-a852-4585-896b-47642ffee8bd", "location": "1", "script": "log.info(\"ODC -- ProcessInstance : \"+33311 +\" - ServiceName : AUDIT CREATE DATA : START\");\r\r\n \r\r\n", "guid": "3fae409b-f06c-4996-af04-f915feb8a936", "versionId": "da273725-b3fd-4621-bd0e-1c1b156b7412"}, "layoutData": {"x": "207", "y": "187", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.2579a8e0-0063-4d22-96c4-b8858061415c", "scriptTypeId": "2", "isActive": "true", "script": " tw.local.odcRequest.appInfo.instanceID= \"33311\";\r\r\n tw.local.odcRequest.requestNo= \"12345678911111_03\";\r\r\n tw.local.odcRequest.requestNature.name= \"new\";\r\r\n tw.local.odcRequest.requestType.name = \"collect \"\r\r\n tw.local.odcRequest.requestDate= new TWDate();\r\r\n tw.local.odcRequest.BasicDetails.requestState=\"Final\";\r\r\n tw.local.odcRequest.appInfo.status=\"Completed\";\r\r\n tw.local.odcRequest.parentRequestNo=\"12345678911111\";\r\r\n tw.local.odcRequest.BasicDetails.flexCubeContractNo=\"11112222\";\r\r\n tw.local.odcRequest.BasicDetails.contractStage=\"stage3\";\r\r\n tw.local.odcRequest.BasicDetails.exportPurpose.name=\"exportP\";\r\r\n tw.local.odcRequest.BasicDetails.paymentTerms.name= \"PT\";\r\r\n tw.local.odcRequest.BasicDetails.productCategory.name= \"PCpc\";\r\r\n tw.local.odcRequest.BasicDetails.commodityDescription= \"CD11\";\r\r\n tw.local.odcRequest.CustomerInfo.cif= \"1234\";\r\r\n tw.local.odcRequest.CustomerInfo.customerName=\"smsma\";\r\r\n tw.local.odcRequest.ContractCreation.baseDate= new TWDate();\r\r\n tw.local.odcRequest.ContractCreation.valueDate = new TWDate();\r\r\n tw.local.odcRequest.ContractCreation.tenorDays= 3;\r\r\n tw.local.odcRequest.ContractCreation.transitDays= 4;\r\r\n tw.local.odcRequest.ContractCreation.maturityDate = new TWDate();\r\r\n tw.local.odcRequest.ContractCreation.userReference= \"1223231\";\r\r\n /**********************************************************************************/\r\r\n tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"bill 03\";\r\r\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\n tw.local.odcRequest.BasicDetails.Bills[1] = new tw.object.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingRef = \"Bill 04\";\r\r\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingDate = new TWDate();\r\r\n \r\r\n tw.local.odcRequest.BasicDetails.Bills[2] = new tw.object.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingRef = \"Bill 05\";\r\r\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingDate = new TWDate();\r\r\n \r\r\n tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\n tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"Inv 11\";\r\r\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\n// tw.local.odcRequest.BasicDetails.Invoice[1] = new tw.object.Invoice();\r\r\n// tw.local.odcRequest.BasicDetails.Invoice[1].invoiceNo = \"Inv 08\";\r\r\n//  tw.local.odcRequest.BasicDetails.Invoice[1].invoiceDate = new TWDate();\r\r\n \r\r\n /******************************************************************************************/\r\r\n\r\r\n tw.local.odcRequest.Parties = new tw.object.odcParties();\r\r\n tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\r\r\n tw.local.odcRequest.Parties.Drawer.partyId = \"00000000\";\r\r\n tw.local.odcRequest.Parties.Drawer.partyName = \"Party01\";\r\r\n tw.local.odcRequest.Parties.Drawer.country = \"Egp\";\r\r\n tw.local.odcRequest.Parties.Drawer.Language = \"EN\";\r\r\n tw.local.odcRequest.Parties.Drawer.Reference = \"1234\";\r\r\n tw.local.odcRequest.Parties.Drawer.address1 = \"add1\";\r\r\n tw.local.odcRequest.Parties.Drawer.address2 = \"add2\";\r\r\n tw.local.odcRequest.Parties.Drawer.address3 = \"add3\";\r\r\n tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\r\r\n tw.local.odcRequest.Parties.Drawee.partyId = \"********\";\r\r\n tw.local.odcRequest.Parties.Drawee.partyName = \"Party02\";\r\r\n tw.local.odcRequest.Parties.Drawee.country = \"EUR\";\r\r\n tw.local.odcRequest.Parties.Drawee.Language = \"arabic\";\r\r\n tw.local.odcRequest.Parties.Drawee.Reference = \"ref1\";\r\r\n tw.local.odcRequest.Parties.Drawee.address1 = \"address1\";\r\r\n tw.local.odcRequest.Parties.Drawee.address2 = \"address2\";\r\r\n tw.local.odcRequest.Parties.Drawee.address3 = \"address3\";\r\r\n tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\r\r\n tw.local.odcRequest.Parties.collectingBank.id = \"********\";\r\r\n tw.local.odcRequest.Parties.collectingBank.name = \"Party03\";\r\r\n tw.local.odcRequest.Parties.collectingBank.country = \"Egypt\";\r\r\n tw.local.odcRequest.Parties.collectingBank.language = \"french\";\r\r\n tw.local.odcRequest.Parties.collectingBank.reference = \"ref2\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address1 = \"address1\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address2 = \"address2\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address3 = \"address3\";\r\r\n tw.local.odcRequest.Parties.collectingBank.cif = \"********\";\r\r\n tw.local.odcRequest.Parties.collectingBank.media = \"swift\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address = \"address\";\r\r\n tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\r\r\n tw.local.odcRequest.Parties.partyTypes.partyCIF = \"********\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyId = \"********\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyName = \"Party04\";\r\r\n tw.local.odcRequest.Parties.partyTypes.country = \"italy\";\r\r\n tw.local.odcRequest.Parties.partyTypes.language = \"italian\";\r\r\n tw.local.odcRequest.Parties.partyTypes.refrence = \"reference0\";\r\r\n tw.local.odcRequest.Parties.partyTypes.address1 = \"add11\";\r\r\n tw.local.odcRequest.Parties.partyTypes.address2 = \"add22\";\r\r\n tw.local.odcRequest.Parties.partyTypes.address3 = \"add33\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.Parties.partyTypes.partyType.name = \"Case In Need\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyType.value = \"Case In Need\";\r\r\n// ------------------------------------------------------------------------------\r\r\n tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].component = \"component\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 11.0;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].waiver = false;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"GL Account\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"1111\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"111\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.branchCode = \"055\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balance = 200;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balanceSign = \"D\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.isOverDraft = true;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.standardExRate = 990.0;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 990.0;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.amountInAccount = 99.9;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].rateType = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].description = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultPercentage = 99.9;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].changePercentage = 99.9;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultAmount = 99.1;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].flatAmount = 900.0;\r\r\n /******************************************************************************************/\r\r\n\r\r\n tw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\r\n tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.currency.name = \"\";\r\r\n tw.local.odcRequest.FcCollections.currency.value = \"Egypt\";\r\r\n tw.local.odcRequest.FcCollections.standardExchangeRate = 119.0;\r\r\n tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 99.9;\r\r\n tw.local.odcRequest.FcCollections.fromDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.ToDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.accountNo.name = \"\";\r\r\n tw.local.odcRequest.FcCollections.accountNo.value = \"11999\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \"22222\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \"11199\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 111.0;\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 109.0;\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \"Egypt\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 190.0;\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency =99.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \"\";\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \"\";\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\n tw.local.odcRequest.FcCollections.isReversed = false;\r\r\n tw.local.odcRequest.FcCollections.usedAmount = 999.0;\r\r\n tw.local.odcRequest.FcCollections.calculatedAmount = 119.0;\r\r\n tw.local.odcRequest.FcCollections.totalAllocatedAmount = 999.9;\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0].name = \"11119\";\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0].value = \"11119\";\r\r\n /******************************************************************************************/\r\r\n\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 100.0; \r\r\n\r\r\ntw.local.odcRequest.appInfo.instanceID= \"1234\";\r\r\ntw.local.odcRequest.appInfo.stepName=\"Create Act06\";\r\r\ntw.local.odcRequest.appInfo.subStatus=\"Completed\";\r\r\n", "isRule": "false", "guid": "e2fdfde5-2257-4d11-ac09-1307a8123876", "versionId": "4c3f3c8e-8ab5-441a-92c1-4e209cb74f91"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1988ef7c-d47c-41ca-8f40-ab05373c7958", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Insert Request_info & Retrieve Request ID", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.3df8b291-5cb3-4ac6-8986-8c041<PERSON><PERSON>ae", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de8", "versionId": "98bf12b5-874e-440d-b1f7-adb207e26bc4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.4615599f-7bb7-4d93-afcf-a98ba521703c", "processItemId": "2025.1988ef7c-d47c-41ca-8f40-ab05373c7958", "location": "1", "script": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START--Insert Path\");\r\r\n\r\r\n\r\r\n\r\r\n", "guid": "7ea2dcdf-d555-4014-8857-4136fa15523b", "versionId": "5da75e1f-c023-4b0d-9016-df720d55a712"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.c5f4499b-4ebe-49b3-82a8-a91bcfbf51be", "processItemId": "2025.1988ef7c-d47c-41ca-8f40-ab05373c7958", "location": "2", "script": {"isNull": "true"}, "guid": "116861cf-3fb0-4484-a21d-64e03061c910", "versionId": "ac0697e6-feed-4dde-95bf-f6c5b1ece73a"}], "layoutData": {"x": "838", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.3df8b291-5cb3-4ac6-8986-8c041<PERSON><PERSON>ae", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/***********************************************************************************/\r\r\n\ttw.local.sql=\" SELECT ID FROM FINAL TABLE ( \"\r\r\n\t\t\t+ \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_RequestInfo ( \"\r\r\n               \t+ \" requestNo, requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,               \"  \r\r\n\t\t\t+ \" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \t\t\t \"  \r\r\n\t\t\t+ \" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \"  \r\r\n\t\t\t+ \" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \"  \r\r\n\t\t\t+ \" amountDefAvalization, collectableAmount, outstandingAmount, maturityDate, maturityDays, financeApprovalNo, hubCode,  \"  \r\r\n\t\t\t+ \" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \t\t\t\t\t  \t\t\t \"  \r\r\n\t\t\t+ \"   CBEClassification, shipmentMethod, shipmentDate, \t\t\t\t\t\t \t\t\t\t\t \"  \r\r\n\t\t\t+ \" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \t\t\t \"  \r\r\n\t\t\t+ \" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \t,ISLIQUIDATED,\t \" \r\r\n\t\t\t+\"  BPMInstanceNumber, currentStepName, substatus,\t\t\t\t\t\t\t\t\t\t\t \"\r\r\n\t\t\t+\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \"\r\r\n\t\t\t+ \" ) VALUES\" \r\r\n\t\t\t+ \" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\"\r\r\n\t\t\t+\",?,?,?,?,?,?,?)\"\r\r\n\t\t\t+ \" )\";\r\r\n\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\"0\")); \r\r\n \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \r\r\n  \r\r\n///\r\r\n\r\r\n//CHARGESACCOUNT\r\r\n//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \r\r\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \r\r\n\r\r\n\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "433d4df9-751b-48b8-bc88-0b5d5bfa7029", "versionId": "24eaab97-560d-4830-847e-ef712ba90464"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.4f8c5959-0089-4977-8ac8-802b96f20d99", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Insert ODC Data", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.de154948-673f-492c-9733-c02ca530b79e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:68332a9f3202b062:612a65b7:18aa9f8699f:5c16", "versionId": "a2413efd-ee77-4af7-bef8-4d92d7181f41", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.6407e712-0264-4a97-909f-169a71a1367a", "processItemId": "2025.4f8c5959-0089-4977-8ac8-802b96f20d99", "location": "1", "script": {"isNull": "true"}, "guid": "ddc4af8f-b653-484c-b1b7-ac68daa76c0e", "versionId": "bfdebec1-578f-4115-8605-1edd09710ca6"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.2d788eba-1880-4a50-9ba6-1ce01572cc40", "processItemId": "2025.4f8c5959-0089-4977-8ac8-802b96f20d99", "location": "2", "script": {"isNull": "true"}, "guid": "c206effa-f71c-4b34-a520-3fa3aa2b5609", "versionId": "fdc3a4cb-c317-4981-845d-fd0db593a4cb"}], "layoutData": {"x": "1313", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.de154948-673f-492c-9733-c02ca530b79e", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\ntw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/***********************************************************************************/\r\r\n\r\r\n/**-----------------------------------------------------------------------**/\r\r\n/**-------------------------- Log All ODC Data ---------------------------**/\r\r\n/**-----------------------------------------------------------------------**/\r\r\n\r\r\nvar i = 0;\r\r\nvar sqlStatementLen= 0;\r\r\n\r\r\n/**-------------------------- Log Invoice Data --------------------------**/\r\r\n\r\r\nwhile (i < tw.local.odcRequest.BasicDetails.Invoice.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_Invoice ( \" \r\r\n\t\t\t\t\t\t\t\t  + \" requesrID, invoiceNo , invoiceDate\" \r\r\n\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate));\r\r\n\t\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log Bills Data --------------------------**/\r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.BasicDetails.Bills.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_Bills ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, billOfLadingRef, billOfLadingDate\" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate));\r\r\n\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log Parties Data --------------------------**/\r\r\n\r\r\n/**-------------------------- Set SQL query to add party record --------------------------**/\r\r\nfunction AuditParty(requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address){\r\r\n\r\r\n\tif(partyId != null || partyId != \"\"){\r\r\n\t\t\r\r\n\t\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_PartiesInfo ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, partyId, partyType, cif, partyName,Country,Language,\" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  +\"  Reference, Address1, Address2, Address3, Media, Address\" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?,?,?,?,?,?,?) \";\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(requesrID));\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyId));\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyType));\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(cif));\t\t\r\r\n\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyName));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(country));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(language));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(reference));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address1));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address2));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address3));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(media));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address));\t\r\r\n\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\telse{\r\r\n\t\treturn false;\r\r\n\t}\r\r\n}\r\r\n//Params: requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address\r\r\n//Audit Drawer \r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawer.partyId , \"Drawer\", \"\" , tw.local.odcRequest.Parties.Drawer.partyName, \r\r\n tw.local.odcRequest.Parties.Drawer.country, tw.local.odcRequest.Parties.Drawer.Language, tw.local.odcRequest.Parties.Drawer.Reference,\r\r\n tw.local.odcRequest.Parties.Drawer.address1, tw.local.odcRequest.Parties.Drawer.address2, tw.local.odcRequest.Parties.Drawer.address3,\"\",\"\");\r\r\n\r\r\n//Audit Drawee\r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawee.partyId , \"Drawee\", \"\", tw.local.odcRequest.Parties.Drawee.partyName,\r\r\ntw.local.odcRequest.Parties.Drawee.country, tw.local.odcRequest.Parties.Drawee.Language, tw.local.odcRequest.Parties.Drawee.Reference,\r\r\ntw.local.odcRequest.Parties.Drawee.address1, tw.local.odcRequest.Parties.Drawee.address2, tw.local.odcRequest.Parties.Drawee.address3,\"\",\"\" );\r\r\n\r\r\n//Audit collecting Bank\r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.collectingBank.id , \"collecting Bank\",tw.local.odcRequest.Parties.collectingBank.cif,\r\r\ntw.local.odcRequest.Parties.collectingBank.name, tw.local.odcRequest.Parties.collectingBank.country, tw.local.odcRequest.Parties.collectingBank.language,\r\r\ntw.local.odcRequest.Parties.collectingBank.reference, tw.local.odcRequest.Parties.collectingBank.address1, tw.local.odcRequest.Parties.collectingBank.address2,\r\r\ntw.local.odcRequest.Parties.collectingBank.address3, tw.local.odcRequest.Parties.collectingBank.media, tw.local.odcRequest.Parties.collectingBank.address);\r\r\n\r\r\n//Audit Accountee/ Case In Need\r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.partyTypes.partyId, tw.local.odcRequest.Parties.partyTypes.partyType.value, tw.local.odcRequest.Parties.partyTypes.partyCIF,\r\r\ntw.local.odcRequest.Parties.partyTypes.partyName, tw.local.odcRequest.Parties.partyTypes.country, tw.local.odcRequest.Parties.partyTypes.language, tw.local.odcRequest.Parties.partyTypes.refrence,\r\r\ntw.local.odcRequest.Parties.partyTypes.address1, tw.local.odcRequest.Parties.partyTypes.address2, tw.local.odcRequest.Parties.partyTypes.address3, \"\", \"\");\r\r\n\r\r\n /**-------------------------- Log Charges And Commissions Data --------------------------**/\r\r\n\r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.ChargesAndCommissions.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_CHARGESCOMMISSIONS ( \" \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, defaultCurrency, defaultAmount, chargeAmount, waiver, accountClass,\"\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" accountNo, branchCode, currency, balance, balanceSign, standardExRate,\"   \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \"negotiatedExRate, debitedAmount, COMPONENT\"\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultAmount));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount));\r\r\n\t\r\r\n\tif(tw.local.odcRequest.ChargesAndCommissions[i].waiver)\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\"1\"));\r\r\n\telse\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\"0\"));\r\r\n\t\r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount)\r\r\n\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount ={};\r\r\n\t    \r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass)\r\r\n\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass ={};\r\r\n\t    \r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value));\r\r\n\t\r\r\n    \r\r\n\tvar accountClass= tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value;\r\r\n\t\r\r\n\tif(accountClass == \"GL Account\"){\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo));\r\r\n\t}\r\r\n\t//Customer Account\r\r\n\telse{\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo));\r\r\n\t}\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode));\r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency)\r\r\n\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency = {};\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign));\r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount)\r\r\n\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount = {};\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].component));\r\r\n\t\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log FlexCube Transactions Data --------------------------**/ \r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.FcCollections.selectedTransactions.listLength) {\r\r\n\t\r\r\n\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n \ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_FCTransaction ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID,accountNo, postingDate, valueDate, transactionAmount, AMOUNTFORCURRENTREQUEST, TRANSACTIONREFNO \"\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest));\r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber));\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log Multi Tenor dates Data --------------------------**/\r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n \ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_MultiTenorDates ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, installmentDate, installmentAmount \"\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date));\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount));\r\r\n\r\r\n    i++;\r\r\n}\r\r\n     ", "isRule": "false", "guid": "44fbf293-0d39-40d0-9c2a-3102e653328a", "versionId": "203c7f75-a96d-4dd0-8daf-d1d398d30540"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Init", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.8b422e90-9e71-427b-ad5c-4842e7323bf4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:d96", "versionId": "c15fc4a4-ed5c-46e0-9f83-4a0a80035bbf", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "178", "y": "103", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.8b422e90-9e71-427b-ad5c-4842e7323bf4", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\nvar requestType= tw.epv.RequestType.Create+\"\";\r\r\nif (tw.local.odcRequest.requestType.value == requestType){\r\r\n\ttw.local.odcRequest.parentRequestNo= tw.local.odcRequest.requestNo;\r\r\n\ttw.local.odcRequest.BasicDetails.parentRequestNo= tw.local.odcRequest.requestNo;\r\r\n\t\r\r\n\t}\r\r\n\r\r\n", "isRule": "false", "guid": "23e2dca2-ecd3-44db-83bb-5adc6a739746", "versionId": "ce194249-9898-40ed-8ea2-d37103267c36"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.*************-4299-8059-f22e8c1ef414", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "update?", "tWComponentName": "Switch", "tWComponentId": "3013.7a2e0c57-f0c3-4b28-af22-9d2ef60d758f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1d22", "versionId": "c1fc5a93-4949-4795-aabf-ffd110046ffa", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.1d0b86fa-92a4-4f3e-9e0f-40df36bb282d", "processItemId": "2025.*************-4299-8059-f22e8c1ef414", "location": "2", "script": {"isNull": "true"}, "guid": "e5a15f2b-4fdd-44d4-ade8-03e0ad504450", "versionId": "ea8f14a3-2dd6-4fdd-b567-9ebac5d1ceed"}, "layoutData": {"x": "682", "y": "122", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.7a2e0c57-f0c3-4b28-af22-9d2ef60d758f", "guid": "a3538f35-f999-4682-a12e-4de5da393f2f", "versionId": "ab1d6396-6760-46ff-881e-ae1e809a37bf", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.c734b7e8-d4af-4c88-99c4-7ca3ee35a271", "switchId": "3013.7a2e0c57-f0c3-4b28-af22-9d2ef60d758f", "seq": "1", "endStateId": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:33ea", "condition": "tw.local.requestID\t  >\t  0", "guid": "8a2bf000-7fca-46c8-8ca2-4198f34240af", "versionId": "6b4c35d7-3df5-4866-8f27-5b28fe0d313e"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c5082aee-343a-452e-8b33-35b492b4251b", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "SQL Update Request Info", "tWComponentName": "SubProcess", "tWComponentId": "3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:-6cb2", "versionId": "da3aca42-bf4a-4298-a5d2-03dadec3c44d", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "963", "y": "173", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomLeft", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:-6c6d", "errorHandlerItemId": "2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "bottomLeft", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "74aacb6c-d487-479a-975e-15d4a8a066bf", "versionId": "1872907d-436c-440d-87e8-5e375494c306", "parameterMapping": [{"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7cb3a2b5-db36-46f6-971b-24a353b686b4", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "5e8442c1-d38c-49a8-b459-2e2acb44e06f", "versionId": "66e74df1-0905-4680-b9eb-da9f19750dc8", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7ad108b9-8bc1-413a-bd57-0b5b18dec262", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "65b9cbe0-add4-4d4d-935d-4c385744c991", "versionId": "af099eac-c00b-474b-90c6-acd9f718dcdd", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1b2d1a6f-afda-4c7e-8fd6-a1debc676f7d", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "2b336a9b-f29c-40c4-b718-35fdbd8c3f65", "versionId": "c0d10068-116c-47a5-83cd-6d6521ae70e8", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.404616b1-7430-40d9-b518-8ac3def53c7a", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "55509f8d-6d9d-47a3-ac75-266609dc6d22", "versionId": "f203abe8-d349-401c-8c07-583ccad688e3", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c5dd03f6-6e48-4402-8ec7-00ff25634c07", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.ba563f65-a7d4-4eb9-bcc6-f73724016b24", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "c934e0aa-f434-455d-93e2-ed56888ab295", "versionId": "f3dbff2e-fa8c-4241-a2e4-7b400f077349", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Get ID of Request SQL Execute Statement", "tWComponentName": "SubProcess", "tWComponentId": "3012.097f5a82-06c9-48af-8b7e-4e3b258e7310", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1cfa", "versionId": "db1fd768-0c77-4662-b999-dfd7daac2678", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.239a0eea-c553-47cc-bcd0-2dccc9dbee23", "processItemId": "2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258", "location": "2", "script": "\r\r\nif (!!tw.local.results && tw.local.results.listLength > 0 )\r\r\n\ttw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;\r\r\n\t\r\r\n\t ", "guid": "7f129082-979c-4efd-8c4d-3d105388fa47", "versionId": "0808f4b1-381e-4cd6-8f1c-08cfe2043058"}, "layoutData": {"x": "479", "y": "103", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.097f5a82-06c9-48af-8b7e-4e3b258e7310", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "699d2fd6-9d14-4bda-aed7-e38f077952dc", "versionId": "577ddef4-b96f-4495-b756-0c1d7052d81d", "parameterMapping": [{"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.dc115463-758a-496e-97d4-0946e73e3e97", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.097f5a82-06c9-48af-8b7e-4e3b258e7310", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "80e784f6-7c98-477e-bddc-d20ba67ffb94", "versionId": "2a8f2417-3d8e-4d32-8808-20f643a36674", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.aed10e43-3a87-4083-bb30-8514f34638cb", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.097f5a82-06c9-48af-8b7e-4e3b258e7310", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "286335f4-2ae5-434a-8dde-47b8e0f87818", "versionId": "6c04f7ac-421d-47e5-b0ce-04ffc026883c", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1569b70a-4aa7-4d43-b8a0-76170c2fe06c", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.097f5a82-06c9-48af-8b7e-4e3b258e7310", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "a6274008-05e9-4179-9a31-77c2f7282611", "versionId": "6e42360d-419b-4dd3-acf3-f191ce8d811c", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.77ff8740-be75-48a6-ac86-8a69ad11094e", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.097f5a82-06c9-48af-8b7e-4e3b258e7310", "useDefault": "false", "value": "1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "3652503a-bcd3-4832-a799-004921fd7b61", "versionId": "ab955b94-df41-4849-8a9f-d1401ceaee8a", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f0e9621c-03be-4109-a5ca-4491812e5681", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.097f5a82-06c9-48af-8b7e-4e3b258e7310", "useDefault": "false", "value": "tw.local.sqlParameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "6f654c19-ea28-4834-8498-946021db58e1", "versionId": "d28fdd3f-a2b0-4744-928a-2aa4840027c9", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Search for requestNo", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.1053282e-f91c-4068-904e-507c5b565eca", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1ce9", "versionId": "e7642cec-291b-49b4-9cf5-8b8dc1991a3b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.c8f96937-8411-4322-8eb0-aea0b3c74f5d", "processItemId": "2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "location": "1", "script": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START\");\r\r\n \r\r\n\r\r\n\r\r\n\r\r\n", "guid": "6c4937c5-a5cf-414f-ad15-a2564f6cac56", "versionId": "ff955c53-7652-4cec-aa6b-8fcfff9b6714"}, "layoutData": {"x": "354", "y": "103", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.1053282e-f91c-4068-904e-507c5b565eca", "scriptTypeId": "2", "isActive": "true", "script": "/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/***********************************************************************************/\r\r\n\ttw.local.sql=\"SELECT ID FROM \"+ tw.env.DBSchema + \".ODC_REQUESTINFO WHERE REQUESTNO = ? \"\r\r\n\t\t\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "e7699340-932a-4778-b668-abcc08ad906f", "versionId": "2bd2dcba-3a42-44c0-bc30-2e73bade0a9e"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.3b354799-bd42-4854-868d-e0629c388301", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Delete ODC Data", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.4836d075-adcf-469e-84da-0dae4932d63e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:-6531", "versionId": "eac4edc5-8337-4a40-bbe8-3baae3218d0b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.2049c474-fa37-4b83-8dc0-96437b8c942d", "processItemId": "2025.3b354799-bd42-4854-868d-e0629c388301", "location": "1", "script": "\r\r\n \r\r\n", "guid": "29ed20dd-45ea-4896-a674-6b677d254e79", "versionId": "51f46c03-d89a-4188-b082-de3ba8f34acb"}, "layoutData": {"x": "1080", "y": "173", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.4836d075-adcf-469e-84da-0dae4932d63e", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/********************************************************************************/\r\r\n\r\r\n/**-------------------------------------------------------------------------**/\r\r\n/**-------------------------- DELETE All ODC Data ---------------------------**/\r\r\n/**-------------------------------------------------------------------------**/\r\r\n\r\r\nvar sqlStatementLen= 0;\r\r\n/**************************************************************************************************************************************************************/\r\r\n/**------------------------------This function is used to delete multiple records from table using the requestId---------------------------------------------**/\r\r\n/**----------------------------------------------- tableName= the name of the table in the DB ---------------------------------------------------------------**/\r\r\n/**************************************************************************************************************************************************************/\r\r\nfunction DeleteRecordsByID(tableName){\r\r\n\t \r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" DELETE FROM \"+ tw.env.DBSchema + \".\"+ tableName + \" WHERE REQUESRID= ? \";\r\r\n\t\t\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n  \t \r\r\n}\r\r\n/**************************************************************************************************************************************************************/\r\r\n\r\r\n/**-------------------------- Delete Data from Tables by request id --------------------------**/\r\r\n\r\r\n\tDeleteRecordsByID (      \"ODC_Invoice\"      );\r\r\n\tDeleteRecordsByID (      \"ODC_Bills\"        );\r\r\n\tDeleteRecordsByID (   \"ODC_PartiesInfo\"     );\t\r\r\n\tDeleteRecordsByID ( \"ODC_CHARGESCOMMISSIONS\");\r\r\n\tDeleteRecordsByID (   \"ODC_FCTransaction\"   );\r\r\n\tDeleteRecordsByID ( \"ODC_MultiTenorDates\"   );\t\r\r\n\r\r\n ", "isRule": "false", "guid": "fab56f63-bcc6-4b52-ad09-38401ac4a476", "versionId": "10b68829-e85a-4d25-9665-68203b886d0d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.29ef6559-8a76-4830-84f5-e12eee5f40a1", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "name": "Get Request ID", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.6428baf7-9b9c-42bd-bef6-ef32b663ae36", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:2577", "versionId": "f2a648b9-54cb-4059-962d-8251be410e26", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1073", "y": "56", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.6428baf7-9b9c-42bd-bef6-ef32b663ae36", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.requestID= tw.local.results[0].rows[0].data[0];\r\r\n", "isRule": "false", "guid": "82b8715a-d611-425f-8ad5-8b885caaaa5a", "versionId": "c05c7e1f-b1fd-401f-a468-a343dc65da12"}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.cde99b1b-0da2-4f57-88a6-01e62e5061ec", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "guid": "8aeebfb7-dbe1-428c-a89d-1fc09d435ac3", "versionId": "27c78313-54b6-4131-bb0a-dd5cc3cf9327"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "99", "y": "126", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "1384", "y": "226", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Audit Create Data", "id": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "e9b2480e-1992-4de1-8261-e524f562a11d"}}}, "ns16:dataInput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.afad40c5-a38b-475c-8154-b4dabd94b6fe", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"create\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"1111222224444\";\r\nautoObject", "useDefault": "false"}}}, "ns16:dataOutput": [{"name": "errorMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.254cf8eb-2743-4c53-8c52-e51c8c22884e"}, {"name": "reqID", "itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "id": "2055.59dc474f-3e47-40ee-8737-ad21d25eb436"}, {"name": "sqlOut", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.0d27ff21-d378-41ac-801e-065cf08cc7a7"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.afad40c5-a38b-475c-8154-b4dabd94b6fe"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.254cf8eb-2743-4c53-8c52-e51c8c22884e", "2055.59dc474f-3e47-40ee-8737-ad21d25eb436", "2055.0d27ff21-d378-41ac-801e-065cf08cc7a7"]}}, "ns16:laneSet": {"id": "c0d17625-3a14-4a88-8ea7-d9e8d453b9c1", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "5e7ccb09-8e69-4a08-833a-ede6a123e618", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["081c9314-33e1-4935-8aa2-fd628f60e06c", "646302c3-7d88-4f2d-8840-dec30757da05", "1988ef7c-d47c-41ca-8f40-ab05373c7958", "6b290809-0a1a-487e-8fcf-899da748dc05", "6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "4f8c5959-0089-4977-8ac8-802b96f20d99", "fcc15744-4366-40c0-863f-44d3fbef78dd", "c32dfa16-a852-4585-896b-47642ffee8bd", "8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "c5082aee-343a-452e-8b33-35b492b4251b", "3b354799-bd42-4854-868d-e0629c388301", "548669f1-7641-4c40-8867-17f51c191b64", "29ef6559-8a76-4830-84f5-e12eee5f40a1", "ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "9f4eb6e8-ca0b-4b3f-8785-678607e11258", "*************-4299-8059-f22e8c1ef414", "17ed215d-500f-49f1-8c31-7bd7cfcd3e67", "2f5bf898-59ef-4548-80f2-bfecfcd3fc4b", "15dbe67a-a7c9-4140-8add-4e119dc15301", "4f33d610-6ca6-4031-8084-0666bcc26181", "a48629c7-9082-4589-8d93-8b735c1dcc82", "bf43fdb4-37f4-4208-80a8-16c178b90bd9"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "081c9314-33e1-4935-8aa2-fd628f60e06c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "99", "y": "126", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "1004085d-935b-4f0b-8b7d-00f0e577a11a"}, "ns16:endEvent": {"name": "End", "id": "646302c3-7d88-4f2d-8840-dec30757da05", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1555", "y": "79", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:3de5", "ns3:preAssignmentScript": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID +\" - ServiceName : AUDIT CREATE DATA : END\");\r\r\n\r\r\ntw.local.reqID= tw.local.requestID;"}, "ns16:incoming": ["4a775d57-f5a3-421f-89c6-8f0cb17b3efa", "cdaf90c4-7974-40d9-8c80-2cd09e52dc77"]}, "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Insert Request_info & Retrieve Request ID", "id": "1988ef7c-d47c-41ca-8f40-ab05373c7958", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "838", "y": "56", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START--Insert Path\");\r\r\n\r\r\n\r\r\n\r\r\n", "ns3:postAssignmentScript": ""}, "ns16:incoming": "c27dcbbc-8a95-4f89-8f2f-45efe37c2fad", "ns16:outgoing": "b3af8797-0b4e-411f-804d-fc11e6ac8cbb", "ns16:script": "\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/***********************************************************************************/\r\r\n\ttw.local.sql=\" SELECT ID FROM FINAL TABLE ( \"\r\r\n\t\t\t+ \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_RequestInfo ( \"\r\r\n               \t+ \" requestNo, requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,               \"  \r\r\n\t\t\t+ \" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \t\t\t \"  \r\r\n\t\t\t+ \" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \"  \r\r\n\t\t\t+ \" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \"  \r\r\n\t\t\t+ \" amountDefAvalization, collectableAmount, outstandingAmount, maturityDate, maturityDays, financeApprovalNo, hubCode,  \"  \r\r\n\t\t\t+ \" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \t\t\t\t\t  \t\t\t \"  \r\r\n\t\t\t+ \"   CBEClassification, shipmentMethod, shipmentDate, \t\t\t\t\t\t \t\t\t\t\t \"  \r\r\n\t\t\t+ \" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \t\t\t \"  \r\r\n\t\t\t+ \" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \t,ISLIQUIDATED,\t \" \r\r\n\t\t\t+\"  BPMInstanceNumber, currentStepName, substatus,\t\t\t\t\t\t\t\t\t\t\t \"\r\r\n\t\t\t+\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \"\r\r\n\t\t\t+ \" ) VALUES\" \r\r\n\t\t\t+ \" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\"\r\r\n\t\t\t+\",?,?,?,?,?,?,?)\"\r\r\n\t\t\t+ \" )\";\r\r\n\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\"0\")); \r\r\n \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \r\r\n  \r\r\n///\r\r\n\r\r\n//CHARGESACCOUNT\r\r\n//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \r\r\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \r\r\n\r\r\n\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Insert ODC Data", "id": "4f8c5959-0089-4977-8ac8-802b96f20d99", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1313", "y": "56", "width": "95", "height": "70"}, "ns3:postAssignmentScript": "", "ns3:preAssignmentScript": ""}, "ns16:incoming": ["f0ce710d-dacd-48c1-87a4-b97b7d2ef860", "3f77b7cb-8b99-48b5-844b-6fdfa82c5b42"], "ns16:outgoing": "aeebf34e-e506-4186-8d36-a9ad0378f4f6", "ns16:script": "\r\r\ntw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/***********************************************************************************/\r\r\n\r\r\n/**-----------------------------------------------------------------------**/\r\r\n/**-------------------------- Log All ODC Data ---------------------------**/\r\r\n/**-----------------------------------------------------------------------**/\r\r\n\r\r\nvar i = 0;\r\r\nvar sqlStatementLen= 0;\r\r\n\r\r\n/**-------------------------- Log Invoice Data --------------------------**/\r\r\n\r\r\nwhile (i < tw.local.odcRequest.BasicDetails.Invoice.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_Invoice ( \" \r\r\n\t\t\t\t\t\t\t\t  + \" requesrID, invoiceNo , invoiceDate\" \r\r\n\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList( tw.local.sqlStatements[sqlStatementLen].parameters.listLength , addSQLParameter(tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate));\r\r\n\t\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log Bills Data --------------------------**/\r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.BasicDetails.Bills.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_Bills ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, billOfLadingRef, billOfLadingDate\" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate));\r\r\n\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log Parties Data --------------------------**/\r\r\n\r\r\n/**-------------------------- Set SQL query to add party record --------------------------**/\r\r\nfunction AuditParty(requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address){\r\r\n\r\r\n\tif(partyId != null || partyId != \"\"){\r\r\n\t\t\r\r\n\t\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_PartiesInfo ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, partyId, partyType, cif, partyName,Country,Language,\" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  +\"  Reference, Address1, Address2, Address3, Media, Address\" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?,?,?,?,?,?,?) \";\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(requesrID));\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyId));\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyType));\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(cif));\t\t\r\r\n\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(partyName));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(country));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(language));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(reference));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address1));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address2));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address3));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(media));\t\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(address));\t\r\r\n\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\telse{\r\r\n\t\treturn false;\r\r\n\t}\r\r\n}\r\r\n//Params: requesrID, partyId , partyType, cif, partyName, country, language, reference, address1, address2, address3, media, address\r\r\n//Audit Drawer \r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawer.partyId , \"Drawer\", \"\" , tw.local.odcRequest.Parties.Drawer.partyName, \r\r\n tw.local.odcRequest.Parties.Drawer.country, tw.local.odcRequest.Parties.Drawer.Language, tw.local.odcRequest.Parties.Drawer.Reference,\r\r\n tw.local.odcRequest.Parties.Drawer.address1, tw.local.odcRequest.Parties.Drawer.address2, tw.local.odcRequest.Parties.Drawer.address3,\"\",\"\");\r\r\n\r\r\n//Audit Drawee\r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.Drawee.partyId , \"Drawee\", \"\", tw.local.odcRequest.Parties.Drawee.partyName,\r\r\ntw.local.odcRequest.Parties.Drawee.country, tw.local.odcRequest.Parties.Drawee.Language, tw.local.odcRequest.Parties.Drawee.Reference,\r\r\ntw.local.odcRequest.Parties.Drawee.address1, tw.local.odcRequest.Parties.Drawee.address2, tw.local.odcRequest.Parties.Drawee.address3,\"\",\"\" );\r\r\n\r\r\n//Audit collecting Bank\r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.collectingBank.id , \"collecting Bank\",tw.local.odcRequest.Parties.collectingBank.cif,\r\r\ntw.local.odcRequest.Parties.collectingBank.name, tw.local.odcRequest.Parties.collectingBank.country, tw.local.odcRequest.Parties.collectingBank.language,\r\r\ntw.local.odcRequest.Parties.collectingBank.reference, tw.local.odcRequest.Parties.collectingBank.address1, tw.local.odcRequest.Parties.collectingBank.address2,\r\r\ntw.local.odcRequest.Parties.collectingBank.address3, tw.local.odcRequest.Parties.collectingBank.media, tw.local.odcRequest.Parties.collectingBank.address);\r\r\n\r\r\n//Audit Accountee/ Case In Need\r\r\nAuditParty(tw.local.requestID ,tw.local.odcRequest.Parties.partyTypes.partyId, tw.local.odcRequest.Parties.partyTypes.partyType.value, tw.local.odcRequest.Parties.partyTypes.partyCIF,\r\r\ntw.local.odcRequest.Parties.partyTypes.partyName, tw.local.odcRequest.Parties.partyTypes.country, tw.local.odcRequest.Parties.partyTypes.language, tw.local.odcRequest.Parties.partyTypes.refrence,\r\r\ntw.local.odcRequest.Parties.partyTypes.address1, tw.local.odcRequest.Parties.partyTypes.address2, tw.local.odcRequest.Parties.partyTypes.address3, \"\", \"\");\r\r\n\r\r\n /**-------------------------- Log Charges And Commissions Data --------------------------**/\r\r\n\r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.ChargesAndCommissions.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_CHARGESCOMMISSIONS ( \" \t\t\t\t\t\t\t\t\t\t\t\t\t\t\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, defaultCurrency, defaultAmount, chargeAmount, waiver, accountClass,\"\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" accountNo, branchCode, currency, balance, balanceSign, standardExRate,\"   \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \"negotiatedExRate, debitedAmount, COMPONENT\"\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultCurrency.value));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].defaultAmount));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].changeAmount));\r\r\n\t\r\r\n\tif(tw.local.odcRequest.ChargesAndCommissions[i].waiver)\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\"1\"));\r\r\n\telse\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(\"0\"));\r\r\n\t\r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount)\r\r\n\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount ={};\r\r\n\t    \r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass)\r\r\n\t    tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass ={};\r\r\n\t    \r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value));\r\r\n\t\r\r\n    \r\r\n\tvar accountClass= tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value;\r\r\n\t\r\r\n\tif(accountClass == \"GL Account\"){\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo));\r\r\n\t}\r\r\n\t//Customer Account\r\r\n\telse{\r\r\n\t\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.customerAccountNo));\r\r\n\t}\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode));\r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency)\r\r\n\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency = {};\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balanceSign));\r\r\n\tif(!tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount)\r\r\n\t      tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount = {};\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.standardExRate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.ChargesAndCommissions[i].component));\r\r\n\t\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log FlexCube Transactions Data --------------------------**/ \r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.FcCollections.selectedTransactions.listLength) {\r\r\n\t\r\r\n\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n \ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_FCTransaction ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID,accountNo, postingDate, valueDate, transactionAmount, AMOUNTFORCURRENTREQUEST, TRANSACTIONREFNO \"\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?,?,?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].accountNo));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].postingDate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].valueDate));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].transactionAmount));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].amountAllocatedForCurrencyRequest));\r\r\n      tw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.selectedTransactions[i].referenceNumber));\r\r\n    i++;\r\r\n}\r\r\n /**-------------------------- Log Multi Tenor dates Data --------------------------**/\r\r\ni = 0;\r\r\n\r\r\nwhile (i < tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.listLength) {\r\r\n\t\r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\r\r\n \ttw.local.sqlStatements[sqlStatementLen].sql = \" INSERT INTO \"+ tw.env.DBSchema + \".ODC_MultiTenorDates ( \" \r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" requesrID, installmentDate, installmentAmount \"\t\r\r\n\t\t\t\t\t\t\t\t\t\t\t\t\t\t\t  + \" ) VALUES\" + \" (?,?,?)\";\r\r\n\t\t\t\r\r\n \ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date));\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount));\r\r\n\r\r\n    i++;\r\r\n}\r\r\n     "}, {"scriptFormat": "text/x-javascript", "name": "Test Data", "id": "c32dfa16-a852-4585-896b-47642ffee8bd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "207", "y": "187", "width": "95", "height": "70", "color": "#FFE14F"}, "ns3:preAssignmentScript": "log.info(\"ODC -- ProcessInstance : \"+33311 +\" - ServiceName : AUDIT CREATE DATA : START\");\r\r\n \r\r\n"}, "ns16:outgoing": "c9f833af-b3c3-453e-8b46-df827d4264eb", "ns16:script": " tw.local.odcRequest.appInfo.instanceID= \"33311\";\r\r\n tw.local.odcRequest.requestNo= \"12345678911111_03\";\r\r\n tw.local.odcRequest.requestNature.name= \"new\";\r\r\n tw.local.odcRequest.requestType.name = \"collect \"\r\r\n tw.local.odcRequest.requestDate= new TWDate();\r\r\n tw.local.odcRequest.BasicDetails.requestState=\"Final\";\r\r\n tw.local.odcRequest.appInfo.status=\"Completed\";\r\r\n tw.local.odcRequest.parentRequestNo=\"12345678911111\";\r\r\n tw.local.odcRequest.BasicDetails.flexCubeContractNo=\"11112222\";\r\r\n tw.local.odcRequest.BasicDetails.contractStage=\"stage3\";\r\r\n tw.local.odcRequest.BasicDetails.exportPurpose.name=\"exportP\";\r\r\n tw.local.odcRequest.BasicDetails.paymentTerms.name= \"PT\";\r\r\n tw.local.odcRequest.BasicDetails.productCategory.name= \"PCpc\";\r\r\n tw.local.odcRequest.BasicDetails.commodityDescription= \"CD11\";\r\r\n tw.local.odcRequest.CustomerInfo.cif= \"1234\";\r\r\n tw.local.odcRequest.CustomerInfo.customerName=\"smsma\";\r\r\n tw.local.odcRequest.ContractCreation.baseDate= new TWDate();\r\r\n tw.local.odcRequest.ContractCreation.valueDate = new TWDate();\r\r\n tw.local.odcRequest.ContractCreation.tenorDays= 3;\r\r\n tw.local.odcRequest.ContractCreation.transitDays= 4;\r\r\n tw.local.odcRequest.ContractCreation.maturityDate = new TWDate();\r\r\n tw.local.odcRequest.ContractCreation.userReference= \"1223231\";\r\r\n /**********************************************************************************/\r\r\n tw.local.odcRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[0] = new tw.object.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef = \"bill 03\";\r\r\n tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\r\n tw.local.odcRequest.BasicDetails.Bills[1] = new tw.object.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingRef = \"Bill 04\";\r\r\n tw.local.odcRequest.BasicDetails.Bills[1].billOfLadingDate = new TWDate();\r\r\n \r\r\n tw.local.odcRequest.BasicDetails.Bills[2] = new tw.object.Bills();\r\r\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingRef = \"Bill 05\";\r\r\n tw.local.odcRequest.BasicDetails.Bills[2].billOfLadingDate = new TWDate();\r\r\n \r\r\n tw.local.odcRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\r\n tw.local.odcRequest.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\r\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo = \"Inv 11\";\r\r\n tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\r\n// tw.local.odcRequest.BasicDetails.Invoice[1] = new tw.object.Invoice();\r\r\n// tw.local.odcRequest.BasicDetails.Invoice[1].invoiceNo = \"Inv 08\";\r\r\n//  tw.local.odcRequest.BasicDetails.Invoice[1].invoiceDate = new TWDate();\r\r\n \r\r\n /******************************************************************************************/\r\r\n\r\r\n tw.local.odcRequest.Parties = new tw.object.odcParties();\r\r\n tw.local.odcRequest.Parties.Drawer = new tw.object.Drawer();\r\r\n tw.local.odcRequest.Parties.Drawer.partyId = \"00000000\";\r\r\n tw.local.odcRequest.Parties.Drawer.partyName = \"Party01\";\r\r\n tw.local.odcRequest.Parties.Drawer.country = \"Egp\";\r\r\n tw.local.odcRequest.Parties.Drawer.Language = \"EN\";\r\r\n tw.local.odcRequest.Parties.Drawer.Reference = \"1234\";\r\r\n tw.local.odcRequest.Parties.Drawer.address1 = \"add1\";\r\r\n tw.local.odcRequest.Parties.Drawer.address2 = \"add2\";\r\r\n tw.local.odcRequest.Parties.Drawer.address3 = \"add3\";\r\r\n tw.local.odcRequest.Parties.Drawee = new tw.object.Drawee();\r\r\n tw.local.odcRequest.Parties.Drawee.partyId = \"********\";\r\r\n tw.local.odcRequest.Parties.Drawee.partyName = \"Party02\";\r\r\n tw.local.odcRequest.Parties.Drawee.country = \"EUR\";\r\r\n tw.local.odcRequest.Parties.Drawee.Language = \"arabic\";\r\r\n tw.local.odcRequest.Parties.Drawee.Reference = \"ref1\";\r\r\n tw.local.odcRequest.Parties.Drawee.address1 = \"address1\";\r\r\n tw.local.odcRequest.Parties.Drawee.address2 = \"address2\";\r\r\n tw.local.odcRequest.Parties.Drawee.address3 = \"address3\";\r\r\n tw.local.odcRequest.Parties.collectingBank = new tw.object.CollectingBank();\r\r\n tw.local.odcRequest.Parties.collectingBank.id = \"********\";\r\r\n tw.local.odcRequest.Parties.collectingBank.name = \"Party03\";\r\r\n tw.local.odcRequest.Parties.collectingBank.country = \"Egypt\";\r\r\n tw.local.odcRequest.Parties.collectingBank.language = \"french\";\r\r\n tw.local.odcRequest.Parties.collectingBank.reference = \"ref2\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address1 = \"address1\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address2 = \"address2\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address3 = \"address3\";\r\r\n tw.local.odcRequest.Parties.collectingBank.cif = \"********\";\r\r\n tw.local.odcRequest.Parties.collectingBank.media = \"swift\";\r\r\n tw.local.odcRequest.Parties.collectingBank.address = \"address\";\r\r\n tw.local.odcRequest.Parties.partyTypes = new tw.object.partyTypes();\r\r\n tw.local.odcRequest.Parties.partyTypes.partyCIF = \"********\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyId = \"********\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyName = \"Party04\";\r\r\n tw.local.odcRequest.Parties.partyTypes.country = \"italy\";\r\r\n tw.local.odcRequest.Parties.partyTypes.language = \"italian\";\r\r\n tw.local.odcRequest.Parties.partyTypes.refrence = \"reference0\";\r\r\n tw.local.odcRequest.Parties.partyTypes.address1 = \"add11\";\r\r\n tw.local.odcRequest.Parties.partyTypes.address2 = \"add22\";\r\r\n tw.local.odcRequest.Parties.partyTypes.address3 = \"add33\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.Parties.partyTypes.partyType.name = \"Case In Need\";\r\r\n tw.local.odcRequest.Parties.partyTypes.partyType.value = \"Case In Need\";\r\r\n// ------------------------------------------------------------------------------\r\r\n tw.local.odcRequest.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].component = \"component\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.name = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultCurrency.value = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].changeAmount = 11.0;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].waiver = false;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"GL Account\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"1111\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"111\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.branchCode = \"055\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.name = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.currency.value = \"EGP\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balance = 200;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.balanceSign = \"D\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAccount.isOverDraft = true;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.standardExRate = 990.0;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 990.0;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].debitedAmount.amountInAccount = 99.9;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].rateType = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].description = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultPercentage = 99.9;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].changePercentage = 99.9;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].defaultAmount = 99.1;\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\r\n tw.local.odcRequest.ChargesAndCommissions[0].flatAmount = 900.0;\r\r\n /******************************************************************************************/\r\r\n\r\r\n tw.local.odcRequest.FcCollections = new tw.object.FCCollections();\r\r\n tw.local.odcRequest.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.currency.name = \"\";\r\r\n tw.local.odcRequest.FcCollections.currency.value = \"Egypt\";\r\r\n tw.local.odcRequest.FcCollections.standardExchangeRate = 119.0;\r\r\n tw.local.odcRequest.FcCollections.negotiatedExchangeRate = 99.9;\r\r\n tw.local.odcRequest.FcCollections.fromDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.ToDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.accountNo.name = \"\";\r\r\n tw.local.odcRequest.FcCollections.accountNo.value = \"11999\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].accountNo = \"22222\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].referenceNumber = \"11199\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].transactionAmount = 111.0;\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].existAmount = 109.0;\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].currency = \"Egypt\";\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 190.0;\r\r\n tw.local.odcRequest.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency =99.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].accountNo = \"\";\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].currency = \"\";\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\r\n tw.local.odcRequest.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\r\n tw.local.odcRequest.FcCollections.isReversed = false;\r\r\n tw.local.odcRequest.FcCollections.usedAmount = 999.0;\r\r\n tw.local.odcRequest.FcCollections.calculatedAmount = 119.0;\r\r\n tw.local.odcRequest.FcCollections.totalAllocatedAmount = 999.9;\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0].name = \"11119\";\r\r\n tw.local.odcRequest.FcCollections.listOfAccounts[0].value = \"11119\";\r\r\n /******************************************************************************************/\r\r\n\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\r\n tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[0].amount = 100.0; \r\r\n\r\r\ntw.local.odcRequest.appInfo.instanceID= \"1234\";\r\r\ntw.local.odcRequest.appInfo.stepName=\"Create Act06\";\r\r\ntw.local.odcRequest.appInfo.subStatus=\"Completed\";\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Update Request Info", "id": "8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "832", "y": "173", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START--Update Path\");\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n", "ns3:postAssignmentScript": "tw.local.sqlOut= tw.local.sql;\r\r\ntw.local.reqID= tw.local.requestID;"}, "ns16:incoming": "55643b29-ffc3-4ee6-89ee-92865f61e092", "ns16:outgoing": "13e4407a-2000-4320-81ff-e1245b2f17b5", "ns16:script": "\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/*********************************************************************************************************************************************/\r\r\n\t\r\r\ntw.local.sql= \"UPDATE \"+ tw.env.DBSchema +  \".ODC_RequestInfo SET (\"\r\r\n\t\t+ \" requestNature ,requestType  ,requestDate , requestState  , requestStatus , parentRequestNo,         \t\t       \"  \r\r\n\t\t+ \" fcContractNo ,contractStage , exportPurpose ,paymentTerms ,productCategory, commodityDescription,  \t\t\t \"  \r\r\n\t\t+ \" cif, customerName, baseDate , valueDate, tenorDays, transitDays, contractMaturityDate, userRef, productCode,         \"  \r\r\n\t\t+ \" productDesc, stage, sourceRef, tradeFoRefNo, discount, extraCharges, ourCharges, amountSight,amountDefNoAvalization, \"  \r\r\n\t\t+ \" amountDefAvalization, collectableAmount,\"\r\r\n\t\t+ \"outstandingAmount, maturityDate, maturityDays,\"\r\r\n\t\t+ \"financeApprovalNo,\"\r\r\n\t\t+\"hubCode,  \"  \r\r\n\t\t+ \" hubName, collectionCurrency, standardExRate, negotiatedExRate,          \t\t\t\t\t  \t\t\t \"  \r\r\n\t\t+ \" CBEClassification, shipmentMethod, shipmentDate, \t\t\t\t\t\t \t\t\t\t\t\t \"  \r\r\n\t\t+ \" importerName, importerCountry, importerAddress, importerPhoneNo, importerBank, bicCode, ibanAccount, \t\t\t \"  \r\r\n\t\t+ \" bankCountry, bankAddress, bankPhoneNo, collectingBankRef, allocatedAmount, totalAllocatedAmount  \t,ISLIQUIDATED ,\t \" \r\r\n\t\t+ \" BPMInstanceNumber, currentStepName, substatus, \t\t\t\t\t\t\t\t\t\t\t \t\"\r\r\n\t\t+\"  TRANSACTIONREFNO, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT, COLLECTIONACCOUNT, CHARGESACCOUNT,COUNTRYOFORIGIN \"\r\r\n\t\t+ \" ) \"\r\r\n\t\t+\" =(?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?\"\r\r\n\t\t+\",?,?,?,?,?,?,?,?,?\"\r\r\n\t\t+\")\"\r\r\n\t\t+\" WHERE requestNo = ? \";\r\r\n\t\t\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNature.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestType.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.requestDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.requestState));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.status));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.parentRequestNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.flexCubeContractNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.contractStage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.exportPurpose.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.paymentTerms.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.productCategory.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.commodityDescription));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.cif));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.CustomerInfo.customerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.baseDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.valueDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.tenorDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.transitDays));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.userReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.productCode.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.Stage));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ContractCreation.sourceReference));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.discount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.extraCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.ourCharges));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountSight));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.collectableAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.outstandingAmount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.maturityDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.financeApprovalNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.executionHub.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.standardExchangeRate)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.negotiatedExchangeRate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.CBECommodityClassification.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shipmentMethod.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ProductShipmentDetails.shippingDate));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerName));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.importerPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bank));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.BICCode));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.ibanAccount));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankCountry.value));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankAddress));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.bankPhoneNo));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.ImporterDetails.collectingBankReference)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.calculatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FcCollections.totalAllocatedAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(\"0\")); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.system.currentProcessInstanceID)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.stepName)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.appInfo.subStatus)); \r\r\n//\r\r\n//TRANSACTIONREFNO MAXIMUMCOLLECTIONDATE REQUESTCURRENCY DOCUMENTAMOUNT ADVANCEAMOUNT COLLECTIONACCOUNT \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsFO.referenceNo)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate)); \r\r\n tw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.currency.name)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.documentAmount)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.amountAdvanced)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value)); \r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.BasicDetails.CountryOfOrigin.name));\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Delete ODC Data", "id": "3b354799-bd42-4854-868d-e0629c388301", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1080", "y": "173", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "\r\r\n \r\r\n"}, "ns16:incoming": "6878fe71-cf06-4406-817b-c4ec9fb17b03", "ns16:outgoing": "f4368575-7433-403f-8e26-387c7f93456b", "ns16:script": "tw.local.sqlStatements=  new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\r\n/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/********************************************************************************/\r\r\n\r\r\n/**-------------------------------------------------------------------------**/\r\r\n/**-------------------------- DELETE All ODC Data ---------------------------**/\r\r\n/**-------------------------------------------------------------------------**/\r\r\n\r\r\nvar sqlStatementLen= 0;\r\r\n/**************************************************************************************************************************************************************/\r\r\n/**------------------------------This function is used to delete multiple records from table using the requestId---------------------------------------------**/\r\r\n/**----------------------------------------------- tableName= the name of the table in the DB ---------------------------------------------------------------**/\r\r\n/**************************************************************************************************************************************************************/\r\r\nfunction DeleteRecordsByID(tableName){\r\r\n\t \r\r\n\tsqlStatementLen = tw.local.sqlStatements.listLength? tw.local.sqlStatements.listLength:0;\r\r\n\ttw.local.sqlStatements[sqlStatementLen]= new tw.object.toolkit.TWSYS.SQLStatement();\r\r\n\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].sql = \" DELETE FROM \"+ tw.env.DBSchema + \".\"+ tableName + \" WHERE REQUESRID= ? \";\r\r\n\t\t\t\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\r\n\ttw.local.sqlStatements[sqlStatementLen].parameters.insertIntoList(tw.local.sqlStatements[sqlStatementLen].parameters.listLength, addSQLParameter(tw.local.requestID));\r\r\n  \t \r\r\n}\r\r\n/**************************************************************************************************************************************************************/\r\r\n\r\r\n/**-------------------------- Delete Data from Tables by request id --------------------------**/\r\r\n\r\r\n\tDeleteRecordsByID (      \"ODC_Invoice\"      );\r\r\n\tDeleteRecordsByID (      \"ODC_Bills\"        );\r\r\n\tDeleteRecordsByID (   \"ODC_PartiesInfo\"     );\t\r\r\n\tDeleteRecordsByID ( \"ODC_CHARGESCOMMISSIONS\");\r\r\n\tDeleteRecordsByID (   \"ODC_FCTransaction\"   );\r\r\n\tDeleteRecordsByID ( \"ODC_MultiTenorDates\"   );\t\r\r\n\r\r\n "}, {"scriptFormat": "text/x-javascript", "name": "Get Request ID", "id": "29ef6559-8a76-4830-84f5-e12eee5f40a1", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1073", "y": "56", "width": "95", "height": "70"}}, "ns16:incoming": "4deb808a-8acb-44c0-818d-8f2dbea1d4cb", "ns16:outgoing": "3f77b7cb-8b99-48b5-844b-6fdfa82c5b42", "ns16:script": "tw.local.requestID= tw.local.results[0].rows[0].data[0];\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Search for requestNo", "id": "ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "354", "y": "103", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "log.info(\"ODC -- ProcessInstance : \"+tw.local.odcRequest.appInfo.instanceID  +\" - ServiceName : AUDIT CREATE DATA : START\");\r\r\n \r\r\n\r\r\n\r\r\n\r\r\n"}, "ns16:incoming": ["c9f833af-b3c3-453e-8b46-df827d4264eb", "f71d7316-fa39-41e3-8bd4-1dcd87381d2d"], "ns16:outgoing": "b85ff298-f81c-4996-807b-ca607cbe39e9", "ns16:script": "/**-------------------------- Add SQL Parameters --------------------------**/\r\r\nfunction addSQLParameter(value){\r\r\n\tif(value == null && typeof value == \"string\"){\r\r\n\t\tvalue= \"\"; \r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"number\"){\r\r\n\t\tvalue= 0;\r\r\n\t}\r\r\n\telse if(value == null && typeof value == \"boolean\"){\r\r\n\t\tvalue = \"0\";\r\r\n\t}\r\r\n var parameter = new tw.object.SQLParameter();  \r\r\n   parameter.value = value;\r\r\n   parameter.mode = \"IN\";\r\r\nreturn parameter;\r\r\n}\r\r\n/***********************************************************************************/\r\r\n\ttw.local.sql=\"SELECT ID FROM \"+ tw.env.DBSchema + \".ODC_REQUESTINFO WHERE REQUESTNO = ? \"\r\r\n\t\t\r\r\ntw.local.sqlParameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.sqlParameters.insertIntoList(tw.local.sqlParameters.listLength, addSQLParameter(tw.local.odcRequest.requestNo));\r\r\n\r\r\n\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "Init", "id": "2f5bf898-59ef-4548-80f2-bfecfcd3fc4b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "178", "y": "103", "width": "95", "height": "70"}}, "ns16:incoming": "1004085d-935b-4f0b-8b7d-00f0e577a11a", "ns16:outgoing": "f71d7316-fa39-41e3-8bd4-1dcd87381d2d", "ns16:script": "\r\r\nvar requestType= tw.epv.RequestType.Create+\"\";\r\r\nif (tw.local.odcRequest.requestType.value == requestType){\r\r\n\ttw.local.odcRequest.parentRequestNo= tw.local.odcRequest.requestNo;\r\r\n\ttw.local.odcRequest.BasicDetails.parentRequestNo= tw.local.odcRequest.requestNo;\r\r\n\t\r\r\n\t}\r\r\n\r\r\n"}], "ns16:sequenceFlow": [{"sourceRef": "1988ef7c-d47c-41ca-8f40-ab05373c7958", "targetRef": "6b290809-0a1a-487e-8fcf-899da748dc05", "name": "To SQL Execute Statement", "id": "b3af8797-0b4e-411f-804d-fc11e6ac8cbb", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "6b290809-0a1a-487e-8fcf-899da748dc05", "targetRef": "29ef6559-8a76-4830-84f5-e12eee5f40a1", "name": "To Get Request ID", "id": "4deb808a-8acb-44c0-818d-8f2dbea1d4cb", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "targetRef": "646302c3-7d88-4f2d-8840-dec30757da05", "name": "To End", "id": "4a775d57-f5a3-421f-89c6-8f0cb17b3efa", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "4f8c5959-0089-4977-8ac8-802b96f20d99", "targetRef": "6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "name": "To Insert ODC Request Execute SQL", "id": "aeebf34e-e506-4186-8d36-a9ad0378f4f6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fcc15744-4366-40c0-863f-44d3fbef78dd", "targetRef": "646302c3-7d88-4f2d-8840-dec30757da05", "name": "To End", "id": "cdaf90c4-7974-40d9-8c80-2cd09e52dc77", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "9f4eb6e8-ca0b-4b3f-8785-678607e11258", "targetRef": "*************-4299-8059-f22e8c1ef414", "name": "To update?", "id": "7197dad2-4b36-4cc6-8e17-52094b099c7c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "targetRef": "c5082aee-343a-452e-8b33-35b492b4251b", "name": "To SQL Update Request Info", "id": "13e4407a-2000-4320-81ff-e1245b2f17b5", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "c5082aee-343a-452e-8b33-35b492b4251b", "targetRef": "3b354799-bd42-4854-868d-e0629c388301", "name": "To Delete ODC Data", "id": "6878fe71-cf06-4406-817b-c4ec9fb17b03", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "3b354799-bd42-4854-868d-e0629c388301", "targetRef": "548669f1-7641-4c40-8867-17f51c191b64", "name": "To Execute SQL Statements", "id": "f4368575-7433-403f-8e26-387c7f93456b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "548669f1-7641-4c40-8867-17f51c191b64", "targetRef": "4f8c5959-0089-4977-8ac8-802b96f20d99", "name": "To Insert ODC Data", "id": "f0ce710d-dacd-48c1-87a4-b97b7d2ef860", "ns16:extensionElements": {"ns3:endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "29ef6559-8a76-4830-84f5-e12eee5f40a1", "targetRef": "4f8c5959-0089-4977-8ac8-802b96f20d99", "name": "To Insert Invoice Data", "id": "3f77b7cb-8b99-48b5-844b-6fdfa82c5b42", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "targetRef": "9f4eb6e8-ca0b-4b3f-8785-678607e11258", "name": "To Get ID of Request SQL Execute Statement", "id": "b85ff298-f81c-4996-807b-ca607cbe39e9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "*************-4299-8059-f22e8c1ef414", "targetRef": "1988ef7c-d47c-41ca-8f40-ab05373c7958", "name": "No", "id": "c27dcbbc-8a95-4f89-8f2f-45efe37c2fad", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "*************-4299-8059-f22e8c1ef414", "targetRef": "8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "name": "Yes", "id": "55643b29-ffc3-4ee6-89ee-92865f61e092", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.requestID\t  >\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "081c9314-33e1-4935-8aa2-fd628f60e06c", "targetRef": "2f5bf898-59ef-4548-80f2-bfecfcd3fc4b", "name": "To Init", "id": "1004085d-935b-4f0b-8b7d-00f0e577a11a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "c32dfa16-a852-4585-896b-47642ffee8bd", "targetRef": "ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "name": "To Search for requestNo", "id": "c9f833af-b3c3-453e-8b46-df827d4264eb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "17ed215d-500f-49f1-8c31-7bd7cfcd3e67", "targetRef": "fcc15744-4366-40c0-863f-44d3fbef78dd", "name": "To Exception Handling", "id": "fd084925-7d7f-49d7-8989-1e1357be3d46", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2f5bf898-59ef-4548-80f2-bfecfcd3fc4b", "targetRef": "ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "name": "To Exclusive Gateway", "id": "f71d7316-fa39-41e3-8bd4-1dcd87381d2d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a48629c7-9082-4589-8d93-8b735c1dcc82", "targetRef": "fcc15744-4366-40c0-863f-44d3fbef78dd", "name": "To Exception Handling", "id": "7919967e-0eb7-4dc7-804f-4b4f47f29532", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4f33d610-6ca6-4031-8084-0666bcc26181", "targetRef": "fcc15744-4366-40c0-863f-44d3fbef78dd", "name": "To Exception Handling", "id": "87a6fb28-a343-4a33-812e-e6eb0a6463f0", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "15dbe67a-a7c9-4140-8add-4e119dc15301", "targetRef": "fcc15744-4366-40c0-863f-44d3fbef78dd", "name": "To Exception Handling", "id": "6371f870-49c9-4241-87f2-2d5a5de4eb27", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "bf43fdb4-37f4-4208-80a8-16c178b90bd9", "targetRef": "fcc15744-4366-40c0-863f-44d3fbef78dd", "name": "To Exception Handling", "id": "ef61f92e-9713-4cb1-8aef-8065c9fa7428", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:callActivity": [{"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "SQL Execute Statement", "id": "6b290809-0a1a-487e-8fcf-899da748dc05", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "955", "y": "56", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:postAssignmentScript": ""}, "ns16:incoming": "b3af8797-0b4e-411f-804d-fc11e6ac8cbb", "ns16:outgoing": "4deb808a-8acb-44c0-818d-8f2dbea1d4cb", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Insert ODC Request Execute SQL", "id": "6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1427", "y": "56", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "aeebf34e-e506-4186-8d36-a9ad0378f4f6", "ns16:outgoing": "4a775d57-f5a3-421f-89c6-8f0cb17b3efa", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlStatements", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0"}}}, {"ns16:targetRef": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "ns16:assignment": {"ns16:from": {"_": "\"SQLResult\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Exception Handling", "id": "fcc15744-4366-40c0-863f-44d3fbef78dd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1458", "y": "203", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["fd084925-7d7f-49d7-8989-1e1357be3d46", "7919967e-0eb7-4dc7-804f-4b4f47f29532", "87a6fb28-a343-4a33-812e-e6eb0a6463f0", "6371f870-49c9-4241-87f2-2d5a5de4eb27", "ef61f92e-9713-4cb1-8aef-8065c9fa7428"], "ns16:outgoing": "cdaf90c4-7974-40d9-8c80-2cd09e52dc77", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Audit Create Data\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "SQL Update Request Info", "id": "c5082aee-343a-452e-8b33-35b492b4251b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "963", "y": "173", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "13e4407a-2000-4320-81ff-e1245b2f17b5", "ns16:outgoing": "6878fe71-cf06-4406-817b-c4ec9fb17b03", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.89ee1e72-5c6b-44ed-9e55-158a6cb613af", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Delete ODC Request Execute SQL", "id": "548669f1-7641-4c40-8867-17f51c191b64", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1190", "y": "173", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "f4368575-7433-403f-8e26-387c7f93456b", "ns16:outgoing": "f0ce710d-dacd-48c1-87a4-b97b7d2ef860", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.c007e1f5-b9cb-4b02-bf74-bc982112aade", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.c023a0b3-e6e5-47a6-a920-04d6b2f8dc26", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlStatements", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0"}}}, {"ns16:targetRef": "2055.628ceac6-aa42-426b-97c7-540674f12f38", "ns16:assignment": {"ns16:from": {"_": "\"SQLResult\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.24961b69-3b9b-4311-8707-6b6dfffaf207", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Get ID of Request SQL Execute Statement", "id": "9f4eb6e8-ca0b-4b3f-8785-678607e11258", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "479", "y": "103", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:postAssignmentScript": "\r\r\nif (!!tw.local.results && tw.local.results.listLength > 0 )\r\r\n\ttw.local.requestID= tw.local.results[0].rows.listLength? tw.local.results[0].rows[0].data[0]:0;\r\r\n\t\r\r\n\t "}, "ns16:incoming": "b85ff298-f81c-4996-807b-ca607cbe39e9", "ns16:outgoing": "7197dad2-4b36-4cc6-8e17-52094b099c7c", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.sqlParameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "sqlParameters", "id": "2056.c7095e57-5f5f-49e1-89d1-7aa304a64524", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\nautoObject[0].value = null;\r\nautoObject[0].type = \"\";\r\nautoObject[0].mode = \"\";\r\nautoObject", "useDefault": "false"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.116007eb-077a-4059-8f3b-1ff2f6837c3d"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.cb80769f-3387-4e8b-8b5e-82a6dfd69a8e"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "requestID", "id": "2056.63cfedee-d6e5-4a3c-8e47-d9d6176aaf3f", "ns16:extensionElements": {"ns3:defaultValue": {"_": "0", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.3990d8b4-983d-4250-bd06-3600f527fed0", "isCollection": "true", "name": "sqlStatements", "id": "2056.ce409362-d245-4476-8bac-bdafde3da38f", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();\r\nautoObject[0].sql = \"\";\r\nautoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\nautoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\nautoObject[0].parameters[0].value = null;\r\nautoObject[0].parameters[0].type = \"\";\r\nautoObject[0].parameters[0].mode = \"\";\r\nautoObject[0].maxRows = 0;\r\nautoObject", "useDefault": "false"}}}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "date", "id": "2056.f401dcad-832c-452f-823c-6fca66361cee", "ns16:extensionElements": {"ns3:defaultValue": {"_": "new TWDate()", "useDefault": "true"}}}], "ns16:exclusiveGateway": {"default": "c27dcbbc-8a95-4f89-8f2f-45efe37c2fad", "name": "update?", "id": "*************-4299-8059-f22e8c1ef414", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "682", "y": "122", "width": "32", "height": "32"}, "ns3:postAssignmentScript": ""}, "ns16:incoming": "7197dad2-4b36-4cc6-8e17-52094b099c7c", "ns16:outgoing": ["c27dcbbc-8a95-4f89-8f2f-45efe37c2fad", "55643b29-ffc3-4ee6-89ee-92865f61e092"]}, "ns16:intermediateCatchEvent": {"name": "Error Event", "id": "17ed215d-500f-49f1-8c31-7bd7cfcd3e67", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1384", "y": "226", "width": "24", "height": "24"}}, "ns16:outgoing": "fd084925-7d7f-49d7-8989-1e1357be3d46", "ns16:errorEventDefinition": {"id": "582948a9-cdc3-457a-84e4-8902e477907a", "eventImplId": "30d16f80-5b70-4f1f-8634-12dbbf5fd5c4", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "parallelMultiple": "false", "name": "Error", "id": "15dbe67a-a7c9-4140-8add-4e119dc15301", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1488", "y": "114", "width": "24", "height": "24"}}, "ns16:outgoing": "6371f870-49c9-4241-87f2-2d5a5de4eb27", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "1c246876-97df-422c-841d-f03f1aff1e32"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "794653c5-6b68-4d7c-81e9-7f67f750847b", "eventImplId": "6052246d-788e-4bdf-8abf-44441276f2a0", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "548669f1-7641-4c40-8867-17f51c191b64", "parallelMultiple": "false", "name": "Error1", "id": "4f33d610-6ca6-4031-8084-0666bcc26181", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1225", "y": "231", "width": "24", "height": "24"}}, "ns16:outgoing": "87a6fb28-a343-4a33-812e-e6eb0a6463f0", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "124edf7c-ca1a-4ac9-8c62-af467eeedfbe"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "70012514-bb4c-468c-89fb-4965e328da43", "eventImplId": "26862378-5ff7-4f34-8c29-6a0a3b1bd431", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "c5082aee-343a-452e-8b33-35b492b4251b", "parallelMultiple": "false", "name": "Error2", "id": "a48629c7-9082-4589-8d93-8b735c1dcc82", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "972", "y": "231", "width": "24", "height": "24"}}, "ns16:outgoing": "7919967e-0eb7-4dc7-804f-4b4f47f29532", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "49982f30-6256-48ef-82dc-48b29d350b1b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "513b40ec-e25c-43e4-8e50-a73ad4502c30", "eventImplId": "7cf5af0d-181d-4961-878f-f7a6f08ef2be", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "6b290809-0a1a-487e-8fcf-899da748dc05", "parallelMultiple": "false", "name": "Error3", "id": "bf43fdb4-37f4-4208-80a8-16c178b90bd9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "990", "y": "114", "width": "24", "height": "24"}}, "ns16:outgoing": "ef61f92e-9713-4cb1-8aef-8065c9fa7428", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "92c496b5-54cd-4f7d-8297-2bc9367cd880"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "6aed0f61-ac99-4f37-8726-1ba2504371d7", "eventImplId": "f042136e-6141-4382-89eb-4cf18cec5008", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4a775d57-f5a3-421f-89c6-8f0cb17b3efa", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81"], "endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb", "toProcessItemId": ["2025.646302c3-7d88-4f2d-8840-dec30757da05", "2025.646302c3-7d88-4f2d-8840-dec30757da05"], "guid": "26e7479c-4a48-4890-ad23-8beeeca631d4", "versionId": "641ea491-48df-4d22-b2fe-79d2db932b78", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To SQL Execute Statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.b3af8797-0b4e-411f-804d-fc11e6ac8cbb", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.1988ef7c-d47c-41ca-8f40-ab05373c7958", "2025.1988ef7c-d47c-41ca-8f40-ab05373c7958"], "endStateId": "Out", "toProcessItemId": ["2025.6b290809-0a1a-487e-8fcf-899da748dc05", "2025.6b290809-0a1a-487e-8fcf-899da748dc05"], "guid": "16ec94c4-9761-48ae-b7c5-da8a9abaa8ae", "versionId": "670bf9d6-7301-4f6b-82d0-0afa9f9b4339", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Insert ODC Data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f0ce710d-dacd-48c1-87a4-b97b7d2ef860", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.548669f1-7641-4c40-8867-17f51c191b64", "2025.548669f1-7641-4c40-8867-17f51c191b64"], "endStateId": "guid:a93dd992dce2d4cc:-381cc70d:115a5e00b05:-7fdb", "toProcessItemId": ["2025.4f8c5959-0089-4977-8ac8-802b96f20d99", "2025.4f8c5959-0089-4977-8ac8-802b96f20d99"], "guid": "76d94d4d-c194-4af1-96d8-dccaeb2cb105", "versionId": "6d22eb7d-34d7-4e1e-b1a9-3f885aca4285", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftBottom", "portType": "2"}}, {"name": "No", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.c27dcbbc-8a95-4f89-8f2f-45efe37c2fad", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.*************-4299-8059-f22e8c1ef414", "2025.*************-4299-8059-f22e8c1ef414"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.1988ef7c-d47c-41ca-8f40-ab05373c7958", "2025.1988ef7c-d47c-41ca-8f40-ab05373c7958"], "guid": "0fc35133-b84b-4cd1-a2e9-0505e3581cb7", "versionId": "73667a9e-12ca-42a5-a34d-0281196a7fcf", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To update?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7197dad2-4b36-4cc6-8e17-52094b099c7c", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258", "2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.*************-4299-8059-f22e8c1ef414", "2025.*************-4299-8059-f22e8c1ef414"], "guid": "ab04cb3a-ac4f-4124-98c3-cd824e7284f0", "versionId": "ac6adfbe-626b-4292-9e74-ee0a4098d15c", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Insert Invoice Data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.3f77b7cb-8b99-48b5-844b-6fdfa82c5b42", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.29ef6559-8a76-4830-84f5-e12eee5f40a1", "2025.29ef6559-8a76-4830-84f5-e12eee5f40a1"], "endStateId": "Out", "toProcessItemId": ["2025.4f8c5959-0089-4977-8ac8-802b96f20d99", "2025.4f8c5959-0089-4977-8ac8-802b96f20d99"], "guid": "478f9544-0afe-4d56-b356-5cffa560d76b", "versionId": "c6d5342f-78be-4b26-b43c-a5557338423d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Search for requestNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.c9f833af-b3c3-453e-8b46-df827d4264eb", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c32dfa16-a852-4585-896b-47642ffee8bd", "2025.c32dfa16-a852-4585-896b-47642ffee8bd"], "endStateId": "Out", "toProcessItemId": ["2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b"], "guid": "0fd27909-94e6-44b5-ba88-ddcfec313f1c", "versionId": "d4a337c8-08eb-42a6-bd90-a6e51eae15cf", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get Request ID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4deb808a-8acb-44c0-818d-8f2dbea1d4cb", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.6b290809-0a1a-487e-8fcf-899da748dc05", "2025.6b290809-0a1a-487e-8fcf-899da748dc05"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.29ef6559-8a76-4830-84f5-e12eee5f40a1", "2025.29ef6559-8a76-4830-84f5-e12eee5f40a1"], "guid": "0f461d4b-8199-45c6-8b70-d0745e56642b", "versionId": "db827bd7-71ba-4592-a403-279dea40349b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Insert ODC Request Execute SQL", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.aeebf34e-e506-4186-8d36-a9ad0378f4f6", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.4f8c5959-0089-4977-8ac8-802b96f20d99", "2025.4f8c5959-0089-4977-8ac8-802b96f20d99"], "endStateId": "Out", "toProcessItemId": ["2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81", "2025.6d3651c3-0746-4c2c-807b-a4f4d7ac6d81"], "guid": "32ae8671-07ff-4b33-b61a-c7b0f4bb5a7a", "versionId": "df98d776-6597-447e-866f-f4003c6c2468", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.cdaf90c4-7974-40d9-8c80-2cd09e52dc77", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.fcc15744-4366-40c0-863f-44d3fbef78dd", "2025.fcc15744-4366-40c0-863f-44d3fbef78dd"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.646302c3-7d88-4f2d-8840-dec30757da05", "2025.646302c3-7d88-4f2d-8840-dec30757da05"], "guid": "35ec832b-12cf-4232-8ac8-0667b160db45", "versionId": "e345ddb2-4da0-4cdd-b118-c7c1583399f5", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To Exclusive Gateway", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f71d7316-fa39-41e3-8bd4-1dcd87381d2d", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b", "2025.2f5bf898-59ef-4548-80f2-bfecfcd3fc4b"], "endStateId": "Out", "toProcessItemId": ["2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b"], "guid": "16950bed-7a56-4080-b562-aae160c3d16d", "versionId": "e6a42832-d155-44bd-aef4-f48b58e369e3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Execute SQL Statements", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f4368575-7433-403f-8e26-387c7f93456b", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.3b354799-bd42-4854-868d-e0629c388301", "2025.3b354799-bd42-4854-868d-e0629c388301"], "endStateId": "Out", "toProcessItemId": ["2025.548669f1-7641-4c40-8867-17f51c191b64", "2025.548669f1-7641-4c40-8867-17f51c191b64"], "guid": "f64fb201-0260-49b4-a87a-def10f01bf94", "versionId": "ec831c93-9db6-4cfc-a921-701e84d4a4b2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Delete ODC Data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.6878fe71-cf06-4406-817b-c4ec9fb17b03", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c5082aee-343a-452e-8b33-35b492b4251b", "2025.c5082aee-343a-452e-8b33-35b492b4251b"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.3b354799-bd42-4854-868d-e0629c388301", "2025.3b354799-bd42-4854-868d-e0629c388301"], "guid": "41fadb9e-fb13-401b-b541-ea6af8dab3a6", "versionId": "ed72cbba-90bd-4cf8-88ee-9077f59fa36b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.55643b29-ffc3-4ee6-89ee-92865f61e092", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.*************-4299-8059-f22e8c1ef414", "2025.*************-4299-8059-f22e8c1ef414"], "endStateId": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:33ea", "toProcessItemId": ["2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2"], "guid": "4400d639-f526-4380-94ad-ef5a45879fcb", "versionId": "f01a1154-2628-4ad2-be70-4b740113abd9", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get ID of Request SQL Execute Statement", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.b85ff298-f81c-4996-807b-ca607cbe39e9", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b", "2025.ec4e3ae8-5b80-40c3-8718-4361f96b6b4b"], "endStateId": "Out", "toProcessItemId": ["2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258", "2025.9f4eb6e8-ca0b-4b3f-8785-678607e11258"], "guid": "5777cd51-5211-4784-9b79-6a81aee85d52", "versionId": "f18818a9-646b-46f4-90a9-7cba759d6c7b", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To SQL Update Request Info", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.13e4407a-2000-4320-81ff-e1245b2f17b5", "processId": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2", "2025.8895b0f8-2ec0-49f3-8848-9ddc17cb68e2"], "endStateId": "Out", "toProcessItemId": ["2025.c5082aee-343a-452e-8b33-35b492b4251b", "2025.c5082aee-343a-452e-8b33-35b492b4251b"], "guid": "d6f960cc-2f2c-4e66-9251-f071a1a24b90", "versionId": "f7f9cdcc-e5e9-4c06-8fae-8f3208a713ba", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}