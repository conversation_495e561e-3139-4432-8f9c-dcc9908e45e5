<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8" name="Set document default properties 2">
        <lastModified>1700250894537</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.8f5b1d5b-266c-4378-8fad-952d8242b937</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
        <errorHandlerItemId>2025.789ca447-01f4-4eae-8c65-1feaabdbe24c</errorHandlerItemId>
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>be3818b9-98dc-499b-9147-a58ac16700b7</guid>
        <versionId>db4be141-e3c9-4dd3-97a2-997aa022479e</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:7bdc" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"artifact":[{"extensionElements":{"nodeVisualInfo":[{"width":288,"x":792,"y":53,"declaredType":"TNodeVisualInfo","height":171}]},"declaredType":"textAnnotation","text":{"content":["-\tCIF Number\n-\tRequest Reference Number\n-\tBPM Instance Id\n-\tDocument Type = \u201cODC Document\u201d\n-\tDocument Name\n"]},"id":"e4db4e10-4560-4019-8a83-bb3659366a4d","textFormat":"text\/plain"}],"flowElement":[{"parallelMultiple":false,"outgoing":["2027.ac07b981-3f89-40d8-886c-daba8cbf8a06"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"27653fd3-260b-41c1-b714-001c95294ec0"},{"incoming":["76ff2e03-a220-4324-bcdd-4b504130689e"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1986"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"0aedcc3d-a222-4fdc-a13c-571e355397d0"},{"targetRef":"8f5b1d5b-266c-4378-8fad-952d8242b937","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set ECM default properties","declaredType":"sequenceFlow","id":"2027.ac07b981-3f89-40d8-886c-daba8cbf8a06","sourceRef":"27653fd3-260b-41c1-b714-001c95294ec0"},{"startQuantity":1,"outgoing":["76ff2e03-a220-4324-bcdd-4b504130689e"],"incoming":["08d8c9bf-263f-432e-8c79-b725e99cf6e7"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":436,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set ECM default properties","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"4ad5b0ca-3520-4099-8d2e-6c8fab55f77d","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.attachmentDetails.ecmProperties.defaultProperties =new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nvar index= 0;\r\n\r\nfunction setECMProps(name, hidden, value , editable){\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].name = name;\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].hidden = hidden;\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].value = value;\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties[index].editable = editable;\r\n\tindex++;\r\n}\r\n\r\nsetECMProps(\"Customer_Number_CIF\", false, tw.local.cif, false);\r\nsetECMProps( \"BPMInstanceId\", false, tw.local.instanceNo, false);\r\nsetECMProps(\"DocumentType\", true, \"ODC Document\",false);\r\nsetECMProps(\"RequestReferenceNumber\", false,tw.local.requestNumber,false);\r\n\r\nsetECMProps(\"DocumentTitle\", true,\"\",false);\r\nsetECMProps(\"RecordInformation\", true,\"\",false);\r\nsetECMProps(\"CmFederatedLockStatus\", true,\"\",false);\r\n\r\n\/\/setECMProps(\"cmis:CmFederatedLockStatus\", false, \"Ts\",false);\r\n\/\/setECMProps(\"cmis:name\", false,\"S\",false);\r\n\/\/setECMProps(\"cmis:RecordInformation\", true,\"RI\",false);\r\n\/\/RecordInformation\r\n\r\n\r\n\r\n\r\n\r\n"]}},{"targetRef":"0aedcc3d-a222-4fdc-a13c-571e355397d0","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"76ff2e03-a220-4324-bcdd-4b504130689e","sourceRef":"4ad5b0ca-3520-4099-8d2e-6c8fab55f77d"},{"startQuantity":1,"outgoing":["08d8c9bf-263f-432e-8c79-b725e99cf6e7"],"incoming":["2027.ac07b981-3f89-40d8-886c-daba8cbf8a06"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":291,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set attchments","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8f5b1d5b-266c-4378-8fad-952d8242b937","scriptFormat":"text\/x-javascript","script":{"content":["var index= 0;\r\n\r\nif(!tw.local.attachmentDetails)\r\n  tw.local.attachmentDetails = {};\r\n\r\nif (tw.local.attachmentDetails.attachment == null || tw.local.attachmentDetails.attachment == undefined) {   \r\n\ttw.local.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\n\t\r\n\tsetProps(\"Customer Request\",  \"Customer Request\",  \"\u0637\u0644\u0628 \u0627\u0644\u0639\u0645\u064a\u0644\"); \r\n\tsetProps(\"BILL OF EXCHANGE\",  \"BILL OF EXCHANGE\",  \"\"); \r\n\tsetProps(\"Invoice\",  \"Invoice\",  \"\u0641\u0627\u062a\u0648\u0631\u0629\"); \r\n\tsetProps(\"BILL OF LADING\",  \"BILL OF LADING\",  \"\"); \t \t\t\r\n\tsetProps(\"AIRWAY BILL\", \"AIRWAY BILL\", \"\" ); \r\n\tsetProps(\"TRUCK CONSIGNMENT NOTE\",\"TRUCK CONSIGNMENT NOTE\", \"\"); \r\n\tsetProps(\"N\/N BILL OF LADING\",\"N\/N BILL OF LADING\",\"\"); \r\n\tsetProps(\"COURIER \/ POST RECEIPT\", \"COURIER \/ POST RECEIPT\" , \"\" ); \r\n\tsetProps(\"PACKING LIST\",\"PACKING LIST\",\"\"); \r\n\tsetProps(\"CERTIFICATE OF ORIGIN\",\"CERTIFICATE OF ORIGIN\", \"\" );\r\n\tsetProps(\"CERTIFICATE OF ANALYSIS\", \"CERTIFICATE OF ANALYSIS\", \"\");\r\n\tsetProps(\"INSURANCE POLICY \/ CERTIFICATE\", \"INSURANCE POLICY \/ CERTIFICATE\",\"\");\r\n\tsetProps(\"BENEFECIARY DECLARATION\", \"BENEFECIARY DECLARATION\", \"\");\r\n\tsetProps(\"NON RADIOACTIVE CERTIFICATE\", \"NON RADIOACTIVE CERTIFICATE\",\"\");\r\n\tsetProps(\"PHYTOSANITARY CERTIFICATE\", \"PHYTOSANITARY CERTIFICATE\",\"\");\r\n\tsetProps(\"CERTIFICATE OF ANALYSIS\",\"Bill of exchange\/draft\", \"\u0627\u0644\u0643\u0645\u0628\u064a\u0627\u0644\u0629\" );\r\n\tsetProps(\"HEALTH CERTIFICATE\",\"HEALTH CERTIFICATE\", \"\");\r\n\tsetProps(\"INSPECTION CERTIFICATE\", \"INSPECTION CERTIFICATE\",  \"\");\r\n\tsetProps(\"WARRANTY CERTIFICATE\", \"WARRANTY CERTIFICATE\",\"\");\r\n\tsetProps( \"TEST CERTIFICATE\",\"TEST CERTIFICATE\", \"\");\r\n\r\n\ttw.local.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\n\ttw.local.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\n\ttw.local.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\n\ttw.local.attachmentDetails.ecmProperties.fullPath = \"\";\r\n\t\t \r\n}\t\r\n\r\nfunction setProps(name, desc, arabicName){\t\r\n\ttw.local.attachmentDetails.attachment[index]= new tw.object.Attachment();\t\r\n\ttw.local.attachmentDetails.attachment[index].name= name;\r\n\ttw.local.attachmentDetails.attachment[index].description= desc;\r\n\ttw.local.attachmentDetails.attachment[index].arabicName= arabicName;\r\n\ttw.local.attachmentDetails.attachment[index].numOfOriginals= 0;\r\n\ttw.local.attachmentDetails.attachment[index].numOfCopies= 0;\r\n\tindex++;\t\r\n}\r\n\r\n"]}},{"targetRef":"4ad5b0ca-3520-4099-8d2e-6c8fab55f77d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set ECM default properties","declaredType":"sequenceFlow","id":"08d8c9bf-263f-432e-8c79-b725e99cf6e7","sourceRef":"8f5b1d5b-266c-4378-8fad-952d8242b937"},{"parallelMultiple":false,"outgoing":["8ccad284-0e45-496b-8a1e-84d12ae0399d"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"86cf04ff-de31-4f5d-8d6c-b62bc817feb6","otherAttributes":{"eventImplId":"ff25eef4-fb31-4598-88c7-3d79b71d29e9"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":307,"y":225,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error Event","declaredType":"intermediateCatchEvent","id":"508ac6be-42fd-434a-86d5-c949141a1dc0"},{"incoming":["eb50f183-97e9-4d0f-88fa-d9db3bb13b4a"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"b472843d-4929-4a4c-8a2d-3742e7b0632c","otherAttributes":{"eventImplId":"83f10afc-c9cf-4b5b-8906-d7a65c272219"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":495,"y":225,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}]}],"declaredType":"endEvent","id":"b02758ea-4c8a-4941-8c22-1f218dbafb5a"},{"targetRef":"789ca447-01f4-4eae-8c65-1feaabdbe24c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"8ccad284-0e45-496b-8a1e-84d12ae0399d","sourceRef":"508ac6be-42fd-434a-86d5-c949141a1dc0"},{"startQuantity":1,"outgoing":["eb50f183-97e9-4d0f-88fa-d9db3bb13b4a"],"incoming":["8ccad284-0e45-496b-8a1e-84d12ae0399d"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":383,"y":202,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exp Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Set document default properties 2\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"789ca447-01f4-4eae-8c65-1feaabdbe24c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.99eb514d-4b62-401e-8cdb-7edc096adffd"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"b02758ea-4c8a-4941-8c22-1f218dbafb5a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"eb50f183-97e9-4d0f-88fa-d9db3bb13b4a","sourceRef":"789ca447-01f4-4eae-8c65-1feaabdbe24c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.c2264df0-7fe3-410b-8614-1f97060e981f"}],"laneSet":[{"id":"3ad21738-fe63-4f52-a34a-51587745acb5","lane":[{"flowNodeRef":["27653fd3-260b-41c1-b714-001c95294ec0","0aedcc3d-a222-4fdc-a13c-571e355397d0","4ad5b0ca-3520-4099-8d2e-6c8fab55f77d","8f5b1d5b-266c-4378-8fad-952d8242b937","e4db4e10-4560-4019-8a83-bb3659366a4d","508ac6be-42fd-434a-86d5-c949141a1dc0","b02758ea-4c8a-4941-8c22-1f218dbafb5a","789ca447-01f4-4eae-8c65-1feaabdbe24c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"9b455e62-b9c8-4de5-b329-438b93b2a05e","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Set document default properties 2","declaredType":"process","id":"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9","name":"attachmentDetails","isCollection":false,"id":"2055.7d269650-ee48-4101-80db-2807cf921562"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.0261e8ad-a540-4682-88c5-87dff3eab23c"}],"inputSet":[{"dataInputRefs":["2055.399c7a58-00b5-4451-9813-41c0b9652088","2055.c28023fb-b45e-4b63-ae36-97e6df6421bc","2055.25394215-074f-4b79-8e84-9a96d32cc83b"]}],"outputSet":[{"dataOutputRefs":["2055.7d269650-ee48-4101-80db-2807cf921562","2055.0261e8ad-a540-4682-88c5-87dff3eab23c"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"11112222\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cif","isCollection":false,"id":"2055.399c7a58-00b5-4451-9813-41c0b9652088"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"12341234123412\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNumber","isCollection":false,"id":"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"1111\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceNo","isCollection":false,"id":"2055.25394215-074f-4b79-8e84-9a96d32cc83b"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="cif">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.399c7a58-00b5-4451-9813-41c0b9652088</processParameterId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>97af6472-e794-41cc-840f-4c2ab4f58209</guid>
            <versionId>b3804390-dfc8-4d68-92f8-741c075e7661</versionId>
        </processParameter>
        <processParameter name="requestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.c28023fb-b45e-4b63-ae36-97e6df6421bc</processParameterId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>450d2020-0c66-4bd8-ad2d-13bbedf02254</guid>
            <versionId>a0b5e622-f5bd-4de1-a298-b29d40d46320</versionId>
        </processParameter>
        <processParameter name="instanceNo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.25394215-074f-4b79-8e84-9a96d32cc83b</processParameterId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>c2fbbb58-77a1-4ff5-a44f-8a2a859f97c2</guid>
            <versionId>fbb80568-c637-47cd-953f-923b0a1aa020</versionId>
        </processParameter>
        <processParameter name="attachmentDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7d269650-ee48-4101-80db-2807cf921562</processParameterId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.55bb335a-d3b3-4749-a082-859e2a48ace9</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>873988dd-8fde-4ac0-8b76-6a3766d893c5</guid>
            <versionId>76fb229f-dca9-48d8-8238-37490d8117d8</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.0261e8ad-a540-4682-88c5-87dff3eab23c</processParameterId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>da415976-4267-4bf6-aa9a-0ee08eb67980</guid>
            <versionId>c1038d88-d1ff-4b40-b84e-3e00586eccf4</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.bae0e241-c1b0-4fd1-960a-e76ffa50f5c5</processParameterId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>e40f70f7-5ff1-4455-8459-6f28473b7cc9</guid>
            <versionId>b6a86379-8fde-4e84-92bc-920919bf63f7</versionId>
        </processParameter>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c2264df0-7fe3-410b-8614-1f97060e981f</processVariableId>
            <description isNull="true" />
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>08334a5d-7df7-4c55-9537-3669d0d2e4aa</guid>
            <versionId>c6cc25d7-b9ab-4d3d-af61-a0a3ad1546b9</versionId>
        </processVariable>
        <note>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLabelId>2028.e4db4e10-4560-4019-8a83-bb3659366a4d</processLabelId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <description>-	CIF Number
-	Request Reference Number
-	BPM Instance Id
-	Document Type = “ODC Document”
-	Document Name
</description>
            <data isNull="true" />
            <guid>3f3632ca-bb5e-4b9e-980b-caa71de7ab0e</guid>
            <versionId>c2da5da4-fc3c-4a49-8b51-550ea490a8c3</versionId>
            <layoutData x="792" y="53" width="288" height="171" />
        </note>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b02758ea-4c8a-4941-8c22-1f218dbafb5a</processItemId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.1c29a6ec-74ef-42cb-b780-fd8cef656e0c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:21b1</guid>
            <versionId>0c366343-176f-4c2a-b18c-c172d458230a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="495" y="225">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.1c29a6ec-74ef-42cb-b780-fd8cef656e0c</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>a5314d40-59c8-4467-9cdc-ce958e065381</guid>
                <versionId>b31ca2bd-6fa0-4c03-b4b8-7966d3bc9488</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2b22f3e1-a039-44c2-8a10-ad98899eb1fd</parameterMappingId>
                    <processParameterId>2055.bae0e241-c1b0-4fd1-960a-e76ffa50f5c5</processParameterId>
                    <parameterMappingParentId>3007.1c29a6ec-74ef-42cb-b780-fd8cef656e0c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>76ded3f9-1196-45d5-9b3a-d80d718e677b</guid>
                    <versionId>ab33ae7f-9b15-43d1-9942-b25ae64485f2</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8f5b1d5b-266c-4378-8fad-952d8242b937</processItemId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <name>Set attchments</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0783c095-c3a1-4f67-a5a6-35a9f894de9f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1eaa</guid>
            <versionId>1aeffcc9-d043-417b-bd47-9be31211bcb2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="291" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0783c095-c3a1-4f67-a5a6-35a9f894de9f</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var index= 0;&#xD;
&#xD;
if(!tw.local.attachmentDetails)&#xD;
  tw.local.attachmentDetails = {};&#xD;
&#xD;
if (tw.local.attachmentDetails.attachment == null || tw.local.attachmentDetails.attachment == undefined) {   &#xD;
	tw.local.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
	&#xD;
	setProps("Customer Request",  "Customer Request",  "طلب العميل"); &#xD;
	setProps("BILL OF EXCHANGE",  "BILL OF EXCHANGE",  ""); &#xD;
	setProps("Invoice",  "Invoice",  "فاتورة"); &#xD;
	setProps("BILL OF LADING",  "BILL OF LADING",  ""); 	 		&#xD;
	setProps("AIRWAY BILL", "AIRWAY BILL", "" ); &#xD;
	setProps("TRUCK CONSIGNMENT NOTE","TRUCK CONSIGNMENT NOTE", ""); &#xD;
	setProps("N/N BILL OF LADING","N/N BILL OF LADING",""); &#xD;
	setProps("COURIER / POST RECEIPT", "COURIER / POST RECEIPT" , "" ); &#xD;
	setProps("PACKING LIST","PACKING LIST",""); &#xD;
	setProps("CERTIFICATE OF ORIGIN","CERTIFICATE OF ORIGIN", "" );&#xD;
	setProps("CERTIFICATE OF ANALYSIS", "CERTIFICATE OF ANALYSIS", "");&#xD;
	setProps("INSURANCE POLICY / CERTIFICATE", "INSURANCE POLICY / CERTIFICATE","");&#xD;
	setProps("BENEFECIARY DECLARATION", "BENEFECIARY DECLARATION", "");&#xD;
	setProps("NON RADIOACTIVE CERTIFICATE", "NON RADIOACTIVE CERTIFICATE","");&#xD;
	setProps("PHYTOSANITARY CERTIFICATE", "PHYTOSANITARY CERTIFICATE","");&#xD;
	setProps("CERTIFICATE OF ANALYSIS","Bill of exchange/draft", "الكمبيالة" );&#xD;
	setProps("HEALTH CERTIFICATE","HEALTH CERTIFICATE", "");&#xD;
	setProps("INSPECTION CERTIFICATE", "INSPECTION CERTIFICATE",  "");&#xD;
	setProps("WARRANTY CERTIFICATE", "WARRANTY CERTIFICATE","");&#xD;
	setProps( "TEST CERTIFICATE","TEST CERTIFICATE", "");&#xD;
&#xD;
	tw.local.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
	tw.local.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
	tw.local.attachmentDetails.ecmProperties.fullPath = "";&#xD;
		 &#xD;
}	&#xD;
&#xD;
function setProps(name, desc, arabicName){	&#xD;
	tw.local.attachmentDetails.attachment[index]= new tw.object.Attachment();	&#xD;
	tw.local.attachmentDetails.attachment[index].name= name;&#xD;
	tw.local.attachmentDetails.attachment[index].description= desc;&#xD;
	tw.local.attachmentDetails.attachment[index].arabicName= arabicName;&#xD;
	tw.local.attachmentDetails.attachment[index].numOfOriginals= 0;&#xD;
	tw.local.attachmentDetails.attachment[index].numOfCopies= 0;&#xD;
	index++;	&#xD;
}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>1c46e760-1601-452a-a6e9-3c7307366dd6</guid>
                <versionId>8c5581c1-728d-425f-b37a-df1caf59fb1c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d</processItemId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <name>Set ECM default properties</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.bd7101c9-b1c1-452d-8811-bab63d6c1ad0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1985</guid>
            <versionId>a084a414-0865-4c03-8310-30cd361f5de7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="436" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.bd7101c9-b1c1-452d-8811-bab63d6c1ad0</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.attachmentDetails.ecmProperties.defaultProperties =new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
var index= 0;&#xD;
&#xD;
function setECMProps(name, hidden, value , editable){&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].name = name;&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].hidden = hidden;&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].value = value;&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].editable = editable;&#xD;
	index++;&#xD;
}&#xD;
&#xD;
setECMProps("Customer_Number_CIF", false, tw.local.cif, false);&#xD;
setECMProps( "BPMInstanceId", false, tw.local.instanceNo, false);&#xD;
setECMProps("DocumentType", true, "ODC Document",false);&#xD;
setECMProps("RequestReferenceNumber", false,tw.local.requestNumber,false);&#xD;
&#xD;
setECMProps("DocumentTitle", true,"",false);&#xD;
setECMProps("RecordInformation", true,"",false);&#xD;
setECMProps("CmFederatedLockStatus", true,"",false);&#xD;
&#xD;
//setECMProps("cmis:CmFederatedLockStatus", false, "Ts",false);&#xD;
//setECMProps("cmis:name", false,"S",false);&#xD;
//setECMProps("cmis:RecordInformation", true,"RI",false);&#xD;
//RecordInformation&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>c8157413-d4d1-4185-8052-73c69af6e8a3</guid>
                <versionId>dd6f1c64-403d-4d34-9363-1b873ccb1d90</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0aedcc3d-a222-4fdc-a13c-571e355397d0</processItemId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.3212471c-3aa5-422b-837f-ff6f0fbed697</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1986</guid>
            <versionId>a97774a7-ca12-4a7b-9389-7199a3615a5b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.3212471c-3aa5-422b-837f-ff6f0fbed697</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b947b996-c784-4e69-94e7-177defeb7207</guid>
                <versionId>e3e9b541-61dd-457b-9650-3f5ea88180ef</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.789ca447-01f4-4eae-8c65-1feaabdbe24c</processItemId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <name>Exp Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cde91268c0d1a44e:-10e36a59:18badede6df:-5d05</guid>
            <versionId>fe7bd252-e854-477a-abf6-1d70bbff903f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="383" y="202">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>7caaed83-a377-4667-bd3f-56a02f9cba2c</guid>
                <versionId>68a6f15d-1685-4d34-8f2e-5e0c1f9fb56b</versionId>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.77195f97-f7ff-4592-a6db-e5086fb97d9c</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>215ad725-eb37-4e3b-9657-ad43830c3cba</guid>
                    <versionId>b0e845db-a79f-453f-bbb8-b81d7bea4f9d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9479e0f2-c7ff-4b15-bfa8-3aa791a4b083</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Set document default properties 2"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e8f93aba-8ee4-4556-baa0-397e2477b2df</guid>
                    <versionId>ba7b91a3-4a7a-4d79-8914-31e07b4ee914</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a4229ae1-b2db-4b2a-bd03-fcaaf29474e7</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.7cedc1ed-3d0b-4963-8512-e0474ff47fac</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>78c04ed8-7ba4-42af-b296-94a83f644b2e</guid>
                    <versionId>dde9101b-8079-469d-a99b-a66f361ce125</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.8f5b1d5b-266c-4378-8fad-952d8242b937</startingProcessItemId>
        <errorHandlerItemId>2025.789ca447-01f4-4eae-8c65-1feaabdbe24c</errorHandlerItemId>
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <globalExceptionHandler>
            <layoutData x="307" y="225">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </globalExceptionHandler>
        <globalExceptionLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </globalExceptionLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Set document default properties 2" id="1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="cif" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.399c7a58-00b5-4451-9813-41c0b9652088">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"11112222"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="requestNumber" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.c28023fb-b45e-4b63-ae36-97e6df6421bc">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"12341234123412"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="instanceNo" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.25394215-074f-4b79-8e84-9a96d32cc83b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"1111"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="attachmentDetails" itemSubjectRef="itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9" isCollection="false" id="2055.7d269650-ee48-4101-80db-2807cf921562" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.0261e8ad-a540-4682-88c5-87dff3eab23c" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.399c7a58-00b5-4451-9813-41c0b9652088</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.c28023fb-b45e-4b63-ae36-97e6df6421bc</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.25394215-074f-4b79-8e84-9a96d32cc83b</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.7d269650-ee48-4101-80db-2807cf921562</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.0261e8ad-a540-4682-88c5-87dff3eab23c</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="3ad21738-fe63-4f52-a34a-51587745acb5">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="9b455e62-b9c8-4de5-b329-438b93b2a05e" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>27653fd3-260b-41c1-b714-001c95294ec0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0aedcc3d-a222-4fdc-a13c-571e355397d0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4ad5b0ca-3520-4099-8d2e-6c8fab55f77d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8f5b1d5b-266c-4378-8fad-952d8242b937</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e4db4e10-4560-4019-8a83-bb3659366a4d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>508ac6be-42fd-434a-86d5-c949141a1dc0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b02758ea-4c8a-4941-8c22-1f218dbafb5a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>789ca447-01f4-4eae-8c65-1feaabdbe24c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="27653fd3-260b-41c1-b714-001c95294ec0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.ac07b981-3f89-40d8-886c-daba8cbf8a06</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="0aedcc3d-a222-4fdc-a13c-571e355397d0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:1986</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>76ff2e03-a220-4324-bcdd-4b504130689e</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="27653fd3-260b-41c1-b714-001c95294ec0" targetRef="8f5b1d5b-266c-4378-8fad-952d8242b937" name="To Set ECM default properties" id="2027.ac07b981-3f89-40d8-886c-daba8cbf8a06">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set ECM default properties" id="4ad5b0ca-3520-4099-8d2e-6c8fab55f77d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="436" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>08d8c9bf-263f-432e-8c79-b725e99cf6e7</ns16:incoming>
                        
                        
                        <ns16:outgoing>76ff2e03-a220-4324-bcdd-4b504130689e</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.attachmentDetails.ecmProperties.defaultProperties =new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
var index= 0;&#xD;
&#xD;
function setECMProps(name, hidden, value , editable){&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].name = name;&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].hidden = hidden;&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].value = value;&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties[index].editable = editable;&#xD;
	index++;&#xD;
}&#xD;
&#xD;
setECMProps("Customer_Number_CIF", false, tw.local.cif, false);&#xD;
setECMProps( "BPMInstanceId", false, tw.local.instanceNo, false);&#xD;
setECMProps("DocumentType", true, "ODC Document",false);&#xD;
setECMProps("RequestReferenceNumber", false,tw.local.requestNumber,false);&#xD;
&#xD;
setECMProps("DocumentTitle", true,"",false);&#xD;
setECMProps("RecordInformation", true,"",false);&#xD;
setECMProps("CmFederatedLockStatus", true,"",false);&#xD;
&#xD;
//setECMProps("cmis:CmFederatedLockStatus", false, "Ts",false);&#xD;
//setECMProps("cmis:name", false,"S",false);&#xD;
//setECMProps("cmis:RecordInformation", true,"RI",false);&#xD;
//RecordInformation&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4ad5b0ca-3520-4099-8d2e-6c8fab55f77d" targetRef="0aedcc3d-a222-4fdc-a13c-571e355397d0" name="To End" id="76ff2e03-a220-4324-bcdd-4b504130689e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set attchments" id="8f5b1d5b-266c-4378-8fad-952d8242b937">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="291" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.ac07b981-3f89-40d8-886c-daba8cbf8a06</ns16:incoming>
                        
                        
                        <ns16:outgoing>08d8c9bf-263f-432e-8c79-b725e99cf6e7</ns16:outgoing>
                        
                        
                        <ns16:script>var index= 0;&#xD;
&#xD;
if(!tw.local.attachmentDetails)&#xD;
  tw.local.attachmentDetails = {};&#xD;
&#xD;
if (tw.local.attachmentDetails.attachment == null || tw.local.attachmentDetails.attachment == undefined) {   &#xD;
	tw.local.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
	&#xD;
	setProps("Customer Request",  "Customer Request",  "طلب العميل"); &#xD;
	setProps("BILL OF EXCHANGE",  "BILL OF EXCHANGE",  ""); &#xD;
	setProps("Invoice",  "Invoice",  "فاتورة"); &#xD;
	setProps("BILL OF LADING",  "BILL OF LADING",  ""); 	 		&#xD;
	setProps("AIRWAY BILL", "AIRWAY BILL", "" ); &#xD;
	setProps("TRUCK CONSIGNMENT NOTE","TRUCK CONSIGNMENT NOTE", ""); &#xD;
	setProps("N/N BILL OF LADING","N/N BILL OF LADING",""); &#xD;
	setProps("COURIER / POST RECEIPT", "COURIER / POST RECEIPT" , "" ); &#xD;
	setProps("PACKING LIST","PACKING LIST",""); &#xD;
	setProps("CERTIFICATE OF ORIGIN","CERTIFICATE OF ORIGIN", "" );&#xD;
	setProps("CERTIFICATE OF ANALYSIS", "CERTIFICATE OF ANALYSIS", "");&#xD;
	setProps("INSURANCE POLICY / CERTIFICATE", "INSURANCE POLICY / CERTIFICATE","");&#xD;
	setProps("BENEFECIARY DECLARATION", "BENEFECIARY DECLARATION", "");&#xD;
	setProps("NON RADIOACTIVE CERTIFICATE", "NON RADIOACTIVE CERTIFICATE","");&#xD;
	setProps("PHYTOSANITARY CERTIFICATE", "PHYTOSANITARY CERTIFICATE","");&#xD;
	setProps("CERTIFICATE OF ANALYSIS","Bill of exchange/draft", "الكمبيالة" );&#xD;
	setProps("HEALTH CERTIFICATE","HEALTH CERTIFICATE", "");&#xD;
	setProps("INSPECTION CERTIFICATE", "INSPECTION CERTIFICATE",  "");&#xD;
	setProps("WARRANTY CERTIFICATE", "WARRANTY CERTIFICATE","");&#xD;
	setProps( "TEST CERTIFICATE","TEST CERTIFICATE", "");&#xD;
&#xD;
	tw.local.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
	tw.local.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
	tw.local.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
	tw.local.attachmentDetails.ecmProperties.fullPath = "";&#xD;
		 &#xD;
}	&#xD;
&#xD;
function setProps(name, desc, arabicName){	&#xD;
	tw.local.attachmentDetails.attachment[index]= new tw.object.Attachment();	&#xD;
	tw.local.attachmentDetails.attachment[index].name= name;&#xD;
	tw.local.attachmentDetails.attachment[index].description= desc;&#xD;
	tw.local.attachmentDetails.attachment[index].arabicName= arabicName;&#xD;
	tw.local.attachmentDetails.attachment[index].numOfOriginals= 0;&#xD;
	tw.local.attachmentDetails.attachment[index].numOfCopies= 0;&#xD;
	index++;	&#xD;
}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="8f5b1d5b-266c-4378-8fad-952d8242b937" targetRef="4ad5b0ca-3520-4099-8d2e-6c8fab55f77d" name="To Set ECM default properties" id="08d8c9bf-263f-432e-8c79-b725e99cf6e7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:intermediateCatchEvent name="Error Event" id="508ac6be-42fd-434a-86d5-c949141a1dc0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="307" y="225" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>8ccad284-0e45-496b-8a1e-84d12ae0399d</ns16:outgoing>
                        
                        
                        <ns16:errorEventDefinition id="86cf04ff-de31-4f5d-8d6c-b62bc817feb6" eventImplId="ff25eef4-fb31-4598-88c7-3d79b71d29e9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:intermediateCatchEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="b02758ea-4c8a-4941-8c22-1f218dbafb5a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="495" y="225" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>eb50f183-97e9-4d0f-88fa-d9db3bb13b4a</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="b472843d-4929-4a4c-8a2d-3742e7b0632c" eventImplId="83f10afc-c9cf-4b5b-8906-d7a65c272219">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="508ac6be-42fd-434a-86d5-c949141a1dc0" targetRef="789ca447-01f4-4eae-8c65-1feaabdbe24c" name="To End Event" id="8ccad284-0e45-496b-8a1e-84d12ae0399d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exp Handling" id="789ca447-01f4-4eae-8c65-1feaabdbe24c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="383" y="202" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8ccad284-0e45-496b-8a1e-84d12ae0399d</ns16:incoming>
                        
                        
                        <ns16:outgoing>eb50f183-97e9-4d0f-88fa-d9db3bb13b4a</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Set document default properties 2"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="789ca447-01f4-4eae-8c65-1feaabdbe24c" targetRef="b02758ea-4c8a-4941-8c22-1f218dbafb5a" name="To End Event" id="eb50f183-97e9-4d0f-88fa-d9db3bb13b4a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.c2264df0-7fe3-410b-8614-1f97060e981f" />
                    
                    
                    <ns16:textAnnotation textFormat="text/plain" id="e4db4e10-4560-4019-8a83-bb3659366a4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="792" y="53" width="288" height="171" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:text>-	CIF Number
-	Request Reference Number
-	BPM Instance Id
-	Document Type = “ODC Document”
-	Document Name
</ns16:text>
                        
                    
                    </ns16:textAnnotation>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Set ECM default properties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.08d8c9bf-263f-432e-8c79-b725e99cf6e7</processLinkId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8f5b1d5b-266c-4378-8fad-952d8242b937</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d</toProcessItemId>
            <guid>025ec6bd-af09-405f-b3ed-eafb4da6c8a8</guid>
            <versionId>25fecff2-c580-471f-b23f-3e7c9904e0b4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8f5b1d5b-266c-4378-8fad-952d8242b937</fromProcessItemId>
            <toProcessItemId>2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.76ff2e03-a220-4324-bcdd-4b504130689e</processLinkId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.0aedcc3d-a222-4fdc-a13c-571e355397d0</toProcessItemId>
            <guid>39f292e9-4ab9-4447-8d9f-688b79eabe2a</guid>
            <versionId>655c9c46-d611-414a-914e-4b1841d105b4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4ad5b0ca-3520-4099-8d2e-6c8fab55f77d</fromProcessItemId>
            <toProcessItemId>2025.0aedcc3d-a222-4fdc-a13c-571e355397d0</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.eb50f183-97e9-4d0f-88fa-d9db3bb13b4a</processLinkId>
            <processId>1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.789ca447-01f4-4eae-8c65-1feaabdbe24c</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.b02758ea-4c8a-4941-8c22-1f218dbafb5a</toProcessItemId>
            <guid>dc7077d8-7053-42dc-9bf9-599dc38051d1</guid>
            <versionId>69146954-e971-4f77-b09d-aba8755670c9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.789ca447-01f4-4eae-8c65-1feaabdbe24c</fromProcessItemId>
            <toProcessItemId>2025.b02758ea-4c8a-4941-8c22-1f218dbafb5a</toProcessItemId>
        </link>
    </process>
</teamworks>

