<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b9bf1811-6e1e-41a3-828b-39059fa168cb" name="RACT01 - Create ODC Reversal Request">
        <lastModified>1734257645371</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.d1a0ca24-0850-4b9b-8620-03efb2ee694d</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:6f87</guid>
        <versionId>964de40b-0522-459f-a4fb-b63a9b125c1e</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e999d93f-6e84-402b-8712-e9005c217c6b"],"isInterrupting":true,"extensionElements":{"default":["2027.e999d93f-6e84-402b-8712-e9005c217c6b"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":-293,"y":202,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"5afd220f-e381-4d2b-8c87-ccd1eae49a85"},{"outgoing":["2027.ece40528-451e-4086-8c6a-138bd6e2e9ff","2027.a095f366-ba9c-4c32-8638-7078a5228473"],"incoming":["2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd","2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34","2027.e999d93f-6e84-402b-8712-e9005c217c6b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":417,"y":179,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":[]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"Reversal_Closure_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a63ebbf8-ee2f-467a-856a-aada47f5d119","optionName":"@label","value":"Reversal Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"38917cb4-8330-47f8-8d2d-7ead4979944e","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e883fe99-7d60-4db9-8433-2c034d7a2f48","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6f032046-b1f4-40ee-8cbc-3ee4451d5ab0","optionName":"closureReasonVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"64a80a11-753f-420e-8cf1-a29a8868b343","optionName":"executionHubVIS","value":"None"}],"viewUUID":"64.f0c268ac-0772-4735-af5b-5fc6caec30a1","binding":"tw.local.odcRequest.ReversalReason","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"8a919c88-3b6d-4e5a-8f10-333d7550d8c2","version":"8550"},{"layoutItemId":"Basic_Details_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a32d87cc-7304-46ed-8245-57e4f0dc7b43","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"744cc34d-de0a-40b5-81d7-5d975c5a8dc7","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"aef23b26-10c3-45c4-89c8-5f0fbd449128","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"882d4dea-aee7-4684-8bb8-fc1af9f190f9","optionName":"parentRequestNoVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"73a8dee7-38bb-4886-867b-53baf139b4bf","optionName":"basicDetailsCVVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"20f0e69e-a763-4659-89e8-93bcf1aabe1e","optionName":"multiTenorDatesVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"09e92959-052f-4c9f-8f68-72275df8248f","optionName":"contractStageVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5512c82f-86af-491c-8678-38cbb6731b0d","optionName":"flexCubeContractNoVIS","value":"None"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"66d71ecf-f6c3-4b6d-83cd-50e7a6fd2d67","version":"8550"},{"layoutItemId":"Customer_Information_cv1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f45c3961-ad4f-4807-8a32-3ec6091f833c","optionName":"@label","value":"Customer Info"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"523052c2-88ce-41a7-8b49-374376a0730a","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cc6bd183-30b9-4d14-801e-1819bb4c16f7","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4be83902-42cf-4f8d-84ed-849bc3f31d8b","optionName":"customerInfoVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"90d63cc3-26c4-4383-81cf-a64fef361ab0","optionName":"listsVIS","value":"Readonly"}],"viewUUID":"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c","binding":"tw.local.odcRequest.CustomerInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"2929e554-3b27-40f4-8e02-06185ade20ff","version":"8550"},{"layoutItemId":"Financial_Details_Branch1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b653bace-8945-4d48-8af5-d88baecaeeba","optionName":"@label","value":"Financial Details Branch"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9578b282-6d07-4aad-81b8-1073576eedf7","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fe5c20d1-df3a-4aec-8f56-dadc0b90a537","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e6638804-9365-40b2-8b46-055c79a7d063","optionName":"financialDetailsCVVis","value":"READONLY"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d193aa73-86ee-43e5-85af-2885f66292ca","optionName":"currencyDocAmountVIS","value":"READONLY"}],"viewUUID":"64.4992aee7-c679-4a00-9de8-49a633cb5edd","binding":"tw.local.odcRequest.FinancialDetailsBR","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"fd7fd0ad-7c61-42b5-8a7d-fb969b07b95a","version":"8550"},{"layoutItemId":"FC_Collections_CV1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"194657fc-5498-4a18-8798-75b486f0cc8b","optionName":"@label","value":"FlexCube Collections"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"49e1deb1-3edf-4be3-80ed-346fa062f3bc","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"57a5af6e-f718-4001-8163-4fc334b67105","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"05597451-9a57-4215-8ba6-bea9d0791703","optionName":"FCVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7e361ecb-c133-48de-8854-3bce69da81e1","optionName":"retrieveBtnVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"17cbc24c-684b-422c-8338-2f59134de550","optionName":"addBtnVIS","value":"None"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"284f24d7-ea29-4a6c-83ce-4603e2b67b96","optionName":"customerCif","value":"tw.local.odcRequest.cif"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9a0daaaa-de99-4790-89ff-d21a36fbf143","optionName":"collectionCurrencyVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d153255c-ce9f-4a52-8302-b1792cf34482","optionName":"negotiatedExchangeRateVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"73da2daa-9670-4dcc-8385-d5ac80032c4b","optionName":"requestCurrency","value":"Readonly"}],"viewUUID":"64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe","binding":"tw.local.odcRequest.FcCollections","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"32780090-57af-4d46-86ba-d58641ad2161","version":"8550"},{"layoutItemId":"Attachment1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8aca6b81-aff5-49c9-8d97-b157a7572167","optionName":"@label","value":"Attachment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3331b593-c2fc-42ad-8dca-fb187b1ecd88","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"188aca3c-d61b-456b-858a-c8f622ab5882","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"09aff001-0eac-41e4-851a-ec44d51a53fd","optionName":"ECMProperties","value":"tw.local.odcRequest.attachmentDetails.ecmProperties"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1f175e19-eb9b-4fda-8d53-6af3225b7889","optionName":"canUpdate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cc9d78c7-c485-4c54-8875-aee0f7574897","optionName":"canCreate","value":"true"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8c6cd406-83a4-41f1-8aea-47ca0c0386ca","optionName":"canDelete","value":"true"}],"viewUUID":"64.797f9d29-e666-4d5c-ba4b-cf4866173643","binding":"tw.local.odcRequest.attachmentDetails.attachment[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c574be62-c1f8-4531-8bcd-5b1ec18a5117","version":"8550"},{"layoutItemId":"DC_History1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"55f45747-717e-44b6-860c-57655f915771","optionName":"@label","value":"History"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"926a9cb7-c7a6-4614-8587-a0e06ec0d319","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"43908811-33b6-4a3d-8fa7-6c5fd727e376","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"static","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cdb5b00a-7e25-4d09-8d6f-0abdc560f0ac","optionName":"@visibility.script","value":"{\"@class\":\"com.ibm.bpm.coachNG.visibility.VisibilityRules\",\"rules\":[{\"@class\":\"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule\",\"var_conditions\":[{\"timeInMs\":null,\"var\":\"tw.local.isFirstTime\",\"operand\":true,\"operator\":0}],\"action\":\"NONE\"}],\"defaultAction\":\"DEFAULT\"}"}],"viewUUID":"64.5df8245e-3f18-41b6-8394-548397e4652f","binding":"tw.local.odcRequest.History[]","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"10d75adb-e3e1-4e95-89f2-1ef57fa8fe2b","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"897bbaa0-480b-4d18-8ba0-11b513d53480"}],"layoutItemId":"Tab_Section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e940a729-0e97-4440-8937-849d7d7f95b5","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"22765fec-c86c-492e-89ce-1758bf79e4bc","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"964876a1-39dc-4221-83a3-c8b86f1b5f4a","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b6a33ae-449c-4784-83cf-d5981e28ed32","optionName":"tabsStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"54a61e78-d123-41b5-8932-b039562ca63c","optionName":"colorStyle","value":"P"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fd135dab-01dd-42fb-85d3-06d267859fe7","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"aa54988c-85d2-48cd-8a40-752bc789fff0","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"85220433-a459-429c-827b-83e60051b4ec"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"60e15862-1473-4ac8-854b-5523230b81a4","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"75bd7767-40f0-442c-8173-816837287ded","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"72b7b189-ce2c-4b17-8f76-7627af26999c","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a300aee0-6ed4-48d5-82b1-bcde25b30fd3","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"850db7db-3179-4b17-834e-e22ec88dc8b5","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5b4ec826-b9a1-40dc-8ce4-e03c9c7051b2","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9e310dc9-3f16-4feb-8a76-bfd60e24c8f6","optionName":"terminateReasonVIS","value":"None"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cb6c3b47-2548-4b44-8f51-49824df6b08f","optionName":"actionConditions","value":"tw.local.actionConditions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6307252b-d065-4794-8208-1ae26f9cc0fb","optionName":"complianceApprovalVis","value":"None"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6e450104-cf1a-429a-8677-454e376fa9fc","optionName":"errorMsg","value":"tw.local.errorMessage"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c0d05fe5-fd94-4501-8a38-7f2e71ef864c","optionName":"complianceApproval","value":"false"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8a8358e9-ddf4-4d0b-8adc-d5bb2ea254f3","optionName":"returnReasonVIS","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a06d78ae-8be9-40d3-8000-649789b722c4","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b1a865b9-ceb1-4bcb-822f-41fa529d883c","optionName":"tradeFoCommentVis","value":"None"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cfdd063e-7447-47d2-862b-031bee67a158","optionName":"exeHubMkrCommentVis","value":"None"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"cb4e8015-07b4-488f-8125-6f1b10de48ac","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Create Reversal Request","isForCompensation":false,"completionQuantity":1,"id":"2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f"},{"incoming":["2027.95e79ac2-ae3a-4006-8abc-cc22d8fdb8b8"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1229,"y":195,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"bae6712f-5851-4002-bb0e-c2d82b0497b8"},{"startQuantity":1,"outgoing":["2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5"],"incoming":["2027.511f8071-2377-4b8a-8d9f-bf7f66719f11"],"default":"2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5","extensionElements":{"nodeVisualInfo":[{"width":95,"x":-72,"y":179,"declaredType":"TNodeVisualInfo","height":70}]},"name":"set request header script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.83b139d0-c3dd-4e0d-802c-bfe0ece0773e","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;\r\ntw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name;\r\n\r\nif(tw.local.isFirstTime == true)\r\n{\r\n\r\n\ttw.local.odcRequest.appInfo.status =\"Initiated\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Initiated\";\r\n\ttw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.RACT01;\r\n\ttw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;\r\n\t\r\n\ttw.local.odcRequest.BasicDetails.requestState = \"Undergoing Reversal\";\r\n\t\r\n\ttw.local.odcRequest.appInfo.requestName = \"ODC Reversal\";\r\n\r\ntw.local.actionConditions = {};\r\ntw.local.actionConditions.complianceApproval= false;\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.RACT01;\t\r\n\t\r\ntw.local.odcRequest.stepLog ={};\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.RACT01;\r\n\r\nif(!tw.local.odcRequest.ReversalReason) \r\n   tw.local.odcRequest.ReversalReason = {};\r\n\r\ntw.local.odcRequest.History = [];\r\n\r\n}\r\nelse\r\n{\r\n\r\n\ttw.local.odcRequest.stepLog ={};\r\n\ttw.local.odcRequest.stepLog.startTime = new Date();\r\n\ttw.local.odcRequest.stepLog.step =  tw.epv.ScreenNames.RACT01;\r\n\ttw.local.actionConditions = {};\r\n\ttw.local.actionConditions.complianceApproval= false;\r\n\ttw.local.actionConditions.screenName= tw.epv.ScreenNames.RACT01;\r\n\r\n\ttw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.RACT01;\r\n}"]}},{"targetRef":"2025.a47e2771-dc05-45f2-836e-53b67b576c08","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Reversal Request","declaredType":"sequenceFlow","id":"2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5","sourceRef":"2025.83b139d0-c3dd-4e0d-802c-bfe0ece0773e"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.59d51a05-8a58-4fc6-8991-fcf4d9e04df4"},{"startQuantity":1,"outgoing":["2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703"],"incoming":["2027.ece40528-451e-4086-8c6a-138bd6e2e9ff"],"default":"2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703","extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":589,"y":182,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.0f0034ec-ba75-4d37-8150-2a080b80bc01","scriptFormat":"text\/x-javascript","script":{"content":[" tw.local.errorMessage =\"\";\r\n var mandatoryTriggered = false;\r\n \r\n\/************************************************************************************************************************\r\n\/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\n************************************************************************************************************************\/\r\n\/\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\n\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)\r\n{\r\n\tmandatory(tw.local.odcRequest.ReversalReason.reversalReason, \"tw.local.odcRequest.ReversalReason.reversalReason\");\r\n\tmaxLength(tw.local.odcRequest.ReversalReason.reversalReason , \"tw.local.odcRequest.ReversalReason.reversalReason\", 160 , \"Shouldn't be more than 160 character\" , \"Reversal Reason: \" + \"Shouldn't be more than 160 character\" );\r\n\r\n}\t\r\n\t\r\n\r\nfunction mandatory(field , fieldName)\r\n{\r\n\tif (field == null || field == undefined )\r\n\t{\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\telse\r\n\t{\t\t\t\r\n\t\tswitch (typeof field)\r\n\t\t{\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0)\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\r\n\t\t\tdefault:\r\n\t\t\t\t\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}\r\n\t\t\t\t\r\n\t\t\t\telse\r\n\t\t\t\t{\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\t\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\n{\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered ? \"\" : tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\";\r\n}\r\nfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\n{\r\n\tif (field.length &gt; len)\r\n\t{\r\n\t\taddError(fieldName , controlMessage , validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\t"]}},{"outgoing":["2027.2308babf-3d77-459e-8cd4-45e5873400ad","2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae"],"incoming":["2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703"],"default":"2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":766,"y":201,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"2025.4e28c43d-1487-40d1-83de-47725c1e6556"},{"targetRef":"2025.4e28c43d-1487-40d1-83de-47725c1e6556","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703","sourceRef":"2025.0f0034ec-ba75-4d37-8150-2a080b80bc01"},{"targetRef":"2025.823cd073-97dc-4d59-8b3f-953d2a3f8a6d","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.2308babf-3d77-459e-8cd4-45e5873400ad","sourceRef":"2025.4e28c43d-1487-40d1-83de-47725c1e6556"},{"incoming":["2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":754,"y":117,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.b8bad59c-1757-42dc-8e94-2ea24d390349"},{"targetRef":"2025.b8bad59c-1757-42dc-8e94-2ea24d390349","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae","sourceRef":"2025.4e28c43d-1487-40d1-83de-47725c1e6556"},{"targetRef":"2025.0f0034ec-ba75-4d37-8150-2a080b80bc01","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"566ad7d9-68fb-47d0-8dc9-b22634e4ece3","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Validation Script","declaredType":"sequenceFlow","id":"2027.ece40528-451e-4086-8c6a-138bd6e2e9ff","sourceRef":"2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f"},{"startQuantity":1,"outgoing":["2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e"],"incoming":["2027.2308babf-3d77-459e-8cd4-45e5873400ad"],"default":"2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e","extensionElements":{"nodeVisualInfo":[{"width":95,"x":874,"y":173,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Setting status and substatus","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.823cd073-97dc-4d59-8b3f-953d2a3f8a6d","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status =\"Initiated\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending Review\";\r\n\ttw.local.lastAction = \"\";\r\n}\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)\r\n{\r\n\ttw.local.odcRequest.appInfo.status = \"Initiated\";\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Pending cancellation confirmation\";\r\n\ttw.local.lastAction = tw.epv.CreationActions.cancelRequest;\r\n}"]}},{"outgoing":["2027.93485cc9-6250-4dc7-84c7-0a20991fd69b"],"incoming":["2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1010,"y":173,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.93485cc9-6250-4dc7-84c7-0a20991fd69b","name":"History","dataInputAssociation":[{"targetRef":"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.ba547af3-d09b-4196-816b-653732f6b226","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.epv.userRole.RevAct01"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.610f7e3a-c3f1-4b8f-8969-3317a8808484","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.416d990b-a0aa-4323-8df7-1f58b014c2ba"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.a617c560-c740-484e-89de-0931088cdc6c"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.5640d61f-8ab9-4476-8a3b-cf6d102461e1"]}],"calledElement":"1.e8e61c7a-dc6d-441e-b350-b583581efb21"},{"targetRef":"2025.610f7e3a-c3f1-4b8f-8969-3317a8808484","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To History","declaredType":"sequenceFlow","id":"2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e","sourceRef":"2025.823cd073-97dc-4d59-8b3f-953d2a3f8a6d"},{"targetRef":"2025.43718c88-58a4-4bcb-8c12-bbaf38f39358","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Audited?","declaredType":"sequenceFlow","id":"2027.93485cc9-6250-4dc7-84c7-0a20991fd69b","sourceRef":"2025.610f7e3a-c3f1-4b8f-8969-3317a8808484"},{"outgoing":["2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd"],"incoming":["2027.a095f366-ba9c-4c32-8638-7078a5228473"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd"],"nodeVisualInfo":[{"width":24,"x":425,"y":77,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 1","declaredType":"intermediateThrowEvent","id":"2025.42f7dd95-21b7-4c57-8279-5d823b453b71"},{"targetRef":"2025.42f7dd95-21b7-4c57-8279-5d823b453b71","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"ce627a9a-9403-43fe-807b-f3d4c1455dca","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topRight","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page 1","declaredType":"sequenceFlow","id":"2027.a095f366-ba9c-4c32-8638-7078a5228473","sourceRef":"2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f"},{"targetRef":"2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Reversal Request","declaredType":"sequenceFlow","id":"2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd","sourceRef":"2025.42f7dd95-21b7-4c57-8279-5d823b453b71"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.e2f42b1d-c6a2-42b7-81b6-4b6bb777f3c0"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.7cafcfa9-4b7f-4ee1-8ec1-8cb21015b06b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fullPath","isCollection":false,"declaredType":"dataObject","id":"2056.6d324112-9a7d-4f8a-890a-7fbc1d34555c"},{"startQuantity":1,"outgoing":["2027.511f8071-2377-4b8a-8d9f-bf7f66719f11"],"default":"2027.511f8071-2377-4b8a-8d9f-bf7f66719f11","extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":-229,"y":179,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"name":"Retrieve Request Number","dataInputAssociation":[{"targetRef":"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.parentRequestNo"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.8359509f-28a9-4ae2-87e4-c3781918564c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}],"sourceRef":["2055.29fabc80-90b8-4cad-81e3-1c319c6f595a"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.4ec4b61d-5cd9-43b6-827c-c1801162373f"]}],"calledElement":"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546"},{"targetRef":"2025.83b139d0-c3dd-4e0d-802c-bfe0ece0773e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To set request header script","declaredType":"sequenceFlow","id":"2027.511f8071-2377-4b8a-8d9f-bf7f66719f11","sourceRef":"2025.8359509f-28a9-4ae2-87e4-c3781918564c"},{"outgoing":["2027.95e79ac2-ae3a-4006-8abc-cc22d8fdb8b8","2027.075aa304-5e83-46f8-858a-4a58450086dc"],"incoming":["2027.93485cc9-6250-4dc7-84c7-0a20991fd69b"],"default":"2027.075aa304-5e83-46f8-858a-4a58450086dc","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1115,"y":192,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Audited?","declaredType":"exclusiveGateway","id":"2025.43718c88-58a4-4bcb-8c12-bbaf38f39358"},{"targetRef":"bae6712f-5851-4002-bb0e-c2d82b0497b8","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"2027.95e79ac2-ae3a-4006-8abc-cc22d8fdb8b8","sourceRef":"2025.43718c88-58a4-4bcb-8c12-bbaf38f39358"},{"incoming":["2027.075aa304-5e83-46f8-858a-4a58450086dc"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1164,"y":301,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Stay on page 2","declaredType":"intermediateThrowEvent","id":"2025.9ec150c9-b7a7-42e7-8226-4cf6e64e0007"},{"targetRef":"2025.9ec150c9-b7a7-42e7-8226-4cf6e64e0007","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.errorMessage!=null)|| (tw.local.errorMessage!=\"\")"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2027.075aa304-5e83-46f8-858a-4a58450086dc","sourceRef":"2025.43718c88-58a4-4bcb-8c12-bbaf38f39358"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.da0fa9ee-c074-4ee6-8dc8-cd0f93bbc99c"},{"targetRef":"2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Retrieve Request Number","declaredType":"sequenceFlow","id":"2027.e999d93f-6e84-402b-8712-e9005c217c6b","sourceRef":"5afd220f-e381-4d2b-8c87-ccd1eae49a85"},{"outgoing":["2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715"],"incoming":["2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5"],"extensionElements":{"mode":["InvokeService"],"nodeVisualInfo":[{"width":95,"x":71,"y":179,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true]},"declaredType":"callActivity","startQuantity":1,"default":"2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715","name":"Set ECM default properties","dataInputAssociation":[{"targetRef":"2055.399c7a58-00b5-4451-9813-41c0b9652088","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.CustomerInfo.cif"]}}]},{"targetRef":"2055.c28023fb-b45e-4b63-ae36-97e6df6421bc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNo"]}}]},{"targetRef":"2055.25394215-074f-4b79-8e84-9a96d32cc83b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.a47e2771-dc05-45f2-836e-53b67b576c08","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9","declaredType":"TFormalExpression","content":["tw.local.odcRequest.attachmentDetails"]}}],"sourceRef":["2055.7d269650-ee48-4101-80db-2807cf921562"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.0261e8ad-a540-4682-88c5-87dff3eab23c"]}],"calledElement":"1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8"},{"outgoing":["2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34"],"incoming":["2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715"],"extensionElements":{"mode":["InvokeService"],"postAssignmentScript":["tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;\/\/ tw.local.parentPath\r\ntw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;\r\n\r\n\/\/if(!!tw.local.error &amp;&amp; tw.local.error.errorText!=null)\r\n\/\/\ttw.local.errorMessage+= tw.local.error.errorText;"],"nodeVisualInfo":[{"width":95,"x":190,"y":179,"declaredType":"TNodeVisualInfo","height":70}],"autoMap":[true],"preAssignmentScript":[]},"declaredType":"callActivity","startQuantity":1,"default":"2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34","name":"Create ECM Folder","dataInputAssociation":[{"targetRef":"2055.4156964b-1c67-40bc-8f62-3804c71cf908","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.e2ce0eed-342c-4942-8214-83e964b550e5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.340999d8-7f46-4260-8eb2-622b844d694b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.odcRequest.folderID"]}}],"sourceRef":["2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.odcRequest.appInfo.instanceID"]}}],"sourceRef":["2055.5f955245-0538-4e40-80a6-12f45c3102f3"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","declaredType":"TFormalExpression","content":["tw.local.error"]}}],"sourceRef":["2055.214c7268-80d0-444d-8702-dd0d5462dbe7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.fullPath"]}}],"sourceRef":["2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.676e3a06-e2cc-4855-84d6-6f82a350500a"]}],"calledElement":"1.46b984a3-b4ad-405a-abd3-8631f907efe4"},{"targetRef":"2025.340999d8-7f46-4260-8eb2-622b844d694b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"Copy of To Create ECM Folder","declaredType":"sequenceFlow","id":"2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715","sourceRef":"2025.a47e2771-dc05-45f2-836e-53b67b576c08"},{"targetRef":"2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create Reversal Request","declaredType":"sequenceFlow","id":"2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34","sourceRef":"2025.340999d8-7f46-4260-8eb2-622b844d694b"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"4eb401cb-d31d-47ec-b350-ddb5999db8e2","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"973c656a-5ea7-4458-80bd-00bbae3c5228","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"RACT01 - Create ODC Reversal Request","declaredType":"globalUserTask","id":"1.b9bf1811-6e1e-41a3-828b-39059fa168cb","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.1bba753e-260d-4827-8632-e837d4c62da9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.433b8941-9eb2-4a70-89d5-74e8024aa06a"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"b651b834-c113-485f-831b-4848ffbe74db","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"e50ca3ed-6c18-4420-807b-06729306a89e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"0160b448-f3d1-4b91-8171-44a72b3c7af3","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.93c5c002-7ac4-4283-83ee-63b8662f9223","epvProcessLinkId":"9ca2ac23-7028-43f5-8be8-d50ea00ca177","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"79720bf1-a7e1-47c9-8db3-5f86b2834011","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"d38bdf4c-e1ff-4a6f-853d-29ae1b700a3f","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"_95515798-b781-4482-b8f8-faedbf07f5f3"}],"outputSet":[{"id":"_73e8cce9-ed7b-40b0-905c-99a510d6bf2c"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.initiator = \"\";\nautoObject.requestNature = {};\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = {};\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new Date();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = {};\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = {};\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = {};\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = {};\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = {};\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = {};\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = {};\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = {};\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = [];\nautoObject.BasicDetails.Bills[0] = {};\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\nautoObject.BasicDetails.Invoice = [];\nautoObject.BasicDetails.Invoice[0] = {};\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\nautoObject.GeneratedDocumentInfo = {};\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = [];\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = {};\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = {};\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = {};\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = [];\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = {};\nautoObject.FcCollections.currency = {};\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new Date();\nautoObject.FcCollections.ToDate = new Date();\nautoObject.FcCollections.accountNo = {};\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = [];\nautoObject.FcCollections.retrievedTransactions[0] = {};\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = [];\nautoObject.FcCollections.selectedTransactions[0] = {};\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = [];\nautoObject.FcCollections.listOfAccounts[0] = {};\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = {};\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new Date();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = {};\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = [];\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.FinancialDetailsFO.multiTenorDates[0].rebate = 0.0;\nautoObject.FinancialDetailsFO.multiTenorDates[0].matDate = new Date();\nautoObject.FinancialDetailsFO.multiTenorDates[0].tenorDays = 0;\nautoObject.FinancialDetailsFO.rebate = 0.0;\nautoObject.ImporterDetails = {};\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = {};\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = {};\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new Date();\nautoObject.ProductShipmentDetails.shipmentMethod = {};\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = {};\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = {};\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = {};\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = {};\nautoObject.ContractCreation.productCode = {};\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new Date();\nautoObject.ContractCreation.valueDate = new Date();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new Date();\nautoObject.Parties = {};\nautoObject.Parties.Drawer = {};\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = {};\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = {};\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = {};\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = {};\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.Parties.caseInNeed = {};\nautoObject.Parties.caseInNeed.partyCIF = \"\";\nautoObject.Parties.caseInNeed.partyId = \"\";\nautoObject.Parties.caseInNeed.partyName = \"\";\nautoObject.Parties.caseInNeed.country = \"\";\nautoObject.Parties.caseInNeed.language = \"\";\nautoObject.Parties.caseInNeed.refrence = \"\";\nautoObject.Parties.caseInNeed.address1 = \"\";\nautoObject.Parties.caseInNeed.address2 = \"\";\nautoObject.Parties.caseInNeed.address3 = \"\";\nautoObject.Parties.caseInNeed.partyType = {};\nautoObject.Parties.caseInNeed.partyType.name = \"\";\nautoObject.Parties.caseInNeed.partyType.value = \"\";\nautoObject.ChargesAndCommissions = [];\nautoObject.ChargesAndCommissions[0] = {};\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ChargesAndCommissions[0].isGLFound = false;\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\nautoObject.ContractLiquidation = {};\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new Date();\nautoObject.ContractLiquidation.creditValueDate = new Date();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.debitedAccountName = \"\";\nautoObject.ContractLiquidation.creditedAccount = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = {};\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = {};\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = [];\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = {};\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = {};\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = [];\nautoObject.attachmentDetails.attachment[0] = {};\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = [];\nautoObject.complianceComments[0] = {};\nautoObject.complianceComments[0].startTime = new Date();\nautoObject.complianceComments[0].endTime = new Date();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = [];\nautoObject.History[0] = {};\nautoObject.History[0].startTime = new Date();\nautoObject.History[0].endTime = new Date();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = {};\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject.folderPath = \"\";\nautoObject.templateDocID = \"\";\nautoObject.requestID = 0;\nautoObject.customerAndPartyAccountList = [];\nautoObject.customerAndPartyAccountList[0] = {};\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\nautoObject.customerAndPartyAccountList[0].frozen = false;\nautoObject.customerAndPartyAccountList[0].dormant = false;\nautoObject.customerAndPartyAccountList[0].noDebit = false;\nautoObject.customerAndPartyAccountList[0].noCredit = false;\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\nautoObject.tradeFoComment = \"\";\nautoObject.exeHubMkrComment = \"\";\nautoObject.compcheckerComment = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.602aae62-b023-4b72-8c17-f498f81897a8"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.hubCode = \"077\";\nautoObject.branchCode = \"\";\nautoObject.initiatorUser = \"\";\nautoObject.branchName = \"\";\nautoObject.hubName = \"\";\nautoObject.branchSeq = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","name":"routingDetails","isCollection":false,"id":"2055.46373b11-9f39-48b9-8787-37bd5d89f78a"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isFirstTime","isCollection":false,"id":"2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"id":"2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"cdcadae6-1e70-4b19-8928-dad0b5727ad9"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.602aae62-b023-4b72-8c17-f498f81897a8</processParameterId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fd992c05-**************-2f12b1708795</guid>
            <versionId>45fefff3-79d0-4a7b-9dba-7d00d405f548</versionId>
        </processParameter>
        <processParameter name="routingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.46373b11-9f39-48b9-8787-37bd5d89f78a</processParameterId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8ce8253c-bddd-4050-94ee-b27980a01e2c</guid>
            <versionId>********-d80d-463a-93d4-8e4ea7c2a7f7</versionId>
        </processParameter>
        <processParameter name="isFirstTime">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258</processParameterId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>093f653b-22b8-4827-b92c-34f21b5874b3</guid>
            <versionId>00131a4a-65c3-4953-a85f-793aefaea36d</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74</processParameterId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d2fa8d56-c699-4e3c-a721-6befc3a5804a</guid>
            <versionId>eea622da-6a51-49e3-88af-9f8ffed30816</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d</processParameterId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>accb7344-7b21-4c5a-81cc-50d5217fe2f7</guid>
            <versionId>367a2826-2336-4c01-b141-3b3df774258c</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.1bba753e-260d-4827-8632-e837d4c62da9</processParameterId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>71042625-fe5f-4082-8a2a-b1819038474d</guid>
            <versionId>cf21a958-2f26-4fb5-bee3-ffbd52cd4773</versionId>
        </processParameter>
        <processParameter name="parentPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108</processParameterId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1cceb12b-723e-4adf-828a-34803b280bca</guid>
            <versionId>d54193a2-f502-48ea-ae4d-fd1112d73f3c</versionId>
        </processParameter>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.59d51a05-8a58-4fc6-8991-fcf4d9e04df4</processVariableId>
            <description isNull="true" />
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c9d1ec49-f023-4264-accb-fb1cada49497</guid>
            <versionId>99321ce7-**************-093ec22a201d</versionId>
        </processVariable>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e2f42b1d-c6a2-42b7-81b6-4b6bb777f3c0</processVariableId>
            <description isNull="true" />
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c58a4a6a-3c07-4c85-9873-3a0a53fe8be2</guid>
            <versionId>f4a04354-b385-497e-8590-40c26c3d51b9</versionId>
        </processVariable>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7cafcfa9-4b7f-4ee1-8ec1-8cb21015b06b</processVariableId>
            <description isNull="true" />
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>9624e0e4-d21c-42d2-b671-14089a37a43e</guid>
            <versionId>0e0ce5dd-3934-432b-989a-5dbdb620f799</versionId>
        </processVariable>
        <processVariable name="fullPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6d324112-9a7d-4f8a-890a-7fbc1d34555c</processVariableId>
            <description isNull="true" />
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>246e2f44-011b-47ca-90d6-d1faa26093ff</guid>
            <versionId>1f0d1c00-e427-4828-af8a-01211adfbab4</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.da0fa9ee-c074-4ee6-8dc8-cd0f93bbc99c</processVariableId>
            <description isNull="true" />
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>15580a2f-7e64-4d55-84e3-11af0d17c92e</guid>
            <versionId>61664e68-7719-45e8-af2b-cbd951d29523</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a47e2771-dc05-45f2-836e-53b67b576c08</processItemId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <name>Set ECM default properties</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c92047d6-6ceb-4dd2-acf3-898c23e115e9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-3835</guid>
            <versionId>27503010-b984-47b4-a4c1-87601309b4fa</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c92047d6-6ceb-4dd2-acf3-898c23e115e9</subProcessId>
                <attachedProcessRef>/1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8</attachedProcessRef>
                <guid>710ded1a-761c-40f1-bba9-90f40f0ce2da</guid>
                <versionId>e5f7b8bf-f53f-4f9d-8c79-17ed7141fec9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cc307aeb-6eeb-4b70-8263-b91bc8832ff7</processItemId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.048a646a-2177-4a1c-b8ba-e926d7c9d086</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:6f88</guid>
            <versionId>52fff5f1-2339-450c-b318-2eb99c6b3f2d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.048a646a-2177-4a1c-b8ba-e926d7c9d086</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b87bfd75-7dc8-4f38-a7d3-9258f00fb902</guid>
                <versionId>4352fcdf-74ab-49ad-bdf9-912dddc087a4</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d1a0ca24-0850-4b9b-8620-03efb2ee694d</processItemId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.df7eed39-4838-4e2f-ade1-2092072d7fb3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:6f89</guid>
            <versionId>62b2ec39-17ca-41ba-9f02-091d506528e9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.340999d8-7f46-4260-8eb2-622b844d694b</processItemId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <name>Create ECM Folder</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.9fc4f4e3-00e4-4410-9d9e-872229d37209</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:a6be0630e884ccca:-427a0b52:18bb428309d:-3834</guid>
            <versionId>c7f87061-1c87-47b1-b9ee-852c6d3df1ac</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.9fc4f4e3-00e4-4410-9d9e-872229d37209</subProcessId>
                <attachedProcessRef>/1.46b984a3-b4ad-405a-abd3-8631f907efe4</attachedProcessRef>
                <guid>3bdc461d-f30a-42af-9de1-3ec84a47d1fa</guid>
                <versionId>7f800106-1f8a-42d6-8aea-9f1374925e05</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8359509f-28a9-4ae2-87e4-c3781918564c</processItemId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <name>Retrieve Request Number</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.38fdde82-3dc3-416f-a586-ed581b1dd204</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-a4f</guid>
            <versionId>f224d2a1-d061-453a-9551-22003c0787a8</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.38fdde82-3dc3-416f-a586-ed581b1dd204</subProcessId>
                <attachedProcessRef>/1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</attachedProcessRef>
                <guid>c2872deb-965e-438a-a27c-79e5317504a7</guid>
                <versionId>bad9654f-ec3c-4c72-afa9-a6107d1f93b1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.610f7e3a-c3f1-4b8f-8969-3317a8808484</processItemId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <name>History</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c1738742-8d31-4de9-87d4-b58f8560bc58</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:7268</guid>
            <versionId>fe4b35c7-e2b6-4973-8277-07a17cd10f4f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c1738742-8d31-4de9-87d4-b58f8560bc58</subProcessId>
                <attachedProcessRef>/1.e8e61c7a-dc6d-441e-b350-b583581efb21</attachedProcessRef>
                <guid>7a83b078-ecb4-49e5-ad7f-94e46ffa6f35</guid>
                <versionId>1f218763-47e7-43e1-b14f-d8c4a5cbb603</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.428353f8-4707-4634-ac86-ee2371e6fff4</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <guid>ae3abba8-6e63-4106-9642-6336a79a2176</guid>
            <versionId>10a8d379-5086-4659-ab1d-57f985f82bb7</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.2a3a3e0b-30dd-44b3-a69b-e21c496b10bf</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <guid>bfec1e05-cc1c-4778-a002-3b8168f9b950</guid>
            <versionId>447e3a29-67ef-4e55-ba2f-cfda68dce8ab</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.90783bf9-dc2a-41d9-bc9b-2848f6176efd</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <guid>89de9f93-ca6e-4b48-9646-d7683fe646ec</guid>
            <versionId>9225ffa4-7d99-466c-bd7b-b9e6fd02297a</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.7575eed2-1f06-4c5b-9f07-ef56ef525e99</epvProcessLinkId>
            <epvId>/21.93c5c002-7ac4-4283-83ee-63b8662f9223</epvId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <guid>32b3c3db-1ea7-4393-85f1-f90726f85593</guid>
            <versionId>d7f6cc46-bfc1-4716-a88c-5e69240ec819</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.b9c24af0-1a1f-443d-8790-1a5405ac4def</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <guid>9fd017e1-ba86-4727-b53c-e4b12bf446e7</guid>
            <versionId>e264d612-d598-44d7-b061-841e40b55b70</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.eaee83b6-2df6-4552-bd24-15a242e0c138</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <guid>b40c205d-d377-4f09-8d59-66da0031cc33</guid>
            <versionId>e9d7669d-fb69-4f14-bb79-174f6654dd4e</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.da7ed78c-b0d8-4982-9efa-026d938fdce1</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <guid>d1fec684-576c-4d4c-aee4-fb14735d1573</guid>
            <versionId>ab8c2e26-819e-4e0d-9514-0e920996dae6</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.d1a0ca24-0850-4b9b-8620-03efb2ee694d</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="cdcadae6-1e70-4b19-8928-dad0b5727ad9" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns16:globalUserTask name="RACT01 - Create ODC Reversal Request" id="1.b9bf1811-6e1e-41a3-828b-39059fa168cb">
                    <ns16:documentation />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation id="973c656a-5ea7-4458-80bd-00bbae3c5228">
                            <ns16:startEvent name="Start" id="5afd220f-e381-4d2b-8c87-ccd1eae49a85">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="-293" y="202" width="24" height="24" color="#F8F8F8" />
                                    <ns3:default>2027.e999d93f-6e84-402b-8712-e9005c217c6b</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.e999d93f-6e84-402b-8712-e9005c217c6b</ns16:outgoing>
                            </ns16:startEvent>
                            <ns3:formTask name="Create Reversal Request" id="2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f">
                                <ns16:extensionElements>
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns13:nodeVisualInfo x="417" y="179" width="95" height="70" />
                                    <ns3:preAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd</ns16:incoming>
                                <ns16:incoming>2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34</ns16:incoming>
                                <ns16:incoming>2027.e999d93f-6e84-402b-8712-e9005c217c6b</ns16:incoming>
                                <ns16:outgoing>2027.ece40528-451e-4086-8c6a-138bd6e2e9ff</ns16:outgoing>
                                <ns16:outgoing>2027.a095f366-ba9c-4c32-8638-7078a5228473</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>cb4e8015-07b4-488f-8125-6f1b10de48ac</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>60e15862-1473-4ac8-854b-5523230b81a4</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>75bd7767-40f0-442c-8173-816837287ded</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>72b7b189-ce2c-4b17-8f76-7627af26999c</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a300aee0-6ed4-48d5-82b1-bcde25b30fd3</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>850db7db-3179-4b17-834e-e22ec88dc8b5</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>5b4ec826-b9a1-40dc-8ce4-e03c9c7051b2</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>9e310dc9-3f16-4feb-8a76-bfd60e24c8f6</ns19:id>
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>cb6c3b47-2548-4b44-8f51-49824df6b08f</ns19:id>
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6307252b-d065-4794-8208-1ae26f9cc0fb</ns19:id>
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>6e450104-cf1a-429a-8677-454e376fa9fc</ns19:id>
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>c0d05fe5-fd94-4501-8a38-7f2e71ef864c</ns19:id>
                                                    <ns19:optionName>complianceApproval</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>8a8358e9-ddf4-4d0b-8adc-d5bb2ea254f3</ns19:id>
                                                    <ns19:optionName>returnReasonVIS</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a06d78ae-8be9-40d3-8000-649789b722c4</ns19:id>
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b1a865b9-ceb1-4bcb-822f-41fa529d883c</ns19:id>
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>cfdd063e-7447-47d2-862b-031bee67a158</ns19:id>
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    <ns19:value>None</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>85220433-a459-429c-827b-83e60051b4ec</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>aa54988c-85d2-48cd-8a40-752bc789fff0</ns19:id>
                                                        <ns19:layoutItemId>Tab_Section1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>e940a729-0e97-4440-8937-849d7d7f95b5</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Tab section</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>22765fec-c86c-492e-89ce-1758bf79e4bc</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>964876a1-39dc-4221-83a3-c8b86f1b5f4a</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>4b6a33ae-449c-4784-83cf-d5981e28ed32</ns19:id>
                                                            <ns19:optionName>tabsStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"S"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>54a61e78-d123-41b5-8932-b039562ca63c</ns19:id>
                                                            <ns19:optionName>colorStyle</ns19:optionName>
                                                            <ns19:value>P</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>fd135dab-01dd-42fb-85d3-06d267859fe7</ns19:id>
                                                            <ns19:optionName>sizeStyle</ns19:optionName>
                                                            <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>897bbaa0-480b-4d18-8ba0-11b513d53480</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>8a919c88-3b6d-4e5a-8f10-333d7550d8c2</ns19:id>
                                                                <ns19:layoutItemId>Reversal_Closure_CV1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>a63ebbf8-ee2f-467a-856a-aada47f5d119</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Reversal Details</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>38917cb4-8330-47f8-8d2d-7ead4979944e</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e883fe99-7d60-4db9-8433-2c034d7a2f48</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>6f032046-b1f4-40ee-8cbc-3ee4451d5ab0</ns19:id>
                                                                    <ns19:optionName>closureReasonVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>64a80a11-753f-420e-8cf1-a29a8868b343</ns19:id>
                                                                    <ns19:optionName>executionHubVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.f0c268ac-0772-4735-af5b-5fc6caec30a1</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.ReversalReason</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>66d71ecf-f6c3-4b6d-83cd-50e7a6fd2d67</ns19:id>
                                                                <ns19:layoutItemId>Basic_Details_CV1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>a32d87cc-7304-46ed-8245-57e4f0dc7b43</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Basic Details</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>744cc34d-de0a-40b5-81d7-5d975c5a8dc7</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>aef23b26-10c3-45c4-89c8-5f0fbd449128</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>882d4dea-aee7-4684-8bb8-fc1af9f190f9</ns19:id>
                                                                    <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>73a8dee7-38bb-4886-867b-53baf139b4bf</ns19:id>
                                                                    <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>20f0e69e-a763-4659-89e8-93bcf1aabe1e</ns19:id>
                                                                    <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>09e92959-052f-4c9f-8f68-72275df8248f</ns19:id>
                                                                    <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>5512c82f-86af-491c-8678-38cbb6731b0d</ns19:id>
                                                                    <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>2929e554-3b27-40f4-8e02-06185ade20ff</ns19:id>
                                                                <ns19:layoutItemId>Customer_Information_cv1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>f45c3961-ad4f-4807-8a32-3ec6091f833c</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Customer Info</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>523052c2-88ce-41a7-8b49-374376a0730a</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cc6bd183-30b9-4d14-801e-1819bb4c16f7</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4be83902-42cf-4f8d-84ed-849bc3f31d8b</ns19:id>
                                                                    <ns19:optionName>customerInfoVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>90d63cc3-26c4-4383-81cf-a64fef361ab0</ns19:id>
                                                                    <ns19:optionName>listsVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.a7074b64-1e8b-4e3a-8ea0-0c830359104c</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.CustomerInfo</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>fd7fd0ad-7c61-42b5-8a7d-fb969b07b95a</ns19:id>
                                                                <ns19:layoutItemId>Financial_Details_Branch1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>b653bace-8945-4d48-8af5-d88baecaeeba</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Financial Details Branch</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9578b282-6d07-4aad-81b8-1073576eedf7</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>fe5c20d1-df3a-4aec-8f56-dadc0b90a537</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>e6638804-9365-40b2-8b46-055c79a7d063</ns19:id>
                                                                    <ns19:optionName>financialDetailsCVVis</ns19:optionName>
                                                                    <ns19:value>READONLY</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d193aa73-86ee-43e5-85af-2885f66292ca</ns19:id>
                                                                    <ns19:optionName>currencyDocAmountVIS</ns19:optionName>
                                                                    <ns19:value>READONLY</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.4992aee7-c679-4a00-9de8-49a633cb5edd</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.FinancialDetailsBR</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>32780090-57af-4d46-86ba-d58641ad2161</ns19:id>
                                                                <ns19:layoutItemId>FC_Collections_CV1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>194657fc-5498-4a18-8798-75b486f0cc8b</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>FlexCube Collections</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>49e1deb1-3edf-4be3-80ed-346fa062f3bc</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>57a5af6e-f718-4001-8163-4fc334b67105</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>05597451-9a57-4215-8ba6-bea9d0791703</ns19:id>
                                                                    <ns19:optionName>FCVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>7e361ecb-c133-48de-8854-3bce69da81e1</ns19:id>
                                                                    <ns19:optionName>retrieveBtnVis</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>17cbc24c-684b-422c-8338-2f59134de550</ns19:id>
                                                                    <ns19:optionName>addBtnVIS</ns19:optionName>
                                                                    <ns19:value>None</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>284f24d7-ea29-4a6c-83ce-4603e2b67b96</ns19:id>
                                                                    <ns19:optionName>customerCif</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.cif</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>9a0daaaa-de99-4790-89ff-d21a36fbf143</ns19:id>
                                                                    <ns19:optionName>collectionCurrencyVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>d153255c-ce9f-4a52-8302-b1792cf34482</ns19:id>
                                                                    <ns19:optionName>negotiatedExchangeRateVIS</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>73da2daa-9670-4dcc-8385-d5ac80032c4b</ns19:id>
                                                                    <ns19:optionName>requestCurrency</ns19:optionName>
                                                                    <ns19:value>Readonly</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.650e2a08-d9a6-42a7-ba50-bdca4cdd54fe</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.FcCollections</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>c574be62-c1f8-4531-8bcd-5b1ec18a5117</ns19:id>
                                                                <ns19:layoutItemId>Attachment1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>8aca6b81-aff5-49c9-8d97-b157a7572167</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Attachment</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>3331b593-c2fc-42ad-8dca-fb187b1ecd88</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>188aca3c-d61b-456b-858a-c8f622ab5882</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>09aff001-0eac-41e4-851a-ec44d51a53fd</ns19:id>
                                                                    <ns19:optionName>ECMProperties</ns19:optionName>
                                                                    <ns19:value>tw.local.odcRequest.attachmentDetails.ecmProperties</ns19:value>
                                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1f175e19-eb9b-4fda-8d53-6af3225b7889</ns19:id>
                                                                    <ns19:optionName>canUpdate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cc9d78c7-c485-4c54-8875-aee0f7574897</ns19:id>
                                                                    <ns19:optionName>canCreate</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>8c6cd406-83a4-41f1-8aea-47ca0c0386ca</ns19:id>
                                                                    <ns19:optionName>canDelete</ns19:optionName>
                                                                    <ns19:value>true</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.797f9d29-e666-4d5c-ba4b-cf4866173643</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.attachmentDetails.attachment[]</ns19:binding>
                                                            </ns19:contributions>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>10d75adb-e3e1-4e95-89f2-1ef57fa8fe2b</ns19:id>
                                                                <ns19:layoutItemId>DC_History1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>55f45747-717e-44b6-860c-57655f915771</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>History</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>926a9cb7-c7a6-4614-8587-a0e06ec0d319</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>43908811-33b6-4a3d-8fa7-6c5fd727e376</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cdb5b00a-7e25-4d09-8d6f-0abdc560f0ac</ns19:id>
                                                                    <ns19:optionName>@visibility.script</ns19:optionName>
                                                                    <ns19:value>{"@class":"com.ibm.bpm.coachNG.visibility.VisibilityRules","rules":[{"@class":"com.ibm.bpm.coachNG.visibility.VariableVisibilityRule","var_conditions":[{"timeInMs":null,"var":"tw.local.isFirstTime","operand":true,"operator":0}],"action":"NONE"}],"defaultAction":"DEFAULT"}</ns19:value>
                                                                    <ns19:valueType>static</ns19:valueType>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.5df8245e-3f18-41b6-8394-548397e4652f</ns19:viewUUID>
                                                                <ns19:binding>tw.local.odcRequest.History[]</ns19:binding>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:endEvent name="End" id="bae6712f-5851-4002-bb0e-c2d82b0497b8">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1229" y="195" width="24" height="24" color="#F8F8F8" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.95e79ac2-ae3a-4006-8abc-cc22d8fdb8b8</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5" name="set request header script" id="2025.83b139d0-c3dd-4e0d-802c-bfe0ece0773e">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="-72" y="179" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.511f8071-2377-4b8a-8d9f-bf7f66719f11</ns16:incoming>
                                <ns16:outgoing>2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5</ns16:outgoing>
                                <ns16:script>tw.local.odcRequest.BasicDetails.requestNature = tw.local.odcRequest.requestNature.name;&#xD;
tw.local.odcRequest.BasicDetails.requestType = tw.local.odcRequest.requestType.name;&#xD;
&#xD;
if(tw.local.isFirstTime == true)&#xD;
{&#xD;
&#xD;
	tw.local.odcRequest.appInfo.status ="Initiated";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Initiated";&#xD;
	tw.local.odcRequest.appInfo.stepName =tw.epv.ScreenNames.RACT01;&#xD;
	tw.local.odcRequest.appInfo.instanceID =tw.local.odcRequest.requestNo;&#xD;
	&#xD;
	tw.local.odcRequest.BasicDetails.requestState = "Undergoing Reversal";&#xD;
	&#xD;
	tw.local.odcRequest.appInfo.requestName = "ODC Reversal";&#xD;
&#xD;
tw.local.actionConditions = {};&#xD;
tw.local.actionConditions.complianceApproval= false;&#xD;
tw.local.actionConditions.screenName= tw.epv.ScreenNames.RACT01;	&#xD;
	&#xD;
tw.local.odcRequest.stepLog ={};&#xD;
tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
tw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.RACT01;&#xD;
&#xD;
if(!tw.local.odcRequest.ReversalReason) &#xD;
   tw.local.odcRequest.ReversalReason = {};&#xD;
&#xD;
tw.local.odcRequest.History = [];&#xD;
&#xD;
}&#xD;
else&#xD;
{&#xD;
&#xD;
	tw.local.odcRequest.stepLog ={};&#xD;
	tw.local.odcRequest.stepLog.startTime = new Date();&#xD;
	tw.local.odcRequest.stepLog.step =  tw.epv.ScreenNames.RACT01;&#xD;
	tw.local.actionConditions = {};&#xD;
	tw.local.actionConditions.complianceApproval= false;&#xD;
	tw.local.actionConditions.screenName= tw.epv.ScreenNames.RACT01;&#xD;
&#xD;
	tw.local.odcRequest.appInfo.stepName = tw.epv.ScreenNames.RACT01;&#xD;
}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.83b139d0-c3dd-4e0d-802c-bfe0ece0773e" targetRef="2025.a47e2771-dc05-45f2-836e-53b67b576c08" name="To Create Reversal Request" id="2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.59d51a05-8a58-4fc6-8991-fcf4d9e04df4" />
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703" name="Validation Script" id="2025.0f0034ec-ba75-4d37-8150-2a080b80bc01">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="589" y="182" width="95" height="70" color="#FF7782" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.ece40528-451e-4086-8c6a-138bd6e2e9ff</ns16:incoming>
                                <ns16:outgoing>2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703</ns16:outgoing>
                                <ns16:script> tw.local.errorMessage ="";&#xD;
 var mandatoryTriggered = false;&#xD;
 &#xD;
/************************************************************************************************************************&#xD;
/*-------------------------------------------------- All Section's Validation ------------------------------------------&#xD;
************************************************************************************************************************/&#xD;
//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------&#xD;
mandatory(tw.local.odcRequest.stepLog.action, "tw.local.odcRequest.stepLog.action");&#xD;
&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)&#xD;
{&#xD;
	mandatory(tw.local.odcRequest.ReversalReason.reversalReason, "tw.local.odcRequest.ReversalReason.reversalReason");&#xD;
	maxLength(tw.local.odcRequest.ReversalReason.reversalReason , "tw.local.odcRequest.ReversalReason.reversalReason", 160 , "Shouldn't be more than 160 character" , "Reversal Reason: " + "Shouldn't be more than 160 character" );&#xD;
&#xD;
}	&#xD;
	&#xD;
&#xD;
function mandatory(field , fieldName)&#xD;
{&#xD;
	if (field == null || field == undefined )&#xD;
	{&#xD;
		addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	else&#xD;
	{			&#xD;
		switch (typeof field)&#xD;
		{&#xD;
			case "string":&#xD;
				if (field.trim() != undefined &amp;&amp; field.trim() != null &amp;&amp; field.trim().length == 0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0)&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
	&#xD;
			default:&#xD;
				&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if( field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime()) ) {}&#xD;
				&#xD;
				else&#xD;
				{&#xD;
					addError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;	&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function addError(fieldName , controlMessage , validationMessage , fromMandatory)&#xD;
{&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered ? "" : tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;";&#xD;
}&#xD;
function maxLength(field , fieldName , len , controlMessage , validationMessage)&#xD;
{&#xD;
	if (field.length &gt; len)&#xD;
	{&#xD;
		addError(fieldName , controlMessage , validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
	</ns16:script>
                            </ns16:scriptTask>
                            <ns16:exclusiveGateway default="2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae" name="Exclusive Gateway" id="2025.4e28c43d-1487-40d1-83de-47725c1e6556">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="766" y="201" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703</ns16:incoming>
                                <ns16:outgoing>2027.2308babf-3d77-459e-8cd4-45e5873400ad</ns16:outgoing>
                                <ns16:outgoing>2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.0f0034ec-ba75-4d37-8150-2a080b80bc01" targetRef="2025.4e28c43d-1487-40d1-83de-47725c1e6556" name="To Exclusive Gateway" id="2027.a13bbd2e-fd04-42eb-8ddd-cbe3d120f703">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.4e28c43d-1487-40d1-83de-47725c1e6556" targetRef="2025.823cd073-97dc-4d59-8b3f-953d2a3f8a6d" name="To End" id="2027.2308babf-3d77-459e-8cd4-45e5873400ad">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page" id="2025.b8bad59c-1757-42dc-8e94-2ea24d390349">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="754" y="117" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.4e28c43d-1487-40d1-83de-47725c1e6556" targetRef="2025.b8bad59c-1757-42dc-8e94-2ea24d390349" name="To Stay on page" id="2027.1310b55d-8a5a-4ec6-8d10-01c8a07398ae">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f" targetRef="2025.0f0034ec-ba75-4d37-8150-2a080b80bc01" name="To Validation Script" id="2027.ece40528-451e-4086-8c6a-138bd6e2e9ff">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="566ad7d9-68fb-47d0-8dc9-b22634e4ece3">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e" name="Setting status and substatus" id="2025.823cd073-97dc-4d59-8b3f-953d2a3f8a6d">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="874" y="173" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.2308babf-3d77-459e-8cd4-45e5873400ad</ns16:incoming>
                                <ns16:outgoing>2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e</ns16:outgoing>
                                <ns16:script>if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.submitRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status ="Initiated";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Pending Review";&#xD;
	tw.local.lastAction = "";&#xD;
}&#xD;
if(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.cancelRequest)&#xD;
{&#xD;
	tw.local.odcRequest.appInfo.status = "Initiated";&#xD;
	tw.local.odcRequest.appInfo.subStatus ="Pending cancellation confirmation";&#xD;
	tw.local.lastAction = tw.epv.CreationActions.cancelRequest;&#xD;
}</ns16:script>
                            </ns16:scriptTask>
                            <ns16:callActivity calledElement="1.e8e61c7a-dc6d-441e-b350-b583581efb21" default="2027.93485cc9-6250-4dc7-84c7-0a20991fd69b" name="History" id="2025.610f7e3a-c3f1-4b8f-8969-3317a8808484">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1010" y="173" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e</ns16:incoming>
                                <ns16:outgoing>2027.93485cc9-6250-4dc7-84c7-0a20991fd69b</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.ba547af3-d09b-4196-816b-653732f6b226</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.epv.userRole.RevAct01</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.416d990b-a0aa-4323-8df7-1f58b014c2ba</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.a617c560-c740-484e-89de-0931088cdc6c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.5640d61f-8ab9-4476-8a3b-cf6d102461e1</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.823cd073-97dc-4d59-8b3f-953d2a3f8a6d" targetRef="2025.610f7e3a-c3f1-4b8f-8969-3317a8808484" name="To History" id="2027.72d2106d-264a-4e71-83aa-4b0ef2f03e0e">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.610f7e3a-c3f1-4b8f-8969-3317a8808484" targetRef="2025.43718c88-58a4-4bcb-8c12-bbaf38f39358" name="To Audited?" id="2027.93485cc9-6250-4dc7-84c7-0a20991fd69b">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 1" id="2025.42f7dd95-21b7-4c57-8279-5d823b453b71">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="425" y="77" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                    <ns3:default>2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd</ns3:default>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.a095f366-ba9c-4c32-8638-7078a5228473</ns16:incoming>
                                <ns16:outgoing>2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f" targetRef="2025.42f7dd95-21b7-4c57-8279-5d823b453b71" name="To Stay on page 1" id="2027.a095f366-ba9c-4c32-8638-7078a5228473">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topRight</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="ce627a9a-9403-43fe-807b-f3d4c1455dca">
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.42f7dd95-21b7-4c57-8279-5d823b453b71" targetRef="2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f" name="To Create Reversal Request" id="2027.f513c908-6e5d-42b4-8be0-bda1db57bbfd">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>leftCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.e2f42b1d-c6a2-42b7-81b6-4b6bb777f3c0" />
                            <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.7cafcfa9-4b7f-4ee1-8ec1-8cb21015b06b" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="fullPath" id="2056.6d324112-9a7d-4f8a-890a-7fbc1d34555c" />
                            <ns16:callActivity calledElement="1.509c6ef8-f489-4ab8-bcb8-45c4531aa546" default="2027.511f8071-2377-4b8a-8d9f-bf7f66719f11" name="Retrieve Request Number" id="2025.8359509f-28a9-4ae2-87e4-c3781918564c">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="-229" y="179" width="95" height="70" />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.511f8071-2377-4b8a-8d9f-bf7f66719f11</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.e1ca7465-4084-405d-8ea6-ab3f3762de92</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.parentRequestNo</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.29fabc80-90b8-4cad-81e3-1c319c6f595a</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.4ec4b61d-5cd9-43b6-827c-c1801162373f</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.8359509f-28a9-4ae2-87e4-c3781918564c" targetRef="2025.83b139d0-c3dd-4e0d-802c-bfe0ece0773e" name="To set request header script" id="2027.511f8071-2377-4b8a-8d9f-bf7f66719f11">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:exclusiveGateway default="2027.075aa304-5e83-46f8-858a-4a58450086dc" name="Audited?" id="2025.43718c88-58a4-4bcb-8c12-bbaf38f39358">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1115" y="192" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.93485cc9-6250-4dc7-84c7-0a20991fd69b</ns16:incoming>
                                <ns16:outgoing>2027.95e79ac2-ae3a-4006-8abc-cc22d8fdb8b8</ns16:outgoing>
                                <ns16:outgoing>2027.075aa304-5e83-46f8-858a-4a58450086dc</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:sequenceFlow sourceRef="2025.43718c88-58a4-4bcb-8c12-bbaf38f39358" targetRef="bae6712f-5851-4002-bb0e-c2d82b0497b8" name="Yes" id="2027.95e79ac2-ae3a-4006-8abc-cc22d8fdb8b8">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isSuccessful</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:intermediateThrowEvent name="Stay on page 2" id="2025.9ec150c9-b7a7-42e7-8226-4cf6e64e0007">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1164" y="301" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.075aa304-5e83-46f8-858a-4a58450086dc</ns16:incoming>
                                <ns3:stayOnPageEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.43718c88-58a4-4bcb-8c12-bbaf38f39358" targetRef="2025.9ec150c9-b7a7-42e7-8226-4cf6e64e0007" name="No" id="2027.075aa304-5e83-46f8-858a-4a58450086dc">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">(tw.local.errorMessage!=null)|| (tw.local.errorMessage!="")</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.da0fa9ee-c074-4ee6-8dc8-cd0f93bbc99c" />
                            <ns16:sequenceFlow sourceRef="5afd220f-e381-4d2b-8c87-ccd1eae49a85" targetRef="2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f" name="To Retrieve Request Number" id="2027.e999d93f-6e84-402b-8712-e9005c217c6b">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftTop</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.b8649f2c-dee5-4e4f-af50-7dc2fb1a01f8" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715" name="Set ECM default properties" id="2025.a47e2771-dc05-45f2-836e-53b67b576c08">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="71" y="179" width="95" height="70" />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.0eeedb0f-a996-4672-8bf6-f21968d0fed5</ns16:incoming>
                                <ns16:outgoing>2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.399c7a58-00b5-4451-9813-41c0b9652088</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.CustomerInfo.cif</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.c28023fb-b45e-4b63-ae36-97e6df6421bc</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.requestNo</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.25394215-074f-4b79-8e84-9a96d32cc83b</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.7d269650-ee48-4101-80db-2807cf921562</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.55bb335a-d3b3-4749-a082-859e2a48ace9">tw.local.odcRequest.attachmentDetails</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.0261e8ad-a540-4682-88c5-87dff3eab23c</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:callActivity calledElement="1.46b984a3-b4ad-405a-abd3-8631f907efe4" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34" name="Create ECM Folder" id="2025.340999d8-7f46-4260-8eb2-622b844d694b">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="190" y="179" width="95" height="70" />
                                    <ns3:postAssignmentScript>tw.local.odcRequest.attachmentDetails.ecmProperties.fullPath =tw.local.fullPath;// tw.local.parentPath&#xD;
tw.local.odcRequest.attachmentDetails.folderID= tw.local.odcRequest.folderID;&#xD;
&#xD;
//if(!!tw.local.error &amp;&amp; tw.local.error.errorText!=null)&#xD;
//	tw.local.errorMessage+= tw.local.error.errorText;</ns3:postAssignmentScript>
                                    <ns3:preAssignmentScript />
                                    <ns3:mode>InvokeService</ns3:mode>
                                    <ns3:autoMap>true</ns3:autoMap>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715</ns16:incoming>
                                <ns16:outgoing>2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.4156964b-1c67-40bc-8f62-3804c71cf908</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.odcRequest</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.e2ce0eed-342c-4942-8214-83e964b550e5</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.routingDetails.hubCode</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.50cbead1-1b81-430e-8ce3-b1af43ad5d89</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.odcRequest.folderID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.5f955245-0538-4e40-80a6-12f45c3102f3</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.odcRequest.appInfo.instanceID</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.214c7268-80d0-444d-8702-dd0d5462dbe7</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45">tw.local.error</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.ef365d67-fff7-4205-8f84-839e4bdf2ad7</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fullPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.09016d6f-7985-4fd8-84ac-9a08ec6ed5c2</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentPath</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.676e3a06-e2cc-4855-84d6-6f82a350500a</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.a47e2771-dc05-45f2-836e-53b67b576c08" targetRef="2025.340999d8-7f46-4260-8eb2-622b844d694b" name="Copy of To Create ECM Folder" id="2027.7721815b-16c4-41cb-8ce8-4ebaa0dc6715">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.340999d8-7f46-4260-8eb2-622b844d694b" targetRef="2025.8089554b-20b3-4ae7-80ff-ad4286c18b0f" name="To Create Reversal Request" id="2027.ad2f2ff0-8863-4263-80e8-df2ba2ff5b34">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:htmlHeaderTag id="4eb401cb-d31d-47ec-b350-ddb5999db8e2">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="b651b834-c113-485f-831b-4848ffbe74db" />
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="e50ca3ed-6c18-4420-807b-06729306a89e" />
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="0160b448-f3d1-4b91-8171-44a72b3c7af3" />
                                <ns3:epvProcessLinkRef epvId="21.93c5c002-7ac4-4283-83ee-63b8662f9223" epvProcessLinkId="9ca2ac23-7028-43f5-8be8-d50ea00ca177" />
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="79720bf1-a7e1-47c9-8db3-5f86b2834011" />
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="d38bdf4c-e1ff-4a6f-853d-29ae1b700a3f" />
                            </ns3:epvProcessLinks>
                            <ns3:localizationResourceLinks>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    <ns3:id>69.433b8941-9eb2-4a70-89d5-74e8024aa06a</ns3:id>
                                </ns3:resourceRef>
                            </ns3:localizationResourceLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.602aae62-b023-4b72-8c17-f498f81897a8">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.initiator = "";
autoObject.requestNature = {};
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = {};
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new Date();
autoObject.ImporterName = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = {};
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = {};
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = {};
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = {};
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = {};
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = {};
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = {};
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = {};
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = [];
autoObject.BasicDetails.Bills[0] = {};
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();
autoObject.BasicDetails.Invoice = [];
autoObject.BasicDetails.Invoice[0] = {};
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new Date();
autoObject.GeneratedDocumentInfo = {};
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = [];
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = [];
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = [];
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = [];
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = [];
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = {};
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = [];
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = {};
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = {};
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new Date();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = {};
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = [];
autoObject.FinancialDetailsBR.listOfAccounts[0] = {};
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = {};
autoObject.FcCollections.currency = {};
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new Date();
autoObject.FcCollections.ToDate = new Date();
autoObject.FcCollections.accountNo = {};
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = [];
autoObject.FcCollections.retrievedTransactions[0] = {};
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = [];
autoObject.FcCollections.selectedTransactions[0] = {};
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new Date();
autoObject.FcCollections.selectedTransactions[0].valueDate = new Date();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = [];
autoObject.FcCollections.listOfAccounts[0] = {};
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = {};
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new Date();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = {};
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = [];
autoObject.FinancialDetailsFO.multiTenorDates[0] = {};
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.FinancialDetailsFO.multiTenorDates[0].rebate = 0.0;
autoObject.FinancialDetailsFO.multiTenorDates[0].matDate = new Date();
autoObject.FinancialDetailsFO.multiTenorDates[0].tenorDays = 0;
autoObject.FinancialDetailsFO.rebate = 0.0;
autoObject.ImporterDetails = {};
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = {};
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = {};
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = {};
autoObject.ProductShipmentDetails.CBECommodityClassification = {};
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new Date();
autoObject.ProductShipmentDetails.shipmentMethod = {};
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = {};
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = {};
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = {};
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = {};
autoObject.ContractCreation.productCode = {};
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new Date();
autoObject.ContractCreation.valueDate = new Date();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new Date();
autoObject.Parties = {};
autoObject.Parties.Drawer = {};
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = {};
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = {};
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = {};
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = {};
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.Parties.caseInNeed = {};
autoObject.Parties.caseInNeed.partyCIF = "";
autoObject.Parties.caseInNeed.partyId = "";
autoObject.Parties.caseInNeed.partyName = "";
autoObject.Parties.caseInNeed.country = "";
autoObject.Parties.caseInNeed.language = "";
autoObject.Parties.caseInNeed.refrence = "";
autoObject.Parties.caseInNeed.address1 = "";
autoObject.Parties.caseInNeed.address2 = "";
autoObject.Parties.caseInNeed.address3 = "";
autoObject.Parties.caseInNeed.partyType = {};
autoObject.Parties.caseInNeed.partyType.name = "";
autoObject.Parties.caseInNeed.partyType.value = "";
autoObject.ChargesAndCommissions = [];
autoObject.ChargesAndCommissions[0] = {};
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = {};
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = {};
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = {};
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ChargesAndCommissions[0].isGLFound = false;
autoObject.ChargesAndCommissions[0].glVerifyMSG = "";
autoObject.ContractLiquidation = {};
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new Date();
autoObject.ContractLiquidation.creditValueDate = new Date();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.debitedAccountName = "";
autoObject.ContractLiquidation.creditedAccount = {};
autoObject.ContractLiquidation.creditedAccount.accountClass = {};
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = {};
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = {};
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = [];
autoObject.actions[0] = "";
autoObject.attachmentDetails = {};
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = {};
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = [];
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = [];
autoObject.attachmentDetails.attachment[0] = {};
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = [];
autoObject.complianceComments[0] = {};
autoObject.complianceComments[0].startTime = new Date();
autoObject.complianceComments[0].endTime = new Date();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = [];
autoObject.History[0] = {};
autoObject.History[0].startTime = new Date();
autoObject.History[0].endTime = new Date();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = {};
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject.folderPath = "";
autoObject.templateDocID = "";
autoObject.requestID = 0;
autoObject.customerAndPartyAccountList = [];
autoObject.customerAndPartyAccountList[0] = {};
autoObject.customerAndPartyAccountList[0].accountNO = "";
autoObject.customerAndPartyAccountList[0].currencyCode = "";
autoObject.customerAndPartyAccountList[0].branchCode = "";
autoObject.customerAndPartyAccountList[0].balance = 0.0;
autoObject.customerAndPartyAccountList[0].typeCode = "";
autoObject.customerAndPartyAccountList[0].customerName = "";
autoObject.customerAndPartyAccountList[0].customerNo = "";
autoObject.customerAndPartyAccountList[0].frozen = false;
autoObject.customerAndPartyAccountList[0].dormant = false;
autoObject.customerAndPartyAccountList[0].noDebit = false;
autoObject.customerAndPartyAccountList[0].noCredit = false;
autoObject.customerAndPartyAccountList[0].postingAllowed = false;
autoObject.customerAndPartyAccountList[0].ibanAccountNumber = "";
autoObject.customerAndPartyAccountList[0].accountClassCode = "";
autoObject.customerAndPartyAccountList[0].balanceType = "";
autoObject.customerAndPartyAccountList[0].accountStatus = "";
autoObject.tradeFoComment = "";
autoObject.exeHubMkrComment = "";
autoObject.compcheckerComment = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="routingDetails" itemSubjectRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727" isCollection="false" id="2055.46373b11-9f39-48b9-8787-37bd5d89f78a">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.hubCode = "077";
autoObject.branchCode = "";
autoObject.initiatorUser = "";
autoObject.branchName = "";
autoObject.hubName = "";
autoObject.branchSeq = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="isFirstTime" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258" />
                        <ns16:dataInput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74" />
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d" />
                        <ns16:dataOutput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.1bba753e-260d-4827-8632-e837d4c62da9" />
                        <ns16:dataOutput name="parentPath" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108" />
                        <ns16:inputSet id="_95515798-b781-4482-b8f8-faedbf07f5f3" />
                        <ns16:outputSet id="_73e8cce9-ed7b-40b0-905c-99a510d6bf2c" />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.09ded5a9-665d-4696-a072-30b2f7faf92a</processLinkId>
            <processId>1.b9bf1811-6e1e-41a3-828b-39059fa168cb</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d1a0ca24-0850-4b9b-8620-03efb2ee694d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.cc307aeb-6eeb-4b70-8263-b91bc8832ff7</toProcessItemId>
            <guid>bf1a858e-b446-474e-9fe0-fdd08c114cb7</guid>
            <versionId>566ca7ac-ebf5-4eb9-917d-d4669c450031</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.d1a0ca24-0850-4b9b-8620-03efb2ee694d</fromProcessItemId>
            <toProcessItemId>2025.cc307aeb-6eeb-4b70-8263-b91bc8832ff7</toProcessItemId>
        </link>
    </process>
</teamworks>

