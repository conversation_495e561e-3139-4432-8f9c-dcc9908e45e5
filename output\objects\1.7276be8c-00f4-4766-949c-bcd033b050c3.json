{"id": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "versionId": "86e4fd32-c430-438d-a46b-870d97e311a2", "name": "Trade Compliance", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "attachment", "hasDefault": false, "type": "1"}, {"name": "ECMproperties", "hasDefault": false, "type": "1"}, {"name": "folderId", "hasDefault": false, "type": "1"}, {"name": "compApprovalInit", "hasDefault": false, "type": "1"}, {"name": "fromTradeFo", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}, {"name": "attachment", "hasDefault": false, "type": "2"}, {"name": "complianceComments", "hasDefault": false, "type": "2"}], "private": [{"name": "errorExist", "hasDefault": false}, {"name": "errorMessage", "hasDefault": false}, {"name": "documentsTypesSelected", "hasDefault": false}, {"name": "validation", "hasDefault": false}, {"name": "parentRequestNoVIS", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "tradefoVis", "hasDefault": false}, {"name": "exehubMkrVis", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Trade Compliance", "id": "2025.19273870-c44a-41f8-83e4-94bd44345007", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Update request state and status in DB", "id": "2025.6a84d426-165c-459f-8ceb-d6006c6ad20d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit ODC Request", "id": "2025.509f906a-c362-44e5-81e5-0364f769022b", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit Request History", "id": "2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Have Errors", "id": "2025.3d21978a-5764-41b9-bf92-a51bcfc0783c", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited", "id": "2025.d17dee7b-c083-4da1-8247-34274a828f10", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audited?", "id": "2025.8b4a5787-28e3-49d2-896d-ef1528ec5852", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "Initialization Script", "id": "2025.335301a8-53b9-4997-b289-0e7f6704abba", "script": "tw.local.odcRequest.stepLog = {};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT07; \r\r\n\r\r\n tw.local.odcRequest.appInfo.stepName= tw.epv.ScreenNames.CACT07;\r\r\n tw.local.odcRequest.appInfo.appID = tw.system.processInstance.id;\r\r\n \r\r\n tw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT07;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;\r\r\n\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\r\n\ttw.local.parentRequestNoVIS = \"READONLY\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\nif(tw.local.fromTradeFo == true)\r\r\n{\r\r\n\ttw.local.tradefoVis = \"Editable\";\r\r\n\ttw.local.exehubMkrVis = \"None\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.tradefoVis = \"None\";\r\r\n\ttw.local.exehubMkrVis = \"Editable\";\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Set Status and substatus", "id": "2025.5e719de4-0f51-4f78-875d-e8b72a537cae", "script": "if(tw.local.compApprovalInit == tw.epv.userRole.CACT03)\r\r\n{\r\r\n\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.returnToMaker;\r\r\n\ttw.local.odcRequest.appInfo.status =\" In Approval \";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Waiting Trade Fo  Maker Approval\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.approveRequest;\r\r\n\t\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing\";\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "validation", "id": "2025.6125901b-f21b-438b-9845-846e04b2e62d", "script": "tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n \r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\t\r\r\n//-------------------------------------------------Trade Fo and compliance comment validation ------------------------------\r\r\nif(tw.local.odcRequest.complianceApproval == true)\r\r\nmandatory(tw.local.odcRequest.compcheckerComment, \"tw.local.odcRequest.compcheckerComment\");\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "name": "Trade Compliance", "lastModified": "1700596998643", "lastModifiedBy": "heba", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.35d64c8d-b9be-4b4d-b426-52b3d023705d", "2025.35d64c8d-b9be-4b4d-b426-52b3d023705d"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "63f01549-b889-4af5-aaeb-0faea1ec8f6f", "versionId": "86e4fd32-c430-438d-a46b-870d97e311a2", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.f0b0fe9f-384e-4473-91b0-464179607ecc\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":50,\"y\":188,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"826774b1-f9a8-4693-9cfe-1ad66c37dd65\"},{\"targetRef\":\"2025.335301a8-53b9-4997-b289-0e7f6704abba\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"Start To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f0b0fe9f-384e-4473-91b0-464179607ecc\",\"sourceRef\":\"826774b1-f9a8-4693-9cfe-1ad66c37dd65\"},{\"startQuantity\":1,\"outgoing\":[\"2027.4bf97862-f836-4e06-9bfc-81c838970a74\"],\"incoming\":[\"2027.f0b0fe9f-384e-4473-91b0-464179607ecc\"],\"default\":\"2027.4bf97862-f836-4e06-9bfc-81c838970a74\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":243,\"y\":165,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Initialization Script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.335301a8-53b9-4997-b289-0e7f6704abba\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.odcRequest.stepLog = {};\\r\\ntw.local.odcRequest.stepLog.startTime = new Date();\\r\\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT07; \\r\\n\\r\\n tw.local.odcRequest.appInfo.stepName= tw.epv.ScreenNames.CACT07;\\r\\n tw.local.odcRequest.appInfo.appID = tw.system.processInstance.id;\\r\\n \\r\\n tw.local.actionConditions = {};\\r\\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\\r\\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT07;\\r\\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\\r\\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;\\r\\n\\r\\n\\/*Visibilty Conditions*\\/\\r\\n\\/*Basic Details CV Visibility*\\/\\r\\n\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\/\\r\\nif(tw.local.odcRequest.requestNature.value == \\\"update\\\"){\\r\\n\\ttw.local.parentRequestNoVIS = \\\"READONLY\\\";\\r\\n}\\t\\r\\nelse{\\r\\n\\ttw.local.parentRequestNoVIS = \\\"None\\\";\\r\\n}\\t\\r\\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\\r\\n\\ttw.local.contractStageVIS = \\\"READONLY\\\";\\r\\n}\\r\\nelse{\\r\\ntw.local.contractStageVIS = \\\"NONE\\\";\\r\\n}\\r\\nif(tw.local.fromTradeFo == true)\\r\\n{\\r\\n\\ttw.local.tradefoVis = \\\"Editable\\\";\\r\\n\\ttw.local.exehubMkrVis = \\\"None\\\";\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\ttw.local.tradefoVis = \\\"None\\\";\\r\\n\\ttw.local.exehubMkrVis = \\\"Editable\\\";\\r\\n}\\r\\n \"]}},{\"startQuantity\":1,\"outgoing\":[\"2027.02254107-cf62-4386-b74b-6666a37e9ea6\"],\"incoming\":[\"2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1\"],\"default\":\"2027.02254107-cf62-4386-b74b-6666a37e9ea6\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":801,\"y\":148,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Status and substatus\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.5e719de4-0f51-4f78-875d-e8b72a537cae\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\r\\nif(tw.local.compApprovalInit == tw.epv.userRole.CACT03)\\r\\n{\\r\\n\\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.returnToMaker;\\r\\n\\ttw.local.odcRequest.appInfo.status =\\\" In Approval \\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus =\\\"Waiting Trade Fo  Maker Approval\\\";\\r\\n}\\r\\nelse\\r\\n{\\r\\n\\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.approveRequest;\\r\\n\\t\\ttw.local.odcRequest.appInfo.status    = \\\"In Execution\\\";\\r\\n\\ttw.local.odcRequest.appInfo.subStatus = \\\"Pending Execution Hub Processing\\\";\\r\\n}\"]}},{\"targetRef\":\"2025.19273870-c44a-41f8-83e4-94bd44345007\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Trade Compliance\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4bf97862-f836-4e06-9bfc-81c838970a74\",\"sourceRef\":\"2025.335301a8-53b9-4997-b289-0e7f6704abba\"},{\"targetRef\":\"2025.6a84d426-165c-459f-8ceb-d6006c6ad20d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.02254107-cf62-4386-b74b-6666a37e9ea6\",\"sourceRef\":\"2025.5e719de4-0f51-4f78-875d-e8b72a537cae\"},{\"outgoing\":[\"2027.18112dba-24b4-45b9-998c-931522e4f8da\"],\"incoming\":[\"2027.638a3b48-b2b8-425b-a512-66d9cf9a605b\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.18112dba-24b4-45b9-998c-931522e4f8da\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":387,\"y\":85,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2\"},{\"targetRef\":\"2025.6125901b-f21b-438b-9845-846e04b2e62d\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"1f95c90f-8b19-4dfc-9081-1fefb92374d6\",\"coachEventPath\":\"ODC_Creation_Template1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightTop\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Get Required Documents\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1\",\"sourceRef\":\"2025.19273870-c44a-41f8-83e4-94bd44345007\"},{\"targetRef\":\"2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"d15989d6-b5f4-4534-b936-4e6cb29120ac\",\"coachEventPath\":\"ODC_Creation_Template1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topLeft\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.638a3b48-b2b8-425b-a512-66d9cf9a605b\",\"sourceRef\":\"2025.19273870-c44a-41f8-83e4-94bd44345007\"},{\"targetRef\":\"2025.19273870-c44a-41f8-83e4-94bd44345007\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Trade Compliance\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.18112dba-24b4-45b9-998c-931522e4f8da\",\"sourceRef\":\"2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2\"},{\"outgoing\":[\"2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1\",\"2027.39733aac-7ee5-4bc2-8ae3-11facf422a16\"],\"incoming\":[\"2027.718ac925-7d5c-4692-9a49-8441c94e6b80\"],\"default\":\"2027.39733aac-7ee5-4bc2-8ae3-11facf422a16\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":64,\"x\":709,\"y\":169,\"declaredType\":\"TNodeVisualInfo\",\"height\":51}],\"preAssignmentScript\":[]},\"name\":\"Have Errors\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c\"},{\"startQuantity\":1,\"outgoing\":[\"2027.718ac925-7d5c-4692-9a49-8441c94e6b80\"],\"incoming\":[\"2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1\"],\"default\":\"2027.718ac925-7d5c-4692-9a49-8441c94e6b80\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":560,\"y\":150,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.6125901b-f21b-438b-9845-846e04b2e62d\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\" tw.local.errorMessage =\\\"\\\";\\r\\n var mandatoryTriggered = false;\\r\\n \\r\\n\\/************************************************************************************************************************\\r\\n\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n************************************************************************************************************************\\/\\r\\n\\/\\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\\r\\nmandatory(tw.local.odcRequest.stepLog.action, \\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\r\\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\\r\\n\\tmandatory(tw.local.odcRequest.stepLog.returnReason, \\\"tw.local.odcRequest.stepLog.returnReason\\\");\\r\\n\\t\\r\\n\\/\\/-------------------------------------------------Trade Fo and compliance comment validation ------------------------------\\r\\nif(tw.local.odcRequest.complianceApproval == true)\\r\\nmandatory(tw.local.odcRequest.compcheckerComment, \\\"tw.local.odcRequest.compcheckerComment\\\");\\r\\n\\r\\nfunction mandatory(field , fieldName)\\r\\n{\\r\\n\\tif (field == null || field == undefined )\\r\\n\\t{\\r\\n\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\telse\\r\\n\\t{\\t\\t\\t\\r\\n\\t\\tswitch (typeof field)\\r\\n\\t\\t{\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\r\\n\\t\\t\\tdefault:\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\/\\/ VALIDATE DATE OBJECT\\r\\n\\t\\t\\t\\tif( field && field.getTime && isFinite(field.getTime()) ) {}\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\telse\\r\\n\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\t\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n{\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n}\\r\\n\\t\\r\\n\\t\"]}},{\"targetRef\":\"2025.5e719de4-0f51-4f78-875d-e8b72a537cae\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1\",\"sourceRef\":\"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c\"},{\"targetRef\":\"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Have Errors\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.718ac925-7d5c-4692-9a49-8441c94e6b80\",\"sourceRef\":\"2025.6125901b-f21b-438b-9845-846e04b2e62d\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"errorExist\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.3cb681c2-3b0b-4631-b001-0a2c882b9f06\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.3039db6f-7017-4dac-804b-2c33c08ae807\"},{\"itemSubjectRef\":\"itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e\",\"name\":\"documentsTypesSelected\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.f250f757-7c1c-4824-a52b-c7a99c4fa5cc\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"validation\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d5955721-5c73-4eea-a4b2-ff7863f4acbd\"},{\"outgoing\":[\"2027.638a3b48-b2b8-425b-a512-66d9cf9a605b\",\"2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1\"],\"incoming\":[\"2027.18112dba-24b4-45b9-998c-931522e4f8da\",\"2027.4bf97862-f836-4e06-9bfc-81c838970a74\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":368,\"y\":165,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cc84854c-2026-4a3e-80ef-81384fc0003e\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"82d1a57b-8783-4495-8acb-f6ff9e5e94c6\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c51f9a43-e803-4766-8f79-f850b6aea066\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"785affde-d6ca-4733-87f4-432d7648950d\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"30568523-626a-4a48-8346-58e2b18fc3e1\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0d4bc528-b1fe-49b5-8769-a397d283f428\",\"optionName\":\"parentRequestNoVis\",\"value\":\"tw.local.parentRequestNoVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"910fd496-ea6a-469c-87ef-205956544fd7\",\"optionName\":\"contractStageVIS\",\"value\":\"tw.local.contractStageVIS\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"5a46ae9d-72a1-40b3-894b-5963f3933430\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f617bcdb-7f12-4bbe-806e-52125ae8974b\",\"optionName\":\"@label\",\"value\":\"Customer Information\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6390e972-5962-48c0-8d54-27af1f5ba356\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b9a3ba97-842f-4845-853d-dbf1617e4e40\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9f9b6ee9-718e-449e-807b-1d3c8bf1b714\",\"optionName\":\"customerInfoVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"759f2a5b-d9d2-48f5-8d9d-c4e30842d018\",\"optionName\":\"listsVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f1d938c2-a9be-41d5-8d97-c2cc5622a13d\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"19e099d3-c648-42a4-878d-f530ee4296df\",\"optionName\":\"@label\",\"value\":\"Financial Details Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3947c921-f3b8-4e49-82aa-a310e7375aac\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fe5afcb7-8388-4935-83bd-f6dcf7773ace\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1cb9f512-1af3-4b1f-8baa-62fb6df1e619\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8f214fca-6653-4a44-86b5-78bf839c7ca6\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"Readonly\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"7bcb31e8-e1e8-4427-8dce-03cf8df12623\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4b348381-cd08-4a0a-83f2-ea0b8b1b898b\",\"optionName\":\"@label\",\"value\":\"Attachment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"837dcd17-775c-4efe-841c-fb80fb394370\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"20aaf5df-642a-437f-8625-da456863acd4\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"91f8c0f7-58f8-41d2-88ce-e383830089a6\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ddb36141-5cd7-4bb0-8413-978aa7a3017e\",\"optionName\":\"@label\",\"value\":\"DC History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b5136aad-7b4e-4ad3-8296-3a79e9a668c0\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b9993545-2227-48e4-8ef6-41caa27b65fd\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f7bb56ab-c9b5-480b-845d-2ad7594ec4c9\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"c0c066c9-2294-4106-87b2-6d8b3112e468\"}],\"layoutItemId\":\"Tabs1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"01a07a4f-74ed-4008-89f6-b6f941f1dc79\",\"optionName\":\"@label\",\"value\":\"Tabs\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f84296a9-6d7d-4ddb-807d-e6eaae1ef446\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"59ebdb84-1589-4bcd-8d1e-f6823a463245\",\"optionName\":\"@width\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"99%\\\"}]}\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f6c61f00-858b-4520-8711-5a487139e947\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"6c7ce844-c89c-4e15-8f09-59f794ee9606\"}],\"layoutItemId\":\"ODC_Creation_Template1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"006ab33a-a3b8-4ee5-8305-1441e543a98b\",\"optionName\":\"@label\",\"value\":\"ODC Creation Template\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"194e54fe-1947-4dca-8ede-93af1121fbf7\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f080343e-1909-41f1-8a4b-b9271db352fd\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9d93c440-8136-4604-81b3-a47ad834a6f1\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"98f98ca8-116e-4dd0-84d8-971a786f1cdb\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"eeae2370-a75b-455e-82d5-0e36ada37cb0\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bb0aadf6-6aa3-4cee-8acf-171607e7d06f\",\"optionName\":\"complianceApprovalVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"baa4fe56-12fe-476a-88a1-6501dcd00dcd\",\"optionName\":\"terminateReasonVIS\",\"value\":\"None\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9b629cbc-23a4-4a03-8270-6455bdf3d818\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"59672ff6-0244-4b90-818c-97d590125c47\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e7e763f6-3093-4aab-8f5e-ca10fecfb022\",\"optionName\":\"approvalComment\",\"value\":\"tw.local.odcRequest.approvalComment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"94da15a9-9325-4e3a-8962-c67d096def46\",\"optionName\":\"approvalCommentVIS\",\"value\":\"Editable\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"325c9ce6-6791-4935-8626-f3e7cc54881b\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"Readonly\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d469bb0d-fb91-4c51-8dd6-69a491f4892f\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"Readonly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"dd0b42b4-0097-4c7c-8ed4-dec6711a0f9e\",\"optionName\":\"tradeFoComment\",\"value\":\"tw.local.odcRequest.tradeFoComment\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bbb5c4d8-3490-4be7-8d15-658463baf634\",\"optionName\":\"exeHubMkrComment\",\"value\":\"tw.local.odcRequest.exeHubMkrComment\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"22b5585c-4b30-4885-8c05-000ba283130b\",\"optionName\":\"compcheckerComment\",\"value\":\"tw.local.odcRequest.compcheckerComment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b65420f1-12d6-4e0c-8bbe-765ea773067c\",\"optionName\":\"compcheckerCommentVIS\",\"value\":\"Editable\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"804e9295-a6d8-4814-8c75-ded5d13cda77\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Trade Compliance\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.19273870-c44a-41f8-83e4-94bd44345007\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNoVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ccc1eddc-ac3e-4636-845d-d8899c8f3629\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a96defa1-4538-4300-87f3-ecf7b0b7cec4\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = {};\\nautoObject.screenName = \\\"\\\";\\nautoObject.userRole = \\\"\\\";\\nautoObject.complianceApproval = false;\\nautoObject.lastStepAction = \\\"\\\";\\nautoObject.subStatus = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.49e16d32-2f3b-40ab-8e9b-b210917ef3a1\"},{\"incoming\":[\"2027.39733aac-7ee5-4bc2-8ae3-11facf422a16\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":705,\"y\":38,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.26bb7a03-2343-47a3-85a9-81c05ecb5530\"},{\"targetRef\":\"2025.26bb7a03-2343-47a3-85a9-81c05ecb5530\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"no\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.39733aac-7ee5-4bc2-8ae3-11facf422a16\",\"sourceRef\":\"2025.3d21978a-5764-41b9-bf92-a51bcfc0783c\"},{\"startQuantity\":1,\"outgoing\":[\"2027.5038ce81-b484-4111-8dd7-c85220b46cb3\"],\"incoming\":[\"2027.02254107-cf62-4386-b74b-6666a37e9ea6\"],\"default\":\"2027.5038ce81-b484-4111-8dd7-c85220b46cb3\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":912,\"y\":148,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Update request state and status in DB\",\"dataInputAssociation\":[{\"targetRef\":\"2055.a84d91ec-e620-4417-85c4-7dd7db58ff31\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.c5194a72-2de4-483f-823c-47d5b98b572c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.3974caa7-9f10-4f46-8275-17080a25476e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.b05449cf-c459-4405-809c-888b00e3e968\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}]},{\"targetRef\":\"2055.b537f107-9e83-46e0-8979-6fe5796c7a7d\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.stepName\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.6a84d426-165c-459f-8ceb-d6006c6ad20d\",\"calledElement\":\"1.2cab04cd-6063-4a13-b148-ec9788e07bf4\"},{\"targetRef\":\"2025.509f906a-c362-44e5-81e5-0364f769022b\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5038ce81-b484-4111-8dd7-c85220b46cb3\",\"sourceRef\":\"2025.6a84d426-165c-459f-8ceb-d6006c6ad20d\"},{\"outgoing\":[\"2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881\"],\"incoming\":[\"2027.5038ce81-b484-4111-8dd7-c85220b46cb3\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":1036,\"y\":148,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881\",\"name\":\"Audit ODC Request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.afad40c5-a38b-475c-8154-b4dabd94b6fe\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.509f906a-c362-44e5-81e5-0364f769022b\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.254cf8eb-2743-4c53-8c52-e51c8c22884e\"]}],\"calledElement\":\"1.7ee96dd0-834b-44cb-af41-b21585627e49\"},{\"incoming\":[\"2027.59fef372-1bac-477d-8a36-085033334685\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1476,\"y\":170,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}],\"preAssignmentScript\":[]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff\"},{\"outgoing\":[\"2027.734d781b-620a-4a45-8c30-281afce1b505\"],\"incoming\":[\"2027.4f838403-693d-4f4a-8df1-27d6875ec647\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":1225,\"y\":149,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.734d781b-620a-4a45-8c30-281afce1b505\",\"name\":\"Audit Request History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.userRole.CACT07\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"outgoing\":[\"2027.59fef372-1bac-477d-8a36-085033334685\",\"2027.d8848e36-792f-45dd-881c-90030cb18e31\"],\"incoming\":[\"2027.734d781b-620a-4a45-8c30-281afce1b505\"],\"default\":\"2027.d8848e36-792f-45dd-881c-90030cb18e31\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1343,\"y\":168,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.d17dee7b-c083-4da1-8247-34274a828f10\"},{\"outgoing\":[\"2027.4f838403-693d-4f4a-8df1-27d6875ec647\",\"2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc\"],\"incoming\":[\"2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881\"],\"default\":\"2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1135,\"y\":168,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Audited?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852\"},{\"incoming\":[\"2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc\",\"2027.d8848e36-792f-45dd-881c-90030cb18e31\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1223,\"y\":247,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0\"},{\"targetRef\":\"2025.d17dee7b-c083-4da1-8247-34274a828f10\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Audited\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.734d781b-620a-4a45-8c30-281afce1b505\",\"sourceRef\":\"2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a\"},{\"targetRef\":\"2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.59fef372-1bac-477d-8a36-085033334685\",\"sourceRef\":\"2025.d17dee7b-c083-4da1-8247-34274a828f10\"},{\"targetRef\":\"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Audited?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881\",\"sourceRef\":\"2025.509f906a-c362-44e5-81e5-0364f769022b\"},{\"targetRef\":\"2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.4f838403-693d-4f4a-8df1-27d6875ec647\",\"sourceRef\":\"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852\"},{\"targetRef\":\"2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc\",\"sourceRef\":\"2025.8b4a5787-28e3-49d2-896d-ef1528ec5852\"},{\"targetRef\":\"2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"rightCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d8848e36-792f-45dd-881c-90030cb18e31\",\"sourceRef\":\"2025.d17dee7b-c083-4da1-8247-34274a828f10\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"tradefoVis\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.bb63e567-928d-4798-8a5d-62378b7e95f5\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"exehubMkrVis\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.5d310b3c-3f82-443d-813d-6d276cfc9944\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"1008d2fb-8e09-4963-8860-a910eda74e70\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"7f504969-a9fd-4c9a-864d-f8d53c95e0fc\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Trade Compliance\",\"declaredType\":\"globalUserTask\",\"id\":\"1.7276be8c-00f4-4766-949c-bcd033b050c3\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.0f59046c-cb7e-4393-bec7-d27739cecd93\"},{\"itemSubjectRef\":\"itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e\",\"name\":\"attachment\",\"isCollection\":true,\"id\":\"2055.f89a842a-9d83-45bf-adeb-2837b1beb056\"},{\"itemSubjectRef\":\"itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49\",\"name\":\"complianceComments\",\"isCollection\":true,\"id\":\"2055.36778ac9-0d81-4996-991e-f909c6f6aa5a\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.5afdc518-a04e-4a57-8dbe-c5eeb28f9e3e\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"104d3792-ae24-4ba9-8c92-9945eea81cb8\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"249442ab-2ec7-4bfa-850c-96de06de549a\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"8b48a36e-e195-47ae-8108-60b7fb639e57\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"616db28e-55e0-4f9e-8b37-f39f1ccded64\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{}],\"outputSet\":[{}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"var autoObject = {};\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = {};\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = [];\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = [];\\nautoObject.FcCollections.listOfAccounts[0] = {};\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.MultiTenorDates = [];\\nautoObject.MultiTenorDates[0] = {};\\nautoObject.MultiTenorDates[0].date = new Date();\\nautoObject.MultiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.Drawer = {};\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = {};\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = {};\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = {};\\nautoObject.Parties.partyTypes.partyCIF = 0;\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].rate = 0.0;\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].minimumAmount = 0.0;\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.initiator = \\\"\\\";\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.6102a850-faf2-401e-9c7a-d8f3a6215da0\"},{\"itemSubjectRef\":\"itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e\",\"name\":\"attachment\",\"isCollection\":true,\"id\":\"2055.6c11359f-601f-4a8a-adab-80428b282316\"},{\"itemSubjectRef\":\"itm.12.b698dbfb-84da-40a5-9db3-676815055e65\",\"name\":\"ECMproperties\",\"isCollection\":false,\"id\":\"2055.3502bdff-abe8-4815-b2bb-71d90d6236ce\"},{\"itemSubjectRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"name\":\"folderId\",\"isCollection\":false,\"id\":\"2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"compApprovalInit\",\"isCollection\":false,\"id\":\"2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"fromTradeFo\",\"isCollection\":false,\"id\":\"2055.dd027994-1915-4bca-8452-c0b7021d8794\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"1.e1c94f86-a87e-4418-8c6b-8725c6ccccea\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6102a850-faf2-401e-9c7a-d8f3a6215da0", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "def18e39-b298-4c2a-adb8-31ad18dca927", "versionId": "775527d3-b5e0-4904-acbb-f2ef7326f873"}, {"name": "attachment", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6c11359f-601f-4a8a-adab-80428b282316", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "1", "isArrayOf": "true", "classId": "/12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "b6701c74-76bf-4173-9f00-306ed7a06d07", "versionId": "e6a773fa-ab7e-478a-b39c-fbb920dffba0"}, {"name": "ECMproperties", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.3502bdff-abe8-4815-b2bb-71d90d6236ce", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "1", "isArrayOf": "false", "classId": "/12.b698dbfb-84da-40a5-9db3-676815055e65", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "c24e2dbe-b5bb-432c-b807-74187b8a3909", "versionId": "a982fe26-8c17-49c8-99a0-43355b42f83d"}, {"name": "folderId", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "1", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "a598b96d-de6b-4d9e-944b-5ced3080e6c1", "versionId": "8450aadc-d751-4742-9b84-26e2920292c8"}, {"name": "compApprovalInit", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "59800dc3-0f46-4b86-8069-779c2d30f6af", "versionId": "4fbf8206-d563-4c70-8419-************"}, {"name": "fromTradeFo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.dd027994-1915-4bca-8452-c0b7021d8794", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7791eea8-b8b1-4980-b712-994b62cd8854", "versionId": "99f26644-dd12-454b-a885-9a580054ba00"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0f59046c-cb7e-4393-bec7-d27739cecd93", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "7", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "daf07137-a150-4df3-a27e-c0b8b74991d8", "versionId": "c0606dc6-00ac-4d5b-8d67-7f4b59a7e68b"}, {"name": "attachment", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.f89a842a-9d83-45bf-adeb-2837b1beb056", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "2", "isArrayOf": "true", "classId": "/12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "9d832f8e-4cfc-4d6d-aa6c-f7d84aba6a5a", "versionId": "938f1161-e080-45cc-b3a3-2eb18df54b3d"}, {"name": "complianceComments", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.36778ac9-0d81-4996-991e-f909c6f6aa5a", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "parameterType": "2", "isArrayOf": "true", "classId": "/12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "seq": "9", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "699cc9d9-de14-4311-8ea0-95915fd4b004", "versionId": "770a50a5-593f-4dab-a7e4-5ba75dc1621d"}], "processVariable": [{"name": "errorExist", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3cb681c2-3b0b-4631-b001-0a2c882b9f06", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0aa13414-eb43-4f14-8233-90a6a0d2855f", "versionId": "f62df95c-56b6-4095-909a-53d0f480983d"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3039db6f-7017-4dac-804b-2c33c08ae807", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ebd25995-7b96-46e4-9a48-77b105110041", "versionId": "13f17e74-9c0b-461b-a058-7dfc18c9fea0"}, {"name": "documentsTypesSelected", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f250f757-7c1c-4824-a52b-c7a99c4fa5cc", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "/12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c6855578-4e30-483e-816d-7c503e1f46de", "versionId": "695ae450-c8da-47f7-828a-374f40370ecd"}, {"name": "validation", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d5955721-5c73-4eea-a4b2-ff7863f4acbd", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "07e8df0f-6daa-44af-bde9-97557cb319da", "versionId": "fdd3b65a-4ec6-451b-a38e-05bd1316654d"}, {"name": "parentRequestNoVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ccc1eddc-ac3e-4636-845d-d8899c8f3629", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bda77c28-9213-4175-aaff-abed762c173a", "versionId": "7a518011-fa43-4b90-b3c1-e75fb5694dbb"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a96defa1-4538-4300-87f3-ecf7b0b7cec4", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "716adec0-02e6-4fd6-9cff-735d04395434", "versionId": "6c3e7b6f-29a1-4823-af3b-82472cca1ef1"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.49e16d32-2f3b-40ab-8e9b-b210917ef3a1", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c9f66afc-b1de-41b8-85c7-8ed1859a2122", "versionId": "5deac724-d8af-4a66-97fa-699d7e7de6cc"}, {"name": "tradefoVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.bb63e567-928d-4798-8a5d-62378b7e95f5", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d8587cf6-5948-4bb3-9ba7-bcf77c097f78", "versionId": "340ca6c8-99e7-44a3-82f4-925641c84625"}, {"name": "exehubMkrVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5d310b3c-3f82-443d-813d-6d276cfc9944", "description": {"isNull": "true"}, "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d3d50f0f-17c8-4db9-bc14-eafbfc15207c", "versionId": "d21037fe-9b3b-49e7-8653-dbbd0b8ed6d8"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.509f906a-c362-44e5-81e5-0364f769022b", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "name": "Audit ODC Request", "tWComponentName": "SubProcess", "tWComponentId": "3012.4e2ab6a7-bb10-424e-96f6-6a78f917d368", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:358c", "versionId": "1da744ca-be21-41ca-bc41-8f4ed36fcfa7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4e2ab6a7-bb10-424e-96f6-6a78f917d368", "attachedProcessRef": "/1.7ee96dd0-834b-44cb-af41-b21585627e49", "guid": "74098f67-fbd5-47f9-8dc1-bc7c5c00bb1c", "versionId": "d67b7c40-fc01-4e3c-9201-ea5802b6e18c"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.35d64c8d-b9be-4b4d-b426-52b3d023705d", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.08b2bc37-4ef2-4b7d-a7a5-acb27a4384c2", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c3e5ad11373681e2:14b3f188:1896fd422e2:6199", "versionId": "a8a6e863-679e-4023-b99c-6dec9fb17f08", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "name": "Audit Request History", "tWComponentName": "SubProcess", "tWComponentId": "3012.288bee4c-ccd1-4142-8c22-b1f006579e4a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:358d", "versionId": "b50696ce-2ff1-4e7c-a22d-12677d57a272", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.288bee4c-ccd1-4142-8c22-b1f006579e4a", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "73b39987-d316-4719-9d9d-c1d2a3c827a2", "versionId": "2a0bf44f-4ce8-4a7e-aa38-26572d54e5c2"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f85a0204-3ac0-433d-95ae-699891512ca9", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.e86921f2-ca34-4973-9ae6-681cd4481d3c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:c3e5ad11373681e2:14b3f188:1896fd422e2:619a", "versionId": "d723eb32-288e-4336-8067-adc1675dc532", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.e86921f2-ca34-4973-9ae6-681cd4481d3c", "haltProcess": "false", "guid": "ebb3195d-02e9-42f0-9a86-75473dcef6e9", "versionId": "621e70d6-23ed-4144-b04e-26d0f07f4a84"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6a84d426-165c-459f-8ceb-d6006c6ad20d", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "name": "Update request state and status in DB", "tWComponentName": "SubProcess", "tWComponentId": "3012.d8f087aa-00df-4de1-8cd8-c2f6a6dc1b05", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:aff098473ecd546d:1d42df0a:18b1b2b8841:-775c", "versionId": "e25a4065-dfe2-4821-bbc0-6cab4dceb9f1", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.d8f087aa-00df-4de1-8cd8-c2f6a6dc1b05", "attachedProcessRef": "/1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "guid": "39cf13e4-fb84-4d2f-9fb4-ec214de9fd84", "versionId": "b748777a-dd0a-43b0-9541-dbaf03bb4cd5"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.7e94a1be-b7aa-4d2b-95ff-f20af88548e8", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "guid": "d009b1b5-2c7b-4ad0-beca-7aa8e502d430", "versionId": "846cf66f-cda8-41b3-bdf8-1e6d39111a5d"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.b1b6919b-6072-4258-94af-************", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "guid": "416d3f97-c58b-4085-a21a-e01feb0bd0d0", "versionId": "8ea604fd-aca0-429c-8daf-6d9dad4dcb8f"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.1e59223b-a3d8-44e2-a32e-deca2cf021b6", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "guid": "2e6165fd-a16f-4143-b887-d33ad99892b4", "versionId": "98b90e71-da67-472b-95fa-d74204e79e06"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.b06ca682-1256-4f73-bd2d-9d567ff93be8", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "guid": "2000bd14-c858-4d53-96ce-95f8deda6c5d", "versionId": "9929532f-40d6-471f-813f-1925aa849c6f"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.f6e0acea-3d75-44c9-b5cd-e086fef2108f", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "guid": "6ad117ad-f00b-4934-a2f1-73e55fde0dec", "versionId": "69e80b1d-8d1f-4644-8526-49e7736a608c"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "1.e1c94f86-a87e-4418-8c6b-8725c6ccccea", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:globalUserTask": {"implementation": "##unspecified", "name": "Trade Compliance", "id": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:userTaskImplementation": {"processType": "None", "isClosed": "false", "id": "7f504969-a9fd-4c9a-864d-f8d53c95e0fc", "ns16:startEvent": {"name": "Start", "id": "826774b1-f9a8-4693-9cfe-1ad66c37dd65", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "50", "y": "188", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.f0b0fe9f-384e-4473-91b0-464179607ecc"}, "ns16:sequenceFlow": [{"sourceRef": "826774b1-f9a8-4693-9cfe-1ad66c37dd65", "targetRef": "2025.335301a8-53b9-4997-b289-0e7f6704abba", "name": "Start To Coach", "id": "2027.f0b0fe9f-384e-4473-91b0-464179607ecc", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.335301a8-53b9-4997-b289-0e7f6704abba", "targetRef": "2025.19273870-c44a-41f8-83e4-94bd44345007", "name": "To Trade Compliance", "id": "2027.4bf97862-f836-4e06-9bfc-81c838970a74", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}}}, {"sourceRef": "2025.5e719de4-0f51-4f78-875d-e8b72a537cae", "targetRef": "2025.6a84d426-165c-459f-8ceb-d6006c6ad20d", "name": "To End", "id": "2027.02254107-cf62-4386-b74b-6666a37e9ea6", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.19273870-c44a-41f8-83e4-94bd44345007", "targetRef": "2025.6125901b-f21b-438b-9845-846e04b2e62d", "name": "To Get Required Documents", "id": "2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightTop", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "1f95c90f-8b19-4dfc-9081-1fefb92374d6", "ns3:coachEventPath": "ODC_Creation_Template1/submit"}}}, {"sourceRef": "2025.19273870-c44a-41f8-83e4-94bd44345007", "targetRef": "2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2", "name": "To Postpone", "id": "2027.638a3b48-b2b8-425b-a512-66d9cf9a605b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topLeft", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "d15989d6-b5f4-4534-b936-4e6cb29120ac", "ns3:coachEventPath": "ODC_Creation_Template1/saveState"}}}, {"sourceRef": "2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2", "targetRef": "2025.19273870-c44a-41f8-83e4-94bd44345007", "name": "To Trade Compliance", "id": "2027.18112dba-24b4-45b9-998c-931522e4f8da", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "topRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.3d21978a-5764-41b9-bf92-a51bcfc0783c", "targetRef": "2025.5e719de4-0f51-4f78-875d-e8b72a537cae", "name": "no", "id": "2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.6125901b-f21b-438b-9845-846e04b2e62d", "targetRef": "2025.3d21978a-5764-41b9-bf92-a51bcfc0783c", "name": "To Have Errors", "id": "2027.718ac925-7d5c-4692-9a49-8441c94e6b80", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.3d21978a-5764-41b9-bf92-a51bcfc0783c", "targetRef": "2025.26bb7a03-2343-47a3-85a9-81c05ecb5530", "name": "no", "id": "2027.39733aac-7ee5-4bc2-8ae3-11facf422a16", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.6a84d426-165c-459f-8ceb-d6006c6ad20d", "targetRef": "2025.509f906a-c362-44e5-81e5-0364f769022b", "name": "To End", "id": "2027.5038ce81-b484-4111-8dd7-c85220b46cb3", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a", "targetRef": "2025.d17dee7b-c083-4da1-8247-34274a828f10", "name": "To Audited", "id": "2027.734d781b-620a-4a45-8c30-281afce1b505", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}}}, {"sourceRef": "2025.d17dee7b-c083-4da1-8247-34274a828f10", "targetRef": "2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff", "name": "Yes", "id": "2027.59fef372-1bac-477d-8a36-085033334685", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.509f906a-c362-44e5-81e5-0364f769022b", "targetRef": "2025.8b4a5787-28e3-49d2-896d-ef1528ec5852", "name": "To Audited?", "id": "2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.8b4a5787-28e3-49d2-896d-ef1528ec5852", "targetRef": "2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a", "name": "Yes", "id": "2027.4f838403-693d-4f4a-8df1-27d6875ec647", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.8b4a5787-28e3-49d2-896d-ef1528ec5852", "targetRef": "2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0", "name": "No", "id": "2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.d17dee7b-c083-4da1-8247-34274a828f10", "targetRef": "2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0", "name": "No", "id": "2027.d8848e36-792f-45dd-881c-90030cb18e31", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "rightCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.4bf97862-f836-4e06-9bfc-81c838970a74", "name": "Initialization Script", "id": "2025.335301a8-53b9-4997-b289-0e7f6704abba", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "243", "y": "165", "width": "95", "height": "70"}}, "ns16:incoming": "2027.f0b0fe9f-384e-4473-91b0-464179607ecc", "ns16:outgoing": "2027.4bf97862-f836-4e06-9bfc-81c838970a74", "ns16:script": "tw.local.odcRequest.stepLog = {};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.stepLog.step = tw.epv.ScreenNames.CACT07; \r\r\n\r\r\n tw.local.odcRequest.appInfo.stepName= tw.epv.ScreenNames.CACT07;\r\r\n tw.local.odcRequest.appInfo.appID = tw.system.processInstance.id;\r\r\n \r\r\n tw.local.actionConditions = {};\r\r\ntw.local.actionConditions.complianceApproval= tw.local.odcRequest.complianceApproval;\r\r\ntw.local.actionConditions.screenName= tw.epv.ScreenNames.CACT07;\r\r\ntw.local.actionConditions.userRole= tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.lastStepAction= tw.epv.ScreenNames.CACT03;\r\r\n\r\r\n/*Visibilty Conditions*/\r\r\n/*Basic Details CV Visibility*/\r\r\n///////////////////////////////\r\r\nif(tw.local.odcRequest.requestNature.value == \"update\"){\r\r\n\ttw.local.parentRequestNoVIS = \"READONLY\";\r\r\n}\t\r\r\nelse{\r\r\n\ttw.local.parentRequestNoVIS = \"None\";\r\r\n}\t\r\r\nif(tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Create ||tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Recreate  || tw.local.odcRequest.BasicDetails.requestType == tw.epv.RequestType.Amendment){\r\r\n\ttw.local.contractStageVIS = \"READONLY\";\r\r\n}\r\r\nelse{\r\r\ntw.local.contractStageVIS = \"NONE\";\r\r\n}\r\r\nif(tw.local.fromTradeFo == true)\r\r\n{\r\r\n\ttw.local.tradefoVis = \"Editable\";\r\r\n\ttw.local.exehubMkrVis = \"None\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.tradefoVis = \"None\";\r\r\n\ttw.local.exehubMkrVis = \"Editable\";\r\r\n}\r\r\n "}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.02254107-cf62-4386-b74b-6666a37e9ea6", "name": "Set Status and substatus", "id": "2025.5e719de4-0f51-4f78-875d-e8b72a537cae", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "801", "y": "148", "width": "95", "height": "70"}}, "ns16:incoming": "2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1", "ns16:outgoing": "2027.02254107-cf62-4386-b74b-6666a37e9ea6", "ns16:script": "\r\r\nif(tw.local.compApprovalInit == tw.epv.userRole.CACT03)\r\r\n{\r\r\n\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.returnToMaker;\r\r\n\ttw.local.odcRequest.appInfo.status =\" In Approval \";\r\r\n\ttw.local.odcRequest.appInfo.subStatus =\"Waiting Trade Fo  Maker Approval\";\r\r\n}\r\r\nelse\r\r\n{\r\r\n\ttw.local.odcRequest.stepLog.action = tw.epv.CreationActions.approveRequest;\r\r\n\t\ttw.local.odcRequest.appInfo.status    = \"In Execution\";\r\r\n\ttw.local.odcRequest.appInfo.subStatus = \"Pending Execution Hub Processing\";\r\r\n}"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.718ac925-7d5c-4692-9a49-8441c94e6b80", "name": "validation", "id": "2025.6125901b-f21b-438b-9845-846e04b2e62d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "560", "y": "150", "width": "95", "height": "70", "color": "#95D087"}}, "ns16:incoming": "2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1", "ns16:outgoing": "2027.718ac925-7d5c-4692-9a49-8441c94e6b80", "ns16:script": " tw.local.errorMessage =\"\";\r\r\n var mandatoryTriggered = false;\r\r\n \r\r\n/************************************************************************************************************************\r\r\n/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n************************************************************************************************************************/\r\r\n//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\nmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\r\r\nif(tw.local.odcRequest.stepLog.action == tw.epv.CreationActions.returnToMaker)\r\r\n\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\t\r\r\n//-------------------------------------------------Trade Fo and compliance comment validation ------------------------------\r\r\nif(tw.local.odcRequest.complianceApproval == true)\r\r\nmandatory(tw.local.odcRequest.compcheckerComment, \"tw.local.odcRequest.compcheckerComment\");\r\r\n\r\r\nfunction mandatory(field , fieldName)\r\r\n{\r\r\n\tif (field == null || field == undefined )\r\r\n\t{\r\r\n\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\telse\r\r\n\t{\t\t\t\r\r\n\t\tswitch (typeof field)\r\r\n\t\t{\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\r\r\n\t\t\t\telse\r\r\n\t\t\t\t{\r\r\n\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n{\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n}\r\r\n\t\r\r\n\t"}], "ns16:intermediateThrowEvent": [{"name": "Postpone", "id": "2025.5036e163-29cb-4f73-be4e-75a2dfefbeb2", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "387", "y": "85", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.18112dba-24b4-45b9-998c-931522e4f8da"}, "ns16:incoming": "2027.638a3b48-b2b8-425b-a512-66d9cf9a605b", "ns16:outgoing": "2027.18112dba-24b4-45b9-998c-931522e4f8da", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page", "id": "2025.26bb7a03-2343-47a3-85a9-81c05ecb5530", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "705", "y": "38", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.39733aac-7ee5-4bc2-8ae3-11facf422a16", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.94d0412a-1e94-4745-8d52-ff8e3382fcc0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1223", "y": "247", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc", "2027.d8848e36-792f-45dd-881c-90030cb18e31"], "ns3:stayOnPageEventDefinition": ""}], "ns16:exclusiveGateway": [{"default": "2027.39733aac-7ee5-4bc2-8ae3-11facf422a16", "gatewayDirection": "Unspecified", "name": "Have Errors", "id": "2025.3d21978a-5764-41b9-bf92-a51bcfc0783c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "709", "y": "169", "width": "64", "height": "51"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.718ac925-7d5c-4692-9a49-8441c94e6b80", "ns16:outgoing": ["2027.caddc367-e6dd-46b0-8bd5-6b48259f37b1", "2027.39733aac-7ee5-4bc2-8ae3-11facf422a16"]}, {"default": "2027.d8848e36-792f-45dd-881c-90030cb18e31", "name": "Audited", "id": "2025.d17dee7b-c083-4da1-8247-34274a828f10", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1343", "y": "168", "width": "32", "height": "32"}}, "ns16:incoming": "2027.734d781b-620a-4a45-8c30-281afce1b505", "ns16:outgoing": ["2027.59fef372-1bac-477d-8a36-085033334685", "2027.d8848e36-792f-45dd-881c-90030cb18e31"]}, {"default": "2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc", "name": "Audited?", "id": "2025.8b4a5787-28e3-49d2-896d-ef1528ec5852", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1135", "y": "168", "width": "32", "height": "32"}}, "ns16:incoming": "2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881", "ns16:outgoing": ["2027.4f838403-693d-4f4a-8df1-27d6875ec647", "2027.06926698-9dfb-4c38-8ca5-9e2f4f7c4abc"]}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "errorExist", "id": "2056.3cb681c2-3b0b-4631-b001-0a2c882b9f06"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.3039db6f-7017-4dac-804b-2c33c08ae807"}, {"itemSubjectRef": "itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "isCollection": "true", "name": "documentsTypesSelected", "id": "2056.f250f757-7c1c-4824-a52b-c7a99c4fa5cc"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "validation", "id": "2056.d5955721-5c73-4eea-a4b2-ff7863f4acbd"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNoVIS", "id": "2056.ccc1eddc-ac3e-4636-845d-d8899c8f3629"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.a96defa1-4538-4300-87f3-ecf7b0b7cec4"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.49e16d32-2f3b-40ab-8e9b-b210917ef3a1", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.screenName = \"\";\r\nautoObject.userRole = \"\";\r\nautoObject.complianceApproval = false;\r\nautoObject.lastStepAction = \"\";\r\nautoObject.subStatus = \"\";\r\nautoObject", "useDefault": "false"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "tradefoVis", "id": "2056.bb63e567-928d-4798-8a5d-62378b7e95f5"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "exehubMkrVis", "id": "2056.5d310b3c-3f82-443d-813d-6d276cfc9944"}], "ns3:formTask": {"name": "Trade Compliance", "id": "2025.19273870-c44a-41f8-83e4-94bd44345007", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "368", "y": "165", "width": "95", "height": "70"}, "ns3:validationStayOnPagePaths": "okbutton"}, "ns16:incoming": ["2027.18112dba-24b4-45b9-998c-931522e4f8da", "2027.4bf97862-f836-4e06-9bfc-81c838970a74"], "ns16:outgoing": ["2027.638a3b48-b2b8-425b-a512-66d9cf9a605b", "2027.2bb14525-8979-4a2b-88dc-ae5dbd0767b1"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "804e9295-a6d8-4814-8c75-ded5d13cda77", "ns19:layoutItemId": "ODC_Creation_Template1", "ns19:configData": [{"ns19:id": "006ab33a-a3b8-4ee5-8305-1441e543a98b", "ns19:optionName": "@label", "ns19:value": "ODC Creation Template"}, {"ns19:id": "194e54fe-1947-4dca-8ede-93af1121fbf7", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "f080343e-1909-41f1-8a4b-b9271db352fd", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "9d93c440-8136-4604-81b3-a47ad834a6f1", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "98f98ca8-116e-4dd0-84d8-971a786f1cdb", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "eeae2370-a75b-455e-82d5-0e36ada37cb0", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "bb0aadf6-6aa3-4cee-8acf-171607e7d06f", "ns19:optionName": "complianceApprovalVis", "ns19:value": "None"}, {"ns19:id": "baa4fe56-12fe-476a-88a1-6501dcd00dcd", "ns19:optionName": "terminateReasonVIS", "ns19:value": "None"}, {"ns19:id": "9b629cbc-23a4-4a03-8270-6455bdf3d818", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "59672ff6-0244-4b90-818c-97d590125c47", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "e7e763f6-3093-4aab-8f5e-ca10fecfb022", "ns19:optionName": "approvalComment", "ns19:value": "tw.local.odcRequest.approvalComment", "ns19:valueType": "dynamic"}, {"ns19:id": "94da15a9-9325-4e3a-8962-c67d096def46", "ns19:optionName": "approvalCommentVIS", "ns19:value": "Editable"}, {"ns19:id": "325c9ce6-6791-4935-8626-f3e7cc54881b", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "<PERSON><PERSON><PERSON>", "ns19:valueType": "static"}, {"ns19:id": "d469bb0d-fb91-4c51-8dd6-69a491f4892f", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "<PERSON><PERSON><PERSON>", "ns19:valueType": "static"}, {"ns19:id": "dd0b42b4-0097-4c7c-8ed4-dec6711a0f9e", "ns19:optionName": "tradeFoComment", "ns19:value": "tw.local.odcRequest.tradeFoComment", "ns19:valueType": "dynamic"}, {"ns19:id": "bbb5c4d8-3490-4be7-8d15-658463baf634", "ns19:optionName": "exeHubMkrComment", "ns19:value": "tw.local.odcRequest.exeHubMkrComment", "ns19:valueType": "dynamic"}, {"ns19:id": "22b5585c-4b30-4885-8c05-000ba283130b", "ns19:optionName": "compcheckerComment", "ns19:value": "tw.local.odcRequest.compcheckerComment", "ns19:valueType": "dynamic"}, {"ns19:id": "b65420f1-12d6-4e0c-8bbe-765ea773067c", "ns19:optionName": "compcheckerCommentVIS", "ns19:value": "Editable"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "6c7ce844-c89c-4e15-8f09-59f794ee9606", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f6c61f00-858b-4520-8711-5a487139e947", "ns19:layoutItemId": "Tabs1", "ns19:configData": [{"ns19:id": "01a07a4f-74ed-4008-89f6-b6f941f1dc79", "ns19:optionName": "@label", "ns19:value": "Tabs"}, {"ns19:id": "f84296a9-6d7d-4ddb-807d-e6eaae1ef446", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "59ebdb84-1589-4bcd-8d1e-f6823a463245", "ns19:optionName": "@width", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"99%\"}]}"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "c0c066c9-2294-4106-87b2-6d8b3112e468", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "5a46ae9d-72a1-40b3-894b-5963f3933430", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "cc84854c-2026-4a3e-80ef-81384fc0003e", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "82d1a57b-8783-4495-8acb-f6ff9e5e94c6", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "c51f9a43-e803-4766-8f79-f850b6aea066", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "785affde-d6ca-4733-87f4-432d7648950d", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "None"}, {"ns19:id": "30568523-626a-4a48-8346-58e2b18fc3e1", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "0d4bc528-b1fe-49b5-8769-a397d283f428", "ns19:optionName": "parentRequestNoVis", "ns19:value": "tw.local.parentRequestNoVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "910fd496-ea6a-469c-87ef-205956544fd7", "ns19:optionName": "contractStageVIS", "ns19:value": "tw.local.contractStageVIS", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f1d938c2-a9be-41d5-8d97-c2cc5622a13d", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "f617bcdb-7f12-4bbe-806e-52125ae8974b", "ns19:optionName": "@label", "ns19:value": "Customer Information"}, {"ns19:id": "6390e972-5962-48c0-8d54-27af1f5ba356", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "b9a3ba97-842f-4845-853d-dbf1617e4e40", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "9f9b6ee9-718e-449e-807b-1d3c8bf1b714", "ns19:optionName": "customerInfoVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "759f2a5b-d9d2-48f5-8d9d-c4e30842d018", "ns19:optionName": "listsVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "7bcb31e8-e1e8-4427-8dce-03cf8df12623", "ns19:layoutItemId": "Financial_Details_Branch_CV1", "ns19:configData": [{"ns19:id": "19e099d3-c648-42a4-878d-f530ee4296df", "ns19:optionName": "@label", "ns19:value": "Financial Details Branch"}, {"ns19:id": "3947c921-f3b8-4e49-82aa-a310e7375aac", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "fe5afcb7-8388-4935-83bd-f6dcf7773ace", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "1cb9f512-1af3-4b1f-8baa-62fb6df1e619", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "8f214fca-6653-4a44-86b5-78bf839c7ca6", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "91f8c0f7-58f8-41d2-88ce-e383830089a6", "ns19:layoutItemId": "Attachment1", "ns19:configData": [{"ns19:id": "4b348381-cd08-4a0a-83f2-ea0b8b1b898b", "ns19:optionName": "@label", "ns19:value": "Attachment"}, {"ns19:id": "837dcd17-775c-4efe-841c-fb80fb394370", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "20aaf5df-642a-437f-8625-da456863acd4", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f7bb56ab-c9b5-480b-845d-2ad7594ec4c9", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "ddb36141-5cd7-4bb0-8413-978aa7a3017e", "ns19:optionName": "@label", "ns19:value": "DC History"}, {"ns19:id": "b5136aad-7b4e-4ad3-8296-3a79e9a668c0", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "b9993545-2227-48e4-8ef6-41caa27b65fd", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:callActivity": [{"calledElement": "1.2cab04cd-6063-4a13-b148-ec9788e07bf4", "default": "2027.5038ce81-b484-4111-8dd7-c85220b46cb3", "name": "Update request state and status in DB", "id": "2025.6a84d426-165c-459f-8ceb-d6006c6ad20d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "912", "y": "148", "width": "95", "height": "70"}}, "ns16:incoming": "2027.02254107-cf62-4386-b74b-6666a37e9ea6", "ns16:outgoing": "2027.5038ce81-b484-4111-8dd7-c85220b46cb3", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.a84d91ec-e620-4417-85c4-7dd7db58ff31", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.c5194a72-2de4-483f-823c-47d5b98b572c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.3974caa7-9f10-4f46-8275-17080a25476e", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b05449cf-c459-4405-809c-888b00e3e968", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b537f107-9e83-46e0-8979-6fe5796c7a7d", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.stepName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.7ee96dd0-834b-44cb-af41-b21585627e49", "default": "2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881", "name": "Audit ODC Request", "id": "2025.509f906a-c362-44e5-81e5-0364f769022b", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1036", "y": "148", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": "2027.5038ce81-b484-4111-8dd7-c85220b46cb3", "ns16:outgoing": "2027.67faf07d-0ec6-40b4-8ddb-8f49839bd881", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.afad40c5-a38b-475c-8154-b4dabd94b6fe", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.254cf8eb-2743-4c53-8c52-e51c8c22884e", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.734d781b-620a-4a45-8c30-281afce1b505", "name": "Audit Request History", "id": "2025.8adb21bf-a1e9-45cb-8dfe-4c0dc15cdc9a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1225", "y": "149", "width": "95", "height": "70"}, "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": "2027.4f838403-693d-4f4a-8df1-27d6875ec647", "ns16:outgoing": "2027.734d781b-620a-4a45-8c30-281afce1b505", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.epv.userRole.CACT07", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:endEvent": {"name": "End", "id": "2025.0c782db5-2aa0-41a3-8bc6-317ace9ad0ff", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1476", "y": "170", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.59fef372-1bac-477d-8a36-085033334685"}, "ns3:htmlHeaderTag": {"id": "1008d2fb-8e09-4963-8860-a910eda74e70", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "104d3792-ae24-4ba9-8c92-9945eea81cb8"}, {"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "249442ab-2ec7-4bfa-850c-96de06de549a"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "8b48a36e-e195-47ae-8108-60b7fb639e57"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "616db28e-55e0-4f9e-8b37-f39f1ccded64"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.5afdc518-a04e-4a57-8dbe-c5eeb28f9e3e"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.6102a850-faf2-401e-9c7a-d8f3a6215da0", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = {};\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = [];\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = [];\r\nautoObject.FcCollections.listOfAccounts[0] = {};\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.MultiTenorDates = [];\r\nautoObject.MultiTenorDates[0] = {};\r\nautoObject.MultiTenorDates[0].date = new Date();\r\nautoObject.MultiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.Drawer = {};\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = {};\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = {};\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = {};\r\nautoObject.Parties.partyTypes.partyCIF = 0;\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].rate = 0.0;\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].minimumAmount = 0.0;\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.initiator = \"\";\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject", "useDefault": "false"}}}, {"name": "attachment", "itemSubjectRef": "itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "isCollection": "true", "id": "2055.6c11359f-601f-4a8a-adab-80428b282316"}, {"name": "ECMproperties", "itemSubjectRef": "itm.12.b698dbfb-84da-40a5-9db3-676815055e65", "isCollection": "false", "id": "2055.3502bdff-abe8-4815-b2bb-71d90d6236ce"}, {"name": "folderId", "itemSubjectRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01", "isCollection": "false", "id": "2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7"}, {"name": "compApprovalInit", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c"}, {"name": "fromTradeFo", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.dd027994-1915-4bca-8452-c0b7021d8794"}], "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.0f59046c-cb7e-4393-bec7-d27739cecd93"}, {"name": "attachment", "itemSubjectRef": "itm.12.07383e61-26ca-4c95-9b61-00fbdbc9735e", "isCollection": "true", "id": "2055.f89a842a-9d83-45bf-adeb-2837b1beb056"}, {"name": "complianceComments", "itemSubjectRef": "itm.12.e4654440-58a7-47b2-8f98-3eaa9cccad49", "isCollection": "true", "id": "2055.36778ac9-0d81-4996-991e-f909c6f6aa5a"}], "ns16:inputSet": "", "ns16:outputSet": ""}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.81a70b0f-bcbf-48ca-a9d3-8bcd43c03ad8", "processId": "1.7276be8c-00f4-4766-949c-bcd033b050c3", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.35d64c8d-b9be-4b4d-b426-52b3d023705d", "2025.35d64c8d-b9be-4b4d-b426-52b3d023705d"], "endStateId": "Out", "toProcessItemId": ["2025.f85a0204-3ac0-433d-95ae-699891512ca9", "2025.f85a0204-3ac0-433d-95ae-699891512ca9"], "guid": "b131f68f-830c-4087-a2bd-8b9f9f8adcea", "versionId": "dc030826-afff-4603-9d31-63e8e3e26262", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}