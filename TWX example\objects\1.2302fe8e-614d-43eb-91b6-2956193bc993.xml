<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2302fe8e-614d-43eb-91b6-2956193bc993" name="Initiate ODC Process">
        <lastModified>1699520054207</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
        <errorHandlerItemId>2025.3fef31ef-99fc-471b-8796-1c23daa19be9</errorHandlerItemId>
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76b8</guid>
        <versionId>7e52ed01-6dbe-4b2e-b2e1-421b774f9c5c</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:651a1a6abf396537:64776e00:18baeba64af:255a" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.0fb58f07-d096-4ec8-8255-943ace2831b9"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":107,"y":186,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"94eb8d0f-ebc9-4403-8b97-5b8944ca4e29"},{"incoming":["3f66f592-cb04-439f-8325-1f2e8ecc9a4d","a1b36c32-c98f-42db-8685-ae5d76aa03f3","b0fbef09-c987-43e4-8185-d3711fae162f","77d41f45-95a5-45bc-8fe7-3a7bfad68d61","e6e4f61c-312e-4a01-822f-a72b5237ec42","096f719b-e0bb-4ad3-88a8-662265249cfa"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":940,"y":189,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ba"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"acf670a7-5e34-496c-8768-b18bbb4c09a4"},{"targetRef":"4b02a02d-49e7-477f-89fa-84162350bcc6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Request Type","declaredType":"sequenceFlow","id":"2027.0fb58f07-d096-4ec8-8255-943ace2831b9","sourceRef":"94eb8d0f-ebc9-4403-8b97-5b8944ca4e29"},{"startQuantity":1,"outgoing":["77d41f45-95a5-45bc-8fe7-3a7bfad68d61"],"incoming":["4f5524cb-d0f1-4b79-82db-7c1a6c25b45c"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":421,"y":89,"declaredType":"TNodeVisualInfo","height":70}]},"name":"start Amend  Process","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"d7efe1f4-4396-40af-8fb8-b4d7fb45108e","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar input = new tw.object.Map();\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\n \r\ntw.local.processName = tw.epv.ODCProcessName.create+\"\";\r\n\/\/tw.local.testData= tw.local.processName;\r\n\r\n\/\/if(tw.env.runningServerIP == \"************\")\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\n\/\/else\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\t\r\n\r\n\/\/tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\r\ntw.system.startProcessByName(tw.local.processName, input, false)"]}},{"outgoing":["4f5524cb-d0f1-4b79-82db-7c1a6c25b45c","bca20caf-ee0c-4bf4-8856-65b582207bc8","9ad63445-77ea-4b28-8a25-b1a81c64533d","a99a5322-5d68-4786-804d-67495a9f8aaf","********-9dd1-4da5-84d4-127606177a5e","dc094bde-97a9-4302-810e-edd5581e8cdd"],"incoming":["2027.0fb58f07-d096-4ec8-8255-943ace2831b9"],"default":"9ad63445-77ea-4b28-8a25-b1a81c64533d","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":137,"y":182,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":[]},"name":"Request Type","declaredType":"exclusiveGateway","id":"4b02a02d-49e7-477f-89fa-84162350bcc6"},{"startQuantity":1,"outgoing":["3f66f592-cb04-439f-8325-1f2e8ecc9a4d"],"incoming":["bca20caf-ee0c-4bf4-8856-65b582207bc8"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":530,"y":166,"declaredType":"TNodeVisualInfo","height":70}]},"name":"start collection process","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"841f3829-ef78-434b-8948-6b4406876e41","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar input = new tw.object.Map();\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\n\r\n\r\ntw.local.processName = tw.epv.ODCProcessName.collection+\"\";\r\n\r\n\/\/if(tw.env.runningServerIP == \"************\")\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\n\/\/else\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\t\r\n\r\n\/\/tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\r\ntw.system.startProcessByName(tw.local.processName, input, false);"]}},{"targetRef":"841f3829-ef78-434b-8948-6b4406876e41","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  ==\t  tw.epv.RequestType.Collection"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Collection","declaredType":"sequenceFlow","id":"bca20caf-ee0c-4bf4-8856-65b582207bc8","sourceRef":"4b02a02d-49e7-477f-89fa-84162350bcc6"},{"startQuantity":1,"outgoing":["e6e4f61c-312e-4a01-822f-a72b5237ec42"],"incoming":["9ad63445-77ea-4b28-8a25-b1a81c64533d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":386,"y":12,"declaredType":"TNodeVisualInfo","height":70}]},"name":"start Create Process","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"913cc9dc-ccc4-4af5-86c4-5216c01014b4","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar input = new tw.object.Map();\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\n\r\ntw.local.processName = tw.epv.ODCProcessName.create;\r\n \r\n\/\/if(tw.env.runningServerIP == \"************\")\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\n\/\/else\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\t\r\n\r\n\/\/tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\r\ntw.system.startProcessByName(tw.local.processName, input, false);"]}},{"targetRef":"913cc9dc-ccc4-4af5-86c4-5216c01014b4","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  ==\t  tw.epv.RequestType.Create"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Create ODC","declaredType":"sequenceFlow","id":"9ad63445-77ea-4b28-8a25-b1a81c64533d","sourceRef":"4b02a02d-49e7-477f-89fa-84162350bcc6"},{"startQuantity":1,"outgoing":["a1b36c32-c98f-42db-8685-ae5d76aa03f3"],"incoming":["a99a5322-5d68-4786-804d-67495a9f8aaf"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":658,"y":245,"declaredType":"TNodeVisualInfo","height":70}]},"name":"start Reversal  Process","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"6ba55b6b-22b9-47d9-89f1-e32116d3a9db","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar input = new tw.object.Map();\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\n\r\ntw.local.processName = tw.epv.ODCProcessName.reversal+\"\";\r\n \r\n\/\/if(tw.env.runningServerIP == \"************\")\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\n\/\/else\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\t\r\n\r\n\/\/tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\r\ntw.system.startProcessByName(tw.local.processName, input, false);"]}},{"startQuantity":1,"outgoing":["b0fbef09-c987-43e4-8185-d3711fae162f"],"incoming":["********-9dd1-4da5-84d4-127606177a5e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":797,"y":313,"declaredType":"TNodeVisualInfo","height":70}]},"name":"start Closure Process","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"f89a4d43-aa72-47b9-8945-1e978931d874","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar input = new tw.object.Map();\r\ninput.put(\"odcRequest\",tw.local.odcRequest);\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\n \r\ntw.local.processName = tw.epv.ODCProcessName.closure+\"\";\r\n \r\n\/\/if(tw.env.runningServerIP == \"************\")\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\n\/\/else\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\t\r\n\r\n\/\/tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\r\ntw.system.startProcessByName(tw.local.processName, input, false);"]}},{"targetRef":"6ba55b6b-22b9-47d9-89f1-e32116d3a9db","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  ==\t  tw.epv.RequestType.Reversal"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Reversal","declaredType":"sequenceFlow","id":"a99a5322-5d68-4786-804d-67495a9f8aaf","sourceRef":"4b02a02d-49e7-477f-89fa-84162350bcc6"},{"targetRef":"f89a4d43-aa72-47b9-8945-1e978931d874","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  ==\t  tw.epv.RequestType.Closure"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Closure","declaredType":"sequenceFlow","id":"********-9dd1-4da5-84d4-127606177a5e","sourceRef":"4b02a02d-49e7-477f-89fa-84162350bcc6"},{"targetRef":"acf670a7-5e34-496c-8768-b18bbb4c09a4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"3f66f592-cb04-439f-8325-1f2e8ecc9a4d","sourceRef":"841f3829-ef78-434b-8948-6b4406876e41"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"ODC Creation and Amendment \u0627\u0646\u0634\u0627\u0621 \u0648 \u062a\u062d\u062f\u064a\u062b \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"processName","isCollection":false,"declaredType":"dataObject","id":"2056.f19f3e47-ad97-46d2-8c9c-ab411519ee88"},{"parallelMultiple":false,"outgoing":["f6bfc0e9-24e8-428c-8359-dfe22bb8fc58"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"cdb58da0-9006-4637-8a9f-1b8ed3af6db2","otherAttributes":{"eventImplId":"0605d09e-8a63-44bf-8c54-ebe04934d57e"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1479,"y":432,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error Event","declaredType":"intermediateCatchEvent","id":"81840969-afc5-4ece-8fb5-9583a41609c8"},{"targetRef":"3fef31ef-99fc-471b-8796-1c23daa19be9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exp. Handling","declaredType":"sequenceFlow","id":"f6bfc0e9-24e8-428c-8359-dfe22bb8fc58","sourceRef":"81840969-afc5-4ece-8fb5-9583a41609c8"},{"targetRef":"d7efe1f4-4396-40af-8fb8-b4d7fb45108e","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  ==\t  tw.epv.RequestType.Amendment"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Amendment","declaredType":"sequenceFlow","id":"4f5524cb-d0f1-4b79-82db-7c1a6c25b45c","sourceRef":"4b02a02d-49e7-477f-89fa-84162350bcc6"},{"targetRef":"acf670a7-5e34-496c-8768-b18bbb4c09a4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"a1b36c32-c98f-42db-8685-ae5d76aa03f3","sourceRef":"6ba55b6b-22b9-47d9-89f1-e32116d3a9db"},{"targetRef":"acf670a7-5e34-496c-8768-b18bbb4c09a4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"b0fbef09-c987-43e4-8185-d3711fae162f","sourceRef":"f89a4d43-aa72-47b9-8945-1e978931d874"},{"targetRef":"acf670a7-5e34-496c-8768-b18bbb4c09a4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"77d41f45-95a5-45bc-8fe7-3a7bfad68d61","sourceRef":"d7efe1f4-4396-40af-8fb8-b4d7fb45108e"},{"targetRef":"acf670a7-5e34-496c-8768-b18bbb4c09a4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e6e4f61c-312e-4a01-822f-a72b5237ec42","sourceRef":"913cc9dc-ccc4-4af5-86c4-5216c01014b4"},{"startQuantity":1,"outgoing":["096f719b-e0bb-4ad3-88a8-662265249cfa"],"incoming":["dc094bde-97a9-4302-810e-edd5581e8cdd"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":850,"y":405,"declaredType":"TNodeVisualInfo","height":70}]},"name":"start Recreate Process","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8960c77d-df1f-42ee-8ef2-6bb15ea1921e","scriptFormat":"text\/x-javascript","script":{"content":["\r\nvar input = new tw.object.Map();\r\ninput.put(\"ODCRequest\",tw.local.odcRequest);\r\ninput.put(\"routingDetails\",tw.local.routingDetails);\r\n\r\n\r\ntw.local.processName = tw.epv.ODCProcessName.create+\"\";\r\n\r\n\r\n\/\/if(tw.env.runningServerIP == \"************\")\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").findSnapshotByName(\"ODC_10-09-2023_V4\").findProcessByName(tw.local.processName).startNew(input, false);\r\n\/\/else\r\n\/\/\ttw.system.model.findProcessAppByAcronym(\"NBEODCR\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\t\r\n\r\n\/\/tw.system.model.findProcessAppByName(\"NBE ODC Processes\").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);\r\n\r\ntw.system.startProcessByName(tw.local.processName, input, false)"]}},{"targetRef":"8960c77d-df1f-42ee-8ef2-6bb15ea1921e","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestType\t  ==\t  tw.epv.RequestType.Recreate"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To start Recreate Process","declaredType":"sequenceFlow","id":"dc094bde-97a9-4302-810e-edd5581e8cdd","sourceRef":"4b02a02d-49e7-477f-89fa-84162350bcc6"},{"targetRef":"acf670a7-5e34-496c-8768-b18bbb4c09a4","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"096f719b-e0bb-4ad3-88a8-662265249cfa","sourceRef":"8960c77d-df1f-42ee-8ef2-6bb15ea1921e"},{"incoming":["f6bfc0e9-24e8-428c-8359-dfe22bb8fc58"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"0e58d863-2474-4a3e-898a-776741b273f8","otherAttributes":{"eventImplId":"f14b6252-6d17-45bb-83b8-26ff2b9f994a"}}],"extensionElements":{"postAssignmentScript":["if(tw.local.errorMessage!=null)\r\n{\t\r\n\tlog.info(\"============================= ODC ================================================================\");\r\n\tlog.info(\"=============================Start Service -&gt;  Initiate ODC Process ===========================================\");\r\n\tlog.info(\"=======================Instace Id= \"+tw.system.currentProcessInstanceID +\" :: Error Message: \"+ tw.local.errorMessage);\r\n\tlog.info(\"=============================End Service -&gt;  Initiate ODC Process ===========================================\");\r\n\t}\r\n\r\n\r\n"],"nodeVisualInfo":[{"width":24,"x":1531,"y":432,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event1","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}]}],"declaredType":"endEvent","id":"3fef31ef-99fc-471b-8796-1c23daa19be9"},{"startQuantity":1,"outgoing":["8ef4a9ce-6b19-4b36-82bd-898ed60b413f"],"incoming":["be95c2c1-d6e3-42a3-88f5-62a1d26fe478","6aae309b-3e79-4776-8ca4-11f0241fa76d","1c50a334-8e56-4fc4-8d18-8ffe08eb65d7","d2354b5c-80f8-4e81-8070-c825bbf904f6","282cef0b-4720-4282-81f5-3dbdafc414b2","81414926-c0a5-4364-8570-a949ac22f913"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":1137,"y":210,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Initiate ODC Process\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"0aaf3b3c-845b-49b1-85fd-e17648df01f2","calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"parallelMultiple":false,"outgoing":["be95c2c1-d6e3-42a3-88f5-62a1d26fe478"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e356eebc-c9d8-4044-8ad8-d58093b5b561"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c9bf18ce-7f60-4e12-8fa7-f952431d7647","otherAttributes":{"eventImplId":"402d5c39-ea1f-41d2-8bbe-85834ef9959e"}}],"attachedToRef":"913cc9dc-ccc4-4af5-86c4-5216c01014b4","extensionElements":{"nodeVisualInfo":[{"width":24,"x":469,"y":17,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"bfbec735-387a-4f92-85c4-bc6408bfd10a","outputSet":{}},{"targetRef":"0aaf3b3c-845b-49b1-85fd-e17648df01f2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"be95c2c1-d6e3-42a3-88f5-62a1d26fe478","sourceRef":"bfbec735-387a-4f92-85c4-bc6408bfd10a"},{"parallelMultiple":false,"outgoing":["81414926-c0a5-4364-8570-a949ac22f913"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e72682e5-291f-4d15-80a5-31aa3a0a5fd3"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"93c47cdc-1d65-4620-8659-da71c56610dc","otherAttributes":{"eventImplId":"f1dbcf05-f85c-4799-81ba-b2d075017bd9"}}],"attachedToRef":"d7efe1f4-4396-40af-8fb8-b4d7fb45108e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":504,"y":94,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"4a5f82bd-83ce-4379-8174-163144a5402c","outputSet":{}},{"parallelMultiple":false,"outgoing":["6aae309b-3e79-4776-8ca4-11f0241fa76d"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a987228d-c94d-4edb-825b-31e596aa179b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"e7471dab-5d8b-4ef5-8acb-3ee0b047d04e","otherAttributes":{"eventImplId":"902f03d5-5082-4ae1-8569-bc6a8486fa05"}}],"attachedToRef":"8960c77d-df1f-42ee-8ef2-6bb15ea1921e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":885,"y":463,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"e15b43a1-b5f2-47a0-827f-356d0d1fc4b5","outputSet":{}},{"targetRef":"0aaf3b3c-845b-49b1-85fd-e17648df01f2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"6aae309b-3e79-4776-8ca4-11f0241fa76d","sourceRef":"e15b43a1-b5f2-47a0-827f-356d0d1fc4b5"},{"parallelMultiple":false,"outgoing":["1c50a334-8e56-4fc4-8d18-8ffe08eb65d7"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"71884565-98e3-4cc7-835d-a8d459bb9284"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"3dd162cf-1225-4bea-8110-08dde396ef61","otherAttributes":{"eventImplId":"3c6f693d-67df-4570-8156-b115aa5e2039"}}],"attachedToRef":"f89a4d43-aa72-47b9-8945-1e978931d874","extensionElements":{"nodeVisualInfo":[{"width":24,"x":832,"y":371,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"b731f798-9d81-4d01-899f-f6c68adf24bd","outputSet":{}},{"targetRef":"0aaf3b3c-845b-49b1-85fd-e17648df01f2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"1c50a334-8e56-4fc4-8d18-8ffe08eb65d7","sourceRef":"b731f798-9d81-4d01-899f-f6c68adf24bd"},{"parallelMultiple":false,"outgoing":["d2354b5c-80f8-4e81-8070-c825bbf904f6"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"eaae946e-bcc4-4fdc-84b5-9bc5fe3a85ec"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"37bc0b66-8954-45e0-8b4f-e06b558674af","otherAttributes":{"eventImplId":"2bceb9ea-88c1-4ded-87ef-0969e9e03ced"}}],"attachedToRef":"6ba55b6b-22b9-47d9-89f1-e32116d3a9db","extensionElements":{"nodeVisualInfo":[{"width":24,"x":693,"y":303,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error4","declaredType":"boundaryEvent","id":"c45db874-d635-4df7-85b9-d6efb83e0c17","outputSet":{}},{"targetRef":"0aaf3b3c-845b-49b1-85fd-e17648df01f2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"d2354b5c-80f8-4e81-8070-c825bbf904f6","sourceRef":"c45db874-d635-4df7-85b9-d6efb83e0c17"},{"parallelMultiple":false,"outgoing":["282cef0b-4720-4282-81f5-3dbdafc414b2"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3dd6e9a0-bc87-4a9a-881e-519228aa77b8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"3a046557-d69a-4fed-84f7-13fa8b36a673","otherAttributes":{"eventImplId":"6b787de9-f688-462d-8452-51b318d18554"}}],"attachedToRef":"841f3829-ef78-434b-8948-6b4406876e41","extensionElements":{"nodeVisualInfo":[{"width":24,"x":539,"y":224,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error5","declaredType":"boundaryEvent","id":"c8b5cd28-ab1a-4af6-8181-7284cc27708d","outputSet":{}},{"targetRef":"0aaf3b3c-845b-49b1-85fd-e17648df01f2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"282cef0b-4720-4282-81f5-3dbdafc414b2","sourceRef":"c8b5cd28-ab1a-4af6-8181-7284cc27708d"},{"targetRef":"0aaf3b3c-845b-49b1-85fd-e17648df01f2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"81414926-c0a5-4364-8570-a949ac22f913","sourceRef":"4a5f82bd-83ce-4379-8174-163144a5402c"},{"incoming":["8ef4a9ce-6b19-4b36-82bd-898ed60b413f"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c38d1f96-1a98-46c0-843b-c2baa0677c7d","otherAttributes":{"eventImplId":"bbe8733f-4a49-4ea3-88ed-7bb7040497f5"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1328,"y":232,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"0348001f-bcde-4377-86ec-1ef2c006fd74"},{"targetRef":"0348001f-bcde-4377-86ec-1ef2c006fd74","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"8ef4a9ce-6b19-4b36-82bd-898ed60b413f","sourceRef":"0aaf3b3c-845b-49b1-85fd-e17648df01f2"}],"laneSet":[{"id":"5a0fc6be-577e-4926-8431-81d2f5453c8c","lane":[{"flowNodeRef":["94eb8d0f-ebc9-4403-8b97-5b8944ca4e29","acf670a7-5e34-496c-8768-b18bbb4c09a4","d7efe1f4-4396-40af-8fb8-b4d7fb45108e","4b02a02d-49e7-477f-89fa-84162350bcc6","841f3829-ef78-434b-8948-6b4406876e41","913cc9dc-ccc4-4af5-86c4-5216c01014b4","6ba55b6b-22b9-47d9-89f1-e32116d3a9db","f89a4d43-aa72-47b9-8945-1e978931d874","81840969-afc5-4ece-8fb5-9583a41609c8","8960c77d-df1f-42ee-8ef2-6bb15ea1921e","3fef31ef-99fc-471b-8796-1c23daa19be9","0aaf3b3c-845b-49b1-85fd-e17648df01f2","bfbec735-387a-4f92-85c4-bc6408bfd10a","4a5f82bd-83ce-4379-8174-163144a5402c","e15b43a1-b5f2-47a0-827f-356d0d1fc4b5","b731f798-9d81-4d01-899f-f6c68adf24bd","c45db874-d635-4df7-85b9-d6efb83e0c17","c8b5cd28-ab1a-4af6-8181-7284cc27708d","0348001f-bcde-4377-86ec-1ef2c006fd74"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":501}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"8615812e-9edc-47db-8da2-9aea7ebb911b","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Initiate ODC Process","declaredType":"process","id":"1.2302fe8e-614d-43eb-91b6-2956193bc993","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"id":"2055.de6250d6-ea05-49bc-896c-eada027a9562"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instanceId","isCollection":false,"id":"2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskId","isCollection":false,"id":"2055.743a63fc-5839-4d37-8d0e-a582fda8ee91"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"id":"2055.192ba003-65ef-451c-89ea-378afe19f644"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"issuccessful","isCollection":false,"id":"2055.5368acb7-69e7-470f-8f37-b60b75384c47"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"53602806-844a-4a91-800a-7aee97897167","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.679fe48b-845c-41d8-b41a-884a27c2acf3","epvProcessLinkId":"f6146c51-4982-4dac-839e-10b8676c179a","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262","2055.82e3268d-9082-40bf-8a48-16f0710ff117","2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf"]}],"outputSet":[{"dataOutputRefs":["2055.de6250d6-ea05-49bc-896c-eada027a9562","2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644","2055.743a63fc-5839-4d37-8d0e-a582fda8ee91","2055.192ba003-65ef-451c-89ea-378afe19f644","2055.5368acb7-69e7-470f-8f37-b60b75384c47"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"Reversal\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestType","isCollection":false,"id":"2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.82e3268d-9082-40bf-8a48-16f0710ff117"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.odcRoutingDetails();\nautoObject.hubCode = \"\";\nautoObject.branchCode = \"\";\nautoObject.initiatorUser = \"\";\nautoObject.branchName = \"\";\nautoObject.hubName = \"\";\nautoObject.branchSeq = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","name":"routingDetails","isCollection":false,"id":"2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>94db1a1d-5663-48af-b58d-5a234e81081c</guid>
            <versionId>622f4178-faa7-4ee1-86a5-9852838334b5</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.82e3268d-9082-40bf-8a48-16f0710ff117</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b19a064b-40f9-4d88-9fa6-68e313f53afb</guid>
            <versionId>de2266a5-6617-4cd9-808f-fabf204beec0</versionId>
        </processParameter>
        <processParameter name="routingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>********-d3f2-4af1-b5c4-ee1edfa652a0</guid>
            <versionId>47071b3b-7b65-43b0-a122-9f6629414bb4</versionId>
        </processParameter>
        <processParameter name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.de6250d6-ea05-49bc-896c-eada027a9562</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cf318efa-4bd9-4cdd-ba73-76ae17363933</guid>
            <versionId>05146451-a4c7-4c18-b2ba-afa0797f2f6f</versionId>
        </processParameter>
        <processParameter name="instanceId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bc589c2f-4fea-4c03-9f34-dba7de11b88c</guid>
            <versionId>b5fda59b-8e0a-449a-b4aa-4c86fc97b91a</versionId>
        </processParameter>
        <processParameter name="taskId">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.743a63fc-5839-4d37-8d0e-a582fda8ee91</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f10a8fcd-3a80-42c2-b681-d7726b1376e6</guid>
            <versionId>d6bcf4b0-7836-4ee6-b473-34b1ebe41a11</versionId>
        </processParameter>
        <processParameter name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.192ba003-65ef-451c-89ea-378afe19f644</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>cdf8801e-4c27-4bb7-95fd-681e935d095d</guid>
            <versionId>bb3304c7-0f3e-481b-80f7-09431fad7968</versionId>
        </processParameter>
        <processParameter name="issuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5368acb7-69e7-470f-8f37-b60b75384c47</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>4170ab95-896b-4fc4-bfbf-a485b6d9a9b1</guid>
            <versionId>6f2343d1-146d-4f5a-8053-fcc8f23d7df0</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.3a3787de-b3c6-456e-8519-33a90f6ddca8</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>9</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>45a3b9ec-12a1-4e44-8095-9ba76f9c942d</guid>
            <versionId>77b8a8a3-cb32-451e-b30a-2f246db09780</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.fba555ed-0c51-4a4a-92a2-d80e37ffafac</processParameterId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>10</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>166894f7-9f96-4568-8ba5-6a72db45e7eb</guid>
            <versionId>80064209-bf08-42ed-9fa9-87fed630b000</versionId>
        </processParameter>
        <processVariable name="processName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f19f3e47-ad97-46d2-8c9c-ab411519ee88</processVariableId>
            <description isNull="true" />
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5ea332c7-c5ba-4671-bfd9-1dd1cd57bcea</guid>
            <versionId>510efa7b-47fb-46e1-8df6-7c0f2aba1ce8</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.841f3829-ef78-434b-8948-6b4406876e41</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>start collection process</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.86c72b34-f50a-4f56-a539-b4266e75c9b3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d00</guid>
            <versionId>014787f0-a25a-4c9a-978f-173a4235ecec</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.19e7169b-c801-4a68-8716-1af9f1bd037a</processItemPrePostId>
                <processItemId>2025.841f3829-ef78-434b-8948-6b4406876e41</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>a9a6b4a2-3819-41b9-95e8-f039b3e9377c</guid>
                <versionId>99f316a9-5612-430d-b832-988bce826d6e</versionId>
            </processPrePosts>
            <layoutData x="530" y="166">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error5</name>
                <locationId>bottomLeft</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:2400</errorHandlerItem>
                <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.86c72b34-f50a-4f56-a539-b4266e75c9b3</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("odcRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.collection+"";&#xD;
&#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</script>
                <isRule>false</isRule>
                <guid>f0a7961d-53ae-4acd-a68c-2d1876c7fc25</guid>
                <versionId>89dfa82c-cb69-4363-a06d-a8549b023a68</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>start Amend  Process</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.903b95eb-64fe-43e1-b1c4-c8cc06e9c9a3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ca</guid>
            <versionId>040213cb-7052-4c9e-a884-97586c998bbd</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="421" y="89">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>rightTop</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:2400</errorHandlerItem>
                <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.903b95eb-64fe-43e1-b1c4-c8cc06e9c9a3</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("ODCRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
 &#xD;
tw.local.processName = tw.epv.ODCProcessName.create+"";&#xD;
//tw.local.testData= tw.local.processName;&#xD;
&#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false)</script>
                <isRule>false</isRule>
                <guid>782df17e-a469-48e7-a479-a566d60d407d</guid>
                <versionId>ba65c0ad-d782-4b31-8c12-eec002853e04</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>start Reversal  Process</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.342887c9-0915-4d5e-8024-513a4de27ffe</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d4f</guid>
            <versionId>06d86271-ee70-4f67-80f5-e15979967f6c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="658" y="245">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error4</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:2400</errorHandlerItem>
                <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.342887c9-0915-4d5e-8024-513a4de27ffe</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("odcRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.reversal+"";&#xD;
 &#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</script>
                <isRule>false</isRule>
                <guid>86e2c90b-c18c-49aa-b63b-d4738f3c5f65</guid>
                <versionId>a9a5f8c8-5a70-4432-bd1c-720700ac59dd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:2400</guid>
            <versionId>0f176861-3c0c-4457-862b-38c7a7bbb25a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="1137" y="210">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>2307a33f-45c6-4767-bf50-92e48f5b8140</guid>
                <versionId>61d08adb-dc9a-4496-b9fc-8cf0c6aabb18</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0f8e84b6-7a85-47f4-a9b4-9df08e29e140</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5b522881-04ff-4dd2-a315-21ebca862fd0</guid>
                    <versionId>80fbac0e-3fd5-4093-8c9a-8717539e8653</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6d1a7491-be5b-4d7f-baea-9396ed8dea89</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Initiate ODC Process"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c18e6839-fb8f-4d03-8977-0a74d1f014a5</guid>
                    <versionId>99d07249-f843-42af-ba22-3a8debbd1ca4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fa418df9-0e00-4ecf-aef7-24bb20812770</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.b11d6965-94d4-4f19-a1ab-f19d2cf37b97</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>a57bdd27-ca86-461b-84ff-32d72b214be8</guid>
                    <versionId>9d2cf5ea-dd57-4e6d-8fe4-72bdaad9a8b0</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>Request Type</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.4d73b434-e964-42cd-b0ea-28c92cc39866</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76cb</guid>
            <versionId>16045ee0-e14d-487f-9e3f-5fdae0351584</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.351f94e2-2471-42f8-a549-bd4e18f184b9</processItemPrePostId>
                <processItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>1c4e1bbf-7b6e-4bee-a6cb-f27177787d44</guid>
                <versionId>9a9f9b1c-4977-4296-9f08-a47ac42f3a73</versionId>
            </processPrePosts>
            <layoutData x="137" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.4d73b434-e964-42cd-b0ea-28c92cc39866</switchId>
                <guid>c6d7e7f7-eab2-4180-a7fb-5612556873db</guid>
                <versionId>4bd51ca5-7e31-4538-930e-ce37fb249731</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.a562d881-0eeb-4589-83e4-1b0196944fa8</switchConditionId>
                    <switchId>3013.4d73b434-e964-42cd-b0ea-28c92cc39866</switchId>
                    <seq>1</seq>
                    <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2554</endStateId>
                    <condition>tw.local.requestType	  ==	  tw.epv.RequestType.Amendment</condition>
                    <guid>e238cfb0-3063-4c37-b7ea-76653b9196a6</guid>
                    <versionId>8680f1d3-0692-472c-bf06-524f27cf618c</versionId>
                </SwitchCondition>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.18db7e57-0015-4737-ac5f-11dd0a089961</switchConditionId>
                    <switchId>3013.4d73b434-e964-42cd-b0ea-28c92cc39866</switchId>
                    <seq>2</seq>
                    <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2555</endStateId>
                    <condition>tw.local.requestType	  ==	  tw.epv.RequestType.Collection</condition>
                    <guid>28cb2dd0-0cfe-4e07-be02-e15cc0d6e9c9</guid>
                    <versionId>5591db06-ff35-4f4a-a9ed-4712d718ba7a</versionId>
                </SwitchCondition>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.4fc4e0d3-d60c-41f4-8419-adfb9914c2ae</switchConditionId>
                    <switchId>3013.4d73b434-e964-42cd-b0ea-28c92cc39866</switchId>
                    <seq>3</seq>
                    <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2556</endStateId>
                    <condition>tw.local.requestType	  ==	  tw.epv.RequestType.Reversal</condition>
                    <guid>8632688e-f54f-4603-b61e-e50d6fffa19c</guid>
                    <versionId>7f9931ae-a94a-42c3-a3b4-63f8f06c21dc</versionId>
                </SwitchCondition>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.06369c43-74b7-414a-a7b4-2667ef13e2ff</switchConditionId>
                    <switchId>3013.4d73b434-e964-42cd-b0ea-28c92cc39866</switchId>
                    <seq>4</seq>
                    <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2557</endStateId>
                    <condition>tw.local.requestType	  ==	  tw.epv.RequestType.Closure</condition>
                    <guid>1a722e44-0879-4352-87ab-df85c43a4dc2</guid>
                    <versionId>641b4c94-7495-4e86-81e0-ebfeadb9f455</versionId>
                </SwitchCondition>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.7f80a620-5a3f-4ef4-a341-c276b38cac07</switchConditionId>
                    <switchId>3013.4d73b434-e964-42cd-b0ea-28c92cc39866</switchId>
                    <seq>5</seq>
                    <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2558</endStateId>
                    <condition>tw.local.requestType	  ==	  tw.epv.RequestType.Recreate</condition>
                    <guid>00305ea3-dc6b-45ef-bd58-064c508f82c8</guid>
                    <versionId>4e0cff73-ccb7-4798-a713-e38e4d67515a</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0348001f-bcde-4377-86ec-1ef2c006fd74</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.a47c2f7f-ff10-40a1-a412-de828985baca</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:2559</guid>
            <versionId>59c3537d-09d6-4d42-a63d-63983be34056</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1328" y="232">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.a47c2f7f-ff10-40a1-a412-de828985baca</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>8cd8b379-4e72-4dbc-a79e-07f6f83f2638</guid>
                <versionId>827a9be3-ab2c-41c9-84f4-89569449e47e</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.50726256-7d80-4f6e-8c80-8a9488c9b419</parameterMappingId>
                    <processParameterId>2055.fba555ed-0c51-4a4a-92a2-d80e37ffafac</processParameterId>
                    <parameterMappingParentId>3007.a47c2f7f-ff10-40a1-a412-de828985baca</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>daad7c7c-8b92-4c54-9be5-1e8a1db46030</guid>
                    <versionId>eede5a9b-d8a4-4c32-a0e2-76890acd37fa</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f89a4d43-aa72-47b9-8945-1e978931d874</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>start Closure Process</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.caaafb3b-68ae-43d2-9daf-cf52a871a712</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d50</guid>
            <versionId>8ea7297c-aba4-42c8-8676-6081e6d07290</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="797" y="313">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:2400</errorHandlerItem>
                <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.caaafb3b-68ae-43d2-9daf-cf52a871a712</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("odcRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
 &#xD;
tw.local.processName = tw.epv.ODCProcessName.closure+"";&#xD;
 &#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</script>
                <isRule>false</isRule>
                <guid>e93a0ae5-bf73-46e3-8fd1-d95185740d36</guid>
                <versionId>43fe57eb-321b-491a-932f-0e0de4a65a4e</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>start Create Process</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.46143d15-727a-4ff1-8518-b55ea927f0c9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a6a24647f:7d01</guid>
            <versionId>a7af3b72-8db9-40de-8326-1e52e5419080</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="386" y="12">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>rightTop</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:2400</errorHandlerItem>
                <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="rightCenter" portType="1" />
                    <toPort locationId="topLeft" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.46143d15-727a-4ff1-8518-b55ea927f0c9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("ODCRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.create;&#xD;
 &#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</script>
                <isRule>false</isRule>
                <guid>4c61eea4-02e7-402e-b68d-aea8de54974c</guid>
                <versionId>194d07e1-547e-40b6-aafa-9c2c9421611a</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>start Recreate Process</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.5255b4a5-9ef8-4742-b4fe-3be4446ad041</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
            <guid>guid:b391c9341fe28de0:43131724:18a837ed779:2fbe</guid>
            <versionId>d0b10187-d7e9-4411-b90b-c0006a978fd1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="850" y="405">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:651a1a6abf396537:64776e00:18baeba64af:2400</errorHandlerItem>
                <errorHandlerItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="bottomCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.5255b4a5-9ef8-4742-b4fe-3be4446ad041</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("ODCRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.create+"";&#xD;
&#xD;
&#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false)</script>
                <isRule>false</isRule>
                <guid>cca914c7-0e6b-4b51-930e-65e25e932cf9</guid>
                <versionId>b6a09da5-a626-4846-81cf-523f7494bbc0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3fef31ef-99fc-471b-8796-1c23daa19be9</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>End Event1</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.aea5e251-9d5b-4565-acc9-48afeb10f5de</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:b391c9341fe28de0:43131724:18a837ed779:64fb</guid>
            <versionId>d44b40de-6aee-47a2-8060-beb7d212ee8a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.bf4eb8e1-af21-4f78-8a5b-10a772f57d2d</processItemPrePostId>
                <processItemId>2025.3fef31ef-99fc-471b-8796-1c23daa19be9</processItemId>
                <location>2</location>
                <script>if(tw.local.errorMessage!=null)&#xD;
{	&#xD;
	log.info("============================= ODC ================================================================");&#xD;
	log.info("=============================Start Service -&gt;  Initiate ODC Process ===========================================");&#xD;
	log.info("=======================Instace Id= "+tw.system.currentProcessInstanceID +" :: Error Message: "+ tw.local.errorMessage);&#xD;
	log.info("=============================End Service -&gt;  Initiate ODC Process ===========================================");&#xD;
	}&#xD;
&#xD;
&#xD;
</script>
                <guid>41267ec8-9218-491a-a46b-************</guid>
                <versionId>7ada5d53-5055-4207-8ea9-45484675b2e4</versionId>
            </processPrePosts>
            <layoutData x="1531" y="432">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.aea5e251-9d5b-4565-acc9-48afeb10f5de</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>ab83db39-4cc4-4859-a225-46be7456c48a</guid>
                <versionId>483cd07d-3b5d-4d27-918f-3ace27063a7a</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f812c31a-cdc9-4e50-8ad1-7b2b744d850b</parameterMappingId>
                    <processParameterId>2055.3a3787de-b3c6-456e-8519-33a90f6ddca8</processParameterId>
                    <parameterMappingParentId>3007.aea5e251-9d5b-4565-acc9-48afeb10f5de</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>0d1da441-2f63-4bf5-92e2-ed883a2117f4</guid>
                    <versionId>de1a5450-9af0-4b24-9341-9b3f2d5f0100</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</processItemId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.5d21f221-5850-4b82-8e9f-5db88e017fb3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ba</guid>
            <versionId>fb23b4a7-cb46-475d-9a62-b1ff0263c425</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="940" y="189">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.5d21f221-5850-4b82-8e9f-5db88e017fb3</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>2b8a71fc-ae79-43b4-b6e7-4a56acac6dea</guid>
                <versionId>2e899098-27f4-4b45-812b-cfb847316fc5</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.55384695-9c9c-4b6c-97fb-e9ac1a2d9dc0</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <guid>960e2c14-0177-4266-963d-88e9c2f4819d</guid>
            <versionId>04a84102-1f39-451b-842a-5ca5f745cd0d</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.8515d147-5313-49ad-8e78-76ea92180077</epvProcessLinkId>
            <epvId>/21.679fe48b-845c-41d8-b41a-884a27c2acf3</epvId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <guid>96437a44-a48e-40b1-8ced-8e9c866b8945</guid>
            <versionId>804ef0fb-1355-48e5-82fe-cd14ff7c8af9</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</startingProcessItemId>
        <errorHandlerItemId>2025.3fef31ef-99fc-471b-8796-1c23daa19be9</errorHandlerItemId>
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="107" y="186">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <globalExceptionHandler>
            <layoutData x="1479" y="432">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </globalExceptionHandler>
        <globalExceptionLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </globalExceptionLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Initiate ODC Process" id="1.2302fe8e-614d-43eb-91b6-2956193bc993" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="53602806-844a-4a91-800a-7aee97897167" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.679fe48b-845c-41d8-b41a-884a27c2acf3" epvProcessLinkId="f6146c51-4982-4dac-839e-10b8676c179a" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="requestType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">"Reversal"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.82e3268d-9082-40bf-8a48-16f0710ff117">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="routingDetails" itemSubjectRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727" isCollection="false" id="2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="false">var autoObject = new tw.object.odcRoutingDetails();
autoObject.hubCode = "";
autoObject.branchCode = "";
autoObject.initiatorUser = "";
autoObject.branchName = "";
autoObject.hubName = "";
autoObject.branchSeq = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="isSuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.de6250d6-ea05-49bc-896c-eada027a9562" />
                        
                        
                        <ns16:dataOutput name="instanceId" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644" />
                        
                        
                        <ns16:dataOutput name="taskId" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.743a63fc-5839-4d37-8d0e-a582fda8ee91" />
                        
                        
                        <ns16:dataOutput name="errorMessage" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.192ba003-65ef-451c-89ea-378afe19f644" />
                        
                        
                        <ns16:dataOutput name="issuccessful" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.5368acb7-69e7-470f-8f37-b60b75384c47" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.b1dedfeb-ab28-406d-8b10-0ce3f109e262</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.82e3268d-9082-40bf-8a48-16f0710ff117</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.d9be1e01-9927-42f3-89c4-4632a73aa4cf</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.de6250d6-ea05-49bc-896c-eada027a9562</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.399458c6-6f2a-41b9-8e3a-cd51b37e9644</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.743a63fc-5839-4d37-8d0e-a582fda8ee91</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.192ba003-65ef-451c-89ea-378afe19f644</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.5368acb7-69e7-470f-8f37-b60b75384c47</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="5a0fc6be-577e-4926-8431-81d2f5453c8c">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="8615812e-9edc-47db-8da2-9aea7ebb911b" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="501" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>94eb8d0f-ebc9-4403-8b97-5b8944ca4e29</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>acf670a7-5e34-496c-8768-b18bbb4c09a4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d7efe1f4-4396-40af-8fb8-b4d7fb45108e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4b02a02d-49e7-477f-89fa-84162350bcc6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>841f3829-ef78-434b-8948-6b4406876e41</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>913cc9dc-ccc4-4af5-86c4-5216c01014b4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6ba55b6b-22b9-47d9-89f1-e32116d3a9db</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f89a4d43-aa72-47b9-8945-1e978931d874</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>81840969-afc5-4ece-8fb5-9583a41609c8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8960c77d-df1f-42ee-8ef2-6bb15ea1921e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3fef31ef-99fc-471b-8796-1c23daa19be9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0aaf3b3c-845b-49b1-85fd-e17648df01f2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>bfbec735-387a-4f92-85c4-bc6408bfd10a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4a5f82bd-83ce-4379-8174-163144a5402c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e15b43a1-b5f2-47a0-827f-356d0d1fc4b5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b731f798-9d81-4d01-899f-f6c68adf24bd</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c45db874-d635-4df7-85b9-d6efb83e0c17</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c8b5cd28-ab1a-4af6-8181-7284cc27708d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0348001f-bcde-4377-86ec-1ef2c006fd74</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="94eb8d0f-ebc9-4403-8b97-5b8944ca4e29">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="107" y="186" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.0fb58f07-d096-4ec8-8255-943ace2831b9</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="acf670a7-5e34-496c-8768-b18bbb4c09a4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="940" y="189" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:fc53d6da47fd17d0:63ae7219:18a65c5cf2a:76ba</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3f66f592-cb04-439f-8325-1f2e8ecc9a4d</ns16:incoming>
                        
                        
                        <ns16:incoming>a1b36c32-c98f-42db-8685-ae5d76aa03f3</ns16:incoming>
                        
                        
                        <ns16:incoming>b0fbef09-c987-43e4-8185-d3711fae162f</ns16:incoming>
                        
                        
                        <ns16:incoming>77d41f45-95a5-45bc-8fe7-3a7bfad68d61</ns16:incoming>
                        
                        
                        <ns16:incoming>e6e4f61c-312e-4a01-822f-a72b5237ec42</ns16:incoming>
                        
                        
                        <ns16:incoming>096f719b-e0bb-4ad3-88a8-662265249cfa</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="94eb8d0f-ebc9-4403-8b97-5b8944ca4e29" targetRef="4b02a02d-49e7-477f-89fa-84162350bcc6" name="To Request Type" id="2027.0fb58f07-d096-4ec8-8255-943ace2831b9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="start Amend  Process" id="d7efe1f4-4396-40af-8fb8-b4d7fb45108e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="421" y="89" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>4f5524cb-d0f1-4b79-82db-7c1a6c25b45c</ns16:incoming>
                        
                        
                        <ns16:outgoing>77d41f45-95a5-45bc-8fe7-3a7bfad68d61</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("ODCRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
 &#xD;
tw.local.processName = tw.epv.ODCProcessName.create+"";&#xD;
//tw.local.testData= tw.local.processName;&#xD;
&#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false)</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:exclusiveGateway default="9ad63445-77ea-4b28-8a25-b1a81c64533d" name="Request Type" id="4b02a02d-49e7-477f-89fa-84162350bcc6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="137" y="182" width="32" height="32" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.0fb58f07-d096-4ec8-8255-943ace2831b9</ns16:incoming>
                        
                        
                        <ns16:outgoing>4f5524cb-d0f1-4b79-82db-7c1a6c25b45c</ns16:outgoing>
                        
                        
                        <ns16:outgoing>bca20caf-ee0c-4bf4-8856-65b582207bc8</ns16:outgoing>
                        
                        
                        <ns16:outgoing>9ad63445-77ea-4b28-8a25-b1a81c64533d</ns16:outgoing>
                        
                        
                        <ns16:outgoing>a99a5322-5d68-4786-804d-67495a9f8aaf</ns16:outgoing>
                        
                        
                        <ns16:outgoing>********-9dd1-4da5-84d4-127606177a5e</ns16:outgoing>
                        
                        
                        <ns16:outgoing>dc094bde-97a9-4302-810e-edd5581e8cdd</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="start collection process" id="841f3829-ef78-434b-8948-6b4406876e41">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="530" y="166" width="95" height="70" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bca20caf-ee0c-4bf4-8856-65b582207bc8</ns16:incoming>
                        
                        
                        <ns16:outgoing>3f66f592-cb04-439f-8325-1f2e8ecc9a4d</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("odcRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.collection+"";&#xD;
&#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b02a02d-49e7-477f-89fa-84162350bcc6" targetRef="841f3829-ef78-434b-8948-6b4406876e41" name="Collection" id="bca20caf-ee0c-4bf4-8856-65b582207bc8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  ==	  tw.epv.RequestType.Collection</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="start Create Process" id="913cc9dc-ccc4-4af5-86c4-5216c01014b4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="386" y="12" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9ad63445-77ea-4b28-8a25-b1a81c64533d</ns16:incoming>
                        
                        
                        <ns16:outgoing>e6e4f61c-312e-4a01-822f-a72b5237ec42</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("ODCRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.create;&#xD;
 &#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b02a02d-49e7-477f-89fa-84162350bcc6" targetRef="913cc9dc-ccc4-4af5-86c4-5216c01014b4" name="Create ODC" id="9ad63445-77ea-4b28-8a25-b1a81c64533d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  ==	  tw.epv.RequestType.Create</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="start Reversal  Process" id="6ba55b6b-22b9-47d9-89f1-e32116d3a9db">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="658" y="245" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a99a5322-5d68-4786-804d-67495a9f8aaf</ns16:incoming>
                        
                        
                        <ns16:outgoing>a1b36c32-c98f-42db-8685-ae5d76aa03f3</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("odcRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.reversal+"";&#xD;
 &#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="start Closure Process" id="f89a4d43-aa72-47b9-8945-1e978931d874">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="797" y="313" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>********-9dd1-4da5-84d4-127606177a5e</ns16:incoming>
                        
                        
                        <ns16:outgoing>b0fbef09-c987-43e4-8185-d3711fae162f</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("odcRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
 &#xD;
tw.local.processName = tw.epv.ODCProcessName.closure+"";&#xD;
 &#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b02a02d-49e7-477f-89fa-84162350bcc6" targetRef="6ba55b6b-22b9-47d9-89f1-e32116d3a9db" name="Reversal" id="a99a5322-5d68-4786-804d-67495a9f8aaf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  ==	  tw.epv.RequestType.Reversal</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b02a02d-49e7-477f-89fa-84162350bcc6" targetRef="f89a4d43-aa72-47b9-8945-1e978931d874" name="Closure" id="********-9dd1-4da5-84d4-127606177a5e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  ==	  tw.epv.RequestType.Closure</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="841f3829-ef78-434b-8948-6b4406876e41" targetRef="acf670a7-5e34-496c-8768-b18bbb4c09a4" name="To End" id="3f66f592-cb04-439f-8325-1f2e8ecc9a4d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="processName" id="2056.f19f3e47-ad97-46d2-8c9c-ab411519ee88">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">"ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير"</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:intermediateCatchEvent name="Error Event" id="81840969-afc5-4ece-8fb5-9583a41609c8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1479" y="432" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>f6bfc0e9-24e8-428c-8359-dfe22bb8fc58</ns16:outgoing>
                        
                        
                        <ns16:errorEventDefinition id="cdb58da0-9006-4637-8a9f-1b8ed3af6db2" eventImplId="0605d09e-8a63-44bf-8c54-ebe04934d57e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:intermediateCatchEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="81840969-afc5-4ece-8fb5-9583a41609c8" targetRef="3fef31ef-99fc-471b-8796-1c23daa19be9" name="To Exp. Handling" id="f6bfc0e9-24e8-428c-8359-dfe22bb8fc58">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b02a02d-49e7-477f-89fa-84162350bcc6" targetRef="d7efe1f4-4396-40af-8fb8-b4d7fb45108e" name="Amendment" id="4f5524cb-d0f1-4b79-82db-7c1a6c25b45c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  ==	  tw.epv.RequestType.Amendment</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="6ba55b6b-22b9-47d9-89f1-e32116d3a9db" targetRef="acf670a7-5e34-496c-8768-b18bbb4c09a4" name="To End" id="a1b36c32-c98f-42db-8685-ae5d76aa03f3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="f89a4d43-aa72-47b9-8945-1e978931d874" targetRef="acf670a7-5e34-496c-8768-b18bbb4c09a4" name="To End" id="b0fbef09-c987-43e4-8185-d3711fae162f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="d7efe1f4-4396-40af-8fb8-b4d7fb45108e" targetRef="acf670a7-5e34-496c-8768-b18bbb4c09a4" name="To End" id="77d41f45-95a5-45bc-8fe7-3a7bfad68d61">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="913cc9dc-ccc4-4af5-86c4-5216c01014b4" targetRef="acf670a7-5e34-496c-8768-b18bbb4c09a4" name="To End" id="e6e4f61c-312e-4a01-822f-a72b5237ec42">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="start Recreate Process" id="8960c77d-df1f-42ee-8ef2-6bb15ea1921e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="850" y="405" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>dc094bde-97a9-4302-810e-edd5581e8cdd</ns16:incoming>
                        
                        
                        <ns16:outgoing>096f719b-e0bb-4ad3-88a8-662265249cfa</ns16:outgoing>
                        
                        
                        <ns16:script>&#xD;
var input = new tw.object.Map();&#xD;
input.put("ODCRequest",tw.local.odcRequest);&#xD;
input.put("routingDetails",tw.local.routingDetails);&#xD;
&#xD;
&#xD;
tw.local.processName = tw.epv.ODCProcessName.create+"";&#xD;
&#xD;
&#xD;
//if(tw.env.runningServerIP == "************")&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").findSnapshotByName("ODC_10-09-2023_V4").findProcessByName(tw.local.processName).startNew(input, false);&#xD;
//else&#xD;
//	tw.system.model.findProcessAppByAcronym("NBEODCR").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
	&#xD;
&#xD;
//tw.system.model.findProcessAppByName("NBE ODC Processes").defaultSnapshot.findProcessByName(tw.local.processName).startNew(input, false);&#xD;
&#xD;
tw.system.startProcessByName(tw.local.processName, input, false)</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="4b02a02d-49e7-477f-89fa-84162350bcc6" targetRef="8960c77d-df1f-42ee-8ef2-6bb15ea1921e" name="To start Recreate Process" id="dc094bde-97a9-4302-810e-edd5581e8cdd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestType	  ==	  tw.epv.RequestType.Recreate</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8960c77d-df1f-42ee-8ef2-6bb15ea1921e" targetRef="acf670a7-5e34-496c-8768-b18bbb4c09a4" name="To End" id="096f719b-e0bb-4ad3-88a8-662265249cfa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event1" id="3fef31ef-99fc-471b-8796-1c23daa19be9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1531" y="432" width="24" height="24" />
                            
                            
                            <ns3:postAssignmentScript>if(tw.local.errorMessage!=null)&#xD;
{	&#xD;
	log.info("============================= ODC ================================================================");&#xD;
	log.info("=============================Start Service -&gt;  Initiate ODC Process ===========================================");&#xD;
	log.info("=======================Instace Id= "+tw.system.currentProcessInstanceID +" :: Error Message: "+ tw.local.errorMessage);&#xD;
	log.info("=============================End Service -&gt;  Initiate ODC Process ===========================================");&#xD;
	}&#xD;
&#xD;
&#xD;
</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>f6bfc0e9-24e8-428c-8359-dfe22bb8fc58</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="0e58d863-2474-4a3e-898a-776741b273f8" eventImplId="f14b6252-6d17-45bb-83b8-26ff2b9f994a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exception Handling" id="0aaf3b3c-845b-49b1-85fd-e17648df01f2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1137" y="210" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>be95c2c1-d6e3-42a3-88f5-62a1d26fe478</ns16:incoming>
                        
                        
                        <ns16:incoming>6aae309b-3e79-4776-8ca4-11f0241fa76d</ns16:incoming>
                        
                        
                        <ns16:incoming>1c50a334-8e56-4fc4-8d18-8ffe08eb65d7</ns16:incoming>
                        
                        
                        <ns16:incoming>d2354b5c-80f8-4e81-8070-c825bbf904f6</ns16:incoming>
                        
                        
                        <ns16:incoming>282cef0b-4720-4282-81f5-3dbdafc414b2</ns16:incoming>
                        
                        
                        <ns16:incoming>81414926-c0a5-4364-8570-a949ac22f913</ns16:incoming>
                        
                        
                        <ns16:outgoing>8ef4a9ce-6b19-4b36-82bd-898ed60b413f</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Initiate ODC Process"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="913cc9dc-ccc4-4af5-86c4-5216c01014b4" parallelMultiple="false" name="Error" id="bfbec735-387a-4f92-85c4-bc6408bfd10a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="469" y="17" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>be95c2c1-d6e3-42a3-88f5-62a1d26fe478</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e356eebc-c9d8-4044-8ad8-d58093b5b561" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c9bf18ce-7f60-4e12-8fa7-f952431d7647" eventImplId="402d5c39-ea1f-41d2-8bbe-85834ef9959e">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="bfbec735-387a-4f92-85c4-bc6408bfd10a" targetRef="0aaf3b3c-845b-49b1-85fd-e17648df01f2" name="To Exception Handling" id="be95c2c1-d6e3-42a3-88f5-62a1d26fe478">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="d7efe1f4-4396-40af-8fb8-b4d7fb45108e" parallelMultiple="false" name="Error1" id="4a5f82bd-83ce-4379-8174-163144a5402c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="504" y="94" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>81414926-c0a5-4364-8570-a949ac22f913</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e72682e5-291f-4d15-80a5-31aa3a0a5fd3" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="93c47cdc-1d65-4620-8659-da71c56610dc" eventImplId="f1dbcf05-f85c-4799-81ba-b2d075017bd9">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8960c77d-df1f-42ee-8ef2-6bb15ea1921e" parallelMultiple="false" name="Error2" id="e15b43a1-b5f2-47a0-827f-356d0d1fc4b5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="885" y="463" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>6aae309b-3e79-4776-8ca4-11f0241fa76d</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a987228d-c94d-4edb-825b-31e596aa179b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="e7471dab-5d8b-4ef5-8acb-3ee0b047d04e" eventImplId="902f03d5-5082-4ae1-8569-bc6a8486fa05">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="e15b43a1-b5f2-47a0-827f-356d0d1fc4b5" targetRef="0aaf3b3c-845b-49b1-85fd-e17648df01f2" name="To Exception Handling" id="6aae309b-3e79-4776-8ca4-11f0241fa76d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="f89a4d43-aa72-47b9-8945-1e978931d874" parallelMultiple="false" name="Error3" id="b731f798-9d81-4d01-899f-f6c68adf24bd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="832" y="371" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>1c50a334-8e56-4fc4-8d18-8ffe08eb65d7</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="71884565-98e3-4cc7-835d-a8d459bb9284" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="3dd162cf-1225-4bea-8110-08dde396ef61" eventImplId="3c6f693d-67df-4570-8156-b115aa5e2039">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b731f798-9d81-4d01-899f-f6c68adf24bd" targetRef="0aaf3b3c-845b-49b1-85fd-e17648df01f2" name="To Exception Handling" id="1c50a334-8e56-4fc4-8d18-8ffe08eb65d7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="6ba55b6b-22b9-47d9-89f1-e32116d3a9db" parallelMultiple="false" name="Error4" id="c45db874-d635-4df7-85b9-d6efb83e0c17">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="693" y="303" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>d2354b5c-80f8-4e81-8070-c825bbf904f6</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="eaae946e-bcc4-4fdc-84b5-9bc5fe3a85ec" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="37bc0b66-8954-45e0-8b4f-e06b558674af" eventImplId="2bceb9ea-88c1-4ded-87ef-0969e9e03ced">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="c45db874-d635-4df7-85b9-d6efb83e0c17" targetRef="0aaf3b3c-845b-49b1-85fd-e17648df01f2" name="To Exception Handling" id="d2354b5c-80f8-4e81-8070-c825bbf904f6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="841f3829-ef78-434b-8948-6b4406876e41" parallelMultiple="false" name="Error5" id="c8b5cd28-ab1a-4af6-8181-7284cc27708d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="539" y="224" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>282cef0b-4720-4282-81f5-3dbdafc414b2</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3dd6e9a0-bc87-4a9a-881e-519228aa77b8" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="3a046557-d69a-4fed-84f7-13fa8b36a673" eventImplId="6b787de9-f688-462d-8452-51b318d18554">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="c8b5cd28-ab1a-4af6-8181-7284cc27708d" targetRef="0aaf3b3c-845b-49b1-85fd-e17648df01f2" name="To Exception Handling" id="282cef0b-4720-4282-81f5-3dbdafc414b2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4a5f82bd-83ce-4379-8174-163144a5402c" targetRef="0aaf3b3c-845b-49b1-85fd-e17648df01f2" name="To Exception Handling" id="81414926-c0a5-4364-8570-a949ac22f913">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="0348001f-bcde-4377-86ec-1ef2c006fd74">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1328" y="232" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>8ef4a9ce-6b19-4b36-82bd-898ed60b413f</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="c38d1f96-1a98-46c0-843b-c2baa0677c7d" eventImplId="bbe8733f-4a49-4ea3-88ed-7bb7040497f5">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="0aaf3b3c-845b-49b1-85fd-e17648df01f2" targetRef="0348001f-bcde-4377-86ec-1ef2c006fd74" name="To End Event" id="8ef4a9ce-6b19-4b36-82bd-898ed60b413f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.77d41f45-95a5-45bc-8fe7-3a7bfad68d61</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
            <guid>1c4d10b5-ca78-4698-b4bf-1f315dae0586</guid>
            <versionId>1f055295-b2da-4dc2-ae3b-d8822e2eece7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e</fromProcessItemId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a1b36c32-c98f-42db-8685-ae5d76aa03f3</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
            <guid>a3f2e688-83e9-40e7-b383-7608264f87c7</guid>
            <versionId>2a13f6b7-abca-4873-b4e4-ed8e83c45df3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db</fromProcessItemId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
        </link>
        <link name="Collection">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bca20caf-ee0c-4bf4-8856-65b582207bc8</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2555</endStateId>
            <toProcessItemId>2025.841f3829-ef78-434b-8948-6b4406876e41</toProcessItemId>
            <guid>d627d29b-a500-4b19-a8ac-bd2ab69dbf29</guid>
            <versionId>3aac8688-0921-4deb-8724-c31f9d61aff2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <toProcessItemId>2025.841f3829-ef78-434b-8948-6b4406876e41</toProcessItemId>
        </link>
        <link name="Amendment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.4f5524cb-d0f1-4b79-82db-7c1a6c25b45c</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2554</endStateId>
            <toProcessItemId>2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e</toProcessItemId>
            <guid>a4eea9ec-efd9-4094-8b84-f1348fe767cc</guid>
            <versionId>3ea05a01-005c-4d32-b4dd-6805278ef0fe</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <toProcessItemId>2025.d7efe1f4-4396-40af-8fb8-b4d7fb45108e</toProcessItemId>
        </link>
        <link name="Create ODC">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9ad63445-77ea-4b28-8a25-b1a81c64533d</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4</toProcessItemId>
            <guid>92c467ba-1aad-4cd3-80d9-983e6a9c274a</guid>
            <versionId>c23d97e2-be60-4622-bd7b-556ee7fdd8b2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <toProcessItemId>2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e6e4f61c-312e-4a01-822f-a72b5237ec42</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
            <guid>fa23cf65-d630-4462-b777-097544e8ed3e</guid>
            <versionId>c996527f-e9e6-4cf5-9c55-25d33c5da663</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.913cc9dc-ccc4-4af5-86c4-5216c01014b4</fromProcessItemId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
        </link>
        <link name="Reversal">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a99a5322-5d68-4786-804d-67495a9f8aaf</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2556</endStateId>
            <toProcessItemId>2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db</toProcessItemId>
            <guid>267d50c4-691b-4e48-8bf1-aa81fef5508d</guid>
            <versionId>cf28ab99-1919-4c86-a30f-b023badc15d8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <toProcessItemId>2025.6ba55b6b-22b9-47d9-89f1-e32116d3a9db</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3f66f592-cb04-439f-8325-1f2e8ecc9a4d</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.841f3829-ef78-434b-8948-6b4406876e41</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
            <guid>6d79dba6-67d8-4016-adb8-1de083344c11</guid>
            <versionId>dbc4503d-1496-4dbf-80dd-64db63921551</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.841f3829-ef78-434b-8948-6b4406876e41</fromProcessItemId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.096f719b-e0bb-4ad3-88a8-662265249cfa</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
            <guid>70ceb723-2975-4801-aed1-67707bcb7b70</guid>
            <versionId>e3b9094b-c4f4-40c5-82af-f06172236a29</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e</fromProcessItemId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8ef4a9ce-6b19-4b36-82bd-898ed60b413f</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.0348001f-bcde-4377-86ec-1ef2c006fd74</toProcessItemId>
            <guid>19e9d660-05af-4ea1-b06d-98db015ce4d9</guid>
            <versionId>e7a22029-2bcc-470e-91a0-a4f06dc90c07</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0aaf3b3c-845b-49b1-85fd-e17648df01f2</fromProcessItemId>
            <toProcessItemId>2025.0348001f-bcde-4377-86ec-1ef2c006fd74</toProcessItemId>
        </link>
        <link name="Closure">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.********-9dd1-4da5-84d4-127606177a5e</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2557</endStateId>
            <toProcessItemId>2025.f89a4d43-aa72-47b9-8945-1e978931d874</toProcessItemId>
            <guid>e887d415-f469-4d42-88dd-6eedd03e5891</guid>
            <versionId>ebdcd806-8a14-45d4-9a3a-ff67954e5fe6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <toProcessItemId>2025.f89a4d43-aa72-47b9-8945-1e978931d874</toProcessItemId>
        </link>
        <link name="To start Recreate Process">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.dc094bde-97a9-4302-810e-edd5581e8cdd</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:2558</endStateId>
            <toProcessItemId>2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e</toProcessItemId>
            <guid>9cae9ffb-d5b1-4b44-8e4c-c80df3f02417</guid>
            <versionId>f6c65923-43f5-4b4b-a68a-e2c20944a9f3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4b02a02d-49e7-477f-89fa-84162350bcc6</fromProcessItemId>
            <toProcessItemId>2025.8960c77d-df1f-42ee-8ef2-6bb15ea1921e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b0fbef09-c987-43e4-8185-d3711fae162f</processLinkId>
            <processId>1.2302fe8e-614d-43eb-91b6-2956193bc993</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f89a4d43-aa72-47b9-8945-1e978931d874</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
            <guid>7e8aa77b-da7c-44fa-aab4-af5e10eee54b</guid>
            <versionId>fe8b486b-7041-4ef0-906d-af097ae211d7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.f89a4d43-aa72-47b9-8945-1e978931d874</fromProcessItemId>
            <toProcessItemId>2025.acf670a7-5e34-496c-8768-b18bbb4c09a4</toProcessItemId>
        </link>
    </process>
</teamworks>

