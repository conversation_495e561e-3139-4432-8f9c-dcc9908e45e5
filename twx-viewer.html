<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>TWX Artifact Viewer - Enhanced</title>
    <link rel="stylesheet" href="coach-view-modal.css">
    <link rel="stylesheet" href="cshs-modal.css">
    <link rel="stylesheet" href="syntax-highlighting.css">
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }

        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            padding: 20px;
        }

        .container {
            max-width: 1400px;
            margin: 0 auto;
            background: white;
            border-radius: 15px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            overflow: hidden;
        }

        .header {
            background: linear-gradient(135deg, #2c3e50 0%, #34495e 100%);
            color: white;
            padding: 30px;
            text-align: center;
        }

        .header h1 {
            font-size: 2.5em;
            margin-bottom: 10px;
            font-weight: 300;
        }

        .header .subtitle {
            font-size: 1.1em;
            opacity: 0.9;
        }

        .stats-bar {
            background: #f8f9fa;
            padding: 20px;
            display: flex;
            justify-content: space-around;
            border-bottom: 1px solid #e9ecef;
        }

        .stat-item {
            text-align: center;
        }

        .stat-number {
            font-size: 2em;
            font-weight: bold;
            color: #2c3e50;
        }

        .stat-label {
            color: #6c757d;
            font-size: 0.9em;
            margin-top: 5px;
        }

        .main-content {
            display: flex;
            min-height: 600px;
        }

        .sidebar {
            width: 300px;
            background: #f8f9fa;
            border-right: 1px solid #e9ecef;
            padding: 20px;
        }

        .content-area {
            flex: 1;
            padding: 20px;
        }

        .filter-section {
            margin-bottom: 30px;
        }

        .filter-section h3 {
            color: #2c3e50;
            margin-bottom: 15px;
            font-size: 1.1em;
        }

        .search-box {
            width: 100%;
            padding: 12px;
            border: 2px solid #e9ecef;
            border-radius: 8px;
            font-size: 14px;
            margin-bottom: 15px;
            transition: border-color 0.3s;
        }

        .search-box:focus {
            outline: none;
            border-color: #667eea;
        }

        .type-filter {
            margin-bottom: 20px;
        }

        .type-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            cursor: pointer;
            padding: 5px;
            border-radius: 5px;
            transition: background-color 0.2s;
        }

        .type-checkbox:hover {
            background-color: #e9ecef;
        }

        .type-checkbox input {
            margin-right: 10px;
        }

        .type-badge {
            background: #667eea;
            color: white;
            padding: 2px 8px;
            border-radius: 12px;
            font-size: 0.8em;
            margin-left: auto;
        }

        .artifacts-grid {
            display: grid;
            gap: 20px;
        }

        .artifact-group {
            background: white;
            border: 1px solid #e9ecef;
            border-radius: 10px;
            overflow: hidden;
            box-shadow: 0 2px 10px rgba(0,0,0,0.05);
        }

        .group-header {
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            color: white;
            padding: 15px 20px;
            display: flex;
            justify-content: space-between;
            align-items: center;
            cursor: pointer;
            transition: all 0.3s;
        }

        .group-header:hover {
            background: linear-gradient(135deg, #5a6fd8 0%, #6a4190 100%);
        }

        .group-title {
            font-size: 1.2em;
            font-weight: 600;
        }

        .group-count {
            background: rgba(255,255,255,0.2);
            padding: 4px 12px;
            border-radius: 15px;
            font-size: 0.9em;
        }

        .group-content {
            max-height: 400px;
            overflow-y: auto;
            transition: max-height 0.3s ease;
        }

        .group-content.collapsed {
            max-height: 0;
            overflow: hidden;
        }

        .artifact-list {
            padding: 0;
            list-style: none;
        }

        .artifact-item {
            padding: 12px 20px;
            border-bottom: 1px solid #f1f3f4;
            display: flex;
            justify-content: space-between;
            align-items: center;
            transition: background-color 0.2s;
        }

        .artifact-item:hover {
            background-color: #f8f9fa;
        }

        .artifact-item:last-child {
            border-bottom: none;
        }

        .artifact-name {
            font-weight: 500;
            color: #2c3e50;
        }

        .artifact-subtype {
            color: #6c757d;
            font-size: 0.9em;
            font-style: italic;
        }

        .loading {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .error {
            text-align: center;
            padding: 50px;
            color: #dc3545;
        }

        .no-results {
            text-align: center;
            padding: 50px;
            color: #6c757d;
        }

        .toggle-icon {
            transition: transform 0.3s;
        }

        .toggle-icon.collapsed {
            transform: rotate(-90deg);
        }

        .filter-button {
            background: #667eea;
            color: white;
            border: none;
            padding: 6px 12px;
            border-radius: 4px;
            font-size: 0.8em;
            cursor: pointer;
            margin-right: 5px;
            transition: background-color 0.2s;
        }

        .filter-button:hover {
            background: #5a6fd8;
        }

        .view-options {
            margin-top: 10px;
        }

        .option-checkbox {
            display: flex;
            align-items: center;
            margin-bottom: 8px;
            cursor: pointer;
            font-size: 0.9em;
        }

        .option-checkbox input {
            margin-right: 8px;
        }

        .object-id {
            font-family: 'Courier New', monospace;
            font-size: 0.8em;
            color: #6c757d;
            margin-top: 2px;
        }

        .object-details {
            font-size: 0.85em;
            color: #6c757d;
            margin-top: 4px;
            font-style: italic;
        }

        .clickable-coach-view {
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 4px;
            padding: 2px 4px;
            margin: -2px -4px;
        }

        .clickable-coach-view:hover {
            background-color: #e3f2fd;
            color: #1565c0;
            transform: translateX(2px);
        }

        .view-details-icon {
            font-size: 0.8em;
            opacity: 0.6;
            margin-left: 5px;
            transition: opacity 0.2s ease;
        }

        .clickable-coach-view:hover .view-details-icon {
            opacity: 1;
        }

        .clickable-cshs {
            cursor: pointer;
            transition: all 0.2s ease;
            border-radius: 4px;
            padding: 2px 4px;
            margin: -2px -4px;
        }

        .clickable-cshs:hover {
            background-color: #fff3e0;
            color: #e65100;
            transform: translateX(2px);
        }

        .clickable-cshs:hover .view-details-icon {
            opacity: 1;
        }

        @media (max-width: 768px) {
            .main-content {
                flex-direction: column;
            }

            .sidebar {
                width: 100%;
            }

            .stats-bar {
                flex-direction: column;
                gap: 15px;
            }
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="header">
            <h1>TWX Artifact Viewer</h1>
            <div class="subtitle">Enhanced Interactive Artifact Browser</div>
            <div id="project-info" class="project-info" style="margin-top: 15px; opacity: 0.9; font-size: 0.95em;">
                <span id="project-name">Loading project information...</span>
            </div>
        </div>

        <div class="stats-bar">
            <div class="stat-item">
                <div class="stat-number" id="total-artifacts">-</div>
                <div class="stat-label">Total Artifacts</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="total-types">-</div>
                <div class="stat-label">Artifact Types</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="total-toolkits">-</div>
                <div class="stat-label">Toolkits</div>
            </div>
            <div class="stat-item">
                <div class="stat-number" id="parse-date">-</div>
                <div class="stat-label">Last Parsed</div>
            </div>
        </div>

        <div class="main-content">
            <div class="sidebar">
                <div class="filter-section">
                    <h3>🔍 Search Artifacts</h3>
                    <input type="text" class="search-box" id="search-input" placeholder="Search by name...">
                </div>

                <div class="filter-section">
                    <h3>📂 Filter by Type</h3>
                    <div id="type-filters" class="type-filter">
                        <!-- Type filters will be populated by JavaScript -->
                    </div>
                    <div style="margin-top: 10px;">
                        <button id="select-all-types" class="filter-button">Select All</button>
                        <button id="clear-all-types" class="filter-button">Clear All</button>
                    </div>
                </div>

                <div class="filter-section">
                    <h3>📊 View Options</h3>
                    <div class="view-options">
                        <label class="option-checkbox">
                            <input type="checkbox" id="show-details" checked>
                            Show object details
                        </label>
                        <label class="option-checkbox">
                            <input type="checkbox" id="show-ids">
                            Show object IDs
                        </label>
                    </div>
                </div>
            </div>

            <div class="content-area">
                <div id="loading" class="loading">
                    <h3>Loading TWX parsing results...</h3>
                    <p>Please wait while we load the data.</p>
                </div>

                <div id="error" class="error" style="display: none;">
                    <h3>Error Loading Data</h3>
                    <p>Could not load the parsing results. Please make sure the parsing has been completed.</p>
                </div>

                <div id="artifacts-container" class="artifacts-grid" style="display: none;">
                    <!-- Artifact groups will be populated by JavaScript -->
                </div>

                <div id="no-results" class="no-results" style="display: none;">
                    <h3>No Results Found</h3>
                    <p>Try adjusting your search or filter criteria.</p>
                </div>
            </div>
        </div>
    </div>

    <script src="script-highlighter.js"></script>
    <script src="coach-view-details.js"></script>
    <script src="CSHSDetails.js"></script>
    <script src="CSHSDetailsInline.js"></script>
    <script src="twx-viewer-enhanced.js"></script>
</body>
</html>
