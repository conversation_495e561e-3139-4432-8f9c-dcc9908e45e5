{"id": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "versionId": "41ebe9f1-593e-4b17-a752-6b1b929d7bf0", "name": "Retrieve ProductCode DB", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "name": "Retrieve ProductCode DB", "lastModified": "1700232877472", "lastModifiedBy": "mohamed.reda", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.c54ecbb0-bbca-4068-8cab-c184f9a2e1fd", "2025.c54ecbb0-bbca-4068-8cab-c184f9a2e1fd"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6bed", "versionId": "41ebe9f1-593e-4b17-a752-6b1b929d7bf0", "dependencySummary": "<dependencySummary id=\"bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6d6d\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.23cd8767-1aae-416d-8f65-70ec7d9fc650\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"79cff2fd-089b-457f-8282-73e7875e381a\"},{\"incoming\":[\"244ea65e-495b-4d9e-8700-02744323b2ba\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6bef\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"f6a135e6-c51b-4b69-8fba-10588faf44aa\"},{\"targetRef\":\"c54ecbb0-bbca-4068-8cab-c184f9a2e1fd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Select SQL\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.23cd8767-1aae-416d-8f65-70ec7d9fc650\",\"sourceRef\":\"79cff2fd-089b-457f-8282-73e7875e381a\"},{\"startQuantity\":1,\"outgoing\":[\"2e465244-5d3f-427c-8a80-c7538a063e1b\"],\"incoming\":[\"0218788e-0fa2-49e8-8bea-1dac4f3cff36\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":302,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Execute SQL\",\"dataInputAssociation\":[{\"targetRef\":\"2055.25f5990f-9abf-4cf6-9101-497d43dee141\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sql\"]}}]},{\"targetRef\":\"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parameters\"]}}]},{\"targetRef\":\"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.DATASOURCE_AUDIT\"]}}]},{\"targetRef\":\"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"declaredType\":\"TFormalExpression\",\"content\":[\"-1\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"e66a9a19-7dc9-4278-8173-83b54d747ee3\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.sqlResults\"]}}],\"sourceRef\":[\"2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764\"]}],\"calledElement\":\"1.4e480adb-5741-4f5e-aead-27b3654e9cd2\"},{\"targetRef\":\"eece5fdf-0372-4ffc-893f-7f90a2769168\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To MapOutput\",\"declaredType\":\"sequenceFlow\",\"id\":\"2e465244-5d3f-427c-8a80-c7538a063e1b\",\"sourceRef\":\"e66a9a19-7dc9-4278-8173-83b54d747ee3\"},{\"startQuantity\":1,\"outgoing\":[\"0218788e-0fa2-49e8-8bea-1dac4f3cff36\"],\"incoming\":[\"2027.23cd8767-1aae-416d-8f65-70ec7d9fc650\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":110,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Select SQL\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"c54ecbb0-bbca-4068-8cab-c184f9a2e1fd\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"var requestTable = tw.env.DBSchema+\\\".ODC_REQUESTINFO\\\"\\r\\n\\r\\ntw.local.sql = \\\"SELECT PRODUCTCODE FROM \\\"+requestTable+\\\" WHERE REQUESTNO = ?\\\";\\r\\n\\r\\ntw.local.parameters = new tw.object.listOf.SQLParameter();\\r\\ntw.local.parameters[0] = new tw.object.SQLParameter();\\r\\ntw.local.parameters[0].value = tw.local.requestNO;\\r\\n\"]}},{\"targetRef\":\"e66a9a19-7dc9-4278-8173-83b54d747ee3\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Execute SQL\",\"declaredType\":\"sequenceFlow\",\"id\":\"0218788e-0fa2-49e8-8bea-1dac4f3cff36\",\"sourceRef\":\"c54ecbb0-bbca-4068-8cab-c184f9a2e1fd\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLParameter();\\nautoObject[0].value = null;\\nautoObject[0].type = \\\"\\\";\\nautoObject[0].mode = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c\",\"name\":\"parameters\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.7378e9b3-efa2-438f-874d-de19d342cfd5\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"sql\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.14de5261-e804-425f-8e2a-81e5b16898ae\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();\\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();\\nautoObject[0].type = \\\"\\\";\\nautoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();\\nautoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();\\nautoObject[0].columns[0].catalogName = \\\"\\\";\\nautoObject[0].columns[0].columnClassName = \\\"\\\";\\nautoObject[0].columns[0].columnDisplaySize = 0;\\nautoObject[0].columns[0].columnLabel = \\\"\\\";\\nautoObject[0].columns[0].columnName = \\\"\\\";\\nautoObject[0].columns[0].columnTypeName = \\\"\\\";\\nautoObject[0].columns[0].precision = 0;\\nautoObject[0].columns[0].scale = 0;\\nautoObject[0].columns[0].schemaName = \\\"\\\";\\nautoObject[0].columns[0].tableName = \\\"\\\";\\nautoObject[0].columns[0].autoIncrement = false;\\nautoObject[0].columns[0].caseSensitive = false;\\nautoObject[0].columns[0].currency = false;\\nautoObject[0].columns[0].definitelyWritable = false;\\nautoObject[0].columns[0].nullable = 0;\\nautoObject[0].columns[0].readOnly = false;\\nautoObject[0].columns[0].searchable = false;\\nautoObject[0].columns[0].signed = false;\\nautoObject[0].columns[0].writable = false;\\nautoObject[0].columnIndexes = null;\\nautoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();\\nautoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();\\nautoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();\\nautoObject[0].rows[0].data[0] = null;\\nautoObject[0].rows[0].indexedMap = null;\\nautoObject[0].updateCount = 0;\\nautoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();\\nautoObject[0].outValues[0] = null;\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1\",\"name\":\"sqlResults\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.0e373af2-f4dc-4f3f-8556-7b3f7b97afd4\"},{\"startQuantity\":1,\"outgoing\":[\"244ea65e-495b-4d9e-8700-02744323b2ba\"],\"incoming\":[\"2e465244-5d3f-427c-8a80-c7538a063e1b\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":474,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"MapOutput\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"eece5fdf-0372-4ffc-893f-7f90a2769168\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.isfound = true;\\r\\nif (tw.local.sqlResults[0].rows.listLength == 0) {\\r\\n\\ttw.local.isfound = false;\\r\\n\\ttw.local.message = \\\"Faild to retrieve Product Code\\\";\\r\\n}else{\\r\\n\\ttw.local.productCode = tw.local.sqlResults[0].rows[0].data[0];\\r\\n\\r\\n}\"]}},{\"targetRef\":\"f6a135e6-c51b-4b69-8fba-10588faf44aa\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"244ea65e-495b-4d9e-8700-02744323b2ba\",\"sourceRef\":\"eece5fdf-0372-4ffc-893f-7f90a2769168\"}],\"laneSet\":[{\"id\":\"49ad595a-eaf5-44d6-8f64-5e38899b13b8\",\"lane\":[{\"flowNodeRef\":[\"79cff2fd-089b-457f-8282-73e7875e381a\",\"f6a135e6-c51b-4b69-8fba-10588faf44aa\",\"e66a9a19-7dc9-4278-8173-83b54d747ee3\",\"c54ecbb0-bbca-4068-8cab-c184f9a2e1fd\",\"eece5fdf-0372-4ffc-893f-7f90a2769168\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"1e15f251-1a8f-491a-89a5-5b9acea3b83b\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Retrieve ProductCode DB\",\"declaredType\":\"process\",\"id\":\"1.0ed017b2-b582-4d5c-b5b5-3649c89ee192\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isfound\",\"isCollection\":false,\"id\":\"2055.e6db1787-627d-4373-80cc-75c8940bc32d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"message\",\"isCollection\":false,\"id\":\"2055.e1811b7a-e326-4b9c-863e-59e994c41703\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"productCode\",\"isCollection\":false,\"id\":\"2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.739ec1bd-4791-48bc-830c-f00e254474a3\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.e6db1787-627d-4373-80cc-75c8940bc32d\",\"2055.e1811b7a-e326-4b9c-863e-59e994c41703\",\"2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"00104230000170\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestNO\",\"isCollection\":false,\"id\":\"2055.739ec1bd-4791-48bc-830c-f00e254474a3\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestNO", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.739ec1bd-4791-48bc-830c-f00e254474a3", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "\"00104230000170\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "6d420889-480b-40b7-b62f-4be8c654fd2b", "versionId": "965f2293-21b6-4da9-a6b8-f61734a09d17"}, {"name": "isfound", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e6db1787-627d-4373-80cc-75c8940bc32d", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "ffd8439b-b5a2-4308-af4f-6b5bb6d776ed", "versionId": "675fb2c9-e344-4a07-a3aa-8a813a18a137"}, {"name": "message", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e1811b7a-e326-4b9c-863e-59e994c41703", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "2a6809ea-5b9e-4b5e-ba81-ade5d26f1d05", "versionId": "8cb910a7-12d2-4675-85fd-908c8e671e86"}, {"name": "productCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "ca3d2c25-771a-4467-9f6e-dc804677e9ef", "versionId": "ad472443-a31d-4379-b1d3-67f3a1f24471"}], "processVariable": [{"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7378e9b3-efa2-438f-874d-de19d342cfd5", "description": {"isNull": "true"}, "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\nautoObject[0].value = null;\r\nautoObject[0].type = \"\";\r\nautoObject[0].mode = \"\";\r\nautoObject", "guid": "76af5bd9-b9ba-4823-b1d8-67faecd972d0", "versionId": "5d3649d8-295e-4ae1-89d2-1f3bfe19d975"}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.14de5261-e804-425f-8e2a-81e5b16898ae", "description": {"isNull": "true"}, "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "5db39a56-e19a-4793-8e2b-962702ea19db", "versionId": "4046221f-685f-4cb6-935a-0d2421b1ba5e"}, {"name": "sqlResults", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0e373af2-f4dc-4f3f-8556-7b3f7b97afd4", "description": {"isNull": "true"}, "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();\r\nautoObject[0].type = \"\";\r\nautoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();\r\nautoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();\r\nautoObject[0].columns[0].catalogName = \"\";\r\nautoObject[0].columns[0].columnClassName = \"\";\r\nautoObject[0].columns[0].columnDisplaySize = 0;\r\nautoObject[0].columns[0].columnLabel = \"\";\r\nautoObject[0].columns[0].columnName = \"\";\r\nautoObject[0].columns[0].columnTypeName = \"\";\r\nautoObject[0].columns[0].precision = 0;\r\nautoObject[0].columns[0].scale = 0;\r\nautoObject[0].columns[0].schemaName = \"\";\r\nautoObject[0].columns[0].tableName = \"\";\r\nautoObject[0].columns[0].autoIncrement = false;\r\nautoObject[0].columns[0].caseSensitive = false;\r\nautoObject[0].columns[0].currency = false;\r\nautoObject[0].columns[0].definitelyWritable = false;\r\nautoObject[0].columns[0].nullable = 0;\r\nautoObject[0].columns[0].readOnly = false;\r\nautoObject[0].columns[0].searchable = false;\r\nautoObject[0].columns[0].signed = false;\r\nautoObject[0].columns[0].writable = false;\r\nautoObject[0].columnIndexes = null;\r\nautoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();\r\nautoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();\r\nautoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();\r\nautoObject[0].rows[0].data[0] = null;\r\nautoObject[0].rows[0].indexedMap = null;\r\nautoObject[0].updateCount = 0;\r\nautoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();\r\nautoObject[0].outValues[0] = null;\r\nautoObject", "guid": "0c1d8a7f-81d2-4743-b66e-e4163cbb21d8", "versionId": "579820f0-286d-4a7f-a304-feae76b9dec4"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e66a9a19-7dc9-4278-8173-83b54d747ee3", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "name": "Execute SQL", "tWComponentName": "SubProcess", "tWComponentId": "3012.4c25cce5-a9a9-44b5-8b54-39c870c3f5ba", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6bf1", "versionId": "1e709d51-0bd8-4e81-a0fc-dea5e39dfd5a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "302", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4c25cce5-a9a9-44b5-8b54-39c870c3f5ba", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "2dfc4ef9-5a72-4a2e-aa16-afe580254a24", "versionId": "c64eb93b-56db-4344-8f11-907e0588b9ac", "parameterMapping": [{"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ddd904e0-5224-4d10-8009-3e9a3771f628", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.4c25cce5-a9a9-44b5-8b54-39c870c3f5ba", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "00e968d7-9101-4fef-b6cc-40215c7bb921", "versionId": "30cba496-f70e-496d-832d-311d922315bc", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c86941bb-5c8b-41f8-a92f-05afefb17579", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.4c25cce5-a9a9-44b5-8b54-39c870c3f5ba", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "7dfb1546-9057-4df8-9c01-a096df660223", "versionId": "7077f43d-822e-4fcb-85bf-70db2d148270", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.214e62df-610f-40b3-8935-880c1cb121a1", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.4c25cce5-a9a9-44b5-8b54-39c870c3f5ba", "useDefault": "false", "value": "tw.env.DATASOURCE_AUDIT", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "ab357a22-4c2f-4315-a274-f8d6f15a1d8b", "versionId": "84b03834-e064-4f0a-bd8c-6fac4004e627", "description": {"isNull": "true"}}, {"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.999d0525-2b31-4416-967b-45227a56e815", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.4c25cce5-a9a9-44b5-8b54-39c870c3f5ba", "useDefault": "false", "value": "tw.local.parameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "1324cf8a-10f1-4658-a7e9-d3da76261e7b", "versionId": "e7aadeec-6468-4e2f-8c28-ab0221e6f3c2", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.9c8e29b3-7ba3-446a-bb26-e9d806a9c2ac", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.4c25cce5-a9a9-44b5-8b54-39c870c3f5ba", "useDefault": "false", "value": "tw.local.sqlResults", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "0c1f5ee6-0654-45e8-b7ef-d4c8cf91c6b6", "versionId": "fb48f0e0-8847-4e51-9f1c-5c2e66bec42c", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f6a135e6-c51b-4b69-8fba-10588faf44aa", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.7179b2c5-bb1e-4dcd-8688-01b0dc52e0aa", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6bef", "versionId": "440a54ad-4725-45ab-b930-22043083db37", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.7179b2c5-bb1e-4dcd-8688-01b0dc52e0aa", "haltProcess": "false", "guid": "583c15f5-2d06-4412-8189-e994311d2a99", "versionId": "317ccf55-016a-44fc-bfc9-0637f0c08df9"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.eece5fdf-0372-4ffc-893f-7f90a2769168", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "name": "MapOutput", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.1b2f908e-4f90-4aa2-b59f-9aa9fd4af8ea", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6d5b", "versionId": "60c50e4f-c0ac-4011-ad17-a7e01739ee70", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "474", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.1b2f908e-4f90-4aa2-b59f-9aa9fd4af8ea", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.isfound = true;\r\r\nif (tw.local.sqlResults[0].rows.listLength == 0) {\r\r\n\ttw.local.isfound = false;\r\r\n\ttw.local.message = \"Faild to retrieve Product Code\";\r\r\n}else{\r\r\n\ttw.local.productCode = tw.local.sqlResults[0].rows[0].data[0];\r\r\n\r\r\n}", "isRule": "false", "guid": "c4ce007f-20f9-488d-bb27-359c1b06e49c", "versionId": "88b4aff1-2ec1-401c-93a8-fc76e193b091"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c54ecbb0-bbca-4068-8cab-c184f9a2e1fd", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "name": "Select SQL", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.b5739d48-a88b-4f6b-9380-490a32318b1e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6c00", "versionId": "fd61956d-5ff4-405b-8afb-40dc06a74fd9", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "110", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.b5739d48-a88b-4f6b-9380-490a32318b1e", "scriptTypeId": "2", "isActive": "true", "script": "var requestTable = tw.env.DBSchema+\".ODC_REQUESTINFO\"\r\r\n\r\r\ntw.local.sql = \"SELECT PRODUCTCODE FROM \"+requestTable+\" WHERE REQUESTNO = ?\";\r\r\n\r\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.parameters[0] = new tw.object.SQLParameter();\r\r\ntw.local.parameters[0].value = tw.local.requestNO;\r\r\n", "isRule": "false", "guid": "2dbcf937-2ee2-41a3-abfe-f8eba42a73b7", "versionId": "c29028a0-47d7-4225-970a-17c748f99235"}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Retrieve ProductCode DB", "id": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "requestNO", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.739ec1bd-4791-48bc-830c-f00e254474a3", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"00104230000170\"", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "isfound", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.e6db1787-627d-4373-80cc-75c8940bc32d"}, {"name": "message", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.e1811b7a-e326-4b9c-863e-59e994c41703"}, {"name": "productCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1"}], "ns16:inputSet": {"ns16:dataInputRefs": "2055.739ec1bd-4791-48bc-830c-f00e254474a3"}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.e6db1787-627d-4373-80cc-75c8940bc32d", "2055.e1811b7a-e326-4b9c-863e-59e994c41703", "2055.f5e7bc0d-d04d-4cfc-8637-35e7e6adc9c1"]}}, "ns16:laneSet": {"id": "49ad595a-eaf5-44d6-8f64-5e38899b13b8", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "1e15f251-1a8f-491a-89a5-5b9acea3b83b", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["79cff2fd-089b-457f-8282-73e7875e381a", "f6a135e6-c51b-4b69-8fba-10588faf44aa", "e66a9a19-7dc9-4278-8173-83b54d747ee3", "c54ecbb0-bbca-4068-8cab-c184f9a2e1fd", "eece5fdf-0372-4ffc-893f-7f90a2769168"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "79cff2fd-089b-457f-8282-73e7875e381a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.23cd8767-1aae-416d-8f65-70ec7d9fc650"}, "ns16:endEvent": {"name": "End", "id": "f6a135e6-c51b-4b69-8fba-10588faf44aa", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6bef"}, "ns16:incoming": "244ea65e-495b-4d9e-8700-02744323b2ba"}, "ns16:sequenceFlow": [{"sourceRef": "79cff2fd-089b-457f-8282-73e7875e381a", "targetRef": "c54ecbb0-bbca-4068-8cab-c184f9a2e1fd", "name": "To Select SQL", "id": "2027.23cd8767-1aae-416d-8f65-70ec7d9fc650", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "e66a9a19-7dc9-4278-8173-83b54d747ee3", "targetRef": "eece5fdf-0372-4ffc-893f-7f90a2769168", "name": "To MapOutput", "id": "2e465244-5d3f-427c-8a80-c7538a063e1b", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"}}, {"sourceRef": "c54ecbb0-bbca-4068-8cab-c184f9a2e1fd", "targetRef": "e66a9a19-7dc9-4278-8173-83b54d747ee3", "name": "To Execute SQL", "id": "0218788e-0fa2-49e8-8bea-1dac4f3cff36", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "eece5fdf-0372-4ffc-893f-7f90a2769168", "targetRef": "f6a135e6-c51b-4b69-8fba-10588faf44aa", "name": "To End", "id": "244ea65e-495b-4d9e-8700-02744323b2ba", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:callActivity": {"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "Execute SQL", "id": "e66a9a19-7dc9-4278-8173-83b54d747ee3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "302", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "0218788e-0fa2-49e8-8bea-1dac4f3cff36", "ns16:outgoing": "2e465244-5d3f-427c-8a80-c7538a063e1b", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.parameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE_AUDIT", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.sqlResults", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Select SQL", "id": "c54ecbb0-bbca-4068-8cab-c184f9a2e1fd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "110", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.23cd8767-1aae-416d-8f65-70ec7d9fc650", "ns16:outgoing": "0218788e-0fa2-49e8-8bea-1dac4f3cff36", "ns16:script": "var requestTable = tw.env.DBSchema+\".ODC_REQUESTINFO\"\r\r\n\r\r\ntw.local.sql = \"SELECT PRODUCTCODE FROM \"+requestTable+\" WHERE REQUESTNO = ?\";\r\r\n\r\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.parameters[0] = new tw.object.SQLParameter();\r\r\ntw.local.parameters[0].value = tw.local.requestNO;\r\r\n"}, {"scriptFormat": "text/x-javascript", "name": "MapOutput", "id": "eece5fdf-0372-4ffc-893f-7f90a2769168", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "474", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2e465244-5d3f-427c-8a80-c7538a063e1b", "ns16:outgoing": "244ea65e-495b-4d9e-8700-02744323b2ba", "ns16:script": "tw.local.isfound = true;\r\r\nif (tw.local.sqlResults[0].rows.listLength == 0) {\r\r\n\ttw.local.isfound = false;\r\r\n\ttw.local.message = \"Faild to retrieve Product Code\";\r\r\n}else{\r\r\n\ttw.local.productCode = tw.local.sqlResults[0].rows[0].data[0];\r\r\n\r\r\n}"}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "parameters", "id": "2056.7378e9b3-efa2-438f-874d-de19d342cfd5", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\nautoObject[0].value = null;\r\nautoObject[0].type = \"\";\r\nautoObject[0].mode = \"\";\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.14de5261-e804-425f-8e2a-81e5b16898ae"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "sqlResults", "id": "2056.0e373af2-f4dc-4f3f-8556-7b3f7b97afd4", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLResult();\r\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLResult();\r\nautoObject[0].type = \"\";\r\nautoObject[0].columns = new tw.object.listOf.toolkit.TWSYS.SQLResultSetColumn();\r\nautoObject[0].columns[0] = new tw.object.toolkit.TWSYS.SQLResultSetColumn();\r\nautoObject[0].columns[0].catalogName = \"\";\r\nautoObject[0].columns[0].columnClassName = \"\";\r\nautoObject[0].columns[0].columnDisplaySize = 0;\r\nautoObject[0].columns[0].columnLabel = \"\";\r\nautoObject[0].columns[0].columnName = \"\";\r\nautoObject[0].columns[0].columnTypeName = \"\";\r\nautoObject[0].columns[0].precision = 0;\r\nautoObject[0].columns[0].scale = 0;\r\nautoObject[0].columns[0].schemaName = \"\";\r\nautoObject[0].columns[0].tableName = \"\";\r\nautoObject[0].columns[0].autoIncrement = false;\r\nautoObject[0].columns[0].caseSensitive = false;\r\nautoObject[0].columns[0].currency = false;\r\nautoObject[0].columns[0].definitelyWritable = false;\r\nautoObject[0].columns[0].nullable = 0;\r\nautoObject[0].columns[0].readOnly = false;\r\nautoObject[0].columns[0].searchable = false;\r\nautoObject[0].columns[0].signed = false;\r\nautoObject[0].columns[0].writable = false;\r\nautoObject[0].columnIndexes = null;\r\nautoObject[0].rows = new tw.object.listOf.toolkit.TWSYS.IndexedMap();\r\nautoObject[0].rows[0] = new tw.object.toolkit.TWSYS.IndexedMap();\r\nautoObject[0].rows[0].data = new tw.object.listOf.toolkit.TWSYS.ANY();\r\nautoObject[0].rows[0].data[0] = null;\r\nautoObject[0].rows[0].indexedMap = null;\r\nautoObject[0].updateCount = 0;\r\nautoObject[0].outValues = new tw.object.listOf.toolkit.TWSYS.ANY();\r\nautoObject[0].outValues[0] = null;\r\nautoObject", "useDefault": "true"}}}]}}}, "link": [{"name": "To Execute SQL", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.0218788e-0fa2-49e8-8bea-1dac4f3cff36", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.c54ecbb0-bbca-4068-8cab-c184f9a2e1fd", "2025.c54ecbb0-bbca-4068-8cab-c184f9a2e1fd"], "endStateId": "Out", "toProcessItemId": ["2025.e66a9a19-7dc9-4278-8173-83b54d747ee3", "2025.e66a9a19-7dc9-4278-8173-83b54d747ee3"], "guid": "7416d0a5-8fd8-4a7a-96a8-591bb41331b2", "versionId": "5cd032f7-9cac-4778-993a-2006c9a3c484", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.244ea65e-495b-4d9e-8700-02744323b2ba", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.eece5fdf-0372-4ffc-893f-7f90a2769168", "2025.eece5fdf-0372-4ffc-893f-7f90a2769168"], "endStateId": "Out", "toProcessItemId": ["2025.f6a135e6-c51b-4b69-8fba-10588faf44aa", "2025.f6a135e6-c51b-4b69-8fba-10588faf44aa"], "guid": "c3883eea-d116-4074-a48e-cbee8e0832b6", "versionId": "849dfcc6-8fde-436a-a3f7-964a3dcfa273", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To MapOutput", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.2e465244-5d3f-427c-8a80-c7538a063e1b", "processId": "1.0ed017b2-b582-4d5c-b5b5-3649c89ee192", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e66a9a19-7dc9-4278-8173-83b54d747ee3", "2025.e66a9a19-7dc9-4278-8173-83b54d747ee3"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.eece5fdf-0372-4ffc-893f-7f90a2769168", "2025.eece5fdf-0372-4ffc-893f-7f90a2769168"], "guid": "8b8807a3-c52b-48b5-92f7-bb6a6c02d0b8", "versionId": "d0283a18-c59a-4791-802f-f49e1071e7d8", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}