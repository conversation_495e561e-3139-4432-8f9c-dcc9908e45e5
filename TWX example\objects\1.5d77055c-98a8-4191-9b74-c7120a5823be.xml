<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.5d77055c-98a8-4191-9b74-c7120a5823be" name="Create">
        <lastModified>1740336614874</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.44d34d2c-0346-4e92-837b-4f5ad945b031</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-2aa6</guid>
        <versionId>78d80004-b3e8-4015-8160-b3ae3b3ad3ea</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:faf129f3cc554db6:5a40ed45:195332c2764:-3b67" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.52cf5142-3d70-4089-81c5-ce5c2a7c10ad"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b2c15844-2e24-4523-8b45-5b2fa8a5951b"},{"incoming":["9a49916c-f5c5-409b-8a34-03cb610f5a3d","5abb97db-c04a-46e6-87d1-eaebc2cc486b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":696,"y":81,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:-2aa4"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"1a814772-52df-4dc1-8881-2f212b065b42"},{"targetRef":"44d34d2c-0346-4e92-837b-4f5ad945b031","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.52cf5142-3d70-4089-81c5-ce5c2a7c10ad","sourceRef":"b2c15844-2e24-4523-8b45-5b2fa8a5951b"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject[0] = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"separatedPath","isCollection":true,"declaredType":"dataObject","id":"2056.e8193ea9-74a7-4ced-8073-eaf9652dd9e3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentFolderPath","isCollection":false,"declaredType":"dataObject","id":"2056.3b96a48d-63c6-4bb9-808d-0111417433e4"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"iterator","isCollection":false,"declaredType":"dataObject","id":"2056.e3e87f58-**************-8827165d49cc"},{"startQuantity":1,"outgoing":["bf053733-2b38-4333-886d-8b18a4be952f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":126,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Splitting Fullpath","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"982deb5c-065e-4dbf-8b7e-5bca263ceeb6","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.iterator =0;\r\ntw.local.separatedPath = new tw.object.listOf.String();\r\ntw.local.separatedPath =tw.local.Path.split(\"\/\");"]}},{"startQuantity":1,"outgoing":["78c26f0f-3dc1-445a-83b8-9e8dad43e526"],"incoming":["bf053733-2b38-4333-886d-8b18a4be952f","a24fd892-0c77-439e-819c-6cdf6840e54b"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":254,"y":58,"declaredType":"TNodeVisualInfo","height":70}]},"name":"creating folder  path","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8a4503de-46f5-4879-8d54-834321f21014","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.iterator == 0)\r\n{\r\n\ttw.local.parentFolderPath = tw.env.FILENET_ROOT_PATH;\r\n}\r\nelse\r\n{\r\n\ttw.local.parentFolderPath += \"\/\"+tw.local.separatedPath[tw.local.iterator-1];\r\n}\r\n "]}},{"targetRef":"8a4503de-46f5-4879-8d54-834321f21014","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To creating folder  path","declaredType":"sequenceFlow","id":"bf053733-2b38-4333-886d-8b18a4be952f","sourceRef":"982deb5c-065e-4dbf-8b7e-5bca263ceeb6"},{"startQuantity":1,"outgoing":["f73798b3-98b8-4385-8cf9-3806557b7f42"],"incoming":["78c26f0f-3dc1-445a-83b8-9e8dad43e526"],"extensionElements":{"postAssignmentScript":["tw.local.iterator += 1;"],"nodeVisualInfo":[{"width":95,"x":389,"y":58,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"create folder on filenet","dataInputAssociation":[{"targetRef":"2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentFolderPath"]}}]},{"targetRef":"2055.98c175d8-7e63-4e2d-ac3f-************","assignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.separatedPath[tw.local.iterator]"]}}]},{"targetRef":"2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.FolderID"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1e825648-a142-4232-8c47-3327fd38a4e0","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.FolderID"]}}],"sourceRef":["2055.21385f08-1be8-41a4-b758-c8dc9b8bb509"]}],"calledElement":"1.8a1c5f67-55b2-4785-97cb-9ec3e4441186"},{"targetRef":"1e825648-a142-4232-8c47-3327fd38a4e0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To create folder on filenet","declaredType":"sequenceFlow","id":"78c26f0f-3dc1-445a-83b8-9e8dad43e526","sourceRef":"8a4503de-46f5-4879-8d54-834321f21014"},{"targetRef":"a372326b-5775-406b-87a5-e3c75dc2ca3a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:-15c3"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"f73798b3-98b8-4385-8cf9-3806557b7f42","sourceRef":"1e825648-a142-4232-8c47-3327fd38a4e0"},{"outgoing":["9a49916c-f5c5-409b-8a34-03cb610f5a3d","a24fd892-0c77-439e-819c-6cdf6840e54b"],"incoming":["f73798b3-98b8-4385-8cf9-3806557b7f42"],"default":"a24fd892-0c77-439e-819c-6cdf6840e54b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":539,"y":77,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"a372326b-5775-406b-87a5-e3c75dc2ca3a"},{"targetRef":"1a814772-52df-4dc1-8881-2f212b065b42","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.iterator\t  &lt;\t  tw.local.separatedPath.listLength"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"9a49916c-f5c5-409b-8a34-03cb610f5a3d","sourceRef":"a372326b-5775-406b-87a5-e3c75dc2ca3a"},{"targetRef":"8a4503de-46f5-4879-8d54-834321f21014","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To creating folder on file net ","declaredType":"sequenceFlow","id":"a24fd892-0c77-439e-819c-6cdf6840e54b","sourceRef":"a372326b-5775-406b-87a5-e3c75dc2ca3a"},{"startQuantity":1,"outgoing":["5abb97db-c04a-46e6-87d1-eaebc2cc486b"],"incoming":["2027.52cf5142-3d70-4089-81c5-ce5c2a7c10ad"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":331,"y":199,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"44d34d2c-0346-4e92-837b-4f5ad945b031","scriptFormat":"text\/x-javascript","script":{"content":["\/\/throw \"Child Error\""]}},{"targetRef":"1a814772-52df-4dc1-8881-2f212b065b42","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"5abb97db-c04a-46e6-87d1-eaebc2cc486b","sourceRef":"44d34d2c-0346-4e92-837b-4f5ad945b031"},{"startQuantity":1,"outgoing":["bc89a4d2-c2bf-42c8-880f-5e4043b52d02"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":344,"y":324,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Linked Service Flow","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Child\""]}}]},{"targetRef":"2055.d1830a67-1c3d-403f-85ac-213a58225a6c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"fcf3ebff-231f-441e-824e-9f622b3d95a6","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"incoming":["bc89a4d2-c2bf-42c8-880f-5e4043b52d02"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"cd4bfd07-0df6-404b-85c3-2f031b574f3c","otherAttributes":{"eventImplId":"d820fc71-1cbd-4bb8-8b39-2b344d3ce8cf"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":730,"y":327,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}]}],"declaredType":"endEvent","id":"df737eb7-eae7-435a-82d7-fd4dcd366a60"},{"targetRef":"df737eb7-eae7-435a-82d7-fd4dcd366a60","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"bc89a4d2-c2bf-42c8-880f-5e4043b52d02","sourceRef":"fcf3ebff-231f-441e-824e-9f622b3d95a6"}],"laneSet":[{"id":"463c74ad-f652-416f-86f7-ec6d3d2c3a9b","lane":[{"flowNodeRef":["b2c15844-2e24-4523-8b45-5b2fa8a5951b","1a814772-52df-4dc1-8881-2f212b065b42","982deb5c-065e-4dbf-8b7e-5bca263ceeb6","8a4503de-46f5-4879-8d54-834321f21014","1e825648-a142-4232-8c47-3327fd38a4e0","a372326b-5775-406b-87a5-e3c75dc2ca3a","44d34d2c-0346-4e92-837b-4f5ad945b031","fcf3ebff-231f-441e-824e-9f622b3d95a6","df737eb7-eae7-435a-82d7-fd4dcd366a60"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"2a672723-6f73-44f0-8db7-47b10d7107ff","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create","declaredType":"process","id":"1.5d77055c-98a8-4191-9b74-c7120a5823be","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01","name":"FolderID","isCollection":false,"id":"2055.41d4d7e4-dd09-4686-87cc-5816b6aaa1f8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.9ff5ab2a-ddd9-4338-81b2-f5b9b9237fa8"}],"inputSet":[{"dataInputRefs":["2055.094f9338-8841-4f14-80be-d92350c25b90"]}],"outputSet":[{"dataOutputRefs":["2055.41d4d7e4-dd09-4686-87cc-5816b6aaa1f8","2055.9ff5ab2a-ddd9-4338-81b2-f5b9b9237fa8"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"\"12345\/DC Outward\/122\/ODC\/Issuance\";"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"Path","isCollection":false,"id":"2055.094f9338-8841-4f14-80be-d92350c25b90"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="Path">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.094f9338-8841-4f14-80be-d92350c25b90</processParameterId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>46f0c64f-e233-4b87-8a96-bc4ae09b0b3d</guid>
            <versionId>44a08f1f-a77a-4f96-aaab-1c235f66d82e</versionId>
        </processParameter>
        <processParameter name="FolderID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.41d4d7e4-dd09-4686-87cc-5816b6aaa1f8</processParameterId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>3200e358-d1cb-4bfc-9060-7aeab0b41165</guid>
            <versionId>3ce98a20-fada-4400-95da-ab768d26a058</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9ff5ab2a-ddd9-4338-81b2-f5b9b9237fa8</processParameterId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>8b16f8d5-48c4-44c4-b5af-8225412092cc</guid>
            <versionId>d51524a5-97f7-48b6-af7c-3a2e752f0a13</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.ddfecda2-ed78-4535-acd1-fac0696c896f</processParameterId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f64bfd78-416a-4f78-8045-c56a9ec715df</guid>
            <versionId>843b1743-f579-4ff3-a8c8-11d13f522a18</versionId>
        </processParameter>
        <processVariable name="separatedPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e8193ea9-74a7-4ced-8073-eaf9652dd9e3</processVariableId>
            <description isNull="true" />
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "";
autoObject</defaultValue>
            <guid>f893fa3c-3628-4d9e-8367-ff328951cb18</guid>
            <versionId>4ec8ad50-af07-4aab-bf52-e31887a6a00a</versionId>
        </processVariable>
        <processVariable name="parentFolderPath">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3b96a48d-63c6-4bb9-808d-0111417433e4</processVariableId>
            <description isNull="true" />
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cc787e80-4194-4f47-ba07-49bdf2f087b2</guid>
            <versionId>0e9e4596-c4a5-4930-a085-b0fb4ec56151</versionId>
        </processVariable>
        <processVariable name="iterator">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.e3e87f58-**************-8827165d49cc</processVariableId>
            <description isNull="true" />
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c5dae41d-06ba-4e4f-b9af-7ecf50bf8072</guid>
            <versionId>89a56fce-0adb-41c0-87c9-af74e92b1b5f</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1e825648-a142-4232-8c47-3327fd38a4e0</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>create folder on filenet</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.5f760ca7-ed88-41bb-b566-c995d5ca9ca7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1bb9</guid>
            <versionId>11f80043-a183-470d-a6d7-988bcb636e11</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.f18ce99c-6c96-4e85-9386-d1bb20f466fe</processItemPrePostId>
                <processItemId>2025.1e825648-a142-4232-8c47-3327fd38a4e0</processItemId>
                <location>2</location>
                <script>tw.local.iterator += 1;</script>
                <guid>dc44a45d-7279-449d-9406-7bb9aefd5676</guid>
                <versionId>bb11bff0-1c86-4225-999c-d5bb0b8becd4</versionId>
            </processPrePosts>
            <layoutData x="389" y="58">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.5f760ca7-ed88-41bb-b566-c995d5ca9ca7</subProcessId>
                <attachedProcessRef>/1.8a1c5f67-55b2-4785-97cb-9ec3e4441186</attachedProcessRef>
                <guid>2284c800-f0e6-4775-9091-966f2cc905e2</guid>
                <versionId>47846938-3b2b-4d4e-9298-05ce3c0f834c</versionId>
                <parameterMapping name="folderName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e3a6c34e-a57a-4d21-ae56-26726e247765</parameterMappingId>
                    <processParameterId>2055.98c175d8-7e63-4e2d-ac3f-************</processParameterId>
                    <parameterMappingParentId>3012.5f760ca7-ed88-41bb-b566-c995d5ca9ca7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.separatedPath[tw.local.iterator]</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>34275db2-2454-461f-a617-f073230b3e96</guid>
                    <versionId>698363af-c359-4627-b482-d9c4aa7f5e3a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parentFolderPath">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.853504b7-db83-40a9-850b-5b8d6dd76f5a</parameterMappingId>
                    <processParameterId>2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e</processParameterId>
                    <parameterMappingParentId>3012.5f760ca7-ed88-41bb-b566-c995d5ca9ca7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentFolderPath</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>62ad848c-c306-4847-96df-5d7240f8521c</guid>
                    <versionId>726473e7-27c9-43f3-a92a-ccdda3661fca</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parentFolderID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b057964f-1476-4a96-8476-91175f832dee</parameterMappingId>
                    <processParameterId>2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63</processParameterId>
                    <parameterMappingParentId>3012.5f760ca7-ed88-41bb-b566-c995d5ca9ca7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.FolderID</value>
                    <classRef>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>581784bc-bb9f-410b-875d-4e504b3153a9</guid>
                    <versionId>7f6af8d0-7768-4e9b-abc3-0f01ade064d1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="folderID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6a861c9c-1414-45ae-aa73-0bdd57f65175</parameterMappingId>
                    <processParameterId>2055.21385f08-1be8-41a4-b758-c8dc9b8bb509</processParameterId>
                    <parameterMappingParentId>3012.5f760ca7-ed88-41bb-b566-c995d5ca9ca7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.FolderID</value>
                    <classRef>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>2a16eed6-08d1-46c3-86da-98497f512d92</guid>
                    <versionId>ca99ba92-2206-40f4-9e52-8abb0b7b5a02</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8a4503de-46f5-4879-8d54-834321f21014</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>creating folder  path</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9548ab09-0137-4bf2-bf01-a10a76408390</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1cb5</guid>
            <versionId>17e72e9e-ff08-4546-a1e7-67208912058a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="254" y="58">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9548ab09-0137-4bf2-bf01-a10a76408390</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.iterator == 0)&#xD;
{&#xD;
	tw.local.parentFolderPath = tw.env.FILENET_ROOT_PATH;&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.parentFolderPath += "/"+tw.local.separatedPath[tw.local.iterator-1];&#xD;
}&#xD;
 </script>
                <isRule>false</isRule>
                <guid>f5f77959-90b9-4143-be2e-ba5d8bba1861</guid>
                <versionId>315df855-519d-44ae-8eb5-96e62aa52b15</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1a814772-52df-4dc1-8881-2f212b065b42</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.ac36ddf6-8599-45ad-8799-ed35db9a4a40</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-2aa4</guid>
            <versionId>260a481d-cdb3-42ac-b54e-c94c0c4867e7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="696" y="81">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.ac36ddf6-8599-45ad-8799-ed35db9a4a40</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>356bd84f-dc27-4b65-a4b3-07538963453d</guid>
                <versionId>89d34563-195d-4820-af9f-f7a1ebd450dd</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.df737eb7-eae7-435a-82d7-fd4dcd366a60</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.b97b9b8a-a797-4e98-9d9f-4d823c84a232</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f7f6553919972802:-74de7c2c:193f7b0288a:-5fe1</guid>
            <versionId>650d7e6c-cb3c-44ca-978e-a36f5534cb98</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="730" y="327">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.b97b9b8a-a797-4e98-9d9f-4d823c84a232</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>64a9a8e1-d166-4cb4-8bf1-cb1b6f778c5e</guid>
                <versionId>ad6b1440-32a7-490c-b3be-27907555d489</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.72c4418f-0a21-4d31-9a91-862bf8f7c1fc</parameterMappingId>
                    <processParameterId>2055.ddfecda2-ed78-4535-acd1-fac0696c896f</processParameterId>
                    <parameterMappingParentId>3007.b97b9b8a-a797-4e98-9d9f-4d823c84a232</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>6161cb38-6b28-495c-98d4-f4be4ef7c8dd</guid>
                    <versionId>d01bf240-2e96-4e07-97f8-2be7d6ec8ba9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.fcf3ebff-231f-441e-824e-9f622b3d95a6</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>Linked Service Flow</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.d8730c00-d83f-4152-8a1e-cb3cfa2cd6a0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f7f6553919972802:-74de7c2c:193f7b0288a:-5fe3</guid>
            <versionId>67ff65e0-31b1-4d2b-9741-f724f990f98b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="344" y="324">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.d8730c00-d83f-4152-8a1e-cb3cfa2cd6a0</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>b50ceca7-2697-4d51-bde5-220c4f5dfdb8</guid>
                <versionId>1dd5f320-c115-43d0-81a6-1959e8eccdd5</versionId>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8a0743b8-4705-405e-8a38-35c87b4a794a</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.d8730c00-d83f-4152-8a1e-cb3cfa2cd6a0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Child"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1f3e4612-efc3-4923-989d-5a9a5dfd0785</guid>
                    <versionId>00774ad7-9720-40f7-b206-20cf14253c53</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3d00ca4f-7f56-4a13-a197-05a6c0b78855</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.d8730c00-d83f-4152-8a1e-cb3cfa2cd6a0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>7d173468-741c-4524-8ad4-246b546441d6</guid>
                    <versionId>012dee8f-2941-4791-bfed-8ecbd3062674</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8e3b7b69-5f72-4327-a625-91f9f336dd17</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.d8730c00-d83f-4152-8a1e-cb3cfa2cd6a0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>59a28929-9678-43e5-96f3-58d395799d0e</guid>
                    <versionId>2c5239d7-02e3-463d-9a13-229d838d8f64</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2c02e66c-e85c-486a-a00c-3dec3dc38b0a</parameterMappingId>
                    <processParameterId>2055.d1830a67-1c3d-403f-85ac-213a58225a6c</processParameterId>
                    <parameterMappingParentId>3012.d8730c00-d83f-4152-8a1e-cb3cfa2cd6a0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d26450f6-9449-4126-a0e3-d82c4c65c37c</guid>
                    <versionId>3b9d7ecc-80e9-41dc-8426-119fb3866eee</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a372326b-5775-406b-87a5-e3c75dc2ca3a</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>Exclusive Gateway</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.373bc16b-ca3d-425a-9bdf-8381a6380ea3</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1b6d</guid>
            <versionId>9ee0247c-fee4-4483-9899-dba96f1a8634</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="539" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.373bc16b-ca3d-425a-9bdf-8381a6380ea3</switchId>
                <guid>2be59390-5cf7-4737-aebf-cacf7012719c</guid>
                <versionId>*************-48e4-b685-575e217a03b4</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.024168b5-aa07-496c-b416-ec111962a8b2</switchConditionId>
                    <switchId>3013.373bc16b-ca3d-425a-9bdf-8381a6380ea3</switchId>
                    <seq>1</seq>
                    <endStateId>guid:faf129f3cc554db6:5a40ed45:195332c2764:-3b68</endStateId>
                    <condition>tw.local.iterator	  &lt;	  tw.local.separatedPath.listLength</condition>
                    <guid>2f1f5bdc-1d6a-4a26-b1a0-a9cf2b1c4502</guid>
                    <versionId>ce1fc295-40f3-4fea-b468-0cb82c6a65b9</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.44d34d2c-0346-4e92-837b-4f5ad945b031</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.05e2ebe6-7191-4916-96ae-29add8a38130</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f7f6553919972802:-74de7c2c:193f7b0288a:-600b</guid>
            <versionId>d0f018ff-d047-4f66-b99f-74168b405581</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="331" y="199">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.05e2ebe6-7191-4916-96ae-29add8a38130</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>//throw "Child Error"</script>
                <isRule>false</isRule>
                <guid>0502a9a5-fbd2-4893-baf4-d84a77b7d6d4</guid>
                <versionId>fd979168-f34b-4892-b17f-9618ff8e0314</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.982deb5c-065e-4dbf-8b7e-5bca263ceeb6</processItemId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <name>Splitting Fullpath</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8305d850-cab5-4005-aa54-1463fc825ffd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fef170a08f25d496:5466e087:189df5f8551:-1d12</guid>
            <versionId>d70b5849-f3ea-42fb-8b88-adffd967a959</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="126" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8305d850-cab5-4005-aa54-1463fc825ffd</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.iterator =0;&#xD;
tw.local.separatedPath = new tw.object.listOf.String();&#xD;
tw.local.separatedPath =tw.local.Path.split("/");</script>
                <isRule>false</isRule>
                <guid>dd30654e-96af-4dae-b68f-7d3cfc703a41</guid>
                <versionId>bbd533ea-fa74-423f-bc1b-f80b357b8a1f</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.44d34d2c-0346-4e92-837b-4f5ad945b031</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                <ns16:process name="Create" id="1.5d77055c-98a8-4191-9b74-c7120a5823be" ns3:executionMode="microflow">
                    <ns16:documentation textFormat="text/plain" />
                    <ns16:extensionElements>
                        <ns3:isSecured>true</ns3:isSecured>
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:dataInput name="Path" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.094f9338-8841-4f14-80be-d92350c25b90">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="false">"12345/DC Outward/122/ODC/Issuance";</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataOutput name="FolderID" itemSubjectRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01" isCollection="false" id="2055.41d4d7e4-dd09-4686-87cc-5816b6aaa1f8" />
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.9ff5ab2a-ddd9-4338-81b2-f5b9b9237fa8" />
                        <ns16:inputSet>
                            <ns16:dataInputRefs>2055.094f9338-8841-4f14-80be-d92350c25b90</ns16:dataInputRefs>
                        </ns16:inputSet>
                        <ns16:outputSet>
                            <ns16:dataOutputRefs>2055.41d4d7e4-dd09-4686-87cc-5816b6aaa1f8</ns16:dataOutputRefs>
                            <ns16:dataOutputRefs>2055.9ff5ab2a-ddd9-4338-81b2-f5b9b9237fa8</ns16:dataOutputRefs>
                        </ns16:outputSet>
                    </ns16:ioSpecification>
                    <ns16:laneSet id="463c74ad-f652-416f-86f7-ec6d3d2c3a9b">
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="2a672723-6f73-44f0-8db7-47b10d7107ff" ns4:isSystemLane="true">
                            <ns16:extensionElements>
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                            </ns16:extensionElements>
                            <ns16:flowNodeRef>b2c15844-2e24-4523-8b45-5b2fa8a5951b</ns16:flowNodeRef>
                            <ns16:flowNodeRef>1a814772-52df-4dc1-8881-2f212b065b42</ns16:flowNodeRef>
                            <ns16:flowNodeRef>982deb5c-065e-4dbf-8b7e-5bca263ceeb6</ns16:flowNodeRef>
                            <ns16:flowNodeRef>8a4503de-46f5-4879-8d54-834321f21014</ns16:flowNodeRef>
                            <ns16:flowNodeRef>1e825648-a142-4232-8c47-3327fd38a4e0</ns16:flowNodeRef>
                            <ns16:flowNodeRef>a372326b-5775-406b-87a5-e3c75dc2ca3a</ns16:flowNodeRef>
                            <ns16:flowNodeRef>44d34d2c-0346-4e92-837b-4f5ad945b031</ns16:flowNodeRef>
                            <ns16:flowNodeRef>fcf3ebff-231f-441e-824e-9f622b3d95a6</ns16:flowNodeRef>
                            <ns16:flowNodeRef>df737eb7-eae7-435a-82d7-fd4dcd366a60</ns16:flowNodeRef>
                        </ns16:lane>
                    </ns16:laneSet>
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b2c15844-2e24-4523-8b45-5b2fa8a5951b">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                        </ns16:extensionElements>
                        <ns16:outgoing>2027.52cf5142-3d70-4089-81c5-ce5c2a7c10ad</ns16:outgoing>
                    </ns16:startEvent>
                    <ns16:endEvent name="End" id="1a814772-52df-4dc1-8881-2f212b065b42">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="696" y="81" width="24" height="24" color="#F8F8F8" />
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-2aa4</ns3:endStateId>
                        </ns16:extensionElements>
                        <ns16:incoming>9a49916c-f5c5-409b-8a34-03cb610f5a3d</ns16:incoming>
                        <ns16:incoming>5abb97db-c04a-46e6-87d1-eaebc2cc486b</ns16:incoming>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="b2c15844-2e24-4523-8b45-5b2fa8a5951b" targetRef="44d34d2c-0346-4e92-837b-4f5ad945b031" name="To End" id="2027.52cf5142-3d70-4089-81c5-ce5c2a7c10ad">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="separatedPath" id="2056.e8193ea9-74a7-4ced-8073-eaf9652dd9e3">
                        <ns16:extensionElements>
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "";
autoObject</ns3:defaultValue>
                        </ns16:extensionElements>
                    </ns16:dataObject>
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentFolderPath" id="2056.3b96a48d-63c6-4bb9-808d-0111417433e4" />
                    <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="iterator" id="2056.e3e87f58-**************-8827165d49cc" />
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Splitting Fullpath" id="982deb5c-065e-4dbf-8b7e-5bca263ceeb6">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="126" y="57" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:outgoing>bf053733-2b38-4333-886d-8b18a4be952f</ns16:outgoing>
                        <ns16:script>tw.local.iterator =0;&#xD;
tw.local.separatedPath = new tw.object.listOf.String();&#xD;
tw.local.separatedPath =tw.local.Path.split("/");</ns16:script>
                    </ns16:scriptTask>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="creating folder  path" id="8a4503de-46f5-4879-8d54-834321f21014">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="254" y="58" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:incoming>bf053733-2b38-4333-886d-8b18a4be952f</ns16:incoming>
                        <ns16:incoming>a24fd892-0c77-439e-819c-6cdf6840e54b</ns16:incoming>
                        <ns16:outgoing>78c26f0f-3dc1-445a-83b8-9e8dad43e526</ns16:outgoing>
                        <ns16:script>if(tw.local.iterator == 0)&#xD;
{&#xD;
	tw.local.parentFolderPath = tw.env.FILENET_ROOT_PATH;&#xD;
}&#xD;
else&#xD;
{&#xD;
	tw.local.parentFolderPath += "/"+tw.local.separatedPath[tw.local.iterator-1];&#xD;
}&#xD;
 </ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="982deb5c-065e-4dbf-8b7e-5bca263ceeb6" targetRef="8a4503de-46f5-4879-8d54-834321f21014" name="To creating folder  path" id="bf053733-2b38-4333-886d-8b18a4be952f">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:callActivity calledElement="1.8a1c5f67-55b2-4785-97cb-9ec3e4441186" isForCompensation="false" startQuantity="1" completionQuantity="1" name="create folder on filenet" id="1e825648-a142-4232-8c47-3327fd38a4e0">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="389" y="58" width="95" height="70" />
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            <ns3:postAssignmentScript>tw.local.iterator += 1;</ns3:postAssignmentScript>
                        </ns16:extensionElements>
                        <ns16:incoming>78c26f0f-3dc1-445a-83b8-9e8dad43e526</ns16:incoming>
                        <ns16:outgoing>f73798b3-98b8-4385-8cf9-3806557b7f42</ns16:outgoing>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.a39fad2f-120b-4d57-af8d-5904bbab8e3e</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentFolderPath</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.98c175d8-7e63-4e2d-ac3f-************</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.separatedPath[tw.local.iterator]</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.e079f8c0-dfc2-45f2-a4e0-631d00442c63</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.FolderID</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataOutputAssociation>
                            <ns16:sourceRef>2055.21385f08-1be8-41a4-b758-c8dc9b8bb509</ns16:sourceRef>
                            <ns16:assignment>
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01">tw.local.FolderID</ns16:to>
                            </ns16:assignment>
                        </ns16:dataOutputAssociation>
                    </ns16:callActivity>
                    <ns16:sequenceFlow sourceRef="8a4503de-46f5-4879-8d54-834321f21014" targetRef="1e825648-a142-4232-8c47-3327fd38a4e0" name="To create folder on filenet" id="78c26f0f-3dc1-445a-83b8-9e8dad43e526">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:sequenceFlow sourceRef="1e825648-a142-4232-8c47-3327fd38a4e0" targetRef="a372326b-5775-406b-87a5-e3c75dc2ca3a" name="To End" id="f73798b3-98b8-4385-8cf9-3806557b7f42">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-15c3</ns3:endStateId>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:exclusiveGateway default="a24fd892-0c77-439e-819c-6cdf6840e54b" name="Exclusive Gateway" id="a372326b-5775-406b-87a5-e3c75dc2ca3a">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="539" y="77" width="32" height="32" />
                        </ns16:extensionElements>
                        <ns16:incoming>f73798b3-98b8-4385-8cf9-3806557b7f42</ns16:incoming>
                        <ns16:outgoing>9a49916c-f5c5-409b-8a34-03cb610f5a3d</ns16:outgoing>
                        <ns16:outgoing>a24fd892-0c77-439e-819c-6cdf6840e54b</ns16:outgoing>
                    </ns16:exclusiveGateway>
                    <ns16:sequenceFlow sourceRef="a372326b-5775-406b-87a5-e3c75dc2ca3a" targetRef="1a814772-52df-4dc1-8881-2f212b065b42" name="To End" id="9a49916c-f5c5-409b-8a34-03cb610f5a3d">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>true</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.iterator	  &lt;	  tw.local.separatedPath.listLength</ns16:conditionExpression>
                    </ns16:sequenceFlow>
                    <ns16:sequenceFlow sourceRef="a372326b-5775-406b-87a5-e3c75dc2ca3a" targetRef="8a4503de-46f5-4879-8d54-834321f21014" name="To creating folder on file net " id="a24fd892-0c77-439e-819c-6cdf6840e54b">
                        <ns16:extensionElements>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                <ns13:showLabel>true</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="44d34d2c-0346-4e92-837b-4f5ad945b031">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="331" y="199" width="95" height="70" />
                        </ns16:extensionElements>
                        <ns16:incoming>2027.52cf5142-3d70-4089-81c5-ce5c2a7c10ad</ns16:incoming>
                        <ns16:outgoing>5abb97db-c04a-46e6-87d1-eaebc2cc486b</ns16:outgoing>
                        <ns16:script>//throw "Child Error"</ns16:script>
                    </ns16:scriptTask>
                    <ns16:sequenceFlow sourceRef="44d34d2c-0346-4e92-837b-4f5ad945b031" targetRef="1a814772-52df-4dc1-8881-2f212b065b42" name="To End" id="5abb97db-c04a-46e6-87d1-eaebc2cc486b">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Linked Service Flow" id="fcf3ebff-231f-441e-824e-9f622b3d95a6">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="344" y="324" width="95" height="70" color="#FF7782" />
                            <ns4:activityType>CalledProcess</ns4:activityType>
                        </ns16:extensionElements>
                        <ns16:outgoing>bc89a4d2-c2bf-42c8-880f-5e4043b52d02</ns16:outgoing>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Child"</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataInputAssociation>
                            <ns16:targetRef>2055.d1830a67-1c3d-403f-85ac-213a58225a6c</ns16:targetRef>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:dataOutputAssociation>
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            <ns16:assignment>
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                            </ns16:assignment>
                        </ns16:dataOutputAssociation>
                    </ns16:callActivity>
                    <ns16:endEvent name="End Event" id="df737eb7-eae7-435a-82d7-fd4dcd366a60">
                        <ns16:extensionElements>
                            <ns13:nodeVisualInfo x="730" y="327" width="24" height="24" />
                        </ns16:extensionElements>
                        <ns16:incoming>bc89a4d2-c2bf-42c8-880f-5e4043b52d02</ns16:incoming>
                        <ns16:dataInputAssociation>
                            <ns16:assignment>
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:from>
                            </ns16:assignment>
                        </ns16:dataInputAssociation>
                        <ns16:errorEventDefinition id="cd4bfd07-0df6-404b-85c3-2f031b574f3c" eventImplId="d820fc71-1cbd-4bb8-8b39-2b344d3ce8cf">
                            <ns16:extensionElements>
                                <ns4:errorEventSettings>
                                    <ns4:catchAll>true</ns4:catchAll>
                                    <ns4:errorCode />
                                </ns4:errorEventSettings>
                            </ns16:extensionElements>
                        </ns16:errorEventDefinition>
                    </ns16:endEvent>
                    <ns16:sequenceFlow sourceRef="fcf3ebff-231f-441e-824e-9f622b3d95a6" targetRef="df737eb7-eae7-435a-82d7-fd4dcd366a60" name="To End Event" id="bc89a4d2-c2bf-42c8-880f-5e4043b52d02">
                        <ns16:extensionElements>
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            <ns13:linkVisualInfo>
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                <ns13:showLabel>false</ns13:showLabel>
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                            </ns13:linkVisualInfo>
                        </ns16:extensionElements>
                    </ns16:sequenceFlow>
                </ns16:process>
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bc89a4d2-c2bf-42c8-880f-5e4043b52d02</processLinkId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.fcf3ebff-231f-441e-824e-9f622b3d95a6</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.df737eb7-eae7-435a-82d7-fd4dcd366a60</toProcessItemId>
            <guid>d7b44db9-bf1b-4d9c-aac0-9252f28bc682</guid>
            <versionId>0a39dff8-3a53-4c68-b0d8-038db52a2865</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.fcf3ebff-231f-441e-824e-9f622b3d95a6</fromProcessItemId>
            <toProcessItemId>2025.df737eb7-eae7-435a-82d7-fd4dcd366a60</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9a49916c-f5c5-409b-8a34-03cb610f5a3d</processLinkId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a372326b-5775-406b-87a5-e3c75dc2ca3a</fromProcessItemId>
            <endStateId>guid:faf129f3cc554db6:5a40ed45:195332c2764:-3b68</endStateId>
            <toProcessItemId>2025.1a814772-52df-4dc1-8881-2f212b065b42</toProcessItemId>
            <guid>69fb9ef6-60e7-4b88-91a6-8152f0f693a7</guid>
            <versionId>23ed26ba-9788-496e-a92a-75ddb37524a1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a372326b-5775-406b-87a5-e3c75dc2ca3a</fromProcessItemId>
            <toProcessItemId>2025.1a814772-52df-4dc1-8881-2f212b065b42</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.f73798b3-98b8-4385-8cf9-3806557b7f42</processLinkId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1e825648-a142-4232-8c47-3327fd38a4e0</fromProcessItemId>
            <endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:-15c3</endStateId>
            <toProcessItemId>2025.a372326b-5775-406b-87a5-e3c75dc2ca3a</toProcessItemId>
            <guid>854305e5-0f5a-4bf6-a238-ab87b541e208</guid>
            <versionId>34804405-cdee-4c09-9a39-db5ade1799a3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1e825648-a142-4232-8c47-3327fd38a4e0</fromProcessItemId>
            <toProcessItemId>2025.a372326b-5775-406b-87a5-e3c75dc2ca3a</toProcessItemId>
        </link>
        <link name="To creating folder  path">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bf053733-2b38-4333-886d-8b18a4be952f</processLinkId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.982deb5c-065e-4dbf-8b7e-5bca263ceeb6</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.8a4503de-46f5-4879-8d54-834321f21014</toProcessItemId>
            <guid>*************-425f-adb9-42874302e7c3</guid>
            <versionId>35a73d40-f550-4b02-ad44-127a77966e0e</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.982deb5c-065e-4dbf-8b7e-5bca263ceeb6</fromProcessItemId>
            <toProcessItemId>2025.8a4503de-46f5-4879-8d54-834321f21014</toProcessItemId>
        </link>
        <link name="To create folder on filenet">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.78c26f0f-3dc1-445a-83b8-9e8dad43e526</processLinkId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8a4503de-46f5-4879-8d54-834321f21014</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1e825648-a142-4232-8c47-3327fd38a4e0</toProcessItemId>
            <guid>5389c978-f005-4265-8e8f-3dd7a85b7318</guid>
            <versionId>db41ef71-040a-417d-b180-b2cb804f35e2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8a4503de-46f5-4879-8d54-834321f21014</fromProcessItemId>
            <toProcessItemId>2025.1e825648-a142-4232-8c47-3327fd38a4e0</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5abb97db-c04a-46e6-87d1-eaebc2cc486b</processLinkId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.44d34d2c-0346-4e92-837b-4f5ad945b031</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1a814772-52df-4dc1-8881-2f212b065b42</toProcessItemId>
            <guid>812f39b6-89e3-4581-b2ce-407ffce8f8e8</guid>
            <versionId>f66a6dee-e66c-428e-a718-e63d7130b9c0</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.44d34d2c-0346-4e92-837b-4f5ad945b031</fromProcessItemId>
            <toProcessItemId>2025.1a814772-52df-4dc1-8881-2f212b065b42</toProcessItemId>
        </link>
        <link name="To creating folder on file net ">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a24fd892-0c77-439e-819c-6cdf6840e54b</processLinkId>
            <processId>1.5d77055c-98a8-4191-9b74-c7120a5823be</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a372326b-5775-406b-87a5-e3c75dc2ca3a</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.8a4503de-46f5-4879-8d54-834321f21014</toProcessItemId>
            <guid>2015d032-7202-463e-8e8c-69c66b50778d</guid>
            <versionId>fee15d78-7995-4143-946d-8199bb26adfd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.a372326b-5775-406b-87a5-e3c75dc2ca3a</fromProcessItemId>
            <toProcessItemId>2025.8a4503de-46f5-4879-8d54-834321f21014</toProcessItemId>
        </link>
    </process>
</teamworks>

