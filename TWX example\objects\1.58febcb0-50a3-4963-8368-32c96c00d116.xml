<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.58febcb0-50a3-4963-8368-32c96c00d116" name="Get Request and Customer Data">
        <lastModified>1700640132785</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:1c16</guid>
        <versionId>74c4c993-b191-4db5-b2ce-3a3a004e5f54</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:4e45c66327642938:4e8a44d0:18bf1ac4de4:5465" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.72a44093-5c50-4545-8193-459e38ad670a"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":6,"y":203,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"19265436-ca75-4c91-81d7-23a5d8630227"},{"incoming":["539be3e2-0358-40ca-83fd-93260927bb93","629fef8d-bc77-4e55-80cc-d6dc14851830","aa9a742a-06ca-485e-8abb-9b0dcae801cd","8a7190f7-1d05-4b55-8cb1-1f773438780b"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":1543,"y":165,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:266f6f4955d8489f:7b0b9b81:18b66504d45:1c18"],"preAssignmentScript":[],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"b7f12cdc-6a9b-420a-876c-c8d48c020ca3"},{"targetRef":"29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL","declaredType":"sequenceFlow","id":"2027.72a44093-5c50-4545-8193-459e38ad670a","sourceRef":"19265436-ca75-4c91-81d7-23a5d8630227"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.ba6075c5-05a5-435b-8e40-4343f8f653c6"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.ec98c981-3818-434e-88f0-d24275a1f30d"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"subResult","isCollection":true,"declaredType":"dataObject","id":"2056.a444daad-a00d-47b4-8d1b-29d2c3513b1c"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMSG","isCollection":false,"declaredType":"dataObject","id":"2056.9b504e0d-6fa4-4854-8bee-651e6f489ef6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.7eb13764-afaf-4054-8858-66122a18ecbb"},{"startQuantity":1,"outgoing":["e5e129f2-cf44-481b-8f3e-428bd1251ce2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":29,"y":418,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.requestType = tw.local.parentODCRequest.requestType.value;\r\ntw.local.requestNature = \"\"+ tw.epv.RequestNature.UpdateRequest;\r\ntw.local.appInfo = tw.local.parentODCRequest.appInfo;\r\ntw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;\r\n"]},"name":"SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"b6425fe1-912b-453f-8b9c-a039277a451b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar j = 0;\r\nvar i = 0;\r\nfunction paramInit (value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti++;\r\n}\r\n\/\/------------------------------------- Get ODC -------------------------------------------- \r\nvar final = \"\" +tw.epv.RequestState.Final;\r\nvar closed = \"\" + tw.epv.RequestState.Closed;\r\nvar reversed = \"\" + tw.epv.RequestState.Reversed;\r\nvar collected = \"\"+ tw.epv.RequestState.Collection;\r\n\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\r\nif(tw.local.parentODCRequest.requestType.value == \"amend\" || tw.local.parentODCRequest.requestType.value == \"closure\" || tw.local.parentODCRequest.requestType.value == \"collection\"){\r\n tw.local.sqlStatements[j].sql=\"SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '\"+final+\"' OR REQUESTSTATE = '\"+collected+\"' OR REQUESTSTATE = '\"+closed+\"' OR REQUESTSTATE = '\"+reversed+\"'  AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) != UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled') )  AND OUTSTANDINGAMOUNT &gt; 0; \";\r\n\r\n}\r\nelse if (tw.local.parentODCRequest.requestType.value == \"recreate\"){\r\n tw.local.sqlStatements[j].sql=\"SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '\"+final+\"' AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND   PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) !=  UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled') ORDER BY ID ASC\";\r\n\r\n}\r\n\r\nelse{\r\n tw.local.sqlStatements[j].sql=\"SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '\"+final+\"' AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) != UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled' ))\";\r\n\r\n}\r\n\r\n tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n paramInit(tw.local.requestNumber);"]}},{"targetRef":"9886a463-0e69-404d-8f95-f6f22c77f59b","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e5e129f2-cf44-481b-8f3e-428bd1251ce2","sourceRef":"b6425fe1-912b-453f-8b9c-a039277a451b"},{"startQuantity":1,"outgoing":["6723fc62-9f8d-4173-8576-719b692e3d87"],"incoming":["e5e129f2-cf44-481b-8f3e-428bd1251ce2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":160,"y":418,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"9886a463-0e69-404d-8f95-f6f22c77f59b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"parallelMultiple":false,"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2e84b0e4-da76-4fdb-8792-4f4e6326233d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"8e93d29a-5a56-4bf1-8955-383b5b267590","otherAttributes":{"eventImplId":"6bfbaff3-63b0-46a6-8200-0a5461329641"}}],"attachedToRef":"9886a463-0e69-404d-8f95-f6f22c77f59b","extensionElements":{"nodeVisualInfo":[{"width":24,"x":196,"y":476,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"d2b49723-5052-4fc7-8c36-517d02057cdc","outputSet":{}},{"startQuantity":1,"incoming":["6723fc62-9f8d-4173-8576-719b692e3d87"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":316,"y":418,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Check","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"980ad00d-5fbb-423c-85ee-08e4caff0a41","scriptFormat":"text\/x-javascript","script":{"content":["if (tw.local.results[0].rows.listLength == 0) {\r\n      if(tw.local.parentODCRequest.requestType.value == \"amend\" || tw.local.parentODCRequest.requestType.value == \"closure\" || tw.local.parentODCRequest.requestType.value == \"collection\"){\r\n\t tw.local.message = \"Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 \";\r\n\t}\r\n\telse\r\n\t tw.local.message = \"Request number does not exist or is in another running request\";\r\n\ttw.local.isFound = false;\r\n}\r\n\r\nelse{\r\n\r\n      tw.local.isFound = true;\r\n      \r\n      if(tw.local.parentODCRequest.requestType.value == \"recreate\"){\r\n      \r\n      \/\/There must be no collection done on ODC\r\n\r\n         for(var i =0 ; i &lt;tw.local.results[0].rows.listLength; i++){\r\n\r\n             if(tw.local.results[0].rows[i].data[3] == \"ODC Collection\"){\r\n                  tw.local.message = \"There must be no collection done on ODC\";\r\n                  tw.local.isFound = false;\r\n                  break;\r\n             }\r\n          }\r\n          if(tw.local.isFound == true ){\r\n          \/\/get the latsest instance\r\n           tw.local.results[0].rows[0] = tw.local.results[0].rows[tw.local.results[0].rows.listLength-1];\r\n\r\n          }\r\n      }\r\n\t\r\n}"]}},{"targetRef":"980ad00d-5fbb-423c-85ee-08e4caff0a41","extensionElements":{"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Recreate?","declaredType":"sequenceFlow","id":"6723fc62-9f8d-4173-8576-719b692e3d87","sourceRef":"9886a463-0e69-404d-8f95-f6f22c77f59b"},{"outgoing":["bd95da45-6fe2-4edb-876f-aa8a35ac419a","539be3e2-0358-40ca-83fd-93260927bb93"],"incoming":["67bb1d02-584a-4d03-81c5-4f69fcd20737"],"default":"bd95da45-6fe2-4edb-876f-aa8a35ac419a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":637,"y":201,"declaredType":"TNodeVisualInfo","height":32}]},"name":"found?","declaredType":"exclusiveGateway","id":"b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d"},{"startQuantity":1,"outgoing":["3654dcb0-15a6-4c46-8808-e0d75db2b600"],"incoming":["bd95da45-6fe2-4edb-876f-aa8a35ac419a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":764,"y":182,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Init SQL for sub","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8ae854db-345a-4c0c-80db-df3c8592f922","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar j = 0;\r\nvar i = 0;\r\nfunction paramInit (value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti++;\r\n}\r\n\r\n\/\/------------------------------------- Get invoice --------------------------------------------\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].sql=\"SELECT  INVOICENO, INVOICEDATE FROM ARCHUSR.ODC_INVOICE WHERE REQUESRID = ?;\";\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\nif(!!tw.local.results[0].rows[0])\r\n   paramInit(tw.local.results[0].rows[0].data[0]);\r\nelse if(!!tw.local.results[1].rows[0])   \r\n   paramInit(tw.local.results[1].rows[0].data[0]);\r\n\/\/------------------------------------- Get bills --------------------------------------------\r\nj++;\r\ni=0;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].sql=\"SELECT BILLOFLADINGREF, BILLOFLADINGDATE FROM ARCHUSR.ODC_BILLS WHERE REQUESRID = ?;\";\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\nif(!!tw.local.results[0].rows[0])\r\n   paramInit(tw.local.results[0].rows[0].data[0]);\r\nelse if(!!tw.local.results[1].rows[0])   \r\n   paramInit(tw.local.results[1].rows[0].data[0]);\r\n\r\n\r\n\/\/------------------------------------- Get multi tenor --------------------------------------------\r\nj++;\r\ni=0;\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\ntw.local.sqlStatements[j].sql=\"SELECT INSTALLMENTDATE, INSTALLMENTAMOUNT FROM ARCHUSR.ODC_MULTITENORDATES WHERE REQUESRID = ?;\";\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\nif(!!tw.local.results[0].rows[0])\r\n   paramInit(tw.local.results[0].rows[0].data[0]);\r\nelse if(!!tw.local.results[1].rows[0])   \r\n   paramInit(tw.local.results[1].rows[0].data[0]);\r\n"]}},{"targetRef":"8ae854db-345a-4c0c-80db-df3c8592f922","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To sub?","declaredType":"sequenceFlow","id":"bd95da45-6fe2-4edb-876f-aa8a35ac419a","sourceRef":"b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d"},{"targetRef":"b7f12cdc-6a9b-420a-876c-c8d48c020ca3","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isFound\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"539be3e2-0358-40ca-83fd-93260927bb93","sourceRef":"b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d"},{"startQuantity":1,"outgoing":["da8842d8-099d-469a-8297-496306df06db"],"incoming":["3654dcb0-15a6-4c46-8808-e0d75db2b600"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":876,"y":182,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"sub SQL Execute Multiple Statements (SQLResult)","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"*************-4b1f-8b9b-c76176c75d1e","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.subResult"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"*************-4b1f-8b9b-c76176c75d1e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To sub SQL Execute Multiple Statements (SQLResult)","declaredType":"sequenceFlow","id":"3654dcb0-15a6-4c46-8808-e0d75db2b600","sourceRef":"8ae854db-345a-4c0c-80db-df3c8592f922"},{"startQuantity":1,"outgoing":["c78bbd5b-9644-44ef-8764-19cd19d19e71"],"incoming":["da8842d8-099d-469a-8297-496306df06db"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":1005,"y":182,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Mapping","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"f4c6489d-c98b-4d0c-8c14-f06f37b4dea3","scriptFormat":"text\/x-javascript","script":{"content":["var i=0-1;\r\nvar j =0-1;\r\nfunction getnext () {\r\n\ti++;\r\n\tvar results =null;\r\n\tif(!!tw.local.results[0]){\r\n\t if(!!tw.local.results[0].rows[0])\r\n\t    results = tw.local.results[0].rows[0].data[i];\r\n\t }   \r\n\telse\r\n\t    results = null;\r\n\treturn results;\r\n}\r\nfunction getCreateData () {\r\n      j++;\r\n\tvar results = \"\";\r\n\tif(!!tw.local.results[1].rows[0])\r\n\t    results = tw.local.results[1].rows[0].data[j];\r\n\telse\r\n\t    results = null\r\n\treturn results;\r\n}\r\nvar amend = \"\" + tw.epv.RequestType.Amendment;\r\nvar recreate = \"\" + tw.epv.RequestType.Recreate;\r\nvar collection = \"\" + tw.epv.RequestType.Collection;\r\nvar closure = \"\" + tw.epv.RequestType.Closure;\r\nvar reversal = \"\" + tw.epv.RequestType.Reversal;\r\n\r\nif(tw.local.parentODCRequest.requestType.value == amend || tw.local.parentODCRequest.requestType.value == recreate  ){\r\ngetnext ();\r\n\r\ntw.local.parentODCRequest = new tw.object.ODCRequest();\r\ntw.local.parentODCRequest.requestNo = getnext ();\r\ntw.local.parentODCRequest.requestNature = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.requestNature.name = tw.local.requestNature;\r\ntw.local.parentODCRequest.BasicDetails = new tw.object.BasicDetails();\r\ntw.local.parentODCRequest.BasicDetails.requestNature = getnext ();\r\ngetnext ();\r\ntw.local.parentODCRequest.requestType  = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.requestType.name =tw.local.requestType;\r\ntw.local.parentODCRequest.requestType.value =tw.local.requestType;\r\ntw.local.parentODCRequest.requestDate = getnext ();\r\ntw.local.parentODCRequest.BasicDetails.requestState = getnext ();\r\n\/\/tw.local.parentODCRequest.appInfo = new tw.object.AppInfo();\r\n\/\/tw.local.parentODCRequest.appInfo.status = getnext ();\r\ngetnext ();\r\ntw.local.parentODCRequest.parentRequestNo = getnext ();\r\ntw.local.parentODCRequest.BasicDetails.flexCubeContractNo = getnext ();\r\ntw.local.parentODCRequest.BasicDetails.contractStage = getnext ();\r\ntw.local.parentODCRequest.BasicDetails.exportPurpose = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.BasicDetails.exportPurpose.name =getnext ();\r\ntw.local.parentODCRequest.BasicDetails.paymentTerms = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.BasicDetails.paymentTerms.name = getnext ();\r\ntw.local.parentODCRequest.BasicDetails.productCategory  = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.BasicDetails.productCategory.name = getnext ();\r\ntw.local.parentODCRequest.BasicDetails.commodityDescription = getnext ();\r\n\r\ntw.local.parentODCRequest.isLiquidated = getnext ();\r\ni--;\r\ntw.local.parentODCRequest.CustomerInfo = new tw.object.CustomerInfo()\r\ntw.local.parentODCRequest.CustomerInfo.cif= getnext ();\r\ntw.local.parentODCRequest.CustomerInfo.customerName= getnext ();\r\ntw.local.parentODCRequest.ContractCreation = new tw.object.ContractCreation();\r\ntw.local.parentODCRequest.ContractCreation.baseDate= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.valueDate= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.tenorDays= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.transitDays= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.maturityDate= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.userReference= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.productCode = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.ContractCreation.productCode.value= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.productDescription= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.Stage= getnext ();\r\ntw.local.parentODCRequest.ContractCreation.sourceReference= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\ntw.local.parentODCRequest.FinancialDetailsFO.referenceNo= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.discount= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.extraCharges= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.ourCharges= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.amountSight= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.amountDefNoAvalization= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.amountDefAvalization= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.collectableAmount= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.outstandingAmount= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.maturityDate= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.noOfDaysMaturity= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.financeApprovalNo= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.executionHub = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.FinancialDetailsFO.executionHub.value= getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsFO.executionHub.name= getnext (); \r\ntw.local.parentODCRequest.ContractLiquidation = new tw.object.ContractLiquidation();\r\ntw.local.parentODCRequest.ContractLiquidation.liqAmount =  getnext (); \r\ntw.local.parentODCRequest.ContractLiquidation.liqCurrency = getnext (); \r\ntw.local.parentODCRequest.FcCollections = new tw.object.FCCollections();\r\ntw.local.parentODCRequest.FcCollections.currency =  new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.FcCollections.currency.name= getnext (); \r\ntw.local.parentODCRequest.FcCollections.standardExchangeRate= getnext (); \r\ntw.local.parentODCRequest.FcCollections.negotiatedExchangeRate= getnext ();\r\ntw.local.parentODCRequest.FcCollections.calculatedAmount = getnext ();\r\ntw.local.parentODCRequest.FcCollections.totalAllocatedAmount = getnext ();\r\ngetnext ();\r\ntw.local.parentODCRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\ntw.local.parentODCRequest.ProductShipmentDetails.CBECommodityClassification = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.ProductShipmentDetails.CBECommodityClassification.name= getnext ();\r\ntw.local.parentODCRequest.ProductShipmentDetails.shipmentMethod = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.ProductShipmentDetails.shipmentMethod.name= getnext ();\r\ntw.local.parentODCRequest.ProductShipmentDetails.shippingDate= getnext ();\r\ntw.local.parentODCRequest.ReversalReason = new tw.object.ReversalReason();\r\ntw.local.parentODCRequest.ReversalReason.reversalReason =  getnext ();\r\ntw.local.parentODCRequest.ReversalReason.closureReason =  getnext ();\r\ntw.local.parentODCRequest.ImporterDetails = new tw.object.ImporterDetails();\r\ntw.local.parentODCRequest.ImporterDetails.importerName= getnext ();\r\ntw.local.parentODCRequest.ImporterName = tw.local.parentODCRequest.ImporterDetails.importerName;\r\ntw.local.parentODCRequest.ImporterDetails.importerCountry = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.ImporterDetails.importerCountry.value= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.importerAddress= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.importerPhoneNo= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.bank= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.BICCode= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.ibanAccount= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.bankCountry = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.ImporterDetails.bankCountry.value= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.bankAddress= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.bankPhoneNo= getnext ();\r\ntw.local.parentODCRequest.ImporterDetails.collectingBankReference= getnext (); \r\ngetnext ();\r\ngetnext ();\r\ngetnext ();\r\n\/\/tw.local.parentODCRequest.appInfo.stepName= getnext ();\r\n\/\/tw.local.parentODCRequest.appInfo.subStatus= getnext ();  \r\n\/\/tw.local.parentODCRequest.appInfo.instanceID = getnext (); \r\ntw.local.parentODCRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\ntw.local.parentODCRequest.FinancialDetailsBR.currency = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.FinancialDetailsBR.currency.name = getnext (); \r\ntw.local.parentODCRequest.FinancialDetailsBR.maxCollectionDate = getnext (); \r\ntw.local.parentODCRequest.FinancialDetailsBR.documentAmount = getnext (); \r\ntw.local.parentODCRequest.FinancialDetailsBR.amountAdvanced = getnext ();\r\ntw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.NameValuePair(); \r\ntw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = getnext (); \r\ntw.local.parentODCRequest.FinancialDetailsBR.collectionAccount = new tw.object.NameValuePair();\r\ntw.local.parentODCRequest.FinancialDetailsBR.collectionAccount.value = getnext (); \r\ngetnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();\r\ntw.local.parentODCRequest.BasicDetails.CountryOfOrigin =  {};\r\ntw.local.parentODCRequest.BasicDetails.CountryOfOrigin.name = getnext (); \r\n}\r\n\r\n\r\nif(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure || tw.local.parentODCRequest.requestType.value == reversal ){\r\n if(!!tw.local.results[0]){\r\n  if(!!tw.local.results[0].rows[0]){\r\n   if(tw.local.parentODCRequest.requestType.value == collection ){\r\n       getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation = {};\r\n       tw.local.parentODCRequest.ContractLiquidation.liqAmount = getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.liqCurrency = getnext ();\r\n       tw.local.parentODCRequest.isLiquidated = getnext ();\r\n       getnext ();\r\n       getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.debitValueDate = getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditValueDate = getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.debitedAccountNo = getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount = {};\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.accountClass = {};\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.accountClass.name = getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.branchCode= getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.balanceSign= getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.currency =  {};\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.currency.name= getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.balance= getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAmount = {};\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.standardExRate= getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.negotiatedExRate= getnext ();\r\n       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.amountInAccount= getnext ();\r\n       tw.local.parentODCRequest.FinancialDetailsFO.outstandingAmount= getnext ();\r\n       \r\n  }\r\n else if( tw.local.parentODCRequest.requestType.value == closure){\r\n     getnext ();\r\n     tw.local.parentODCRequest.ReversalReason.closureReason = getnext ();\r\n   }\r\n  else{\r\n     getnext ();\r\n     tw.local.parentODCRequest.ReversalReason.reversalReason = getnext ();\r\n    }\r\n }\r\n}\r\n\r\n  getCreateData();\r\n  tw.local.parentODCRequest.BasicDetails.exportPurpose.name = getCreateData();\r\n  tw.local.parentODCRequest.BasicDetails.paymentTerms.name = getCreateData();\r\n  tw.local.parentODCRequest.BasicDetails.productCategory.name = getCreateData();\r\n  tw.local.parentODCRequest.BasicDetails.commodityDescription= getCreateData();\r\n  tw.local.parentODCRequest.cif= getCreateData();\r\n  tw.local.parentODCRequest.customerName= getCreateData();\r\n  tw.local.parentODCRequest.CustomerInfo.cif = tw.local.parentODCRequest.cif;\r\n  tw.local.parentODCRequest.CustomerInfo.customerName =  tw.local.parentODCRequest.customerName;\r\n  tw.local.parentODCRequest.ImporterName = getCreateData();\r\n  tw.local.parentODCRequest.FinancialDetailsBR.maxCollectionDate = getCreateData();\r\n  tw.local.parentODCRequest.FinancialDetailsBR.currency.name = getCreateData();\r\n  tw.local.parentODCRequest.FinancialDetailsBR.documentAmount = getCreateData();\r\n  tw.local.parentODCRequest.FinancialDetailsBR.amountAdvanced = getCreateData();\r\n  tw.local.parentODCRequest.FcCollections.currency.name = getCreateData();\r\n  tw.local.parentODCRequest.FcCollections.standardExchangeRate = getCreateData();\r\n  tw.local.parentODCRequest.FcCollections.negotiatedExchangeRate = getCreateData();\r\n  tw.local.parentODCRequest.FinancialDetailsBR.collectionAccount.value = getCreateData();\r\n  tw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = getCreateData();\r\n  tw.local.parentODCRequest.BasicDetails.CountryOfOrigin =  {};\r\n  tw.local.parentODCRequest.BasicDetails.CountryOfOrigin.name =  getCreateData(); \r\n  tw.local.parentODCRequest.requestNo = getCreateData ();\r\n  tw.local.parentODCRequest.requestNature = new tw.object.NameValuePair();\r\n  tw.local.parentODCRequest.requestNature.name = tw.local.requestNature;\r\n  tw.local.parentODCRequest.requestType  = new tw.object.NameValuePair();\r\n  tw.local.parentODCRequest.requestType.name =tw.local.requestType;\r\n  tw.local.parentODCRequest.requestType.value =tw.local.requestType;\r\n  getCreateData();\r\n  getCreateData();\r\n  tw.local.parentODCRequest.requestDate = getCreateData();\r\n\r\n}  \r\ntw.local.parentODCRequest.appInfo = tw.local.appInfo;\r\n\r\nif(!!tw.local.subResult){\r\n\/\/------------------------------------- invoice ---------------------------------------\r\ntw.local.parentODCRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nfor (var i=0; i&lt;tw.local.subResult[0].rows.listLength; i++) {\r\n\ttw.local.parentODCRequest.BasicDetails.Invoice[i] = new tw.object.Invoice();\r\n\ttw.local.parentODCRequest.BasicDetails.Invoice[i].invoiceNo = tw.local.subResult[0].rows[i].data[0];\r\n\ttw.local.parentODCRequest.BasicDetails.Invoice[i].invoiceDate = tw.local.subResult[0].rows[i].data[1];\r\n}\r\n\/\/------------------------------------- bill ---------------------------------------\r\ntw.local.parentODCRequest.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nfor (var i=0; i&lt;tw.local.subResult[1].rows.listLength; i++) {\r\n\ttw.local.parentODCRequest.BasicDetails.Bills[i] = new tw.object.Bills();\r\n\ttw.local.parentODCRequest.BasicDetails.Bills[i].billOfLadingRef = tw.local.subResult[1].rows[i].data[0];\r\n\ttw.local.parentODCRequest.BasicDetails.Bills[i].billOfLadingDate = tw.local.subResult[1].rows[i].data[1];\r\n}\r\n\r\n\/\/------------------------------------- Multi tenor ---------------------------------------\r\ntw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nfor (var i=0; i&lt;tw.local.subResult[1].rows.listLength; i++) {\r\n\ttw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();\r\n\ttw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i].amount = tw.local.subResult[1].rows[i].data[0];\r\n\ttw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i].date = tw.local.subResult[1].rows[i].data[1];\r\n}\r\n}\r\n"]}},{"targetRef":"f4c6489d-c98b-4d0c-8c14-f06f37b4dea3","extensionElements":{"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Mapping","declaredType":"sequenceFlow","id":"da8842d8-099d-469a-8297-496306df06db","sourceRef":"*************-4b1f-8b9b-c76176c75d1e"},{"startQuantity":1,"outgoing":["c87c0cc0-63d0-44c1-8159-a055ecf7f01c"],"incoming":["689af0e8-65db-446e-8ce1-a243c870e502","75865d8a-edb4-4791-850d-ddfa76ea09fc","c9e35d21-b640-4df9-8db8-57c4dceafbee","9ace293d-083e-4607-80a1-5d6425b74e1a","c5c3a7c9-bae1-4c6f-81b7-0cddab860201","c4879f9d-5ed9-4516-8f51-a28a201baf43"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":660,"y":375,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Catch Errors","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Get Request and customer data\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"d01dc092-a8ab-4347-84c0-07788e8d53c5","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"cc174e9a-35da-4e42-82c8-c8b6aee07473","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"c87c0cc0-63d0-44c1-8159-a055ecf7f01c","sourceRef":"d01dc092-a8ab-4347-84c0-07788e8d53c5"},{"incoming":["c87c0cc0-63d0-44c1-8159-a055ecf7f01c"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"613550d1-afdb-477f-801e-4e3de6de6932","otherAttributes":{"eventImplId":"f5f14586-281c-4c76-89a2-e308d8f47b52"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":791,"y":441,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"cc174e9a-35da-4e42-82c8-c8b6aee07473"},{"itemSubjectRef":"itm.12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6","name":"appInfo","isCollection":false,"declaredType":"dataObject","id":"2056.b3483e37-ca25-48db-846d-617a8b18bd0a"},{"startQuantity":1,"outgoing":["22ac707d-a2d0-4a29-805d-8c60e272a3e1"],"incoming":["5384ef98-eb34-418e-8327-17d7409b65f1"],"extensionElements":{"postAssignmentScript":["tw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;"],"nodeVisualInfo":[{"width":95,"x":1143,"y":182,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Get Customer Data","dataInputAssociation":[{"targetRef":"2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentODCRequest.CustomerInfo.cif"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"4f0fe545-64ed-45fa-85ee-22fc6e427cbe","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a","declaredType":"TFormalExpression","content":["tw.local.parentODCRequest.CustomerInfo"]}}],"sourceRef":["2055.373a4a6c-181b-43d5-8f0e-8b400049f72e"]}],"calledElement":"1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb"},{"targetRef":"b174846a-ab48-4ccc-8126-9936415a4ed6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get Customer Data","declaredType":"sequenceFlow","id":"c78bbd5b-9644-44ef-8764-19cd19d19e71","sourceRef":"f4c6489d-c98b-4d0c-8c14-f06f37b4dea3"},{"targetRef":"f7340a62-ac61-41b9-80fa-4c57df997206","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:83e1efe624431d49:-7084ad56:189daab98ba:-788e"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Query BC Contract","declaredType":"sequenceFlow","id":"22ac707d-a2d0-4a29-805d-8c60e272a3e1","sourceRef":"4f0fe545-64ed-45fa-85ee-22fc6e427cbe"},{"startQuantity":1,"outgoing":["629fef8d-bc77-4e55-80cc-d6dc14851830"],"incoming":["83becffc-96b8-46de-8f3e-9ed46a8b2d06"],"extensionElements":{"postAssignmentScript":["tw.local.parentODCRequest.CustomerInfo = tw.local.customerInfo;\r\ntw.local.parentODCRequest.cif = tw.local.parentODCRequest.CustomerInfo.cif;\r\ntw.local.parentODCRequest.customerName =tw.local.parentODCRequest.CustomerInfo.customerName\r\n;\r\ntw.local.parentODCRequest.requestNature.value = tw.local.requestNature;\r\ntw.local.parentODCRequest.requestType.value = tw.local.requestType;"],"nodeVisualInfo":[{"width":95,"x":1429,"y":181,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"Query BC Contract","dataInputAssociation":[{"targetRef":"2055.9465f3bb-e80a-444e-8672-899423d668fb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.parentODCRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"beca00b9-c736-47b8-8068-da53f409b0c6","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.parentODCRequest"]}}],"sourceRef":["2055.1447a720-69f1-46ca-8d27-88a12b035edf"]}],"calledElement":"1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676"},{"targetRef":"b7f12cdc-6a9b-420a-876c-c8d48c020ca3","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"629fef8d-bc77-4e55-80cc-d6dc14851830","sourceRef":"beca00b9-c736-47b8-8068-da53f409b0c6"},{"itemSubjectRef":"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a","name":"customerInfo","isCollection":false,"declaredType":"dataObject","id":"2056.20be0104-f271-45fb-8727-0c8b34d26ea3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNature","isCollection":false,"declaredType":"dataObject","id":"2056.548547b9-091d-4195-820c-5bdf07185978"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestType","isCollection":false,"declaredType":"dataObject","id":"2056.3f672b88-c49e-4748-89c9-4a4491f5b319"},{"startQuantity":1,"outgoing":["2fce8c92-cc97-4cbb-8665-3f4a4539259f"],"incoming":["2027.72a44093-5c50-4545-8193-459e38ad670a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":73,"y":180,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.requestType = tw.local.parentODCRequest.requestType.value;\r\ntw.local.requestNature = \"\"+ tw.epv.RequestNature.UpdateRequest;\r\ntw.local.parentODCRequest.parentRequestNo = tw.local.requestNumber;\r\ntw.local.appInfo = tw.local.parentODCRequest.appInfo;\r\ntw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;\r\ntw.local.match = true;\r\ntw.local.errorMSG =\"\";"]},"name":"Check Parent Request is Completed SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar j = 0;\r\nvar i = 0;\r\nfunction paramInit (value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti++;\r\n}\r\n\r\nvar final = \"\" +tw.epv.RequestState.Final;\r\nvar collected = \"\"+ tw.epv.RequestState.Collection;\r\nvar amend = \"\" + tw.epv.RequestType.Amendment;\r\nvar recreate = \"\" + tw.epv.RequestType.Recreate;\r\nvar collection = \"\" + tw.epv.RequestType.Collection;\r\nvar completed = \"\" + tw.epv.Status.completed;  \r\nvar terminated = \"\" + tw.epv.Status.terminated;\r\nvar cancelled = \"\" + tw.epv.Status.Canceled;\r\n\r\n\/\/check parent request is completed\r\ntw.local.sqlStatements[j] = {};\r\ntw.local.sqlStatements[j].sql=\"SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTNO = ? AND REQUESTSTATE = '\"+final+\"' AND REQUESTSTATUS = '\"+completed+\"';\";\r\n\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\nparamInit(tw.local.parentODCRequest.parentRequestNo);\r\n\r\n\/\/check no amend\/recreate\/collecion running\r\nj++;\r\ni=0;\r\ntw.local.sqlStatements[j] = {};\r\ntw.local.sqlStatements[j].sql=\"SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND (REQUESTTYPE = '\"+amend+\"' OR REQUESTTYPE = '\"+recreate+\"' OR REQUESTTYPE = '\"+collection+\"') AND (REQUESTSTATUS != '\"+completed+\"' AND REQUESTSTATUS != '\"+terminated+\"' AND REQUESTSTATUS != '\"+cancelled+\"');\";\r\n\r\ntw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\nparamInit(tw.local.parentODCRequest.parentRequestNo);\r\n\r\n\/\/check no collection done in case of recreate\r\nj++;\r\ni=0;\r\nif(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Recreate){\r\n   tw.local.sqlStatements[j] = {};\r\n   tw.local.sqlStatements[j].sql=\"SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND REQUESTSTATE = '\"+collected+\"' AND REQUESTSTATUS = '\"+completed+\"';\";\r\n\r\n   tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n   paramInit(tw.local.parentODCRequest.parentRequestNo);\r\n}"]}},{"startQuantity":1,"outgoing":["7b6e893d-ace2-4575-8ff8-9db0467537ca"],"incoming":["2fce8c92-cc97-4cbb-8665-3f4a4539259f"],"extensionElements":{"postAssignmentScript":["tw.local.isFound = true;\r\n\r\nif (tw.local.results[0].rows.listLength == 0) {\r\n    \r\n\t tw.local.message = \"Request number does not exist or is in another running request \";\r\n\t tw.local.isFound = false;\r\n\t \r\n\t}\r\n  \r\nif(!!tw.local.results[1]){\r\n  if (tw.local.results[1].rows.listLength &gt;0){   \r\n  \t tw.local.message = \"Request number is in another running request \";\r\n\t tw.local.isFound = false;\r\n  } \r\n}  \r\n\/\/recreate\t\r\nif(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Recreate){\r\n if(!!tw.local.results[2]){\r\n  if (tw.local.results[2].rows.listLength &gt; 0){\r\n   \r\n     tw.local.message = \"There must be no collection done on ODC\"\r\n     tw.local.isFound = false;\r\n   }\r\n } \r\n\r\n}   "],"nodeVisualInfo":[{"width":95,"x":202,"y":180,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"SQL Execute Check","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"3f05b5f1-d684-4d58-8da6-58a8ed39350c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"parallelMultiple":false,"outgoing":["75865d8a-edb4-4791-850d-ddfa76ea09fc"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"2e84b0e4-da76-4fdb-8792-4f4e6326233d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"21e996e2-85b3-4292-8ae5-b91dd3d448f9","otherAttributes":{"eventImplId":"00914df3-74fe-4b4f-8942-6b1af4ecd6f2"}}],"attachedToRef":"3f05b5f1-d684-4d58-8da6-58a8ed39350c","extensionElements":{"nodeVisualInfo":[{"width":24,"x":237,"y":238,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Copy of Error1","declaredType":"boundaryEvent","id":"55f4c6b0-344c-432a-8df5-d1b81764133d","outputSet":{}},{"targetRef":"3f05b5f1-d684-4d58-8da6-58a8ed39350c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute Check","declaredType":"sequenceFlow","id":"2fce8c92-cc97-4cbb-8665-3f4a4539259f","sourceRef":"29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc"},{"outgoing":["486d8d2b-71ea-4a47-854a-0d1947d38d06","aa9a742a-06ca-485e-8abb-9b0dcae801cd"],"incoming":["7b6e893d-ace2-4575-8ff8-9db0467537ca"],"default":"486d8d2b-71ea-4a47-854a-0d1947d38d06","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":311,"y":198,"declaredType":"TNodeVisualInfo","height":32}]},"name":"exist?","declaredType":"exclusiveGateway","id":"5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f"},{"startQuantity":1,"outgoing":["67bb1d02-584a-4d03-81c5-4f69fcd20737"],"incoming":["23c3b638-4eb7-4d6e-8743-af945b48d965"],"extensionElements":{"postAssignmentScript":["if (tw.local.results[0].rows.listLength == 0) {\r\n      if(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Amendment|| tw.local.parentODCRequest.requestType.value ==  tw.epv.RequestType.Closure || tw.local.parentODCRequest.requestType.value ==  tw.epv.RequestType.Collection){\r\n\t tw.local.message = \"Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 \";\r\n\t}\r\n\telse\r\n\t tw.local.message = \"Request number does not exist or is in another running request\";\r\n\t tw.local.isFound = false;\r\n\t\r\n\tif(!!tw.local.results[1]){\r\n\tif(!!tw.local.results[1].rows){\r\n\t   if (tw.local.results[1].rows.listLength == 0){\r\n            tw.local.message = \"Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 \";\r\n\t      tw.local.isFound = false;\r\n\r\n          }\r\n          else\r\n              tw.local.isFound = true;\r\n        \r\n\t   }\r\n\t }\r\n}\r\n\r\n\r\n"],"nodeVisualInfo":[{"width":95,"x":516,"y":179,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[],"activityType":["CalledProcess"]},"name":"SQL Execute","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.sqlStatements"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"3c8dc3f1-8259-4f0b-8f25-5083a268e5be","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"targetRef":"5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f","extensionElements":{"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To exist?","declaredType":"sequenceFlow","id":"7b6e893d-ace2-4575-8ff8-9db0467537ca","sourceRef":"3f05b5f1-d684-4d58-8da6-58a8ed39350c"},{"targetRef":"********-a136-4364-859c-8a4eda3d953d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To SQL","declaredType":"sequenceFlow","id":"486d8d2b-71ea-4a47-854a-0d1947d38d06","sourceRef":"5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f"},{"targetRef":"b7f12cdc-6a9b-420a-876c-c8d48c020ca3","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.isFound\t  ==\t  false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"aa9a742a-06ca-485e-8abb-9b0dcae801cd","sourceRef":"5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f"},{"targetRef":"b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d","extensionElements":{"endStateId":["guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To found?","declaredType":"sequenceFlow","id":"67bb1d02-584a-4d03-81c5-4f69fcd20737","sourceRef":"3c8dc3f1-8259-4f0b-8f25-5083a268e5be"},{"targetRef":"d01dc092-a8ab-4347-84c0-07788e8d53c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"75865d8a-edb4-4791-850d-ddfa76ea09fc","sourceRef":"55f4c6b0-344c-432a-8df5-d1b81764133d"},{"startQuantity":1,"outgoing":["23c3b638-4eb7-4d6e-8743-af945b48d965"],"incoming":["486d8d2b-71ea-4a47-854a-0d1947d38d06"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":399,"y":179,"declaredType":"TNodeVisualInfo","height":70}]},"name":"SQL","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"********-a136-4364-859c-8a4eda3d953d","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar j = 0;\r\nvar i = 0;\r\nfunction paramInit (value) {\r\n\ttw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();\r\n\ttw.local.sqlStatements[j].parameters[i].value = value;\r\n\ti++;\r\n}\r\n\/\/------------------------------------- Get ODC -------------------------------------------- \r\nvar final = \"\" +tw.epv.RequestState.Final;\r\nvar closed = \"\" + tw.epv.RequestState.Closed;\r\nvar reversed = \"\" + tw.epv.RequestState.Reversed;\r\nvar collected = \"\"+ tw.epv.RequestState.Collection;\r\nvar amend = \"\" + tw.epv.RequestType.Amendment;\r\nvar recreate = \"\" + tw.epv.RequestType.Recreate;\r\nvar collection = \"\" + tw.epv.RequestType.Collection;\r\nvar closure = \"\" + tw.epv.RequestType.Closure;\r\nvar reversal = \"\" + tw.epv.RequestType.Reversal;\r\nvar create = \"\" + tw.epv.RequestType.Create;\r\nvar completed = \"\" + tw.epv.Status.completed; \r\nvar approved = \"\" + tw.epv.Status.Approved;\r\n\r\ntw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n\r\nif(tw.local.parentODCRequest.requestType.value == amend || tw.local.parentODCRequest.requestType.value == recreate  ){\r\n  tw.local.sqlStatements[j].sql=\"SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND (REQUESTTYPE = '\"+amend+\"' OR REQUESTTYPE = '\"+recreate+\"' OR REQUESTTYPE = '\"+create+\"') AND REQUESTSTATE = '\"+final+\"'  AND REQUESTSTATUS='\"+completed+\"')\";\r\n  if(tw.local.parentODCRequest.requestType.value == amend ) \r\n    tw.local.sqlStatements[j].sql+= \" AND OUTSTANDINGAMOUNT &gt; 0;\";\r\n   tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n   paramInit(tw.local.parentODCRequest.parentRequestNo);\r\n}\r\n\r\nelse if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure || tw.local.parentODCRequest.requestType.value == reversal ){\r\n\r\n  \r\n  if(tw.local.parentODCRequest.requestType.value == collection){\r\n      tw.local.sqlStatements[j].sql=\"SELECT ID, liqAmount, liqCurrency, isLiquidated , \"\r\n      +\"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT\"\r\n      +\" FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '\"+collection+\"'   AND REQUESTSTATE = '\"+collected+\"' AND REQUESTSTATUS='\"+completed+\"')\";\r\n      tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n      paramInit(tw.local.parentODCRequest.parentRequestNo);\r\n  \r\n  }\r\n  else if (tw.local.parentODCRequest.requestType.value == closure){\r\n       tw.local.sqlStatements[j].sql=\"SELECT ID, CLOSUREREASON FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '\"+closure+\"'  AND REQUESTSTATE = '\"+closed+\"' AND REQUESTSTATUS='\"+completed+\"')\";\r\n       tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n       paramInit(tw.local.parentODCRequest.parentRequestNo);\r\n  \r\n  \r\n  }\r\n  else{\r\n       tw.local.sqlStatements[j].sql=\"SELECT ID, REVERSALREASON FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '\"+reversal+\"'  AND REQUESTSTATE = '\"+reversed+\"' AND (REQUESTSTATUS='\"+completed+\"' OR REQUESTSTATUS='\"+approved+\"' ))\";\r\n       tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n       paramInit(tw.local.parentODCRequest.parentRequestNo);\r\n  }\r\n  \r\n  j++;\r\n  i=0;\r\n  \/\/ get some of create data\r\n  tw.local.sqlStatements[j] = new tw.object.SQLStatement();\r\n  tw.local.sqlStatements[j].sql=\"SELECT ID, EXPORTPURPOSE,PAYMENTTERMS,PRODUCTCATEGORY,COMMODITYDESCRIPTION,CIF,CUSTOMERNAME,IMPORTERNAME, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT,collectionCurrency, standardExRate, negotiatedExRate ,  COLLECTIONACCOUNT, CHARGESACCOUNT,  COUNTRYOFORIGIN,requestNo, requestNature , requestType , requestDate\"\r\n   +\" FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND (REQUESTTYPE = '\"+amend+\"' OR REQUESTTYPE = '\"+recreate+\"' OR REQUESTTYPE = '\"+create+\"') AND REQUESTSTATE = '\"+final+\"'  AND REQUESTSTATUS='\"+completed+\"' ) \";\r\n  \r\n  if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure ) \r\n      tw.local.sqlStatements[j].sql +=\" AND OUTSTANDINGAMOUNT &gt; 0;\";\r\n      tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();\r\n     paramInit(tw.local.parentODCRequest.parentRequestNo);   \r\n\r\n}\r\n\r\n\r\nelse{}\r\n\r\n"]}},{"targetRef":"3c8dc3f1-8259-4f0b-8f25-5083a268e5be","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To SQL Execute","declaredType":"sequenceFlow","id":"23c3b638-4eb7-4d6e-8743-af945b48d965","sourceRef":"********-a136-4364-859c-8a4eda3d953d"},{"parallelMultiple":false,"outgoing":["c9e35d21-b640-4df9-8db8-57c4dceafbee"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"f3465617-948f-4a7a-87c0-6760ef40ff58"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"a388daa9-db68-4071-888a-892055b01acc","otherAttributes":{"eventImplId":"d8e132cb-2233-4b5e-87ad-58daac44d033"}}],"attachedToRef":"4f0fe545-64ed-45fa-85ee-22fc6e427cbe","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1152,"y":240,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"c83f07e7-4d6a-4852-826f-7c4e5a93bd6b","outputSet":{}},{"targetRef":"d01dc092-a8ab-4347-84c0-07788e8d53c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c9e35d21-b640-4df9-8db8-57c4dceafbee","sourceRef":"c83f07e7-4d6a-4852-826f-7c4e5a93bd6b"},{"parallelMultiple":false,"outgoing":["9ace293d-083e-4607-80a1-5d6425b74e1a"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"45497576-b23f-48f6-8b29-ebd8e1e14091"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"5c41723a-f5a2-48ea-8f0c-9bcd0a209161","otherAttributes":{"eventImplId":"2d5f9ed0-99c0-477d-862e-83a501075dfd"}}],"attachedToRef":"beca00b9-c736-47b8-8068-da53f409b0c6","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1438,"y":239,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error2","declaredType":"boundaryEvent","id":"8b0113d8-d2f9-40b1-8258-c0578c6f4659","outputSet":{}},{"targetRef":"d01dc092-a8ab-4347-84c0-07788e8d53c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"9ace293d-083e-4607-80a1-5d6425b74e1a","sourceRef":"8b0113d8-d2f9-40b1-8258-c0578c6f4659"},{"parallelMultiple":false,"outgoing":["c5c3a7c9-bae1-4c6f-81b7-0cddab860201"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"b57731cd-2386-40c7-8ced-8f9451a4605d"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"78a2fd80-049c-4484-8a17-80f508ba47ab","otherAttributes":{"eventImplId":"bb18da33-cb46-44f1-8b2b-45364d05e8da"}}],"attachedToRef":"8ae854db-345a-4c0c-80db-df3c8592f922","extensionElements":{"nodeVisualInfo":[{"width":24,"x":799,"y":240,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error3","declaredType":"boundaryEvent","id":"233be30a-a1ac-459f-86fe-a448f57849e4","outputSet":{}},{"targetRef":"d01dc092-a8ab-4347-84c0-07788e8d53c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c5c3a7c9-bae1-4c6f-81b7-0cddab860201","sourceRef":"233be30a-a1ac-459f-86fe-a448f57849e4"},{"parallelMultiple":false,"outgoing":["689af0e8-65db-446e-8ce1-a243c870e502"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"544485ef-4e7a-40a7-b1e8-71a056c32bed"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"97ea7075-8b4f-44d4-8f7d-838744382220","otherAttributes":{"eventImplId":"3d38db4c-46ff-4e7f-8a1f-1f83e8080829"}}],"attachedToRef":"*************-4b1f-8b9b-c76176c75d1e","extensionElements":{"nodeVisualInfo":[{"width":24,"x":911,"y":241,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error4","declaredType":"boundaryEvent","id":"861189a8-6ee5-4c01-8300-e3d71a6904ef","outputSet":{}},{"targetRef":"d01dc092-a8ab-4347-84c0-07788e8d53c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"689af0e8-65db-446e-8ce1-a243c870e502","sourceRef":"861189a8-6ee5-4c01-8300-e3d71a6904ef"},{"startQuantity":1,"outgoing":["83becffc-96b8-46de-8f3e-9ed46a8b2d06"],"incoming":["22ac707d-a2d0-4a29-805d-8c60e272a3e1"],"extensionElements":{"postAssignmentScript":["if(!!tw.local.parentODCRequest.FinancialDetailsBR){\r\n\r\n  if(!tw.local.parentODCRequest.FcCollections)\r\n      tw.local.parentODCRequest.FcCollections = {};\r\n      \r\n  tw.local.parentODCRequest.FcCollections.listOfAccounts = [];\r\n\t\r\n\tfor(var i=0;i&lt;tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts.length;i++)\r\n\t{\r\n\t     \r\n\t\ttw.local.parentODCRequest.FcCollections.listOfAccounts[i] = {};\r\n\t\ttw.local.parentODCRequest.FcCollections.listOfAccounts[i]= tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts[i];\r\n\t}\r\n} \r\n"],"nodeVisualInfo":[{"width":95,"x":1273,"y":181,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get Customer Accounts","dataInputAssociation":[{"targetRef":"2055.0bc98ce8-10aa-460a-8857-8a8600dafc90","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentODCRequest.CustomerInfo.cif"]}}]},{"targetRef":"2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentODCRequest.FcCollections.currency.value"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"f7340a62-ac61-41b9-80fa-4c57df997206","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","declaredType":"TFormalExpression","content":["tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts"]}}],"sourceRef":["2055.208f75bb-611b-4393-83d6-d60470c4a6a4"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMSG"]}}],"sourceRef":["2055.7fdee937-d555-46b6-88d2-46c40e165c27"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074"]}],"calledElement":"1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.23b90cb0-77a8-406b-8cd3-4891cf594d48"},{"targetRef":"beca00b9-c736-47b8-8068-da53f409b0c6","extensionElements":{"endStateId":["guid:fef170a08f25d496:5466e087:189df5f8551:75c2"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Query BC Contract","declaredType":"sequenceFlow","id":"83becffc-96b8-46de-8f3e-9ed46a8b2d06","sourceRef":"f7340a62-ac61-41b9-80fa-4c57df997206"},{"parallelMultiple":false,"outgoing":["c4879f9d-5ed9-4516-8f51-a28a201baf43"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"3f7d7a4b-295f-46f4-8a99-8ef957a20628"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"4e2984b7-d21f-4bed-816e-b0dac71b91c8","otherAttributes":{"eventImplId":"6bca84bb-b477-4631-872c-a2519ba5b7b1"}}],"attachedToRef":"f7340a62-ac61-41b9-80fa-4c57df997206","extensionElements":{"nodeVisualInfo":[{"width":24,"x":1308,"y":239,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error5","declaredType":"boundaryEvent","id":"36bf88b4-fe33-40a6-86e3-04bf6c38ff76","outputSet":{}},{"targetRef":"d01dc092-a8ab-4347-84c0-07788e8d53c5","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Catch Errors","declaredType":"sequenceFlow","id":"c4879f9d-5ed9-4516-8f51-a28a201baf43","sourceRef":"36bf88b4-fe33-40a6-86e3-04bf6c38ff76"},{"outgoing":["5384ef98-eb34-418e-8327-17d7409b65f1","2bcf95a6-6173-4bed-8878-79a582b256f7"],"incoming":["c78bbd5b-9644-44ef-8764-19cd19d19e71"],"default":"2bcf95a6-6173-4bed-8878-79a582b256f7","gatewayDirection":"Unspecified","extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":32,"x":1052,"y":280,"declaredType":"TNodeVisualInfo","height":32}],"preAssignmentScript":[]},"name":"Match?","declaredType":"exclusiveGateway","id":"b174846a-ab48-4ccc-8126-9936415a4ed6"},{"targetRef":"4f0fe545-64ed-45fa-85ee-22fc6e427cbe","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.customerInfo.cif\t  ==\t  tw.local.parentODCRequest.CustomerInfo.cif"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"5384ef98-eb34-418e-8327-17d7409b65f1","sourceRef":"b174846a-ab48-4ccc-8126-9936415a4ed6"},{"targetRef":"b7f12cdc-6a9b-420a-876c-c8d48c020ca3","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.customerInfo.cif != tw.local.parentODCRequest.CustomerInfo.cif"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomLeft","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"8a7190f7-1d05-4b55-8cb1-1f773438780b","sourceRef":"43955c42-94c2-481f-84b0-40a75607048c"},{"startQuantity":1,"outgoing":["8a7190f7-1d05-4b55-8cb1-1f773438780b"],"incoming":["2bcf95a6-6173-4bed-8878-79a582b256f7"],"extensionElements":{"nodeVisualInfo":[{"color":"#95D087","width":95,"x":1145,"y":292,"declaredType":"TNodeVisualInfo","height":70}]},"name":"validation script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"43955c42-94c2-481f-84b0-40a75607048c","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMSG = \"This CIF Number Not related to Parent Request Number\";\r\ntw.local.match = false;\r\n\r\n\r\n"]}},{"targetRef":"43955c42-94c2-481f-84b0-40a75607048c","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.customerInfo.cif != tw.local.parentODCRequest.cif"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"2bcf95a6-6173-4bed-8878-79a582b256f7","sourceRef":"b174846a-ab48-4ccc-8126-9936415a4ed6"}],"laneSet":[{"id":"463c83d0-ed75-425d-85b7-631de7d89c5e","lane":[{"flowNodeRef":["19265436-ca75-4c91-81d7-23a5d8630227","b7f12cdc-6a9b-420a-876c-c8d48c020ca3","b6425fe1-912b-453f-8b9c-a039277a451b","9886a463-0e69-404d-8f95-f6f22c77f59b","d2b49723-5052-4fc7-8c36-517d02057cdc","980ad00d-5fbb-423c-85ee-08e4caff0a41","b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d","8ae854db-345a-4c0c-80db-df3c8592f922","*************-4b1f-8b9b-c76176c75d1e","861189a8-6ee5-4c01-8300-e3d71a6904ef","f4c6489d-c98b-4d0c-8c14-f06f37b4dea3","d01dc092-a8ab-4347-84c0-07788e8d53c5","cc174e9a-35da-4e42-82c8-c8b6aee07473","4f0fe545-64ed-45fa-85ee-22fc6e427cbe","beca00b9-c736-47b8-8068-da53f409b0c6","29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc","3f05b5f1-d684-4d58-8da6-58a8ed39350c","55f4c6b0-344c-432a-8df5-d1b81764133d","5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f","3c8dc3f1-8259-4f0b-8f25-5083a268e5be","********-a136-4364-859c-8a4eda3d953d","c83f07e7-4d6a-4852-826f-7c4e5a93bd6b","8b0113d8-d2f9-40b1-8258-c0578c6f4659","233be30a-a1ac-459f-86fe-a448f57849e4","f7340a62-ac61-41b9-80fa-4c57df997206","36bf88b4-fe33-40a6-86e3-04bf6c38ff76","b174846a-ab48-4ccc-8126-9936415a4ed6","43955c42-94c2-481f-84b0-40a75607048c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"a3a8b0c3-5820-4c38-89bc-26c49b8e6eb4","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Request and Customer Data","declaredType":"process","id":"1.58febcb0-50a3-4963-8368-32c96c00d116","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"message","isCollection":false,"id":"2055.b1f93375-04e2-4a6a-8e23-a158a2320eb6"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isFound","isCollection":false,"id":"2055.f7fc5ea8-49a6-40b1-8b25-250a8fdd25fa"},{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"parentODCRequest","isCollection":false,"id":"2055.6448783e-44be-4081-88f8-e6d08fe0c107"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.58299858-8daf-4093-89ff-ec58ea61013c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"match","isCollection":false,"id":"2055.013d14bf-4f05-44e3-89dd-360f5ad5ceb8"}],"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.062854b5-6513-4da8-84ab-0126f90e550d","epvProcessLinkId":"ef953f1a-e83a-4d3f-8172-6a0d6ee4550f","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"61c694b5-4fc3-40d6-89dc-f78f607c058c","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"42bbcb93-d82a-45c0-8ff4-9bca773aa7a1","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.340b122c-2fdf-400c-822c-b0c52fb7b022","epvProcessLinkId":"5dcc648e-af88-476d-8751-442a7b9a0866","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.67ed8128-22da-46f1-894a-ab580c23f58f","2055.d9a8520f-f8b4-408f-8cab-eafb8f688056"]}],"outputSet":[{"dataOutputRefs":["2055.b1f93375-04e2-4a6a-8e23-a158a2320eb6","2055.f7fc5ea8-49a6-40b1-8b25-250a8fdd25fa","2055.6448783e-44be-4081-88f8-e6d08fe0c107","2055.58299858-8daf-4093-89ff-ec58ea61013c","2055.013d14bf-4f05-44e3-89dd-360f5ad5ceb8"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"00104230000228\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNumber","isCollection":false,"id":"2055.67ed8128-22da-46f1-894a-ab580c23f58f"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"amend\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"00104230000228\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"599IAVC222440002\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"parentODCRequest","isCollection":false,"id":"2055.d9a8520f-f8b4-408f-8cab-eafb8f688056"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="requestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.67ed8128-22da-46f1-894a-ab580c23f58f</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"00104230000228"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f6446ebd-8675-4250-81ce-ff97bc89f10e</guid>
            <versionId>4de29456-98d0-471b-951d-907a848c6200</versionId>
        </processParameter>
        <processParameter name="parentODCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d9a8520f-f8b4-408f-8cab-eafb8f688056</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "amend";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "00104230000228";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "599IAVC222440002";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>38005b01-7f8d-4346-888e-2c066d957aa4</guid>
            <versionId>bbdef648-f564-4422-9b78-2ebc03334899</versionId>
        </processParameter>
        <processParameter name="message">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b1f93375-04e2-4a6a-8e23-a158a2320eb6</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>991b7ab7-cf42-493e-a80d-6e616dc11fbe</guid>
            <versionId>72cac220-9ad4-45d9-bb8c-c4826ca493a9</versionId>
        </processParameter>
        <processParameter name="isFound">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.f7fc5ea8-49a6-40b1-8b25-250a8fdd25fa</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f900d2d3-daad-4d86-81b9-20aec601c122</guid>
            <versionId>a6833095-c68a-4097-81e9-44a75a5dc783</versionId>
        </processParameter>
        <processParameter name="parentODCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6448783e-44be-4081-88f8-e6d08fe0c107</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>1a408043-4526-4b99-9294-1f9f8c269606</guid>
            <versionId>54d736ae-6217-424b-8b44-beb9323ac5ca</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.58299858-8daf-4093-89ff-ec58ea61013c</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>84df9a4c-175d-4f05-9113-b036ce84edc3</guid>
            <versionId>fec40dc2-4a3d-4f5c-a2f8-0821eba7b823</versionId>
        </processParameter>
        <processParameter name="match">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.013d14bf-4f05-44e3-89dd-360f5ad5ceb8</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>866fabb7-b37e-493c-be9e-02a5316baa74</guid>
            <versionId>72b3e572-391c-480b-91b2-b6973f41c383</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.17de011c-4e97-4bf5-aac8-978f3dd6ae71</processParameterId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>8</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>353c8ed0-d0b0-4d9b-afb9-bcf1e3b739a4</guid>
            <versionId>e3cc29e8-ffab-4272-ab89-a41d209e88e4</versionId>
        </processParameter>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ba6075c5-05a5-435b-8e40-4343f8f653c6</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>75745116-1d6b-4acc-86cb-362926b8e85d</guid>
            <versionId>5605d076-d230-42c3-923c-63ed442e6092</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ec98c981-3818-434e-88f0-d24275a1f30d</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5f9f1f52-00b3-4a81-8a25-383b24219e8a</guid>
            <versionId>60d6fb49-b418-4410-bb82-43390b970eec</versionId>
        </processVariable>
        <processVariable name="subResult">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a444daad-a00d-47b4-8d1b-29d2c3513b1c</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>80863327-e07a-45e9-8f78-576ea69f825e</guid>
            <versionId>3d173ab0-1acc-4575-be2f-ca70415cd820</versionId>
        </processVariable>
        <processVariable name="errorMSG">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9b504e0d-6fa4-4854-8bee-651e6f489ef6</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>93c3332c-aacf-4d7c-902a-814dd992eae7</guid>
            <versionId>4b02a9c8-58d5-488d-af9e-aae043e6cef7</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7eb13764-afaf-4054-8858-66122a18ecbb</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2032e1a3-b41e-43cc-904b-7bf5c743bd7c</guid>
            <versionId>ed4a9892-a5c1-4e78-91be-40777bf155cf</versionId>
        </processVariable>
        <processVariable name="appInfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b3483e37-ca25-48db-846d-617a8b18bd0a</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>341e5684-f12d-4cf6-aac8-d2dc77d648ab/12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>105c3d62-bfa5-44f4-bb30-7087414ea70c</guid>
            <versionId>3b8bf8ed-c980-4100-8aa9-78d14ebfcef8</versionId>
        </processVariable>
        <processVariable name="customerInfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.20be0104-f271-45fb-8727-0c8b34d26ea3</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d3249f7a-6446-40a5-ad2e-f3ce04257717</guid>
            <versionId>abea1238-e06e-471f-a5fd-a879db500c80</versionId>
        </processVariable>
        <processVariable name="requestNature">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.548547b9-091d-4195-820c-5bdf07185978</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>51a75e35-9661-439d-b16d-f026aa2287d2</guid>
            <versionId>9e4e75d9-72e2-4fd9-8cc0-cdab3b16e377</versionId>
        </processVariable>
        <processVariable name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.3f672b88-c49e-4748-89c9-4a4491f5b319</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0e4f16e4-3a9e-4330-aab9-ff4094bfb698</guid>
            <versionId>f540028c-d459-4d86-bc08-6a04dc1e8fa2</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.23b90cb0-77a8-406b-8cd3-4891cf594d48</processVariableId>
            <description isNull="true" />
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d3b794d3-2c54-4b58-a539-5b782f4a84ce</guid>
            <versionId>04c1c73c-c2f4-4452-b1b5-647880df6b71</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.*************-4b1f-8b9b-c76176c75d1e</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>sub SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.cbc6ca74-abf7-4b29-baaa-46417e5fc18c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:51f</guid>
            <versionId>0c7eb139-1e54-4675-ba76-8ee97d6ecd6a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="876" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error4</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d54</errorHandlerItem>
                <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.cbc6ca74-abf7-4b29-baaa-46417e5fc18c</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>f5129ff3-a6b5-443a-a92a-1319f410f97d</guid>
                <versionId>eafbbfe8-2d4d-48e7-96e9-3ba73fb05d76</versionId>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6d77d005-d76e-44b9-a46a-303b3971dca3</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.cbc6ca74-abf7-4b29-baaa-46417e5fc18c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>065e1885-984c-4d0e-945a-2f277325a4cd</guid>
                    <versionId>0e602b6f-5de0-466e-b231-f7372290e073</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a43de770-c8c5-436d-a6f3-c53799623f87</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.cbc6ca74-abf7-4b29-baaa-46417e5fc18c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.subResult</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>39984d60-ddb1-4da8-a9f2-43dbe93e61b7</guid>
                    <versionId>a258db4c-9886-420f-b38a-f90eb96d0340</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.64f50d29-215b-4d04-966b-65964f4dc687</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.cbc6ca74-abf7-4b29-baaa-46417e5fc18c</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>232402f0-d721-43a6-a0d0-1643556f6fda</guid>
                    <versionId>c5ed70cc-d3ab-4424-b570-4268eb564543</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.25daf28f-9a29-4011-98bf-6774eb53b416</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:1c18</guid>
            <versionId>24aa3707-38b5-442a-abde-14f6babcabde</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.40dbfde0-4c67-4e3d-a684-5c52fef4b5a5</processItemPrePostId>
                <processItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>7f4b8746-4cdf-460c-8102-9cd2dd44cb79</guid>
                <versionId>044de104-243f-4e29-a4ee-074cdbf84c32</versionId>
            </processPrePosts>
            <layoutData x="1543" y="165">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.25daf28f-9a29-4011-98bf-6774eb53b416</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b21067f4-b205-415b-ae54-c472eb26ed67</guid>
                <versionId>f86504c1-74cb-4fb1-984a-0c585c616e76</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.beca00b9-c736-47b8-8068-da53f409b0c6</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Query BC Contract</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b748a73e-a9c6-4dd8-a00b-7f269e137ccc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2d03</guid>
            <versionId>2c0a8deb-e135-4065-b025-795d397082fe</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.91127c72-465e-4295-b5d5-1bf281f97441</processItemPrePostId>
                <processItemId>2025.beca00b9-c736-47b8-8068-da53f409b0c6</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>4685944d-075e-4476-811f-bfde5481a067</guid>
                <versionId>358170ba-5b83-449c-8a48-c8f3ad33c3cf</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.e3f2c6c2-e518-442e-b3b5-a74b6fe9f535</processItemPrePostId>
                <processItemId>2025.beca00b9-c736-47b8-8068-da53f409b0c6</processItemId>
                <location>2</location>
                <script>tw.local.parentODCRequest.CustomerInfo = tw.local.customerInfo;&#xD;
tw.local.parentODCRequest.cif = tw.local.parentODCRequest.CustomerInfo.cif;&#xD;
tw.local.parentODCRequest.customerName =tw.local.parentODCRequest.CustomerInfo.customerName&#xD;
;&#xD;
tw.local.parentODCRequest.requestNature.value = tw.local.requestNature;&#xD;
tw.local.parentODCRequest.requestType.value = tw.local.requestType;</script>
                <guid>ce8bd02d-274b-4e67-a7c4-58a1f9c99453</guid>
                <versionId>a8578c90-1c8e-4816-bfba-01a48b764328</versionId>
            </processPrePosts>
            <layoutData x="1429" y="181">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error2</name>
                <locationId>bottomLeft</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d54</errorHandlerItem>
                <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b748a73e-a9c6-4dd8-a00b-7f269e137ccc</subProcessId>
                <attachedProcessRef>/1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676</attachedProcessRef>
                <guid>4806d1c5-bb5e-46f1-a9e2-f7e1811e0a5f</guid>
                <versionId>be0c2423-8604-496d-962d-c3ca191359cb</versionId>
                <parameterMapping name="ContractDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5894b5fb-1edc-4380-9f55-78009b0a8539</parameterMappingId>
                    <processParameterId>2055.9465f3bb-e80a-444e-8672-899423d668fb</processParameterId>
                    <parameterMappingParentId>3012.b748a73e-a9c6-4dd8-a00b-7f269e137ccc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentODCRequest</value>
                    <classRef>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>de0a919e-b7aa-44db-a5d8-44182d565100</guid>
                    <versionId>8d9011a2-b519-44e2-8d43-d26e23fab758</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="ContractDetails">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e7c4cd2b-c33b-4250-8943-0c70e4e73bea</parameterMappingId>
                    <processParameterId>2055.1447a720-69f1-46ca-8d27-88a12b035edf</processParameterId>
                    <parameterMappingParentId>3012.b748a73e-a9c6-4dd8-a00b-7f269e137ccc</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentODCRequest</value>
                    <classRef>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4d7b9982-a561-4da1-8163-7037b7d3e227</guid>
                    <versionId>a39c9bc4-d635-414d-b8f1-17d9f748ba9a</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f7340a62-ac61-41b9-80fa-4c57df997206</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Get Customer Accounts</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.503b6683-dca9-4958-873e-f125d1cecf00</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
            <guid>guid:d744cf1163db9fc8:62d78dda:18bce2c7ce1:-6a7f</guid>
            <versionId>32bf4d24-df70-4986-983f-fbdb6e9491a5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.b2bdcbe3-266c-4687-878a-8e8b4ce7ae20</processItemPrePostId>
                <processItemId>2025.f7340a62-ac61-41b9-80fa-4c57df997206</processItemId>
                <location>2</location>
                <script>if(!!tw.local.parentODCRequest.FinancialDetailsBR){&#xD;
&#xD;
  if(!tw.local.parentODCRequest.FcCollections)&#xD;
      tw.local.parentODCRequest.FcCollections = {};&#xD;
      &#xD;
  tw.local.parentODCRequest.FcCollections.listOfAccounts = [];&#xD;
	&#xD;
	for(var i=0;i&lt;tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts.length;i++)&#xD;
	{&#xD;
	     &#xD;
		tw.local.parentODCRequest.FcCollections.listOfAccounts[i] = {};&#xD;
		tw.local.parentODCRequest.FcCollections.listOfAccounts[i]= tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts[i];&#xD;
	}&#xD;
} &#xD;
</script>
                <guid>da6528bd-68c8-43da-8cee-76d859f0f43b</guid>
                <versionId>0eb4a7e3-c7f2-422a-b619-c937e217d121</versionId>
            </processPrePosts>
            <layoutData x="1273" y="181">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error5</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d54</errorHandlerItem>
                <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightTop" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.503b6683-dca9-4958-873e-f125d1cecf00</subProcessId>
                <attachedProcessRef>/1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f</attachedProcessRef>
                <guid>1100debc-f6c9-4d3f-8e23-76d27f0174c4</guid>
                <versionId>6ec8edb8-70f3-4827-b9a7-052c0b5044c4</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e54d0df0-b275-4e7d-8014-383c63483618</parameterMappingId>
                    <processParameterId>2055.208f75bb-611b-4393-83d6-d60470c4a6a4</processParameterId>
                    <parameterMappingParentId>3012.503b6683-dca9-4958-873e-f125d1cecf00</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>851ea853-774b-4690-922b-bf9939b5e4d9</guid>
                    <versionId>26936a05-53d6-4398-87e9-cec5e737a40b</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="data">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.4f703547-6349-4e89-92bb-11a8430c8f58</parameterMappingId>
                    <processParameterId>2055.0bc98ce8-10aa-460a-8857-8a8600dafc90</processParameterId>
                    <parameterMappingParentId>3012.503b6683-dca9-4958-873e-f125d1cecf00</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentODCRequest.CustomerInfo.cif</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>612e5ca3-7bc7-422b-a1bf-f35605db9d0f</guid>
                    <versionId>43859d91-9645-4aff-8539-6e18983f12c1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="collectionCurrency">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9ba67170-96ea-47ad-9427-c9423f2724ad</parameterMappingId>
                    <processParameterId>2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861</processParameterId>
                    <parameterMappingParentId>3012.503b6683-dca9-4958-873e-f125d1cecf00</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentODCRequest.FcCollections.currency.value</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>e863aad3-265e-4023-a49f-6fc027f70630</guid>
                    <versionId>5b216542-8422-4fee-8fe8-dce43905d892</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d058cc38-8b75-4314-961a-b5bf1d33d17b</parameterMappingId>
                    <processParameterId>2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074</processParameterId>
                    <parameterMappingParentId>3012.503b6683-dca9-4958-873e-f125d1cecf00</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>ccc1fa2d-3dbc-4071-bcf2-c1ecc2fc0a57</guid>
                    <versionId>7dc8359e-aeb5-4179-a5f0-ff16cc9e371e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d0887f00-2922-4183-b044-4131198b176b</parameterMappingId>
                    <processParameterId>2055.7fdee937-d555-46b6-88d2-46c40e165c27</processParameterId>
                    <parameterMappingParentId>3012.503b6683-dca9-4958-873e-f125d1cecf00</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMSG</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>024a889c-10b5-4c1d-bf40-903b2405d59e</guid>
                    <versionId>f0191b43-8895-44f1-a1c8-72957a41be8a</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.********-a136-4364-859c-8a4eda3d953d</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.79888a59-5853-48c4-8f17-74aa32b7e597</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:f07</guid>
            <versionId>45bd16fb-bc98-4958-80ac-ec9a5a2aa20b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="399" y="179">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.79888a59-5853-48c4-8f17-74aa32b7e597</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
//------------------------------------- Get ODC -------------------------------------------- &#xD;
var final = "" +tw.epv.RequestState.Final;&#xD;
var closed = "" + tw.epv.RequestState.Closed;&#xD;
var reversed = "" + tw.epv.RequestState.Reversed;&#xD;
var collected = ""+ tw.epv.RequestState.Collection;&#xD;
var amend = "" + tw.epv.RequestType.Amendment;&#xD;
var recreate = "" + tw.epv.RequestType.Recreate;&#xD;
var collection = "" + tw.epv.RequestType.Collection;&#xD;
var closure = "" + tw.epv.RequestType.Closure;&#xD;
var reversal = "" + tw.epv.RequestType.Reversal;&#xD;
var create = "" + tw.epv.RequestType.Create;&#xD;
var completed = "" + tw.epv.Status.completed; &#xD;
var approved = "" + tw.epv.Status.Approved;&#xD;
&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == amend || tw.local.parentODCRequest.requestType.value == recreate  ){&#xD;
  tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND (REQUESTTYPE = '"+amend+"' OR REQUESTTYPE = '"+recreate+"' OR REQUESTTYPE = '"+create+"') AND REQUESTSTATE = '"+final+"'  AND REQUESTSTATUS='"+completed+"')";&#xD;
  if(tw.local.parentODCRequest.requestType.value == amend ) &#xD;
    tw.local.sqlStatements[j].sql+= " AND OUTSTANDINGAMOUNT &gt; 0;";&#xD;
   tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
   paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
}&#xD;
&#xD;
else if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure || tw.local.parentODCRequest.requestType.value == reversal ){&#xD;
&#xD;
  &#xD;
  if(tw.local.parentODCRequest.requestType.value == collection){&#xD;
      tw.local.sqlStatements[j].sql="SELECT ID, liqAmount, liqCurrency, isLiquidated , "&#xD;
      +"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT"&#xD;
      +" FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '"+collection+"'   AND REQUESTSTATE = '"+collected+"' AND REQUESTSTATUS='"+completed+"')";&#xD;
      tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
      paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
  &#xD;
  }&#xD;
  else if (tw.local.parentODCRequest.requestType.value == closure){&#xD;
       tw.local.sqlStatements[j].sql="SELECT ID, CLOSUREREASON FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '"+closure+"'  AND REQUESTSTATE = '"+closed+"' AND REQUESTSTATUS='"+completed+"')";&#xD;
       tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
       paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
  &#xD;
  &#xD;
  }&#xD;
  else{&#xD;
       tw.local.sqlStatements[j].sql="SELECT ID, REVERSALREASON FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '"+reversal+"'  AND REQUESTSTATE = '"+reversed+"' AND (REQUESTSTATUS='"+completed+"' OR REQUESTSTATUS='"+approved+"' ))";&#xD;
       tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
       paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
  }&#xD;
  &#xD;
  j++;&#xD;
  i=0;&#xD;
  // get some of create data&#xD;
  tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
  tw.local.sqlStatements[j].sql="SELECT ID, EXPORTPURPOSE,PAYMENTTERMS,PRODUCTCATEGORY,COMMODITYDESCRIPTION,CIF,CUSTOMERNAME,IMPORTERNAME, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT,collectionCurrency, standardExRate, negotiatedExRate ,  COLLECTIONACCOUNT, CHARGESACCOUNT,  COUNTRYOFORIGIN,requestNo, requestNature , requestType , requestDate"&#xD;
   +" FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND (REQUESTTYPE = '"+amend+"' OR REQUESTTYPE = '"+recreate+"' OR REQUESTTYPE = '"+create+"') AND REQUESTSTATE = '"+final+"'  AND REQUESTSTATUS='"+completed+"' ) ";&#xD;
  &#xD;
  if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure ) &#xD;
      tw.local.sqlStatements[j].sql +=" AND OUTSTANDINGAMOUNT &gt; 0;";&#xD;
      tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
     paramInit(tw.local.parentODCRequest.parentRequestNo);   &#xD;
&#xD;
}&#xD;
&#xD;
&#xD;
else{}&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>cdd70b6a-5883-46b0-9b85-d7d03756def6</guid>
                <versionId>08842c9e-935f-4639-b7d9-701e11fab0a9</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.980ad00d-5fbb-423c-85ee-08e4caff0a41</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Check</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.b48fb1a4-dfcb-4e5b-bdf9-16ceb373d652</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:495</guid>
            <versionId>5785e38d-910f-41b9-96f7-cdc6768025ee</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="316" y="418">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.b48fb1a4-dfcb-4e5b-bdf9-16ceb373d652</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if (tw.local.results[0].rows.listLength == 0) {&#xD;
      if(tw.local.parentODCRequest.requestType.value == "amend" || tw.local.parentODCRequest.requestType.value == "closure" || tw.local.parentODCRequest.requestType.value == "collection"){&#xD;
	 tw.local.message = "Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 ";&#xD;
	}&#xD;
	else&#xD;
	 tw.local.message = "Request number does not exist or is in another running request";&#xD;
	tw.local.isFound = false;&#xD;
}&#xD;
&#xD;
else{&#xD;
&#xD;
      tw.local.isFound = true;&#xD;
      &#xD;
      if(tw.local.parentODCRequest.requestType.value == "recreate"){&#xD;
      &#xD;
      //There must be no collection done on ODC&#xD;
&#xD;
         for(var i =0 ; i &lt;tw.local.results[0].rows.listLength; i++){&#xD;
&#xD;
             if(tw.local.results[0].rows[i].data[3] == "ODC Collection"){&#xD;
                  tw.local.message = "There must be no collection done on ODC";&#xD;
                  tw.local.isFound = false;&#xD;
                  break;&#xD;
             }&#xD;
          }&#xD;
          if(tw.local.isFound == true ){&#xD;
          //get the latsest instance&#xD;
           tw.local.results[0].rows[0] = tw.local.results[0].rows[tw.local.results[0].rows.listLength-1];&#xD;
&#xD;
          }&#xD;
      }&#xD;
	&#xD;
}</script>
                <isRule>false</isRule>
                <guid>2a7faa9b-110f-4bb4-95ec-39037f6de574</guid>
                <versionId>ddce4061-d3e3-4ac7-b2ff-e164ef8497bf</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Catch Errors</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.cb7964c1-af1c-4934-836f-e723a76ff4b4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d54</guid>
            <versionId>641ebfbb-9298-4b30-b9be-2a1bab0ec3f6</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="660" y="375">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.cb7964c1-af1c-4934-836f-e723a76ff4b4</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>4ed0dd18-7859-44dc-9364-8f84d5cedc3a</guid>
                <versionId>1b464763-5653-4877-b277-56b95b4063bd</versionId>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.388b62eb-b99f-41f8-8f62-927a6bbd0870</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.cb7964c1-af1c-4934-836f-e723a76ff4b4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Get Request and customer data"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>0a2f5f02-7d90-4aec-8ab6-d4e03523c42c</guid>
                    <versionId>0cd91fe9-7b62-485f-b911-ab4fb91c44fd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ebf6d12f-0229-4a8e-97e9-54fd9bca4b68</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.cb7964c1-af1c-4934-836f-e723a76ff4b4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>7083bb99-336c-474f-89cb-3b22a0168379</guid>
                    <versionId>7d40b1b0-bf1a-4ec9-b3fc-377d7e7674c0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.70c33314-9b01-460e-b3db-91395f355666</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.cb7964c1-af1c-4934-836f-e723a76ff4b4</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e3c7fea0-4a82-47a4-8650-e577d5940d3f</guid>
                    <versionId>eb283902-6467-46e1-bc1d-7c5ad5be3d61</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Match?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.b2af0b10-9461-492f-9099-22c5bf76d833</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:20ce</guid>
            <versionId>81e11e25-0410-470a-b58c-f46e09cf572b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.743bb92b-04c5-4ead-970f-4ee5bc193d61</processItemPrePostId>
                <processItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</processItemId>
                <location>2</location>
                <script isNull="true" />
                <guid>80907bab-5e28-40c8-895c-6adee3fd53a3</guid>
                <versionId>1a5693e1-a3b7-47b0-8f81-0651c9bdd892</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.27d5d4e7-5c29-4c03-af41-4ed306cc6172</processItemPrePostId>
                <processItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>a4057504-4a00-4502-83ef-a7eed56ca725</guid>
                <versionId>63077474-f728-4dee-8c76-4de328e6765b</versionId>
            </processPrePosts>
            <layoutData x="1052" y="280">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.b2af0b10-9461-492f-9099-22c5bf76d833</switchId>
                <guid>0df98b95-b1e1-47c7-a098-b0ad70c999f8</guid>
                <versionId>6ba40126-8877-4581-af87-ecf7ba03951a</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.09ecbf0d-8405-4d0c-9d4d-3e649738be0d</switchConditionId>
                    <switchId>3013.b2af0b10-9461-492f-9099-22c5bf76d833</switchId>
                    <seq>1</seq>
                    <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:5464</endStateId>
                    <condition>tw.local.customerInfo.cif	  ==	  tw.local.parentODCRequest.CustomerInfo.cif</condition>
                    <guid>121391da-6418-4759-a8a1-792cfafa4ca5</guid>
                    <versionId>933b9d59-f313-4124-93c5-a15c1693b041</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9886a463-0e69-404d-8f95-f6f22c77f59b</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>SQL Execute Multiple Statements (SQLResult)</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.df9337ac-f085-43b5-8ecd-16cdfede534d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:47f</guid>
            <versionId>8d8463ed-7c1c-4a1c-a557-7bd2ee34df3b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="160" y="418">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.df9337ac-f085-43b5-8ecd-16cdfede534d</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>83fd02d5-7b5e-4d68-8d3e-2e21115b6b55</guid>
                <versionId>0c225a4b-feee-4ef8-b7f6-f3bb67068e24</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b323450c-e77c-472b-9e4b-159529b9ff5b</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.df9337ac-f085-43b5-8ecd-16cdfede534d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>4c7ac0a4-ceac-423a-974b-7b5f4e155e62</guid>
                    <versionId>0867c5b7-1209-4600-bd0a-d67c6e3bcf7a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.71d40e30-9d91-4da4-aaf2-39ac773addb8</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.df9337ac-f085-43b5-8ecd-16cdfede534d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>630975c8-ae44-4e43-8141-90c4779d3c66</guid>
                    <versionId>52a6cf52-a631-4f5e-96de-96b7c8c35a93</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.876fc8c6-6a3e-40d4-b4c4-cf596b316675</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.df9337ac-f085-43b5-8ecd-16cdfede534d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>612616fa-c45d-4bd4-80ff-e25457c4f5cd</guid>
                    <versionId>7a5dc259-df1d-494f-ab8d-c85dda7fa9ce</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3f05b5f1-d684-4d58-8da6-58a8ed39350c</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>SQL Execute Check</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.eba53119-36be-41e3-8ba9-0e11b7400719</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:a8</guid>
            <versionId>b14c8e3b-2bdd-45a8-8635-15f7a8b0d26a</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.18dc1c2a-8f39-48e9-b8b5-4b29fd4528ed</processItemPrePostId>
                <processItemId>2025.3f05b5f1-d684-4d58-8da6-58a8ed39350c</processItemId>
                <location>2</location>
                <script>tw.local.isFound = true;&#xD;
&#xD;
if (tw.local.results[0].rows.listLength == 0) {&#xD;
    &#xD;
	 tw.local.message = "Request number does not exist or is in another running request ";&#xD;
	 tw.local.isFound = false;&#xD;
	 &#xD;
	}&#xD;
  &#xD;
if(!!tw.local.results[1]){&#xD;
  if (tw.local.results[1].rows.listLength &gt;0){   &#xD;
  	 tw.local.message = "Request number is in another running request ";&#xD;
	 tw.local.isFound = false;&#xD;
  } &#xD;
}  &#xD;
//recreate	&#xD;
if(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Recreate){&#xD;
 if(!!tw.local.results[2]){&#xD;
  if (tw.local.results[2].rows.listLength &gt; 0){&#xD;
   &#xD;
     tw.local.message = "There must be no collection done on ODC"&#xD;
     tw.local.isFound = false;&#xD;
   }&#xD;
 } &#xD;
&#xD;
}   </script>
                <guid>0bac2f55-2b73-4226-bae7-230ed0560bce</guid>
                <versionId>6c75e718-2bf6-48b3-8a79-109e01d6d6d3</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.b7184fef-fb05-4c5e-bde6-ab2b6d5441b3</processItemPrePostId>
                <processItemId>2025.3f05b5f1-d684-4d58-8da6-58a8ed39350c</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>cce7a2dd-01a2-4140-a56b-00470243329e</guid>
                <versionId>71cd045f-ea5f-4245-99ca-ee562980ed64</versionId>
            </processPrePosts>
            <layoutData x="202" y="180">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Copy of Error1</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d54</errorHandlerItem>
                <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.eba53119-36be-41e3-8ba9-0e11b7400719</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>58a07df9-2966-498b-a491-734c38120ae1</guid>
                <versionId>2838e720-7d07-4ce3-acf8-8a41dfa1fb12</versionId>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b6229677-c1c4-4d7f-90dd-e539c7ce6855</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.eba53119-36be-41e3-8ba9-0e11b7400719</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>009269bc-e960-477a-ac62-f02cff1659d5</guid>
                    <versionId>07cf0530-5ab8-464c-82a1-cd0604f182d1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2491f080-8f4d-406e-b45d-7f2a62dd7458</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.eba53119-36be-41e3-8ba9-0e11b7400719</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>8cc54278-b8dc-4ec6-95e9-2c58427fcf6a</guid>
                    <versionId>5f2d8ec6-4bd0-4379-b0a5-a24a61c86f2c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6c25ecf9-c131-4542-b6a5-89929361f8f3</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.eba53119-36be-41e3-8ba9-0e11b7400719</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>758aeda8-eeac-4dd9-b568-fdfe9b1ce4c5</guid>
                    <versionId>b9147010-acdf-4232-9c91-a3cb21f12a3c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8ae854db-345a-4c0c-80db-df3c8592f922</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Init SQL for sub</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.8d1388ea-824c-4d58-962d-a8f7567754f1</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:4ae</guid>
            <versionId>b94a4841-7fc8-4cec-9a40-9c45e9b39659</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="764" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error3</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d54</errorHandlerItem>
                <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.8d1388ea-824c-4d58-962d-a8f7567754f1</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
&#xD;
//------------------------------------- Get invoice --------------------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql="SELECT  INVOICENO, INVOICEDATE FROM ARCHUSR.ODC_INVOICE WHERE REQUESRID = ?;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
if(!!tw.local.results[0].rows[0])&#xD;
   paramInit(tw.local.results[0].rows[0].data[0]);&#xD;
else if(!!tw.local.results[1].rows[0])   &#xD;
   paramInit(tw.local.results[1].rows[0].data[0]);&#xD;
//------------------------------------- Get bills --------------------------------------------&#xD;
j++;&#xD;
i=0;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql="SELECT BILLOFLADINGREF, BILLOFLADINGDATE FROM ARCHUSR.ODC_BILLS WHERE REQUESRID = ?;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
if(!!tw.local.results[0].rows[0])&#xD;
   paramInit(tw.local.results[0].rows[0].data[0]);&#xD;
else if(!!tw.local.results[1].rows[0])   &#xD;
   paramInit(tw.local.results[1].rows[0].data[0]);&#xD;
&#xD;
&#xD;
//------------------------------------- Get multi tenor --------------------------------------------&#xD;
j++;&#xD;
i=0;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql="SELECT INSTALLMENTDATE, INSTALLMENTAMOUNT FROM ARCHUSR.ODC_MULTITENORDATES WHERE REQUESRID = ?;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
if(!!tw.local.results[0].rows[0])&#xD;
   paramInit(tw.local.results[0].rows[0].data[0]);&#xD;
else if(!!tw.local.results[1].rows[0])   &#xD;
   paramInit(tw.local.results[1].rows[0].data[0]);&#xD;
</script>
                <isRule>false</isRule>
                <guid>984160b9-d143-4ee3-b7f3-1fd7e0700040</guid>
                <versionId>56136164-f239-4955-860f-351a6878d12f</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>exist?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.76c66461-b0a7-4f30-8537-18807fd47ffe</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:188</guid>
            <versionId>c063e294-e55c-429a-9e1b-73cc0d1e20f7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="311" y="198">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.76c66461-b0a7-4f30-8537-18807fd47ffe</switchId>
                <guid>60aeb995-d21d-4b30-b522-0a4ad0409b6f</guid>
                <versionId>e66f4305-a346-4610-a8fc-9003bc92ef1b</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.5e5712f2-115f-425d-aa28-8f007169a56d</switchConditionId>
                    <switchId>3013.76c66461-b0a7-4f30-8537-18807fd47ffe</switchId>
                    <seq>1</seq>
                    <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:5463</endStateId>
                    <condition>tw.local.isFound	  ==	  false</condition>
                    <guid>d91c2a9a-64ec-48e6-9e0b-e55ad51a5746</guid>
                    <versionId>4303e36d-1665-4b9c-b135-b855044832f4</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.43955c42-94c2-481f-84b0-40a75607048c</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>validation script</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.13ba63da-b3cc-4477-a73e-ee1aceb6b859</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:2348</guid>
            <versionId>c20dec9d-a179-4f76-989c-6ec8eced18a9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#95D087</nodeColor>
            <layoutData x="1145" y="292">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.13ba63da-b3cc-4477-a73e-ee1aceb6b859</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.errorMSG = "This CIF Number Not related to Parent Request Number";&#xD;
tw.local.match = false;&#xD;
&#xD;
&#xD;
</script>
                <isRule>false</isRule>
                <guid>37ad21da-5d7d-410d-973a-a5a95c175160</guid>
                <versionId>c6556ba6-3c46-4fb6-828c-6860a303eaff</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.cc174e9a-35da-4e42-82c8-c8b6aee07473</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.9518d26a-f57c-405d-933a-230fe656debd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d5a</guid>
            <versionId>c26e3e38-7109-4e0d-bcb4-c8d2c63e4299</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="791" y="441">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.9518d26a-f57c-405d-933a-230fe656debd</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>0bf3eee5-8afe-4a89-9f66-3f31fc6d5a7d</guid>
                <versionId>24687b64-1d2d-4fc1-99a9-73964fb4cd18</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.071a5079-3b93-4dce-85e6-bd26b6cfb652</parameterMappingId>
                    <processParameterId>2055.17de011c-4e97-4bf5-aac8-978f3dd6ae71</processParameterId>
                    <parameterMappingParentId>3007.9518d26a-f57c-405d-933a-230fe656debd</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>37dcc1e1-987e-4208-9f4a-ac56aa7aa2b2</guid>
                    <versionId>fb5df830-b82a-4715-a4c2-2feab2d9c6c5</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>found?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.da7b6994-ae7c-4275-873e-66c6c38ee399</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:4af</guid>
            <versionId>cc260673-ad3b-465d-824d-5c5de9b81ae9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="637" y="201">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.da7b6994-ae7c-4275-873e-66c6c38ee399</switchId>
                <guid>2ac6fd49-5c18-4a2e-9ffb-c46893f0d90e</guid>
                <versionId>74021ab4-2422-4e26-a105-818a5b029f70</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.cbe7de4f-dc01-45c7-bac9-d877a6267f8e</switchConditionId>
                    <switchId>3013.da7b6994-ae7c-4275-873e-66c6c38ee399</switchId>
                    <seq>1</seq>
                    <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:5462</endStateId>
                    <condition>tw.local.isFound	  ==	  false</condition>
                    <guid>772df06d-8ebc-497a-852f-8d83efb621b0</guid>
                    <versionId>a9702689-e121-4ffc-b1bb-10dccf081663</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.4f0fe545-64ed-45fa-85ee-22fc6e427cbe</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Get Customer Data</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.4079db67-d171-4247-a3de-69ac05e5d151</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
            <guid>guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-2f97</guid>
            <versionId>d08178b3-9f46-4e70-8df7-ac830c8a234f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.6fc63dd8-3dcb-40e0-ae79-d4e0b414d8a2</processItemPrePostId>
                <processItemId>2025.4f0fe545-64ed-45fa-85ee-22fc6e427cbe</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>5b556e71-8bd6-4d05-84bd-8884e1550358</guid>
                <versionId>17580e7c-f357-42bd-923c-37b0b8d90bd1</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.30dd3c46-982a-4556-826f-c7060d3abf82</processItemPrePostId>
                <processItemId>2025.4f0fe545-64ed-45fa-85ee-22fc6e427cbe</processItemId>
                <location>2</location>
                <script>tw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;</script>
                <guid>c8e13995-ac34-49a4-82f2-016a7885480c</guid>
                <versionId>8981ae49-2ebd-4443-8c1d-9bfeae4daff1</versionId>
            </processPrePosts>
            <layoutData x="1143" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomLeft</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:056f27db08707381:36b20ea0:18b7be1ec94:2d54</errorHandlerItem>
                <errorHandlerItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.4079db67-d171-4247-a3de-69ac05e5d151</subProcessId>
                <attachedProcessRef>/1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb</attachedProcessRef>
                <guid>57ecd496-a513-434e-a98e-d4ec0294d2ed</guid>
                <versionId>e1ede77d-a84f-4c41-ab25-b443d20de1a6</versionId>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.019de47b-99f9-40d5-8e33-55d0424a3abc</parameterMappingId>
                    <processParameterId>2055.7ba5060f-0a87-4db5-8405-e30a1ccf8dc4</processParameterId>
                    <parameterMappingParentId>3012.4079db67-d171-4247-a3de-69ac05e5d151</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>123a3575-a686-4223-8bd5-7b4bc2369cec</guid>
                    <versionId>347b25cd-1073-4cd3-aa21-507a6c87db63</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="data">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c6695b57-e1f9-4f3e-82bb-6e781682626c</parameterMappingId>
                    <processParameterId>2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed</processParameterId>
                    <parameterMappingParentId>3012.4079db67-d171-4247-a3de-69ac05e5d151</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentODCRequest.CustomerInfo.cif</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>af21cec9-3bbd-4bfa-8c4c-89430659b945</guid>
                    <versionId>437be413-a2be-4344-9c55-5ef5229bbab3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8ad52630-f7d6-4279-8228-ef8790b36e85</parameterMappingId>
                    <processParameterId>2055.373a4a6c-181b-43d5-8f0e-8b400049f72e</processParameterId>
                    <parameterMappingParentId>3012.4079db67-d171-4247-a3de-69ac05e5d151</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentODCRequest.CustomerInfo</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d6258ed3-a6a9-49e4-870b-f2fb75c0d7a8</guid>
                    <versionId>4a92d64e-2a61-48b9-abc1-4341cb385092</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f4c6489d-c98b-4d0c-8c14-f06f37b4dea3</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Mapping</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.a63f5933-b970-4517-97a4-1feb5157fc17</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:056f27db08707381:36b20ea0:18b6c1db471:566</guid>
            <versionId>dcd2277e-032b-4dec-9d88-9f50c03658ac</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="1005" y="182">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.a63f5933-b970-4517-97a4-1feb5157fc17</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>var i=0-1;&#xD;
var j =0-1;&#xD;
function getnext () {&#xD;
	i++;&#xD;
	var results =null;&#xD;
	if(!!tw.local.results[0]){&#xD;
	 if(!!tw.local.results[0].rows[0])&#xD;
	    results = tw.local.results[0].rows[0].data[i];&#xD;
	 }   &#xD;
	else&#xD;
	    results = null;&#xD;
	return results;&#xD;
}&#xD;
function getCreateData () {&#xD;
      j++;&#xD;
	var results = "";&#xD;
	if(!!tw.local.results[1].rows[0])&#xD;
	    results = tw.local.results[1].rows[0].data[j];&#xD;
	else&#xD;
	    results = null&#xD;
	return results;&#xD;
}&#xD;
var amend = "" + tw.epv.RequestType.Amendment;&#xD;
var recreate = "" + tw.epv.RequestType.Recreate;&#xD;
var collection = "" + tw.epv.RequestType.Collection;&#xD;
var closure = "" + tw.epv.RequestType.Closure;&#xD;
var reversal = "" + tw.epv.RequestType.Reversal;&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == amend || tw.local.parentODCRequest.requestType.value == recreate  ){&#xD;
getnext ();&#xD;
&#xD;
tw.local.parentODCRequest = new tw.object.ODCRequest();&#xD;
tw.local.parentODCRequest.requestNo = getnext ();&#xD;
tw.local.parentODCRequest.requestNature = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.requestNature.name = tw.local.requestNature;&#xD;
tw.local.parentODCRequest.BasicDetails = new tw.object.BasicDetails();&#xD;
tw.local.parentODCRequest.BasicDetails.requestNature = getnext ();&#xD;
getnext ();&#xD;
tw.local.parentODCRequest.requestType  = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.requestType.name =tw.local.requestType;&#xD;
tw.local.parentODCRequest.requestType.value =tw.local.requestType;&#xD;
tw.local.parentODCRequest.requestDate = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.requestState = getnext ();&#xD;
//tw.local.parentODCRequest.appInfo = new tw.object.AppInfo();&#xD;
//tw.local.parentODCRequest.appInfo.status = getnext ();&#xD;
getnext ();&#xD;
tw.local.parentODCRequest.parentRequestNo = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.flexCubeContractNo = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.contractStage = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.exportPurpose = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.BasicDetails.exportPurpose.name =getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.paymentTerms = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.BasicDetails.paymentTerms.name = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.productCategory  = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.BasicDetails.productCategory.name = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.commodityDescription = getnext ();&#xD;
&#xD;
tw.local.parentODCRequest.isLiquidated = getnext ();&#xD;
i--;&#xD;
tw.local.parentODCRequest.CustomerInfo = new tw.object.CustomerInfo()&#xD;
tw.local.parentODCRequest.CustomerInfo.cif= getnext ();&#xD;
tw.local.parentODCRequest.CustomerInfo.customerName= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation = new tw.object.ContractCreation();&#xD;
tw.local.parentODCRequest.ContractCreation.baseDate= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.valueDate= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.tenorDays= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.transitDays= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.maturityDate= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.userReference= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.productCode = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ContractCreation.productCode.value= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.productDescription= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.Stage= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.sourceReference= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.referenceNo= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.discount= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.extraCharges= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.ourCharges= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.amountSight= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.amountDefNoAvalization= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.amountDefAvalization= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.collectableAmount= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.outstandingAmount= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.maturityDate= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.noOfDaysMaturity= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.financeApprovalNo= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.executionHub = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.executionHub.value= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.executionHub.name= getnext (); &#xD;
tw.local.parentODCRequest.ContractLiquidation = new tw.object.ContractLiquidation();&#xD;
tw.local.parentODCRequest.ContractLiquidation.liqAmount =  getnext (); &#xD;
tw.local.parentODCRequest.ContractLiquidation.liqCurrency = getnext (); &#xD;
tw.local.parentODCRequest.FcCollections = new tw.object.FCCollections();&#xD;
tw.local.parentODCRequest.FcCollections.currency =  new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FcCollections.currency.name= getnext (); &#xD;
tw.local.parentODCRequest.FcCollections.standardExchangeRate= getnext (); &#xD;
tw.local.parentODCRequest.FcCollections.negotiatedExchangeRate= getnext ();&#xD;
tw.local.parentODCRequest.FcCollections.calculatedAmount = getnext ();&#xD;
tw.local.parentODCRequest.FcCollections.totalAllocatedAmount = getnext ();&#xD;
getnext ();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.CBECommodityClassification = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.CBECommodityClassification.name= getnext ();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.shipmentMethod = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.shipmentMethod.name= getnext ();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.shippingDate= getnext ();&#xD;
tw.local.parentODCRequest.ReversalReason = new tw.object.ReversalReason();&#xD;
tw.local.parentODCRequest.ReversalReason.reversalReason =  getnext ();&#xD;
tw.local.parentODCRequest.ReversalReason.closureReason =  getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails = new tw.object.ImporterDetails();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerName= getnext ();&#xD;
tw.local.parentODCRequest.ImporterName = tw.local.parentODCRequest.ImporterDetails.importerName;&#xD;
tw.local.parentODCRequest.ImporterDetails.importerCountry = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerCountry.value= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerAddress= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerPhoneNo= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bank= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.BICCode= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.ibanAccount= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankCountry = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankCountry.value= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankAddress= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankPhoneNo= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.collectingBankReference= getnext (); &#xD;
getnext ();&#xD;
getnext ();&#xD;
getnext ();&#xD;
//tw.local.parentODCRequest.appInfo.stepName= getnext ();&#xD;
//tw.local.parentODCRequest.appInfo.subStatus= getnext ();  &#xD;
//tw.local.parentODCRequest.appInfo.instanceID = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.currency = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.currency.name = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.maxCollectionDate = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.documentAmount = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.amountAdvanced = getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.NameValuePair(); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.collectionAccount = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.collectionAccount.value = getnext (); &#xD;
getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.CountryOfOrigin =  {};&#xD;
tw.local.parentODCRequest.BasicDetails.CountryOfOrigin.name = getnext (); &#xD;
}&#xD;
&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure || tw.local.parentODCRequest.requestType.value == reversal ){&#xD;
 if(!!tw.local.results[0]){&#xD;
  if(!!tw.local.results[0].rows[0]){&#xD;
   if(tw.local.parentODCRequest.requestType.value == collection ){&#xD;
       getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.liqAmount = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.liqCurrency = getnext ();&#xD;
       tw.local.parentODCRequest.isLiquidated = getnext ();&#xD;
       getnext ();&#xD;
       getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.debitValueDate = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditValueDate = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.debitedAccountNo = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.accountClass = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.accountClass.name = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.branchCode= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.balanceSign= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.currency =  {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.currency.name= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.balance= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.standardExRate= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.negotiatedExRate= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.amountInAccount= getnext ();&#xD;
       tw.local.parentODCRequest.FinancialDetailsFO.outstandingAmount= getnext ();&#xD;
       &#xD;
  }&#xD;
 else if( tw.local.parentODCRequest.requestType.value == closure){&#xD;
     getnext ();&#xD;
     tw.local.parentODCRequest.ReversalReason.closureReason = getnext ();&#xD;
   }&#xD;
  else{&#xD;
     getnext ();&#xD;
     tw.local.parentODCRequest.ReversalReason.reversalReason = getnext ();&#xD;
    }&#xD;
 }&#xD;
}&#xD;
&#xD;
  getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.exportPurpose.name = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.paymentTerms.name = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.productCategory.name = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.commodityDescription= getCreateData();&#xD;
  tw.local.parentODCRequest.cif= getCreateData();&#xD;
  tw.local.parentODCRequest.customerName= getCreateData();&#xD;
  tw.local.parentODCRequest.CustomerInfo.cif = tw.local.parentODCRequest.cif;&#xD;
  tw.local.parentODCRequest.CustomerInfo.customerName =  tw.local.parentODCRequest.customerName;&#xD;
  tw.local.parentODCRequest.ImporterName = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.maxCollectionDate = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.currency.name = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.documentAmount = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.amountAdvanced = getCreateData();&#xD;
  tw.local.parentODCRequest.FcCollections.currency.name = getCreateData();&#xD;
  tw.local.parentODCRequest.FcCollections.standardExchangeRate = getCreateData();&#xD;
  tw.local.parentODCRequest.FcCollections.negotiatedExchangeRate = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.collectionAccount.value = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.CountryOfOrigin =  {};&#xD;
  tw.local.parentODCRequest.BasicDetails.CountryOfOrigin.name =  getCreateData(); &#xD;
  tw.local.parentODCRequest.requestNo = getCreateData ();&#xD;
  tw.local.parentODCRequest.requestNature = new tw.object.NameValuePair();&#xD;
  tw.local.parentODCRequest.requestNature.name = tw.local.requestNature;&#xD;
  tw.local.parentODCRequest.requestType  = new tw.object.NameValuePair();&#xD;
  tw.local.parentODCRequest.requestType.name =tw.local.requestType;&#xD;
  tw.local.parentODCRequest.requestType.value =tw.local.requestType;&#xD;
  getCreateData();&#xD;
  getCreateData();&#xD;
  tw.local.parentODCRequest.requestDate = getCreateData();&#xD;
&#xD;
}  &#xD;
tw.local.parentODCRequest.appInfo = tw.local.appInfo;&#xD;
&#xD;
if(!!tw.local.subResult){&#xD;
//------------------------------------- invoice ---------------------------------------&#xD;
tw.local.parentODCRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
for (var i=0; i&lt;tw.local.subResult[0].rows.listLength; i++) {&#xD;
	tw.local.parentODCRequest.BasicDetails.Invoice[i] = new tw.object.Invoice();&#xD;
	tw.local.parentODCRequest.BasicDetails.Invoice[i].invoiceNo = tw.local.subResult[0].rows[i].data[0];&#xD;
	tw.local.parentODCRequest.BasicDetails.Invoice[i].invoiceDate = tw.local.subResult[0].rows[i].data[1];&#xD;
}&#xD;
//------------------------------------- bill ---------------------------------------&#xD;
tw.local.parentODCRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
for (var i=0; i&lt;tw.local.subResult[1].rows.listLength; i++) {&#xD;
	tw.local.parentODCRequest.BasicDetails.Bills[i] = new tw.object.Bills();&#xD;
	tw.local.parentODCRequest.BasicDetails.Bills[i].billOfLadingRef = tw.local.subResult[1].rows[i].data[0];&#xD;
	tw.local.parentODCRequest.BasicDetails.Bills[i].billOfLadingDate = tw.local.subResult[1].rows[i].data[1];&#xD;
}&#xD;
&#xD;
//------------------------------------- Multi tenor ---------------------------------------&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
for (var i=0; i&lt;tw.local.subResult[1].rows.listLength; i++) {&#xD;
	tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();&#xD;
	tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i].amount = tw.local.subResult[1].rows[i].data[0];&#xD;
	tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i].date = tw.local.subResult[1].rows[i].data[1];&#xD;
}&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>279d3a72-b221-423e-9871-155a1d8d7b07</guid>
                <versionId>b276bbf8-eaed-42bc-9281-ef52ca304a1c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.b6425fe1-912b-453f-8b9c-a039277a451b</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.c17feff2-38a6-4ec3-8629-f0f281efe2ef</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:317c</guid>
            <versionId>eaff2bcd-9189-4aec-bd7e-8c9d4efae511</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.5f24e8d6-2354-4568-b683-688f266025bc</processItemPrePostId>
                <processItemId>2025.b6425fe1-912b-453f-8b9c-a039277a451b</processItemId>
                <location>1</location>
                <script>tw.local.requestType = tw.local.parentODCRequest.requestType.value;&#xD;
tw.local.requestNature = ""+ tw.epv.RequestNature.UpdateRequest;&#xD;
tw.local.appInfo = tw.local.parentODCRequest.appInfo;&#xD;
tw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;&#xD;
</script>
                <guid>922d140b-fad0-4ffd-ae30-f6ebd5f8fe4a</guid>
                <versionId>66f3702f-c8a2-4bde-bdc1-781a2b57255c</versionId>
            </processPrePosts>
            <layoutData x="29" y="418">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.c17feff2-38a6-4ec3-8629-f0f281efe2ef</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
//------------------------------------- Get ODC -------------------------------------------- &#xD;
var final = "" +tw.epv.RequestState.Final;&#xD;
var closed = "" + tw.epv.RequestState.Closed;&#xD;
var reversed = "" + tw.epv.RequestState.Reversed;&#xD;
var collected = ""+ tw.epv.RequestState.Collection;&#xD;
&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == "amend" || tw.local.parentODCRequest.requestType.value == "closure" || tw.local.parentODCRequest.requestType.value == "collection"){&#xD;
 tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '"+final+"' OR REQUESTSTATE = '"+collected+"' OR REQUESTSTATE = '"+closed+"' OR REQUESTSTATE = '"+reversed+"'  AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) != UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled') )  AND OUTSTANDINGAMOUNT &gt; 0; ";&#xD;
&#xD;
}&#xD;
else if (tw.local.parentODCRequest.requestType.value == "recreate"){&#xD;
 tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '"+final+"' AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND   PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) !=  UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled') ORDER BY ID ASC";&#xD;
&#xD;
}&#xD;
&#xD;
else{&#xD;
 tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '"+final+"' AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) != UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled' ))";&#xD;
&#xD;
}&#xD;
&#xD;
 tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
 paramInit(tw.local.requestNumber);</script>
                <isRule>false</isRule>
                <guid>138b5fa2-e301-42e9-99f8-39c540cb38e0</guid>
                <versionId>87a6924d-3853-4835-94d5-cc55e9ab0156</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>Check Parent Request is Completed SQL</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.709c7ea2-bc25-4eea-808b-525b7428d531</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:53</guid>
            <versionId>f3c48193-fcc0-4e00-a0ff-e7936dbd6f65</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.*************-4c88-862b-35b6d1590e92</processItemPrePostId>
                <processItemId>2025.29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc</processItemId>
                <location>1</location>
                <script>tw.local.requestType = tw.local.parentODCRequest.requestType.value;&#xD;
tw.local.requestNature = ""+ tw.epv.RequestNature.UpdateRequest;&#xD;
tw.local.parentODCRequest.parentRequestNo = tw.local.requestNumber;&#xD;
tw.local.appInfo = tw.local.parentODCRequest.appInfo;&#xD;
tw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;&#xD;
tw.local.match = true;&#xD;
tw.local.errorMSG ="";</script>
                <guid>c2efd0f0-680a-4bec-a39b-d85fc76dc27b</guid>
                <versionId>08c60ed8-3cf3-47fa-9785-165bb65fe3f6</versionId>
            </processPrePosts>
            <layoutData x="73" y="180">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.709c7ea2-bc25-4eea-808b-525b7428d531</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
&#xD;
var final = "" +tw.epv.RequestState.Final;&#xD;
var collected = ""+ tw.epv.RequestState.Collection;&#xD;
var amend = "" + tw.epv.RequestType.Amendment;&#xD;
var recreate = "" + tw.epv.RequestType.Recreate;&#xD;
var collection = "" + tw.epv.RequestType.Collection;&#xD;
var completed = "" + tw.epv.Status.completed;  &#xD;
var terminated = "" + tw.epv.Status.terminated;&#xD;
var cancelled = "" + tw.epv.Status.Canceled;&#xD;
&#xD;
//check parent request is completed&#xD;
tw.local.sqlStatements[j] = {};&#xD;
tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTNO = ? AND REQUESTSTATE = '"+final+"' AND REQUESTSTATUS = '"+completed+"';";&#xD;
&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
&#xD;
//check no amend/recreate/collecion running&#xD;
j++;&#xD;
i=0;&#xD;
tw.local.sqlStatements[j] = {};&#xD;
tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND (REQUESTTYPE = '"+amend+"' OR REQUESTTYPE = '"+recreate+"' OR REQUESTTYPE = '"+collection+"') AND (REQUESTSTATUS != '"+completed+"' AND REQUESTSTATUS != '"+terminated+"' AND REQUESTSTATUS != '"+cancelled+"');";&#xD;
&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
&#xD;
//check no collection done in case of recreate&#xD;
j++;&#xD;
i=0;&#xD;
if(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Recreate){&#xD;
   tw.local.sqlStatements[j] = {};&#xD;
   tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND REQUESTSTATE = '"+collected+"' AND REQUESTSTATUS = '"+completed+"';";&#xD;
&#xD;
   tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
   paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
}</script>
                <isRule>false</isRule>
                <guid>12701ac6-3a79-4ca5-9a8f-04f06f21ec4a</guid>
                <versionId>be31d5ff-c447-461e-b549-f982bb802fc1</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3c8dc3f1-8259-4f0b-8f25-5083a268e5be</processItemId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <name>SQL Execute</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.e897c448-d920-4c39-a12c-656a1c96caa8</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:3fdc77012b8c8cc6:-28592d9:18ba55f702e:186</guid>
            <versionId>fb441970-c00b-4a8f-a7a4-e1b6d49bd170</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.1dbfa080-6c55-4732-9266-615a0ab7047e</processItemPrePostId>
                <processItemId>2025.3c8dc3f1-8259-4f0b-8f25-5083a268e5be</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>fab6d79e-63c9-4529-ac7d-6df03154ec66</guid>
                <versionId>3cb8bcff-cb2f-4da6-9647-ecb8b7192a11</versionId>
            </processPrePosts>
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.564b4282-95bf-4821-9c8b-2d986b58723b</processItemPrePostId>
                <processItemId>2025.3c8dc3f1-8259-4f0b-8f25-5083a268e5be</processItemId>
                <location>2</location>
                <script>if (tw.local.results[0].rows.listLength == 0) {&#xD;
      if(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Amendment|| tw.local.parentODCRequest.requestType.value ==  tw.epv.RequestType.Closure || tw.local.parentODCRequest.requestType.value ==  tw.epv.RequestType.Collection){&#xD;
	 tw.local.message = "Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 ";&#xD;
	}&#xD;
	else&#xD;
	 tw.local.message = "Request number does not exist or is in another running request";&#xD;
	 tw.local.isFound = false;&#xD;
	&#xD;
	if(!!tw.local.results[1]){&#xD;
	if(!!tw.local.results[1].rows){&#xD;
	   if (tw.local.results[1].rows.listLength == 0){&#xD;
            tw.local.message = "Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 ";&#xD;
	      tw.local.isFound = false;&#xD;
&#xD;
          }&#xD;
          else&#xD;
              tw.local.isFound = true;&#xD;
        &#xD;
	   }&#xD;
	 }&#xD;
}&#xD;
&#xD;
&#xD;
</script>
                <guid>84e90b3b-31fe-4f80-968f-348feb987631</guid>
                <versionId>c67b4573-71fb-439b-a706-88e598f382aa</versionId>
            </processPrePosts>
            <layoutData x="516" y="179">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.e897c448-d920-4c39-a12c-656a1c96caa8</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>465ec5b5-a8a8-43f5-a987-de7ee03dcd09</guid>
                <versionId>674d4ee2-97c6-4c05-b8fd-1254486bdf4d</versionId>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ee4dc1c3-14c1-4d17-b5b8-d2b30373775d</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.e897c448-d920-4c39-a12c-656a1c96caa8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a1f88869-7466-4552-bed0-ad2e884da9fe</guid>
                    <versionId>326a5722-032a-4da0-b4c4-327361b7cb0f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.3ddeb1e4-c460-4651-9432-162d92a7f35c</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.e897c448-d920-4c39-a12c-656a1c96caa8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>28ffc69a-c132-4064-9cd9-03896f1e99d4</guid>
                    <versionId>47ddaecf-d17a-4b73-8694-539c8534d98e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.2afacca8-6186-4fad-a05e-e033ed6ecb5d</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.e897c448-d920-4c39-a12c-656a1c96caa8</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlStatements</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>d4638aa6-3e58-4ed0-a14b-d55b1528c35f</guid>
                    <versionId>e41eacf3-ba8b-4569-9879-a031988677f8</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f0cc25c8-83f5-4ff0-9419-bcd836e7d3cd</epvProcessLinkId>
            <epvId>/21.062854b5-6513-4da8-84ab-0126f90e550d</epvId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <guid>b92d4d94-4c5a-4a6c-b72b-af1707599960</guid>
            <versionId>22b9987a-fd66-4562-9152-245d705491e9</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.ae3dbd8d-e0a8-47ce-9a56-0e951425b119</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <guid>58af79d4-f382-4c45-b103-f0889835ca1e</guid>
            <versionId>4c25c288-7a2a-4473-86e7-d408028da808</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.4703a6c3-a647-4fdc-a289-bfa1b303b26b</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <guid>f940cf4f-9216-4cda-b95d-b39d6a34bd0f</guid>
            <versionId>7b7abc3c-d0f2-40c6-bbcc-2e4d39d8a14c</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.7e8f0db7-7356-42fd-b92c-a0302ba07ffc</epvProcessLinkId>
            <epvId>/21.340b122c-2fdf-400c-822c-b0c52fb7b022</epvId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <guid>ca4cbb00-ef90-40ab-9f7d-f8260db37e93</guid>
            <versionId>f1b66638-cfc4-430d-aee3-64b44c999f2c</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="6" y="203">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Request and Customer Data" id="1.58febcb0-50a3-4963-8368-32c96c00d116" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.062854b5-6513-4da8-84ab-0126f90e550d" epvProcessLinkId="ef953f1a-e83a-4d3f-8172-6a0d6ee4550f" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="61c694b5-4fc3-40d6-89dc-f78f607c058c" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="42bbcb93-d82a-45c0-8ff4-9bca773aa7a1" />
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.340b122c-2fdf-400c-822c-b0c52fb7b022" epvProcessLinkId="5dcc648e-af88-476d-8751-442a7b9a0866" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="requestNumber" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.67ed8128-22da-46f1-894a-ab580c23f58f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"00104230000228"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="parentODCRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.d9a8520f-f8b4-408f-8cab-eafb8f688056">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "amend";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "00104230000228";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "599IAVC222440002";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="message" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b1f93375-04e2-4a6a-8e23-a158a2320eb6" />
                        
                        
                        <ns16:dataOutput name="isFound" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.f7fc5ea8-49a6-40b1-8b25-250a8fdd25fa" />
                        
                        
                        <ns16:dataOutput name="parentODCRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.6448783e-44be-4081-88f8-e6d08fe0c107" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.58299858-8daf-4093-89ff-ec58ea61013c" />
                        
                        
                        <ns16:dataOutput name="match" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.013d14bf-4f05-44e3-89dd-360f5ad5ceb8" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.67ed8128-22da-46f1-894a-ab580c23f58f</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.d9a8520f-f8b4-408f-8cab-eafb8f688056</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.b1f93375-04e2-4a6a-8e23-a158a2320eb6</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.f7fc5ea8-49a6-40b1-8b25-250a8fdd25fa</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.6448783e-44be-4081-88f8-e6d08fe0c107</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.58299858-8daf-4093-89ff-ec58ea61013c</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.013d14bf-4f05-44e3-89dd-360f5ad5ceb8</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="463c83d0-ed75-425d-85b7-631de7d89c5e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="a3a8b0c3-5820-4c38-89bc-26c49b8e6eb4" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>19265436-ca75-4c91-81d7-23a5d8630227</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b7f12cdc-6a9b-420a-876c-c8d48c020ca3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b6425fe1-912b-453f-8b9c-a039277a451b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9886a463-0e69-404d-8f95-f6f22c77f59b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d2b49723-5052-4fc7-8c36-517d02057cdc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>980ad00d-5fbb-423c-85ee-08e4caff0a41</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8ae854db-345a-4c0c-80db-df3c8592f922</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>*************-4b1f-8b9b-c76176c75d1e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>861189a8-6ee5-4c01-8300-e3d71a6904ef</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f4c6489d-c98b-4d0c-8c14-f06f37b4dea3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>d01dc092-a8ab-4347-84c0-07788e8d53c5</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>cc174e9a-35da-4e42-82c8-c8b6aee07473</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>4f0fe545-64ed-45fa-85ee-22fc6e427cbe</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>beca00b9-c736-47b8-8068-da53f409b0c6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3f05b5f1-d684-4d58-8da6-58a8ed39350c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>55f4c6b0-344c-432a-8df5-d1b81764133d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3c8dc3f1-8259-4f0b-8f25-5083a268e5be</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>********-a136-4364-859c-8a4eda3d953d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c83f07e7-4d6a-4852-826f-7c4e5a93bd6b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8b0113d8-d2f9-40b1-8258-c0578c6f4659</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>233be30a-a1ac-459f-86fe-a448f57849e4</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f7340a62-ac61-41b9-80fa-4c57df997206</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>36bf88b4-fe33-40a6-86e3-04bf6c38ff76</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>b174846a-ab48-4ccc-8126-9936415a4ed6</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>43955c42-94c2-481f-84b0-40a75607048c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="19265436-ca75-4c91-81d7-23a5d8630227">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="6" y="203" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.72a44093-5c50-4545-8193-459e38ad670a</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="b7f12cdc-6a9b-420a-876c-c8d48c020ca3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1543" y="165" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:1c18</ns3:endStateId>
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>539be3e2-0358-40ca-83fd-93260927bb93</ns16:incoming>
                        
                        
                        <ns16:incoming>629fef8d-bc77-4e55-80cc-d6dc14851830</ns16:incoming>
                        
                        
                        <ns16:incoming>aa9a742a-06ca-485e-8abb-9b0dcae801cd</ns16:incoming>
                        
                        
                        <ns16:incoming>8a7190f7-1d05-4b55-8cb1-1f773438780b</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="19265436-ca75-4c91-81d7-23a5d8630227" targetRef="29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc" name="To SQL" id="2027.72a44093-5c50-4545-8193-459e38ad670a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.ba6075c5-05a5-435b-8e40-4343f8f653c6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.ec98c981-3818-434e-88f0-d24275a1f30d" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="subResult" id="2056.a444daad-a00d-47b4-8d1b-29d2c3513b1c" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMSG" id="2056.9b504e0d-6fa4-4854-8bee-651e6f489ef6" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.7eb13764-afaf-4054-8858-66122a18ecbb" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="SQL" id="b6425fe1-912b-453f-8b9c-a039277a451b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="29" y="418" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>tw.local.requestType = tw.local.parentODCRequest.requestType.value;&#xD;
tw.local.requestNature = ""+ tw.epv.RequestNature.UpdateRequest;&#xD;
tw.local.appInfo = tw.local.parentODCRequest.appInfo;&#xD;
tw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;&#xD;
</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>e5e129f2-cf44-481b-8f3e-428bd1251ce2</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
//------------------------------------- Get ODC -------------------------------------------- &#xD;
var final = "" +tw.epv.RequestState.Final;&#xD;
var closed = "" + tw.epv.RequestState.Closed;&#xD;
var reversed = "" + tw.epv.RequestState.Reversed;&#xD;
var collected = ""+ tw.epv.RequestState.Collection;&#xD;
&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == "amend" || tw.local.parentODCRequest.requestType.value == "closure" || tw.local.parentODCRequest.requestType.value == "collection"){&#xD;
 tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '"+final+"' OR REQUESTSTATE = '"+collected+"' OR REQUESTSTATE = '"+closed+"' OR REQUESTSTATE = '"+reversed+"'  AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) != UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled') )  AND OUTSTANDINGAMOUNT &gt; 0; ";&#xD;
&#xD;
}&#xD;
else if (tw.local.parentODCRequest.requestType.value == "recreate"){&#xD;
 tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '"+final+"' AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND   PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) !=  UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled') ORDER BY ID ASC";&#xD;
&#xD;
}&#xD;
&#xD;
else{&#xD;
 tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTSTATE = '"+final+"' AND UPPER(REQUESTSTATUS) = UPPER('Completed') AND ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND UPPER(REQUESTSTATUS) != UPPER('Terminated') AND UPPER(REQUESTSTATUS) != UPPER('Canceled' ))";&#xD;
&#xD;
}&#xD;
&#xD;
 tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
 paramInit(tw.local.requestNumber);</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b6425fe1-912b-453f-8b9c-a039277a451b" targetRef="9886a463-0e69-404d-8f95-f6f22c77f59b" name="To End" id="e5e129f2-cf44-481b-8f3e-428bd1251ce2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Multiple Statements (SQLResult)" id="9886a463-0e69-404d-8f95-f6f22c77f59b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="160" y="418" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e5e129f2-cf44-481b-8f3e-428bd1251ce2</ns16:incoming>
                        
                        
                        <ns16:outgoing>6723fc62-9f8d-4173-8576-719b692e3d87</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="9886a463-0e69-404d-8f95-f6f22c77f59b" parallelMultiple="false" name="Error1" id="d2b49723-5052-4fc7-8c36-517d02057cdc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="196" y="476" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2e84b0e4-da76-4fdb-8792-4f4e6326233d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="8e93d29a-5a56-4bf1-8955-383b5b267590" eventImplId="6bfbaff3-63b0-46a6-8200-0a5461329641">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Check" id="980ad00d-5fbb-423c-85ee-08e4caff0a41">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="316" y="418" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>6723fc62-9f8d-4173-8576-719b692e3d87</ns16:incoming>
                        
                        
                        <ns16:script>if (tw.local.results[0].rows.listLength == 0) {&#xD;
      if(tw.local.parentODCRequest.requestType.value == "amend" || tw.local.parentODCRequest.requestType.value == "closure" || tw.local.parentODCRequest.requestType.value == "collection"){&#xD;
	 tw.local.message = "Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 ";&#xD;
	}&#xD;
	else&#xD;
	 tw.local.message = "Request number does not exist or is in another running request";&#xD;
	tw.local.isFound = false;&#xD;
}&#xD;
&#xD;
else{&#xD;
&#xD;
      tw.local.isFound = true;&#xD;
      &#xD;
      if(tw.local.parentODCRequest.requestType.value == "recreate"){&#xD;
      &#xD;
      //There must be no collection done on ODC&#xD;
&#xD;
         for(var i =0 ; i &lt;tw.local.results[0].rows.listLength; i++){&#xD;
&#xD;
             if(tw.local.results[0].rows[i].data[3] == "ODC Collection"){&#xD;
                  tw.local.message = "There must be no collection done on ODC";&#xD;
                  tw.local.isFound = false;&#xD;
                  break;&#xD;
             }&#xD;
          }&#xD;
          if(tw.local.isFound == true ){&#xD;
          //get the latsest instance&#xD;
           tw.local.results[0].rows[0] = tw.local.results[0].rows[tw.local.results[0].rows.listLength-1];&#xD;
&#xD;
          }&#xD;
      }&#xD;
	&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="9886a463-0e69-404d-8f95-f6f22c77f59b" targetRef="980ad00d-5fbb-423c-85ee-08e4caff0a41" name="To Recreate?" id="6723fc62-9f8d-4173-8576-719b692e3d87">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="bd95da45-6fe2-4edb-876f-aa8a35ac419a" name="found?" id="b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="637" y="201" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>67bb1d02-584a-4d03-81c5-4f69fcd20737</ns16:incoming>
                        
                        
                        <ns16:outgoing>bd95da45-6fe2-4edb-876f-aa8a35ac419a</ns16:outgoing>
                        
                        
                        <ns16:outgoing>539be3e2-0358-40ca-83fd-93260927bb93</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Init SQL for sub" id="8ae854db-345a-4c0c-80db-df3c8592f922">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="764" y="182" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bd95da45-6fe2-4edb-876f-aa8a35ac419a</ns16:incoming>
                        
                        
                        <ns16:outgoing>3654dcb0-15a6-4c46-8808-e0d75db2b600</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
&#xD;
//------------------------------------- Get invoice --------------------------------------------&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql="SELECT  INVOICENO, INVOICEDATE FROM ARCHUSR.ODC_INVOICE WHERE REQUESRID = ?;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
if(!!tw.local.results[0].rows[0])&#xD;
   paramInit(tw.local.results[0].rows[0].data[0]);&#xD;
else if(!!tw.local.results[1].rows[0])   &#xD;
   paramInit(tw.local.results[1].rows[0].data[0]);&#xD;
//------------------------------------- Get bills --------------------------------------------&#xD;
j++;&#xD;
i=0;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql="SELECT BILLOFLADINGREF, BILLOFLADINGDATE FROM ARCHUSR.ODC_BILLS WHERE REQUESRID = ?;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
if(!!tw.local.results[0].rows[0])&#xD;
   paramInit(tw.local.results[0].rows[0].data[0]);&#xD;
else if(!!tw.local.results[1].rows[0])   &#xD;
   paramInit(tw.local.results[1].rows[0].data[0]);&#xD;
&#xD;
&#xD;
//------------------------------------- Get multi tenor --------------------------------------------&#xD;
j++;&#xD;
i=0;&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
tw.local.sqlStatements[j].sql="SELECT INSTALLMENTDATE, INSTALLMENTAMOUNT FROM ARCHUSR.ODC_MULTITENORDATES WHERE REQUESRID = ?;";&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
if(!!tw.local.results[0].rows[0])&#xD;
   paramInit(tw.local.results[0].rows[0].data[0]);&#xD;
else if(!!tw.local.results[1].rows[0])   &#xD;
   paramInit(tw.local.results[1].rows[0].data[0]);&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d" targetRef="8ae854db-345a-4c0c-80db-df3c8592f922" name="To sub?" id="bd95da45-6fe2-4edb-876f-aa8a35ac419a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d" targetRef="b7f12cdc-6a9b-420a-876c-c8d48c020ca3" name="To End" id="539be3e2-0358-40ca-83fd-93260927bb93">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isFound	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="sub SQL Execute Multiple Statements (SQLResult)" id="*************-4b1f-8b9b-c76176c75d1e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="876" y="182" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3654dcb0-15a6-4c46-8808-e0d75db2b600</ns16:incoming>
                        
                        
                        <ns16:outgoing>da8842d8-099d-469a-8297-496306df06db</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.subResult</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8ae854db-345a-4c0c-80db-df3c8592f922" targetRef="*************-4b1f-8b9b-c76176c75d1e" name="To sub SQL Execute Multiple Statements (SQLResult)" id="3654dcb0-15a6-4c46-8808-e0d75db2b600">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Mapping" id="f4c6489d-c98b-4d0c-8c14-f06f37b4dea3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1005" y="182" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>da8842d8-099d-469a-8297-496306df06db</ns16:incoming>
                        
                        
                        <ns16:outgoing>c78bbd5b-9644-44ef-8764-19cd19d19e71</ns16:outgoing>
                        
                        
                        <ns16:script>var i=0-1;&#xD;
var j =0-1;&#xD;
function getnext () {&#xD;
	i++;&#xD;
	var results =null;&#xD;
	if(!!tw.local.results[0]){&#xD;
	 if(!!tw.local.results[0].rows[0])&#xD;
	    results = tw.local.results[0].rows[0].data[i];&#xD;
	 }   &#xD;
	else&#xD;
	    results = null;&#xD;
	return results;&#xD;
}&#xD;
function getCreateData () {&#xD;
      j++;&#xD;
	var results = "";&#xD;
	if(!!tw.local.results[1].rows[0])&#xD;
	    results = tw.local.results[1].rows[0].data[j];&#xD;
	else&#xD;
	    results = null&#xD;
	return results;&#xD;
}&#xD;
var amend = "" + tw.epv.RequestType.Amendment;&#xD;
var recreate = "" + tw.epv.RequestType.Recreate;&#xD;
var collection = "" + tw.epv.RequestType.Collection;&#xD;
var closure = "" + tw.epv.RequestType.Closure;&#xD;
var reversal = "" + tw.epv.RequestType.Reversal;&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == amend || tw.local.parentODCRequest.requestType.value == recreate  ){&#xD;
getnext ();&#xD;
&#xD;
tw.local.parentODCRequest = new tw.object.ODCRequest();&#xD;
tw.local.parentODCRequest.requestNo = getnext ();&#xD;
tw.local.parentODCRequest.requestNature = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.requestNature.name = tw.local.requestNature;&#xD;
tw.local.parentODCRequest.BasicDetails = new tw.object.BasicDetails();&#xD;
tw.local.parentODCRequest.BasicDetails.requestNature = getnext ();&#xD;
getnext ();&#xD;
tw.local.parentODCRequest.requestType  = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.requestType.name =tw.local.requestType;&#xD;
tw.local.parentODCRequest.requestType.value =tw.local.requestType;&#xD;
tw.local.parentODCRequest.requestDate = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.requestState = getnext ();&#xD;
//tw.local.parentODCRequest.appInfo = new tw.object.AppInfo();&#xD;
//tw.local.parentODCRequest.appInfo.status = getnext ();&#xD;
getnext ();&#xD;
tw.local.parentODCRequest.parentRequestNo = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.flexCubeContractNo = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.contractStage = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.exportPurpose = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.BasicDetails.exportPurpose.name =getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.paymentTerms = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.BasicDetails.paymentTerms.name = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.productCategory  = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.BasicDetails.productCategory.name = getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.commodityDescription = getnext ();&#xD;
&#xD;
tw.local.parentODCRequest.isLiquidated = getnext ();&#xD;
i--;&#xD;
tw.local.parentODCRequest.CustomerInfo = new tw.object.CustomerInfo()&#xD;
tw.local.parentODCRequest.CustomerInfo.cif= getnext ();&#xD;
tw.local.parentODCRequest.CustomerInfo.customerName= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation = new tw.object.ContractCreation();&#xD;
tw.local.parentODCRequest.ContractCreation.baseDate= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.valueDate= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.tenorDays= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.transitDays= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.maturityDate= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.userReference= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.productCode = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ContractCreation.productCode.value= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.productDescription= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.Stage= getnext ();&#xD;
tw.local.parentODCRequest.ContractCreation.sourceReference= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.referenceNo= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.discount= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.extraCharges= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.ourCharges= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.amountSight= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.amountDefNoAvalization= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.amountDefAvalization= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.collectableAmount= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.outstandingAmount= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.maturityDate= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.noOfDaysMaturity= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.financeApprovalNo= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.executionHub = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.executionHub.value= getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.executionHub.name= getnext (); &#xD;
tw.local.parentODCRequest.ContractLiquidation = new tw.object.ContractLiquidation();&#xD;
tw.local.parentODCRequest.ContractLiquidation.liqAmount =  getnext (); &#xD;
tw.local.parentODCRequest.ContractLiquidation.liqCurrency = getnext (); &#xD;
tw.local.parentODCRequest.FcCollections = new tw.object.FCCollections();&#xD;
tw.local.parentODCRequest.FcCollections.currency =  new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FcCollections.currency.name= getnext (); &#xD;
tw.local.parentODCRequest.FcCollections.standardExchangeRate= getnext (); &#xD;
tw.local.parentODCRequest.FcCollections.negotiatedExchangeRate= getnext ();&#xD;
tw.local.parentODCRequest.FcCollections.calculatedAmount = getnext ();&#xD;
tw.local.parentODCRequest.FcCollections.totalAllocatedAmount = getnext ();&#xD;
getnext ();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails = new tw.object.ProductShipmentDetails();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.CBECommodityClassification = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.CBECommodityClassification.name= getnext ();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.shipmentMethod = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.shipmentMethod.name= getnext ();&#xD;
tw.local.parentODCRequest.ProductShipmentDetails.shippingDate= getnext ();&#xD;
tw.local.parentODCRequest.ReversalReason = new tw.object.ReversalReason();&#xD;
tw.local.parentODCRequest.ReversalReason.reversalReason =  getnext ();&#xD;
tw.local.parentODCRequest.ReversalReason.closureReason =  getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails = new tw.object.ImporterDetails();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerName= getnext ();&#xD;
tw.local.parentODCRequest.ImporterName = tw.local.parentODCRequest.ImporterDetails.importerName;&#xD;
tw.local.parentODCRequest.ImporterDetails.importerCountry = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerCountry.value= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerAddress= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.importerPhoneNo= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bank= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.BICCode= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.ibanAccount= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankCountry = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankCountry.value= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankAddress= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.bankPhoneNo= getnext ();&#xD;
tw.local.parentODCRequest.ImporterDetails.collectingBankReference= getnext (); &#xD;
getnext ();&#xD;
getnext ();&#xD;
getnext ();&#xD;
//tw.local.parentODCRequest.appInfo.stepName= getnext ();&#xD;
//tw.local.parentODCRequest.appInfo.subStatus= getnext ();  &#xD;
//tw.local.parentODCRequest.appInfo.instanceID = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.currency = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.currency.name = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.maxCollectionDate = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.documentAmount = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.amountAdvanced = getnext ();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.NameValuePair(); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = getnext (); &#xD;
tw.local.parentODCRequest.FinancialDetailsBR.collectionAccount = new tw.object.NameValuePair();&#xD;
tw.local.parentODCRequest.FinancialDetailsBR.collectionAccount.value = getnext (); &#xD;
getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();getnext ();&#xD;
tw.local.parentODCRequest.BasicDetails.CountryOfOrigin =  {};&#xD;
tw.local.parentODCRequest.BasicDetails.CountryOfOrigin.name = getnext (); &#xD;
}&#xD;
&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure || tw.local.parentODCRequest.requestType.value == reversal ){&#xD;
 if(!!tw.local.results[0]){&#xD;
  if(!!tw.local.results[0].rows[0]){&#xD;
   if(tw.local.parentODCRequest.requestType.value == collection ){&#xD;
       getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.liqAmount = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.liqCurrency = getnext ();&#xD;
       tw.local.parentODCRequest.isLiquidated = getnext ();&#xD;
       getnext ();&#xD;
       getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.debitValueDate = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditValueDate = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.debitedAccountNo = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.accountClass = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.accountClass.name = getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.branchCode= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.balanceSign= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.currency =  {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.currency.name= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAccount.balance= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount = {};&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.standardExRate= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.negotiatedExRate= getnext ();&#xD;
       tw.local.parentODCRequest.ContractLiquidation.creditedAmount.amountInAccount= getnext ();&#xD;
       tw.local.parentODCRequest.FinancialDetailsFO.outstandingAmount= getnext ();&#xD;
       &#xD;
  }&#xD;
 else if( tw.local.parentODCRequest.requestType.value == closure){&#xD;
     getnext ();&#xD;
     tw.local.parentODCRequest.ReversalReason.closureReason = getnext ();&#xD;
   }&#xD;
  else{&#xD;
     getnext ();&#xD;
     tw.local.parentODCRequest.ReversalReason.reversalReason = getnext ();&#xD;
    }&#xD;
 }&#xD;
}&#xD;
&#xD;
  getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.exportPurpose.name = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.paymentTerms.name = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.productCategory.name = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.commodityDescription= getCreateData();&#xD;
  tw.local.parentODCRequest.cif= getCreateData();&#xD;
  tw.local.parentODCRequest.customerName= getCreateData();&#xD;
  tw.local.parentODCRequest.CustomerInfo.cif = tw.local.parentODCRequest.cif;&#xD;
  tw.local.parentODCRequest.CustomerInfo.customerName =  tw.local.parentODCRequest.customerName;&#xD;
  tw.local.parentODCRequest.ImporterName = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.maxCollectionDate = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.currency.name = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.documentAmount = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.amountAdvanced = getCreateData();&#xD;
  tw.local.parentODCRequest.FcCollections.currency.name = getCreateData();&#xD;
  tw.local.parentODCRequest.FcCollections.standardExchangeRate = getCreateData();&#xD;
  tw.local.parentODCRequest.FcCollections.negotiatedExchangeRate = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.collectionAccount.value = getCreateData();&#xD;
  tw.local.parentODCRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value = getCreateData();&#xD;
  tw.local.parentODCRequest.BasicDetails.CountryOfOrigin =  {};&#xD;
  tw.local.parentODCRequest.BasicDetails.CountryOfOrigin.name =  getCreateData(); &#xD;
  tw.local.parentODCRequest.requestNo = getCreateData ();&#xD;
  tw.local.parentODCRequest.requestNature = new tw.object.NameValuePair();&#xD;
  tw.local.parentODCRequest.requestNature.name = tw.local.requestNature;&#xD;
  tw.local.parentODCRequest.requestType  = new tw.object.NameValuePair();&#xD;
  tw.local.parentODCRequest.requestType.name =tw.local.requestType;&#xD;
  tw.local.parentODCRequest.requestType.value =tw.local.requestType;&#xD;
  getCreateData();&#xD;
  getCreateData();&#xD;
  tw.local.parentODCRequest.requestDate = getCreateData();&#xD;
&#xD;
}  &#xD;
tw.local.parentODCRequest.appInfo = tw.local.appInfo;&#xD;
&#xD;
if(!!tw.local.subResult){&#xD;
//------------------------------------- invoice ---------------------------------------&#xD;
tw.local.parentODCRequest.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
for (var i=0; i&lt;tw.local.subResult[0].rows.listLength; i++) {&#xD;
	tw.local.parentODCRequest.BasicDetails.Invoice[i] = new tw.object.Invoice();&#xD;
	tw.local.parentODCRequest.BasicDetails.Invoice[i].invoiceNo = tw.local.subResult[0].rows[i].data[0];&#xD;
	tw.local.parentODCRequest.BasicDetails.Invoice[i].invoiceDate = tw.local.subResult[0].rows[i].data[1];&#xD;
}&#xD;
//------------------------------------- bill ---------------------------------------&#xD;
tw.local.parentODCRequest.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
for (var i=0; i&lt;tw.local.subResult[1].rows.listLength; i++) {&#xD;
	tw.local.parentODCRequest.BasicDetails.Bills[i] = new tw.object.Bills();&#xD;
	tw.local.parentODCRequest.BasicDetails.Bills[i].billOfLadingRef = tw.local.subResult[1].rows[i].data[0];&#xD;
	tw.local.parentODCRequest.BasicDetails.Bills[i].billOfLadingDate = tw.local.subResult[1].rows[i].data[1];&#xD;
}&#xD;
&#xD;
//------------------------------------- Multi tenor ---------------------------------------&#xD;
tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
for (var i=0; i&lt;tw.local.subResult[1].rows.listLength; i++) {&#xD;
	tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();&#xD;
	tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i].amount = tw.local.subResult[1].rows[i].data[0];&#xD;
	tw.local.parentODCRequest.FinancialDetailsFO.multiTenorDates[i].date = tw.local.subResult[1].rows[i].data[1];&#xD;
}&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="*************-4b1f-8b9b-c76176c75d1e" targetRef="f4c6489d-c98b-4d0c-8c14-f06f37b4dea3" name="To Mapping" id="da8842d8-099d-469a-8297-496306df06db">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Catch Errors" id="d01dc092-a8ab-4347-84c0-07788e8d53c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="660" y="375" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>689af0e8-65db-446e-8ce1-a243c870e502</ns16:incoming>
                        
                        
                        <ns16:incoming>75865d8a-edb4-4791-850d-ddfa76ea09fc</ns16:incoming>
                        
                        
                        <ns16:incoming>c9e35d21-b640-4df9-8db8-57c4dceafbee</ns16:incoming>
                        
                        
                        <ns16:incoming>9ace293d-083e-4607-80a1-5d6425b74e1a</ns16:incoming>
                        
                        
                        <ns16:incoming>c5c3a7c9-bae1-4c6f-81b7-0cddab860201</ns16:incoming>
                        
                        
                        <ns16:incoming>c4879f9d-5ed9-4516-8f51-a28a201baf43</ns16:incoming>
                        
                        
                        <ns16:outgoing>c87c0cc0-63d0-44c1-8159-a055ecf7f01c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Get Request and customer data"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="d01dc092-a8ab-4347-84c0-07788e8d53c5" targetRef="cc174e9a-35da-4e42-82c8-c8b6aee07473" name="To End" id="c87c0cc0-63d0-44c1-8159-a055ecf7f01c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="cc174e9a-35da-4e42-82c8-c8b6aee07473">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="791" y="441" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c87c0cc0-63d0-44c1-8159-a055ecf7f01c</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="613550d1-afdb-477f-801e-4e3de6de6932" eventImplId="f5f14586-281c-4c76-89a2-e308d8f47b52">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6" isCollection="false" name="appInfo" id="2056.b3483e37-ca25-48db-846d-617a8b18bd0a" />
                    
                    
                    <ns16:callActivity calledElement="1.edc6e711-e06d-47bf-9d45-d9af38fb9bcb" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get Customer Data" id="4f0fe545-64ed-45fa-85ee-22fc6e427cbe">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1143" y="182" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>tw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>5384ef98-eb34-418e-8327-17d7409b65f1</ns16:incoming>
                        
                        
                        <ns16:outgoing>22ac707d-a2d0-4a29-805d-8c60e272a3e1</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5f1299c2-f79c-452a-84b4-5ad8c5a33fed</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentODCRequest.CustomerInfo.cif</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.373a4a6c-181b-43d5-8f0e-8b400049f72e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a">tw.local.parentODCRequest.CustomerInfo</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="f4c6489d-c98b-4d0c-8c14-f06f37b4dea3" targetRef="b174846a-ab48-4ccc-8126-9936415a4ed6" name="To Get Customer Data" id="c78bbd5b-9644-44ef-8764-19cd19d19e71">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="4f0fe545-64ed-45fa-85ee-22fc6e427cbe" targetRef="f7340a62-ac61-41b9-80fa-4c57df997206" name="To Query BC Contract" id="22ac707d-a2d0-4a29-805d-8c60e272a3e1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns3:endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:-788e</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676" name="Query BC Contract" id="beca00b9-c736-47b8-8068-da53f409b0c6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1429" y="181" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>tw.local.parentODCRequest.CustomerInfo = tw.local.customerInfo;&#xD;
tw.local.parentODCRequest.cif = tw.local.parentODCRequest.CustomerInfo.cif;&#xD;
tw.local.parentODCRequest.customerName =tw.local.parentODCRequest.CustomerInfo.customerName&#xD;
;&#xD;
tw.local.parentODCRequest.requestNature.value = tw.local.requestNature;&#xD;
tw.local.parentODCRequest.requestType.value = tw.local.requestType;</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>83becffc-96b8-46de-8f3e-9ed46a8b2d06</ns16:incoming>
                        
                        
                        <ns16:outgoing>629fef8d-bc77-4e55-80cc-d6dc14851830</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9465f3bb-e80a-444e-8672-899423d668fb</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.parentODCRequest</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.1447a720-69f1-46ca-8d27-88a12b035edf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9">tw.local.parentODCRequest</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="beca00b9-c736-47b8-8068-da53f409b0c6" targetRef="b7f12cdc-6a9b-420a-876c-c8d48c020ca3" name="To End" id="629fef8d-bc77-4e55-80cc-d6dc14851830">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a" isCollection="false" name="customerInfo" id="2056.20be0104-f271-45fb-8727-0c8b34d26ea3" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestNature" id="2056.548547b9-091d-4195-820c-5bdf07185978" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestType" id="2056.3f672b88-c49e-4748-89c9-4a4491f5b319" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Check Parent Request is Completed SQL" id="29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="73" y="180" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>tw.local.requestType = tw.local.parentODCRequest.requestType.value;&#xD;
tw.local.requestNature = ""+ tw.epv.RequestNature.UpdateRequest;&#xD;
tw.local.parentODCRequest.parentRequestNo = tw.local.requestNumber;&#xD;
tw.local.appInfo = tw.local.parentODCRequest.appInfo;&#xD;
tw.local.customerInfo = tw.local.parentODCRequest.CustomerInfo;&#xD;
tw.local.match = true;&#xD;
tw.local.errorMSG ="";</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.72a44093-5c50-4545-8193-459e38ad670a</ns16:incoming>
                        
                        
                        <ns16:outgoing>2fce8c92-cc97-4cbb-8665-3f4a4539259f</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
&#xD;
var final = "" +tw.epv.RequestState.Final;&#xD;
var collected = ""+ tw.epv.RequestState.Collection;&#xD;
var amend = "" + tw.epv.RequestType.Amendment;&#xD;
var recreate = "" + tw.epv.RequestType.Recreate;&#xD;
var collection = "" + tw.epv.RequestType.Collection;&#xD;
var completed = "" + tw.epv.Status.completed;  &#xD;
var terminated = "" + tw.epv.Status.terminated;&#xD;
var cancelled = "" + tw.epv.Status.Canceled;&#xD;
&#xD;
//check parent request is completed&#xD;
tw.local.sqlStatements[j] = {};&#xD;
tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE REQUESTNO = ? AND REQUESTSTATE = '"+final+"' AND REQUESTSTATUS = '"+completed+"';";&#xD;
&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
&#xD;
//check no amend/recreate/collecion running&#xD;
j++;&#xD;
i=0;&#xD;
tw.local.sqlStatements[j] = {};&#xD;
tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND (REQUESTTYPE = '"+amend+"' OR REQUESTTYPE = '"+recreate+"' OR REQUESTTYPE = '"+collection+"') AND (REQUESTSTATUS != '"+completed+"' AND REQUESTSTATUS != '"+terminated+"' AND REQUESTSTATUS != '"+cancelled+"');";&#xD;
&#xD;
tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
&#xD;
//check no collection done in case of recreate&#xD;
j++;&#xD;
i=0;&#xD;
if(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Recreate){&#xD;
   tw.local.sqlStatements[j] = {};&#xD;
   tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE PARENTREQUESTNO = ? AND REQUESTSTATE = '"+collected+"' AND REQUESTSTATUS = '"+completed+"';";&#xD;
&#xD;
   tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
   paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute Check" id="3f05b5f1-d684-4d58-8da6-58a8ed39350c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="202" y="180" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>tw.local.isFound = true;&#xD;
&#xD;
if (tw.local.results[0].rows.listLength == 0) {&#xD;
    &#xD;
	 tw.local.message = "Request number does not exist or is in another running request ";&#xD;
	 tw.local.isFound = false;&#xD;
	 &#xD;
	}&#xD;
  &#xD;
if(!!tw.local.results[1]){&#xD;
  if (tw.local.results[1].rows.listLength &gt;0){   &#xD;
  	 tw.local.message = "Request number is in another running request ";&#xD;
	 tw.local.isFound = false;&#xD;
  } &#xD;
}  &#xD;
//recreate	&#xD;
if(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Recreate){&#xD;
 if(!!tw.local.results[2]){&#xD;
  if (tw.local.results[2].rows.listLength &gt; 0){&#xD;
   &#xD;
     tw.local.message = "There must be no collection done on ODC"&#xD;
     tw.local.isFound = false;&#xD;
   }&#xD;
 } &#xD;
&#xD;
}   </ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2fce8c92-cc97-4cbb-8665-3f4a4539259f</ns16:incoming>
                        
                        
                        <ns16:outgoing>7b6e893d-ace2-4575-8ff8-9db0467537ca</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="3f05b5f1-d684-4d58-8da6-58a8ed39350c" parallelMultiple="false" name="Copy of Error1" id="55f4c6b0-344c-432a-8df5-d1b81764133d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="237" y="238" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>75865d8a-edb4-4791-850d-ddfa76ea09fc</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="2e84b0e4-da76-4fdb-8792-4f4e6326233d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="21e996e2-85b3-4292-8ae5-b91dd3d448f9" eventImplId="00914df3-74fe-4b4f-8942-6b1af4ecd6f2">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc" targetRef="3f05b5f1-d684-4d58-8da6-58a8ed39350c" name="To SQL Execute Check" id="2fce8c92-cc97-4cbb-8665-3f4a4539259f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="486d8d2b-71ea-4a47-854a-0d1947d38d06" name="exist?" id="5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="311" y="198" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7b6e893d-ace2-4575-8ff8-9db0467537ca</ns16:incoming>
                        
                        
                        <ns16:outgoing>486d8d2b-71ea-4a47-854a-0d1947d38d06</ns16:outgoing>
                        
                        
                        <ns16:outgoing>aa9a742a-06ca-485e-8abb-9b0dcae801cd</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="SQL Execute" id="3c8dc3f1-8259-4f0b-8f25-5083a268e5be">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="516" y="179" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:preAssignmentScript />
                            
                            
                            <ns3:postAssignmentScript>if (tw.local.results[0].rows.listLength == 0) {&#xD;
      if(tw.local.parentODCRequest.requestType.value == tw.epv.RequestType.Amendment|| tw.local.parentODCRequest.requestType.value ==  tw.epv.RequestType.Closure || tw.local.parentODCRequest.requestType.value ==  tw.epv.RequestType.Collection){&#xD;
	 tw.local.message = "Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 ";&#xD;
	}&#xD;
	else&#xD;
	 tw.local.message = "Request number does not exist or is in another running request";&#xD;
	 tw.local.isFound = false;&#xD;
	&#xD;
	if(!!tw.local.results[1]){&#xD;
	if(!!tw.local.results[1].rows){&#xD;
	   if (tw.local.results[1].rows.listLength == 0){&#xD;
            tw.local.message = "Request number does not exist or is in another running request or Outstanding amount is not &gt; 0 ";&#xD;
	      tw.local.isFound = false;&#xD;
&#xD;
          }&#xD;
          else&#xD;
              tw.local.isFound = true;&#xD;
        &#xD;
	   }&#xD;
	 }&#xD;
}&#xD;
&#xD;
&#xD;
</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>23c3b638-4eb7-4d6e-8743-af945b48d965</ns16:incoming>
                        
                        
                        <ns16:outgoing>67bb1d02-584a-4d03-81c5-4f69fcd20737</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.sqlStatements</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="3f05b5f1-d684-4d58-8da6-58a8ed39350c" targetRef="5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f" name="To exist?" id="7b6e893d-ace2-4575-8ff8-9db0467537ca">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f" targetRef="********-a136-4364-859c-8a4eda3d953d" name="To SQL" id="486d8d2b-71ea-4a47-854a-0d1947d38d06">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f" targetRef="b7f12cdc-6a9b-420a-876c-c8d48c020ca3" name="To End" id="aa9a742a-06ca-485e-8abb-9b0dcae801cd">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.isFound	  ==	  false</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="3c8dc3f1-8259-4f0b-8f25-5083a268e5be" targetRef="b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d" name="To found?" id="67bb1d02-584a-4d03-81c5-4f69fcd20737">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="55f4c6b0-344c-432a-8df5-d1b81764133d" targetRef="d01dc092-a8ab-4347-84c0-07788e8d53c5" name="To Catch Errors" id="75865d8a-edb4-4791-850d-ddfa76ea09fc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="SQL" id="********-a136-4364-859c-8a4eda3d953d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="399" y="179" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>486d8d2b-71ea-4a47-854a-0d1947d38d06</ns16:incoming>
                        
                        
                        <ns16:outgoing>23c3b638-4eb7-4d6e-8743-af945b48d965</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var j = 0;&#xD;
var i = 0;&#xD;
function paramInit (value) {&#xD;
	tw.local.sqlStatements[j].parameters[i] = new tw.object.SQLParameter();&#xD;
	tw.local.sqlStatements[j].parameters[i].value = value;&#xD;
	i++;&#xD;
}&#xD;
//------------------------------------- Get ODC -------------------------------------------- &#xD;
var final = "" +tw.epv.RequestState.Final;&#xD;
var closed = "" + tw.epv.RequestState.Closed;&#xD;
var reversed = "" + tw.epv.RequestState.Reversed;&#xD;
var collected = ""+ tw.epv.RequestState.Collection;&#xD;
var amend = "" + tw.epv.RequestType.Amendment;&#xD;
var recreate = "" + tw.epv.RequestType.Recreate;&#xD;
var collection = "" + tw.epv.RequestType.Collection;&#xD;
var closure = "" + tw.epv.RequestType.Closure;&#xD;
var reversal = "" + tw.epv.RequestType.Reversal;&#xD;
var create = "" + tw.epv.RequestType.Create;&#xD;
var completed = "" + tw.epv.Status.completed; &#xD;
var approved = "" + tw.epv.Status.Approved;&#xD;
&#xD;
tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
&#xD;
if(tw.local.parentODCRequest.requestType.value == amend || tw.local.parentODCRequest.requestType.value == recreate  ){&#xD;
  tw.local.sqlStatements[j].sql="SELECT * FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND (REQUESTTYPE = '"+amend+"' OR REQUESTTYPE = '"+recreate+"' OR REQUESTTYPE = '"+create+"') AND REQUESTSTATE = '"+final+"'  AND REQUESTSTATUS='"+completed+"')";&#xD;
  if(tw.local.parentODCRequest.requestType.value == amend ) &#xD;
    tw.local.sqlStatements[j].sql+= " AND OUTSTANDINGAMOUNT &gt; 0;";&#xD;
   tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
   paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
}&#xD;
&#xD;
else if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure || tw.local.parentODCRequest.requestType.value == reversal ){&#xD;
&#xD;
  &#xD;
  if(tw.local.parentODCRequest.requestType.value == collection){&#xD;
      tw.local.sqlStatements[j].sql="SELECT ID, liqAmount, liqCurrency, isLiquidated , "&#xD;
      +"LIQDEBITVALUEDATE, LIQCREDITVALUEDATE, LIQDEBITACCNUM, LIQCREDITACCOUNTCLASS, LIQCREDITBRANCHCODE, LIQCREDITBALANCESIGN, LIQCREDITACCOUNTCURRENCY, LIQCREDITACCOUNTBALANCE, STANDARDEXRATE, NEGOTIATEDEXRATE,LIQCREDITAMOUNTINACCOUNT, OUTSTANDINGAMOUNT"&#xD;
      +" FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '"+collection+"'   AND REQUESTSTATE = '"+collected+"' AND REQUESTSTATUS='"+completed+"')";&#xD;
      tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
      paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
  &#xD;
  }&#xD;
  else if (tw.local.parentODCRequest.requestType.value == closure){&#xD;
       tw.local.sqlStatements[j].sql="SELECT ID, CLOSUREREASON FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '"+closure+"'  AND REQUESTSTATE = '"+closed+"' AND REQUESTSTATUS='"+completed+"')";&#xD;
       tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
       paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
  &#xD;
  &#xD;
  }&#xD;
  else{&#xD;
       tw.local.sqlStatements[j].sql="SELECT ID, REVERSALREASON FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND REQUESTTYPE = '"+reversal+"'  AND REQUESTSTATE = '"+reversed+"' AND (REQUESTSTATUS='"+completed+"' OR REQUESTSTATUS='"+approved+"' ))";&#xD;
       tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
       paramInit(tw.local.parentODCRequest.parentRequestNo);&#xD;
  }&#xD;
  &#xD;
  j++;&#xD;
  i=0;&#xD;
  // get some of create data&#xD;
  tw.local.sqlStatements[j] = new tw.object.SQLStatement();&#xD;
  tw.local.sqlStatements[j].sql="SELECT ID, EXPORTPURPOSE,PAYMENTTERMS,PRODUCTCATEGORY,COMMODITYDESCRIPTION,CIF,CUSTOMERNAME,IMPORTERNAME, MAXIMUMCOLLECTIONDATE, REQUESTCURRENCY, DOCUMENTAMOUNT, ADVANCEAMOUNT,collectionCurrency, standardExRate, negotiatedExRate ,  COLLECTIONACCOUNT, CHARGESACCOUNT,  COUNTRYOFORIGIN,requestNo, requestNature , requestType , requestDate"&#xD;
   +" FROM ARCHUSR.ODC_REQUESTINFO WHERE ID = (SELECT max(ID) FROM ARCHUSR.ODC_REQUESTINFO WHERE  PARENTREQUESTNO = ? AND (REQUESTTYPE = '"+amend+"' OR REQUESTTYPE = '"+recreate+"' OR REQUESTTYPE = '"+create+"') AND REQUESTSTATE = '"+final+"'  AND REQUESTSTATUS='"+completed+"' ) ";&#xD;
  &#xD;
  if(tw.local.parentODCRequest.requestType.value == collection || tw.local.parentODCRequest.requestType.value == closure ) &#xD;
      tw.local.sqlStatements[j].sql +=" AND OUTSTANDINGAMOUNT &gt; 0;";&#xD;
      tw.local.sqlStatements[j].parameters = new tw.object.listOf.SQLParameter();&#xD;
     paramInit(tw.local.parentODCRequest.parentRequestNo);   &#xD;
&#xD;
}&#xD;
&#xD;
&#xD;
else{}&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="********-a136-4364-859c-8a4eda3d953d" targetRef="3c8dc3f1-8259-4f0b-8f25-5083a268e5be" name="To SQL Execute" id="23c3b638-4eb7-4d6e-8743-af945b48d965">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="4f0fe545-64ed-45fa-85ee-22fc6e427cbe" parallelMultiple="false" name="Error" id="c83f07e7-4d6a-4852-826f-7c4e5a93bd6b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1152" y="240" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c9e35d21-b640-4df9-8db8-57c4dceafbee</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="f3465617-948f-4a7a-87c0-6760ef40ff58" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="a388daa9-db68-4071-888a-892055b01acc" eventImplId="d8e132cb-2233-4b5e-87ad-58daac44d033">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="c83f07e7-4d6a-4852-826f-7c4e5a93bd6b" targetRef="d01dc092-a8ab-4347-84c0-07788e8d53c5" name="To Catch Errors" id="c9e35d21-b640-4df9-8db8-57c4dceafbee">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="beca00b9-c736-47b8-8068-da53f409b0c6" parallelMultiple="false" name="Error2" id="8b0113d8-d2f9-40b1-8258-c0578c6f4659">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1438" y="239" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>9ace293d-083e-4607-80a1-5d6425b74e1a</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="45497576-b23f-48f6-8b29-ebd8e1e14091" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="5c41723a-f5a2-48ea-8f0c-9bcd0a209161" eventImplId="2d5f9ed0-99c0-477d-862e-83a501075dfd">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="8b0113d8-d2f9-40b1-8258-c0578c6f4659" targetRef="d01dc092-a8ab-4347-84c0-07788e8d53c5" name="To Catch Errors" id="9ace293d-083e-4607-80a1-5d6425b74e1a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8ae854db-345a-4c0c-80db-df3c8592f922" parallelMultiple="false" name="Error3" id="233be30a-a1ac-459f-86fe-a448f57849e4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="799" y="240" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c5c3a7c9-bae1-4c6f-81b7-0cddab860201</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="b57731cd-2386-40c7-8ced-8f9451a4605d" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="78a2fd80-049c-4484-8a17-80f508ba47ab" eventImplId="bb18da33-cb46-44f1-8b2b-45364d05e8da">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="233be30a-a1ac-459f-86fe-a448f57849e4" targetRef="d01dc092-a8ab-4347-84c0-07788e8d53c5" name="To Catch Errors" id="c5c3a7c9-bae1-4c6f-81b7-0cddab860201">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="*************-4b1f-8b9b-c76176c75d1e" parallelMultiple="false" name="Error4" id="861189a8-6ee5-4c01-8300-e3d71a6904ef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="911" y="241" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>689af0e8-65db-446e-8ce1-a243c870e502</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="544485ef-4e7a-40a7-b1e8-71a056c32bed" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="97ea7075-8b4f-44d4-8f7d-838744382220" eventImplId="3d38db4c-46ff-4e7f-8a1f-1f83e8080829">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="861189a8-6ee5-4c01-8300-e3d71a6904ef" targetRef="d01dc092-a8ab-4347-84c0-07788e8d53c5" name="To Catch Errors" id="689af0e8-65db-446e-8ce1-a243c870e502">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.57c0ee22-60ab-41c1-9555-e6adbd56ca4f" name="Get Customer Accounts" id="f7340a62-ac61-41b9-80fa-4c57df997206">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1273" y="181" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                            
                            <ns3:postAssignmentScript>if(!!tw.local.parentODCRequest.FinancialDetailsBR){&#xD;
&#xD;
  if(!tw.local.parentODCRequest.FcCollections)&#xD;
      tw.local.parentODCRequest.FcCollections = {};&#xD;
      &#xD;
  tw.local.parentODCRequest.FcCollections.listOfAccounts = [];&#xD;
	&#xD;
	for(var i=0;i&lt;tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts.length;i++)&#xD;
	{&#xD;
	     &#xD;
		tw.local.parentODCRequest.FcCollections.listOfAccounts[i] = {};&#xD;
		tw.local.parentODCRequest.FcCollections.listOfAccounts[i]= tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts[i];&#xD;
	}&#xD;
} &#xD;
</ns3:postAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>22ac707d-a2d0-4a29-805d-8c60e272a3e1</ns16:incoming>
                        
                        
                        <ns16:outgoing>83becffc-96b8-46de-8f3e-9ed46a8b2d06</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0bc98ce8-10aa-460a-8857-8a8600dafc90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentODCRequest.CustomerInfo.cif</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0b3efcd6-eeb2-45cd-8308-5e2d2d64f861</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentODCRequest.FcCollections.currency.value</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.208f75bb-611b-4393-83d6-d60470c4a6a4</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.parentODCRequest.FinancialDetailsBR.listOfAccounts</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.7fdee937-d555-46b6-88d2-46c40e165c27</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMSG</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.4043399c-ab46-481b-8c1e-5b6ecd5e5074</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.23b90cb0-77a8-406b-8cd3-4891cf594d48" />
                    
                    
                    <ns16:sequenceFlow sourceRef="f7340a62-ac61-41b9-80fa-4c57df997206" targetRef="beca00b9-c736-47b8-8068-da53f409b0c6" name="To Query BC Contract" id="83becffc-96b8-46de-8f3e-9ed46a8b2d06">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:75c2</ns3:endStateId>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="f7340a62-ac61-41b9-80fa-4c57df997206" parallelMultiple="false" name="Error5" id="36bf88b4-fe33-40a6-86e3-04bf6c38ff76">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1308" y="239" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>c4879f9d-5ed9-4516-8f51-a28a201baf43</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="3f7d7a4b-295f-46f4-8a99-8ef957a20628" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="4e2984b7-d21f-4bed-816e-b0dac71b91c8" eventImplId="6bca84bb-b477-4631-872c-a2519ba5b7b1">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="36bf88b4-fe33-40a6-86e3-04bf6c38ff76" targetRef="d01dc092-a8ab-4347-84c0-07788e8d53c5" name="To Catch Errors" id="c4879f9d-5ed9-4516-8f51-a28a201baf43">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightTop</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:exclusiveGateway default="2bcf95a6-6173-4bed-8878-79a582b256f7" name="Match?" id="b174846a-ab48-4ccc-8126-9936415a4ed6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1052" y="280" width="32" height="32" />
                            
                            
                            <ns3:postAssignmentScript />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c78bbd5b-9644-44ef-8764-19cd19d19e71</ns16:incoming>
                        
                        
                        <ns16:outgoing>5384ef98-eb34-418e-8327-17d7409b65f1</ns16:outgoing>
                        
                        
                        <ns16:outgoing>2bcf95a6-6173-4bed-8878-79a582b256f7</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="b174846a-ab48-4ccc-8126-9936415a4ed6" targetRef="4f0fe545-64ed-45fa-85ee-22fc6e427cbe" name="Yes" id="5384ef98-eb34-418e-8327-17d7409b65f1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftBottom</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.customerInfo.cif	  ==	  tw.local.parentODCRequest.CustomerInfo.cif</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="43955c42-94c2-481f-84b0-40a75607048c" targetRef="b7f12cdc-6a9b-420a-876c-c8d48c020ca3" name="To End" id="8a7190f7-1d05-4b55-8cb1-1f773438780b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomLeft</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.customerInfo.cif != tw.local.parentODCRequest.CustomerInfo.cif</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="validation script" id="43955c42-94c2-481f-84b0-40a75607048c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="1145" y="292" width="95" height="70" color="#95D087" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2bcf95a6-6173-4bed-8878-79a582b256f7</ns16:incoming>
                        
                        
                        <ns16:outgoing>8a7190f7-1d05-4b55-8cb1-1f773438780b</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.errorMSG = "This CIF Number Not related to Parent Request Number";&#xD;
tw.local.match = false;&#xD;
&#xD;
&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="b174846a-ab48-4ccc-8126-9936415a4ed6" targetRef="43955c42-94c2-481f-84b0-40a75607048c" name="No" id="2bcf95a6-6173-4bed-8878-79a582b256f7">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.customerInfo.cif != tw.local.parentODCRequest.cif</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e5e129f2-cf44-481b-8f3e-428bd1251ce2</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b6425fe1-912b-453f-8b9c-a039277a451b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.9886a463-0e69-404d-8f95-f6f22c77f59b</toProcessItemId>
            <guid>0c58ba54-2535-49e0-8c6a-2d4c74ee5e88</guid>
            <versionId>2120ca62-197e-4f2b-acf6-183dc04211c2</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b6425fe1-912b-453f-8b9c-a039277a451b</fromProcessItemId>
            <toProcessItemId>2025.9886a463-0e69-404d-8f95-f6f22c77f59b</toProcessItemId>
        </link>
        <link name="To Get Customer Data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c78bbd5b-9644-44ef-8764-19cd19d19e71</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f4c6489d-c98b-4d0c-8c14-f06f37b4dea3</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</toProcessItemId>
            <guid>8e28442f-f4e6-45b2-8287-4595f1b4f37b</guid>
            <versionId>2b7079bf-12cf-4cd2-8028-ae22780150e6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f4c6489d-c98b-4d0c-8c14-f06f37b4dea3</fromProcessItemId>
            <toProcessItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</toProcessItemId>
        </link>
        <link name="To Query BC Contract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.22ac707d-a2d0-4a29-805d-8c60e272a3e1</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.4f0fe545-64ed-45fa-85ee-22fc6e427cbe</fromProcessItemId>
            <endStateId>guid:83e1efe624431d49:-7084ad56:189daab98ba:-788e</endStateId>
            <toProcessItemId>2025.f7340a62-ac61-41b9-80fa-4c57df997206</toProcessItemId>
            <guid>d9102fe2-47d4-4892-a424-9baf028b7523</guid>
            <versionId>321114e8-3a16-44f8-9ca5-e19ab5d6d7a9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.4f0fe545-64ed-45fa-85ee-22fc6e427cbe</fromProcessItemId>
            <toProcessItemId>2025.f7340a62-ac61-41b9-80fa-4c57df997206</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2bcf95a6-6173-4bed-8878-79a582b256f7</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.43955c42-94c2-481f-84b0-40a75607048c</toProcessItemId>
            <guid>353613fe-e9af-45e0-ac67-2164cb6d3370</guid>
            <versionId>32ad2a5a-83ce-40bd-ae6e-7c0c087fd550</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</fromProcessItemId>
            <toProcessItemId>2025.43955c42-94c2-481f-84b0-40a75607048c</toProcessItemId>
        </link>
        <link name="To SQL">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.486d8d2b-71ea-4a47-854a-0d1947d38d06</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.********-a136-4364-859c-8a4eda3d953d</toProcessItemId>
            <guid>02f0b89b-caef-4f44-8120-19a1e78103cc</guid>
            <versionId>39956466-af17-41ae-9fa4-b4006b129bc4</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</fromProcessItemId>
            <toProcessItemId>2025.********-a136-4364-859c-8a4eda3d953d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.8a7190f7-1d05-4b55-8cb1-1f773438780b</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.43955c42-94c2-481f-84b0-40a75607048c</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
            <guid>31e09ede-f5a9-4a6e-8395-bcd780c09ee6</guid>
            <versionId>47dcff27-293d-4971-87e8-091a3e00ecc7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomLeft" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.43955c42-94c2-481f-84b0-40a75607048c</fromProcessItemId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
        </link>
        <link name="To SQL Execute">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.23c3b638-4eb7-4d6e-8743-af945b48d965</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.********-a136-4364-859c-8a4eda3d953d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3c8dc3f1-8259-4f0b-8f25-5083a268e5be</toProcessItemId>
            <guid>ba8f71ff-cd45-4590-9fee-fae621c977a1</guid>
            <versionId>4ff5cd01-3c6e-4ba0-a318-1fe4763add5c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.********-a136-4364-859c-8a4eda3d953d</fromProcessItemId>
            <toProcessItemId>2025.3c8dc3f1-8259-4f0b-8f25-5083a268e5be</toProcessItemId>
        </link>
        <link name="To found?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.67bb1d02-584a-4d03-81c5-4f69fcd20737</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3c8dc3f1-8259-4f0b-8f25-5083a268e5be</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</toProcessItemId>
            <guid>0a9f09fe-685c-475c-9cb6-39816f9f8436</guid>
            <versionId>5faa2a10-43d3-4dc7-979b-41257ff7c261</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3c8dc3f1-8259-4f0b-8f25-5083a268e5be</fromProcessItemId>
            <toProcessItemId>2025.b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.aa9a742a-06ca-485e-8abb-9b0dcae801cd</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</fromProcessItemId>
            <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:5463</endStateId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
            <guid>8c588ab5-7b18-4ff2-bd57-5cdb7cbcde66</guid>
            <versionId>753e148e-cd4e-4f43-8ad0-effe20db260f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</fromProcessItemId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
        </link>
        <link name="To sub?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bd95da45-6fe2-4edb-876f-aa8a35ac419a</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.8ae854db-345a-4c0c-80db-df3c8592f922</toProcessItemId>
            <guid>d4bcca55-b564-447b-b0db-f6c21d489234</guid>
            <versionId>7af7b2d7-5295-4313-933c-9a522841e935</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</fromProcessItemId>
            <toProcessItemId>2025.8ae854db-345a-4c0c-80db-df3c8592f922</toProcessItemId>
        </link>
        <link name="To exist?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7b6e893d-ace2-4575-8ff8-9db0467537ca</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.3f05b5f1-d684-4d58-8da6-58a8ed39350c</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</toProcessItemId>
            <guid>e2e0577f-9e39-4a11-a83f-099cd5f50204</guid>
            <versionId>84338aa3-f181-4162-b4e8-01ef6ba88cc6</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.3f05b5f1-d684-4d58-8da6-58a8ed39350c</fromProcessItemId>
            <toProcessItemId>2025.5a4ce1ca-fd3b-48d2-8b6d-bfd871b1198f</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.629fef8d-bc77-4e55-80cc-d6dc14851830</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.beca00b9-c736-47b8-8068-da53f409b0c6</fromProcessItemId>
            <endStateId>guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd</endStateId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
            <guid>33062fd5-b1e6-442c-9cbe-73614a41b6db</guid>
            <versionId>865cc9e8-72b5-49a4-a14a-553d2340c84d</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.beca00b9-c736-47b8-8068-da53f409b0c6</fromProcessItemId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5384ef98-eb34-418e-8327-17d7409b65f1</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</fromProcessItemId>
            <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:5464</endStateId>
            <toProcessItemId>2025.4f0fe545-64ed-45fa-85ee-22fc6e427cbe</toProcessItemId>
            <guid>0aff68ea-b3a8-40a2-b743-bf0c10634f0d</guid>
            <versionId>8ddb9396-46ef-4838-aae8-4a07117050cd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftBottom" portType="2" />
            <fromProcessItemId>2025.b174846a-ab48-4ccc-8126-9936415a4ed6</fromProcessItemId>
            <toProcessItemId>2025.4f0fe545-64ed-45fa-85ee-22fc6e427cbe</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c87c0cc0-63d0-44c1-8159-a055ecf7f01c</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.cc174e9a-35da-4e42-82c8-c8b6aee07473</toProcessItemId>
            <guid>aab997b8-a2af-481d-b18f-d96346e66ad5</guid>
            <versionId>905433f1-7d43-4254-9b61-c49fb8e01671</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.d01dc092-a8ab-4347-84c0-07788e8d53c5</fromProcessItemId>
            <toProcessItemId>2025.cc174e9a-35da-4e42-82c8-c8b6aee07473</toProcessItemId>
        </link>
        <link name="To Recreate?">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6723fc62-9f8d-4173-8576-719b692e3d87</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9886a463-0e69-404d-8f95-f6f22c77f59b</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.980ad00d-5fbb-423c-85ee-08e4caff0a41</toProcessItemId>
            <guid>367cbe78-ed79-4914-a80c-b2d1902bec95</guid>
            <versionId>9a3dee83-408f-478d-8892-cddff81be91c</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9886a463-0e69-404d-8f95-f6f22c77f59b</fromProcessItemId>
            <toProcessItemId>2025.980ad00d-5fbb-423c-85ee-08e4caff0a41</toProcessItemId>
        </link>
        <link name="To Mapping">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.da8842d8-099d-469a-8297-496306df06db</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.*************-4b1f-8b9b-c76176c75d1e</fromProcessItemId>
            <endStateId>guid:b5ffa6577ed3a684:-4e3a2174:115951f71d3:-7fe8</endStateId>
            <toProcessItemId>2025.f4c6489d-c98b-4d0c-8c14-f06f37b4dea3</toProcessItemId>
            <guid>037dacc5-cb17-44ca-bbae-abf6135e544e</guid>
            <versionId>9b050cdf-9e89-4ac4-861d-1b3ef0930c4a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.*************-4b1f-8b9b-c76176c75d1e</fromProcessItemId>
            <toProcessItemId>2025.f4c6489d-c98b-4d0c-8c14-f06f37b4dea3</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.539be3e2-0358-40ca-83fd-93260927bb93</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</fromProcessItemId>
            <endStateId>guid:4e45c66327642938:4e8a44d0:18bf1ac4de4:5462</endStateId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
            <guid>fdc8784b-f48e-4f2c-a27e-a6f0f502e278</guid>
            <versionId>ab359834-eed3-40ff-af48-d62b78395726</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.b0be2bd6-7c74-4c4f-8a6b-af4d10c8da0d</fromProcessItemId>
            <toProcessItemId>2025.b7f12cdc-6a9b-420a-876c-c8d48c020ca3</toProcessItemId>
        </link>
        <link name="To Query BC Contract">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.83becffc-96b8-46de-8f3e-9ed46a8b2d06</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f7340a62-ac61-41b9-80fa-4c57df997206</fromProcessItemId>
            <endStateId>guid:fef170a08f25d496:5466e087:189df5f8551:75c2</endStateId>
            <toProcessItemId>2025.beca00b9-c736-47b8-8068-da53f409b0c6</toProcessItemId>
            <guid>f11e5034-a547-4f03-a5ec-84e470237f6e</guid>
            <versionId>ab806a70-9d07-4746-87f7-81ee00fd9789</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f7340a62-ac61-41b9-80fa-4c57df997206</fromProcessItemId>
            <toProcessItemId>2025.beca00b9-c736-47b8-8068-da53f409b0c6</toProcessItemId>
        </link>
        <link name="To SQL Execute Check">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.2fce8c92-cc97-4cbb-8665-3f4a4539259f</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3f05b5f1-d684-4d58-8da6-58a8ed39350c</toProcessItemId>
            <guid>591c5d10-6049-439c-8140-93429035e5af</guid>
            <versionId>e2f9736b-0cc9-4da8-b3dc-267c6782e265</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.29af9e3f-bcc1-40fa-8c4e-3c525ec1ecbc</fromProcessItemId>
            <toProcessItemId>2025.3f05b5f1-d684-4d58-8da6-58a8ed39350c</toProcessItemId>
        </link>
        <link name="To sub SQL Execute Multiple Statements (SQLResult)">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.3654dcb0-15a6-4c46-8808-e0d75db2b600</processLinkId>
            <processId>1.58febcb0-50a3-4963-8368-32c96c00d116</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8ae854db-345a-4c0c-80db-df3c8592f922</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.*************-4b1f-8b9b-c76176c75d1e</toProcessItemId>
            <guid>70d5967b-8f51-4778-85a8-117512e63849</guid>
            <versionId>eea84a66-**************-1d0d896be7a9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8ae854db-345a-4c0c-80db-df3c8592f922</fromProcessItemId>
            <toProcessItemId>2025.*************-4b1f-8b9b-c76176c75d1e</toProcessItemId>
        </link>
    </process>
</teamworks>

