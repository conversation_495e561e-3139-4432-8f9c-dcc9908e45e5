<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.9321de6e-e6a5-435e-accb-0fbbd129c48a" name="ACT04 - ODC Execution Hub Initiation">
        <lastModified>1748954111587</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>10</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength isNull="true" />
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>false</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>false</isAjaxExposed>
        <description isNull="true" />
        <guid>42171bf6-dd95-4757-850f-0e7fa1d52dfb</guid>
        <versionId>69ae4197-2b05-43e5-b85f-c7987517a34b</versionId>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"extensionElements":{"userTaskImplementation":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7"],"isInterrupting":true,"extensionElements":{"postAssignmentScript":[],"default":["2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":113,"y":111,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"a7b2f73c-28cf-4a6a-9993-f252e17ae70f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.b2b0bdc1-1032-4018-8978-24a052bafe51"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorPanelVIS","isCollection":false,"declaredType":"dataObject","id":"2056.9af9df34-0783-4778-9dbc-e75ae58da5b6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"terminateReasonVIS","isCollection":false,"declaredType":"dataObject","id":"2056.7bd925a6-f30d-4822-83e0-1cf0ba29f6cb"},{"itemSubjectRef":"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4","name":"actionConditions","isCollection":false,"declaredType":"dataObject","id":"2056.5d359bdf-d4b5-4dc0-877a-197f885477b6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"deliveryterms","isCollection":false,"declaredType":"dataObject","id":"2056.843ee6c7-a857-4d2c-87b1-c6374e53fba3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"paymentTerms","isCollection":false,"declaredType":"dataObject","id":"2056.ce89d2d4-ed8d-4d40-8b7d-0890d5ad74a6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"specialInstructions","isCollection":false,"declaredType":"dataObject","id":"2056.d2579c3c-4e4a-4a1b-8e46-87a45d36bcba"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"instructions","isCollection":false,"declaredType":"dataObject","id":"2056.2aaea123-b9bb-409d-8af1-2b3310bdc747"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"flexCubeVIS","isCollection":false,"declaredType":"dataObject","id":"2056.72531c8e-7b17-45d5-883b-c9e2ca4e7092"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestTypeVIS","isCollection":false,"declaredType":"dataObject","id":"2056.4a865219-2b57-427b-8a39-7df701820161"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"contractStageVIS","isCollection":false,"declaredType":"dataObject","id":"2056.a40cf44c-95f9-431c-863f-e3cf0e04ea9b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"bankRefVIS","isCollection":false,"declaredType":"dataObject","id":"2056.096c2f16-3cbd-48c1-805b-960b7cf63ee0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"event","isCollection":false,"declaredType":"dataObject","id":"2056.8be43605-ddb8-4a68-835f-f1ca3b1af4ab"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"currencyList","isCollection":true,"declaredType":"dataObject","id":"2056.fae4f2a7-8281-424e-87e4-fa1fb316208c"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"addchargeBtnVIS","isCollection":false,"declaredType":"dataObject","id":"2056.2785bfd8-f42e-49dc-8743-e5295f8753d9"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.addressLine1 = \"\";\nautoObject.addressLine2 = \"\";\nautoObject.addressLine3 = \"\";\nautoObject.customerSector = {};\nautoObject.customerSector.name = \"\";\nautoObject.customerSector.value = \"\";\nautoObject.customerType = \"\";\nautoObject.customerNoCBE = \"\";\nautoObject.facilityType = {};\nautoObject.facilityType.name = \"\";\nautoObject.facilityType.value = \"\";\nautoObject.commercialRegistrationNo = \"\";\nautoObject.commercialRegistrationOffice = \"\";\nautoObject.taxCardNo = \"\";\nautoObject.importCardNo = \"\";\nautoObject.initiationHub = \"\";\nautoObject.country = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a","name":"collectingBankInfo","isCollection":false,"declaredType":"dataObject","id":"2056.dfe30b45-a52b-4f95-8a2d-6241995032cc"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.addressLine1 = \"\";\nautoObject.addressLine2 = \"\";\nautoObject.addressLine3 = \"\";\nautoObject.customerSector = {};\nautoObject.customerSector.name = \"\";\nautoObject.customerSector.value = \"\";\nautoObject.customerType = \"\";\nautoObject.customerNoCBE = \"\";\nautoObject.facilityType = {};\nautoObject.facilityType.name = \"\";\nautoObject.facilityType.value = \"\";\nautoObject.commercialRegistrationNo = \"\";\nautoObject.commercialRegistrationOffice = \"\";\nautoObject.taxCardNo = \"\";\nautoObject.importCardNo = \"\";\nautoObject.initiationHub = \"\";\nautoObject.country = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a","name":"partyTypesInfo","isCollection":false,"declaredType":"dataObject","id":"2056.af9c4b39-6fcd-4d9f-8eca-f39dd5ab5369"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"multiTenorVIS","isCollection":false,"declaredType":"dataObject","id":"2056.f28609ef-8dde-4927-864f-fc323f694ba9"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"PartyAccountsList","isCollection":true,"declaredType":"dataObject","id":"2056.640fa68e-cdb2-4dc0-842a-3d9b44690ddb"},{"itemSubjectRef":"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13","name":"initialAccountList","isCollection":true,"declaredType":"dataObject","id":"2056.1162617c-dd7d-4ede-83ca-f0ff3a73c4a9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNumberVIS","isCollection":false,"declaredType":"dataObject","id":"2056.69e6eedd-1a2f-4d00-81d3-4be407a38938"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"x","isCollection":false,"declaredType":"dataObject","id":"2056.d092fe95-e100-4c09-8a3c-ff77d4c19f7f"},{"itemSubjectRef":"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42","name":"drawerAccountsList","isCollection":true,"declaredType":"dataObject","id":"2056.9fea3a16-f7b0-4b82-8b84-e34715aaea53"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"selectedIndex","isCollection":false,"declaredType":"dataObject","id":"2056.6ca322b0-f3cc-4e23-8c9c-38a72d23c824"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fromCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.0c3150de-f48c-4392-8745-78bf480ec637"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"toCurrency","isCollection":false,"declaredType":"dataObject","id":"2056.c575aebe-5805-45f1-8658-f4b61135671e"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"rate","isCollection":false,"declaredType":"dataObject","id":"2056.6af17de0-5078-4f22-8c4b-993cf87cace9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"barCodePdf","isCollection":false,"declaredType":"dataObject","id":"2056.ed625282-4579-4a61-8ea5-612dbaeb5b82"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"generationStatus","isCollection":false,"declaredType":"dataObject","id":"2056.066a7f3a-f039-4041-8f63-ea92395a16ec"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"calculatedChangeAmnt","isCollection":false,"declaredType":"dataObject","id":"2056.b08e403d-8597-463f-8e22-c776688990c6"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerAndPartyCifs","isCollection":true,"declaredType":"dataObject","id":"2056.474688e9-036c-4acd-858a-98a352832288"},{"itemSubjectRef":"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651","name":"customerFullDetails","isCollection":false,"declaredType":"dataObject","id":"2056.015a8404-6af2-4492-81ac-ced4c5cf575d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"addressBICList","isCollection":true,"declaredType":"dataObject","id":"2056.124649bc-dddd-4055-8621-bbd3938104d0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"partyTypeName","isCollection":false,"declaredType":"dataObject","id":"2056.a7233099-da0d-429f-822c-bc1ed9a69ae3"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"new Date()"}]},"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"todayDate","isCollection":false,"declaredType":"dataObject","id":"2056.f0e1069c-61d1-4f97-89e6-d78c498604f1"},{"itemSubjectRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","name":"invalidTabs","isCollection":true,"declaredType":"dataObject","id":"2056.918536a9-7d45-4725-857b-def837024050"},{"outgoing":["2027.04017f08-d5e0-446b-8673-524fd01f95e7","2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352"],"incoming":["2027.64964c28-a358-40ac-89dd-4f3c8453f4fd","2027.d832440b-ec84-4d61-840a-c6a1e40396d9","2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7"],"extensionElements":{"postAssignmentScript":[],"nodeVisualInfo":[{"width":95,"x":65,"y":24,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"],"preAssignmentScript":[]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"contentBoxContrib":[{"contributions":[{"layoutItemId":"5","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"9b587cf5-02f3-45ec-8539-5acdc3534c9f","optionName":"@label","value":"Financial Details Trade FO"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ef44c152-c0f1-4dd9-881f-44af781c5381","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2f16c85e-28a7-44ff-8ac7-80daad5bedde","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"efd6cc50-bcaa-4ffc-83be-b501e5247b14","optionName":"act3VIS","value":"ReadOnly"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ba61ab19-07ac-42b1-8bf3-f78ce5a6cb33","optionName":"requestType","value":"tw.local.odcRequest.requestType.value"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0e8fc121-c9a2-4445-8fa9-4d113987737f","optionName":"multiTenorDatesVIS","value":"tw.local.multiTenorVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"6212e428-cda6-4f2c-8de3-c2a483bbc367","optionName":"documentAmount","value":"tw.local.odcRequest.FinancialDetailsBR.documentAmount"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"860e1df0-8dce-4110-8125-f195cc249d01","optionName":"amountAdvanced","value":"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8835530a-33c5-4cef-8e3d-762bfc5a4ac3","optionName":"todayDate","value":"tw.local.todayDate"}],"viewUUID":"64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1","binding":"tw.local.odcRequest.FinancialDetailsFO","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"f2dc714e-5c43-4ddc-8e49-8a416fb3f37b","version":"8550"},{"layoutItemId":"0","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fb00f497-826f-46c5-8f7f-7e6c637a3f5c","optionName":"@label","value":"Basic Details"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ff028ba6-8748-4836-8be8-fbe74ee68484","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"335805ec-15fc-48d0-8d52-2ad758d4d800","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a1028169-fbbf-4004-8dfb-0dae97ebc9c3","optionName":"parentRequestNoVis","value":"tw.local.parentRequestNumberVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a1bfea0-8b6a-4cad-835e-1a0401d0bc93","optionName":"contractStageVIS","value":"tw.local.contractStageVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b24aa2e1-bade-4c12-8820-fc41b6d9d248","optionName":"flexCubeContractNoVIS","value":"Editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"ce4170a1-ae16-4fad-8015-05205a52f208","optionName":"multiTenorDatesVIS","value":"tw.local.multiTenorVIS"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"e21ea690-daf9-4ec5-8e73-d72dadc63db2","optionName":"basicDetailsCVVIS","value":"Editable"}],"viewUUID":"64.f7009c53-bea2-4a1f-8dc8-db4918768c05","binding":"tw.local.odcRequest.BasicDetails","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"bf381ae5-eb28-44fe-8b2c-95fe1b9d60b7","version":"8550"},{"layoutItemId":"8","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d61cc0e6-28ab-45b9-8e66-1401808422ac","optionName":"@label","value":"Contract Creation"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"099b04ee-f97e-44e0-8721-1b4f69e30b8f","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"219ba03e-7b89-4667-897f-404656354ca9","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"c364e5d8-b768-4044-89f8-a77b2a2406a9","optionName":"contractStage","value":"tw.local.odcRequest.BasicDetails.contractStage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4e393b5b-13ab-46ef-8fe0-1384cb6e228e","optionName":"bpmRequestNumber","value":"tw.local.odcRequest.requestNo"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a1d571ea-e2ef-4710-8911-35d4627e0653","optionName":"currency","value":"tw.local.odcRequest.FinancialDetailsBR.currency.value"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"52946f33-428b-44e4-87aa-31aff2ff4cef","optionName":"nbeCollectableAmount","value":"tw.local.odcRequest.FinancialDetailsFO.collectableAmount"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7a9ad29f-24b8-4959-83d9-7a3b19a9f2fd","optionName":"baseDate","value":"tw.local.today"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"19e11f3e-ab78-4e75-8182-b3b640c6d62f","optionName":"contractCreationVis","value":"editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"18f643d7-0df4-4db6-8b56-ac9008acb7d7","optionName":"todayDate","value":"tw.local.todayDate"}],"viewUUID":"64.0f40f56d-733f-4bd5-916c-92ae7dccbb10","binding":"tw.local.odcRequest.ContractCreation","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c1549433-4905-4fc5-877f-a41e227a93d8","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"ca6107ca-bc4b-4305-86bc-8d212b250ce3"}],"layoutItemId":"Tab_section1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"812e543b-3aba-4db9-89e9-0704be88ff7b","optionName":"@label","value":"Tab section"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1744ab20-12ea-4f76-88cf-dd1a4140e7b6","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4b329f05-f47d-41fc-8e7a-06a25d18388e","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2e45773a-0f09-4ef8-8d83-653988e6a074","optionName":"colorStyle","value":"P"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2e436236-eb83-449a-8d6f-17318d446e77","optionName":"tabsStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"688cf571-19a2-4bf8-8f5e-995cbbccd4fa","optionName":"sizeStyle","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"cbd32f5e-b3eb-4587-8b36-82a68f0a982f","optionName":"@visibility","value":"{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"REQUIRED\"}]}"}],"viewUUID":"64.c05b439f-a4bd-48b0-8644-fa3b59052217","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"3599af2a-192e-46e3-8bca-622ef4ddbeff","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"273a4cab-8194-472d-8d90-50b65d113642"}],"layoutItemId":"Vertical_layout1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"64b87181-3389-43b0-8f62-b27073e3765e","optionName":"@label","value":"Vertical layout"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"07e93a36-bb18-439e-8a67-0b8dafce8cdd","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"28e3dffa-508b-43af-8be5-7827b937eb04","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"ae78654d-6e48-4e5b-82e4-8305300fa439","version":"8550"}],"contentBoxId":"ContentBox1","declaredType":"com.ibm.bpmsdk.model.coach.ContentBoxContrib","id":"94aa8cef-ca3b-46f6-8538-7d77edd23b85"}],"layoutItemId":"DC_Templete1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7a160b51-fe6b-4a7a-824b-c66c05c6f693","optionName":"@label","value":"DC Templete"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b1ed620c-d310-4ece-84fa-e12c0a26e65b","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"981204cd-12fb-4701-89c1-ef88e7e98c38","optionName":"@labelVisibility","value":"SHOW"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4a33ec08-3168-4539-8b54-276083cf66f3","optionName":"stepLog","value":"tw.local.odcRequest.stepLog"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"34a2bcf8-ef34-4ab0-88a5-30e63981c2fc","optionName":"action","value":"tw.local.odcRequest.actions[]"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"399a5351-739a-42e7-811b-32d66d54847f","optionName":"selectedAction","value":"tw.local.odcRequest.stepLog.action"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5c4e7e20-d117-4fc0-889f-810bc997da20","optionName":"complianceApproval","value":"tw.local.odcRequest.complianceApproval"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3bd029f5-2465-4bac-8d5e-25454fc139ad","optionName":"complianceApprovalVis","value":"Editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a2e07ca0-29dc-4389-89f4-26a5c540266a","optionName":"errorMsg","value":"tw.local.errorMessage"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"14eb0410-73d6-4144-8a7e-d63aaa71ef47","optionName":"errorPanelVIS","value":"tw.local.errorPanelVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"839364a7-5d2b-46e9-84f2-9a948bcf5884","optionName":"terminateReasonVIS","value":"tw.local.terminateReasonVIS"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"04487a36-4d5d-4185-803a-e8d2168fdaf7","optionName":"actionConditions","value":"tw.local.actionConditions"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"0200fe69-af55-4577-8aae-426fbf618115","optionName":"approvalCommentVIS","value":"NONE"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"2092fe0a-7939-47cd-8f9e-6e4c00a8580c","optionName":"tradeFoCommentVis","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"b98e3d8d-3616-471e-8b60-a569760116f6","optionName":"exeHubMkrCommentVis","value":"Editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f6735176-e7c7-4acf-8153-e5a13b0cfe3b","optionName":"tradeFoComment","value":"tw.local.odcRequest.tradeFoComment"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5fb9634b-99ac-4bfa-82d3-54895449b89e","optionName":"exeHubMkrComment","value":"tw.local.odcRequest.exeHubMkrComment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"bd99d6d6-d684-487e-8d45-ab8cdb620741","optionName":"returnReasonVIS","value":"Editable"},{"valueType":"dynamic","declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"191cf4de-3c5c-4ffc-85d8-fd41a9b64152","optionName":"compcheckerComment","value":"tw.local.odcRequest.compcheckerComment"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"3d7aa9c2-68a4-45f8-8400-33430377893f","optionName":"compcheckerCommentVIS","value":"Readonly"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"5003054e-ba80-47d3-8a49-670101356da3","optionName":"disableSubmit","value":"false"}],"viewUUID":"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f","binding":"tw.local.odcRequest.appInfo","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"a4e967b5-84f5-44e8-86b0-0dcd259a0ccf","version":"8550"},{"layoutItemId":"Button1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1b743c51-509a-48f9-8bf7-026f654787d9","optionName":"@label","value":"Button"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"d134b671-591a-47a2-8235-20249b18f14f","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"4c2434aa-5a0d-425d-8aa7-************","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"698d8eb4-67b2-4963-814f-788b4505ef2e","version":"8550"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Screen","isForCompensation":false,"completionQuantity":1,"dataChangeScript":"","id":"2025.4695605b-49e8-461c-8104-b8bc0e52442d"},{"outgoing":["2027.d832440b-ec84-4d61-840a-c6a1e40396d9","2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0"],"incoming":["2027.b683912e-2ca5-4470-8164-22f6918b58b8"],"default":"2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":397,"y":42,"declaredType":"TNodeVisualInfo","height":32}]},"name":"isValid","declaredType":"exclusiveGateway","id":"2025.c83fdd58-5656-45c1-88be-786d246161b5"},{"startQuantity":1,"outgoing":["2027.b683912e-2ca5-4470-8164-22f6918b58b8"],"incoming":["2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352"],"default":"2027.b683912e-2ca5-4470-8164-22f6918b58b8","extensionElements":{"postAssignmentScript":["console.clear();\r\nconsole.dir(bpmext.ui.getInvalidViews());"],"nodeVisualInfo":[{"color":"#95D087","width":95,"x":261,"y":23,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Validation","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMessage = \"\";\r\nvar mandatoryTriggered = false;\r\nvar tempLength = 0;\r\ntw.local.invalidTabs = [];\r\ntw.system.coachValidation.clearValidationErrors();\r\n\r\n\/\/\/\/-------------------------------------------BASIC DETAILS VALIDATION -----------------------------------\r\nmandatory(                                 \r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo,\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate,\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef,\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate,\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate\"\r\n);\r\n\r\nmaxLength(\r\n\ttw.local.odcRequest.BasicDetails.flexCubeContractNo,\r\n\t\"tw.local.odcRequest.BasicDetails.flexCubeContractNo\",\r\n\t16,\r\n\ttw.resource.ValidationMessages.MaxLength16,\r\n\t\"Flex Cube Contract Number: \" + tw.resource.ValidationMessages.MaxLength16\r\n);\r\n\/\/add mess 160 to local file\r\nmaxLength(\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\",\r\n\t160,\r\n\t\"Shouldn't be more than 160 character\",\r\n\t\"Commodity Description: \" + \"Shouldn't be more than 160 character\"\r\n);\r\nfor (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\n\tmaxLength(\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\n\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\",\r\n\t\t20,\r\n\t\t\"Shouldn't be more than 20 character\",\r\n\t\t\"invoice Number: \" + \"Shouldn't be more than 20 character\"\r\n\t);\r\n}\r\n\r\nfor (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceDate\"\r\n\t\t);\r\n\t}\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\"\r\n\t\t);\r\n\t}\r\n}\r\n\r\nfor (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Bills.length; i++) {\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingDate\"\r\n\t\t);\r\n\t}\r\n\tif (\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == \"\" ||\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == null\r\n\t) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef,\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingRef\"\r\n\t\t);\r\n\t}\r\n}\r\nvalidateTab(0, \"Basic Details Tab\");\r\n\/\/-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------\r\nmandatory(\r\n\ttw.local.odcRequest.FinancialDetailsBR.maxCollectionDate,\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate\"\r\n);\r\n\r\nminLength(\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\n\t2,\r\n\t\"Shouldn't be less than 14 character\",\r\n\t\"Amount Advanced: Shouldn't be less than 2 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\n\t14,\r\n\t\"Shouldn't be more than 14 character\",\r\n\t\"Amount Advanced:\" + \"Shouldn't be more than 14 character\"\r\n);\r\nvalidateTab(3, \"Financial Details - Branch Tab\");\r\n\/\/-------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------\r\nif (tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0) {\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.currency.name,\r\n\t\t\"tw.local.odcRequest.FcCollections.currency\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\n\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.fromDate,\r\n\t\t\"tw.local.odcRequest.FcCollections.fromDate\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.ToDate,\r\n\t\t\"tw.local.odcRequest.FcCollections.ToDate\"\r\n\t);\r\n\tmandatory(\r\n\t\ttw.local.odcRequest.FcCollections.accountNo.value,\r\n\t\t\"tw.local.odcRequest.FcCollections.accountNo\"\r\n\t);\r\n\r\n\tif (\r\n\t\ttw.local.odcRequest.FcCollections != null &amp;&amp;\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate != null\r\n\t) {\r\n\t\tminLength(\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\n\t\t\t6,\r\n\t\t\t\"Shouldn't be less than 6 character\",\r\n\t\t\t\" Negotiated Exchange Rate: Shouldn't be less than 6 character\"\r\n\t\t);\r\n\t\tmaxLength(\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\n\t\t\t10,\r\n\t\t\t\"Shouldn't be more than 10 character\",\r\n\t\t\t\" Negotiated Exchange Rate:\" + \"Shouldn't be more than 10 character\"\r\n\t\t);\r\n\t}\r\n\r\n\tvalidateTab(4, \"Flexcube collections Tab\");\r\n}\r\n\/\/-----------------------------------------Financial Details - Trade FO VALIDATION ----------------------------\r\n\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.discount,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.discount\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.extraCharges,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.extraCharges\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.ourCharges,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.ourCharges\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\"\r\n);\r\n\r\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\") {\r\n\tif (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length == 0) {\r\n\t\tmandatory(\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates,\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates\"\r\n\t\t);\r\n\t\ttw.local.errorMessage +=\r\n\t\t\t\"&lt;li&gt;\" + \"Fill in at least one entry in Multi Tenordates\" + \"&lt;\/li&gt;\";\r\n\t}\r\n}\r\n\r\nvar sum = 0;\r\n\r\nfor (var i = 0; i &lt; tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length; i++) {\r\n\tif (\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == \"\" ||\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == null\r\n\t) {\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\",\r\n\t\t\t\"Mandatory field\"\r\n\t\t);\r\n\t} else if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount != \"\") {\r\n\t\tcheckNegativeValue(\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount,\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\"\r\n\t\t);\r\n\t}\r\n\tif (\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == \"\" ||\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == null\r\n\t) {\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].date\",\r\n\t\t\t\"Mandatory field\"\r\n\t\t);\r\n\t}\r\n\tsum = sum + tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount;\r\n}\r\n\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\"\r\n);\r\ncheckNegativeValue(\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\"\r\n);\r\n\r\nif (\r\n\t!!tw.local.odcRequest.FinancialDetailsFO.collectableAmount &amp;&amp;\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; 0\r\n)\r\n\tif (\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount != null &amp;&amp;\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; sum &amp;&amp;\r\n\t\ttw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\"\r\n\t) {\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\",\r\n\t\t\t\"Sum of all Installment Amounts in a request must be &lt;= Amount Collectable by NBE (\u0627\u062c\u0645\u0627\u0644\u0649 \u0627\u0644\u0645\u0628\u0627\u0644\u063a \u0627\u0644\u0645\u0637\u0644\u0648\u0628 \u062a\u062d\u0635\u064a\u0644\u0647\u0627)\"\r\n\t\t);\r\n\t\ttw.system.coachValidation.addValidationError(\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length -1 ].date\",\r\n\t\t\t\"Mandatory field\"\r\n\t\t);\r\n\t}\r\n\r\nif (\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization &gt; 0 &amp;&amp;\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization &gt; 0\r\n) {\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\n\t\t\"Partial Avalization isn't allowed\"\r\n\t);\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\n\t\t\"Partial Avalization isn't allowed\"\r\n\t);\r\n}\r\nvar sumAvalization = parseFloat(0);\r\nsumAvalization =\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization +\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization +\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight;\r\nif (sumAvalization != tw.local.odcRequest.FinancialDetailsFO.collectableAmount) {\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\n\t);\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\n\t);\r\n\ttw.system.coachValidation.addValidationError(\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\",\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\n\t);\r\n}\r\nmandatory(\r\n\ttw.local.odcRequest.FinancialDetailsFO.maturityDate,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.maturityDate\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity,\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\"\r\n);\r\n\r\n\/\/--------------------------------------------Importer Details VALIDATION ----------------------------------\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\"\r\n);\r\nmandatory(tw.local.odcRequest.ImporterDetails.bank, \"tw.local.odcRequest.ImporterDetails.bank\");\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\"\r\n);\r\n\r\nmandatory(\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\"\r\n);\r\n\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\",\r\n\t250,\r\n\t\"Shouldn't be more than 250 character\",\r\n\t\"Importer Name:\" + \"Shouldn't be more than 250 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\",\r\n\t400,\r\n\t\"Shouldn't be more than 400 character\",\r\n\t\"Importer Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.importerPhoneNo,\r\n\t\"tw.local.odcRequest.ImporterDetails.importerPhoneNo\",\r\n\t20,\r\n\t\"Shouldn't be more than 20 character\",\r\n\t\"Importer Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.bank,\r\n\t\"tw.local.odcRequest.ImporterDetails.bank\",\r\n\t250,\r\n\t\"Shouldn't be more than 250 character\",\r\n\t\"Importer Bank:\" + \"Shouldn't be more than 250 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\",\r\n\t11,\r\n\t\"Shouldn't be more than 11 character\",\r\n\t\"Importer Bank BIC Code:\" + \"Shouldn't be more than 11 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.ibanAccount,\r\n\t\"tw.local.odcRequest.ImporterDetails.ibanAccount\",\r\n\t40,\r\n\t\"Shouldn't be more than 40 character\",\r\n\t\"Importer Account(IBAN):\" + \"Shouldn't be more than 40 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\",\r\n\t400,\r\n\t\"Shouldn't be more than 400 character\",\r\n\t\"Importer Bank Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.bankPhoneNo,\r\n\t\"tw.local.odcRequest.ImporterDetails.bankPhoneNo\",\r\n\t20,\r\n\t\"Shouldn't be more than 20 character\",\r\n\t\"Importer Bank Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ImporterDetails.collectingBankReference,\r\n\t\"tw.local.odcRequest.ImporterDetails.collectingBankReference\",\r\n\t30,\r\n\t\"Shouldn't be more than 30 character\",\r\n\t\"Collecting Bank Reference:\" + \"Shouldn't be more than 30 character\"\r\n);\r\nvalidateTab(6, \"Importer Details Tab\");\r\n\r\n\/\/-----------------------------------------Contract Creation VALIDATION -------------------------------------\r\n\r\n\r\nvalidateString(\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\n);\r\nmaxLength(\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\",\r\n\t16,\r\n\t\"Shouldn't be more than 16 character\",\r\n\t\"Contract Creation User Reference\" + \"Shouldn't be more than 16 character\"\r\n);\r\n\r\nvalidateTab(8, \"Contract Creation Tab\");\r\n\/\/---------------------------------------------\/\/Charges and Commissions VALIDATION -------------------------------------\r\nfor (var i = 0; i &lt; tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\n\t\/\/Description - Flat Amount\r\n\tif (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount &lt; 0 ||\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null\r\n\t\t) {\r\n\t\t\taddError(\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\n\t\t\t\t\"Must be &gt;= 0\"\r\n\t\t\t);\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0) {\r\n\t\t\tvalidateDecimal2(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t\/\/Fixed Rate\r\n\t} else {\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage &lt; 0 ||\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null\r\n\t\t) {\r\n\t\t\taddError(\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\n\t\t\t\t\"Must be &gt;= 0\"\r\n\t\t\t);\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage &gt; 0) {\r\n\t\t\tvalidateDecimal2(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\n\t\t\t);\r\n\t\t}\r\n\t}\r\n\r\n\t\/\/skip validation if waiver or changeAmnt &lt; 0\r\n\tif (\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].waiver == false &amp;&amp;\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0\r\n\t) {\r\n\t\t\/\/GL Account\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value ==\r\n\t\t\t\"GL Account\"\r\n\t\t) {\r\n\t\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\n\t\t\t\taddError(\r\n\t\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\t\ti +\r\n\t\t\t\t\t\t\"].debitedAccount.glAccountNo\",\r\n\t\t\t\t\t\"GL Account Not Verified\"\r\n\t\t\t\t);\r\n\t\t\t}\r\n\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.glAccountNo\"\r\n\t\t\t);\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.currency.value\"\r\n\t\t\t);\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.branchCode\"\r\n\t\t\t);\r\n\r\n\t\t\t\/\/Customer Account\r\n\t\t} else {\r\n\t\t\tmandatory(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount\r\n\t\t\t\t\t.customerAccountNo,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAccount.customerAccountNo\"\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t\/\/DebitedAmount\r\n\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate &gt; 0) {\r\n\t\t\tvalidateDecimal(\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate,\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAmount.negotiatedExRate\",\r\n\t\t\t\t\"Must be Decimal(16,10)\"\r\n\t\t\t);\r\n\t\t}\r\n\r\n\t\t\/\/Correct Validation but Waiting confirmation on what to do if GL account\r\n\t\tif (\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount &gt;\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance &amp;&amp;\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false\r\n\t\t) {\r\n\t\t\taddError(\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\n\t\t\t\t\ti +\r\n\t\t\t\t\t\"].debitedAmount.amountInAccount\",\r\n\t\t\t\t\"ERROR: Must be &lt;= Account Balance\"\r\n\t\t\t);\r\n\t\t}\r\n\t}\r\n}\r\nvalidateTab(9, \"Charges and commissions Tab\");\r\n\/\/---------------------------------------------\/\/Parties VALIDATION -------------------------------------\r\n\/\/\/\/\/Drawer Section\r\nmandatory(tw.local.odcRequest.Parties.Drawer.partyId, \"tw.local.odcRequest.Parties.Drawer.partyId\");\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.Drawer.partyName,\r\n\t\"tw.local.odcRequest.Parties.Drawer.partyName\"\r\n);\r\n\r\n\/\/\/\/\/Drawee Section\r\nmandatory(tw.local.odcRequest.Parties.Drawee.partyId, \"tw.local.odcRequest.Parties.Drawee.partyId\");\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.Drawee.partyName,\r\n\t\"tw.local.odcRequest.Parties.Drawee.partyName\"\r\n);\r\n\r\n\/\/\/\/\/Parties Types (Accountee)\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.partyTypes.partyId,\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyId\"\r\n);\r\nmandatory(\r\n\ttw.local.odcRequest.Parties.partyTypes.partyName,\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyName\"\r\n);\r\n\r\nvalidateTab(10, \"Parties Tab\");\r\n\/\/---------------------------------------------------------------------------------\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\n\tfromMandatory &amp;&amp; mandatoryTriggered\r\n\t\t? \"\"\r\n\t\t: (tw.local.errorMessage += \"&lt;li&gt;\" + validationMessage + \"&lt;\/li&gt;\");\r\n}\r\n\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\n\tif (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len) {\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\n\tif (field.length &gt; len) {\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction mandatory(field, fieldName, message) {\r\n\tif (!message) {\r\n\t\tmessage = camelCaseToTitle(fieldName) + \" is Mandatory\";\r\n\t}\r\n\r\n\tif (field == null || field == undefined) {\r\n\t\taddError(fieldName, message, message, true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t} else {\r\n\t\tswitch (typeof field) {\r\n\t\t\tcase \"string\":\r\n\t\t\t\tif (\r\n\t\t\t\t\tfield.trim() != undefined &amp;&amp;\r\n\t\t\t\t\tfield.trim() != null &amp;&amp;\r\n\t\t\t\t\tfield.trim().length == 0\r\n\t\t\t\t) {\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\t\t\tcase \"number\":\r\n\t\t\t\tif (field == 0.0) {\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tif (field &lt; 0) {\r\n\t\t\t\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\n\t\t\t\t\taddError(fieldName, msg, msg, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t\t\tbreak;\r\n\r\n\t\t\tdefault:\r\n\t\t\t\t\/\/ VALIDATE DATE OBJECT\r\n\t\t\t\tif (field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime())) {\r\n\t\t\t\t} else {\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\n\t\t\t\t\tmandatoryTriggered = true;\r\n\t\t\t\t\treturn false;\r\n\t\t\t\t}\r\n\t\t}\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction sumDates(fromDate, toDate, fieldName, controlMessage, validationMessage) {\r\n\tif (toDate - fromDate == 0) {\r\n\t\treturn true;\r\n\t}\r\n\taddError(fieldName, controlMessage, validationMessage);\r\n\treturn false;\r\n}\r\n\/\/=========================================================\r\nfunction checkNegativeValue(field, fieldName) {\r\n\tif (field &lt; 0) {\r\n\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\n\t\taddError(fieldName, msg, msg, true);\r\n\t\tmandatoryTriggered = true;\r\n\t\treturn false;\r\n\t}\r\n\treturn true;\r\n}\r\n\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\n\tvar decimalPattern = \/^\\d{1,4}(\\.\\d{1,6})?$\/;\r\n\tif (!decimalPattern.test(field)) {\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t} else {\r\n\t\treturn true;\r\n\t}\r\n}\r\n\/\/========================================================================================\r\nfunction validateTab(index, tabName) {\r\n\tif (tw.system.coachValidation.validationErrors.length &gt; tempLength) {\r\n\t\tif (tw.local.errorMessage.length == 0) {\r\n\t\t\ttw.local.errorMessage +=\r\n\t\t\t\t\"&lt;p&gt;\" + \"Please complete fields in the following tabs:\" + \"&lt;\/p&gt;\";\r\n\t\t}\r\n\t\ttw.local.errorMessage += \"&lt;li&gt;\" + tabName + \"&lt;\/li&gt;\";\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length;\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\n\t}\r\n}\r\n\/\/==============================================================\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\n\tvar decimalPattern = \/^\\d{1,12}(\\.\\d{1,2})?$\/;\r\n\tif (!decimalPattern.test(field)) {\r\n\t\t\/\/ Decimal is valid\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\n\t\treturn false;\r\n\t} else {\r\n\t\t\/\/ Decimal is invalid\r\n\t\treturn true;\r\n\t}\r\n}\r\n\r\nfunction camelCaseToTitle(camelCase) {\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\n\t\/\/ Convert camelCase to Title Case\r\n\tvar titleCase = fieldName.replace(\/([A-Z])\/g, \" $1\");\r\n\t\/\/ Uppercase the first character\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\n\treturn titleCase;\r\n}\r\n\r\nfunction validateString(field, fieldName) {\r\n      if (!field) return;\r\n\t\/\/ Regular expression to match only characters (letters)\r\n\tvar regex = \/^[a-zA-Z]+$\/;\r\n\r\n\t\/\/ Test if the inputString matches the regex\r\n      if (regex.test(field)) {\r\n\t\treturn true;\r\n\t} else {\r\n\t\taddError(fieldName, \"Numbers aren't allowed\", \"Numbers aren't allowed\");\r\n\t\treturn false;\r\n\t}\r\n}\r\n\/\/=================================================================================\r\ntw.local.errorMessage != null\r\n\t? (tw.local.errorPanelVIS = \"EDITABLE\")\r\n\t: (tw.local.errorPanelVIS = \"NONE\");\r\n\/\/=================================================================================\r\n"]}},{"outgoing":["2027.64964c28-a358-40ac-89dd-4f3c8453f4fd"],"incoming":["2027.04017f08-d5e0-446b-8673-524fd01f95e7"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition"}],"extensionElements":{"default":["2027.894a9c37-37a6-4ce7-8330-51f32d9c3104"],"nodeVisualInfo":[{"width":24,"x":78,"y":-42,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"Postpone","declaredType":"intermediateThrowEvent","id":"2025.5de5922f-74f9-4f78-83a8-4955a265ceaa"},{"targetRef":"2025.c83fdd58-5656-45c1-88be-786d246161b5","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Valid?","declaredType":"sequenceFlow","id":"2027.b683912e-2ca5-4470-8164-22f6918b58b8","sourceRef":"2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7"},{"targetRef":"2025.4695605b-49e8-461c-8104-b8bc0e52442d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Screen","declaredType":"sequenceFlow","id":"2027.64964c28-a358-40ac-89dd-4f3c8453f4fd","sourceRef":"2025.5de5922f-74f9-4f78-83a8-4955a265ceaa"},{"targetRef":"2025.4695605b-49e8-461c-8104-b8bc0e52442d","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  &gt;\t  0"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Screen","declaredType":"sequenceFlow","id":"2027.d832440b-ec84-4d61-840a-c6a1e40396d9","sourceRef":"2025.c83fdd58-5656-45c1-88be-786d246161b5"},{"targetRef":"2025.5de5922f-74f9-4f78-83a8-4955a265ceaa","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"60d64e04-001d-4515-9834-e7d4116fa8b5","coachEventPath":"DC_Templete1\/saveState"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Postpone","declaredType":"sequenceFlow","id":"2027.04017f08-d5e0-446b-8673-524fd01f95e7","sourceRef":"2025.4695605b-49e8-461c-8104-b8bc0e52442d"},{"targetRef":"2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"aed447f0-6b9e-43bd-8d65-bd136cad5f16","coachEventPath":"DC_Templete1\/submit"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352","sourceRef":"2025.4695605b-49e8-461c-8104-b8bc0e52442d"},{"incoming":["2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":516,"y":47,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"Default","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions"}]},"name":"End","declaredType":"endEvent","id":"2025.3ff0b976-5220-4b64-80db-00db7f9f28c4"},{"targetRef":"2025.3ff0b976-5220-4b64-80db-00db7f9f28c4","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.system.coachValidation.validationErrors.length\t  ==\t  0"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0","sourceRef":"2025.c83fdd58-5656-45c1-88be-786d246161b5"},{"outgoing":["2027.8a760579-2363-4b05-8732-923cffbbb6d7"],"incoming":["2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21","2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":858,"y":247,"declaredType":"TNodeVisualInfo","height":70}],"validationStayOnPagePaths":["okbutton"]},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask","isHeritageCoach":false,"cachePage":false,"startQuantity":1,"formDefinition":{"coachDefinition":{"layout":{"layoutItem":[{"layoutItemId":"test_view1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"8f5bc17f-751a-4ff1-8d45-856e2ff1582f","optionName":"@label","value":"test view"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"db47fb73-0c4a-43f1-8a84-8f3bff4bd773","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"fbd4b89c-ed9a-4fd2-87c9-fb63bc8ef7ed","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.98459c6f-cb8f-462d-9fae-63d331db4606","binding":"tw.local.deliveryterms","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"65ef7b8c-0fe4-4821-824a-0cb002a1587e","version":"8550"},{"layoutItemId":"test_view_21","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7fbeb4b8-4a79-4d45-8cf9-bff28c7ebe15","optionName":"@label","value":"test view 2"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"140204a9-1f55-4d0e-8d85-b3cd3e4d0199","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"1e56ed9b-e6dc-438d-8c1f-8129dc096cad","optionName":"@labelVisibility","value":"SHOW"}],"viewUUID":"64.22a68a10-5120-47a7-bdbe-27efec0bd40b","binding":"tw.local.paymentTerms","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"fb636457-0963-4cd4-849b-828d977b3855","version":"8550"},{"layoutItemId":"Output_Text1","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"7567d6c8-acd8-4290-8cb0-4ca156cd1b05","optionName":"@label","value":"Display text"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"a9eed448-d760-4107-8876-d8dbed4041db","optionName":"@helpText","value":""},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"16cec4f5-3acc-434f-8b3a-28ce68e2c2a8","optionName":"@labelVisibility","value":"SHOW"},{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"207a4bcc-ef92-43de-8961-826feaffe9da","optionName":"colorStyle","value":"G"}],"viewUUID":"64.f634f22e-7800-4bd7-9f1e-87177acfb3bc","binding":"tw.local.errorMessage","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"5b56b0ed-0bab-40cb-85eb-c7d0748b907d","version":"8550"},{"layoutItemId":"okbutton","configData":[{"declaredType":"com.ibm.bpmsdk.model.coach.ConfigData","id":"f148d791-209a-4e36-8e0d-e2bf3b41cd2b","optionName":"@label","value":"OK"}],"viewUUID":"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36","declaredType":"com.ibm.bpmsdk.model.coach.ViewRef","id":"c1344b74-856b-44a5-892c-b49ad277e4bc"}]},"declaredType":"com.ibm.bpmsdk.model.coach.TCoachDefinition"}},"commonLayoutArea":0,"name":"Coach","isForCompensation":false,"completionQuantity":1,"id":"2025.06f37113-06bc-4948-8dc6-92db16860efb"},{"startQuantity":1,"outgoing":["2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21"],"incoming":["2027.8a760579-2363-4b05-8732-923cffbbb6d7"],"default":"2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21","extensionElements":{"nodeVisualInfo":[{"width":95,"x":1061,"y":248,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Client-Side Script","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.1830251f-d054-49e9-8954-7562c5ab62d9","scriptFormat":"text\/x-javascript","script":{"content":["\/\/ \/DC_Templete1\r\n\r\nvar lib = bpmext.ui.getView(\"\/test_view1\");\r\n\r\nlib.mandatory(tw.local.deliveryterms, \"tw.local.deliveryterms\");\r\n\r\n\r\nlib.getErrorList( tw.system.coachValidation );"]}},{"targetRef":"2025.06f37113-06bc-4948-8dc6-92db16860efb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21","sourceRef":"2025.1830251f-d054-49e9-8954-7562c5ab62d9"},{"outgoing":["2027.e0e8c13f-f566-4655-843a-caee6c5d70a5"],"incoming":["2027.290f916c-9015-4c0d-8e5f-d4883652dc5d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":443,"y":249,"declaredType":"TNodeVisualInfo","height":70}]},"declaredType":"callActivity","startQuantity":1,"default":"2027.e0e8c13f-f566-4655-843a-caee6c5d70a5","name":"Test Error","dataInputAssociation":[{"targetRef":"2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.input1"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"2025.34ee082d-7910-4a7a-8639-b97420edb475","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","declaredType":"TFormalExpression","content":["tw.local.output1"]}}],"sourceRef":["2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6","declaredType":"TFormalExpression","content":["tw.local.output2"]}}],"sourceRef":["2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0"]}],"calledElement":"1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8"},{"targetRef":"2025.4695605b-49e8-461c-8104-b8bc0e52442d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Create","declaredType":"sequenceFlow","id":"2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7","sourceRef":"a7b2f73c-28cf-4a6a-9993-f252e17ae70f"},{"targetRef":"2025.1830251f-d054-49e9-8954-7562c5ab62d9","extensionElements":{"coachEventBinding":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding","id":"e7eeff5e-7a0b-46c4-8b9f-6eac4bc4a3f4","coachEventPath":"okbutton"}],"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":true,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Client-Side Script","declaredType":"sequenceFlow","id":"2027.8a760579-2363-4b05-8732-923cffbbb6d7","sourceRef":"2025.06f37113-06bc-4948-8dc6-92db16860efb"},{"targetRef":"2025.091f583e-7ba7-4397-816c-2bcd94163432","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Client-Side Script 1","declaredType":"sequenceFlow","id":"2027.e0e8c13f-f566-4655-843a-caee6c5d70a5","sourceRef":"2025.34ee082d-7910-4a7a-8639-b97420edb475"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"input1","isCollection":false,"declaredType":"dataObject","id":"2056.b54e68ad-04e6-478c-86d2-305a2a83f840"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"declaredType":"dataObject","id":"2056.32167ef1-4fa5-466f-8702-a225f50215cd"},{"itemSubjectRef":"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be","name":"output1","isCollection":false,"declaredType":"dataObject","id":"2056.bdcb8985-ae7b-401f-8383-4ce216166eeb"},{"itemSubjectRef":"itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6","name":"output2","isCollection":true,"declaredType":"dataObject","id":"2056.b913fb7d-1bbc-4edf-8420-c7b85912449e"},{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.e2568422-f163-4dbd-8f2c-a782918ddeb3"],"isInterrupting":true,"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition"}],"extensionElements":{"default":["2027.e2568422-f163-4dbd-8f2c-a782918ddeb3"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":50,"y":200,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"2025.debb1ad5-4b9a-4d03-860c-09cb0a594169"},{"incoming":["2027.b2ae1e6e-b5ff-4332-8f78-193de087a034"],"eventDefinition":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition"}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":700,"y":200,"declaredType":"TNodeVisualInfo","height":24}],"navigationInstructions":[{"targetType":"ToOtherDashboard","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions","targetURL":"tw.system.url.bpmDataEndpoint"}]},"name":"Stay on page","declaredType":"intermediateThrowEvent","id":"2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c"},{"startQuantity":1,"outgoing":["2027.b2ae1e6e-b5ff-4332-8f78-193de087a034"],"incoming":["2027.e2568422-f163-4dbd-8f2c-a782918ddeb3"],"default":"2027.b2ae1e6e-b5ff-4332-8f78-193de087a034","extensionElements":{"nodeVisualInfo":[{"width":95,"x":353,"y":175,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Client-Side Script 2","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.060a5457-7180-4157-85b0-242ad81d0129","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.errorMessage = String( tw.error.data )\r\nconsole.log(\"&lt;&lt;ERROR&gt;&gt;\");\r\nconsole.log(tw.error)\r\n\r\ntw.system.url."]}},{"targetRef":"2025.060a5457-7180-4157-85b0-242ad81d0129","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Client-Side Script 2","declaredType":"sequenceFlow","id":"2027.e2568422-f163-4dbd-8f2c-a782918ddeb3","sourceRef":"2025.debb1ad5-4b9a-4d03-860c-09cb0a594169"},{"targetRef":"2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Stay on page","declaredType":"sequenceFlow","id":"2027.b2ae1e6e-b5ff-4332-8f78-193de087a034","sourceRef":"2025.060a5457-7180-4157-85b0-242ad81d0129"}],"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":687,"y":77,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Error","triggeredByEvent":true,"isForCompensation":false,"completionQuantity":1,"declaredType":"subProcess","id":"2025.7f290839-c94b-4fcd-8099-fe07b5ec75c5"},{"startQuantity":1,"outgoing":["2027.290f916c-9015-4c0d-8e5f-d4883652dc5d"],"default":"2027.290f916c-9015-4c0d-8e5f-d4883652dc5d","extensionElements":{"nodeVisualInfo":[{"width":95,"x":264,"y":250,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Create","isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8","calledElement":"1.5d77055c-98a8-4191-9b74-c7120a5823be"},{"targetRef":"2025.34ee082d-7910-4a7a-8639-b97420edb475","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.290f916c-9015-4c0d-8e5f-d4883652dc5d","sourceRef":"2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8"},{"startQuantity":1,"outgoing":["2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e"],"incoming":["2027.e0e8c13f-f566-4655-843a-caee6c5d70a5"],"default":"2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e","extensionElements":{"nodeVisualInfo":[{"width":95,"x":652,"y":247,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Client-Side Script 1","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"2025.091f583e-7ba7-4397-816c-2bcd94163432","scriptFormat":"text\/x-javascript","script":{"content":["tw.system."]}},{"targetRef":"2025.06f37113-06bc-4948-8dc6-92db16860efb","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Coach","declaredType":"sequenceFlow","id":"2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e","sourceRef":"2025.091f583e-7ba7-4397-816c-2bcd94163432"}],"htmlHeaderTag":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag","id":"6d0ba8e0-0d58-434a-89fa-335d347a4c0b","tagName":"viewport","content":"width=device-width,initial-scale=1.0","enabled":true}],"isClosed":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation","id":"dbaf8d1e-a616-49a6-8ba6-e0f6b619f57f","processType":"None"}],"exposedAs":["NotExposed"]},"implementation":"##unspecified","documentation":[{"textFormat":"text\/plain"}],"name":"ACT04 - ODC Execution Hub Initiation","declaredType":"globalUserTask","id":"1.9321de6e-e6a5-435e-accb-0fbbd129c48a","ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.021ed4ee-8b5e-423d-8672-bdcaceed27d8"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"compApprovalInit","isCollection":false,"id":"2055.*************-426c-8701-7fc1c235c6a3"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"id":"2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991"}],"extensionElements":{"localizationResourceLinks":[{"resourceRef":[{"resourceBundleGroupID":"50.72059ba3-20f9-4926-b151-02b418301dd4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.31cfd076-ef2f-4737-85d1-a256beb27f48"},{"resourceBundleGroupID":"50.41101508-d2e4-4682-b3ef-b9b22266bb5a","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef","id":"69.217edee5-1f2d-4b8b-8502-8576e027daf4"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks"}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"bb759592-f2f7-40f3-8d2c-1489ab500ca9","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.bed40437-f5de-4b1c-a063-7040de4075df","epvProcessLinkId":"a32c52e1-a276-4e4d-81c2-cd41ab5bc551","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.86dc5c1d-931d-46d2-9be1-12397cc9f048","epvProcessLinkId":"9cd57e59-b7e1-4482-8c4c-588e29e57de4","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.769dc134-1d15-4dd4-a967-c5f61cf352dc","epvProcessLinkId":"2d23c808-1ed4-4142-8ddb-d0dcba7601f2","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.f79b6325-0b4a-48cd-b342-f9a61300eace","epvProcessLinkId":"0632eb96-065b-4557-887c-01e27486455e","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"3171034a-3c65-4e0d-8aee-052ffb8cbeba","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"id":"20372ac9-5465-4e48-b85b-6617c2365423"}],"outputSet":[{"id":"0c19fa57-ebfe-46ea-a3b8-02de562e4688"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = {};\nautoObject.initiator = \"\";\nautoObject.requestNature = {};\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = {};\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new Date();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = {};\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = {};\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = {};\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = {};\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = {};\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = {};\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = {};\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = {};\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = {};\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = {};\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = [];\nautoObject.BasicDetails.Bills[0] = {};\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\nautoObject.BasicDetails.Invoice = [];\nautoObject.BasicDetails.Invoice[0] = {};\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\nautoObject.GeneratedDocumentInfo = {};\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = [];\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = {};\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = {};\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = {};\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = [];\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = {};\nautoObject.FcCollections.currency = {};\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new Date();\nautoObject.FcCollections.ToDate = new Date();\nautoObject.FcCollections.accountNo = {};\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = [];\nautoObject.FcCollections.retrievedTransactions[0] = {};\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = [];\nautoObject.FcCollections.selectedTransactions[0] = {};\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = [];\nautoObject.FcCollections.listOfAccounts[0] = {};\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = {};\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new Date();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = {};\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = [];\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.FinancialDetailsFO.multiTenorDates[0].rebate = 0.0;\nautoObject.FinancialDetailsFO.multiTenorDates[0].matDate = new Date();\nautoObject.FinancialDetailsFO.multiTenorDates[0].tenorDays = 0;\nautoObject.FinancialDetailsFO.rebate = 0.0;\nautoObject.ImporterDetails = {};\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = {};\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = {};\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new Date();\nautoObject.ProductShipmentDetails.shipmentMethod = {};\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = {};\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = {};\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = {};\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = {};\nautoObject.ContractCreation.productCode = {};\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new Date();\nautoObject.ContractCreation.valueDate = new Date();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new Date();\nautoObject.Parties = {};\nautoObject.Parties.Drawer = {};\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = {};\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = {};\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = {};\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = {};\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.Parties.caseInNeed = {};\nautoObject.Parties.caseInNeed.partyCIF = \"\";\nautoObject.Parties.caseInNeed.partyId = \"\";\nautoObject.Parties.caseInNeed.partyName = \"\";\nautoObject.Parties.caseInNeed.country = \"\";\nautoObject.Parties.caseInNeed.language = \"\";\nautoObject.Parties.caseInNeed.refrence = \"\";\nautoObject.Parties.caseInNeed.address1 = \"\";\nautoObject.Parties.caseInNeed.address2 = \"\";\nautoObject.Parties.caseInNeed.address3 = \"\";\nautoObject.Parties.caseInNeed.partyType = {};\nautoObject.Parties.caseInNeed.partyType.name = \"\";\nautoObject.Parties.caseInNeed.partyType.value = \"\";\nautoObject.ChargesAndCommissions = [];\nautoObject.ChargesAndCommissions[0] = {};\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ChargesAndCommissions[0].isGLFound = false;\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\nautoObject.ContractLiquidation = {};\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new Date();\nautoObject.ContractLiquidation.creditValueDate = new Date();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.debitedAccountName = \"\";\nautoObject.ContractLiquidation.creditedAccount = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = {};\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = {};\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = {};\nautoObject.stepLog.startTime = new Date();\nautoObject.stepLog.endTime = new Date();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = [];\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = {};\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = {};\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = [];\nautoObject.attachmentDetails.attachment[0] = {};\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = [];\nautoObject.complianceComments[0] = {};\nautoObject.complianceComments[0].startTime = new Date();\nautoObject.complianceComments[0].endTime = new Date();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = [];\nautoObject.History[0] = {};\nautoObject.History[0].startTime = new Date();\nautoObject.History[0].endTime = new Date();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = {};\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject.folderPath = \"\";\nautoObject.templateDocID = \"\";\nautoObject.requestID = 0;\nautoObject.customerAndPartyAccountList = [];\nautoObject.customerAndPartyAccountList[0] = {};\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\nautoObject.customerAndPartyAccountList[0].frozen = false;\nautoObject.customerAndPartyAccountList[0].dormant = false;\nautoObject.customerAndPartyAccountList[0].noDebit = false;\nautoObject.customerAndPartyAccountList[0].noCredit = false;\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\nautoObject.tradeFoComment = \"\";\nautoObject.exeHubMkrComment = \"\";\nautoObject.compcheckerComment = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"regeneratedRemittanceLetterTitleVIS","isCollection":false,"id":"2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"fromExeChecker","isCollection":false,"id":"2055.21028eba-aa2f-4b93-806b-78b695c14ddf"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"bd4929e7-306f-4912-abfc-c1274d288f81"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d</processParameterId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d2ee210b-0e47-4350-b5af-f9738e6e8078</guid>
            <versionId>4fbaf903-9454-4881-a8b7-8c2e648b8952</versionId>
        </processParameter>
        <processParameter name="regeneratedRemittanceLetterTitleVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e</processParameterId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a3ca1a79-77da-45f2-8e1e-50962818ab96</guid>
            <versionId>fbebf4e5-0d2a-446c-b5b9-9af9bb3276c5</versionId>
        </processParameter>
        <processParameter name="fromExeChecker">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.21028eba-aa2f-4b93-806b-78b695c14ddf</processParameterId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>91303e83-8846-47bd-908f-fc1d0775bbb1</guid>
            <versionId>bcacf624-9d6e-4aa0-8fe1-e3e34130d471</versionId>
        </processParameter>
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.021ed4ee-8b5e-423d-8672-bdcaceed27d8</processParameterId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ccadb9eb-115c-413d-8c65-720d0afcf397</guid>
            <versionId>55b5092d-bc7a-4b26-baf2-c206ff3b1df4</versionId>
        </processParameter>
        <processParameter name="compApprovalInit">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.*************-426c-8701-7fc1c235c6a3</processParameterId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>61819b6e-bcea-42a7-84de-fe6f1002d16d</guid>
            <versionId>fb8ac4fc-ca43-44cd-a1d5-fe4a62f5424b</versionId>
        </processParameter>
        <processParameter name="lastAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991</processParameterId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>15ca4c4b-f404-475b-88ab-549098cb8fb5</guid>
            <versionId>a9f8777e-37c9-4613-9517-72c95c16cd14</versionId>
        </processParameter>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b2b0bdc1-1032-4018-8978-24a052bafe51</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4738cffa-b0d6-4a69-be08-6fef20b18224</guid>
            <versionId>585fde88-f002-44a0-90d9-b597157438e5</versionId>
        </processVariable>
        <processVariable name="errorPanelVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9af9df34-0783-4778-9dbc-e75ae58da5b6</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f76067a8-ba16-4af7-83ef-0014af9b3ae1</guid>
            <versionId>566fe5c0-7450-416e-9d77-fd321b1380aa</versionId>
        </processVariable>
        <processVariable name="terminateReasonVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.7bd925a6-f30d-4822-83e0-1cf0ba29f6cb</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>eda0b748-0a47-4ffb-a720-2523049dcc85</guid>
            <versionId>fb3be65b-e719-4596-a815-22d21510d296</versionId>
        </processVariable>
        <processVariable name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.5d359bdf-d4b5-4dc0-877a-197f885477b6</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fda7f3b6-f1b4-4a07-8e25-24e5241e4133</guid>
            <versionId>d3f0a1a0-ffe8-49a6-91f2-c058a6fe319f</versionId>
        </processVariable>
        <processVariable name="deliveryterms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.843ee6c7-a857-4d2c-87b1-c6374e53fba3</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f5429ea1-2bc8-4d72-84f6-d462ea6a2826</guid>
            <versionId>28a8d20f-c206-4b26-adc5-1799ce2a3ca7</versionId>
        </processVariable>
        <processVariable name="paymentTerms">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ce89d2d4-ed8d-4d40-8b7d-0890d5ad74a6</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>586f4722-6118-40b8-957c-e432ea40fcc4</guid>
            <versionId>7708be25-4f00-4d3c-bee6-431f24df4713</versionId>
        </processVariable>
        <processVariable name="specialInstructions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d2579c3c-4e4a-4a1b-8e46-87a45d36bcba</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>7</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e3f9e340-247e-4c5c-a1dc-43d366fc764e</guid>
            <versionId>48a01f3f-f57f-4fe0-aabe-aa4d6830ecad</versionId>
        </processVariable>
        <processVariable name="instructions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2aaea123-b9bb-409d-8af1-2b3310bdc747</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>8</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3efcec6a-bad8-4e10-9ee2-25a560d071c0</guid>
            <versionId>236f9cf0-c74e-4934-8fab-d4e578da1dab</versionId>
        </processVariable>
        <processVariable name="flexCubeVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.72531c8e-7b17-45d5-883b-c9e2ca4e7092</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>9</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a7f30789-ee4a-484c-b0c4-a48d2de00aad</guid>
            <versionId>c4248def-e347-4b21-814e-3685903160fa</versionId>
        </processVariable>
        <processVariable name="requestTypeVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.4a865219-2b57-427b-8a39-7df701820161</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>10</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>8319df7a-314b-4baa-a593-ed352e54f138</guid>
            <versionId>03f89394-fe98-4dde-a5c8-1c29f82ad0b5</versionId>
        </processVariable>
        <processVariable name="contractStageVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a40cf44c-95f9-431c-863f-e3cf0e04ea9b</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>11</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ab90d1e8-85fc-4c07-b2f9-fe7c8223a9d9</guid>
            <versionId>8719079a-2329-4d91-b84f-88e8fac30c7e</versionId>
        </processVariable>
        <processVariable name="bankRefVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.096c2f16-3cbd-48c1-805b-960b7cf63ee0</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>12</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>40e9f77a-4197-43e8-aa34-e88d17871f5b</guid>
            <versionId>96e4a19a-f4b9-4b25-863b-0b190e4a4dc2</versionId>
        </processVariable>
        <processVariable name="event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.8be43605-ddb8-4a68-835f-f1ca3b1af4ab</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>13</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1cf11e9b-a49a-4eb8-b171-f93ae2838789</guid>
            <versionId>9b4df392-efe0-4963-97b8-94e62b6c8791</versionId>
        </processVariable>
        <processVariable name="currencyList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fae4f2a7-8281-424e-87e4-fa1fb316208c</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>14</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4f8c3da4-dc14-4be7-a7eb-909560c73b68</guid>
            <versionId>1a8afada-71f5-4337-83c5-38d78685df7a</versionId>
        </processVariable>
        <processVariable name="addchargeBtnVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.2785bfd8-f42e-49dc-8743-e5295f8753d9</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>15</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a6200f07-b94a-401d-b8c0-69c791f2cad8</guid>
            <versionId>73a70f7b-7b02-4fd5-b1a7-0f7c572dd5ef</versionId>
        </processVariable>
        <processVariable name="collectingBankInfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.dfe30b45-a52b-4f95-8a2d-6241995032cc</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>16</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>e99d0fe7-bf52-4fab-9b4b-0a6384cb46d6</guid>
            <versionId>4f447fa7-a009-4cbd-9a64-dde34f72b763</versionId>
        </processVariable>
        <processVariable name="partyTypesInfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.af9c4b39-6fcd-4d9f-8eca-f39dd5ab5369</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>17</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>efcaf508-73bf-4247-9b06-9be55f3811d2</guid>
            <versionId>be1739b4-03e0-4a2c-b19b-735390ffe285</versionId>
        </processVariable>
        <processVariable name="multiTenorVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f28609ef-8dde-4927-864f-fc323f694ba9</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>18</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2145ba58-964a-4de0-9d4a-f1c20183b582</guid>
            <versionId>db9a5a84-fbe3-43f0-8957-69b18b39168f</versionId>
        </processVariable>
        <processVariable name="PartyAccountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.640fa68e-cdb2-4dc0-842a-3d9b44690ddb</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>19</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>39bbc2fd-2e12-4ef9-a84f-00d17a4511af</guid>
            <versionId>0369353b-1a2c-4941-8007-0c35fb2d6cdd</versionId>
        </processVariable>
        <processVariable name="initialAccountList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1162617c-dd7d-4ede-83ca-f0ff3a73c4a9</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>20</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b8283e1f-a79e-4f66-a123-ceef60d5c30e</guid>
            <versionId>b0f9a062-bfc0-428d-82b9-3358a9f3e8bf</versionId>
        </processVariable>
        <processVariable name="parentRequestNumberVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.69e6eedd-1a2f-4d00-81d3-4be407a38938</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>21</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fc5a6bac-4ae4-4fdc-83df-e6cb7f375798</guid>
            <versionId>dccb05ad-24ec-4854-b913-d9f9b621c2e8</versionId>
        </processVariable>
        <processVariable name="x">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.d092fe95-e100-4c09-8a3c-ff77d4c19f7f</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>22</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>3dac9c4c-5556-43c3-a536-efb927ee2ba5</guid>
            <versionId>9e7628f6-5e11-4f46-b111-548e608d4e26</versionId>
        </processVariable>
        <processVariable name="drawerAccountsList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.9fea3a16-f7b0-4b82-8b84-e34715aaea53</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>23</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b4e59263-482c-41c6-a583-459b88bb2309</guid>
            <versionId>9f83aced-0e80-402c-9e0c-9007703c0306</versionId>
        </processVariable>
        <processVariable name="selectedIndex">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6ca322b0-f3cc-4e23-8c9c-38a72d23c824</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>24</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0acc9861-fb05-444f-b8aa-5760a2a7d9f4</guid>
            <versionId>080b71a7-0924-43d1-972a-95e373630c40</versionId>
        </processVariable>
        <processVariable name="fromCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0c3150de-f48c-4392-8745-78bf480ec637</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>25</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0c3ff73e-5c43-4510-8174-5ce125dc0696</guid>
            <versionId>78319e2d-6328-45eb-a2ef-86311962f74f</versionId>
        </processVariable>
        <processVariable name="toCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c575aebe-5805-45f1-8658-f4b61135671e</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>26</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>96edd91a-3afa-45c2-a78a-09500caaa32b</guid>
            <versionId>0fc58914-52f9-47a5-96bc-c20804a99c77</versionId>
        </processVariable>
        <processVariable name="rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6af17de0-5078-4f22-8c4b-993cf87cace9</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>27</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4d8a85ee-2925-4329-8e17-bd73490e58c3</guid>
            <versionId>e3667924-3efb-4237-95de-58955718f51c</versionId>
        </processVariable>
        <processVariable name="barCodePdf">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ed625282-4579-4a61-8ea5-612dbaeb5b82</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>28</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>647d7719-6b9f-41f2-ab07-************</guid>
            <versionId>260190a8-0b9b-4925-8dcf-50f175c86bd6</versionId>
        </processVariable>
        <processVariable name="generationStatus">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.066a7f3a-f039-4041-8f63-ea92395a16ec</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>29</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>d9f951ad-f1b2-4b41-a147-615f92307985</guid>
            <versionId>08a00a3d-7464-4a03-8f4e-b9d76777dd7c</versionId>
        </processVariable>
        <processVariable name="calculatedChangeAmnt">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b08e403d-8597-463f-8e22-c776688990c6</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>30</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>54f4b18a-20cb-4091-ae51-f7e8de5e998e</guid>
            <versionId>f104c5a5-6f70-4f6f-9be1-dafb754b7aa8</versionId>
        </processVariable>
        <processVariable name="customerAndPartyCifs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.474688e9-036c-4acd-858a-98a352832288</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>31</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>1c3baf45-0edd-48a8-805b-f6841366a8cf</guid>
            <versionId>dc8e7918-7f2f-4ad6-b367-e8d23e29039a</versionId>
        </processVariable>
        <processVariable name="customerFullDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.015a8404-6af2-4492-81ac-ced4c5cf575d</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>32</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a1526dd1-4d32-4cf4-a0f3-292e8e60afc4</guid>
            <versionId>181f1b7d-8d38-478c-8de5-cd7cabcfbd67</versionId>
        </processVariable>
        <processVariable name="addressBICList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.124649bc-dddd-4055-8621-bbd3938104d0</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>33</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>24cc12c0-2f0a-416f-be1c-0b39ab93acc5</guid>
            <versionId>ac3bf277-45d3-4abf-bd5a-13bdb5e6f621</versionId>
        </processVariable>
        <processVariable name="partyTypeName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.a7233099-da0d-429f-822c-bc1ed9a69ae3</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>34</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>67281079-303c-4d82-b662-f57619fc5a37</guid>
            <versionId>0c48fef4-51ef-4914-a0a9-e34f9ec37e6c</versionId>
        </processVariable>
        <processVariable name="todayDate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f0e1069c-61d1-4f97-89e6-d78c498604f1</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>35</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cf11e8fa-9dc0-4977-ab8b-c662788f0c2c</guid>
            <versionId>a4f6b194-087c-405f-8056-402f3bb9944b</versionId>
        </processVariable>
        <processVariable name="invalidTabs">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.918536a9-7d45-4725-857b-def837024050</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>36</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4506038a-acf1-4571-a6cc-a91119f19378</guid>
            <versionId>d04c940e-7471-4dc5-b53f-c648d481710d</versionId>
        </processVariable>
        <processVariable name="input1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b54e68ad-04e6-478c-86d2-305a2a83f840</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>37</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>c825d035-6244-4137-89e3-b92656b49e1f</guid>
            <versionId>7f4fa753-5968-402f-a923-3baa3d3fe42b</versionId>
        </processVariable>
        <processVariable name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.32167ef1-4fa5-466f-8702-a225f50215cd</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>38</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>73c97dca-114b-45f7-9228-f86eff74388e</guid>
            <versionId>310bf520-cc1f-40f8-8559-d5d98d25b794</versionId>
        </processVariable>
        <processVariable name="output1">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.bdcb8985-ae7b-401f-8383-4ce216166eeb</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>39</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a23cd5d9-2044-4fe5-b67c-9ee75d7e174d</guid>
            <versionId>d6eef00d-5bf2-4f93-827d-17c4bc87d7b1</versionId>
        </processVariable>
        <processVariable name="output2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b913fb7d-1bbc-4edf-8420-c7b85912449e</processVariableId>
            <description isNull="true" />
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <namespace>2</namespace>
            <seq>40</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>/12.90d4772d-4081-4a73-a8c2-e7f904511cd6</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>6522136b-8f42-44bd-8303-3cde3ec0df35</guid>
            <versionId>159ea413-afc8-42b1-9c9b-9da6ead6c178</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8</processItemId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <name>Create</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.7ad6fd9f-6cbf-4202-ada8-accc94964004</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:faf129f3cc554db6:5a40ed45:195332c2764:-3b7d</guid>
            <versionId>3ea1bfd0-0cb9-4565-b936-8d52d32d3fc2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.7ad6fd9f-6cbf-4202-ada8-accc94964004</subProcessId>
                <attachedProcessRef>/1.5d77055c-98a8-4191-9b74-c7120a5823be</attachedProcessRef>
                <guid>da9d07bd-4a07-415a-a7f7-08de72cde53b</guid>
                <versionId>ecc41f35-98b5-4129-bacf-eb340de8c205</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6b1114d2-2fd1-421a-bc5f-7dc264e40eb4</processItemId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.5a4089de-b5ef-4f50-a9ad-1eda71c823bb</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-6ca5</guid>
            <versionId>5e96b278-f47b-4ae3-808a-5b045963e081</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.5a4089de-b5ef-4f50-a9ad-1eda71c823bb</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>*************-452f-a375-c77ba7b80fd4</guid>
                <versionId>e91e95fb-43f2-4c80-bec9-0c05d4a33a93</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.34ee082d-7910-4a7a-8639-b97420edb475</processItemId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <name>Test Error</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.3bc1766e-13ff-4c65-93be-52e15083dd99</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f7f6553919972802:-74de7c2c:193f7b0288a:-604e</guid>
            <versionId>a446e410-475f-4e94-88a4-f911eedd1735</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.3bc1766e-13ff-4c65-93be-52e15083dd99</subProcessId>
                <attachedProcessRef>/1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8</attachedProcessRef>
                <guid>3a6cb695-0066-4c81-8492-5ba3ac40f646</guid>
                <versionId>93fa9656-40eb-48c1-8bbc-ca8972ea0a4c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599</processItemId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <name>CoachFlowWrapper</name>
            <tWComponentName>CoachFlow</tWComponentName>
            <tWComponentId>3032.82455cad-69f8-4d47-922a-f39d28c22a84</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-6ca7</guid>
            <versionId>e9df911d-d608-4cb0-bbf3-5ca440d2c799</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent />
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.0de9d3e9-**************-8edb7cfe26a9</epvProcessLinkId>
            <epvId>/21.f79b6325-0b4a-48cd-b342-f9a61300eace</epvId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>25e60df2-a8a1-40c3-963f-6877889659c9</guid>
            <versionId>15e41e4b-aa5e-44a6-ac71-96f4fdb36d15</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.f6959013-fd5d-40c4-b42b-c790abe785bd</epvProcessLinkId>
            <epvId>/21.bed40437-f5de-4b1c-a063-7040de4075df</epvId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>0e70bd04-f4b6-4a98-9e39-e05be9f99ef6</guid>
            <versionId>2822cd9b-6753-46bb-a064-f9cb0d5f7074</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.cda02f04-16b1-4bde-8754-b9aa144fb80c</epvProcessLinkId>
            <epvId>/21.769dc134-1d15-4dd4-a967-c5f61cf352dc</epvId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>e8f2a6e6-c646-489c-b6db-6fb53f8cc69c</guid>
            <versionId>4a8c5b94-a1da-43e6-b398-ea864206fb93</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.8f35d556-c161-45e8-8dfc-ca36711eed1f</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>42859e4c-08d9-458b-945f-c6e0d65bed6c</guid>
            <versionId>4addbe82-7906-4fb4-b919-7070fc07bb1b</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.c08c8bb9-8c4b-468f-93fe-7cfaf5a125c5</epvProcessLinkId>
            <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>b2ee3ba1-cd63-4fd0-972c-805dee53ae4e</guid>
            <versionId>730b152b-8b25-4fbe-864f-6aa1f72e8ba4</versionId>
        </EPV_PROCESS_LINK>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.cc8c8d43-5d7a-46de-8791-5588e2e6d0f9</epvProcessLinkId>
            <epvId>/21.86dc5c1d-931d-46d2-9be1-12397cc9f048</epvId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>f279a4b9-782b-427e-b445-18bade328f71</guid>
            <versionId>fb252735-87ce-4d94-b5b4-52619398eaac</versionId>
        </EPV_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.94373cb9-c19b-401c-8ce3-0e058a9af245</resourceProcessLinkId>
            <resourceBundleGroupId>/50.72059ba3-20f9-4926-b151-02b418301dd4</resourceBundleGroupId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>61d15ede-ff56-453c-9fe6-86d714f89a0a</guid>
            <versionId>363685cc-c2e9-456a-abdf-c5607f41a558</versionId>
        </RESOURCE_PROCESS_LINK>
        <RESOURCE_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <resourceProcessLinkId>2059.4cea5a13-bacb-498a-ae63-da89db440683</resourceProcessLinkId>
            <resourceBundleGroupId>/50.41101508-d2e4-4682-b3ef-b9b22266bb5a</resourceBundleGroupId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <guid>dc8c0a3c-864d-4f45-9e28-9e44da2ad533</guid>
            <versionId>c327f647-644d-49d7-a265-d17d01b77362</versionId>
        </RESOURCE_PROCESS_LINK>
        <startingProcessItemId>2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="0" y="0">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="rightCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <coachflow>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" id="bd4929e7-306f-4912-abfc-c1274d288f81" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript">
                <ns16:globalUserTask name="ACT04 - ODC Execution Hub Initiation" id="1.9321de6e-e6a5-435e-accb-0fbbd129c48a">
                    <ns16:documentation />
                    <ns16:extensionElements>
                        <ns3:userTaskImplementation id="dbaf8d1e-a616-49a6-8ba6-e0f6b619f57f">
                            <ns16:startEvent name="Start" id="a7b2f73c-28cf-4a6a-9993-f252e17ae70f">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="113" y="111" width="24" height="24" color="#F8F8F8" />
                                    <ns3:postAssignmentScript />
                                    <ns3:default>2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7</ns3:default>
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7</ns16:outgoing>
                            </ns16:startEvent>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.b2b0bdc1-1032-4018-8978-24a052bafe51" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorPanelVIS" id="2056.9af9df34-0783-4778-9dbc-e75ae58da5b6" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="terminateReasonVIS" id="2056.7bd925a6-f30d-4822-83e0-1cf0ba29f6cb" />
                            <ns16:dataObject itemSubjectRef="itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4" isCollection="false" name="actionConditions" id="2056.5d359bdf-d4b5-4dc0-877a-197f885477b6" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="deliveryterms" id="2056.843ee6c7-a857-4d2c-87b1-c6374e53fba3" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="paymentTerms" id="2056.ce89d2d4-ed8d-4d40-8b7d-0890d5ad74a6" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="specialInstructions" id="2056.d2579c3c-4e4a-4a1b-8e46-87a45d36bcba" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="instructions" id="2056.2aaea123-b9bb-409d-8af1-2b3310bdc747" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="flexCubeVIS" id="2056.72531c8e-7b17-45d5-883b-c9e2ca4e7092" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestTypeVIS" id="2056.4a865219-2b57-427b-8a39-7df701820161">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="contractStageVIS" id="2056.a40cf44c-95f9-431c-863f-e3cf0e04ea9b" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="bankRefVIS" id="2056.096c2f16-3cbd-48c1-805b-960b7cf63ee0" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="event" id="2056.8be43605-ddb8-4a68-835f-f1ca3b1af4ab" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="currencyList" id="2056.fae4f2a7-8281-424e-87e4-fa1fb316208c" />
                            <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="addchargeBtnVIS" id="2056.2785bfd8-f42e-49dc-8743-e5295f8753d9" />
                            <ns16:dataObject itemSubjectRef="itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a" isCollection="false" name="collectingBankInfo" id="2056.dfe30b45-a52b-4f95-8a2d-6241995032cc">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.cif = "";
autoObject.customerName = "";
autoObject.addressLine1 = "";
autoObject.addressLine2 = "";
autoObject.addressLine3 = "";
autoObject.customerSector = {};
autoObject.customerSector.name = "";
autoObject.customerSector.value = "";
autoObject.customerType = "";
autoObject.customerNoCBE = "";
autoObject.facilityType = {};
autoObject.facilityType.name = "";
autoObject.facilityType.value = "";
autoObject.commercialRegistrationNo = "";
autoObject.commercialRegistrationOffice = "";
autoObject.taxCardNo = "";
autoObject.importCardNo = "";
autoObject.initiationHub = "";
autoObject.country = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a" isCollection="false" name="partyTypesInfo" id="2056.af9c4b39-6fcd-4d9f-8eca-f39dd5ab5369">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.cif = "";
autoObject.customerName = "";
autoObject.addressLine1 = "";
autoObject.addressLine2 = "";
autoObject.addressLine3 = "";
autoObject.customerSector = {};
autoObject.customerSector.name = "";
autoObject.customerSector.value = "";
autoObject.customerType = "";
autoObject.customerNoCBE = "";
autoObject.facilityType = {};
autoObject.facilityType.name = "";
autoObject.facilityType.value = "";
autoObject.commercialRegistrationNo = "";
autoObject.commercialRegistrationOffice = "";
autoObject.taxCardNo = "";
autoObject.importCardNo = "";
autoObject.initiationHub = "";
autoObject.country = "";
autoObject</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="multiTenorVIS" id="2056.f28609ef-8dde-4927-864f-fc323f694ba9" />
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="PartyAccountsList" id="2056.640fa68e-cdb2-4dc0-842a-3d9b44690ddb" />
                            <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="initialAccountList" id="2056.1162617c-dd7d-4ede-83ca-f0ff3a73c4a9" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentRequestNumberVIS" id="2056.69e6eedd-1a2f-4d00-81d3-4be407a38938" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="x" id="2056.d092fe95-e100-4c09-8a3c-ff77d4c19f7f" />
                            <ns16:dataObject itemSubjectRef="itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42" isCollection="true" name="drawerAccountsList" id="2056.9fea3a16-f7b0-4b82-8b84-e34715aaea53" />
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="false" name="selectedIndex" id="2056.6ca322b0-f3cc-4e23-8c9c-38a72d23c824" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="fromCurrency" id="2056.0c3150de-f48c-4392-8745-78bf480ec637" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="toCurrency" id="2056.c575aebe-5805-45f1-8658-f4b61135671e" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="rate" id="2056.6af17de0-5078-4f22-8c4b-993cf87cace9" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="barCodePdf" id="2056.ed625282-4579-4a61-8ea5-612dbaeb5b82" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="generationStatus" id="2056.066a7f3a-f039-4041-8f63-ea92395a16ec" />
                            <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="calculatedChangeAmnt" id="2056.b08e403d-8597-463f-8e22-c776688990c6" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="customerAndPartyCifs" id="2056.474688e9-036c-4acd-858a-98a352832288" />
                            <ns16:dataObject itemSubjectRef="itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651" isCollection="false" name="customerFullDetails" id="2056.015a8404-6af2-4492-81ac-ced4c5cf575d" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="addressBICList" id="2056.124649bc-dddd-4055-8621-bbd3938104d0" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="partyTypeName" id="2056.a7233099-da0d-429f-822c-bc1ed9a69ae3" />
                            <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="todayDate" id="2056.f0e1069c-61d1-4f97-89e6-d78c498604f1">
                                <ns16:extensionElements>
                                    <ns3:defaultValue useDefault="true">new Date()</ns3:defaultValue>
                                </ns16:extensionElements>
                            </ns16:dataObject>
                            <ns16:dataObject itemSubjectRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d" isCollection="true" name="invalidTabs" id="2056.918536a9-7d45-4725-857b-def837024050" />
                            <ns3:formTask isHeritageCoach="false" cachePage="false" commonLayoutArea="0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Screen" id="2025.4695605b-49e8-461c-8104-b8bc0e52442d">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="65" y="24" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                    <ns3:preAssignmentScript />
                                    <ns3:postAssignmentScript />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.64964c28-a358-40ac-89dd-4f3c8453f4fd</ns16:incoming>
                                <ns16:incoming>2027.d832440b-ec84-4d61-840a-c6a1e40396d9</ns16:incoming>
                                <ns16:incoming>2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7</ns16:incoming>
                                <ns16:outgoing>2027.04017f08-d5e0-446b-8673-524fd01f95e7</ns16:outgoing>
                                <ns16:outgoing>2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>a4e967b5-84f5-44e8-86b0-0dcd259a0ccf</ns19:id>
                                                <ns19:layoutItemId>DC_Templete1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>7a160b51-fe6b-4a7a-824b-c66c05c6f693</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>DC Templete</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b1ed620c-d310-4ece-84fa-e12c0a26e65b</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>981204cd-12fb-4701-89c1-ef88e7e98c38</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>4a33ec08-3168-4539-8b54-276083cf66f3</ns19:id>
                                                    <ns19:optionName>stepLog</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>34a2bcf8-ef34-4ab0-88a5-30e63981c2fc</ns19:id>
                                                    <ns19:optionName>action</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.actions[]</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>399a5351-739a-42e7-811b-32d66d54847f</ns19:id>
                                                    <ns19:optionName>selectedAction</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.stepLog.action</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>5c4e7e20-d117-4fc0-889f-810bc997da20</ns19:id>
                                                    <ns19:optionName>complianceApproval</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.complianceApproval</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>3bd029f5-2465-4bac-8d5e-25454fc139ad</ns19:id>
                                                    <ns19:optionName>complianceApprovalVis</ns19:optionName>
                                                    <ns19:value>Editable</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a2e07ca0-29dc-4389-89f4-26a5c540266a</ns19:id>
                                                    <ns19:optionName>errorMsg</ns19:optionName>
                                                    <ns19:value>tw.local.errorMessage</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>14eb0410-73d6-4144-8a7e-d63aaa71ef47</ns19:id>
                                                    <ns19:optionName>errorPanelVIS</ns19:optionName>
                                                    <ns19:value>tw.local.errorPanelVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>839364a7-5d2b-46e9-84f2-9a948bcf5884</ns19:id>
                                                    <ns19:optionName>terminateReasonVIS</ns19:optionName>
                                                    <ns19:value>tw.local.terminateReasonVIS</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>04487a36-4d5d-4185-803a-e8d2168fdaf7</ns19:id>
                                                    <ns19:optionName>actionConditions</ns19:optionName>
                                                    <ns19:value>tw.local.actionConditions</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>0200fe69-af55-4577-8aae-426fbf618115</ns19:id>
                                                    <ns19:optionName>approvalCommentVIS</ns19:optionName>
                                                    <ns19:value>NONE</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>2092fe0a-7939-47cd-8f9e-6e4c00a8580c</ns19:id>
                                                    <ns19:optionName>tradeFoCommentVis</ns19:optionName>
                                                    <ns19:value>Readonly</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>b98e3d8d-3616-471e-8b60-a569760116f6</ns19:id>
                                                    <ns19:optionName>exeHubMkrCommentVis</ns19:optionName>
                                                    <ns19:value>Editable</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>f6735176-e7c7-4acf-8153-e5a13b0cfe3b</ns19:id>
                                                    <ns19:optionName>tradeFoComment</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.tradeFoComment</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>5fb9634b-99ac-4bfa-82d3-54895449b89e</ns19:id>
                                                    <ns19:optionName>exeHubMkrComment</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.exeHubMkrComment</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>bd99d6d6-d684-487e-8d45-ab8cdb620741</ns19:id>
                                                    <ns19:optionName>returnReasonVIS</ns19:optionName>
                                                    <ns19:value>Editable</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>191cf4de-3c5c-4ffc-85d8-fd41a9b64152</ns19:id>
                                                    <ns19:optionName>compcheckerComment</ns19:optionName>
                                                    <ns19:value>tw.local.odcRequest.compcheckerComment</ns19:value>
                                                    <ns19:valueType>dynamic</ns19:valueType>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>3d7aa9c2-68a4-45f8-8400-33430377893f</ns19:id>
                                                    <ns19:optionName>compcheckerCommentVIS</ns19:optionName>
                                                    <ns19:value>Readonly</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>5003054e-ba80-47d3-8a49-670101356da3</ns19:id>
                                                    <ns19:optionName>disableSubmit</ns19:optionName>
                                                    <ns19:value>false</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</ns19:viewUUID>
                                                <ns19:binding>tw.local.odcRequest.appInfo</ns19:binding>
                                                <ns19:contentBoxContrib>
                                                    <ns19:id>94aa8cef-ca3b-46f6-8538-7d77edd23b85</ns19:id>
                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                        <ns19:id>ae78654d-6e48-4e5b-82e4-8305300fa439</ns19:id>
                                                        <ns19:layoutItemId>Vertical_layout1</ns19:layoutItemId>
                                                        <ns19:configData>
                                                            <ns19:id>64b87181-3389-43b0-8f62-b27073e3765e</ns19:id>
                                                            <ns19:optionName>@label</ns19:optionName>
                                                            <ns19:value>Vertical layout</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>07e93a36-bb18-439e-8a67-0b8dafce8cdd</ns19:id>
                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                            <ns19:value />
                                                        </ns19:configData>
                                                        <ns19:configData>
                                                            <ns19:id>28e3dffa-508b-43af-8be5-7827b937eb04</ns19:id>
                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                            <ns19:value>SHOW</ns19:value>
                                                        </ns19:configData>
                                                        <ns19:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns19:viewUUID>
                                                        <ns19:contentBoxContrib>
                                                            <ns19:id>273a4cab-8194-472d-8d90-50b65d113642</ns19:id>
                                                            <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                            <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                <ns19:id>3599af2a-192e-46e3-8bca-622ef4ddbeff</ns19:id>
                                                                <ns19:layoutItemId>Tab_section1</ns19:layoutItemId>
                                                                <ns19:configData>
                                                                    <ns19:id>812e543b-3aba-4db9-89e9-0704be88ff7b</ns19:id>
                                                                    <ns19:optionName>@label</ns19:optionName>
                                                                    <ns19:value>Tab section</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>1744ab20-12ea-4f76-88cf-dd1a4140e7b6</ns19:id>
                                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                                    <ns19:value />
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>4b329f05-f47d-41fc-8e7a-06a25d18388e</ns19:id>
                                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                    <ns19:value>SHOW</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2e45773a-0f09-4ef8-8d83-653988e6a074</ns19:id>
                                                                    <ns19:optionName>colorStyle</ns19:optionName>
                                                                    <ns19:value>P</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>2e436236-eb83-449a-8d6f-17318d446e77</ns19:id>
                                                                    <ns19:optionName>tabsStyle</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"S"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>688cf571-19a2-4bf8-8f5e-995cbbccd4fa</ns19:id>
                                                                    <ns19:optionName>sizeStyle</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"X"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:configData>
                                                                    <ns19:id>cbd32f5e-b3eb-4587-8b36-82a68f0a982f</ns19:id>
                                                                    <ns19:optionName>@visibility</ns19:optionName>
                                                                    <ns19:value>{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"REQUIRED"}]}</ns19:value>
                                                                </ns19:configData>
                                                                <ns19:viewUUID>64.c05b439f-a4bd-48b0-8644-fa3b59052217</ns19:viewUUID>
                                                                <ns19:contentBoxContrib>
                                                                    <ns19:id>ca6107ca-bc4b-4305-86bc-8d212b250ce3</ns19:id>
                                                                    <ns19:contentBoxId>ContentBox1</ns19:contentBoxId>
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        <ns19:id>f2dc714e-5c43-4ddc-8e49-8a416fb3f37b</ns19:id>
                                                                        <ns19:layoutItemId>5</ns19:layoutItemId>
                                                                        <ns19:configData>
                                                                            <ns19:id>9b587cf5-02f3-45ec-8539-5acdc3534c9f</ns19:id>
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            <ns19:value>Financial Details Trade FO</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>ef44c152-c0f1-4dd9-881f-44af781c5381</ns19:id>
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            <ns19:value />
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>2f16c85e-28a7-44ff-8ac7-80daad5bedde</ns19:id>
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            <ns19:value>SHOW</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>efd6cc50-bcaa-4ffc-83be-b501e5247b14</ns19:id>
                                                                            <ns19:optionName>act3VIS</ns19:optionName>
                                                                            <ns19:value>ReadOnly</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>ba61ab19-07ac-42b1-8bf3-f78ce5a6cb33</ns19:id>
                                                                            <ns19:optionName>requestType</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.requestType.value</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>0e8fc121-c9a2-4445-8fa9-4d113987737f</ns19:id>
                                                                            <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                            <ns19:value>tw.local.multiTenorVIS</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>6212e428-cda6-4f2c-8de3-c2a483bbc367</ns19:id>
                                                                            <ns19:optionName>documentAmount</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.FinancialDetailsBR.documentAmount</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>860e1df0-8dce-4110-8125-f195cc249d01</ns19:id>
                                                                            <ns19:optionName>amountAdvanced</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.FinancialDetailsBR.amountAdvanced</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>8835530a-33c5-4cef-8e3d-762bfc5a4ac3</ns19:id>
                                                                            <ns19:optionName>todayDate</ns19:optionName>
                                                                            <ns19:value>tw.local.todayDate</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:viewUUID>64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1</ns19:viewUUID>
                                                                        <ns19:binding>tw.local.odcRequest.FinancialDetailsFO</ns19:binding>
                                                                    </ns19:contributions>
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        <ns19:id>bf381ae5-eb28-44fe-8b2c-95fe1b9d60b7</ns19:id>
                                                                        <ns19:layoutItemId>0</ns19:layoutItemId>
                                                                        <ns19:configData>
                                                                            <ns19:id>fb00f497-826f-46c5-8f7f-7e6c637a3f5c</ns19:id>
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            <ns19:value>Basic Details</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>ff028ba6-8748-4836-8be8-fbe74ee68484</ns19:id>
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            <ns19:value />
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>335805ec-15fc-48d0-8d52-2ad758d4d800</ns19:id>
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            <ns19:value>SHOW</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>a1028169-fbbf-4004-8dfb-0dae97ebc9c3</ns19:id>
                                                                            <ns19:optionName>parentRequestNoVis</ns19:optionName>
                                                                            <ns19:value>tw.local.parentRequestNumberVIS</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>4a1bfea0-8b6a-4cad-835e-1a0401d0bc93</ns19:id>
                                                                            <ns19:optionName>contractStageVIS</ns19:optionName>
                                                                            <ns19:value>tw.local.contractStageVIS</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>b24aa2e1-bade-4c12-8820-fc41b6d9d248</ns19:id>
                                                                            <ns19:optionName>flexCubeContractNoVIS</ns19:optionName>
                                                                            <ns19:value>Editable</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>ce4170a1-ae16-4fad-8015-05205a52f208</ns19:id>
                                                                            <ns19:optionName>multiTenorDatesVIS</ns19:optionName>
                                                                            <ns19:value>tw.local.multiTenorVIS</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>e21ea690-daf9-4ec5-8e73-d72dadc63db2</ns19:id>
                                                                            <ns19:optionName>basicDetailsCVVIS</ns19:optionName>
                                                                            <ns19:value>Editable</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:viewUUID>64.f7009c53-bea2-4a1f-8dc8-db4918768c05</ns19:viewUUID>
                                                                        <ns19:binding>tw.local.odcRequest.BasicDetails</ns19:binding>
                                                                    </ns19:contributions>
                                                                    <ns19:contributions xsi:type="ns19:ViewRef" version="8550">
                                                                        <ns19:id>c1549433-4905-4fc5-877f-a41e227a93d8</ns19:id>
                                                                        <ns19:layoutItemId>8</ns19:layoutItemId>
                                                                        <ns19:configData>
                                                                            <ns19:id>d61cc0e6-28ab-45b9-8e66-1401808422ac</ns19:id>
                                                                            <ns19:optionName>@label</ns19:optionName>
                                                                            <ns19:value>Contract Creation</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>099b04ee-f97e-44e0-8721-1b4f69e30b8f</ns19:id>
                                                                            <ns19:optionName>@helpText</ns19:optionName>
                                                                            <ns19:value />
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>219ba03e-7b89-4667-897f-404656354ca9</ns19:id>
                                                                            <ns19:optionName>@labelVisibility</ns19:optionName>
                                                                            <ns19:value>SHOW</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>c364e5d8-b768-4044-89f8-a77b2a2406a9</ns19:id>
                                                                            <ns19:optionName>contractStage</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.BasicDetails.contractStage</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>4e393b5b-13ab-46ef-8fe0-1384cb6e228e</ns19:id>
                                                                            <ns19:optionName>bpmRequestNumber</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.requestNo</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>a1d571ea-e2ef-4710-8911-35d4627e0653</ns19:id>
                                                                            <ns19:optionName>currency</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.FinancialDetailsBR.currency.value</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>52946f33-428b-44e4-87aa-31aff2ff4cef</ns19:id>
                                                                            <ns19:optionName>nbeCollectableAmount</ns19:optionName>
                                                                            <ns19:value>tw.local.odcRequest.FinancialDetailsFO.collectableAmount</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>7a9ad29f-24b8-4959-83d9-7a3b19a9f2fd</ns19:id>
                                                                            <ns19:optionName>baseDate</ns19:optionName>
                                                                            <ns19:value>tw.local.today</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>19e11f3e-ab78-4e75-8182-b3b640c6d62f</ns19:id>
                                                                            <ns19:optionName>contractCreationVis</ns19:optionName>
                                                                            <ns19:value>editable</ns19:value>
                                                                        </ns19:configData>
                                                                        <ns19:configData>
                                                                            <ns19:id>18f643d7-0df4-4db6-8b56-ac9008acb7d7</ns19:id>
                                                                            <ns19:optionName>todayDate</ns19:optionName>
                                                                            <ns19:value>tw.local.todayDate</ns19:value>
                                                                            <ns19:valueType>dynamic</ns19:valueType>
                                                                        </ns19:configData>
                                                                        <ns19:viewUUID>64.0f40f56d-733f-4bd5-916c-92ae7dccbb10</ns19:viewUUID>
                                                                        <ns19:binding>tw.local.odcRequest.ContractCreation</ns19:binding>
                                                                    </ns19:contributions>
                                                                </ns19:contentBoxContrib>
                                                            </ns19:contributions>
                                                        </ns19:contentBoxContrib>
                                                    </ns19:contributions>
                                                </ns19:contentBoxContrib>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>698d8eb4-67b2-4963-814f-788b4505ef2e</ns19:id>
                                                <ns19:layoutItemId>Button1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>1b743c51-509a-48f9-8bf7-026f654787d9</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Button</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>d134b671-591a-47a2-8235-20249b18f14f</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>4c2434aa-5a0d-425d-8aa7-************</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                                <ns3:dataChangeScript />
                            </ns3:formTask>
                            <ns16:exclusiveGateway default="2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0" gatewayDirection="Unspecified" name="isValid" id="2025.c83fdd58-5656-45c1-88be-786d246161b5">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="397" y="42" width="32" height="32" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.b683912e-2ca5-4470-8164-22f6918b58b8</ns16:incoming>
                                <ns16:outgoing>2027.d832440b-ec84-4d61-840a-c6a1e40396d9</ns16:outgoing>
                                <ns16:outgoing>2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0</ns16:outgoing>
                            </ns16:exclusiveGateway>
                            <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b683912e-2ca5-4470-8164-22f6918b58b8" name="Validation" id="2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="261" y="23" width="95" height="70" color="#95D087" />
                                    <ns3:postAssignmentScript>console.clear();&#xD;
console.dir(bpmext.ui.getInvalidViews());</ns3:postAssignmentScript>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352</ns16:incoming>
                                <ns16:outgoing>2027.b683912e-2ca5-4470-8164-22f6918b58b8</ns16:outgoing>
                                <ns16:script>tw.local.errorMessage = "";&#xD;
var mandatoryTriggered = false;&#xD;
var tempLength = 0;&#xD;
tw.local.invalidTabs = [];&#xD;
tw.system.coachValidation.clearValidationErrors();&#xD;
&#xD;
////-------------------------------------------BASIC DETAILS VALIDATION -----------------------------------&#xD;
mandatory(                                 &#xD;
	tw.local.odcRequest.BasicDetails.commodityDescription,&#xD;
	"tw.local.odcRequest.BasicDetails.commodityDescription"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo,&#xD;
	"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate,&#xD;
	"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef,&#xD;
	"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate,&#xD;
	"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate"&#xD;
);&#xD;
&#xD;
maxLength(&#xD;
	tw.local.odcRequest.BasicDetails.flexCubeContractNo,&#xD;
	"tw.local.odcRequest.BasicDetails.flexCubeContractNo",&#xD;
	16,&#xD;
	tw.resource.ValidationMessages.MaxLength16,&#xD;
	"Flex Cube Contract Number: " + tw.resource.ValidationMessages.MaxLength16&#xD;
);&#xD;
//add mess 160 to local file&#xD;
maxLength(&#xD;
	tw.local.odcRequest.BasicDetails.commodityDescription,&#xD;
	"tw.local.odcRequest.BasicDetails.commodityDescription",&#xD;
	160,&#xD;
	"Shouldn't be more than 160 character",&#xD;
	"Commodity Description: " + "Shouldn't be more than 160 character"&#xD;
);&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {&#xD;
	maxLength(&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,&#xD;
		"tw.local.odcRequest.BasicDetails.Invoice[" + i + "].invoiceNo",&#xD;
		20,&#xD;
		"Shouldn't be more than 20 character",&#xD;
		"invoice Number: " + "Shouldn't be more than 20 character"&#xD;
	);&#xD;
}&#xD;
&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Invoice.length; i++) {&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate,&#xD;
			"tw.local.odcRequest.BasicDetails.Invoice[" + i + "].invoiceDate"&#xD;
		);&#xD;
	}&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,&#xD;
			"tw.local.odcRequest.BasicDetails.Invoice[" + i + "].invoiceNo"&#xD;
		);&#xD;
	}&#xD;
}&#xD;
&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.BasicDetails.Bills.length; i++) {&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate,&#xD;
			"tw.local.odcRequest.BasicDetails.Bills[" + i + "].billOfLadingDate"&#xD;
		);&#xD;
	}&#xD;
	if (&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == "" ||&#xD;
		tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == null&#xD;
	) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef,&#xD;
			"tw.local.odcRequest.BasicDetails.Bills[" + i + "].billOfLadingRef"&#xD;
		);&#xD;
	}&#xD;
}&#xD;
validateTab(0, "Basic Details Tab");&#xD;
//-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------&#xD;
mandatory(&#xD;
	tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate,&#xD;
	"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate"&#xD;
);&#xD;
&#xD;
minLength(&#xD;
	tw.local.odcRequest.FinancialDetailsBR.amountAdvanced,&#xD;
	"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced",&#xD;
	2,&#xD;
	"Shouldn't be less than 14 character",&#xD;
	"Amount Advanced: Shouldn't be less than 2 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.FinancialDetailsBR.amountAdvanced,&#xD;
	"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced",&#xD;
	14,&#xD;
	"Shouldn't be more than 14 character",&#xD;
	"Amount Advanced:" + "Shouldn't be more than 14 character"&#xD;
);&#xD;
validateTab(3, "Financial Details - Branch Tab");&#xD;
//-------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------&#xD;
if (tw.local.odcRequest.FinancialDetailsBR.amountAdvanced &gt; 0) {&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.currency.name,&#xD;
		"tw.local.odcRequest.FcCollections.currency"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.negotiatedExchangeRate,&#xD;
		"tw.local.odcRequest.FcCollections.negotiatedExchangeRate"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.fromDate,&#xD;
		"tw.local.odcRequest.FcCollections.fromDate"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.ToDate,&#xD;
		"tw.local.odcRequest.FcCollections.ToDate"&#xD;
	);&#xD;
	mandatory(&#xD;
		tw.local.odcRequest.FcCollections.accountNo.value,&#xD;
		"tw.local.odcRequest.FcCollections.accountNo"&#xD;
	);&#xD;
&#xD;
	if (&#xD;
		tw.local.odcRequest.FcCollections != null &amp;&amp;&#xD;
		tw.local.odcRequest.FcCollections.negotiatedExchangeRate != null&#xD;
	) {&#xD;
		minLength(&#xD;
			tw.local.odcRequest.FcCollections.negotiatedExchangeRate,&#xD;
			"tw.local.odcRequest.FcCollections.negotiatedExchangeRate",&#xD;
			6,&#xD;
			"Shouldn't be less than 6 character",&#xD;
			" Negotiated Exchange Rate: Shouldn't be less than 6 character"&#xD;
		);&#xD;
		maxLength(&#xD;
			tw.local.odcRequest.FcCollections.negotiatedExchangeRate,&#xD;
			"tw.local.odcRequest.FcCollections.negotiatedExchangeRate",&#xD;
			10,&#xD;
			"Shouldn't be more than 10 character",&#xD;
			" Negotiated Exchange Rate:" + "Shouldn't be more than 10 character"&#xD;
		);&#xD;
	}&#xD;
&#xD;
	validateTab(4, "Flexcube collections Tab");&#xD;
}&#xD;
//-----------------------------------------Financial Details - Trade FO VALIDATION ----------------------------&#xD;
&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.discount,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.discount"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.extraCharges,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.extraCharges"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.ourCharges,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.ourCharges"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountSight,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.amountSight"&#xD;
);&#xD;
&#xD;
if (tw.local.odcRequest.BasicDetails.paymentTerms.name != "001") {&#xD;
	if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length == 0) {&#xD;
		mandatory(&#xD;
			tw.local.odcRequest.FinancialDetailsFO.multiTenorDates,&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates"&#xD;
		);&#xD;
		tw.local.errorMessage +=&#xD;
			"&lt;li&gt;" + "Fill in at least one entry in Multi Tenordates" + "&lt;/li&gt;";&#xD;
	}&#xD;
}&#xD;
&#xD;
var sum = 0;&#xD;
&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length; i++) {&#xD;
	if (&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == "" ||&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == null&#xD;
	) {&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[" + i + "].amount",&#xD;
			"Mandatory field"&#xD;
		);&#xD;
	} else if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount != "") {&#xD;
		checkNegativeValue(&#xD;
			tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount,&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[" + i + "].amount"&#xD;
		);&#xD;
	}&#xD;
	if (&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == "" ||&#xD;
		tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == null&#xD;
	) {&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[" + i + "].date",&#xD;
			"Mandatory field"&#xD;
		);&#xD;
	}&#xD;
	sum = sum + tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount;&#xD;
}&#xD;
&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.collectableAmount,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.collectableAmount"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization"&#xD;
);&#xD;
checkNegativeValue(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization"&#xD;
);&#xD;
&#xD;
if (&#xD;
	!!tw.local.odcRequest.FinancialDetailsFO.collectableAmount &amp;&amp;&#xD;
	tw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; 0&#xD;
)&#xD;
	if (&#xD;
		tw.local.odcRequest.FinancialDetailsFO.collectableAmount != null &amp;&amp;&#xD;
		tw.local.odcRequest.FinancialDetailsFO.collectableAmount &lt; sum &amp;&amp;&#xD;
		tw.local.odcRequest.BasicDetails.paymentTerms.name != "001"&#xD;
	) {&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.collectableAmount",&#xD;
			"Sum of all Installment Amounts in a request must be &lt;= Amount Collectable by NBE (اجمالى المبالغ المطلوب تحصيلها)"&#xD;
		);&#xD;
		tw.system.coachValidation.addValidationError(&#xD;
			"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length -1 ].date",&#xD;
			"Mandatory field"&#xD;
		);&#xD;
	}&#xD;
&#xD;
if (&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization &gt; 0 &amp;&amp;&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization &gt; 0&#xD;
) {&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization",&#xD;
		"Partial Avalization isn't allowed"&#xD;
	);&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization",&#xD;
		"Partial Avalization isn't allowed"&#xD;
	);&#xD;
}&#xD;
var sumAvalization = parseFloat(0);&#xD;
sumAvalization =&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization +&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization +&#xD;
	tw.local.odcRequest.FinancialDetailsFO.amountSight;&#xD;
if (sumAvalization != tw.local.odcRequest.FinancialDetailsFO.collectableAmount) {&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization",&#xD;
		"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization"&#xD;
	);&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization",&#xD;
		"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization"&#xD;
	);&#xD;
	tw.system.coachValidation.addValidationError(&#xD;
		"tw.local.odcRequest.FinancialDetailsFO.amountSight",&#xD;
		"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization"&#xD;
	);&#xD;
}&#xD;
mandatory(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.maturityDate,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.maturityDate"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity,&#xD;
	"tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity"&#xD;
);&#xD;
&#xD;
//--------------------------------------------Importer Details VALIDATION ----------------------------------&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.importerName,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerName"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.importerAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerAddress"&#xD;
);&#xD;
mandatory(tw.local.odcRequest.ImporterDetails.bank, "tw.local.odcRequest.ImporterDetails.bank");&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.BICCode,&#xD;
	"tw.local.odcRequest.ImporterDetails.BICCode"&#xD;
);&#xD;
&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ImporterDetails.bankAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.bankAddress"&#xD;
);&#xD;
&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.importerName,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerName",&#xD;
	250,&#xD;
	"Shouldn't be more than 250 character",&#xD;
	"Importer Name:" + "Shouldn't be more than 250 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.importerAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerAddress",&#xD;
	400,&#xD;
	"Shouldn't be more than 400 character",&#xD;
	"Importer Detailed Address:" + "Shouldn't be more than 400 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.importerPhoneNo,&#xD;
	"tw.local.odcRequest.ImporterDetails.importerPhoneNo",&#xD;
	20,&#xD;
	"Shouldn't be more than 20 character",&#xD;
	"Importer Telephone No.:" + "Shouldn't be more than 20 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.bank,&#xD;
	"tw.local.odcRequest.ImporterDetails.bank",&#xD;
	250,&#xD;
	"Shouldn't be more than 250 character",&#xD;
	"Importer Bank:" + "Shouldn't be more than 250 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.BICCode,&#xD;
	"tw.local.odcRequest.ImporterDetails.BICCode",&#xD;
	11,&#xD;
	"Shouldn't be more than 11 character",&#xD;
	"Importer Bank BIC Code:" + "Shouldn't be more than 11 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.ibanAccount,&#xD;
	"tw.local.odcRequest.ImporterDetails.ibanAccount",&#xD;
	40,&#xD;
	"Shouldn't be more than 40 character",&#xD;
	"Importer Account(IBAN):" + "Shouldn't be more than 40 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.bankAddress,&#xD;
	"tw.local.odcRequest.ImporterDetails.bankAddress",&#xD;
	400,&#xD;
	"Shouldn't be more than 400 character",&#xD;
	"Importer Bank Detailed Address:" + "Shouldn't be more than 400 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.bankPhoneNo,&#xD;
	"tw.local.odcRequest.ImporterDetails.bankPhoneNo",&#xD;
	20,&#xD;
	"Shouldn't be more than 20 character",&#xD;
	"Importer Bank Telephone No.:" + "Shouldn't be more than 20 character"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ImporterDetails.collectingBankReference,&#xD;
	"tw.local.odcRequest.ImporterDetails.collectingBankReference",&#xD;
	30,&#xD;
	"Shouldn't be more than 30 character",&#xD;
	"Collecting Bank Reference:" + "Shouldn't be more than 30 character"&#xD;
);&#xD;
validateTab(6, "Importer Details Tab");&#xD;
&#xD;
//-----------------------------------------Contract Creation VALIDATION -------------------------------------&#xD;
&#xD;
&#xD;
validateString(&#xD;
	tw.local.odcRequest.ContractCreation.userReference,&#xD;
	"tw.local.odcRequest.ContractCreation.userReference"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.ContractCreation.userReference,&#xD;
	"tw.local.odcRequest.ContractCreation.userReference"&#xD;
);&#xD;
maxLength(&#xD;
	tw.local.odcRequest.ContractCreation.userReference,&#xD;
	"tw.local.odcRequest.ContractCreation.userReference",&#xD;
	16,&#xD;
	"Shouldn't be more than 16 character",&#xD;
	"Contract Creation User Reference" + "Shouldn't be more than 16 character"&#xD;
);&#xD;
&#xD;
validateTab(8, "Contract Creation Tab");&#xD;
//---------------------------------------------//Charges and Commissions VALIDATION -------------------------------------&#xD;
for (var i = 0; i &lt; tw.local.odcRequest.ChargesAndCommissions.length; i++) {&#xD;
	//Description - Flat Amount&#xD;
	if (tw.local.odcRequest.ChargesAndCommissions[i].rateType == "Flat Amount") {&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &lt; 0 ||&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null&#xD;
		) {&#xD;
			addError(&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changeAmount",&#xD;
				"Must be &gt;= 0"&#xD;
			);&#xD;
		} else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0) {&#xD;
			validateDecimal2(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].changeAmount,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changeAmount",&#xD;
				"Must be Decimal(14,2)"&#xD;
			);&#xD;
		}&#xD;
&#xD;
		//Fixed Rate&#xD;
	} else {&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changePercentage &lt; 0 ||&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null&#xD;
		) {&#xD;
			addError(&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changePercentage",&#xD;
				"Must be &gt;= 0"&#xD;
			);&#xD;
		} else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage &gt; 0) {&#xD;
			validateDecimal2(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].changePercentage,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" + i + "].changePercentage",&#xD;
				"Must be Decimal(14,2)"&#xD;
			);&#xD;
		}&#xD;
	}&#xD;
&#xD;
	//skip validation if waiver or changeAmnt &lt; 0&#xD;
	if (&#xD;
		tw.local.odcRequest.ChargesAndCommissions[i].waiver == false &amp;&amp;&#xD;
		tw.local.odcRequest.ChargesAndCommissions[i].changeAmount &gt; 0&#xD;
	) {&#xD;
		//GL Account&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value ==&#xD;
			"GL Account"&#xD;
		) {&#xD;
			if (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {&#xD;
				addError(&#xD;
					"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
						i +&#xD;
						"].debitedAccount.glAccountNo",&#xD;
					"GL Account Not Verified"&#xD;
				);&#xD;
			}&#xD;
&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.glAccountNo"&#xD;
			);&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.currency.value"&#xD;
			);&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.branchCode"&#xD;
			);&#xD;
&#xD;
			//Customer Account&#xD;
		} else {&#xD;
			mandatory(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount&#xD;
					.customerAccountNo,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAccount.customerAccountNo"&#xD;
			);&#xD;
		}&#xD;
&#xD;
		//DebitedAmount&#xD;
		if (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate &gt; 0) {&#xD;
			validateDecimal(&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate,&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAmount.negotiatedExRate",&#xD;
				"Must be Decimal(16,10)"&#xD;
			);&#xD;
		}&#xD;
&#xD;
		//Correct Validation but Waiting confirmation on what to do if GL account&#xD;
		if (&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount &gt;&#xD;
				tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance &amp;&amp;&#xD;
			tw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false&#xD;
		) {&#xD;
			addError(&#xD;
				"tw.local.odcRequest.ChargesAndCommissions[" +&#xD;
					i +&#xD;
					"].debitedAmount.amountInAccount",&#xD;
				"ERROR: Must be &lt;= Account Balance"&#xD;
			);&#xD;
		}&#xD;
	}&#xD;
}&#xD;
validateTab(9, "Charges and commissions Tab");&#xD;
//---------------------------------------------//Parties VALIDATION -------------------------------------&#xD;
/////Drawer Section&#xD;
mandatory(tw.local.odcRequest.Parties.Drawer.partyId, "tw.local.odcRequest.Parties.Drawer.partyId");&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.Drawer.partyName,&#xD;
	"tw.local.odcRequest.Parties.Drawer.partyName"&#xD;
);&#xD;
&#xD;
/////Drawee Section&#xD;
mandatory(tw.local.odcRequest.Parties.Drawee.partyId, "tw.local.odcRequest.Parties.Drawee.partyId");&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.Drawee.partyName,&#xD;
	"tw.local.odcRequest.Parties.Drawee.partyName"&#xD;
);&#xD;
&#xD;
/////Parties Types (Accountee)&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.partyTypes.partyId,&#xD;
	"tw.local.odcRequest.Parties.partyTypes.partyId"&#xD;
);&#xD;
mandatory(&#xD;
	tw.local.odcRequest.Parties.partyTypes.partyName,&#xD;
	"tw.local.odcRequest.Parties.partyTypes.partyName"&#xD;
);&#xD;
&#xD;
validateTab(10, "Parties Tab");&#xD;
//---------------------------------------------------------------------------------&#xD;
function addError(fieldName, controlMessage, validationMessage, fromMandatory) {&#xD;
	tw.system.coachValidation.addValidationError(fieldName, controlMessage);&#xD;
	fromMandatory &amp;&amp; mandatoryTriggered&#xD;
		? ""&#xD;
		: (tw.local.errorMessage += "&lt;li&gt;" + validationMessage + "&lt;/li&gt;");&#xD;
}&#xD;
&#xD;
function minLength(field, fieldName, len, controlMessage, validationMessage) {&#xD;
	if (field != null &amp;&amp; field != undefined &amp;&amp; field.length &lt; len) {&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function maxLength(field, fieldName, len, controlMessage, validationMessage) {&#xD;
	if (field.length &gt; len) {&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function mandatory(field, fieldName, message) {&#xD;
	if (!message) {&#xD;
		message = camelCaseToTitle(fieldName) + " is Mandatory";&#xD;
	}&#xD;
&#xD;
	if (field == null || field == undefined) {&#xD;
		addError(fieldName, message, message, true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	} else {&#xD;
		switch (typeof field) {&#xD;
			case "string":&#xD;
				if (&#xD;
					field.trim() != undefined &amp;&amp;&#xD;
					field.trim() != null &amp;&amp;&#xD;
					field.trim().length == 0&#xD;
				) {&#xD;
					addError(fieldName, message, message, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (field == 0.0) {&#xD;
					addError(fieldName, message, message, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				if (field &lt; 0) {&#xD;
					var msg = "Invalid Value, This field can not be negative value.";&#xD;
					addError(fieldName, msg, msg, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
&#xD;
			default:&#xD;
				// VALIDATE DATE OBJECT&#xD;
				if (field &amp;&amp; field.getTime &amp;&amp; isFinite(field.getTime())) {&#xD;
				} else {&#xD;
					addError(fieldName, message, message, true);&#xD;
					mandatoryTriggered = true;&#xD;
					return false;&#xD;
				}&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function sumDates(fromDate, toDate, fieldName, controlMessage, validationMessage) {&#xD;
	if (toDate - fromDate == 0) {&#xD;
		return true;&#xD;
	}&#xD;
	addError(fieldName, controlMessage, validationMessage);&#xD;
	return false;&#xD;
}&#xD;
//=========================================================&#xD;
function checkNegativeValue(field, fieldName) {&#xD;
	if (field &lt; 0) {&#xD;
		var msg = "Invalid Value, This field can not be negative value.";&#xD;
		addError(fieldName, msg, msg, true);&#xD;
		mandatoryTriggered = true;&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
}&#xD;
&#xD;
function validateDecimal(field, fieldName, controlMessage, validationMessage) {&#xD;
	var decimalPattern = /^\d{1,4}(\.\d{1,6})?$/;&#xD;
	if (!decimalPattern.test(field)) {&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	} else {&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
//========================================================================================&#xD;
function validateTab(index, tabName) {&#xD;
	if (tw.system.coachValidation.validationErrors.length &gt; tempLength) {&#xD;
		if (tw.local.errorMessage.length == 0) {&#xD;
			tw.local.errorMessage +=&#xD;
				"&lt;p&gt;" + "Please complete fields in the following tabs:" + "&lt;/p&gt;";&#xD;
		}&#xD;
		tw.local.errorMessage += "&lt;li&gt;" + tabName + "&lt;/li&gt;";&#xD;
		tempLength = tw.system.coachValidation.validationErrors.length;&#xD;
		tw.local.invalidTabs[tw.local.invalidTabs.length] = index;&#xD;
	}&#xD;
}&#xD;
//==============================================================&#xD;
function validateDecimal2(field, fieldName, controlMessage, validationMessage) {&#xD;
	var decimalPattern = /^\d{1,12}(\.\d{1,2})?$/;&#xD;
	if (!decimalPattern.test(field)) {&#xD;
		// Decimal is valid&#xD;
		addError(fieldName, controlMessage, validationMessage);&#xD;
		return false;&#xD;
	} else {&#xD;
		// Decimal is invalid&#xD;
		return true;&#xD;
	}&#xD;
}&#xD;
&#xD;
function camelCaseToTitle(camelCase) {&#xD;
	var fieldName = camelCase.split(".").pop();&#xD;
	// Convert camelCase to Title Case&#xD;
	var titleCase = fieldName.replace(/([A-Z])/g, " $1");&#xD;
	// Uppercase the first character&#xD;
	titleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);&#xD;
	return titleCase;&#xD;
}&#xD;
&#xD;
function validateString(field, fieldName) {&#xD;
      if (!field) return;&#xD;
	// Regular expression to match only characters (letters)&#xD;
	var regex = /^[a-zA-Z]+$/;&#xD;
&#xD;
	// Test if the inputString matches the regex&#xD;
      if (regex.test(field)) {&#xD;
		return true;&#xD;
	} else {&#xD;
		addError(fieldName, "Numbers aren't allowed", "Numbers aren't allowed");&#xD;
		return false;&#xD;
	}&#xD;
}&#xD;
//=================================================================================&#xD;
tw.local.errorMessage != null&#xD;
	? (tw.local.errorPanelVIS = "EDITABLE")&#xD;
	: (tw.local.errorPanelVIS = "NONE");&#xD;
//=================================================================================&#xD;
</ns16:script>
                            </ns16:scriptTask>
                            <ns16:intermediateThrowEvent name="Postpone" id="2025.5de5922f-74f9-4f78-83a8-4955a265ceaa">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="78" y="-42" width="24" height="24" />
                                    <ns3:default>2027.894a9c37-37a6-4ce7-8330-51f32d9c3104</ns3:default>
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.04017f08-d5e0-446b-8673-524fd01f95e7</ns16:incoming>
                                <ns16:outgoing>2027.64964c28-a358-40ac-89dd-4f3c8453f4fd</ns16:outgoing>
                                <ns3:postponeTaskEventDefinition />
                            </ns16:intermediateThrowEvent>
                            <ns16:sequenceFlow sourceRef="2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7" targetRef="2025.c83fdd58-5656-45c1-88be-786d246161b5" name="To Valid?" id="2027.b683912e-2ca5-4470-8164-22f6918b58b8">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.5de5922f-74f9-4f78-83a8-4955a265ceaa" targetRef="2025.4695605b-49e8-461c-8104-b8bc0e52442d" name="To Screen" id="2027.64964c28-a358-40ac-89dd-4f3c8453f4fd">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topLeft</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.c83fdd58-5656-45c1-88be-786d246161b5" targetRef="2025.4695605b-49e8-461c-8104-b8bc0e52442d" name="To Screen" id="2027.d832440b-ec84-4d61-840a-c6a1e40396d9">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  &gt;	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.4695605b-49e8-461c-8104-b8bc0e52442d" targetRef="2025.5de5922f-74f9-4f78-83a8-4955a265ceaa" name="To Postpone" id="2027.04017f08-d5e0-446b-8673-524fd01f95e7">
                                <ns16:extensionElements>
                                    <ns3:coachEventBinding id="60d64e04-001d-4515-9834-e7d4116fa8b5">
                                        <ns3:coachEventPath>DC_Templete1/saveState</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topLeft</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.4695605b-49e8-461c-8104-b8bc0e52442d" targetRef="2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7" name="To End" id="2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352">
                                <ns16:extensionElements>
                                    <ns3:coachEventBinding id="aed447f0-6b9e-43bd-8d65-bd136cad5f16">
                                        <ns3:coachEventPath>DC_Templete1/submit</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:endEvent name="End" id="2025.3ff0b976-5220-4b64-80db-00db7f9f28c4">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="516" y="47" width="24" height="24" />
                                    <ns3:navigationInstructions>
                                        <ns3:targetType>Default</ns3:targetType>
                                    </ns3:navigationInstructions>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0</ns16:incoming>
                            </ns16:endEvent>
                            <ns16:sequenceFlow sourceRef="2025.c83fdd58-5656-45c1-88be-786d246161b5" targetRef="2025.3ff0b976-5220-4b64-80db-00db7f9f28c4" name="To End" id="2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>true</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                                <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.system.coachValidation.validationErrors.length	  ==	  0</ns16:conditionExpression>
                            </ns16:sequenceFlow>
                            <ns3:formTask name="Coach" id="2025.06f37113-06bc-4948-8dc6-92db16860efb">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="858" y="247" width="95" height="70" />
                                    <ns3:validationStayOnPagePaths>okbutton</ns3:validationStayOnPagePaths>
                                </ns16:extensionElements>
                                <ns16:incoming>2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21</ns16:incoming>
                                <ns16:incoming>2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e</ns16:incoming>
                                <ns16:outgoing>2027.8a760579-2363-4b05-8732-923cffbbb6d7</ns16:outgoing>
                                <ns3:formDefinition>
                                    <ns19:coachDefinition>
                                        <ns19:layout>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>65ef7b8c-0fe4-4821-824a-0cb002a1587e</ns19:id>
                                                <ns19:layoutItemId>test_view1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>8f5bc17f-751a-4ff1-8d45-856e2ff1582f</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>test view</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>db47fb73-0c4a-43f1-8a84-8f3bff4bd773</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>fbd4b89c-ed9a-4fd2-87c9-fb63bc8ef7ed</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.98459c6f-cb8f-462d-9fae-63d331db4606</ns19:viewUUID>
                                                <ns19:binding>tw.local.deliveryterms</ns19:binding>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>fb636457-0963-4cd4-849b-828d977b3855</ns19:id>
                                                <ns19:layoutItemId>test_view_21</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>7fbeb4b8-4a79-4d45-8cf9-bff28c7ebe15</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>test view 2</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>140204a9-1f55-4d0e-8d85-b3cd3e4d0199</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>1e56ed9b-e6dc-438d-8c1f-8129dc096cad</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.22a68a10-5120-47a7-bdbe-27efec0bd40b</ns19:viewUUID>
                                                <ns19:binding>tw.local.paymentTerms</ns19:binding>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef" version="8550">
                                                <ns19:id>5b56b0ed-0bab-40cb-85eb-c7d0748b907d</ns19:id>
                                                <ns19:layoutItemId>Output_Text1</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>7567d6c8-acd8-4290-8cb0-4ca156cd1b05</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>Display text</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>a9eed448-d760-4107-8876-d8dbed4041db</ns19:id>
                                                    <ns19:optionName>@helpText</ns19:optionName>
                                                    <ns19:value />
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>16cec4f5-3acc-434f-8b3a-28ce68e2c2a8</ns19:id>
                                                    <ns19:optionName>@labelVisibility</ns19:optionName>
                                                    <ns19:value>SHOW</ns19:value>
                                                </ns19:configData>
                                                <ns19:configData>
                                                    <ns19:id>207a4bcc-ef92-43de-8961-826feaffe9da</ns19:id>
                                                    <ns19:optionName>colorStyle</ns19:optionName>
                                                    <ns19:value>G</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns19:viewUUID>
                                                <ns19:binding>tw.local.errorMessage</ns19:binding>
                                            </ns19:layoutItem>
                                            <ns19:layoutItem xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns19:ViewRef">
                                                <ns19:id>c1344b74-856b-44a5-892c-b49ad277e4bc</ns19:id>
                                                <ns19:layoutItemId>okbutton</ns19:layoutItemId>
                                                <ns19:configData>
                                                    <ns19:id>f148d791-209a-4e36-8e0d-e2bf3b41cd2b</ns19:id>
                                                    <ns19:optionName>@label</ns19:optionName>
                                                    <ns19:value>OK</ns19:value>
                                                </ns19:configData>
                                                <ns19:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns19:viewUUID>
                                            </ns19:layoutItem>
                                        </ns19:layout>
                                    </ns19:coachDefinition>
                                </ns3:formDefinition>
                            </ns3:formTask>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21" name="Client-Side Script" id="2025.1830251f-d054-49e9-8954-7562c5ab62d9">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="1061" y="248" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.8a760579-2363-4b05-8732-923cffbbb6d7</ns16:incoming>
                                <ns16:outgoing>2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21</ns16:outgoing>
                                <ns16:script>// /DC_Templete1&#xD;
&#xD;
var lib = bpmext.ui.getView("/test_view1");&#xD;
&#xD;
lib.mandatory(tw.local.deliveryterms, "tw.local.deliveryterms");&#xD;
&#xD;
&#xD;
lib.getErrorList( tw.system.coachValidation );</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.1830251f-d054-49e9-8954-7562c5ab62d9" targetRef="2025.06f37113-06bc-4948-8dc6-92db16860efb" name="To Coach" id="2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>topRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:callActivity calledElement="1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8" default="2027.e0e8c13f-f566-4655-843a-caee6c5d70a5" name="Test Error" id="2025.34ee082d-7910-4a7a-8639-b97420edb475">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="443" y="249" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.290f916c-9015-4c0d-8e5f-d4883652dc5d</ns16:incoming>
                                <ns16:outgoing>2027.e0e8c13f-f566-4655-843a-caee6c5d70a5</ns16:outgoing>
                                <ns16:dataInputAssociation>
                                    <ns16:targetRef>2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5</ns16:targetRef>
                                    <ns16:assignment>
                                        <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.input1</ns16:from>
                                    </ns16:assignment>
                                </ns16:dataInputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be">tw.local.output1</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                                <ns16:dataOutputAssociation>
                                    <ns16:sourceRef>2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0</ns16:sourceRef>
                                    <ns16:assignment>
                                        <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6">tw.local.output2</ns16:to>
                                    </ns16:assignment>
                                </ns16:dataOutputAssociation>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="a7b2f73c-28cf-4a6a-9993-f252e17ae70f" targetRef="2025.4695605b-49e8-461c-8104-b8bc0e52442d" name="To Create" id="2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>bottomRight</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.06f37113-06bc-4948-8dc6-92db16860efb" targetRef="2025.1830251f-d054-49e9-8954-7562c5ab62d9" name="To Client-Side Script" id="2027.8a760579-2363-4b05-8732-923cffbbb6d7">
                                <ns16:extensionElements>
                                    <ns3:sequenceFlowImplementation sboSyncEnabled="false" fireValidation="Never" />
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>true</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                    <ns3:coachEventBinding id="e7eeff5e-7a0b-46c4-8b9f-6eac4bc4a3f4">
                                        <ns3:coachEventPath>okbutton</ns3:coachEventPath>
                                    </ns3:coachEventBinding>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:sequenceFlow sourceRef="2025.34ee082d-7910-4a7a-8639-b97420edb475" targetRef="2025.091f583e-7ba7-4397-816c-2bcd94163432" name="To Client-Side Script 1" id="2027.e0e8c13f-f566-4655-843a-caee6c5d70a5">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="input1" id="2056.b54e68ad-04e6-478c-86d2-305a2a83f840" />
                            <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMsg" id="2056.32167ef1-4fa5-466f-8702-a225f50215cd" />
                            <ns16:dataObject itemSubjectRef="itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" isCollection="false" name="output1" id="2056.bdcb8985-ae7b-401f-8383-4ce216166eeb" />
                            <ns16:dataObject itemSubjectRef="itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6" isCollection="true" name="output2" id="2056.b913fb7d-1bbc-4edf-8420-c7b85912449e" />
                            <ns16:subProcess triggeredByEvent="true" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Error" id="2025.7f290839-c94b-4fcd-8099-fe07b5ec75c5">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="687" y="77" width="95" height="70" color="#FF7782" />
                                </ns16:extensionElements>
                                <ns16:startEvent isInterrupting="true" parallelMultiple="false" name="Start" id="2025.debb1ad5-4b9a-4d03-860c-09cb0a594169">
                                    <ns16:extensionElements>
                                        <ns3:default>2027.e2568422-f163-4dbd-8f2c-a782918ddeb3</ns3:default>
                                        <ns13:nodeVisualInfo x="50" y="200" width="24" height="24" color="#F8F8F8" />
                                    </ns16:extensionElements>
                                    <ns16:outgoing>2027.e2568422-f163-4dbd-8f2c-a782918ddeb3</ns16:outgoing>
                                    <ns16:errorEventDefinition>
                                        <ns16:extensionElements>
                                            <ns4:errorEventSettings>
                                                <ns4:catchAll>true</ns4:catchAll>
                                            </ns4:errorEventSettings>
                                        </ns16:extensionElements>
                                    </ns16:errorEventDefinition>
                                </ns16:startEvent>
                                <ns16:intermediateThrowEvent name="Stay on page" id="2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c">
                                    <ns16:extensionElements>
                                        <ns13:nodeVisualInfo x="700" y="200" width="24" height="24" />
                                        <ns3:navigationInstructions>
                                            <ns3:targetType>ToOtherDashboard</ns3:targetType>
                                            <ns3:targetURL>tw.system.url.bpmDataEndpoint</ns3:targetURL>
                                        </ns3:navigationInstructions>
                                    </ns16:extensionElements>
                                    <ns16:incoming>2027.b2ae1e6e-b5ff-4332-8f78-193de087a034</ns16:incoming>
                                    <ns3:stayOnPageEventDefinition />
                                </ns16:intermediateThrowEvent>
                                <ns16:scriptTask scriptFormat="text/x-javascript" isForCompensation="false" startQuantity="1" completionQuantity="1" default="2027.b2ae1e6e-b5ff-4332-8f78-193de087a034" name="Client-Side Script 2" id="2025.060a5457-7180-4157-85b0-242ad81d0129">
                                    <ns16:extensionElements>
                                        <ns13:nodeVisualInfo x="353" y="175" width="95" height="70" />
                                    </ns16:extensionElements>
                                    <ns16:incoming>2027.e2568422-f163-4dbd-8f2c-a782918ddeb3</ns16:incoming>
                                    <ns16:outgoing>2027.b2ae1e6e-b5ff-4332-8f78-193de087a034</ns16:outgoing>
                                    <ns16:script>tw.local.errorMessage = String( tw.error.data )&#xD;
console.log("&lt;&lt;ERROR&gt;&gt;");&#xD;
console.log(tw.error)&#xD;
&#xD;
tw.system.url.</ns16:script>
                                </ns16:scriptTask>
                                <ns16:sequenceFlow sourceRef="2025.debb1ad5-4b9a-4d03-860c-09cb0a594169" targetRef="2025.060a5457-7180-4157-85b0-242ad81d0129" name="To Client-Side Script 2" id="2027.e2568422-f163-4dbd-8f2c-a782918ddeb3">
                                    <ns16:extensionElements>
                                        <ns13:linkVisualInfo>
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            <ns13:showLabel>false</ns13:showLabel>
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                        </ns13:linkVisualInfo>
                                    </ns16:extensionElements>
                                </ns16:sequenceFlow>
                                <ns16:sequenceFlow sourceRef="2025.060a5457-7180-4157-85b0-242ad81d0129" targetRef="2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c" name="To Stay on page" id="2027.b2ae1e6e-b5ff-4332-8f78-193de087a034">
                                    <ns16:extensionElements>
                                        <ns13:linkVisualInfo>
                                            <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                            <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                            <ns13:showLabel>false</ns13:showLabel>
                                            <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                            <ns13:labelPosition>0.0</ns13:labelPosition>
                                            <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                        </ns13:linkVisualInfo>
                                    </ns16:extensionElements>
                                </ns16:sequenceFlow>
                            </ns16:subProcess>
                            <ns16:callActivity calledElement="1.5d77055c-98a8-4191-9b74-c7120a5823be" default="2027.290f916c-9015-4c0d-8e5f-d4883652dc5d" name="Create" id="2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="264" y="250" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:outgoing>2027.290f916c-9015-4c0d-8e5f-d4883652dc5d</ns16:outgoing>
                            </ns16:callActivity>
                            <ns16:sequenceFlow sourceRef="2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8" targetRef="2025.34ee082d-7910-4a7a-8639-b97420edb475" name="To Coach" id="2027.290f916c-9015-4c0d-8e5f-d4883652dc5d">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns16:scriptTask scriptFormat="text/x-javascript" default="2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e" name="Client-Side Script 1" id="2025.091f583e-7ba7-4397-816c-2bcd94163432">
                                <ns16:extensionElements>
                                    <ns13:nodeVisualInfo x="652" y="247" width="95" height="70" />
                                </ns16:extensionElements>
                                <ns16:incoming>2027.e0e8c13f-f566-4655-843a-caee6c5d70a5</ns16:incoming>
                                <ns16:outgoing>2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e</ns16:outgoing>
                                <ns16:script>tw.system.</ns16:script>
                            </ns16:scriptTask>
                            <ns16:sequenceFlow sourceRef="2025.091f583e-7ba7-4397-816c-2bcd94163432" targetRef="2025.06f37113-06bc-4948-8dc6-92db16860efb" name="To Coach" id="2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e">
                                <ns16:extensionElements>
                                    <ns13:linkVisualInfo>
                                        <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                        <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                        <ns13:showLabel>false</ns13:showLabel>
                                        <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                        <ns13:labelPosition>0.0</ns13:labelPosition>
                                        <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                    </ns13:linkVisualInfo>
                                </ns16:extensionElements>
                            </ns16:sequenceFlow>
                            <ns3:htmlHeaderTag id="6d0ba8e0-0d58-434a-89fa-335d347a4c0b">
                                <ns3:tagName>viewport</ns3:tagName>
                                <ns3:content>width=device-width,initial-scale=1.0</ns3:content>
                                <ns3:enabled>true</ns3:enabled>
                            </ns3:htmlHeaderTag>
                        </ns3:userTaskImplementation>
                        <ns3:exposedAs>NotExposed</ns3:exposedAs>
                    </ns16:extensionElements>
                    <ns16:ioSpecification>
                        <ns16:extensionElements>
                            <ns3:epvProcessLinks>
                                <ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="bb759592-f2f7-40f3-8d2c-1489ab500ca9" />
                                <ns3:epvProcessLinkRef epvId="21.bed40437-f5de-4b1c-a063-7040de4075df" epvProcessLinkId="a32c52e1-a276-4e4d-81c2-cd41ab5bc551" />
                                <ns3:epvProcessLinkRef epvId="21.86dc5c1d-931d-46d2-9be1-12397cc9f048" epvProcessLinkId="9cd57e59-b7e1-4482-8c4c-588e29e57de4" />
                                <ns3:epvProcessLinkRef epvId="21.769dc134-1d15-4dd4-a967-c5f61cf352dc" epvProcessLinkId="2d23c808-1ed4-4142-8ddb-d0dcba7601f2" />
                                <ns3:epvProcessLinkRef epvId="21.f79b6325-0b4a-48cd-b342-f9a61300eace" epvProcessLinkId="0632eb96-065b-4557-887c-01e27486455e" />
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="3171034a-3c65-4e0d-8aee-052ffb8cbeba" />
                            </ns3:epvProcessLinks>
                            <ns3:localizationResourceLinks>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.72059ba3-20f9-4926-b151-02b418301dd4</ns3:resourceBundleGroupID>
                                    <ns3:id>69.31cfd076-ef2f-4737-85d1-a256beb27f48</ns3:id>
                                </ns3:resourceRef>
                                <ns3:resourceRef>
                                    <ns3:resourceBundleGroupID>50.41101508-d2e4-4682-b3ef-b9b22266bb5a</ns3:resourceBundleGroupID>
                                    <ns3:id>69.217edee5-1f2d-4b8b-8502-8576e027daf4</ns3:id>
                                </ns3:resourceRef>
                            </ns3:localizationResourceLinks>
                        </ns16:extensionElements>
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d">
                            <ns16:extensionElements>
                                <ns3:defaultValue useDefault="true">var autoObject = {};
autoObject.initiator = "";
autoObject.requestNature = {};
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = {};
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new Date();
autoObject.ImporterName = "";
autoObject.appInfo = {};
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = {};
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = {};
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = {};
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = {};
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = {};
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = {};
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = {};
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = {};
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = {};
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = [];
autoObject.BasicDetails.Bills[0] = {};
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();
autoObject.BasicDetails.Invoice = [];
autoObject.BasicDetails.Invoice[0] = {};
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new Date();
autoObject.GeneratedDocumentInfo = {};
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = [];
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = [];
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = [];
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = [];
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = [];
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = {};
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = [];
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = {};
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = {};
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new Date();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = {};
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = [];
autoObject.FinancialDetailsBR.listOfAccounts[0] = {};
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = {};
autoObject.FcCollections.currency = {};
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new Date();
autoObject.FcCollections.ToDate = new Date();
autoObject.FcCollections.accountNo = {};
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = [];
autoObject.FcCollections.retrievedTransactions[0] = {};
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = [];
autoObject.FcCollections.selectedTransactions[0] = {};
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new Date();
autoObject.FcCollections.selectedTransactions[0].valueDate = new Date();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = [];
autoObject.FcCollections.listOfAccounts[0] = {};
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = {};
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new Date();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = {};
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = [];
autoObject.FinancialDetailsFO.multiTenorDates[0] = {};
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.FinancialDetailsFO.multiTenorDates[0].rebate = 0.0;
autoObject.FinancialDetailsFO.multiTenorDates[0].matDate = new Date();
autoObject.FinancialDetailsFO.multiTenorDates[0].tenorDays = 0;
autoObject.FinancialDetailsFO.rebate = 0.0;
autoObject.ImporterDetails = {};
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = {};
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = {};
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = {};
autoObject.ProductShipmentDetails.CBECommodityClassification = {};
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new Date();
autoObject.ProductShipmentDetails.shipmentMethod = {};
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = {};
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = {};
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = {};
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = {};
autoObject.ContractCreation.productCode = {};
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new Date();
autoObject.ContractCreation.valueDate = new Date();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new Date();
autoObject.Parties = {};
autoObject.Parties.Drawer = {};
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = {};
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = {};
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = {};
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = {};
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.Parties.caseInNeed = {};
autoObject.Parties.caseInNeed.partyCIF = "";
autoObject.Parties.caseInNeed.partyId = "";
autoObject.Parties.caseInNeed.partyName = "";
autoObject.Parties.caseInNeed.country = "";
autoObject.Parties.caseInNeed.language = "";
autoObject.Parties.caseInNeed.refrence = "";
autoObject.Parties.caseInNeed.address1 = "";
autoObject.Parties.caseInNeed.address2 = "";
autoObject.Parties.caseInNeed.address3 = "";
autoObject.Parties.caseInNeed.partyType = {};
autoObject.Parties.caseInNeed.partyType.name = "";
autoObject.Parties.caseInNeed.partyType.value = "";
autoObject.ChargesAndCommissions = [];
autoObject.ChargesAndCommissions[0] = {};
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = {};
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = {};
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = {};
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ChargesAndCommissions[0].isGLFound = false;
autoObject.ChargesAndCommissions[0].glVerifyMSG = "";
autoObject.ContractLiquidation = {};
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new Date();
autoObject.ContractLiquidation.creditValueDate = new Date();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.debitedAccountName = "";
autoObject.ContractLiquidation.creditedAccount = {};
autoObject.ContractLiquidation.creditedAccount.accountClass = {};
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = {};
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = {};
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = {};
autoObject.stepLog.startTime = new Date();
autoObject.stepLog.endTime = new Date();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = [];
autoObject.actions[0] = "";
autoObject.attachmentDetails = {};
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = {};
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = [];
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = [];
autoObject.attachmentDetails.attachment[0] = {};
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = [];
autoObject.complianceComments[0] = {};
autoObject.complianceComments[0].startTime = new Date();
autoObject.complianceComments[0].endTime = new Date();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = [];
autoObject.History[0] = {};
autoObject.History[0].startTime = new Date();
autoObject.History[0].endTime = new Date();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = {};
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject.folderPath = "";
autoObject.templateDocID = "";
autoObject.requestID = 0;
autoObject.customerAndPartyAccountList = [];
autoObject.customerAndPartyAccountList[0] = {};
autoObject.customerAndPartyAccountList[0].accountNO = "";
autoObject.customerAndPartyAccountList[0].currencyCode = "";
autoObject.customerAndPartyAccountList[0].branchCode = "";
autoObject.customerAndPartyAccountList[0].balance = 0.0;
autoObject.customerAndPartyAccountList[0].typeCode = "";
autoObject.customerAndPartyAccountList[0].customerName = "";
autoObject.customerAndPartyAccountList[0].customerNo = "";
autoObject.customerAndPartyAccountList[0].frozen = false;
autoObject.customerAndPartyAccountList[0].dormant = false;
autoObject.customerAndPartyAccountList[0].noDebit = false;
autoObject.customerAndPartyAccountList[0].noCredit = false;
autoObject.customerAndPartyAccountList[0].postingAllowed = false;
autoObject.customerAndPartyAccountList[0].ibanAccountNumber = "";
autoObject.customerAndPartyAccountList[0].accountClassCode = "";
autoObject.customerAndPartyAccountList[0].balanceType = "";
autoObject.customerAndPartyAccountList[0].accountStatus = "";
autoObject.tradeFoComment = "";
autoObject.exeHubMkrComment = "";
autoObject.compcheckerComment = "";
autoObject</ns3:defaultValue>
                            </ns16:extensionElements>
                        </ns16:dataInput>
                        <ns16:dataInput name="regeneratedRemittanceLetterTitleVIS" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e" />
                        <ns16:dataInput name="fromExeChecker" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.21028eba-aa2f-4b93-806b-78b695c14ddf" />
                        <ns16:dataOutput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.021ed4ee-8b5e-423d-8672-bdcaceed27d8" />
                        <ns16:dataOutput name="compApprovalInit" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.*************-426c-8701-7fc1c235c6a3" />
                        <ns16:dataOutput name="lastAction" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991" />
                        <ns16:inputSet id="20372ac9-5465-4e48-b85b-6617c2365423" />
                        <ns16:outputSet id="0c19fa57-ebfe-46ea-a3b8-02de562e4688" />
                    </ns16:ioSpecification>
                </ns16:globalUserTask>
            </ns16:definitions>
        </coachflow>
        <link name="Untitled">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d247280b-01cb-41cc-a99d-bf3710f72a5f</processLinkId>
            <processId>1.9321de6e-e6a5-435e-accb-0fbbd129c48a</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.6b1114d2-2fd1-421a-bc5f-7dc264e40eb4</toProcessItemId>
            <guid>124522bd-34b1-4f74-afcb-8bf97ebee9ef</guid>
            <versionId>e05d7668-eef0-431b-b8a5-8f808ad05820</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="rightCenter" portType="2" />
            <fromProcessItemId>2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599</fromProcessItemId>
            <toProcessItemId>2025.6b1114d2-2fd1-421a-bc5f-7dc264e40eb4</toProcessItemId>
        </link>
    </process>
</teamworks>

