{"id": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "versionId": "97ba8feb-0017-4384-9f96-8ff8181f41a1", "name": "RequestNature", "type": "epv", "typeName": "Environment Property Variable", "details": {}, "_fullObjectData": {"teamworks": {"epv": {"id": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "name": "RequestNature", "lastModified": "1691760290617", "lastModifiedBy": "abdelrahman.saleh", "epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "extDescription": "<html><body></body></html>", "participantRef": {"isNull": "true"}, "feedback": "", "description": "<html><body></body></html>", "guid": "guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:-6a41", "versionId": "97ba8feb-0017-4384-9f96-8ff8181f41a1", "EPV_VAR": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvVarId": "2013.931d165c-f524-4882-8902-2ebe955702b2", "epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "name": "UpdateRequest", "varName": "UpdateRequest", "extDescription": "<html><body></body></html>", "variableTypeRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "defaultValue": "update", "isChgActiveProc": "false", "guid": "a5ad0b80-0419-4413-99a0-937fd0c31bda", "versionId": "42acbdd3-f90a-4a15-a4f9-e4b36a4291c9"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvVarId": "2013.55af8ee2-7fbd-4010-8b68-d9a2e18a6cca", "epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "name": "NewRequest", "varName": "NewRequest", "extDescription": "<html><body><div><br /></div></body></html>", "variableTypeRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "defaultValue": "new", "isChgActiveProc": "false", "guid": "720c7442-2d4b-4f2c-9509-04e97ce36493", "versionId": "f2060778-2088-4b02-9a2e-158c763e1583"}]}}}}