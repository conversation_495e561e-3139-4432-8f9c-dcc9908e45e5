{"id": "64.0ff96fd8-0740-4d17-887e-a56c8ef7921b", "versionId": "f2f1658d-ae7b-45ee-a60e-36b46b282024", "name": "Importer Details CV", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"bindingType": "importerDetails", "configOptions": ["importerDetailsCVVIS", "requestType", "bankRefVIS"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "this.collectingBankRefVIS  = function()\r\r\n{\r\r\n\tif(this.context.options.requestType.get(\"value\") == \"amend\" || this.context.options.requestType.get(\"value\") == \"recreate\" )\r\r\n\t\tthis.context.options.bankRefVIS.set(\"value\", \"Editable\");\r\r\n\telse\r\r\n\r\r\n\t\tthis.context.options.bankRefVIS.set(\"value\", \"None\");\r\r\n}"}]}, "hasDetails": true}