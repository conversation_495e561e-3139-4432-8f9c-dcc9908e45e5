<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f" name="DC Templete">
        <lastModified>1748954143619</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
        <isTemplate>true</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;bd85072d-7c74-4d39-8ce1-037a06009e0d&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Header_View1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;9b5f4bc3-8427-4832-852a-c8f48faf98dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Header View&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;de5c6869-6947-4e71-8962-b6bcabb8da57&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;716e5467-6ca2-436c-8542-6799aa48ff5f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;73596bc1-8b1a-4b42-869a-bb3dbecc84a6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;subStatusVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.55afafe5-9321-40fd-9b9a-cbbd556a8005&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.appinfo&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:CustomHTML" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;d580be2c-2d87-4dc1-8759-bc34a6932204&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CustomHTML1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ebc01f54-8cf9-401c-83ee-b3a93ae88a0b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@customHTML.contentType&lt;/ns2:optionName&gt;&lt;ns2:value&gt;TEXT&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:GridLayoutContainer" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;fe6673cd-c9c8-4352-8f2b-2a116a7db739&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1cf5e062-c534-4392-8835-84a350a1a43b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;e3ed836d-8431-481b-80ec-fdae3f7cc017&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ae7c2394-eca4-4d90-8d79-8cff10979f82&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;124b173b-a571-4d10-8953-301c7d6cd4c2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;049efd81-5559-4859-843d-93cc8c9f55b1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":1},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;396d58d9-b954-46d4-81c7-c0432739407f&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e0aad31-6390-44b5-8568-10911f4e9a93&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":11},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;dfddd41d-4593-4320-8d92-e12f42d4d815&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;211242dc-bb7f-49b1-8f6a-8aea00b17ca9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":11},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;51501f53-9c8b-4682-8ad8-c8eedf9afbf3&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c7706d44-f248-41d5-882a-d79a986a3324&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ContentBox" version="8550"&gt;&lt;ns2:id&gt;65288874-0c0e-4639-8826-1e82d0ad9be9&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ContentBox1&lt;/ns2:layoutItemId&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;e584eb66-137c-4387-803e-90cdb7def669&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2bd85218-7b45-4dce-813a-3b438158dc16&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;ba612e02-7ff7-4851-893f-e7f9cc3daf15&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer7&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e73d3a2a-3de5-40d0-8f5b-3dab06b58c68&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":5}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;aa2dd0be-9193-45ef-879c-f94824eab375&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer8&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1cd96d41-f88d-46a7-80c3-a349b2a79c15&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":5}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;22504682-5fa4-4eb2-868e-0d762e7a556e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell8&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;63a11d16-c87c-450b-8cdb-27cec691a484&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":4},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;edd64fc6-86f0-4442-81d9-e0b5fe752092&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;actionList&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4fe9f100-6e4a-4dbf-8288-e8a6b5ba7f52&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Action&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;76d3fb63-3d19-47fc-8351-d791142f7354&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;819aac2d-543d-4f83-80bf-67cac93a235b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a9998b2c-779a-4b6d-8336-981a8b8fa65e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemLookupMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;db9f7924-823a-4005-8074-0973556d946e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemList&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.action[]&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8d4f54b6-80a2-42f7-8915-2e8e3de139d4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;569361a0-4aea-4262-8ce0-dc46166e89d2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3e4dc718-89b8-4d53-847e-dd0767c8cdb4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;dataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c19ff324-8fe2-4853-8006-327ce52d2835&lt;/ns2:id&gt;&lt;ns2:optionName&gt;businessDataMapping&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bbf6f090-1e88-4e9c-8e2f-f8b63fbc01be&lt;/ns2:id&gt;&lt;ns2:optionName&gt;itemService&lt;/ns2:optionName&gt;&lt;ns2:value&gt;1.60333c1d-84b6-4ab1-9df9-ecc81d9ea5f3&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;e823edfc-3731-4ecf-8100-3c7c1ca26395&lt;/ns2:id&gt;&lt;ns2:optionName&gt;inputData&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.actionConditions&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fd4da558-40d8-47be-92ca-c305708dc7b7&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.selectedAction&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;13fadc07-8b84-4822-8639-c33cebcff6cc&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ReturnReason&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6b20c52d-822e-494a-a71a-53f096c8626f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2a09518f-8ada-4e96-8f5a-41bf217d07a9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Return Reason&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b22360a-ac69-41ac-8257-a40a6eeb3a61&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;86ed6434-1b34-4fe6-8472-e173bd22d304&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c67a33c-f7de-44d4-891d-fc058ebaf700&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.ui.getAbsoluteName()&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bdeefa5b-041b-422d-8907-25d65e052aec&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.returnReasonVIS&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.stepLog.returnReason&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;d2acd7f6-53dc-46f2-8926-4090a2d7f870&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;56cd9c7a-629e-4b2d-8a1a-9ffcfde64caa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Terminate Reason&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;17347a41-14b2-43fc-8d11-507d5c5bb2eb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;10e6b0e3-925c-41bd-8977-acf77b0c7c20&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7dffd307-1956-4f7a-8e35-27b5fe7738b2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.terminateReasonVIS&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.stepLog.terminateReason&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;3ac17695-7e95-4f8a-8841-ed7806fa5b00&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell2&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2352c7bb-92fe-4b6e-8bf6-9d34987e7042&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":5}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;868f927b-42bc-4aba-8737-6106f0ae83ea&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;complianceApproval&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;37b85f00-5952-433c-8ae2-30884468c26f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Need Compliance Approval&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;05b322d6-59d3-4e30-83e9-261f76708af8&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f7916876-1f3f-474f-8e58-64edf8531af2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.complianceApprovalVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;dca9bf79-fd06-4115-8e7b-539f46257eaa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@padding&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"30px 0px 0px 0px"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;90f35fff-5605-4a1d-8db2-2b7691571b75&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;${actionList}.setAction(); &lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a53e9310-4afb-4fe6-8afd-1b6a19974c13&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.complianceApproval&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2e8e65d6-3296-455d-8ff0-25479ec37027&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;294b0b85-655f-4b3a-ad05-d48c1287713c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;aba44115-a7ad-4b25-85da-5c5395beac2f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Trade Fo Comment&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3445cd08-f5df-42a8-8378-5b0535b0c506&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;17d5d4c2-5bf3-40f3-8f6b-2ea6258972c2&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a5d8a98e-5074-4881-8f35-99556f29c6c0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;printOverflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;808b4d61-24fd-4b96-88c8-39f9c18a097b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7ee911cf-0aad-4b6b-8c3d-8950b5836cc6&lt;/ns2:id&gt;&lt;ns2:optionName&gt;height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;756b7cd3-4336-4562-8ef7-4c111f0f2cc5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.tradeFoCommentVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.tradeFoComment&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;2819829d-5e73-4524-839e-784b8d470101&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text4&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ec24920-75c5-496c-8b1d-ebc06597c396&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;65520db7-cbbf-4596-8a37-19e9425d21a7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Execution Hub Maker Comment&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;86c4f93d-f1a6-406a-8d05-c1947d16d663&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2b0a27d6-a766-43e2-8815-b1e8ead08a6b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2c1ed17d-817e-488f-80d3-cd9b8a6188d7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;printOverflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;69c8f306-312f-4da5-8d3f-a32e6d69e478&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;67fb8e8f-eb38-47c3-827a-adbd134bab54&lt;/ns2:id&gt;&lt;ns2:optionName&gt;height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f0173162-e6e0-4e0b-882a-079d9444a155&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.exeHubMkrCommentVis&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.exeHubMkrComment&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;b16bda84-8d03-40b0-8ffb-18c753269b03&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text5&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc528f81-818d-483d-81b1-97b8a2ee7f65&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;27de7629-7345-4dec-834f-d44f3348ce66&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Compliance Checker Comment&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7abe3875-b416-4dfb-8542-b5f7e9790cd0&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2bcbe5ef-61be-4946-860e-1bb82b6f29b9&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b4ff5cc-9bc5-4fd7-854c-874b14b4a1d4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;printOverflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;165d58cf-ae5d-4959-8d45-be6a60d1e487&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;bb25eead-4bc2-4520-8722-ce0f3c481b01&lt;/ns2:id&gt;&lt;ns2:optionName&gt;height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;734ccd70-4aeb-400b-8902-992135296fad&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;tw.options.compcheckerCommentVIS&lt;/ns2:value&gt;&lt;ns2:valueType&gt;dynamic&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.compcheckerComment&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;e2a54f1e-e72f-4b2b-853f-dca14fed87fb&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ff28bdcb-b795-4c0e-8a03-d43b9fcbff05&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":1}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutContainer" version="8550"&gt;&lt;ns2:id&gt;2a21989c-ae42-4e84-80b2-3d4922dcd758&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutContainer9&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;31c90a13-3856-4bd3-8b69-c46503d60e01&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":11}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;70b9b0e5-6d16-447e-8bbe-b80c337ea08b&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell9&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c738c7df-b6ef-4885-8835-3f53e009d91d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;VERTICAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;5fa0a34b-f1dc-4906-800c-a97c603a9503&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text3&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4a039874-6963-4c86-886a-e2167007a662&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5fe1840f-81e3-435c-8101-4a1ec3f9fd26&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Comment&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4e5d22b9-abd7-4a4c-892b-7a19dfd6c93a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;c2cf53e8-097f-4628-8fc1-37a8e654bfe1&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;54270f5b-2948-4336-84ca-05d31c94d542&lt;/ns2:id&gt;&lt;ns2:optionName&gt;printOverflow&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;81b5da13-1eaf-4107-8674-9772f4414f99&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7bbc6d53-0b49-4b09-87cf-d362e433a3dd&lt;/ns2:id&gt;&lt;ns2:optionName&gt;height&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.0e61869e-73fd-4401-b156-8c11adaec3f8&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.stepLog.comment&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;ns2:gridContainerLayoutItems xsi:type="ns2:GridLayoutCell" version="8550"&gt;&lt;ns2:id&gt;be2cafb6-c223-4fc7-809e-fd7938dc0e74&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;GridLayoutCell1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;91f558d3-ef2c-468c-8781-b6b2369b19fc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalSpan&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":10},{"deviceConfigID":"SmallID","value":12}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a2839375-fe5c-4188-8888-5b88dc366d75&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@horizontalAlignment&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"RIGHT"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:direction&gt;HORIZONTAL&lt;/ns2:direction&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;1a15b3a1-**************-b90aafb5c861&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;isLiquidated&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6345cfb5-b1b5-48fa-8674-1aa9f5383385&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Liquidation Created&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b5fe358d-72e7-4e64-868b-e631334e842d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8614dbee-5e7c-4c37-877d-30de42293956&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"HIDDEN"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.fffd1628-baee-44e8-b7ca-5ae48644b0be&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.options.actionConditions.isLiquidated&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;623252a0-d638-4d98-8cb3-40d132d9ae70&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;saveState&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;09b4a9cf-d33f-4bd0-86fa-5818b1067b69&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Save&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6566ca05-0e69-43b4-8d60-9b4cfd4b067c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a6d1fb2d-845e-43bf-8e71-b99980d9f9d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cb790568-a5b5-4a3d-82ad-c4385dd41715&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;B&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;95aba975-8cb9-4e89-84dd-ae1b5a5a6184&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;55cc9e36-a88d-4b4e-850f-7bd0f62ef497&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;068aa8c7-137f-4a31-8978-2d79d1f87cd7&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1e318248-2bb5-4c28-8b97-e39cd86ccc10&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;760c5652-c984-46ce-89d3-bb743819613e&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;alert( me.ui.getAbsoluteName() )&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;ns2:gridCellLayoutItems xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;351de0b7-6c37-4db3-88cf-cd2393baae6e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;submit&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;b8165fc6-2bef-4600-8d17-cbbd14a62ae4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Submit&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7dde2648-1619-40fe-8eb1-a86ab6de6ffc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;54a08579-6fcb-451b-8857-3c06eac16d7a&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8846fc24-88ff-470c-88f7-d9102a70ec54&lt;/ns2:id&gt;&lt;ns2:optionName&gt;shapeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;R&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0c1af7d4-71cb-46f6-8a29-1729f3979264&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;S&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a0537c1a-6bf4-4e22-8b43-24c1b67b92ed&lt;/ns2:id&gt;&lt;ns2:optionName&gt;outline&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3b870918-00ef-45a2-8f58-5a68bd769365&lt;/ns2:id&gt;&lt;ns2:optionName&gt;ghostMode&lt;/ns2:optionName&gt;&lt;ns2:value&gt;false&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;71d8c93f-ecd5-4ffe-8d1e-9b40040ab7ab&lt;/ns2:id&gt;&lt;ns2:optionName&gt;iconLocation&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"R"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;38404cbc-eb83-496f-89e4-4914602bbfa4&lt;/ns2:id&gt;&lt;ns2:optionName&gt;icon&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":""}]}&lt;/ns2:value&gt;&lt;ns2:valueType&gt;static&lt;/ns2:valueType&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc104e69-5831-4c95-8451-1eae4fec1e35&lt;/ns2:id&gt;&lt;ns2:optionName&gt;sizeStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"D"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;7daf668e-9881-440b-8f3d-6934c183ae4c&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CLICK&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;6deefe61-b56f-4eb9-8377-c17f27ef33bb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//alert( me.ui.getAbsoluteName() )&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4269b418-05e0-4ac4-806f-9c0faf0497d5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_BOUNDARYEVT&lt;/ns2:optionName&gt;&lt;ns2:value&gt;${actionList}.setAction(); &lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;&lt;/ns2:binding&gt;&lt;/ns2:gridCellLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:gridContainerLayoutItems&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction></loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction></changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>1de9ae94-e96e-4b0b-a9dc-8cc3e2f59b9d</guid>
        <versionId>a4863016-bca6-415f-9407-7cd45040cbde</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="appinfo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.a278377a-9724-4710-984a-b787d6179397</coachViewBindingTypeId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <classId>341e5684-f12d-4cf6-aac8-d2dc77d648ab/12.e7100a5a-462f-4f06-a0ea-5f9c5b209bd6</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>af964fa3-adcb-4238-b3b8-0fc64b6c9811</guid>
            <versionId>2e29cc27-c95d-49e4-b8c4-76f6d5c8229e</versionId>
        </bindingType>
        <configOption name="stepLog">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.44af1cd5-d132-47c5-ba78-11e19d7d7edf</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.e4654440-58a7-47b2-8f98-3eaa9cccad49</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>9372713b-8958-43af-ad6b-5157f4271873</guid>
            <versionId>7a526bc8-bca0-4541-aca0-2982cf0a7b06</versionId>
        </configOption>
        <configOption name="action">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.5fed2b8c-68dd-471b-81cd-113c1d580c3e</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>true</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>68e17316-da1d-4b1f-8892-44ab55783b7e</guid>
            <versionId>11630f56-4e67-4e5b-a388-8ba5ab9bce35</versionId>
        </configOption>
        <configOption name="selectedAction">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.211201fc-be1c-48cf-a7e7-ba0e08097527</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>2</seq>
            <description></description>
            <groupName></groupName>
            <guid>c29aa599-7591-470f-aa3e-713617ef8e82</guid>
            <versionId>67e81d09-7072-4374-9c94-c6656a496c9f</versionId>
        </configOption>
        <configOption name="complianceApproval">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.edd6ef26-d4d6-45f8-9e98-118c23c65974</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>3</seq>
            <description></description>
            <groupName></groupName>
            <guid>76379fce-2cfb-47c7-bece-4b9bfdb16821</guid>
            <versionId>b133e2f6-fadf-44a1-8e32-3ccc66245b9e</versionId>
        </configOption>
        <configOption name="complianceApprovalVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.19090976-0ac1-4ce7-b322-edca722ada00</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>4</seq>
            <description></description>
            <groupName></groupName>
            <guid>27ccd8ad-824e-417f-a00f-d320784c866e</guid>
            <versionId>22b0c3d9-02cf-47cf-9aa4-1029c03c4ef1</versionId>
        </configOption>
        <configOption name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.89030d45-dd39-4cad-b6a7-d749e38d3cdc</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>5</seq>
            <description></description>
            <groupName></groupName>
            <guid>65197f99-f612-45cf-9f63-8a6d9556e694</guid>
            <versionId>c2def6a8-f329-4fbf-b00c-33f98c8f1895</versionId>
        </configOption>
        <configOption name="terminateReasonVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.aeacaa5d-0c93-4747-972e-09a49025de74</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>6</seq>
            <description></description>
            <groupName></groupName>
            <guid>1e642dbd-e85a-40c0-bf3a-ee131dc18cae</guid>
            <versionId>5483f728-2bc8-4991-a9e5-3eb6f70bd690</versionId>
        </configOption>
        <configOption name="errorPanelVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.f5ef53b0-b6df-4db2-9f7b-2b44db251720</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>7</seq>
            <description></description>
            <groupName></groupName>
            <guid>8809e0da-9a1d-4d03-9d37-3de3c5987153</guid>
            <versionId>7fec6e17-b938-4042-98c4-0016ec21695a</versionId>
        </configOption>
        <configOption name="actionConditions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.5df9b584-9924-4086-83ee-c0b9cbda73db</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>/12.004a1efa-0a17-40a6-a5b9-125042216ff4</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>8</seq>
            <description></description>
            <groupName></groupName>
            <guid>9cb2ef47-adbd-4fda-9a87-9f33f94c3cad</guid>
            <versionId>45898804-9ed3-482b-88af-a35427d80095</versionId>
        </configOption>
        <configOption name="returnReasonVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.3fecbbf2-a3f7-48a8-b8f5-711f9c6142b9</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>9</seq>
            <description></description>
            <groupName></groupName>
            <guid>5f30f90e-4595-4a5e-81ae-4bb1bd1092cc</guid>
            <versionId>a9a31d6e-8b1a-4712-a342-ea2325914ef4</versionId>
        </configOption>
        <configOption name="tradeFoComment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.26c6c110-dfc0-428e-85f7-e894ba21d52e</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>10</seq>
            <description></description>
            <groupName></groupName>
            <guid>a49a45e6-cb22-4a23-a61f-cda8e03531b0</guid>
            <versionId>04bf0794-a191-4018-a225-52efbc7e144f</versionId>
        </configOption>
        <configOption name="tradeFoCommentVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.44aba9c0-2938-484f-ba24-f789f2fe2b30</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>11</seq>
            <description></description>
            <groupName></groupName>
            <guid>fbf2af97-b9fc-472a-be78-f1b775b4cbc0</guid>
            <versionId>c66b06aa-d4a1-4ea7-a0b9-4152ce4c0f37</versionId>
        </configOption>
        <configOption name="exeHubMkrComment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.8ae3669b-087b-47c1-8b15-9c323204f216</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>12</seq>
            <description></description>
            <groupName></groupName>
            <guid>8c6e6789-6f71-4e2e-9974-f479ee441141</guid>
            <versionId>cf7552a0-5fd4-4eed-983b-344a6245d43f</versionId>
        </configOption>
        <configOption name="exeHubMkrCommentVis">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.afc9e1a8-fa7d-418f-8a52-54f74520bcf3</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>13</seq>
            <description></description>
            <groupName></groupName>
            <guid>cf1277b4-1eb1-4c64-92a5-86108e99356e</guid>
            <versionId>dc3cdecc-bb27-430d-8269-bcaba225db44</versionId>
        </configOption>
        <configOption name="compcheckerComment">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.9b45db2b-2f0a-4e53-96db-9ba48ba77d3e</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>14</seq>
            <description></description>
            <groupName></groupName>
            <guid>f7769732-e305-45a3-ab9b-755bd2dffac0</guid>
            <versionId>2f5263e2-9b1a-4c2b-b538-f95d6983d0ca</versionId>
        </configOption>
        <configOption name="compcheckerCommentVIS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.47e3416e-eecf-4d15-8383-5044a4a36fd5</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>15</seq>
            <description></description>
            <groupName></groupName>
            <guid>5d06a419-da2d-4e67-a119-6dd1a6dbe44f</guid>
            <versionId>8c8da9ca-ef2b-4f06-8d93-a016f481ae96</versionId>
        </configOption>
        <configOption name="disableSubmit">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.5c07b7b6-67b1-4d35-b48f-766870404a4f</coachViewConfigOptionId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>16</seq>
            <description></description>
            <groupName></groupName>
            <guid>c2a89daa-af8b-4a3d-9cc6-8757e3c3b410</guid>
            <versionId>21f14305-2ceb-4ec6-81cd-d5d5330ee5f2</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.1ec7b036-dcea-4c98-b033-c4807635d0cd</coachViewInlineScriptId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>&#xD;
//---------------VALIDATION FUNCTIONS---------------------------------------------------------&#xD;
&#xD;
/* &#xD;
How to use:&#xD;
    - put these functions at the inline JS of the template.&#xD;
	- in the CSHS script, add the following line:&#xD;
		var lib = bpmext.ui.getView('templatePathName');&#xD;
	- at te start of the script, add the following line, to clear the error list:&#xD;
		lib.setErrorList();&#xD;
	- use the functions as follows:&#xD;
		lib.addError(path, message);&#xD;
		lib.mandatory(value, path, message);&#xD;
	- at the end of the script, add the following line, to get the error list:&#xD;
		lib.getErrorList();&#xD;
*/&#xD;
&#xD;
var dateOptions = { day: "numeric", month: "long", year: "numeric" };&#xD;
var validationList = [];&#xD;
&#xD;
/* Add a coach validation error if the field is empty, message is OPTIONAL!!&#xD;
mandatory(tw.local.input, "tw.local.input", message : concat) : CSHS */&#xD;
this.mandatory = function (value, path, message) {&#xD;
&#xD;
	message = message || 'This field is Mandatory';&#xD;
&#xD;
	if (value == null || value == undefined) {&#xD;
		addError(path, message);&#xD;
		return false;&#xD;
	} else {&#xD;
		switch (typeof value) {&#xD;
			case "string":&#xD;
				if (value.trim().length == 0) {&#xD;
					addError(path, message);&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
			case "number":&#xD;
				if (value == 0.0 || value &lt; 0.0) {&#xD;
					addError(path, message);&#xD;
					return false;&#xD;
				}&#xD;
				break;&#xD;
		}&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error 		&#xD;
EX: addError(tw.local.name , 'validation message!')*/&#xD;
function addError (path, message) {&#xD;
	&#xD;
	if (validationList == null) {&#xD;
		validationList = [];&#xD;
	}&#xD;
	validationList.push({&#xD;
		errorBOPath: path,&#xD;
		errorMessage: message,&#xD;
	});&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is in the past today.&#xD;
   EX: notPastDate(tw.local.date , 'tw.local.date' , 'validation message!' , false)*/&#xD;
this.notPastDate = function (value, path, exclude, message) {&#xD;
	if (!value) return;&#xD;
	message = message || "Please input a valid future date";&#xD;
&#xD;
	var checkDate = this.getMidNightDate();&#xD;
	//exclude today?&#xD;
	if (exclude &amp;&amp; value &lt; checkDate) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	if (!exclude &amp;&amp; value &lt;= new Date()) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is between two dates.&#xD;
   EX: dateBetween(tw.local.date,'tw.local.date', date1 , date2,'validation message!')*/&#xD;
this.dateBetween = function (value, path, date1, date2, message) {&#xD;
	if (!value) return;&#xD;
&#xD;
	message =&#xD;
		message ||&#xD;
		`This date must be between ${date1.toLocaleDateString(&#xD;
			"en-GB",&#xD;
			dateOptions&#xD;
		)} and ${date2.toLocaleDateString("en-GB", dateOptions)}`;&#xD;
&#xD;
	if (value &amp;&amp; value &gt; date1 &amp;&amp; value &lt; date2) {&#xD;
		return true;&#xD;
	}&#xD;
&#xD;
	this.addError(path, message);&#xD;
	return false;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the date is in the future and the last variable is OPTIONAL to exclude today.&#xD;
  EX:	notFutureDate(tw.local.date, 'tw.local.date', 'validation message!' , false)*/&#xD;
this.notFutureDate = function (value, path, exclude, message) {&#xD;
	if (!value) return;&#xD;
	&#xD;
	message = message || "Please input a valid date in the past";&#xD;
&#xD;
	var checkDate = this.getMidNightDate();&#xD;
	//exclude today?&#xD;
	if (exclude &amp;&amp; value &gt; checkDate) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	if (!exclude &amp;&amp; value &gt;= new Date()) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the STRING length is greater than given length (len).&#xD;
  EX:	maxLength(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/&#xD;
this.maxLength = function (value, path, len, message) {&#xD;
	if (!value) return;&#xD;
	message = message || `This field can only contain up to ${len} characters.`;&#xD;
	if (value.length &gt; len) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the STRING length is less than given length (len).&#xD;
   EX: minLength(tw.local.name , 'tw.local.name', 10, 'validation message!')*/&#xD;
this.minLength = function (value, path, len, message) {&#xD;
	if (!value) return;&#xD;
&#xD;
	message = message || `This field must contain at least ${len} characters.`;&#xD;
	if (value.length &lt; len) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the number is greater than given value (max).&#xD;
 * EX: maxNumber(tw.local.name , 'tw.local.name' , 14 ,'validation message!')*/&#xD;
this.maxNumber = function (value, path, max, message) {&#xD;
	if (!value) return;&#xD;
	&#xD;
	message = message || `This value must be &lt; ${max}`;&#xD;
	if (!isNaN(value) &amp;&amp; value &gt; max) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
/* Add a coach validation error if the number is less than given value (min).&#xD;
   EX: minNumber(tw.local.name , 'tw.local.name' , 14 , 'validation message!')*/&#xD;
this.minNumber = function (value, path, min, message) {&#xD;
	if (!value) return;&#xD;
	&#xD;
	message = message || `This value must be &gt; ${min}`;&#xD;
	if (!isNaN(value) &amp;&amp; value &lt; min) {&#xD;
		this.addError(path, message);&#xD;
		return false;&#xD;
	}&#xD;
	return true;&#xD;
};&#xD;
&#xD;
// FOR VIEW&#xD;
//prevents input length exceeding len, only works for string and on input event&#xD;
this.maxLenOnInput = function (me, potential, len, message) {&#xD;
	if (potential.length &gt; len) {&#xD;
		var label = me.getLabel();&#xD;
		message = message || `${label} can only contain up to ${len} characters.`;&#xD;
		me.setValid(false, message);&#xD;
		return false;&#xD;
	}&#xD;
	me.setValid(true);&#xD;
	return true;&#xD;
};&#xD;
&#xD;
this.isChecker = function (fun, me, ...args) {&#xD;
	if (!me) return;&#xD;
	if (me.getVisibility() == "READONLY" || me.getVisibility() == "HIDDEN") {&#xD;
		return;&#xD;
	}&#xD;
	return fun(...args);&#xD;
};&#xD;
//-------------------------------------Private-----------------------------------------------------&#xD;
// returns 'new Date()' but reset it to '00:00:00'.&#xD;
this.getMidNightDate = function () {&#xD;
	var midNightDate = new Date();&#xD;
	// Set hours, minutes, seconds and milliseconds to 0&#xD;
	midNightDate.setHours(0);&#xD;
	midNightDate.setMinutes(0);&#xD;
	midNightDate.setSeconds(0);&#xD;
	midNightDate.setMilliseconds(0);&#xD;
&#xD;
	return midNightDate;&#xD;
};&#xD;
&#xD;
// getter for 'validationErrors', input is (tw.system.coachValidation).&#xD;
this.getErrorList = function (coachValidation) {&#xD;
	coachValidation.validationErrors = validationList;&#xD;
	// this.setErrorList();&#xD;
      validationList = [];&#xD;
}&#xD;
&#xD;
// setter for 'validationErrors'.&#xD;
this.setErrorList = function () {&#xD;
	validationList = [];&#xD;
};&#xD;
&#xD;
this.camelCaseToTitle = function (camelCase) {&#xD;
	var fieldName = camelCase.split(".").pop();&#xD;
	// Convert camelCase to Title Case&#xD;
	var titleCase = fieldName.replace(/([A-Z])/g, " $1");&#xD;
	// Uppercase the first character&#xD;
	titleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);&#xD;
	return titleCase;&#xD;
};&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>ec33e843-6f2d-498b-af37-058eff9105e5</guid>
            <versionId>2255547a-c51f-457c-a503-f7d184b46389</versionId>
        </inlineScript>
        <inlineScript name="Inline CSS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.151e60af-454b-4f0e-a95b-d2c7cf2ca5b5</coachViewInlineScriptId>
            <coachViewId>64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f</coachViewId>
            <scriptType>CSS</scriptType>
            <scriptBlock>/* Main container for tabs */&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {&#xD;
    display: flex;&#xD;
    flex-wrap: wrap; /* Allow tabs to wrap onto multiple lines */&#xD;
    align-items: center;&#xD;
    padding: 5px;&#xD;
    border-bottom: 1px solid #ddd;&#xD;
    background-color: #f8f9fa;&#xD;
    list-style-type: none; /* Remove default list styling */&#xD;
    margin: 0;&#xD;
}&#xD;
&#xD;
/* Style each tab */&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs &gt; li {&#xD;
    /*margin-right: 5px;*/&#xD;
    margin-bottom: 2px;&#xD;
    list-style-type: none; /* Ensure no bullets */&#xD;
    white-space: nowrap; /* Prevent text wrapping inside each tab */&#xD;
}&#xD;
&#xD;
/* Tab anchor styling */&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs &gt; li &gt; a {&#xD;
    display: block;&#xD;
    padding: 5px 5px;&#xD;
    background-color: #ffffff;&#xD;
    border: 1px solid #ddd;&#xD;
    border-radius: 3px;&#xD;
    color: #007bff;&#xD;
    text-decoration: none;&#xD;
    transition: background-color 0.3s ease, color 0.3s ease;&#xD;
    height:26px;&#xD;
}&#xD;
&#xD;
/* Active tab and hover state */&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs &gt; li.active &gt; a,&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs &gt; li &gt; a:hover {&#xD;
    background-color: #007bff;&#xD;
    color: #ffffff;&#xD;
}&#xD;
&#xD;
/* Hide the secondary dropdown menu if it exists */&#xD;
ul.nav.nav-tabs-mnu.nav-tabs-simple.tabs-primary {&#xD;
    display: none !important;&#xD;
}&#xD;
&#xD;
/* Make sure the first &lt;ul&gt; doesn't get altered by dropdown behavior */&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-toggle {&#xD;
    display: none !important;&#xD;
}&#xD;
&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs .dropdown-menu {&#xD;
    display: none !important;&#xD;
}&#xD;
&#xD;
/* Responsive adjustments to ensure tabs wrap correctly */&#xD;
@media (max-width: 768px) {&#xD;
    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs {&#xD;
        flex-direction: row;&#xD;
        flex-wrap: wrap; /* Ensure tabs wrap on small screens */&#xD;
    }&#xD;
&#xD;
    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs &gt; li {&#xD;
        margin-right: 0;&#xD;
        white-space: normal; /* Allow text to wrap within tabs */&#xD;
    }&#xD;
&#xD;
    ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs &gt; li &gt; a {&#xD;
        text-align: center; /* Center text within tabs */&#xD;
    }&#xD;
}&#xD;
/* Ensure tabs don't leave white space when removed */&#xD;
ul.nav.nav-tabs.nav-tabs-strip.nav-tabs-simple.tabs-primary.tab-btn-xs &gt; li.hidden {&#xD;
    display: none !important;&#xD;
}&#xD;
&#xD;
/* Ensure all tabs are displayed */&#xD;
.nav-tabs &gt; li {&#xD;
    display: block !important; /* Override any display:none */&#xD;
    /*visibility: visible !important; /* Ensure visibility */&#xD;
}</scriptBlock>
            <seq>1</seq>
            <description></description>
            <guid>0c64f21f-f918-44a3-81c0-f6c84af1c872</guid>
            <versionId>d97115a5-6d89-49a8-a457-29e148b67631</versionId>
        </inlineScript>
    </coachView>
</teamworks>

