{"id": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "versionId": "12a25f86-b68a-4af4-958b-1a646d73db0c", "name": "Query BC Contract", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "Query BC Contract", "lastModified": "1699596998385", "lastModifiedBy": "<PERSON><PERSON>", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.fb6629d3-6ae1-457b-8b38-af38db543c30", "2025.fb6629d3-6ae1-457b-8b38-af38db543c30"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdb", "versionId": "12a25f86-b68a-4af4-958b-1a646d73db0c", "dependencySummary": "<dependencySummary id=\"bpdid:a6be0630e884ccca:-427a0b52:18bb428309d:-1f82\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4\"},{\"incoming\":[\"4eb3106f-4d5a-4a4f-80b2-82c6bd88d120\",\"d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"2d13efc7-fd6a-4ee2-88b6-df11c91426d9\"},{\"targetRef\":\"fb6629d3-6ae1-457b-8b38-af38db543c30\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d\",\"sourceRef\":\"a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4\"},{\"startQuantity\":1,\"outgoing\":[\"f883374b-953c-4fb6-8cf7-f7a22b978a04\"],\"incoming\":[\"2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":105,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[],\"activityType\":[\"CalledProcess\"]},\"name\":\"MW_FC Query DC Contract\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.prefix\"]}}]},{\"targetRef\":\"2055.4ff12b5f-6b69-4504-a730-046ee5c2000a\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user_id\"]}}]},{\"targetRef\":\"2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"ODC Creation \\/ Amendment Process Details\\\"\"]}}]},{\"targetRef\":\"2055.8f786b77-ae66-4547-a444-d6ccb8969c42\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.ContractDetails.BasicDetails.flexCubeContractNo\"]}}]},{\"targetRef\":\"2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.currentProcessInstanceID\"]}}]},{\"targetRef\":\"2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.42effea6-8d8b-437c-958a-da5cf9119674\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"fb6629d3-6ae1-457b-8b38-af38db543c30\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.95835270-515f-4794-a207-a5e2aa301c0e\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorCode\"]}}],\"sourceRef\":[\"2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMSG\"]}}],\"sourceRef\":[\"2055.ee7811d9-22b1-4727-9148-bcac74c306af\"]},{\"assignment\":[{\"to\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.queryBCContractResults\"]}}],\"sourceRef\":[\"2055.004a9996-2e6b-4d60-821e-5db8e4ba2271\"]}],\"calledElement\":\"1.3875c748-15fc-40ef-bef1-ea4d905d7f75\"},{\"parallelMultiple\":false,\"outgoing\":[\"c4d36477-c1e7-439b-8bf0-9a1da68c66d9\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"e0f92768-0b1d-4714-8693-1a9cae9bed20\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"8598f4cb-ae21-49e1-84ea-153ead3bb56f\",\"otherAttributes\":{\"eventImplId\":\"d0616793-7785-4615-881e-a59f1e877202\"}}],\"attachedToRef\":\"fb6629d3-6ae1-457b-8b38-af38db543c30\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":140,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error7\",\"declaredType\":\"boundaryEvent\",\"id\":\"3a2bfe99-d7d8-4de6-873a-f7957c638f19\",\"outputSet\":{}},{\"targetRef\":\"ab240dfe-8d20-4a88-8e96-fdd10ff72c98\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To isSuccessful?\",\"declaredType\":\"sequenceFlow\",\"id\":\"f883374b-953c-4fb6-8cf7-f7a22b978a04\",\"sourceRef\":\"fb6629d3-6ae1-457b-8b38-af38db543c30\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorCode\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ad7cdaa4-504c-4708-8344-a1dc47262608\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMSG\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ed0addfb-42f9-4e4d-8bd2-ae08e8d7b003\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2cd964e1-a67e-485f-8103-0a50c17067c8\"},{\"itemSubjectRef\":\"itm.12.*************-4cbb-a781-44d233d577c6\",\"name\":\"queryBCContractResults\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.0ec55024-95a1-4599-8cd5-980f0273de16\"},{\"outgoing\":[\"725ef966-f992-4b08-889c-ede6a8e46c53\",\"d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619\"],\"incoming\":[\"f883374b-953c-4fb6-8cf7-f7a22b978a04\"],\"default\":\"725ef966-f992-4b08-889c-ede6a8e46c53\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":236,\"y\":99,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"isSuccessful?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"ab240dfe-8d20-4a88-8e96-fdd10ff72c98\"},{\"targetRef\":\"304679b7-62fb-4dd1-8d05-2a1f24393037\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To Mapping\",\"declaredType\":\"sequenceFlow\",\"id\":\"725ef966-f992-4b08-889c-ede6a8e46c53\",\"sourceRef\":\"ab240dfe-8d20-4a88-8e96-fdd10ff72c98\"},{\"startQuantity\":1,\"outgoing\":[\"4eb3106f-4d5a-4a4f-80b2-82c6bd88d120\"],\"incoming\":[\"725ef966-f992-4b08-889c-ede6a8e46c53\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":421,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Mapping\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"304679b7-62fb-4dd1-8d05-2a1f24393037\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if(!!tw.local.queryBCContractResults)\\r\\n{\\r\\n\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;\\r\\n\\/\\/\\ttw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);\\r\\n\\ttw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;\\r\\n\\ttw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);\\r\\n\\t\\r\\n\\/\\/\\ttw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;\\r\\n\\/\\/      tw.local.ContractDetails.CustomerInfo.customerName = tw.local.queryBCContractResults.TXTCUSTNAME;\\r\\n\\t\\r\\ntw.local.ContractDetails.Parties = new tw.object.odcParties();\\r\\n\\r\\n\\t\\r\\n\\tfor(var i=0;i<tw.local.queryBCContractResults.Contract_Parties.listLength;i++)\\r\\n\\t{\\r\\n\\t\\tif(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \\\"COLLECTING BANK\\\")\\r\\n\\t\\t{\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\\t\\t\\t\\r\\n\\r\\n\\t\\t}\\r\\n\\t\\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \\\"DRAWEE\\\")\\r\\n\\t\\t{\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\\r\\n\\r\\n\\t\\t}\\r\\n\\t\\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \\\"DRAWER\\\")\\r\\n\\t\\t{\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\\r\\n\\t\\t}\\r\\n\\t\\telse\\r\\n\\t\\t{\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\\r\\n\\t\\t\\ttw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\\t\\t\\t\\r\\n\\r\\n\\t\\t}\\r\\n\\r\\n\\t}\\r\\n\\/\\/tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\r\\n\\/\\/\\t\\r\\n\\/\\/\\tif(tw.local.queryBCContractResults.Contract_Multitnr != null)\\r\\n\\/\\/\\t{\\r\\n\\/\\/\\t\\tfor(i=0;i<tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)\\r\\n\\/\\/\\t\\t{\\r\\n\\/\\/\\t\\t\\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();\\r\\n\\/\\/\\t\\t\\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);\\r\\n\\/\\/\\t\\t\\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);\\r\\n\\/\\/\\t\\t}\\r\\n\\/\\/\\r\\n\\/\\/\\t}\\r\\ntw.local.ContractDetails.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\t\\r\\n\\tif(tw.local.queryBCContractResults.Charges != null && tw.local.queryBCContractResults.Charges[0] != null)\\r\\n\\t{\\r\\n\\t\\tfor(i=0;i<tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)\\r\\n\\t\\t{\\r\\n\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();\\r\\n\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;\\r\\n\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;\\r\\n\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;\\r\\n\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();\\r\\n\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;\\r\\n\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;\\r\\n\\t\\t\\tif(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == \\\"y\\\")\\r\\n\\t\\t\\t{\\r\\n\\t\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;\\r\\n\\t\\t\\t}\\r\\n\\t\\t\\telse\\r\\n\\t\\t\\t{\\r\\n\\t\\t\\t\\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;\\t\\t\\t\\r\\n\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\t\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\/\\/\\tif (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency==\\\"\\\")\\r\\n\\/\\/\\t\\ttw.local.queryBCContractResults.Currency= \\\"USD\\\";\\r\\n\\r\\n\\/\\/\\t\\ttw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;\\r\\n\\/\\/\\t\\ttw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;\\t\\t\\r\\n}\\r\\n\\r\\n\\t\\/\\/tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();\\r\\n\\t\\/\\/tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;\\r\\n\\t\\/\\/tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;\\r\\n\\r\\n\\t\\r\\n\\t\"]}},{\"targetRef\":\"2d13efc7-fd6a-4ee2-88b6-df11c91426d9\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"4eb3106f-4d5a-4a4f-80b2-82c6bd88d120\",\"sourceRef\":\"304679b7-62fb-4dd1-8d05-2a1f24393037\"},{\"targetRef\":\"2d13efc7-fd6a-4ee2-88b6-df11c91426d9\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\\t  ==\\t  false\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619\",\"sourceRef\":\"ab240dfe-8d20-4a88-8e96-fdd10ff72c98\"},{\"startQuantity\":1,\"outgoing\":[\"f1ca6063-0c89-4796-834d-523c900b68ff\"],\"incoming\":[\"c4d36477-c1e7-439b-8bf0-9a1da68c66d9\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":423,\"y\":181,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Catch Errors\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"544bc577-**************-1af3744c36b3\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"if (tw.local.error != undefined && !!tw.local.error.errorText) {\\r\\n\\ttw.local.errorMSG = tw.local.error.errorText;\\r\\n}else if (tw.system.error != undefined && tw.system.error != null) {\\r\\n\\ttw.local.errorMSG = String(tw.system.error.getAttribute(\\\"type\\\")) +\\\",\\\"+ String(tw.system.error.getElementByTagName(\\\"localizedMessage\\\").item(0).getText());\\r\\n\\ttw.local.error = new tw.object.AjaxError();\\r\\n}else{\\r\\n\\ttw.local.error = new tw.object.AjaxError();\\r\\n}\\r\\n\\r\\ntw.local.error.errorText = \\\"Error Occured<br> Service Name : \\\"+tw.system.serviceFlow.name+\\\"<br> Error Message : \\\"+tw.local.errorMSG;\"]}},{\"targetRef\":\"544bc577-**************-1af3744c36b3\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Catch Errors\",\"declaredType\":\"sequenceFlow\",\"id\":\"c4d36477-c1e7-439b-8bf0-9a1da68c66d9\",\"sourceRef\":\"3a2bfe99-d7d8-4de6-873a-f7957c638f19\"},{\"incoming\":[\"f1ca6063-0c89-4796-834d-523c900b68ff\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"errorCode\":\"\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"06b11757-8691-404d-88c5-7931dd3e8286\",\"otherAttributes\":{\"eventImplId\":\"86a88c86-a921-49e5-861b-2b138e6be302\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":557,\"y\":204,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"End Event\",\"declaredType\":\"endEvent\",\"id\":\"bc2bdd28-628c-42a1-8f18-62a326b9e796\"},{\"targetRef\":\"bc2bdd28-628c-42a1-8f18-62a326b9e796\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End Event\",\"declaredType\":\"sequenceFlow\",\"id\":\"f1ca6063-0c89-4796-834d-523c900b68ff\",\"sourceRef\":\"544bc577-**************-1af3744c36b3\"}],\"laneSet\":[{\"id\":\"b37d2936-f644-4e7e-81bf-f1892f66dcb1\",\"lane\":[{\"flowNodeRef\":[\"a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4\",\"2d13efc7-fd6a-4ee2-88b6-df11c91426d9\",\"fb6629d3-6ae1-457b-8b38-af38db543c30\",\"3a2bfe99-d7d8-4de6-873a-f7957c638f19\",\"ab240dfe-8d20-4a88-8e96-fdd10ff72c98\",\"304679b7-62fb-4dd1-8d05-2a1f24393037\",\"544bc577-**************-1af3744c36b3\",\"bc2bdd28-628c-42a1-8f18-62a326b9e796\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"ddbc1ae6-7303-4703-85fb-5b76369a2ea0\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Query BC Contract\",\"declaredType\":\"process\",\"id\":\"1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"ContractDetails\",\"isCollection\":false,\"id\":\"2055.1447a720-69f1-46ca-8d27-88a12b035edf\"}],\"inputSet\":[{\"dataInputRefs\":[\"2055.9465f3bb-e80a-444e-8672-899423d668fb\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.1447a720-69f1-46ca-8d27-88a12b035edf\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.ODCRequest();\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new TWDate();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = new tw.object.BasicDetails();\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"599IAVC222440002\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = new tw.object.FCCollections();\\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new TWDate();\\nautoObject.FcCollections.ToDate = new TWDate();\\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = new tw.object.ODCCollection();\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = new tw.object.ReversalReason();\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = new tw.object.ContractCreation();\\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new TWDate();\\nautoObject.ContractCreation.valueDate = new TWDate();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new TWDate();\\nautoObject.Parties = new tw.object.odcParties();\\nautoObject.Parties.Drawer = new tw.object.Drawer();\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = new tw.object.Drawee();\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = new tw.object.StepLog();\\nautoObject.stepLog.startTime = new TWDate();\\nautoObject.stepLog.endTime = new TWDate();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = new tw.object.listOf.StepLog();\\nautoObject.complianceComments[0] = new tw.object.StepLog();\\nautoObject.complianceComments[0].startTime = new TWDate();\\nautoObject.complianceComments[0].endTime = new TWDate();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = new tw.object.listOf.StepLog();\\nautoObject.History[0] = new tw.object.StepLog();\\nautoObject.History[0].startTime = new TWDate();\\nautoObject.History[0].endTime = new TWDate();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"ContractDetails\",\"isCollection\":false,\"id\":\"2055.9465f3bb-e80a-444e-8672-899423d668fb\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "ContractDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.9465f3bb-e80a-444e-8672-899423d668fb", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"599IAVC222440002\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "1b123bfd-ab13-4a17-9e35-3b53f1f05858", "versionId": "c7f8b180-a3c5-4233-b1b1-7ce1e08bc181"}, {"name": "ContractDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.1447a720-69f1-46ca-8d27-88a12b035edf", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3c5b0515-0b0d-4365-b99c-dcffeb7b37f3", "versionId": "de965ad7-7e69-4bce-8660-1f8714211f0f"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.fadbd4a8-eb37-44b9-9536-2302e262f759", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "parameterType": "3", "isArrayOf": "false", "classId": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "be3ece56-576f-4b02-9ebc-d9aa51f45b71", "versionId": "3bc42887-1097-4152-a341-81cc27066b51"}], "processVariable": [{"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ad7cdaa4-504c-4708-8344-a1dc47262608", "description": {"isNull": "true"}, "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f0c3aa9e-4b39-4001-9e9f-0f3b1befc056", "versionId": "5698ae96-4306-4015-b78b-ed076cfd981b"}, {"name": "errorMSG", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ed0addfb-42f9-4e4d-8bd2-ae08e8d7b003", "description": {"isNull": "true"}, "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c8c7c03c-e29d-4e80-8730-5a835d1478b5", "versionId": "d8167f23-e8a0-4cef-ace0-3dd471577033"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2cd964e1-a67e-485f-8103-0a50c17067c8", "description": {"isNull": "true"}, "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b5a5420a-ca21-425c-9337-3494988d7715", "versionId": "fd193d35-d1ba-4d42-90d3-68f841051325"}, {"name": "queryBCContractResults", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0ec55024-95a1-4599-8cd5-980f0273de16", "description": {"isNull": "true"}, "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "51771243-cb9e-427e-9e3a-b2dbb03f88bb", "versionId": "11ffe0db-af07-439b-9407-c29fc7ac34f2"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.cbdb548d-fbc6-4394-b2f9-39f2cea25888", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd", "versionId": "10ae37e0-00ae-4131-9811-03a6d828dff2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.cbdb548d-fbc6-4394-b2f9-39f2cea25888", "haltProcess": "false", "guid": "5b404c69-8937-4eba-9f11-aa9d3a251736", "versionId": "c58f1025-a3af-44b0-a0e1-cab599bc8f32"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.544bc577-**************-1af3744c36b3", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "Catch Errors", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.0eb26366-fdbc-4a04-a5c5-90d9d502954e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7be1ec94:2ff3", "versionId": "48b722b8-7913-47f1-9156-e5b124119467", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "423", "y": "181", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.0eb26366-fdbc-4a04-a5c5-90d9d502954e", "scriptTypeId": "2", "isActive": "true", "script": "if (tw.local.error != undefined && !!tw.local.error.errorText) {\r\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\r\n}else if (tw.system.error != undefined && tw.system.error != null) {\r\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}else{\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}\r\r\n\r\r\ntw.local.error.errorText = \"Error Occured<br> Service Name : \"+tw.system.serviceFlow.name+\"<br> Error Message : \"+tw.local.errorMSG;", "isRule": "false", "guid": "96f07f83-4d07-46d6-be52-6d1824632628", "versionId": "1ab0aade-4f89-42bc-af7e-15174fd3c5b8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.bc2bdd28-628c-42a1-8f18-62a326b9e796", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.4098f339-f9f2-414a-b616-aa0c01aa06f0", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7be1ec94:2ff5", "versionId": "7ee9f4e6-9f4e-4f66-b798-9555c5bf15d7", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "557", "y": "204", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.4098f339-f9f2-414a-b616-aa0c01aa06f0", "message": "", "faultStyle": "1", "guid": "4eb014f7-e21f-4848-bf57-d8e7840398d4", "versionId": "b7b57378-dd7e-498a-991a-aaae1ce6633d", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.bbcb68e7-1b3d-4961-a72b-9332031a0c85", "processParameterId": "2055.fadbd4a8-eb37-44b9-9536-2302e262f759", "parameterMappingParentId": "3007.4098f339-f9f2-414a-b616-aa0c01aa06f0", "useDefault": "false", "value": {"isNull": "true"}, "classRef": "/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6", "isList": "false", "isInput": "false", "guid": "c154690e-a21a-4f88-b395-4bc9501ae30b", "versionId": "1ff0c83d-a787-430a-a99b-66b1004ccaf5", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fb6629d3-6ae1-457b-8b38-af38db543c30", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "MW_FC Query DC Contract", "tWComponentName": "SubProcess", "tWComponentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.544bc577-**************-1af3744c36b3", "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1be4", "versionId": "99be730b-0731-4b2a-b53f-1a7293e390ee", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.bf74b978-b84a-4f28-8ced-6e03868fd39d", "processItemId": "2025.fb6629d3-6ae1-457b-8b38-af38db543c30", "location": "1", "script": {"isNull": "true"}, "guid": "e220e3b6-1da5-43dc-a7a7-4e6b001470da", "versionId": "c9ec6f15-ab5b-441c-911f-9e3b04f30ba9"}, "layoutData": {"x": "105", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error7", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b7be1ec94:2ff3", "errorHandlerItemId": "2025.544bc577-**************-1af3744c36b3", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.3875c748-15fc-40ef-bef1-ea4d905d7f75", "guid": "33ac653c-aa56-4696-bf9e-8aefdc2a3128", "versionId": "d545cd77-70be-4100-86a3-bfe81f673e82", "parameterMapping": [{"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.cb447438-fb45-4042-b27e-db89711daf62", "processParameterId": "2055.42effea6-8d8b-437c-958a-da5cf9119674", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "080ffbf6-c44a-492c-af73-900366a175cb", "versionId": "052d0cfa-d6a6-45c8-bf64-465104627b5c", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a5567a74-22d5-4a26-8f04-706f61d30d84", "processParameterId": "2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "84bc47bd-b102-4aee-878c-e6a05a12afdd", "versionId": "113f0bce-cf07-48fc-9c8a-ef4a55b38587", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.d2e9795f-5611-4da5-966a-54ee401ad2ba", "processParameterId": "2055.ee7811d9-22b1-4727-9148-bcac74c306af", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.local.errorMSG", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "6424d5e1-dd9c-499d-a953-58d9701a414e", "versionId": "13225021-51ba-4716-9a39-8a5e2f8346db", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3a2470dd-5d8f-41c5-9cf3-a8ea1199f4fd", "processParameterId": "2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "4708d74e-6910-4ad3-a6a0-546374f5e00f", "versionId": "1b32dacd-6582-48d6-a3a1-9924f12dbeb6", "description": {"isNull": "true"}}, {"name": "SCQueryBCContractRes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5c5f975b-1244-4546-af76-5521962eecc2", "processParameterId": "2055.004a9996-2e6b-4d60-821e-5db8e4ba2271", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.local.queryBCContractResults", "classRef": "/12.*************-4cbb-a781-44d233d577c6", "isList": "false", "isInput": "false", "guid": "18879049-8241-4238-ba09-532a903afb83", "versionId": "22663580-18ec-4336-981f-de51bed73cae", "description": {"isNull": "true"}}, {"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3492e501-40ba-47c4-a236-ee1edabad6b8", "processParameterId": "2055.4ff12b5f-6b69-4504-a730-046ee5c2000a", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.system.user_id", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "9b754073-8f62-46c5-99b9-9fc32798e3f5", "versionId": "465cccd4-16c3-4862-bb6b-134f80906b45", "description": {"isNull": "true"}}, {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.08f48c77-db9f-4fc2-b207-f220d1d2c26f", "processParameterId": "2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "\"ODC Creation / Amendment Process Details\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "df1aedb3-7450-4c4b-9372-390a68e4d2ea", "versionId": "4f5ec440-c33c-4360-99da-a4bf34adce20", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.88ae70db-0b0b-40c3-97a9-280ec606f5fc", "processParameterId": "2055.95835270-515f-4794-a207-a5e2aa301c0e", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "c9ad9eee-0db4-4feb-b0d5-b32064dfa6c2", "versionId": "8bd2c8b0-f9fd-41bd-94e5-9888cf1f6d94", "description": {"isNull": "true"}}, {"name": "contractNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e3eb6d59-f474-425c-9661-0a7f51945d75", "processParameterId": "2055.8f786b77-ae66-4547-a444-d6ccb8969c42", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.local.ContractDetails.BasicDetails.flexCubeContractNo", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "0167defd-d8d4-4a77-9f6f-5cc0e1818b5d", "versionId": "903d5d71-4ad9-41db-bd2d-e8d8941a5d31", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.506d7c61-5c88-459b-a025-158a84f9aa63", "processParameterId": "2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.local.errorCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "d6c30cc6-de5f-4d4c-a624-5ac7919ecc12", "versionId": "c509b46b-24df-4a25-a5f0-641c51f6bf98", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.04b11798-f5ad-4139-9619-af4e05c6f858", "processParameterId": "2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9", "parameterMappingParentId": "3012.67253851-197f-41ec-9b6f-ea87fbbc6165", "useDefault": "false", "value": "tw.system.currentProcessInstanceID", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "8496b783-ea6e-4ff7-bbdf-4204d3e8f9a6", "versionId": "e7944257-4e76-4680-bb5c-4f9c9d780b8b", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.304679b7-62fb-4dd1-8d05-2a1f24393037", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "Mapping", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.ae3dea46-0837-44b2-8963-ff87bd3105c9", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1d02", "versionId": "bb1caa7f-0a97-4b39-adf6-b5473a188610", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "421", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.ae3dea46-0837-44b2-8963-ff87bd3105c9", "scriptTypeId": "2", "isActive": "true", "script": "if(!!tw.local.queryBCContractResults)\r\r\n{\r\r\n\r\r\n//\ttw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();\r\r\n//\ttw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;\r\r\n//\ttw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;\r\r\n//\ttw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;\r\r\n//\ttw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);\r\r\n//\ttw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);\r\r\n//\ttw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;\r\r\n//\ttw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;\r\r\n//\ttw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);\r\r\n//\ttw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);\r\r\n//\ttw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;\r\r\n//\ttw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);\r\r\n\ttw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;\r\r\n\ttw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);\r\r\n\t\r\r\n//\ttw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;\r\r\n//      tw.local.ContractDetails.CustomerInfo.customerName = tw.local.queryBCContractResults.TXTCUSTNAME;\r\r\n\t\r\r\ntw.local.ContractDetails.Parties = new tw.object.odcParties();\r\r\n\r\r\n\t\r\r\n\tfor(var i=0;i<tw.local.queryBCContractResults.Contract_Parties.listLength;i++)\r\r\n\t{\r\r\n\t\tif(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"COLLECTING BANK\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\r\n\r\r\n\t\t}\r\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWEE\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\r\n\r\r\n\t\t}\r\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWER\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\r\n\t\t}\r\r\n\t\telse\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\r\n\r\r\n\t\t}\r\r\n\r\r\n\t}\r\r\n//tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\n//\t\r\r\n//\tif(tw.local.queryBCContractResults.Contract_Multitnr != null)\r\r\n//\t{\r\r\n//\t\tfor(i=0;i<tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)\r\r\n//\t\t{\r\r\n//\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();\r\r\n//\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);\r\r\n//\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);\r\r\n//\t\t}\r\r\n//\r\r\n//\t}\r\r\ntw.local.ContractDetails.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\t\r\r\n\tif(tw.local.queryBCContractResults.Charges != null && tw.local.queryBCContractResults.Charges[0] != null)\r\r\n\t{\r\r\n\t\tfor(i=0;i<tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;\r\r\n\t\t\tif(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == \"y\")\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;\r\r\n\t\t\t}\r\r\n\t\t\telse\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;\t\t\t\r\r\n\t\t\t}\r\r\n\t\t\t\t\t\r\r\n\t\t}\r\r\n\t}\r\r\n//\tif (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency==\"\")\r\r\n//\t\ttw.local.queryBCContractResults.Currency= \"USD\";\r\r\n\r\r\n//\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;\r\r\n//\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;\t\t\r\r\n}\r\r\n\r\r\n\t//tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();\r\r\n\t//tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;\r\r\n\t//tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;\r\r\n\r\r\n\t\r\r\n\t", "isRule": "false", "guid": "1ccc2381-46ad-4ac3-8618-b3233816193d", "versionId": "78445592-fcd6-472e-8f15-154a1dcaeff8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "name": "isSuccessful?", "tWComponentName": "Switch", "tWComponentId": "3013.24175661-0df5-4c1c-8624-db89f48a7711", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1cfc", "versionId": "cc19c15f-c82c-4dda-854c-ce5e61249613", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "236", "y": "99", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.24175661-0df5-4c1c-8624-db89f48a7711", "guid": "047df29e-120c-4e38-8943-bc429b0338cd", "versionId": "6bd83ed8-a53e-4a83-b018-987a36f2c535", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.843e0b89-4732-4c61-97d3-2933d9d9bef5", "switchId": "3013.24175661-0df5-4c1c-8624-db89f48a7711", "seq": "1", "endStateId": "guid:a6be0630e884ccca:-427a0b52:18bb428309d:-1f83", "condition": "tw.local.isSuccessful\t  ==\t  false", "guid": "be763256-9106-4938-94ac-4f6ba9d17b18", "versionId": "2a7d74e8-cda2-438f-b6b5-c474b25e45ce"}}}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Query BC Contract", "id": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:dataInput": {"name": "ContractDetails", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.9465f3bb-e80a-444e-8672-899423d668fb", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.ODCRequest();\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new TWDate();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = new tw.object.BasicDetails();\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"599IAVC222440002\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\r\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\r\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\r\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = new tw.object.FCCollections();\r\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new TWDate();\r\nautoObject.FcCollections.ToDate = new TWDate();\r\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\r\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = new tw.object.ODCCollection();\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = new tw.object.ReversalReason();\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = new tw.object.ContractCreation();\r\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new TWDate();\r\nautoObject.ContractCreation.valueDate = new TWDate();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new TWDate();\r\nautoObject.Parties = new tw.object.odcParties();\r\nautoObject.Parties.Drawer = new tw.object.Drawer();\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = new tw.object.Drawee();\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\r\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = new tw.object.StepLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\r\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = new tw.object.listOf.StepLog();\r\nautoObject.complianceComments[0] = new tw.object.StepLog();\r\nautoObject.complianceComments[0].startTime = new TWDate();\r\nautoObject.complianceComments[0].endTime = new TWDate();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = new tw.object.listOf.StepLog();\r\nautoObject.History[0] = new tw.object.StepLog();\r\nautoObject.History[0].startTime = new TWDate();\r\nautoObject.History[0].endTime = new TWDate();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject", "useDefault": "true"}}}, "ns16:dataOutput": {"name": "ContractDetails", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.1447a720-69f1-46ca-8d27-88a12b035edf"}, "ns16:inputSet": {"ns16:dataInputRefs": "2055.9465f3bb-e80a-444e-8672-899423d668fb"}, "ns16:outputSet": {"ns16:dataOutputRefs": "2055.1447a720-69f1-46ca-8d27-88a12b035edf"}}, "ns16:laneSet": {"id": "b37d2936-f644-4e7e-81bf-f1892f66dcb1", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "ddbc1ae6-7303-4703-85fb-5b76369a2ea0", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4", "2d13efc7-fd6a-4ee2-88b6-df11c91426d9", "fb6629d3-6ae1-457b-8b38-af38db543c30", "3a2bfe99-d7d8-4de6-873a-f7957c638f19", "ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "304679b7-62fb-4dd1-8d05-2a1f24393037", "544bc577-**************-1af3744c36b3", "bc2bdd28-628c-42a1-8f18-62a326b9e796"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d"}, "ns16:endEvent": [{"name": "End", "id": "2d13efc7-fd6a-4ee2-88b6-df11c91426d9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:056f27db08707381:36b20ea0:18b7b4cc468:1bdd"}, "ns16:incoming": ["4eb3106f-4d5a-4a4f-80b2-82c6bd88d120", "d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619"]}, {"name": "End Event", "id": "bc2bdd28-628c-42a1-8f18-62a326b9e796", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "557", "y": "204", "width": "24", "height": "24"}}, "ns16:incoming": "f1ca6063-0c89-4796-834d-523c900b68ff", "ns16:errorEventDefinition": {"id": "06b11757-8691-404d-88c5-7931dd3e8286", "eventImplId": "86a88c86-a921-49e5-861b-2b138e6be302", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:sequenceFlow": [{"sourceRef": "a9d3ab15-3cdf-4f47-8ffc-f5f5a1092de4", "targetRef": "fb6629d3-6ae1-457b-8b38-af38db543c30", "name": "To End", "id": "2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fb6629d3-6ae1-457b-8b38-af38db543c30", "targetRef": "ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "name": "To isSuccessful?", "id": "f883374b-953c-4fb6-8cf7-f7a22b978a04", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "targetRef": "304679b7-62fb-4dd1-8d05-2a1f24393037", "name": "To Mapping", "id": "725ef966-f992-4b08-889c-ede6a8e46c53", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "304679b7-62fb-4dd1-8d05-2a1f24393037", "targetRef": "2d13efc7-fd6a-4ee2-88b6-df11c91426d9", "name": "To End", "id": "4eb3106f-4d5a-4a4f-80b2-82c6bd88d120", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "targetRef": "2d13efc7-fd6a-4ee2-88b6-df11c91426d9", "name": "To End", "id": "d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.isSuccessful\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "3a2bfe99-d7d8-4de6-873a-f7957c638f19", "targetRef": "544bc577-**************-1af3744c36b3", "name": "To Catch Errors", "id": "c4d36477-c1e7-439b-8bf0-9a1da68c66d9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "544bc577-**************-1af3744c36b3", "targetRef": "bc2bdd28-628c-42a1-8f18-62a326b9e796", "name": "To End Event", "id": "f1ca6063-0c89-4796-834d-523c900b68ff", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:callActivity": {"calledElement": "1.3875c748-15fc-40ef-bef1-ea4d905d7f75", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "MW_FC Query DC Contract", "id": "fb6629d3-6ae1-457b-8b38-af38db543c30", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "105", "y": "57", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "", "ns4:activityType": "CalledProcess"}, "ns16:incoming": "2027.f92a0cd1-c7f2-41ef-8c28-dca53506413d", "ns16:outgoing": "f883374b-953c-4fb6-8cf7-f7a22b978a04", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.4ff12b5f-6b69-4504-a730-046ee5c2000a", "ns16:assignment": {"ns16:from": {"_": "tw.system.user_id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea", "ns16:assignment": {"ns16:from": {"_": "\"ODC Creation / Amendment Process Details\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8f786b77-ae66-4547-a444-d6ccb8969c42", "ns16:assignment": {"ns16:from": {"_": "tw.local.ContractDetails.BasicDetails.flexCubeContractNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9", "ns16:assignment": {"ns16:from": {"_": "tw.system.currentProcessInstanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.42effea6-8d8b-437c-958a-da5cf9119674", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.95835270-515f-4794-a207-a5e2aa301c0e", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.ee7811d9-22b1-4727-9148-bcac74c306af", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMSG", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.004a9996-2e6b-4d60-821e-5db8e4ba2271", "ns16:assignment": {"ns16:to": {"_": "tw.local.queryBCContractResults", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}}]}, "ns16:boundaryEvent": {"cancelActivity": "true", "attachedToRef": "fb6629d3-6ae1-457b-8b38-af38db543c30", "parallelMultiple": "false", "name": "Error7", "id": "3a2bfe99-d7d8-4de6-873a-f7957c638f19", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "140", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "c4d36477-c1e7-439b-8bf0-9a1da68c66d9", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "e0f92768-0b1d-4714-8693-1a9cae9bed20"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "8598f4cb-ae21-49e1-84ea-153ead3bb56f", "eventImplId": "d0616793-7785-4615-881e-a59f1e877202", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorCode", "id": "2056.ad7cdaa4-504c-4708-8344-a1dc47262608"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMSG", "id": "2056.ed0addfb-42f9-4e4d-8bd2-ae08e8d7b003"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.2cd964e1-a67e-485f-8103-0a50c17067c8"}, {"itemSubjectRef": "itm.12.*************-4cbb-a781-44d233d577c6", "isCollection": "false", "name": "queryBCContractResults", "id": "2056.0ec55024-95a1-4599-8cd5-980f0273de16"}], "ns16:exclusiveGateway": {"default": "725ef966-f992-4b08-889c-ede6a8e46c53", "name": "isSuccessful?", "id": "ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "236", "y": "99", "width": "32", "height": "32"}}, "ns16:incoming": "f883374b-953c-4fb6-8cf7-f7a22b978a04", "ns16:outgoing": ["725ef966-f992-4b08-889c-ede6a8e46c53", "d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619"]}, "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Mapping", "id": "304679b7-62fb-4dd1-8d05-2a1f24393037", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "421", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "725ef966-f992-4b08-889c-ede6a8e46c53", "ns16:outgoing": "4eb3106f-4d5a-4a4f-80b2-82c6bd88d120", "ns16:script": "if(!!tw.local.queryBCContractResults)\r\r\n{\r\r\n\r\r\n//\ttw.local.ContractDetails.ContractCreation.productCode = new tw.object.NameValuePair();\r\r\n//\ttw.local.ContractDetails.ContractCreation.productCode.value = tw.local.queryBCContractResults.ProductCode;\r\r\n//\ttw.local.ContractDetails.ContractCreation.productCode.name = tw.local.queryBCContractResults.ProductDesc;\r\r\n//\ttw.local.ContractDetails.ContractCreation.productDescription = tw.local.queryBCContractResults.ProductDesc;\r\r\n//\ttw.local.ContractDetails.ContractCreation.baseDate = new Date(tw.local.queryBCContractResults.BaseDate);\r\r\n//\ttw.local.ContractDetails.ContractCreation.maturityDate = new Date(tw.local.queryBCContractResults.MaturityDate);\r\r\n//\ttw.local.ContractDetails.ContractCreation.sourceReference = tw.local.queryBCContractResults.ExtRefNum;\r\r\n//\ttw.local.ContractDetails.ContractCreation.Stage = tw.local.queryBCContractResults.Stag;\r\r\n//\ttw.local.ContractDetails.ContractCreation.tenorDays = Number(tw.local.queryBCContractResults.TenorDays);\r\r\n//\ttw.local.ContractDetails.ContractCreation.transitDays = Number(tw.local.queryBCContractResults.TransitDays);\r\r\n//\ttw.local.ContractDetails.ContractCreation.userReference = tw.local.queryBCContractResults.UseRefNum;\r\r\n//\ttw.local.ContractDetails.ContractCreation.valueDate = new Date(tw.local.queryBCContractResults.ValueDate);\r\r\n\ttw.local.ContractDetails.ContractCreation.currency = tw.local.queryBCContractResults.Currency;\r\r\n\ttw.local.ContractDetails.ContractCreation.amount = parseFloat(tw.local.queryBCContractResults.Amount);\r\r\n\t\r\r\n//\ttw.local.ContractDetails.CustomerInfo.cif = tw.local.queryBCContractResults.CustomerId;\r\r\n//      tw.local.ContractDetails.CustomerInfo.customerName = tw.local.queryBCContractResults.TXTCUSTNAME;\r\r\n\t\r\r\ntw.local.ContractDetails.Parties = new tw.object.odcParties();\r\r\n\r\r\n\t\r\r\n\tfor(var i=0;i<tw.local.queryBCContractResults.Contract_Parties.listLength;i++)\r\r\n\t{\r\r\n\t\tif(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"COLLECTING BANK\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank = new tw.object.CollectingBank();\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.cif = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.name = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.id = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.collectingBank.reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\r\n\r\r\n\t\t}\r\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWEE\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee = new tw.object.Drawee();\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawee.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\r\n\r\r\n\t\t}\r\r\n\t\telse if(tw.local.queryBCContractResults.Contract_Parties[i].PartyType == \"DRAWER\")\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer = new tw.object.Drawer();\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.Drawer.Reference = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\r\r\n\t\t}\r\r\n\t\telse\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes = new tw.object.partyTypes();\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyCIF = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.country = tw.local.queryBCContractResults.Contract_Parties[i].PartyCountryCode;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address1 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress1;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address2 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress2;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.address3 = tw.local.queryBCContractResults.Contract_Parties[i].PartyAddress3;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.language = tw.local.queryBCContractResults.Contract_Parties[i].PartyLang;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyName = tw.local.queryBCContractResults.Contract_Parties[i].PartyName;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.partyId = tw.local.queryBCContractResults.Contract_Parties[i].PartyId;\r\r\n\t\t\ttw.local.ContractDetails.Parties.partyTypes.refrence = tw.local.queryBCContractResults.Contract_Parties[i].PartyRefNum;\t\t\t\r\r\n\r\r\n\t\t}\r\r\n\r\r\n\t}\r\r\n//tw.local.ContractDetails.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\r\r\n//\t\r\r\n//\tif(tw.local.queryBCContractResults.Contract_Multitnr != null)\r\r\n//\t{\r\r\n//\t\tfor(i=0;i<tw.local.queryBCContractResults.Contract_Multitnr.listLength;i++)\r\r\n//\t\t{\r\r\n//\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i] = new tw.object.MultiTenorDates();\r\r\n//\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].amount = parseFloat(tw.local.queryBCContractResults.Contract_Multitnr[i].BillAmount);\r\r\n//\t\t\ttw.local.ContractDetails.FinancialDetailsFO.multiTenorDates[i].date = new Date(tw.local.queryBCContractResults.Contract_Multitnr[i].ValueDate);\r\r\n//\t\t}\r\r\n//\r\r\n//\t}\r\r\ntw.local.ContractDetails.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\t\r\r\n\tif(tw.local.queryBCContractResults.Charges != null && tw.local.queryBCContractResults.Charges[0] != null)\r\r\n\t{\r\r\n\t\tfor(i=0;i<tw.local.queryBCContractResults.Charges[0].ChargeApplication.listLength;i++)\r\r\n\t\t{\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i] = new tw.object.ChargesAndCommissions();\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGAMT;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].basicAmountCurrency = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].TGCCY;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].changeAmount = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGAMT;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency = new tw.object.NameValuePair();\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].defaultCurrency.value = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].CHRGCCY;\r\r\n\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].component = tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].COMPNT;\r\r\n\t\t\tif(tw.local.queryBCContractResults.Charges[0].ChargeApplication[i].WAIVER.toLowerCase() == \"y\")\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = true;\r\r\n\t\t\t}\r\r\n\t\t\telse\r\r\n\t\t\t{\r\r\n\t\t\t\ttw.local.ContractDetails.ChargesAndCommissions[i].waiver = false;\t\t\t\r\r\n\t\t\t}\r\r\n\t\t\t\t\t\r\r\n\t\t}\r\r\n\t}\r\r\n//\tif (tw.local.queryBCContractResults.Currency==null || tw.local.queryBCContractResults.Currency==\"\")\r\r\n//\t\ttw.local.queryBCContractResults.Currency= \"USD\";\r\r\n\r\r\n//\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.name=  tw.local.queryBCContractResults.Currency;\r\r\n//\t\ttw.local.ContractDetails.FinancialDetailsBR.currency.value=  tw.local.queryBCContractResults.Currency;\t\t\r\r\n}\r\r\n\r\r\n\t//tw.local.ContractDetails.BasicDetails = new tw.object.BasicDetails();\r\r\n\t//tw.local.ContractDetails.BasicDetails.requestNature = tw.local.ContractDetails.requestNature.name;\r\r\n\t//tw.local.ContractDetails.BasicDetails.requestType = tw.local.ContractDetails.requestType.name;\r\r\n\r\r\n\t\r\r\n\t"}, {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Catch Errors", "id": "544bc577-**************-1af3744c36b3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "423", "y": "181", "width": "95", "height": "70", "color": "#FF7782"}}, "ns16:incoming": "c4d36477-c1e7-439b-8bf0-9a1da68c66d9", "ns16:outgoing": "f1ca6063-0c89-4796-834d-523c900b68ff", "ns16:script": "if (tw.local.error != undefined && !!tw.local.error.errorText) {\r\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\r\n}else if (tw.system.error != undefined && tw.system.error != null) {\r\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}else{\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}\r\r\n\r\r\ntw.local.error.errorText = \"Error Occured<br> Service Name : \"+tw.system.serviceFlow.name+\"<br> Error Message : \"+tw.local.errorMSG;"}]}}}, "link": [{"name": "To End Event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f1ca6063-0c89-4796-834d-523c900b68ff", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.544bc577-**************-1af3744c36b3", "2025.544bc577-**************-1af3744c36b3"], "endStateId": "Out", "toProcessItemId": ["2025.bc2bdd28-628c-42a1-8f18-62a326b9e796", "2025.bc2bdd28-628c-42a1-8f18-62a326b9e796"], "guid": "fb031cc9-561f-4012-b75c-9da0cfb391ac", "versionId": "0d9d73eb-27b9-424b-9217-7a380b964051", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d7b9f95d-b9b1-4d36-8bcb-b00bdeb7a619", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98"], "endStateId": "guid:a6be0630e884ccca:-427a0b52:18bb428309d:-1f83", "toProcessItemId": ["2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9", "2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9"], "guid": "19540ee3-d321-46bd-bd4d-612fe0e8cf51", "versionId": "14605d33-6baa-44f6-943a-52040b6e4e0a", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To Mapping", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.725ef966-f992-4b08-889c-ede6a8e46c53", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.304679b7-62fb-4dd1-8d05-2a1f24393037", "2025.304679b7-62fb-4dd1-8d05-2a1f24393037"], "guid": "191d8572-b38a-4334-a874-a2d6e937efaa", "versionId": "b8fad230-d72a-48d5-b6f9-474251d9ec9e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To isSuccessful?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.f883374b-953c-4fb6-8cf7-f7a22b978a04", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.fb6629d3-6ae1-457b-8b38-af38db543c30", "2025.fb6629d3-6ae1-457b-8b38-af38db543c30"], "endStateId": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754", "toProcessItemId": ["2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98", "2025.ab240dfe-8d20-4a88-8e96-fdd10ff72c98"], "guid": "17f41caa-9812-42fa-8aea-d2ff108016d4", "versionId": "ce9cf400-f109-49ce-b9ae-a68d1f3a3ba2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.4eb3106f-4d5a-4a4f-80b2-82c6bd88d120", "processId": "1.57e7d7a6-ec6d-474d-8eae-eac0f30bb676", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.304679b7-62fb-4dd1-8d05-2a1f24393037", "2025.304679b7-62fb-4dd1-8d05-2a1f24393037"], "endStateId": "Out", "toProcessItemId": ["2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9", "2025.2d13efc7-fd6a-4ee2-88b6-df11c91426d9"], "guid": "c893897b-59e5-4b12-8778-0baf5f8e78cb", "versionId": "f072e2e9-a981-4d9a-8461-dfcd5658a2c1", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}