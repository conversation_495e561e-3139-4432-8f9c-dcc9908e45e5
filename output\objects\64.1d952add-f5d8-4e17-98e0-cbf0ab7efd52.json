{"id": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "versionId": "a72c1fc9-ee22-4196-9904-eb4eb8bfe774", "name": "Charges And Commissions CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (!this.context.options.isChecker.get(\"value\")) {\r\r\n\tthis.ui.get(\"GetChargesBtn\").click();\r\r\n}else{\r\r\n\tthis.ui.get(\"Vertical_Layout7\").setEnabled(false);\r\r\n}", "bindingType": "chargesAndCommission", "configOptions": ["amountCollectableByNBE", "accountList", "accountNo", "index", "commissionSectionVIS", "calculatedChangeAmnt", "chargesCustomerAccountList", "exRate", "<PERSON><PERSON><PERSON><PERSON>"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//On Change Amount and Negotiated Exchange Rate - calculate Debited Amount\r\r\nthis.calculateDebitedAmount = function(index){\r\r\n\tvar chargeAmount = this.context.binding.get(\"value\").get(index).get(\"changeAmount\");\r\r\n\tvar NegotiableRate = this.context.binding.get(\"value\").get(index).get(\"debitedAmount\").get(\"negotiatedExRate\");\r\r\n\tvar debitedAmount = chargeAmount * NegotiableRate;\r\r\n\t\r\r\n\tthis.context.binding.get(\"value\").get(index).get(\"debitedAmount\").set(\"amountInAccount\", debitedAmount);\r\r\n}\r\r\n\r\r\n//On Account Class - Set vis for GL and Customer account\r\r\n//this.accountNumVis = function(value){\r\r\n//\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\") == \"001\") {\r\r\n//\t    //Customer\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setData(\"\");\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t    }\r\r\n//\t\r\r\n//\t} else {\r\r\n//\t    //GL\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setData(null);\r\r\n//\t\r\r\n//\t        //GL account cant be overDrafted or not\r\r\n//\t        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setEnabled(false);\r\r\n//\t    }\r\r\n//\t}\r\r\n//\r\r\n//}\r\r\n\r\r\n//On Rate Type - Set Vis for Flat amount and fixed rate\r\r\nthis.rateTypeVis = function(value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rateType= value.getData();\r\r\n\tif(rateType!=null && rateType == \"Flat Amount\"){\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setData(0);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setData(0);\r\r\n\t}\r\r\n\telse if(rateType!=null && rateType == \"Fixed Rate\"){\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//====================================================================================================\r\r\n//Generic Validate Positive amount (accepts 0) (Change Amount)\r\r\nthis.validatePositive = function(value){\r\r\n\tif (value.getData() < 0) {\r\r\n\t\tvalue.setValid(false,\"Must be >= 0\");\r\r\n//\t\tvalue.setData(0.0);\r\r\n\t}else{\r\r\n\t\tvalue.setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//Generic Validate Digits Only (Account Branch Code)\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Change of Change Percentage - Validate Change percentage before calculate change amount\r\r\nthis.amountValidation = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"rateType\") ==\"Fixed Rate\") {\t\t\r\r\n\t\tif (value.getData() < 0 || value.getData() > 100){\r\r\n\t\t\tvalue.setData(0);\r\r\n\t\t\tvalue.setValid(false, \"Must be >= 0 and < 100\");\r\r\n\t\t}else{\r\r\n\t\t\tvalue.setValid(true);\r\r\n\t\t\tthis.calcChangeAmnt(value);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nvar newIndex;\r\r\n//call from amountValidation function - Calculate change amount service call\r\r\nthis.calcChangeAmnt = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar requestAmnt = this.context.options.amountCollectableByNBE.get(\"value\");\r\r\n\tvar changePercentage = this.context.binding.get(\"value\").get(index).get(\"changePercentage\");\r\r\n\t\r\r\n\tvar concatedDefault = requestAmnt + \",\" + changePercentage;\r\r\n\tthis.ui.get(\"calculateChangeAmount1[\"+index+\"]\").execute(concatedDefault);\r\r\n\tnewIndex = index;\r\r\n}\r\r\n\r\r\n//On result of calculateChangeAmount1 - Map Service call ouput\r\r\nthis.setChangeAmnt = function(){\r\r\n\tvar index = newIndex;\r\r\n\tvar changeAmount = this.context.options.calculatedChangeAmnt.get(\"value\");\r\r\n\tif(!!changeAmount){\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",changeAmount);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",0);\r\r\n\t}\r\r\n} \r\r\n\r\r\n//On change of Account Num - Set Account Info for each customer Account\r\r\nthis.fillAccountData = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tfor (var i = 0; i < this.context.options.chargesCustomerAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t//Set isOverDraft\r\r\n\t\t\tvar commClassCode = this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0, 1);\r\r\n\r\r\n\t\t\tif (code == \"O\" || code == \"D\") {\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\r\r\n\t\t\t//Set Account Info\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"branchCode\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\", {});\r\r\n\t\t\tthis.ui.get(\"accountCurrency[\"+index+\"]\").setData(this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").set(\"vale\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\",this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balance\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t\t\r\r\n\t\t\tthis.context.options.index.set(\"value\", index);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of GL Account Num - Reset Data on GL account change\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"isGLFound\",false);\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"glVerifyMSG\",\"\");\r\r\n}\r\r\n\r\r\n//On click of validate GL Btn - Call A service call to validate GL\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"glAccountNo\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\n//On result of Validate GL - Map the result of GL service call\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"isGLFound\") ) {\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(true);\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"Verified\");\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of Account Cry - Apply vis and Call A service to get exchange rate between Default Cry and Account Cry\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar cryCode = value.getData();\r\r\n//\tvar cryCode = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").get(\"value\");\r\r\n\tif(cryCode != \"\" && cryCode != null && cryCode != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == cryCode) {\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = cryCode;\r\r\n\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Result of get exchange rate\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\n//On Debited Amount in Account Currency and Account Balance- validate is over Draft\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar accountBalance = this.ui.get(\"accountBalance[\"+index+\"]\").getData();\r\r\n\tvar debitedAmount = this.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").getData();\r\r\n\t\r\r\n\t//Need to add &&accountClass is Customer Account-->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\r\n\tif (debitedAmount > accountBalance) {\r\r\n\t\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t\t}else if (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == false){\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"Error: Must be < Account Balance\");\r\r\n\t\t}\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n// Reusable function to set visibility and enabled state\r\r\nthis.setUIVisibilityAndState = function (ui, index, config) {\r\r\n    for (var i = 0; i < config.length; i++) {\r\r\n\t\tvar item = config[i];\r\r\n\t\tui.get(item.name + \"[\" + index + \"]\").setVisible(item.visible, true);\r\r\n\t\tif (item.enabled !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setEnabled(item.enabled);\r\r\n\t\t}\r\r\n\t\tif (item.data !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setData(item.data);\r\r\n\t\t}\r\r\n    }\r\r\n};\r\r\n\r\r\n// Handle Customer Account\r\r\nthis.handleCustomerAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: true },\r\r\n        { name: \"accountBalance\", visible: true },\r\r\n        { name: \"customerAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: false },\r\r\n        { name: \"GLSection1\", visible: false },\r\r\n        { name: \"accountCurrency\", enabled: false }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"glAccountNo\", data: \"\" });\r\r\n    } else {\r\r\n        config.push({ name: \"customerAccountNo\", enabled: false });\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n    this.ui.get(\"accountBranchCode[\" + index + \"]\").setEnabled(false);\r\r\n};\r\r\n\r\r\n// Handle GL Account\r\r\nthis.handleGLAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: false },\r\r\n        { name: \"accountBalance\", visible: false },\r\r\n        { name: \"customerAccountNo\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"GLSection1\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: true, enabled: !isChecker },\r\r\n        { name: \"accountCurrency\", enabled: !isChecker },\r\r\n        { name: \"accountBranchCode\", enabled: !isChecker }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"customerAccountNo\", data: null });\r\r\n        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n};\r\r\n\r\r\n// Main function\r\r\nthis.accountNumVis = function (value) {\r\r\n    var index = value;\r\r\n    var accountClass = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\");\r\r\n    var isChecker = this.context.options.isChecker.get(\"value\");\r\r\n\r\r\n    if (accountClass === \"001\") {\r\r\n        // Customer\r\r\n        this.handleCustomerAccount(index, isChecker);\r\r\n    } else {\r\r\n        // GL\r\r\n        this.handleGLAccount(index, isChecker);\r\r\n    }\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": ".panel-heading {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    justify-content: space-between; /* Distribute space between title and controls */\r\r\n    height: 50px; /* Fixed height for the panel heading */\r\r\n    padding: 0 10px; /* Add horizontal padding */\r\r\n    box-sizing: border-box; /* Include padding and border in height calculations */\r\r\n    overflow: hidden; /* Hide any overflowing content */\r\r\n}\r\r\n\r\r\n.panel-title {\r\r\n    flex: 1; /* Allow title to grow and fill available space */\r\r\n    white-space: nowrap; /* Prevent text from wrapping */\r\r\n    overflow: hidden; /* Hide overflow text */\r\r\n    text-overflow: ellipsis; /* Add ellipsis if text overflows */\r\r\n    font-size: 14px; /* Adjust font size to make the title smaller */\r\r\n    line-height: 1; /* Adjust line height for better text fitting */\r\r\n    margin: 0; /* Ensure no extra margin affecting layout */\r\r\n}\r\r\n\r\r\n.panel-heading-controls {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    margin-left: auto; /* Push controls to the right */\r\r\n}"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "name": "Charges And Commissions CV 2", "lastModified": "1722491012436", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isTemplate": "false", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>6c9965da-68d8-488f-858b-c7632f474de4</ns2:id><ns2:layoutItemId>Panel1</ns2:layoutItemId><ns2:configData><ns2:id>b071d055-c471-42b6-827c-fb2ce2179f88</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Applied Commissions / Charges</ns2:value></ns2:configData><ns2:configData><ns2:id>fa5c1bf4-4d73-48aa-8d63-32f4549d6fa9</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>bc66be4b-d03d-4471-85af-34533c1dcb53</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>9dac7457-f188-4623-80df-8939eab165d7</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>5967affe-43cd-4c81-81ef-c756a5cbcaaf</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.455e44ab-b77b-4337-b3f9-435e234fb569</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>4f941797-0aa6-4676-838a-91edf0c21093</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>53ace8eb-89ac-49fe-8763-b4ba1f30c223</ns2:id><ns2:layoutItemId>Vertical_Layout7</ns2:layoutItemId><ns2:configData><ns2:id>5adfcf85-6f4f-4a55-80f7-b48a65485ef5</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 7</ns2:value></ns2:configData><ns2:configData><ns2:id>a61f4167-d404-4c2e-8fb3-a25b49235585</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>8e485f31-3664-47e2-8522-2d5a1179ce64</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>f0bfc96a-81d7-4b08-8018-************</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>tw.options.commissionSectionVIS</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission[]</ns2:binding><ns2:contentBoxContrib><ns2:id>b2c372ff-03de-48ef-8f6d-8d1c3f946806</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>43424280-21a0-4750-8fa8-5dfa3d965b07</ns2:id><ns2:layoutItemId>chargesAndCommission</ns2:layoutItemId><ns2:configData><ns2:id>b6e09029-7f19-4be8-824c-de574463bfb2</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>tw.businessData.chargesAndCommission.currentItem.description</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>a963b81e-0c49-49c2-82bc-3ccf7956136f</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>9eb3bc4a-d941-487d-8845-8e5fd9d26cd1</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>18e0b511-d5b1-4517-88b1-2a0bc8c26dab</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>058cc9d3-da64-49ae-8fbc-84809634b3a7</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:configData><ns2:id>904a6f4a-4d13-4363-8896-03ab5f668c35</ns2:id><ns2:optionName>initiallyCollapsed</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>cf621baa-9259-4f44-88e0-f44b22446791</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>b8b98a80-0f41-441c-8687-abc2e6f0017e</ns2:id><ns2:layoutItemId>Horizontal_Layout3</ns2:layoutItemId><ns2:configData><ns2:id>59dae1d3-6b03-49a8-8302-ea835bf9b287</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 3</ns2:value></ns2:configData><ns2:configData><ns2:id>cb83e7c6-9a72-4414-8e79-7e5f0ca9ffd1</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>30ef7c84-f047-4f98-850d-6f0bf136c088</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>9a2ea8ae-ba4d-4223-88bf-c9f1c160ad47</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>41634280-0a30-45bd-8243-7700b0b7a22c</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>edccb848-393d-41bb-8789-27e681374f3f</ns2:id><ns2:layoutItemId>Vertical_Layout1</ns2:layoutItemId><ns2:configData><ns2:id>7b86e9ba-f94b-4207-856f-2a2fb3c835bf</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout</ns2:value></ns2:configData><ns2:configData><ns2:id>42b65ac8-79b9-4711-8973-e644396116b3</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>e37c1f86-26ca-4ff9-84c6-0ea295d1c04f</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>c1db5512-3181-4e0a-8be9-94eae8d7593f</ns2:id><ns2:optionName>@width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>d6ec2c21-b219-4af5-8260-cb0de5946b6b</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>69b482a2-474d-4849-83d7-aa9a1cef8670</ns2:id><ns2:layoutItemId>component</ns2:layoutItemId><ns2:configData><ns2:id>708f669f-5edd-45e7-9a8e-b68ed78014db</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>6570be34-5eea-4c4e-8051-2197858ca656</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Component</ns2:value></ns2:configData><ns2:configData><ns2:id>33e5f88c-354c-4b06-8231-5629a608a902</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>914249d9-36c7-41ea-8e52-ce338c586009</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.component</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>851986d6-c42d-4980-89db-50112d02cd41</ns2:id><ns2:layoutItemId>rateType</ns2:layoutItemId><ns2:configData><ns2:id>3668268f-f8e9-4aa6-b99f-fd89a2e94565</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>35ab16a2-2806-4061-8f87-a584cc182477</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Rate Type</ns2:value></ns2:configData><ns2:configData><ns2:id>58848896-15f7-4302-8925-326817755cf7</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>e2ac37f4-088e-4e05-8ca7-27cd34740660</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>998020a3-5f1f-45e1-8041-88edbd67f2a4</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.rateTypeVis(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>75a85937-4132-46a0-814a-256b8fee629f</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>view.rateTypeVis(me);</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.rateType</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>c30b436e-f3c0-423e-83dd-010612b002a5</ns2:id><ns2:layoutItemId>defaultPercentage</ns2:layoutItemId><ns2:configData><ns2:id>af7bed4d-1994-4621-8211-bf243be836b7</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>f5fdeea9-3ca8-4bb7-8693-8d9ffb5baf82</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Default Percentage</ns2:value></ns2:configData><ns2:configData><ns2:id>d9902048-4d22-44df-8ec4-e3d42aa02c32</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>b99feeda-8178-40f7-8101-1c93e028f220</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>823f4f1e-c042-428e-88c5-e3759c248b49</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.defaultPercentage</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>bc3ddc97-9448-4b1d-8c70-87c88ab9d7a5</ns2:id><ns2:layoutItemId>defaultAmount</ns2:layoutItemId><ns2:configData><ns2:id>e61f6c6e-8fc8-4218-a9f1-cc8dd99a9d40</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>4bf2b01a-72f8-4767-894f-99602ad95da2</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Default Amount</ns2:value></ns2:configData><ns2:configData><ns2:id>e166b285-7ae2-4562-8e96-024a39bc6dfb</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>32eeef16-1e5e-49bf-804e-43bdb2281025</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>beca0346-f852-47f5-81bc-f1310e01ccda</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>var rateType= ${rateType}.getData();\r\n\r\nif(rateType== \"Flat Amount\")\r\n\t${changeAmount}.setData(me.getData());//if flat amount</ns2:value></ns2:configData><ns2:configData><ns2:id>e23de8ba-8949-4fba-83ed-03d1f7bf1bfa</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.defaultAmount</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>81b6faee-deed-40c1-8972-b94c961ca363</ns2:id><ns2:layoutItemId>waiver</ns2:layoutItemId><ns2:configData><ns2:id>cc7c3346-c328-4532-8c36-7426bb94cac9</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Waiver</ns2:value></ns2:configData><ns2:configData><ns2:id>45b1e1c4-7b37-430a-8a90-24c824ca0c0a</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>47461993-5ce4-4782-8913-c13af5b61915</ns2:id><ns2:optionName>@height</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>97fd9058-6a44-4a3b-85da-7f901ef8d282</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"20px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>b5d9564b-01af-4a23-8c4f-19109bd63f4c</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.fffd1628-baee-44e8-b7ca-5ae48644b0be</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.waiver</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>4cf3c942-4f7d-444d-829f-3d827b1c73ed</ns2:id><ns2:layoutItemId>Vertical_Layout2</ns2:layoutItemId><ns2:configData><ns2:id>1075cd53-6049-4216-86e8-970da2ce8868</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 2</ns2:value></ns2:configData><ns2:configData><ns2:id>8e2f85b4-336f-4e3f-80c6-cdb339ec62c2</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>0b605a04-2a91-4630-881a-0ff31e03d9d9</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>e41c83b2-2623-4969-8cc1-e16972363b58</ns2:id><ns2:optionName>@width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>d6509911-b748-49b4-81c3-f8c16421dba5</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>2fac58b0-a6fc-4cd2-8c24-edea9572089f</ns2:id><ns2:layoutItemId>description</ns2:layoutItemId><ns2:configData><ns2:id>f722544e-ef78-40e2-8509-aed11afe2ab0</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>068cd7df-087b-4137-809c-d05c8e7aad4d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Description</ns2:value></ns2:configData><ns2:configData><ns2:id>be334621-03fb-4667-8b50-038da29119b4</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>5e31d6bb-5b9d-48b6-8ee2-ce56a334dbac</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.description</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>0214920f-d419-48ad-8585-6047a96a6311</ns2:id><ns2:layoutItemId>defaultCurrency</ns2:layoutItemId><ns2:configData><ns2:id>e04fed56-2de3-4051-969d-cdff11b2253f</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>e172a97b-9333-43b6-8dfe-6a7f9393209a</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Default Currency</ns2:value></ns2:configData><ns2:configData><ns2:id>1292e399-bbcb-4e75-8a23-2b2439ddb58c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>b1444cb9-e555-4a51-8b61-831e697290ae</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>9019a022-791d-4b88-8e00-68cef3142208</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>3509804f-ea8f-4bc8-8227-dcfb114036b3</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</ns2:value></ns2:configData><ns2:configData><ns2:id>3cb5c856-ac04-4589-8a97-4fd49f8f3492</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.resource.odcLookupsTable.Currency</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.defaultCurrency.value</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>991985f5-046a-4078-8f78-b2a4cc66920e</ns2:id><ns2:layoutItemId>changePercentage</ns2:layoutItemId><ns2:configData><ns2:id>5eace80d-3423-41f0-afc0-0e47f0f39eba</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>0ad68a13-0c9a-4594-8c93-6b75a7b6602f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Change Percentage</ns2:value></ns2:configData><ns2:configData><ns2:id>c5ecc3bd-198d-4af7-8c72-40202a1e31ff</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>92815e0e-75a8-4392-8dd1-b83554d0f5b9</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>9ce36d90-6755-4eeb-8b83-b1604cb756c0</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.amountValidation(me);</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.changePercentage</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>4ff91b91-b1c3-478f-890d-fa54c32d03ee</ns2:id><ns2:layoutItemId>changeAmount</ns2:layoutItemId><ns2:configData><ns2:id>0a556781-90cb-4a05-990d-d3925b480d5d</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>7991b8e7-b0fc-4614-8cdd-28cb13cc4e1f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Change Amount</ns2:value></ns2:configData><ns2:configData><ns2:id>c4f0faf7-98f8-4c9f-8916-592a3fa868dc</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>82532b04-7efe-462d-8090-dd38802a773c</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>caf6b808-dcc8-442d-8007-1aec8195307a</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.validatePositive(me);\r\nview.calculateDebitedAmount(me.ui.getIndex());\r\n\r\nvar value = me.getData();\r\nme.setData(Number(value.toFixed(2)));\r\n\r\n\r\nif(${rateType}.getData()==\"\" &amp;&amp; me.getData()&lt;0)\r\n\treturn 0;\r\n\t</ns2:value></ns2:configData><ns2:configData><ns2:id>351863d8-c1d8-48a1-865a-4f256a17bf9b</ns2:id><ns2:optionName>numericFormatting</ns2:optionName><ns2:value>auto</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.changeAmount</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>85bfb105-8126-444c-853c-5503a39ebf4f</ns2:id><ns2:layoutItemId>calculateChangeAmount1</ns2:layoutItemId><ns2:configData><ns2:id>f6b9eda0-d6f2-4d07-8e69-333c282b7c81</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Calculate Change Amount</ns2:value></ns2:configData><ns2:configData><ns2:id>797bfbde-f31e-4344-8540-913e4dad3757</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>fd33d54b-fb0d-4297-81e6-fc4f449df53c</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>30233caa-0f70-44f7-842b-2af69f08fba8</ns2:id><ns2:optionName>attachedService</ns2:optionName><ns2:value>1.40a7d34d-1192-436a-ba5a-0360265d3261</ns2:value></ns2:configData><ns2:configData><ns2:id>9691a49b-c735-419d-8915-88918d097ea6</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>d287888d-3ba1-42c8-85f4-8739ecd2a145</ns2:id><ns2:optionName>eventON_SVCRESULT</ns2:optionName><ns2:value>\r\nview.setChangeAmnt();</ns2:value></ns2:configData><ns2:configData><ns2:id>4dba5704-bd1d-4d87-8725-4e0f9bb3d7a3</ns2:id><ns2:optionName>eventON_SVCINVOKE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:viewUUID>64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9</ns2:viewUUID><ns2:binding>tw.options.calculatedChangeAmnt</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d5e73efa-fdad-4eaa-8318-35f6a31db51b</ns2:id><ns2:layoutItemId>debitAccounts</ns2:layoutItemId><ns2:configData><ns2:id>f521f70a-8164-4dae-8c17-81d09e12ff06</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Debited Accounts</ns2:value></ns2:configData><ns2:configData><ns2:id>281be054-36bc-4d01-8e46-efb590687972</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>1ad9cc69-a0d1-4054-8fc1-80cb1de4e6c5</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>666c42ce-0f33-4da5-8b1a-0a0499075dfa</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>eabf4899-e52c-4001-8ce4-d3a66059fab3</ns2:id><ns2:optionName>initiallyCollapsed</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>e001b155-b0db-4394-82ba-36a487e3f66b</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>b9048adb-db04-4fe3-811f-e986816efff6</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>c717ff79-99d8-4095-831d-9cdbb2543231</ns2:id><ns2:layoutItemId>Horizontal_Layout4</ns2:layoutItemId><ns2:configData><ns2:id>7c0a47fa-fd6d-4666-819a-0ec1a0e89578</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 4</ns2:value></ns2:configData><ns2:configData><ns2:id>b032bfe2-5c4c-4499-8d6c-27a215fe52e9</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>a1278113-1d20-4753-8937-f54a268eb845</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>a6bf04de-b947-4530-8294-a65a47c3b998</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>5b07cbc8-289e-4cd1-8131-1a2573b41274</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>3770c4d7-538f-4e3f-809c-b0c8d23fc502</ns2:id><ns2:layoutItemId>Vertical_Layout3</ns2:layoutItemId><ns2:configData><ns2:id>3d2995df-bace-48ab-8a82-095a5927d792</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 3</ns2:value></ns2:configData><ns2:configData><ns2:id>c60a576b-8f2a-4c20-821d-87b9e4047dde</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>feb04f51-bc65-434d-8445-f7cd1b6cbe06</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>138bcfb1-3b05-42c2-8aee-28c469196a31</ns2:id><ns2:optionName>@width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"49%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>b76e641c-1ff6-4a90-8409-73795a2548a3</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>93ad861f-e8d2-485f-8f70-b1482cb1ee36</ns2:id><ns2:layoutItemId>Single_Select1</ns2:layoutItemId><ns2:configData><ns2:id>cff53078-e2b3-4403-83ce-855dfffdcd3d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Class (Customer / GL)</ns2:value></ns2:configData><ns2:configData><ns2:id>e60a4534-8e9d-412e-81c3-2672d238261a</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>655d95cd-1914-4f5c-885b-28c81bf27435</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>a2c11eec-7a18-437e-8905-82a3fb468c10</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>4d1c0fa5-31c2-472a-86aa-5d74d374f848</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</ns2:value></ns2:configData><ns2:configData><ns2:id>7e656a51-784a-4dbd-80e3-e7cdb4e1dc58</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.resource.odcLookupsTable.AccountClass</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>3019dacf-c9ed-455b-89d2-0760fae216f5</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.accountNumVis(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>23b53ebd-9b7a-4de6-88b5-af40680787d7</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>8f5b71ff-4d30-4754-891e-cfd66ed91e15</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>a4950278-9cf4-4d44-88f4-44817a1d3b3c</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value>view.accountNumVis(me);</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAccount.accountClass</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>77309e1e-440a-4f08-8df0-eb4b998a6d00</ns2:id><ns2:layoutItemId>customerAccountNo</ns2:layoutItemId><ns2:configData><ns2:id>6f1239f3-81f3-49bc-aaf9-e45305a768a9</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>e554f9c2-09c4-4aea-8110-5f5b1d4242ab</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Number</ns2:value></ns2:configData><ns2:configData><ns2:id>f9d8254b-eb92-401e-8617-f9e8ba55a36f</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>72d2e74e-**************-4acd42b6d0ad</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>5958adf3-96e5-4fa6-8c57-37c739764307</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>B</ns2:value></ns2:configData><ns2:configData><ns2:id>10c35bfb-8e98-4bfa-854f-cef6ab5a4f81</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value></ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>98a6d341-f513-4f0f-8456-dcf39c38b8fa</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.fillAccountData(me);\r\n//${GetRateButton1}.click();\r\n//console.log(me.ui.getIndex());</ns2:value></ns2:configData><ns2:configData><ns2:id>9e9b5c8e-4a22-4aa5-8578-ea636f467180</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>0d2b68da-5353-4378-83ef-093b146c5b09</ns2:id><ns2:optionName>itemList</ns2:optionName><ns2:value>tw.options.chargesCustomerAccountList[]</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>17981b80-63d3-44c0-8f03-e976b193943a</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"accountNO\",\"optionDisplayProperty\":\"accountNO\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>6378b02d-9b58-4e3e-843c-12986907008c</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"accountNO\",\"optionDisplayProperty\":\"accountNO\"}</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAccount.customerAccountNo</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>63a9c4d0-c48e-4b5b-8f46-b1d086ea381a</ns2:id><ns2:layoutItemId>GLSection1</ns2:layoutItemId><ns2:configData><ns2:id>30688ff3-5bcc-41da-81d5-d314e233aeb0</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>GL Section</ns2:value></ns2:configData><ns2:configData><ns2:id>e6085bcb-9e68-4d34-8a1a-9ca4c273bcec</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>193931e2-50c0-4f17-8a78-58a764d65e5c</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>HIDE</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>c39f0152-d65e-4a8d-8ca5-5a1294c1b6bc</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>4d233470-9f5b-4d1b-8f89-657b8ca986f8</ns2:id><ns2:layoutItemId>glAccountNo</ns2:layoutItemId><ns2:configData><ns2:id>8eefea63-f7af-4be6-87c3-e7d0ed5aafad</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>6af44d13-5bfc-4f7b-8b66-c51415ca89ea</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>GL Account Number</ns2:value></ns2:configData><ns2:configData><ns2:id>42ed4dd8-b64d-4fc2-8f79-ee77b71fd19c</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>a7b49e1a-e0a3-4185-8431-cae6f06daa49</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"DEFAULT\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>debdbdde-0937-4e97-88bc-775eec0e393b</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value>if(potential.length &gt; 9){\r\n\tme.setValid(false , \"must be 9 characters\");\r\n//\tme.setData(\"\");\r\n\treturn false;\r\n}else{\r\n\tme.setValid(true);\r\n\treturn true;\r\n}</ns2:value></ns2:configData><ns2:configData><ns2:id>c593600d-63c2-43cf-8a95-be796ce5632b</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.resetGLButton(me);</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAccount.glAccountNo</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>eb896b10-1afd-4025-80d0-2da37a65e480</ns2:id><ns2:layoutItemId>verifyGLButton</ns2:layoutItemId><ns2:configData><ns2:id>ce062b29-0f90-4177-8ab3-f8b606229726</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Verify GL</ns2:value></ns2:configData><ns2:configData><ns2:id>101d9319-6ed1-42f1-8e27-1a2145d0c070</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>03e74710-5b53-4e3a-84cc-ffd87c81a926</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>1b1dc3a6-d490-4d66-84aa-55167e6f8ddc</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"20px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>0d92386e-1aff-48d2-8e57-6272674daea7</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"20%\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>33970af5-fa6c-4f5d-8f23-198d07dd2be5</ns2:id><ns2:optionName>eventON_CLICK</ns2:optionName><ns2:value>view.executeGL(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>c922a983-a585-42d6-8c55-0ae165ca13b8</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>34b6549a-c55d-4728-8173-4581911aaaca</ns2:id><ns2:optionName>shapeStyle</ns2:optionName><ns2:value>R</ns2:value></ns2:configData><ns2:configData><ns2:id>9eec007d-6354-4ad9-8e5c-************</ns2:id><ns2:optionName>expression</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>8ec0f5af-1122-4df1-8f89-70e0f6321c11</ns2:id><ns2:optionName>eventON_LOAD</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>79b7fec0-0869-490f-810a-ca615f35942c</ns2:id><ns2:optionName>preventMultipleClicks</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID><ns2:binding></ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d4572d84-1de0-458e-8874-94234ffbe514</ns2:id><ns2:layoutItemId>verifiedText</ns2:layoutItemId><ns2:configData><ns2:id>883b703f-c459-4e07-89ef-6c1e6724b81c</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Verified text</ns2:value></ns2:configData><ns2:configData><ns2:id>0fe02ae2-65eb-4a1a-8948-6686520f583d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>296acaa4-d94d-4544-8143-c7c275e5757b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>HIDE</ns2:value></ns2:configData><ns2:configData><ns2:id>50c77068-9da7-4f71-86dd-b7b7f53f02fc</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>a9929494-4d73-4023-82c4-dfccab80cfc8</ns2:id><ns2:optionName>weightStyle</ns2:optionName><ns2:value>M</ns2:value></ns2:configData><ns2:configData><ns2:id>fd4ab0a8-ec3f-4f75-8bd0-ead54f5cc7b1</ns2:id><ns2:optionName>textAlignment</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"C\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>d3b417ce-8afe-4a2c-8739-93bca45a7f53</ns2:id><ns2:optionName>labelPlacement</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"T\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>e728ed88-2e83-48db-8889-26a2766c2b80</ns2:id><ns2:optionName>textWrap</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"N\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>3f54d5a3-d3ab-4d35-81c1-545aa991ed77</ns2:id><ns2:optionName>sizeStyle</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"L\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>108ffcf1-38ce-4d03-8488-e0992f1b22e6</ns2:id><ns2:optionName>labelWeightNormal</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":false}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>192e08a5-dedb-49d1-866d-e46320f27e10</ns2:id><ns2:optionName>@margin</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"0px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>ad2032f7-04ca-4112-8cfe-b58a8999d9c3</ns2:id><ns2:optionName>@padding</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"30px 0px 0px 0px\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>e6f31807-1f2e-48e0-82f3-d2d6707439fb</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"20%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.glVerifyMSG</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>60d7e19c-c102-42d6-83ad-b4445eaf6838</ns2:id><ns2:layoutItemId>accountCurrency</ns2:layoutItemId><ns2:configData><ns2:id>8b4deaa2-d77f-47dd-b33d-7bf83460cc70</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>6f591d64-9ada-4156-8200-1eeff9cac9c2</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Currency</ns2:value></ns2:configData><ns2:configData><ns2:id>5ee42c4a-3147-4d2c-8750-66512fe5deb3</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>32ab848f-fc05-40d1-8937-d20fd6d937c7</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>7f58b512-3c15-436c-8c61-39fecceb1299</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:configData><ns2:id>a205fb46-b8f9-4abd-87b6-bcda6230235a</ns2:id><ns2:optionName>itemService</ns2:optionName><ns2:value>1.2f93c4b5-368e-4a13-aff6-b12926260bb3</ns2:value></ns2:configData><ns2:configData><ns2:id>8fdfca90-693b-4b4e-8684-4d80e6176b53</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value>tw.resource.odcLookupsTable.Currency</ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>194752bc-e935-4547-8ed5-a39da7d826e0</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.setExchangeRate(me);\r\n//view.NeoExRateVis(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>125d1598-0467-46d1-87a7-53636c3bd59f</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:configData><ns2:id>59f24924-453d-46e1-8dcb-7922f7021a7f</ns2:id><ns2:optionName>businessDataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"name\",\"optionDisplayProperty\":\"value\"}</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAccount.currency.value</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>2c0e31db-8da9-4f42-8d54-c059db3f59b7</ns2:id><ns2:layoutItemId>Vertical_Layout4</ns2:layoutItemId><ns2:configData><ns2:id>75ec1a47-ea19-4889-8f8f-1f001a2c61df</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 4</ns2:value></ns2:configData><ns2:configData><ns2:id>40fd3c8a-2141-40f7-8d57-b019d8e7a329</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>1fb3c354-b58e-40e5-81dc-6fb76454a340</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>7cbfa79f-ab1c-4858-84b0-2c0633184a99</ns2:id><ns2:optionName>@width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"49%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>8cad7bbb-a275-4902-8a1f-8009d5b72abb</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d87bab08-5ab3-4e71-8a65-e991214f7e63</ns2:id><ns2:layoutItemId>accountBranchCode</ns2:layoutItemId><ns2:configData><ns2:id>ebc729b4-cfb1-4dff-b949-fd349b13fa02</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>67ee0a19-c5ae-4306-8bcf-ad939804526c</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Branch Code</ns2:value></ns2:configData><ns2:configData><ns2:id>6edf9d3a-4e11-4f71-8af9-c054155418a3</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>27fed48d-9fb3-4ec0-83d7-5677e09747f9</ns2:id><ns2:optionName>eventON_INPUT</ns2:optionName><ns2:value>if(potential.length &gt; 3)\r\n{\r\n\tme.setValid(false , \"max lenght 3 Digits\");\r\n\treturn false;\r\n}\r\nelse\r\n{\r\n\tme.setValid(true);\r\n\treturn true;\r\n}</ns2:value></ns2:configData><ns2:configData><ns2:id>4fb0e785-fc20-463d-86f0-e98a25e9f35e</ns2:id><ns2:optionName>regExp</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>4fa442b9-9efc-4f52-89de-19518ef0a4d3</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.validateDigits(me);</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAccount.branchCode</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>3aaa6633-731a-4ae2-8d61-e4c99f898d83</ns2:id><ns2:layoutItemId>balanceSign</ns2:layoutItemId><ns2:configData><ns2:id>dd9b8e04-cbb0-4e9b-beef-06aca009be21</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>70e0a784-2d6d-4984-843d-************</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Balance Sign</ns2:value></ns2:configData><ns2:configData><ns2:id>32ea5d7d-b1d7-4d96-83f6-c961e7b440cb</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>********-d275-4a51-8ac4-6cc182188775</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAccount.balanceSign</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>416be139-913e-4611-8e2f-5adc81ae6d5d</ns2:id><ns2:layoutItemId>accountBalance</ns2:layoutItemId><ns2:configData><ns2:id>798f77fe-7fc8-40bb-a7c2-fb1eeefe9e0d</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>12fc8e5a-687c-41f6-8943-120757036e8c</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Account Balance</ns2:value></ns2:configData><ns2:configData><ns2:id>5e5bbce0-0bfb-4605-8bac-ac9c38848651</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>f1a3b98f-ec97-4d38-89a7-43a9f4fd63a3</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>03306e63-fe0b-4051-88cb-0b8b5ee3b930</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.validateOverDraft(me);</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAccount.balance</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>7587bc3c-c2cf-4c1b-8bb7-94b953aa46f5</ns2:id><ns2:layoutItemId>CheckexistingGLAccount1</ns2:layoutItemId><ns2:configData><ns2:id>df5826ce-432a-4d9f-8dde-3383231b867b</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Check existing GL Account</ns2:value></ns2:configData><ns2:configData><ns2:id>f856ff0d-da98-4203-81f7-c4a6afcef609</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>802628bc-273e-4411-8629-89e235744488</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>f8118136-1b76-43ac-8e85-63dbe60265e7</ns2:id><ns2:optionName>attachedService</ns2:optionName><ns2:value>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</ns2:value></ns2:configData><ns2:configData><ns2:id>9903f0e3-8859-41ee-846c-98ea111577fa</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value></ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>0434e8e2-a7a4-4f65-8dbf-11bcc3615e32</ns2:id><ns2:optionName>eventON_SVCRESULT</ns2:optionName><ns2:value>view.validGlAccount(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>0bac456a-a9ba-40c2-86f8-ceeb0fcbbd85</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value>console.dir(error);\r\nview.AjaxErrorHandling(error.errorText);</ns2:value></ns2:configData><ns2:configData><ns2:id>819cb011-6aa5-45c9-8921-b3e4f24adf03</ns2:id><ns2:optionName>busyIndicator</ns2:optionName><ns2:value>S</ns2:value></ns2:configData><ns2:viewUUID>64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.isGLFound</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>a174fcd1-66fc-4d95-862c-10a3354c238b</ns2:id><ns2:layoutItemId>GetRateButton1</ns2:layoutItemId><ns2:configData><ns2:id>1605876a-6739-4688-8ea9-44ffd350af75</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Button</ns2:value></ns2:configData><ns2:configData><ns2:id>f5c3e450-03bd-484a-8853-145c2c9ee5ee</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>0e8ef7f4-e40c-418c-84a0-f9bc0fdb8dbe</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>20858185-986a-4da6-86c3-255ccc1b6685</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"HIDDEN\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>8e0b27b3-9f18-484e-8858-18f254e09147</ns2:id><ns2:layoutItemId>debitAmounts</ns2:layoutItemId><ns2:configData><ns2:id>b279ff1d-8028-4ecd-8c13-a81ca02c0e38</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Debited Amounts</ns2:value></ns2:configData><ns2:configData><ns2:id>034e5b3e-43e9-47c1-891f-0333ea1a7ae1</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>931fb940-ae60-43ae-8b9e-532d3da0e670</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>9e3b0c19-f227-4c6d-850e-c132abc8b759</ns2:id><ns2:optionName>colorStyle</ns2:optionName><ns2:value>P</ns2:value></ns2:configData><ns2:configData><ns2:id>0a663d82-8beb-4e82-8e05-9fdf1fdb1df4</ns2:id><ns2:optionName>initiallyCollapsed</ns2:optionName><ns2:value>false</ns2:value></ns2:configData><ns2:configData><ns2:id>d2615f8b-6e87-415e-8204-b8ddd5cc50d2</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a</ns2:viewUUID><ns2:binding></ns2:binding><ns2:contentBoxContrib><ns2:id>c6d159aa-9f4b-47a6-8357-d5a7e7887e67</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>23c1e08f-52a5-4bd2-895d-6dd013bca3b0</ns2:id><ns2:layoutItemId>Horizontal_Layout5</ns2:layoutItemId><ns2:configData><ns2:id>804ac415-8214-4623-8c5f-6afcc437c674</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout 5</ns2:value></ns2:configData><ns2:configData><ns2:id>0263d774-d9ee-403f-8c44-0fafd302420d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>68ae756f-6170-4fb5-859c-46d850cadea8</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>8d522587-8573-4795-8ee2-17ad601cc877</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value></ns2:value><ns2:valueType>static</ns2:valueType></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>cc0dbc26-8271-42bb-8302-c63a73bd2745</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>d4d611a5-4f54-4f6d-8a55-5983d3d57d8d</ns2:id><ns2:layoutItemId>Vertical_Layout5</ns2:layoutItemId><ns2:configData><ns2:id>c23e5a97-62d9-48e6-8274-64c59b380077</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 5</ns2:value></ns2:configData><ns2:configData><ns2:id>6d29de68-2db6-47b7-8209-9a170fb3240d</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>da74cecf-e9e9-4078-8c95-8bfe13f2a66d</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>cddd604e-4463-4ee1-869b-8a3e81daa4a6</ns2:id><ns2:optionName>@width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"49%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>8ccf5356-290f-46c0-8c46-5db339c226d9</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>1a38e289-8b96-4a2a-8a71-e517c3d6dea4</ns2:id><ns2:layoutItemId>standardExchangeRate</ns2:layoutItemId><ns2:configData><ns2:id>3ad33ce6-392a-4fca-8211-d4e90bbbb560</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>6</ns2:value></ns2:configData><ns2:configData><ns2:id>9d44a489-969a-4b90-8798-687829da2b11</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Standard Exchange Rate</ns2:value></ns2:configData><ns2:configData><ns2:id>acf10365-34cf-4f63-87a0-885f782d23f6</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>1d6af485-3ace-4165-810e-36ad77c9f478</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>7f13708e-2bd2-46b0-847a-bb01ab993aa8</ns2:id><ns2:optionName>numericFormatting</ns2:optionName><ns2:value>auto</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAmount.standardExRate</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>a58ec1a1-ee25-4771-8f12-67c9cc48651a</ns2:id><ns2:layoutItemId>negotiatedExchangeRate</ns2:layoutItemId><ns2:configData><ns2:id>604b841e-3b66-4381-b5dc-f55d65be9ac0</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>6</ns2:value></ns2:configData><ns2:configData><ns2:id>d6268d6a-b08c-4355-8dd5-beb63ecc2967</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Negotiated Exchange Rate</ns2:value></ns2:configData><ns2:configData><ns2:id>021e5602-7340-4a9f-8d6f-d1186a8df3c9</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>7279c42e-f13c-4670-81b7-df45ed06de7b</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.calculateDebitedAmount(me.ui.getIndex());\r\n\r\nvar value = me.getData();\r\nme.setData(Number(value.toFixed(6)));</ns2:value></ns2:configData><ns2:configData><ns2:id>512db637-a7ac-4741-8143-4ccf7fbb2a92</ns2:id><ns2:optionName>numericFormatting</ns2:optionName><ns2:value>auto</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAmount.negotiatedExRate</ns2:binding></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>31a9355b-e623-4fbd-8c69-b137ae88be09</ns2:id><ns2:layoutItemId>GetExchangeRate</ns2:layoutItemId><ns2:configData><ns2:id>575a856b-fd53-4e09-873b-937af96e8e26</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Get Exchange Rate</ns2:value></ns2:configData><ns2:configData><ns2:id>9b1182ea-7ea5-4639-8496-4622721fb9f0</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>69ca65de-ac6e-4f6f-872e-426584a729bf</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>9e54fbdb-1df8-4e0e-8d69-063dc2a0cc84</ns2:id><ns2:optionName>attachedService</ns2:optionName><ns2:value>1.ffd340a6-6b9d-4a9c-a56b-1d4038a5e05e</ns2:value></ns2:configData><ns2:configData><ns2:id>e78ad170-fcb7-43b3-8bf1-8361d7d93d01</ns2:id><ns2:optionName>inputData</ns2:optionName><ns2:value></ns2:value><ns2:valueType>dynamic</ns2:valueType></ns2:configData><ns2:configData><ns2:id>82657dfc-193f-4e99-87d8-259904f5ae94</ns2:id><ns2:optionName>eventON_SVCINVOKE</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>7c6c485e-80cc-4df0-85a6-2e52058436a1</ns2:id><ns2:optionName>eventON_SVCRESULT</ns2:optionName><ns2:value>view.setExServiceResults(me);</ns2:value></ns2:configData><ns2:configData><ns2:id>bd50e4da-f21c-4695-8f88-bce1ae25d6c7</ns2:id><ns2:optionName>eventON_SVCERROR</ns2:optionName><ns2:value>alert(error.errorText);</ns2:value></ns2:configData><ns2:configData><ns2:id>91e2e95a-f44b-433e-8a5e-6e7f76c641b1</ns2:id><ns2:optionName>busyIndicator</ns2:optionName><ns2:value>C</ns2:value></ns2:configData><ns2:viewUUID>64.1feaead9-b1d2-4a7e-80a3-22156e6fe8f9</ns2:viewUUID><ns2:binding>tw.options.exRate</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>49c7a5d4-6722-4fa6-88d4-fb0cc8b92322</ns2:id><ns2:layoutItemId>Vertical_Layout6</ns2:layoutItemId><ns2:configData><ns2:id>67d3274f-a959-460c-8fc6-7b4270311f6d</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Vertical layout 6</ns2:value></ns2:configData><ns2:configData><ns2:id>0e328623-e561-4936-8160-64545b3fb6fe</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>e1bedaf7-e02b-4ab5-8112-f9417c061e7a</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>624cd1d7-3fab-4677-8392-************</ns2:id><ns2:optionName>@width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"49%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>5b5eb59c-16f4-47a4-830a-1fbd2a45b75f</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>8e3bb984-d6bb-48d7-8c25-fa090cb3f30c</ns2:id><ns2:layoutItemId>debitedAmountInCurrency</ns2:layoutItemId><ns2:configData><ns2:id>26caa661-567c-42ac-a124-f0488cdfdef9</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>b181afc2-1ce6-4343-8d55-a2a03cebedba</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Debited Amount in Account Currency</ns2:value></ns2:configData><ns2:configData><ns2:id>b520c4e9-aa89-4446-8ebd-2342701610c8</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>d5447ba4-7dcc-4a51-8324-84f7b352f83b</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>f676d93f-44f7-4448-8bbc-29d24c4520a6</ns2:id><ns2:optionName>decimalSep</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>92cc5dca-93c0-49ac-81f9-01c18e8ef1f9</ns2:id><ns2:optionName>decimalPlaces</ns2:optionName><ns2:value>2</ns2:value></ns2:configData><ns2:configData><ns2:id>4674a294-9cb2-4f68-8763-d45bf70a6339</ns2:id><ns2:optionName>eventON_CHANGE</ns2:optionName><ns2:value>view.validateOverDraft(me);</ns2:value></ns2:configData><ns2:viewUUID>64.e0ede0f2-f3af-408c-af7b-e7a58eb5e2b4</ns2:viewUUID><ns2:binding>tw.businessData.chargesAndCommission.currentItem.debitedAmount.amountInAccount</ns2:binding></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:contributions></ns2:contentBoxContrib></ns2:layoutItem><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>a4e23b88-7944-4fad-8b49-1e8d0b4b21d2</ns2:id><ns2:layoutItemId>GetChargesBtn</ns2:layoutItemId><ns2:configData><ns2:id>6f9b41ff-2283-4a8c-89a7-810949f28345</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Get Charges on load</ns2:value></ns2:configData><ns2:configData><ns2:id>c0d01e81-a6a0-425f-80b8-91ad92078b08</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>747760ad-e7c2-4c03-84e6-5e4edd0e131c</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>da01ab07-4d13-49a1-8a97-7d20ffda20f5</ns2:id><ns2:optionName>@visibility</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"HIDDEN\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "true", "loadJsFunction": "if (!this.context.options.isChecker.get(\"value\")) {\r\r\n\tthis.ui.get(\"GetChargesBtn\").click();\r\r\n}else{\r\r\n\tthis.ui.get(\"Vertical_Layout7\").setEnabled(false);\r\r\n}\r\r\n", "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": {"isNull": "true"}, "changeJsFunction": {"isNull": "true"}, "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "8c6bee91-6d2e-4d72-8e55-b5778e8a447c", "versionId": "a72c1fc9-ee22-4196-9904-eb4eb8bfe774", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "bindingType": {"name": "chargesAndCommission", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewBindingTypeId": "65.e46aab16-396d-4e0a-8c98-3fb57d9ebff9", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "true", "classId": "/12.e3018bad-b453-4bf5-96fd-09a5141cd061", "seq": "0", "description": {"isNull": "true"}, "guid": "3d422ff1-cacf-41d2-89e4-be2a62379fc9", "versionId": "c3823455-517d-41ff-8c44-59fc3a01ef9b"}, "configOption": [{"name": "amountCollectableByNBE", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.dd0dcfea-27bf-4938-b09e-dce93eab2c4f", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "0", "description": "", "groupName": "", "guid": "ee60f882-f307-46ee-8eda-e665e1d5b36b", "versionId": "c34f2c4b-7320-48f2-9859-0c1a6c5fc308"}, {"name": "accountList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.42a31739-3ecc-4aad-9483-601fa0824b69", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "1", "description": "", "groupName": "", "guid": "24a61b90-1fef-4912-84f4-bbfa24170ccd", "versionId": "023dfd73-bd7d-4de8-878f-732eca5c2bbc"}, {"name": "accountNo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.e2633861-4d6c-4bbb-8fcf-97ee09356181", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "2", "description": "", "groupName": "", "guid": "94de94df-4eeb-4972-a862-48b7fe352b1c", "versionId": "f4e3b5a0-2b62-4f5e-a31f-0f8f09a2f9c3"}, {"name": "index", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.41a4de21-1c0a-4f6b-b886-de346e1dcfde", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "3", "description": "", "groupName": "", "guid": "276828a9-4dee-41f2-acad-7d763f4bcb69", "versionId": "aa744b70-6ed8-4926-90d4-7d84787f15c9"}, {"name": "commissionSectionVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.d1979495-a1aa-4dd0-b095-6a3ffe295d02", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "4", "description": "", "groupName": "", "guid": "707b5974-94b6-4502-ad1a-0fac108ec949", "versionId": "c2b08c5b-6089-4880-8c02-2277b039e245"}, {"name": "calculatedChangeAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.909953e4-47eb-49ff-b272-3dea9aba0579", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "5", "description": "", "groupName": "", "guid": "4c8b2fa2-b7fe-4456-91d1-6e6a36ebdf51", "versionId": "80f7471b-0ee9-4672-b85a-badea0c1478a"}, {"name": "chargesCustomerAccountList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.5a10ded8-708f-43e4-b930-e95598598668", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "true", "propertyType": "OBJECT", "label": "", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "6", "description": "", "groupName": "", "guid": "7f2c8fd7-365b-46bd-bc83-2bcc8743779d", "versionId": "03f9639f-1cba-451e-a79d-36cc8966204b"}, {"name": "exRate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.e133be5b-fac1-482c-8c96-05499f37051d", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "7", "description": "", "groupName": "", "guid": "7c132345-6251-4908-bab9-747930bb5fbb", "versionId": "55ab67d5-d3cf-4720-87e9-d28b2b16df99"}, {"name": "<PERSON><PERSON><PERSON><PERSON>", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewConfigOptionId": "66.9d6f4fa4-f407-48dd-8925-9fbfb28cbcc1", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "isList": "false", "propertyType": "OBJECT", "label": "", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "processId": {"isNull": "true"}, "actionflowId": {"isNull": "true"}, "isAdaptive": "false", "seq": "8", "description": "", "groupName": "", "guid": "e99ba1cc-a6f5-422e-92af-10a91523a99b", "versionId": "dce2d4a7-d7de-4753-9807-84e3f79bffc7"}], "inlineScript": [{"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.74c38ba8-106c-458f-9cfe-9bd2ef8b3211", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "scriptType": "JS", "scriptBlock": "//On Change Amount and Negotiated Exchange Rate - calculate Debited Amount\r\r\nthis.calculateDebitedAmount = function(index){\r\r\n\tvar chargeAmount = this.context.binding.get(\"value\").get(index).get(\"changeAmount\");\r\r\n\tvar NegotiableRate = this.context.binding.get(\"value\").get(index).get(\"debitedAmount\").get(\"negotiatedExRate\");\r\r\n\tvar debitedAmount = chargeAmount * NegotiableRate;\r\r\n\t\r\r\n\tthis.context.binding.get(\"value\").get(index).get(\"debitedAmount\").set(\"amountInAccount\", debitedAmount);\r\r\n}\r\r\n\r\r\n//On Account Class - Set vis for GL and Customer account\r\r\n//this.accountNumVis = function(value){\r\r\n//\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\") == \"001\") {\r\r\n//\t    //Customer\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setData(\"\");\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t    }\r\r\n//\t\r\r\n//\t} else {\r\r\n//\t    //GL\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setData(null);\r\r\n//\t\r\r\n//\t        //GL account cant be overDrafted or not\r\r\n//\t        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setEnabled(false);\r\r\n//\t    }\r\r\n//\t}\r\r\n//\r\r\n//}\r\r\n\r\r\n//On Rate Type - Set Vis for Flat amount and fixed rate\r\r\nthis.rateTypeVis = function(value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rateType= value.getData();\r\r\n\tif(rateType!=null && rateType == \"Flat Amount\"){\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setData(0);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setData(0);\r\r\n\t}\r\r\n\telse if(rateType!=null && rateType == \"Fixed Rate\"){\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//====================================================================================================\r\r\n//Generic Validate Positive amount (accepts 0) (Change Amount)\r\r\nthis.validatePositive = function(value){\r\r\n\tif (value.getData() < 0) {\r\r\n\t\tvalue.setValid(false,\"Must be >= 0\");\r\r\n//\t\tvalue.setData(0.0);\r\r\n\t}else{\r\r\n\t\tvalue.setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//Generic Validate Digits Only (Account Branch Code)\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Change of Change Percentage - Validate Change percentage before calculate change amount\r\r\nthis.amountValidation = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"rateType\") ==\"Fixed Rate\") {\t\t\r\r\n\t\tif (value.getData() < 0 || value.getData() > 100){\r\r\n\t\t\tvalue.setData(0);\r\r\n\t\t\tvalue.setValid(false, \"Must be >= 0 and < 100\");\r\r\n\t\t}else{\r\r\n\t\t\tvalue.setValid(true);\r\r\n\t\t\tthis.calcChangeAmnt(value);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nvar newIndex;\r\r\n//call from amountValidation function - Calculate change amount service call\r\r\nthis.calcChangeAmnt = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar requestAmnt = this.context.options.amountCollectableByNBE.get(\"value\");\r\r\n\tvar changePercentage = this.context.binding.get(\"value\").get(index).get(\"changePercentage\");\r\r\n\t\r\r\n\tvar concatedDefault = requestAmnt + \",\" + changePercentage;\r\r\n\tthis.ui.get(\"calculateChangeAmount1[\"+index+\"]\").execute(concatedDefault);\r\r\n\tnewIndex = index;\r\r\n}\r\r\n\r\r\n//On result of calculateChangeAmount1 - Map Service call ouput\r\r\nthis.setChangeAmnt = function(){\r\r\n\tvar index = newIndex;\r\r\n\tvar changeAmount = this.context.options.calculatedChangeAmnt.get(\"value\");\r\r\n\tif(!!changeAmount){\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",changeAmount);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",0);\r\r\n\t}\r\r\n} \r\r\n\r\r\n//On change of Account Num - Set Account Info for each customer Account\r\r\nthis.fillAccountData = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tfor (var i = 0; i < this.context.options.chargesCustomerAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t//Set isOverDraft\r\r\n\t\t\tvar commClassCode = this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0, 1);\r\r\n\r\r\n\t\t\tif (code == \"O\" || code == \"D\") {\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\r\r\n\t\t\t//Set Account Info\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"branchCode\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\", {});\r\r\n\t\t\tthis.ui.get(\"accountCurrency[\"+index+\"]\").setData(this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").set(\"vale\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\",this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balance\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t\t\r\r\n\t\t\tthis.context.options.index.set(\"value\", index);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of GL Account Num - Reset Data on GL account change\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"isGLFound\",false);\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"glVerifyMSG\",\"\");\r\r\n}\r\r\n\r\r\n//On click of validate GL Btn - Call A service call to validate GL\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"glAccountNo\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\n//On result of Validate GL - Map the result of GL service call\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"isGLFound\") ) {\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(true);\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"Verified\");\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of Account Cry - Apply vis and Call A service to get exchange rate between Default Cry and Account Cry\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar cryCode = value.getData();\r\r\n//\tvar cryCode = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").get(\"value\");\r\r\n\tif(cryCode != \"\" && cryCode != null && cryCode != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == cryCode) {\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = cryCode;\r\r\n\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Result of get exchange rate\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\n//On Debited Amount in Account Currency and Account Balance- validate is over Draft\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar accountBalance = this.ui.get(\"accountBalance[\"+index+\"]\").getData();\r\r\n\tvar debitedAmount = this.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").getData();\r\r\n\t\r\r\n\t//Need to add &&accountClass is Customer Account-->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\r\n\tif (debitedAmount > accountBalance) {\r\r\n\t\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t\t}else if (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == false){\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"Error: Must be < Account Balance\");\r\r\n\t\t}\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n// Reusable function to set visibility and enabled state\r\r\nthis.setUIVisibilityAndState = function (ui, index, config) {\r\r\n    for (var i = 0; i < config.length; i++) {\r\r\n\t\tvar item = config[i];\r\r\n\t\tui.get(item.name + \"[\" + index + \"]\").setVisible(item.visible, true);\r\r\n\t\tif (item.enabled !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setEnabled(item.enabled);\r\r\n\t\t}\r\r\n\t\tif (item.data !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setData(item.data);\r\r\n\t\t}\r\r\n    }\r\r\n};\r\r\n\r\r\n// Handle Customer Account\r\r\nthis.handleCustomerAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: true },\r\r\n        { name: \"accountBalance\", visible: true },\r\r\n        { name: \"customerAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: false },\r\r\n        { name: \"GLSection1\", visible: false },\r\r\n        { name: \"accountCurrency\", enabled: false }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"glAccountNo\", data: \"\" });\r\r\n    } else {\r\r\n        config.push({ name: \"customerAccountNo\", enabled: false });\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n    this.ui.get(\"accountBranchCode[\" + index + \"]\").setEnabled(false);\r\r\n};\r\r\n\r\r\n// Handle GL Account\r\r\nthis.handleGLAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: false },\r\r\n        { name: \"accountBalance\", visible: false },\r\r\n        { name: \"customerAccountNo\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"GLSection1\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: true, enabled: !isChecker },\r\r\n        { name: \"accountCurrency\", enabled: !isChecker },\r\r\n        { name: \"accountBranchCode\", enabled: !isChecker }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"customerAccountNo\", data: null });\r\r\n        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n};\r\r\n\r\r\n// Main function\r\r\nthis.accountNumVis = function (value) {\r\r\n    var index = value;\r\r\n    var accountClass = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\");\r\r\n    var isChecker = this.context.options.isChecker.get(\"value\");\r\r\n\r\r\n    if (accountClass === \"001\") {\r\r\n        // Customer\r\r\n        this.handleCustomerAccount(index, isChecker);\r\r\n    } else {\r\r\n        // GL\r\r\n        this.handleGLAccount(index, isChecker);\r\r\n    }\r\r\n};\r\r\n", "seq": "0", "description": "", "guid": "4d978b13-e676-40ac-b4c8-84b9e3b14b57", "versionId": "761c0fcb-2fc8-4cb0-9ebb-671f24aa0045"}, {"name": "Inline CSS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.a06fbc97-7feb-4160-9284-1212f1389ae5", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "scriptType": "CSS", "scriptBlock": ".panel-heading {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    justify-content: space-between; /* Distribute space between title and controls */\r\r\n    height: 50px; /* Fixed height for the panel heading */\r\r\n    padding: 0 10px; /* Add horizontal padding */\r\r\n    box-sizing: border-box; /* Include padding and border in height calculations */\r\r\n    overflow: hidden; /* Hide any overflowing content */\r\r\n}\r\r\n\r\r\n.panel-title {\r\r\n    flex: 1; /* Allow title to grow and fill available space */\r\r\n    white-space: nowrap; /* Prevent text from wrapping */\r\r\n    overflow: hidden; /* Hide overflow text */\r\r\n    text-overflow: ellipsis; /* Add ellipsis if text overflows */\r\r\n    font-size: 14px; /* Adjust font size to make the title smaller */\r\r\n    line-height: 1; /* Adjust line height for better text fitting */\r\r\n    margin: 0; /* Ensure no extra margin affecting layout */\r\r\n}\r\r\n\r\r\n.panel-heading-controls {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    margin-left: auto; /* Push controls to the right */\r\r\n}\r\r\n", "seq": "1", "description": "", "guid": "26bae596-96ab-4639-b699-bbb52ce8d89b", "versionId": "a0f85647-5fe3-4cc0-a949-fced5c1566a9"}], "localization": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewLocalResId": "69.6edc6057-e193-4ab2-8171-660f10c5f7fa", "coachViewId": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "resourceBundleGroupId": "/50.41101508-d2e4-4682-b3ef-b9b22266bb5a", "seq": "0", "guid": "d15b49ec-c960-4c8f-aedb-7c8bd83aa7ff", "versionId": "899d0ce8-edb7-4c59-bda9-de2b581ce9d0"}}}}, "hasDetails": true}