{"id": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "versionId": "a72c1fc9-ee22-4196-9904-eb4eb8bfe774", "name": "Charges And Commissions CV 2", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"loadJsFunction": "if (!this.context.options.isChecker.get(\"value\")) {\r\r\n\tthis.ui.get(\"GetChargesBtn\").click();\r\r\n}else{\r\r\n\tthis.ui.get(\"Vertical_Layout7\").setEnabled(false);\r\r\n}", "bindingType": "chargesAndCommission", "configOptions": ["amountCollectableByNBE", "accountList", "accountNo", "index", "commissionSectionVIS", "calculatedChangeAmnt", "chargesCustomerAccountList", "exRate", "<PERSON><PERSON><PERSON><PERSON>"], "inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "//On Change Amount and Negotiated Exchange Rate - calculate Debited Amount\r\r\nthis.calculateDebitedAmount = function(index){\r\r\n\tvar chargeAmount = this.context.binding.get(\"value\").get(index).get(\"changeAmount\");\r\r\n\tvar NegotiableRate = this.context.binding.get(\"value\").get(index).get(\"debitedAmount\").get(\"negotiatedExRate\");\r\r\n\tvar debitedAmount = chargeAmount * NegotiableRate;\r\r\n\t\r\r\n\tthis.context.binding.get(\"value\").get(index).get(\"debitedAmount\").set(\"amountInAccount\", debitedAmount);\r\r\n}\r\r\n\r\r\n//On Account Class - Set vis for GL and Customer account\r\r\n//this.accountNumVis = function(value){\r\r\n//\r\r\n//\tvar index = value.ui.getIndex();\r\r\n//\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\") == \"001\") {\r\r\n//\t    //Customer\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setData(\"\");\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t    }\r\r\n//\t\r\r\n//\t} else {\r\r\n//\t    //GL\r\r\n//\t    if (!this.context.options.isChecker.get(\"value\")) {\r\r\n//\t\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setData(null);\r\r\n//\t\r\r\n//\t        //GL account cant be overDrafted or not\r\r\n//\t        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n//\t    }else{\r\r\n//\t        this.ui.get(\"customerAccountNo[\" + index + \"]\").setVisible(false, true);\r\r\n//\t\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"glAccountNo[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"GLSection1[\" + index + \"]\").setEnabled(false);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setVisible(true, true);\r\r\n//\t        this.ui.get(\"verifyGLButton[\" + index + \"]\").setEnabled(false);\r\r\n//\t    }\r\r\n//\t}\r\r\n//\r\r\n//}\r\r\n\r\r\n//On Rate Type - Set Vis for Flat amount and fixed rate\r\r\nthis.rateTypeVis = function(value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar rateType= value.getData();\r\r\n\tif(rateType!=null && rateType == \"Flat Amount\"){\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(false,true);\r\r\n\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setData(0);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setData(0);\r\r\n\t}\r\r\n\telse if(rateType!=null && rateType == \"Fixed Rate\"){\r\r\n\t\tthis.ui.get(\"defaultPercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changePercentage[\"+index+\"]\").setVisible(true,true);\r\r\n\t\tthis.ui.get(\"changeAmount[\"+index+\"]\").setEnabled(false);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//====================================================================================================\r\r\n//Generic Validate Positive amount (accepts 0) (Change Amount)\r\r\nthis.validatePositive = function(value){\r\r\n\tif (value.getData() < 0) {\r\r\n\t\tvalue.setValid(false,\"Must be >= 0\");\r\r\n//\t\tvalue.setData(0.0);\r\r\n\t}else{\r\r\n\t\tvalue.setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n//Generic Validate Digits Only (Account Branch Code)\r\r\nthis.validateDigits = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif(isNaN(Number(value.getData()))){\r\r\n\t\tvalue.setData(\"\");\r\r\n\t\tvalue.setValid(false ,\"must be digits\");\r\r\n\t\treturn false;\r\r\n\t}else\r\r\n\t{\r\r\n\t\tvalue.setValid(true);\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Change of Change Percentage - Validate Change percentage before calculate change amount\r\r\nthis.amountValidation = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tif (this.context.binding.get(\"value\").get(index).get(\"rateType\") ==\"Fixed Rate\") {\t\t\r\r\n\t\tif (value.getData() < 0 || value.getData() > 100){\r\r\n\t\t\tvalue.setData(0);\r\r\n\t\t\tvalue.setValid(false, \"Must be >= 0 and < 100\");\r\r\n\t\t}else{\r\r\n\t\t\tvalue.setValid(true);\r\r\n\t\t\tthis.calcChangeAmnt(value);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\nvar newIndex;\r\r\n//call from amountValidation function - Calculate change amount service call\r\r\nthis.calcChangeAmnt = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar requestAmnt = this.context.options.amountCollectableByNBE.get(\"value\");\r\r\n\tvar changePercentage = this.context.binding.get(\"value\").get(index).get(\"changePercentage\");\r\r\n\t\r\r\n\tvar concatedDefault = requestAmnt + \",\" + changePercentage;\r\r\n\tthis.ui.get(\"calculateChangeAmount1[\"+index+\"]\").execute(concatedDefault);\r\r\n\tnewIndex = index;\r\r\n}\r\r\n\r\r\n//On result of calculateChangeAmount1 - Map Service call ouput\r\r\nthis.setChangeAmnt = function(){\r\r\n\tvar index = newIndex;\r\r\n\tvar changeAmount = this.context.options.calculatedChangeAmnt.get(\"value\");\r\r\n\tif(!!changeAmount){\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",changeAmount);\r\r\n\t}else{\r\r\n\t\tthis.context.binding.get(\"value\").get(index).set(\"changeAmount\",0);\r\r\n\t}\r\r\n} \r\r\n\r\r\n//On change of Account Num - Set Account Info for each customer Account\r\r\nthis.fillAccountData = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tfor (var i = 0; i < this.context.options.chargesCustomerAccountList.get(\"value\").length(); i++) {\r\r\n\t\tif (this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountNO\") == value.getData()) {\r\r\n\t\t\r\r\n\t\t\t//Set isOverDraft\r\r\n\t\t\tvar commClassCode = this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"accountClassCode\");\r\r\n\t\t\tvar code = commClassCode.substring(0, 1);\r\r\n\r\r\n\t\t\tif (code == \"O\" || code == \"D\") {\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", true);\r\r\n\t\t\t}else{\r\r\n\t\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", false);\r\r\n\t\t\t}\r\r\n\t\t\t\r\r\n\r\r\n\t\t\t//Set Account Info\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"branchCode\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"branchCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\", {});\r\r\n\t\t\tthis.ui.get(\"accountCurrency[\"+index+\"]\").setData(this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").set(\"vale\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n//\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"currency\",this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"currencyCode\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balance\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balance\"));\r\r\n\t\t\tthis.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"balanceSign\", this.context.options.chargesCustomerAccountList.get(\"value\").get(i).get(\"balanceType\"));\r\r\n\t\t\t\r\r\n\t\t\tthis.context.options.index.set(\"value\", index);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of GL Account Num - Reset Data on GL account change\r\r\nthis.resetGLButton = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"isGLFound\",false);\r\r\n\tthis.context.binding.get(\"value\").get(index).set(\"glVerifyMSG\",\"\");\r\r\n}\r\r\n\r\r\n//On click of validate GL Btn - Call A service call to validate GL\r\r\nthis.executeGL = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n//\tvalue.setEnabled(false);\r\r\n\r\r\n\tvar data = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"glAccountNo\");\r\r\n\tthis.ui.get(\"CheckexistingGLAccount1[\"+index+\"]\").execute(data);\r\r\n}\r\r\n\r\r\n//On result of Validate GL - Map the result of GL service call\r\r\nthis.validGlAccount = function (value){\r\r\n\trecord = value.ui.getIndex();\r\r\n\tif (!this.context.binding.get(\"value\").get(record).get(\"isGLFound\") ) {\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(false, \"Account Not Found!\");\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"\");\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"glAccountNo[\"+record+\"]\").setValid(true);\r\r\n\t\tthis.context.binding.get(\"value\").get(record).set(\"glVerifyMSG\",\"Verified\");\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On change of Account Cry - Apply vis and Call A service to get exchange rate between Default Cry and Account Cry\r\r\nthis.setExchangeRate = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar cryCode = value.getData();\r\r\n//\tvar cryCode = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"currency\").get(\"value\");\r\r\n\tif(cryCode != \"\" && cryCode != null && cryCode != undefined){\r\r\n\t\tif (this.ui.get(\"defaultCurrency[\"+index+\"]\").getData() == cryCode) {\r\r\n\t\t\t\r\r\n\t\t\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(1.0);\r\r\n\t\t\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t}else{\r\r\n//\t\t\tthis.ui.get(\"NegotiatedExchangeRate[\"+index+\"]\").setEnabled(false);\r\r\n\t\t\tvar defaultCurrency = this.ui.get(\"defaultCurrency[\"+index+\"]\").getData();\r\r\n\t\t\tvar accountCurrency = cryCode;\r\r\n\r\r\n\t\t\tconcatedCurrency = {ccFrom : defaultCurrency , ccTo : accountCurrency , type:\"TRANSFER\" , sType:\"S\"};\r\r\n\t\t\tvar inputCurr = JSON.stringify(concatedCurrency);\r\r\n\t\t\tthis.ui.get(\"GetExchangeRate[\"+index+\"]\").execute(inputCurr);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\n\r\r\n//On Result of get exchange rate\r\r\nthis.setExServiceResults = function (value){\r\r\n\tvar index = value.ui.getIndex();\r\r\n\r\r\n\tvar rate = this.context.options.exRate.get(\"value\");\r\r\n\tthis.ui.get(\"standardExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setData(Number(rate.toFixed(6)));\r\r\n\tthis.ui.get(\"negotiatedExchangeRate[\"+index+\"]\").setEnabled(true);\r\r\n}\r\r\n\r\r\n//On Debited Amount in Account Currency and Account Balance- validate is over Draft\r\r\nthis.validateOverDraft = function (value) {\r\r\n\tvar index = value.ui.getIndex();\r\r\n\tvar accountBalance = this.ui.get(\"accountBalance[\"+index+\"]\").getData();\r\r\n\tvar debitedAmount = this.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").getData();\r\r\n\t\r\r\n\t//Need to add &&accountClass is Customer Account-->>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>>\r\r\n\tif (debitedAmount > accountBalance) {\r\r\n\t\tif (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == true) {\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"WARNING: Must be < Account Balance\");\r\r\n\t\t}else if (this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"isOverDraft\") == false){\r\r\n\t\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(false,\"Error: Must be < Account Balance\");\r\r\n\t\t}\r\r\n\t}else{\r\r\n\t\tthis.ui.get(\"debitedAmountInCurrency[\"+index+\"]\").setValid(true);\r\r\n\t}\r\r\n}\r\r\n\r\r\n// Reusable function to set visibility and enabled state\r\r\nthis.setUIVisibilityAndState = function (ui, index, config) {\r\r\n    for (var i = 0; i < config.length; i++) {\r\r\n\t\tvar item = config[i];\r\r\n\t\tui.get(item.name + \"[\" + index + \"]\").setVisible(item.visible, true);\r\r\n\t\tif (item.enabled !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setEnabled(item.enabled);\r\r\n\t\t}\r\r\n\t\tif (item.data !== undefined) {\r\r\n\t\t\tui.get(item.name + \"[\" + index + \"]\").setData(item.data);\r\r\n\t\t}\r\r\n    }\r\r\n};\r\r\n\r\r\n// Handle Customer Account\r\r\nthis.handleCustomerAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: true },\r\r\n        { name: \"accountBalance\", visible: true },\r\r\n        { name: \"customerAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: false },\r\r\n        { name: \"GLSection1\", visible: false },\r\r\n        { name: \"accountCurrency\", enabled: false }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"glAccountNo\", data: \"\" });\r\r\n    } else {\r\r\n        config.push({ name: \"customerAccountNo\", enabled: false });\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n    this.ui.get(\"accountBranchCode[\" + index + \"]\").setEnabled(false);\r\r\n};\r\r\n\r\r\n// Handle GL Account\r\r\nthis.handleGLAccount = function (index, isChecker) {\r\r\n    var config = [\r\r\n        { name: \"balanceSign\", visible: false },\r\r\n        { name: \"accountBalance\", visible: false },\r\r\n        { name: \"customerAccountNo\", visible: false },\r\r\n        { name: \"glAccountNo\", visible: true, enabled: !isChecker },\r\r\n        { name: \"GLSection1\", visible: true, enabled: !isChecker },\r\r\n        { name: \"verifyGLButton\", visible: true, enabled: !isChecker },\r\r\n        { name: \"accountCurrency\", enabled: !isChecker },\r\r\n        { name: \"accountBranchCode\", enabled: !isChecker }\r\r\n    ];\r\r\n\r\r\n    if (!isChecker) {\r\r\n        config.push({ name: \"customerAccountNo\", data: null });\r\r\n        this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").set(\"isOverDraft\", null);\r\r\n    }\r\r\n\r\r\n    this.setUIVisibilityAndState(this.ui, index, config);\r\r\n};\r\r\n\r\r\n// Main function\r\r\nthis.accountNumVis = function (value) {\r\r\n    var index = value;\r\r\n    var accountClass = this.context.binding.get(\"value\").get(index).get(\"debitedAccount\").get(\"accountClass\").get(\"name\");\r\r\n    var isChecker = this.context.options.isChecker.get(\"value\");\r\r\n\r\r\n    if (accountClass === \"001\") {\r\r\n        // Customer\r\r\n        this.handleCustomerAccount(index, isChecker);\r\r\n    } else {\r\r\n        // GL\r\r\n        this.handleGLAccount(index, isChecker);\r\r\n    }\r\r\n};"}, {"name": "Inline CSS", "scriptType": "CSS", "scriptBlock": ".panel-heading {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    justify-content: space-between; /* Distribute space between title and controls */\r\r\n    height: 50px; /* Fixed height for the panel heading */\r\r\n    padding: 0 10px; /* Add horizontal padding */\r\r\n    box-sizing: border-box; /* Include padding and border in height calculations */\r\r\n    overflow: hidden; /* Hide any overflowing content */\r\r\n}\r\r\n\r\r\n.panel-title {\r\r\n    flex: 1; /* Allow title to grow and fill available space */\r\r\n    white-space: nowrap; /* Prevent text from wrapping */\r\r\n    overflow: hidden; /* Hide overflow text */\r\r\n    text-overflow: ellipsis; /* Add ellipsis if text overflows */\r\r\n    font-size: 14px; /* Adjust font size to make the title smaller */\r\r\n    line-height: 1; /* Adjust line height for better text fitting */\r\r\n    margin: 0; /* Ensure no extra margin affecting layout */\r\r\n}\r\r\n\r\r\n.panel-heading-controls {\r\r\n    display: flex; /* Use flexbox for layout */\r\r\n    align-items: center; /* Center content vertically */\r\r\n    margin-left: auto; /* Push controls to the right */\r\r\n}"}]}, "hasDetails": true}