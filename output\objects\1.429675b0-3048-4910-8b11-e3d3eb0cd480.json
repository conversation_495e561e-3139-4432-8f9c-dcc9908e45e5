{"id": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "versionId": "e44dbf59-fb14-40c0-9374-7689ef0a2c4d", "name": "Col02 - Review ODC Collection Request", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "customerAccounts", "hasDefault": false, "type": "1"}, {"name": "parentPath", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "errorMessageVis", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "parentRequestNoVIS", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "requestTypeVIS", "hasDefault": false}, {"name": "error", "hasDefault": false}, {"name": "exchangeRate", "hasDefault": false}, {"name": "selectedIndex", "hasDefault": false}, {"name": "calculatedChangeAmnt", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Coach", "id": "2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Set Status And Sub Status", "id": "2025.60ecd941-d556-408c-8f34-0035d98eff47", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Audit Collection Data", "id": "2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Update History", "id": "2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel request", "id": "2025.96add09d-2cd1-4fc2-83f7-663321c91f5d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "Valid?", "id": "2025.6da03458-78da-4bec-8c2a-f95256955724", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Check Action", "id": "2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "audited successfully?", "id": "2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "log history?", "id": "2025.380802ed-0adc-4a70-89bb-9e165e4740da", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "cancel", "id": "2025.a575820a-e008-4e46-8fd8-cd55730132f9", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Exclusive Gateway", "id": "2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "Init", "id": "2025.3f741413-b33e-4822-866b-5b54bf5afa54", "script": "tw.local.actionConditions={};\r\r\ntw.local.actionConditions.complianceApproval=false;\r\r\ntw.local.actionConditions.lastStepAction=tw.local.odcRequest.stepLog.action;\r\r\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol02;\r\r\ntw.local.actionConditions.userRole=tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\t\r\r\n \r\r\n\r\r\n//---------------------------------Init Step log ----------------------------\t \t \r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol02;\r\r\n \r\r\n tw.local.errorMessageVis =\"NONE\"\r\r\n \r\r\n//tw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Validation", "id": "2025.7107a4cd-2470-4441-8cd4-313006e4821d", "script": "tw.local.errorMessage =\"\";\r\r\n\t var mandatoryTriggered = false;\r\r\n\r\r\n\t/************************************************************************************************************************\r\r\n\t/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n\t************************************************************************************************************************/\r\r\n\t//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\n\tmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\t\r\r\n\tif(tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel)\r\r\n\t\tmandatory(tw.local.odcRequest.stepLog.comment, \"tw.local.odcRequest.stepLog.comment\");\r\r\n\t\r\r\n\tif(tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.returnToMaker)\r\r\n\t\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\r\r\n\t/************************************************************************************************************************\r\r\n\t/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n\t************************************************************************************************************************/\r\r\n\t/*\r\r\n\t* =========================================================================================================\r\r\n\t*  \r\r\n\t* Add a coach validation error \r\r\n\t* \t\t\r\r\n\t* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n\t*\r\r\n\t* =========================================================================================================\r\r\n\t*/\r\r\n\tfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n\t{\r\r\n\t\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\t\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n\t}\r\r\n\t/*\r\r\n\t* =================================================================================================================================\r\r\n\t*\r\r\n\t* Add a coach validation error if the string length is less than given length\r\r\n\t*\t\r\r\n\t* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n\t*\r\r\n\t* =================================================================================================================================\r\r\n\t*/\r\r\n\r\r\n\tfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n\t{\r\r\n\t\tif (field != null && field != undefined && field.length < len)\r\r\n\t\t{\r\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\t/*\r\r\n\t* =======================================================================================================================\r\r\n\t*\r\r\n\t* Add a coach validation error if the string length is greater than given length\r\r\n\t*\t\r\r\n\t* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n\t*\r\r\n\t* =======================================================================================================================\r\r\n\t*/\r\r\n\tfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n\t{\r\r\n\t\tif (field.length > len)\r\r\n\t\t{\r\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\t/*\r\r\n\t* ==================================================================================================================\r\r\n\t*\r\r\n\t* Add a coach validation error if the field is null 'Mandatory'\r\r\n\t*\t\r\r\n\t* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n\t*\r\r\n\t* ==================================================================================================================\r\r\n\t*/\r\r\nfunction mandatory(field , fieldName)\r\r\n\t{\r\r\n\t\tif (field == null || field == undefined )\r\r\n\t\t{\r\r\n\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\tmandatoryTriggered = true;\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\telse\r\r\n\t\t{\t\t\t\r\r\n\t\t\tswitch (typeof field)\r\r\n\t\t\t{\r\r\n\t\t\t\tcase \"string\":\r\r\n\t\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\tcase \"number\":\r\r\n\t\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\tcase \"boolean\":\r\r\n\t\t\t\t\tif (field == false)\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\tdefault:\r\r\n\t\t\t\t\t\r\r\n\t\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\t\r\r\n\t\t\t\t\telse\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\t \r\r\n\t \r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorMessageVis =\"EDITABLE\": tw.local.errorMessageVis =\"NONE\";", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "Col02 - Review ODC Collection Request", "lastModified": "1700644271549", "lastModifiedBy": "mohamed.reda", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.8cfd9241-421e-42f7-b45e-ca2a4ba4484c", "2025.8cfd9241-421e-42f7-b45e-ca2a4ba4484c"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eef37169:-79e8", "versionId": "e44dbf59-fb14-40c0-9374-7689ef0a2c4d", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.58004e94-2f91-43dc-8e84-62d25ae95ed7\"],\"isInterrupting\":true,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":74,\"y\":188,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"894fc1f2-d233-4560-85e3-621dfbb612f8\"},{\"outgoing\":[\"2027.ad77e344-40c4-4ac8-8a2a-b06f39956b46\",\"2027.5d59231e-874c-46b6-837c-6472e43a5100\"],\"incoming\":[\"2027.2ac6f51d-476a-4123-86ed-42f63f0310dd\",\"2027.bc5a001c-5afc-436b-8302-176b81638311\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":340,\"y\":165,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okButton\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"Contract_Liquidation2\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"edb492ad-0668-48ed-8e1c-c50bf28da735\",\"optionName\":\"@label\",\"value\":\"Contract Liquidation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a9287e6b-41a2-4e34-85be-6ffa5da7a613\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"937fe3b6-e4ac-4654-890c-78bd279699d4\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"99f4992e-146d-4954-8a3a-f2ef276ecb92\",\"optionName\":\"cif\",\"value\":\"\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e3891a7d-60bb-4348-810a-3e8439430531\",\"optionName\":\"isGlAccount\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9ad20ec5-bc1a-4c3f-87a4-230263c970f0\",\"optionName\":\"contractLiqVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ead4885d-e395-40d5-86fe-655f901e5baa\",\"optionName\":\"isChecker\",\"value\":\"true\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"********-564c-4ea6-87ab-ec290fe3691c\",\"optionName\":\"customerAccounts\",\"value\":\"tw.local.customerAccounts[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"43b7030a-9602-4205-846f-e575368d6460\",\"optionName\":\"exchangeRate\",\"value\":\"tw.local.exchangeRate\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a5f22ec2-ae94-4cff-8369-c4b097bf92d4\",\"optionName\":\"@visibility\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"READONLY\\\"}]}\"}],\"viewUUID\":\"64.14ee925a-157a-48e5-9ab8-7b8879adbe5b\",\"binding\":\"tw.local.odcRequest\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"a0f0df18-2505-49c2-8657-b7479d20ef3c\",\"version\":\"8550\"},{\"layoutItemId\":\"Charges_And_Commissions_CV_21\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"23964dac-e308-44ea-8165-4c43a1f8ff47\",\"optionName\":\"@label\",\"value\":\"Charges And Commissions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"81f30782-27a6-420f-8388-d94bfdc1f3b3\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c1b4e67f-df2b-4fc6-8089-06fcc7235ef1\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9ff1f270-3485-4cd8-8988-46af88b44ba6\",\"optionName\":\"processType\",\"value\":\"LIQD\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"680e5372-8a34-4ad5-8afa-5171588d0710\",\"optionName\":\"amountCollectableByNBE\",\"value\":\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d299e9a1-36bf-4f79-82a1-9aad6a9513dd\",\"optionName\":\"exRate\",\"value\":\"tw.local.exchangeRate\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"64b9d179-b599-4577-8831-cc12b2ed57be\",\"optionName\":\"chargesCustomerAccountList\",\"value\":\"tw.local.odcRequest.customerAndPartyAccountList[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e2c9a710-0f1b-4cbe-87c0-ac34b5763a9b\",\"optionName\":\"calculatedChangeAmnt\",\"value\":\"tw.local.calculatedChangeAmnt\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b07511e5-a6a4-4f47-8b7c-2e5bcfdf58a4\",\"optionName\":\"index\",\"value\":\"tw.local.selectedIndex\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e5e00c22-c080-4fd5-889f-cbbb1ddb224b\",\"optionName\":\"isChecker\",\"value\":\"true\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fb45e4dd-e215-4835-82a2-93778d224d9b\",\"optionName\":\"@visibility\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"READONLY\\\"}]}\"}],\"viewUUID\":\"64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52\",\"binding\":\"tw.local.odcRequest.ChargesAndCommissions[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"31e7b723-3cf5-4a93-804e-9c9ab5b49192\",\"version\":\"8550\"},{\"layoutItemId\":\"Basic_Details_CV1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a3aaa2a1-ee44-4d9d-8824-23eb37da26c3\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1045f93f-7633-46a3-845a-f9a44c51a91c\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e5c4703b-00ea-4d52-857a-bdc0ca27819f\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b65fdaf2-8e16-43e4-8afa-a8a5c46e3c41\",\"optionName\":\"basicDetailsVIS\",\"value\":\"READONLY\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1ff89fb6-3018-46ba-881a-6debd4124943\",\"optionName\":\"parentRequestNoVis\",\"value\":\"NONE\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0d297212-ff38-4e1b-83c5-2bc342800b5c\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"NONE\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1e02a6cb-ebe7-4dcb-8365-a4446f8d4a32\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"READONLY\"},{\"valueType\":\"static\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5cc7ed2e-fb22-4719-8ea8-26f2d1deefe8\",\"optionName\":\"contractStageVIS\",\"value\":\"NONE\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"32cc62b1-b60f-4c30-8026-65e98310588a\",\"version\":\"8550\"},{\"layoutItemId\":\"Customer_Information_cv1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2fa9bf99-f2c6-4e42-8f74-37a4a0552cbb\",\"optionName\":\"@label\",\"value\":\"Customer Info\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"67b40391-b22f-48eb-8468-7563e05c646c\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fa93b2fd-5038-4c95-829c-c670080fad95\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6e0793e9-36b2-44a1-8338-52d78a12011f\",\"optionName\":\"customerInfoVIS\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"98b48273-2801-4d50-8906-8b93bc5c3bda\",\"optionName\":\"listsVIS\",\"value\":\"READONLY\"}],\"viewUUID\":\"64.a7074b64-1e8b-4e3a-8ea0-0c830359104c\",\"binding\":\"tw.local.odcRequest.CustomerInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"2a78ab09-a126-4a9e-8f63-6928fc32c284\",\"version\":\"8550\"},{\"layoutItemId\":\"Financial_Details_Branch1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"87e83398-f02d-4722-8441-31c739a07abb\",\"optionName\":\"@label\",\"value\":\"Financial Details - Branch\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"febdf2a6-00b8-4f8f-8925-960e1e909bce\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b0b0c0ae-0bde-434b-864c-6f9a422d007f\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a8744738-c083-4a55-8b32-4a7c37b3b72a\",\"optionName\":\"financialDetailsVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"10d67f78-02b4-4a4b-803d-3c24e29a2bc0\",\"optionName\":\"fcCollectionVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"57eae5b9-e484-4fa8-8f18-ea580f39aae9\",\"optionName\":\"financialDetailsCVVis\",\"value\":\"READONLY\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"94e5fd52-201a-47c3-8fc6-ef56daa0defe\",\"optionName\":\"currencyDocAmountVIS\",\"value\":\"READONLY\"}],\"viewUUID\":\"64.4992aee7-c679-4a00-9de8-49a633cb5edd\",\"binding\":\"tw.local.odcRequest.FinancialDetailsBR\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"d6432cde-e15c-477a-8d1f-5d34e6e19f55\",\"version\":\"8550\"},{\"layoutItemId\":\"Attachment1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"749cf594-6c7e-4257-8f3d-296f892e7694\",\"optionName\":\"@label\",\"value\":\"Attachments\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"463ad8c0-53d5-4939-8e05-3677b4409193\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3d3f3f4e-2b60-4344-88ba-543de3ea4380\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6ba2c701-5b6b-47a6-870a-b16fe5b12673\",\"optionName\":\"ECMProperties\",\"value\":\"tw.local.odcRequest.attachmentDetails.ecmProperties\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"67b489e9-7ce7-4f71-835a-879051d1f3fc\",\"optionName\":\"canUpdate\",\"value\":\"false\"}],\"viewUUID\":\"64.797f9d29-e666-4d5c-ba4b-cf4866173643\",\"binding\":\"tw.local.odcRequest.attachmentDetails.attachment[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"8e0a1cd9-1918-48eb-8dcd-1035331d034f\",\"version\":\"8550\"},{\"layoutItemId\":\"DC_History1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c5f931e2-b2b0-4eee-8ef6-4a13318c77fb\",\"optionName\":\"@label\",\"value\":\"History\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fa1f596a-252e-4fdd-8e11-38c208780326\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"97f7ac43-63d3-41f8-8466-70209bae0438\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.5df8245e-3f18-41b6-8394-548397e4652f\",\"binding\":\"tw.local.odcRequest.History[]\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"a2684b81-8a83-499a-8487-1a38006bef7c\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"5d22ab62-8802-4ee5-88a8-cadb70f1d280\"}],\"layoutItemId\":\"Tab_Section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"baf7f9e9-fd29-4312-8f9c-e250023fb121\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"96a9d9f6-da76-429d-8af9-3fac51f11c22\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2dd4e1cd-112c-44da-8bd3-c2eea7a86bfa\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"2955179c-5df7-4f15-80d6-17e8cec3be27\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"010a6c90-0384-4c5d-890b-6837fe33dfc9\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"349bf098-21b0-44b2-86fc-5a8715908ac5\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8f075798-c051-4eb5-8f85-9e016c87e9c6\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2de4521d-8e6a-439e-8846-1f01cd85ffca\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8a6d535f-73e4-4a68-8bf7-7b0491b25f33\",\"optionName\":\"terminateReasonVIS\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ff09222a-aff5-4982-8256-39452c148ae3\",\"optionName\":\"errorPanelVIS\",\"value\":\"tw.local.errorMessageVis\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"84d340b5-4750-4bdf-828b-0ea2884242bb\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e75457fb-e598-4f15-8805-115506216697\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ea636e88-b996-42ab-8e9b-9cbacaf646fb\",\"optionName\":\"complianceApprovalVis\",\"value\":\"NONE\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"316a7bea-3cf2-4b8e-850a-56d12c2f3eeb\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b045af3b-70f2-43a0-866e-4d0d241f3825\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"95658de9-ee59-4be9-88ac-f207a2f37068\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"205e2aba-b760-4f7d-8579-20442b4c0852\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bbfc66c6-572e-4ef9-8b05-5b8fd8f1ef49\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"None\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3c6a339f-4552-4743-836f-5b9412340fc8\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"None\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"5bd68a3b-9bec-4033-84af-dfe94c1bb9c6\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Coach\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9\"},{\"targetRef\":\"2025.3f741413-b33e-4822-866b-5b54bf5afa54\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"Start To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.58004e94-2f91-43dc-8e84-62d25ae95ed7\",\"sourceRef\":\"894fc1f2-d233-4560-85e3-621dfbb612f8\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7fcccd4c-cdef-4429-8f89-733b98de3af5\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessageVis\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9752bbb1-7989-4c4b-8129-bdff17ee2cfe\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a48aab00-b0f3-48ff-8572-26e209ff05b8\"},{\"outgoing\":[\"2027.2ac6f51d-476a-4123-86ed-42f63f0310dd\"],\"incoming\":[\"2027.ad77e344-40c4-4ac8-8a2a-b06f39956b46\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.2ac6f51d-476a-4123-86ed-42f63f0310dd\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":383,\"y\":283,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.f6eb724e-739a-4b5d-8cb8-5713123f94da\"},{\"targetRef\":\"2025.f6eb724e-739a-4b5d-8cb8-5713123f94da\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"e0758a07-b5fe-42d8-8807-581bfc793823\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ad77e344-40c4-4ac8-8a2a-b06f39956b46\",\"sourceRef\":\"2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9\"},{\"targetRef\":\"2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2ac6f51d-476a-4123-86ed-42f63f0310dd\",\"sourceRef\":\"2025.f6eb724e-739a-4b5d-8cb8-5713123f94da\"},{\"startQuantity\":1,\"outgoing\":[\"2027.bc5a001c-5afc-436b-8302-176b81638311\"],\"incoming\":[\"2027.58004e94-2f91-43dc-8e84-62d25ae95ed7\"],\"default\":\"2027.bc5a001c-5afc-436b-8302-176b81638311\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":125,\"y\":165,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Init\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.3f741413-b33e-4822-866b-5b54bf5afa54\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.actionConditions={};\\r\\ntw.local.actionConditions.complianceApproval=false;\\r\\ntw.local.actionConditions.lastStepAction=tw.local.odcRequest.stepLog.action;\\r\\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol02;\\r\\ntw.local.actionConditions.userRole=tw.local.odcRequest.initiator;\\r\\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\\t\\r\\n \\r\\n\\r\\n\\/\\/---------------------------------Init Step log ----------------------------\\t \\t \\r\\ntw.local.odcRequest.stepLog={};\\r\\ntw.local.odcRequest.stepLog.startTime = new Date();\\r\\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol02;\\r\\n \\r\\n tw.local.errorMessageVis =\\\"NONE\\\"\\r\\n \\r\\n\\/\\/tw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;\"]}},{\"targetRef\":\"2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.bc5a001c-5afc-436b-8302-176b81638311\",\"sourceRef\":\"2025.3f741413-b33e-4822-866b-5b54bf5afa54\"},{\"targetRef\":\"2025.7107a4cd-2470-4441-8cd4-313006e4821d\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"f3abebe9-eaa1-47fa-8f8f-00ad412597c8\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5d59231e-874c-46b6-837c-6472e43a5100\",\"sourceRef\":\"2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9\"},{\"outgoing\":[\"2027.2551382a-fc57-4d3d-8f68-c96c81f76825\",\"2027.123d8de3-8a5d-4232-89a5-03c3f9854c14\"],\"incoming\":[\"2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c\"],\"default\":\"2027.2551382a-fc57-4d3d-8f68-c96c81f76825\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":629,\"y\":183,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Valid?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.6da03458-78da-4bec-8c2a-f95256955724\"},{\"targetRef\":\"2025.60ecd941-d556-408c-8f34-0035d98eff47\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.2551382a-fc57-4d3d-8f68-c96c81f76825\",\"sourceRef\":\"2025.6da03458-78da-4bec-8c2a-f95256955724\"},{\"incoming\":[\"2027.123d8de3-8a5d-4232-89a5-03c3f9854c14\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":644,\"y\":258,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.b3451acc-3e1b-4e4a-85e3-93b142b5fc52\"},{\"targetRef\":\"2025.b3451acc-3e1b-4e4a-85e3-93b142b5fc52\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  !=\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.123d8de3-8a5d-4232-89a5-03c3f9854c14\",\"sourceRef\":\"2025.6da03458-78da-4bec-8c2a-f95256955724\"},{\"startQuantity\":1,\"outgoing\":[\"2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c\"],\"incoming\":[\"2027.5d59231e-874c-46b6-837c-6472e43a5100\"],\"default\":\"2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c\",\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":507,\"y\":164,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.7107a4cd-2470-4441-8cd4-313006e4821d\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\t tw.local.errorMessage =\\\"\\\";\\r\\n\\t var mandatoryTriggered = false;\\r\\n\\r\\n\\t\\/************************************************************************************************************************\\r\\n\\t\\/*-------------------------------------------------- All Section's Validation ------------------------------------------\\r\\n\\t************************************************************************************************************************\\/\\r\\n\\t\\/\\/-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\\r\\n\\tmandatory(tw.local.odcRequest.stepLog.action, \\\"tw.local.odcRequest.stepLog.action\\\");\\r\\n\\t\\r\\n\\tif(tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel)\\r\\n\\t\\tmandatory(tw.local.odcRequest.stepLog.comment, \\\"tw.local.odcRequest.stepLog.comment\\\");\\r\\n\\t\\r\\n\\tif(tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.returnToMaker)\\r\\n\\t\\tmandatory(tw.local.odcRequest.stepLog.returnReason, \\\"tw.local.odcRequest.stepLog.returnReason\\\");\\r\\n\\r\\n\\t\\/************************************************************************************************************************\\r\\n\\t\\/*-------------------------------------------------- Validation Functions ------------------------------------------\\r\\n\\t************************************************************************************************************************\\/\\r\\n\\t\\/*\\r\\n\\t* =========================================================================================================\\r\\n\\t*  \\r\\n\\t* Add a coach validation error \\r\\n\\t* \\t\\t\\r\\n\\t* EX:\\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\\r\\n\\t*\\r\\n\\t* =========================================================================================================\\r\\n\\t*\\/\\r\\n\\tfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\\r\\n\\t{\\r\\n\\t\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\t\\tfromMandatory && mandatoryTriggered ? \\\"\\\" : tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\";\\r\\n\\t}\\r\\n\\t\\/*\\r\\n\\t* =================================================================================================================================\\r\\n\\t*\\r\\n\\t* Add a coach validation error if the string length is less than given length\\r\\n\\t*\\t\\r\\n\\t* EX:\\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\\r\\n\\t*\\r\\n\\t* =================================================================================================================================\\r\\n\\t*\\/\\r\\n\\r\\n\\tfunction minLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n\\t{\\r\\n\\t\\tif (field != null && field != undefined && field.length < len)\\r\\n\\t\\t{\\r\\n\\t\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\t\\treturn false;\\r\\n\\t\\t}\\r\\n\\t\\treturn true;\\r\\n\\t}\\r\\n\\t\\/*\\r\\n\\t* =======================================================================================================================\\r\\n\\t*\\r\\n\\t* Add a coach validation error if the string length is greater than given length\\r\\n\\t*\\t\\r\\n\\t* EX:\\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\\r\\n\\t*\\r\\n\\t* =======================================================================================================================\\r\\n\\t*\\/\\r\\n\\tfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\\r\\n\\t{\\r\\n\\t\\tif (field.length > len)\\r\\n\\t\\t{\\r\\n\\t\\t\\taddError(fieldName , controlMessage , validationMessage);\\r\\n\\t\\t\\treturn false;\\r\\n\\t\\t}\\r\\n\\t\\treturn true;\\r\\n\\t}\\r\\n\\t\\/*\\r\\n\\t* ==================================================================================================================\\r\\n\\t*\\r\\n\\t* Add a coach validation error if the field is null 'Mandatory'\\r\\n\\t*\\t\\r\\n\\t* EX:\\tnotNull(tw.local.name , 'tw.local.name')\\r\\n\\t*\\r\\n\\t* ==================================================================================================================\\r\\n\\t*\\/\\r\\nfunction mandatory(field , fieldName)\\r\\n\\t{\\r\\n\\t\\tif (field == null || field == undefined )\\r\\n\\t\\t{\\r\\n\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\treturn false;\\r\\n\\t\\t}\\r\\n\\t\\telse\\r\\n\\t\\t{\\t\\t\\t\\r\\n\\t\\t\\tswitch (typeof field)\\r\\n\\t\\t\\t{\\r\\n\\t\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\t\\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\\r\\n\\t\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\t\\tif (field == 0.0)\\r\\n\\t\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\t\\tcase \\\"boolean\\\":\\r\\n\\t\\t\\t\\t\\tif (field == false)\\r\\n\\t\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\t\\tdefault:\\r\\n\\t\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\t\\/\\/ VALIDATE DATE OBJECT\\r\\n\\t\\t\\t\\t\\tif( field && field.getTime && isFinite(field.getTime()) ) {}\\r\\n\\t\\t\\t\\t\\t\\r\\n\\t\\t\\t\\t\\telse\\r\\n\\t\\t\\t\\t\\t{\\r\\n\\t\\t\\t\\t\\t\\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\\r\\n\\t\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\t\\treturn false;\\t\\r\\n\\t\\t\\t\\t\\t}\\r\\n\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t\\treturn true;\\r\\n\\t}\\r\\n\\t \\r\\n\\t \\r\\n\\ttw.local.errorMessage!=null ?  tw.local.errorMessageVis =\\\"EDITABLE\\\": tw.local.errorMessageVis =\\\"NONE\\\";\\t\\t\"]}},{\"targetRef\":\"2025.6da03458-78da-4bec-8c2a-f95256955724\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c\",\"sourceRef\":\"2025.7107a4cd-2470-4441-8cd4-313006e4821d\"},{\"outgoing\":[\"2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec\"],\"incoming\":[\"2027.2551382a-fc57-4d3d-8f68-c96c81f76825\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":728,\"y\":165,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec\",\"name\":\"Set Status And Sub Status\",\"dataInputAssociation\":[{\"targetRef\":\"2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\"]}}]},{\"targetRef\":\"2055.6010a917-3b0f-4c13-8973-51c4eede4cd9\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.stepName\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.60ecd941-d556-408c-8f34-0035d98eff47\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}],\"sourceRef\":[\"2055.71a67d77-b802-42cf-8cf2-380a9594a9c2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}],\"sourceRef\":[\"2055.ce1f6bfc-b971-4978-80c8-092f5d40c209\"]}],\"calledElement\":\"1.81656d33-5348-479b-a7af-5631356d9476\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNoVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6929ca73-2efc-4a7b-8ead-e0b217a10c4f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d7485c46-63ab-4524-829b-2f8c287a1272\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.36a8ed78-9f44-442a-821a-4297a59933b1\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.0cf4db66-4747-4e2e-8173-ef55b9b62c3f\"},{\"outgoing\":[\"2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0\"],\"incoming\":[\"2027.7198b3b8-fd84-4587-8164-f19a87a7cc76\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":997,\"y\":165,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"preAssignmentScript\":[\"\\r\\ntw.local.odcRequest.BasicDetails.requestState=tw.epv.RequestState.Collection ;\\r\\n\\r\\n\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0\",\"name\":\"Audit Collection Data\",\"dataInputAssociation\":[{\"targetRef\":\"2055.e39bfaa6-c863-41f9-8061-0e371dff89cb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.isLiquidated\"]}}]},{\"targetRef\":\"2055.5d4b901c-324e-4bea-8f10-e160a656c696\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.OdcCollection.amount\"]}}]},{\"targetRef\":\"2055.b51575f2-8ce0-48d0-8179-71d12e0440e7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.OdcCollection.currency\"]}}]},{\"targetRef\":\"2055.84daf689-5f95-458f-8b7f-d8c08459d4c1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNo\"]}}]},{\"targetRef\":\"2055.20995cf3-6a12-4378-8292-51106389c796\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestNature.name\"]}}]},{\"targetRef\":\"2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestType.name\"]}}]},{\"targetRef\":\"2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.requestDate\"]}}]},{\"targetRef\":\"2055.85dca7ee-4057-4dcd-878f-b924dff64190\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.BasicDetails.requestState\"]}}]},{\"targetRef\":\"2055.328377fd-ccc9-4119-80ca-435deb518aee\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.status\"]}}]},{\"targetRef\":\"2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.parentRequestNo\"]}}]},{\"targetRef\":\"2055.37b99722-adca-4c0b-8d6d-aa2eeae29994\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.instanceID\"]}}]},{\"targetRef\":\"2055.b0be0c94-0742-4365-875f-1b01b63caf0c\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]},{\"targetRef\":\"2055.27a871f0-6893-4366-80d9-133f55bffddb\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"null\"]}}]},{\"targetRef\":\"2055.ebcd1729-7d20-4759-81b3-e98e9f554767\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.appInfo.subStatus\"]}}]},{\"targetRef\":\"2055.debd9766-ed8e-45c7-8bbb-c471a2567088\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.8d379594-e94f-4a21-8222-396c4ba9b2e1\"]}],\"calledElement\":\"1.40b72dbc-5d84-4b6d-9621-4e738d6838a1\"},{\"targetRef\":\"2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To audited successfully?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0\",\"sourceRef\":\"2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3\"},{\"targetRef\":\"2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Update History\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec\",\"sourceRef\":\"2025.60ecd941-d556-408c-8f34-0035d98eff47\"},{\"outgoing\":[\"2027.7198b3b8-fd84-4587-8164-f19a87a7cc76\",\"2027.09436183-be9f-450c-8f48-e8b1b6c7bdce\"],\"incoming\":[\"2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec\"],\"default\":\"2027.09436183-be9f-450c-8f48-e8b1b6c7bdce\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":843,\"y\":184,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Check Action\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8\"},{\"targetRef\":\"2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.odcRequest.stepLog.action== tw.epv.Col_Actions.authorize) || (tw.local.odcRequest.stepLog.action== tw.epv.Col_Actions.cancel)\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"Authoriza\\/Cancel\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7198b3b8-fd84-4587-8164-f19a87a7cc76\",\"sourceRef\":\"2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8\"},{\"targetRef\":\"2025.e127d662-e51e-4114-848f-7ba1bcf7a31a\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action== tw.epv.Col_Actions.returnToMaker\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Return to Maker\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.09436183-be9f-450c-8f48-e8b1b6c7bdce\",\"sourceRef\":\"2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8\"},{\"outgoing\":[\"2027.74b5fcfd-6654-4818-858d-cdeed73fb2a9\",\"2027.e78696d2-ff65-4300-8d1c-deab0f69117a\"],\"incoming\":[\"2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0\"],\"default\":\"2027.e78696d2-ff65-4300-8d1c-deab0f69117a\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1147,\"y\":184,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"audited successfully?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f\"},{\"targetRef\":\"2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.74b5fcfd-6654-4818-858d-cdeed73fb2a9\",\"sourceRef\":\"2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f\"},{\"incoming\":[\"2027.e78696d2-ff65-4300-8d1c-deab0f69117a\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1198,\"y\":283,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.fe547349-0a81-4c67-8b32-109fcde936f8\"},{\"targetRef\":\"2025.fe547349-0a81-4c67-8b32-109fcde936f8\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage!= null || tw.local.errorMessage!=\\\"\\\"\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e78696d2-ff65-4300-8d1c-deab0f69117a\",\"sourceRef\":\"2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f\"},{\"outgoing\":[\"2027.26edce82-51c9-4452-800f-c98ec2364e47\"],\"incoming\":[\"2027.74b5fcfd-6654-4818-858d-cdeed73fb2a9\"],\"extensionElements\":{\"mode\":[\"InvokeService\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":1255,\"y\":163,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"autoMap\":[true],\"preAssignmentScript\":[\"tw.local.error={};\"]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.26edce82-51c9-4452-800f-c98ec2364e47\",\"name\":\"Update History\",\"dataInputAssociation\":[{\"targetRef\":\"2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.ba547af3-d09b-4196-816b-653732f6b226\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.epv.userRole.RevACt02\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}],\"sourceRef\":[\"2055.416d990b-a0aa-4323-8df7-1f58b014c2ba\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.a617c560-c740-484e-89de-0931088cdc6c\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114\"]}],\"calledElement\":\"1.e8e61c7a-dc6d-441e-b350-b583581efb21\"},{\"incoming\":[\"2027.09436183-be9f-450c-8f48-e8b1b6c7bdce\",\"2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68\",\"2027.8fd8869c-6940-4680-8971-4adf0eb593dd\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":1884,\"y\":186,\"declaredType\":\"TNodeVisualInfo\",\"height\":44}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Copy of End\",\"declaredType\":\"endEvent\",\"id\":\"2025.e127d662-e51e-4114-848f-7ba1bcf7a31a\"},{\"incoming\":[\"2027.f4c14674-5505-47b7-8078-2003ae1bb4b6\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1443,\"y\":256,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Copy of Stay on page 1\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.c0f85d51-40b7-4479-8444-5b880ad6b61c\"},{\"outgoing\":[\"2027.9e5105d8-922f-4f9c-8480-6f717a29f2b9\",\"2027.f4c14674-5505-47b7-8078-2003ae1bb4b6\"],\"incoming\":[\"2027.26edce82-51c9-4452-800f-c98ec2364e47\"],\"default\":\"2027.f4c14674-5505-47b7-8078-2003ae1bb4b6\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1376,\"y\":182,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"log history?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.380802ed-0adc-4a70-89bb-9e165e4740da\"},{\"targetRef\":\"2025.380802ed-0adc-4a70-89bb-9e165e4740da\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To log history?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.26edce82-51c9-4452-800f-c98ec2364e47\",\"sourceRef\":\"2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e\"},{\"targetRef\":\"2025.a575820a-e008-4e46-8fd8-cd55730132f9\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9e5105d8-922f-4f9c-8480-6f717a29f2b9\",\"sourceRef\":\"2025.380802ed-0adc-4a70-89bb-9e165e4740da\"},{\"targetRef\":\"2025.c0f85d51-40b7-4479-8444-5b880ad6b61c\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage!= null) || (tw.local.errorMessage!= \\\"\\\")\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.f4c14674-5505-47b7-8078-2003ae1bb4b6\",\"sourceRef\":\"2025.380802ed-0adc-4a70-89bb-9e165e4740da\"},{\"outgoing\":[\"2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68\",\"2027.12f83726-8c19-40cb-8000-1ffdc6217a54\"],\"incoming\":[\"2027.9e5105d8-922f-4f9c-8480-6f717a29f2b9\"],\"default\":\"2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1557,\"y\":182,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"cancel\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.a575820a-e008-4e46-8fd8-cd55730132f9\"},{\"outgoing\":[\"2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4\"],\"incoming\":[\"2027.12f83726-8c19-40cb-8000-1ffdc6217a54\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1640,\"y\":245,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4\",\"name\":\"cancel request\",\"dataInputAssociation\":[{\"targetRef\":\"2055.55a76aa1-e513-4fd3-835f-7fe160120af7\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest\"]}}]},{\"targetRef\":\"2055.77d2b3d6-2a2a-4520-ad08-31686157d431\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.folderID\"]}}]},{\"targetRef\":\"2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.parentPath\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.96add09d-2cd1-4fc2-83f7-663321c91f5d\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMessage\"]}}],\"sourceRef\":[\"2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a\"]}],\"calledElement\":\"1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc\"},{\"targetRef\":\"2025.e127d662-e51e-4114-848f-7ba1bcf7a31a\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"No\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68\",\"sourceRef\":\"2025.a575820a-e008-4e46-8fd8-cd55730132f9\"},{\"targetRef\":\"2025.96add09d-2cd1-4fc2-83f7-663321c91f5d\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.odcRequest.stepLog.action\\t  ==\\t  tw.epv.CreationActions.cancelRequest\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To cancel request\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.12f83726-8c19-40cb-8000-1ffdc6217a54\",\"sourceRef\":\"2025.a575820a-e008-4e46-8fd8-cd55730132f9\"},{\"targetRef\":\"2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"Copy of To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4\",\"sourceRef\":\"2025.96add09d-2cd1-4fc2-83f7-663321c91f5d\"},{\"incoming\":[\"2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":1801,\"y\":371,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Stay on page 3\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.15f48c73-0753-4b8b-8f39-489b049f18b9\"},{\"outgoing\":[\"2027.8fd8869c-6940-4680-8971-4adf0eb593dd\",\"2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb\"],\"incoming\":[\"2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4\"],\"default\":\"2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":1846,\"y\":258,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"Exclusive Gateway\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7\"},{\"targetRef\":\"2025.e127d662-e51e-4114-848f-7ba1bcf7a31a\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"(tw.local.errorMessage== null || tw.local.errorMessage==\\\"\\\")\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"Copy 2 of To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8fd8869c-6940-4680-8971-4adf0eb593dd\",\"sourceRef\":\"2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7\"},{\"targetRef\":\"2025.15f48c73-0753-4b8b-8f39-489b049f18b9\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"Copy of To Stay on page 3\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb\",\"sourceRef\":\"2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"exchangeRate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b183ef89-e434-40f2-8821-a3bb142d4af0\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"selectedIndex\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c0572527-8ba1-46c2-8cff-fccee6caa683\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"calculatedChangeAmnt\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a834fb5c-a2f7-4048-8585-f47e18327f1e\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"ea9bc070-b4d0-45dd-8d23-9626283211a3\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"cb791626-de77-47cb-820f-1ceeb0dedc19\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Col02 - Review ODC Collection Request\",\"declaredType\":\"globalUserTask\",\"id\":\"1.429675b0-3048-4910-8b11-e3d3eb0cd480\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.568ffb88-c172-4fff-841d-2e47908795cb\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.aaadf3db-e847-41d9-8daf-6dde3d4fd8f1\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd\",\"epvProcessLinkId\":\"f1aded2b-52c0-44ac-89dd-8c75c4b4021f\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.e22cd1cb-4788-4edd-beb4-825e8f27f335\",\"epvProcessLinkId\":\"cdb0ce7c-6d4a-4cf8-801f-1fa1f72111d4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"21d97b65-f94e-4986-87c0-d21e05373a57\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"c5f1d454-11b1-4a30-8325-770eed01ff5f\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.062854b5-6513-4da8-84ab-0126f90e550d\",\"epvProcessLinkId\":\"0d9ef4f2-7c49-4294-89ab-aa90be3443f7\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"371c7037-03a5-412f-84c3-a940a9941c7d\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{}],\"outputSet\":[{}],\"dataInput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a\"},{\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"customerAccounts\",\"isCollection\":true,\"id\":\"2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentPath\",\"isCollection\":false,\"id\":\"2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"1.6d886d4a-fc92-4944-82e4-58d15607456c\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "f72efb7f-64d8-4208-a103-f636db421f96", "versionId": "cb60c725-728b-4d46-b7d3-50f834ccbbfe"}, {"name": "customerAccounts", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "parameterType": "1", "isArrayOf": "true", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "377c7e81-a6f5-4245-b443-527cf4570802", "versionId": "a26f952b-7308-4dea-bd37-2165f96e193b"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "d960165d-d4a0-4a79-b38f-adf77b68bd52", "versionId": "f499c2a3-5115-4a13-a688-f05c1ac0f7c1"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.568ffb88-c172-4fff-841d-2e47908795cb", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1014afac-80f3-483a-853d-6bf7b798ba4e", "versionId": "5041ae73-5c8d-4d8c-8a83-83f70287b1a3"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7fcccd4c-cdef-4429-8f89-733b98de3af5", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "235d1225-4c8f-4f10-8eb8-230d05411fe5", "versionId": "3951e1c2-e04b-4e56-8624-30375b05370c"}, {"name": "errorMessageVis", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9752bbb1-7989-4c4b-8129-bdff17ee2cfe", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9a65d3f2-a3a0-4df6-a017-39175ff8c9dd", "versionId": "f6acbc97-da63-488b-828d-732426082f78"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a48aab00-b0f3-48ff-8572-26e209ff05b8", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3b5190ce-8813-4f99-9178-5d0db0021395", "versionId": "abb6a062-cdbc-40c6-88be-ffb2267c7837"}, {"name": "parentRequestNoVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6929ca73-2efc-4a7b-8ead-e0b217a10c4f", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "24971b98-60c4-4f7c-a40b-f02802fef1e7", "versionId": "f824e297-2c30-4bdb-b26c-d28d5d6e322f"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d7485c46-63ab-4524-829b-2f8c287a1272", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c8a05966-991e-4d4c-be27-5c372e00b27d", "versionId": "7afe489e-a4a8-432a-84a3-eaed13be22e2"}, {"name": "requestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.36a8ed78-9f44-442a-821a-4297a59933b1", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "38656128-1ee7-4a49-bf2e-d3b74ea46efa", "versionId": "8cde459d-7e59-480c-b19a-db85445a246e"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0cf4db66-4747-4e2e-8173-ef55b9b62c3f", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "977714ff-ce72-4707-bb77-d93477a70d5b", "versionId": "4a7fc717-a96b-42a0-b65a-a55ac39df6a4"}, {"name": "exchangeRate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b183ef89-e434-40f2-8821-a3bb142d4af0", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3c7c0355-e65d-4788-a728-56523ef84dc7", "versionId": "796b75bf-9428-4c6f-a59b-cc7fccee3636"}, {"name": "selectedIndex", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c0572527-8ba1-46c2-8cff-fccee6caa683", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "45c94935-10b5-4be1-8805-5386f210935e", "versionId": "004e0164-4dc9-4a8b-89be-567b1fe74ccf"}, {"name": "calculatedChangeAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a834fb5c-a2f7-4048-8585-f47e18327f1e", "description": {"isNull": "true"}, "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bee9a3de-669e-4de2-b808-88104fdef1fe", "versionId": "ea985dcb-b7c5-4331-b9b5-de19018c748c"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.60ecd941-d556-408c-8f34-0035d98eff47", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "Set Status And Sub Status", "tWComponentName": "SubProcess", "tWComponentId": "3012.4c48022b-6d47-496b-9e00-8c098ec1d837", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:25fd907f15501a6a:510e674:189fee9ec23:-b3", "versionId": "55651d0b-bf41-4cc5-8d82-5b6180dd9c85", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.4c48022b-6d47-496b-9e00-8c098ec1d837", "attachedProcessRef": "/1.81656d33-5348-479b-a7af-5631356d9476", "guid": "7631f103-aa97-42bb-b322-a89ffe94a86f", "versionId": "b54669df-51f6-4b5b-8c65-6dd63676ad02"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.96add09d-2cd1-4fc2-83f7-663321c91f5d", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "cancel request", "tWComponentName": "SubProcess", "tWComponentId": "3012.b03b7e4b-965d-4f71-b28b-719dc9e1cdfc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7a40", "versionId": "5dc8269a-ee59-44f0-ba09-184a7ca84d62", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.b03b7e4b-965d-4f71-b28b-719dc9e1cdfc", "attachedProcessRef": "/1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "guid": "69660bf0-b34a-407f-ad05-52009457dee8", "versionId": "8c5e6e51-7851-43c6-870a-e360ee82580e"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8cfd9241-421e-42f7-b45e-ca2a4ba4484c", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.d1fd01f6-767e-48da-a069-0f22ef6e9ee7", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eef37169:-79e6", "versionId": "a12393de-0df5-43be-aa9a-4c282b54db01", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "Audit Collection Data", "tWComponentName": "SubProcess", "tWComponentId": "3012.5c992176-cdf0-4f51-ba32-db0f875086e5", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:989374ae3827db3a:f13aa6c:18a98f4fe63:4686", "versionId": "adf6f9d4-f6ec-47e3-883f-a94058b36b4e", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5c992176-cdf0-4f51-ba32-db0f875086e5", "attachedProcessRef": "/1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "guid": "81430660-277e-4845-8c25-f4907d1c3a52", "versionId": "19590f0e-e93e-4dc7-b61e-1e8aac3a486d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "Update History", "tWComponentName": "SubProcess", "tWComponentId": "3012.03ae06ad-2e6f-45e8-b02a-dbea6401c554", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:ee1d8a182ba8b1ae:-f408c1e:18b855767df:-1057", "versionId": "b56c6190-e30c-496b-b43b-ecc736a81da4", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.03ae06ad-2e6f-45e8-b02a-dbea6401c554", "attachedProcessRef": "/1.e8e61c7a-dc6d-441e-b350-b583581efb21", "guid": "b2466054-5c1a-4bd8-951f-1654cc9cae89", "versionId": "342d8385-e1f2-45f2-ab15-ebfd7a16eb82"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.036655af-687f-4584-970c-13e97c9e565b", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.e58bf884-4a26-43b7-9572-24d043a7fdd8", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:6ad7eb4224455a46:11f23e39:189eef37169:-79e7", "versionId": "dd90d1d9-9716-4700-a571-d743a2f39fe8", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.e58bf884-4a26-43b7-9572-24d043a7fdd8", "haltProcess": "false", "guid": "f375ea7b-9d87-4f0a-83e1-513db4fa384a", "versionId": "ab31295a-8044-4964-940e-4cdde7301f00"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.4f0ce27c-0385-45b5-af48-ecd689ecce5d", "epvId": "/21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "guid": "263b8012-f07d-4e4d-b88e-ea7f3641e8d1", "versionId": "2b5d73bc-92d0-4dd2-aac7-453a5594ed82"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.b58ea136-b992-4c31-8511-4c258288a57f", "epvId": "/21.062854b5-6513-4da8-84ab-0126f90e550d", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "guid": "a7b3bb5a-acd4-48af-8e91-26a0a6be7d91", "versionId": "586bffa0-1d3d-4e86-94da-3bf5bf6fee0b"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.eac7710a-3bca-4134-8f4e-e6485395cf5a", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "guid": "f72e173f-376b-4bb2-9a2a-3474205e978d", "versionId": "66c7bc0f-042b-4a51-9fe4-e1a0ef4b0537"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.24b1e4fa-ecd4-45ac-9734-c3b57db4fd81", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "guid": "af49f84f-39f9-4c69-bd66-5fcca0ad0b18", "versionId": "99204977-550b-4302-84d3-cc2ecc3b2cc9"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.fffe3ea0-3735-453e-ac94-83327f3b3946", "epvId": "/21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "guid": "41e6c4a8-5922-400e-9375-900eb145f3e1", "versionId": "a33f4e16-c541-4d89-b14a-f1668f8421eb"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.196e6520-1e6b-4665-90b4-fc6759059cf1", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "guid": "898b1cfc-4bdd-4da9-a8b3-f07fe0188695", "versionId": "d1012d70-5f9b-44d7-b7ec-f7cca0ff15fe"}], "RESOURCE_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.07e7c474-1d1a-483f-b39e-29e47948f9fc", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "guid": "16da35c4-037d-4da0-9bd5-bb2d1415fe33", "versionId": "cc07e9c1-ca69-4f66-b48c-4d53fbbc8154"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "1.6d886d4a-fc92-4944-82e4-58d15607456c", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:globalUserTask": {"implementation": "##unspecified", "name": "Col02 - Review ODC Collection Request", "id": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:userTaskImplementation": {"processType": "None", "isClosed": "false", "id": "cb791626-de77-47cb-820f-1ceeb0dedc19", "ns16:startEvent": {"name": "Start", "id": "894fc1f2-d233-4560-85e3-621dfbb612f8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "74", "y": "188", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.58004e94-2f91-43dc-8e84-62d25ae95ed7"}, "ns3:formTask": {"name": "Coach", "id": "2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "340", "y": "165", "width": "95", "height": "70"}, "ns3:validationStayOnPagePaths": "okButton"}, "ns16:incoming": ["2027.2ac6f51d-476a-4123-86ed-42f63f0310dd", "2027.bc5a001c-5afc-436b-8302-176b81638311"], "ns16:outgoing": ["2027.ad77e344-40c4-4ac8-8a2a-b06f39956b46", "2027.5d59231e-874c-46b6-837c-6472e43a5100"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "5bd68a3b-9bec-4033-84af-dfe94c1bb9c6", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "349bf098-21b0-44b2-86fc-5a8715908ac5", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "8f075798-c051-4eb5-8f85-9e016c87e9c6", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "2de4521d-8e6a-439e-8846-1f01cd85ffca", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "8a6d535f-73e4-4a68-8bf7-7b0491b25f33", "ns19:optionName": "terminateReasonVIS", "ns19:value": "NONE"}, {"ns19:id": "ff09222a-aff5-4982-8256-39452c148ae3", "ns19:optionName": "errorPanelVIS", "ns19:value": "tw.local.errorMessageVis", "ns19:valueType": "dynamic"}, {"ns19:id": "84d340b5-4750-4bdf-828b-0ea2884242bb", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "e75457fb-e598-4f15-8805-115506216697", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "ea636e88-b996-42ab-8e9b-9cbacaf646fb", "ns19:optionName": "complianceApprovalVis", "ns19:value": "NONE"}, {"ns19:id": "316a7bea-3cf2-4b8e-850a-56d12c2f3eeb", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "b045af3b-70f2-43a0-866e-4d0d241f3825", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "95658de9-ee59-4be9-88ac-f207a2f37068", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "205e2aba-b760-4f7d-8579-20442b4c0852", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "bbfc66c6-572e-4ef9-8b05-5b8fd8f1ef49", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "None"}, {"ns19:id": "3c6a339f-4552-4743-836f-5b9412340fc8", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "None"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "010a6c90-0384-4c5d-890b-6837fe33dfc9", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "2955179c-5df7-4f15-80d6-17e8cec3be27", "ns19:layoutItemId": "Tab_Section1", "ns19:configData": [{"ns19:id": "baf7f9e9-fd29-4312-8f9c-e250023fb121", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "96a9d9f6-da76-429d-8af9-3fac51f11c22", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "2dd4e1cd-112c-44da-8bd3-c2eea7a86bfa", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "5d22ab62-8802-4ee5-88a8-cadb70f1d280", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "a0f0df18-2505-49c2-8657-b7479d20ef3c", "ns19:layoutItemId": "Contract_Liquidation2", "ns19:configData": [{"ns19:id": "edb492ad-0668-48ed-8e1c-c50bf28da735", "ns19:optionName": "@label", "ns19:value": "Contract Liquidation"}, {"ns19:id": "a9287e6b-41a2-4e34-85be-6ffa5da7a613", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "937fe3b6-e4ac-4654-890c-78bd279699d4", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "99f4992e-146d-4954-8a3a-f2ef276ecb92", "ns19:optionName": "cif", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "e3891a7d-60bb-4348-810a-3e8439430531", "ns19:optionName": "isGlAccount", "ns19:value": "", "ns19:valueType": "static"}, {"ns19:id": "9ad20ec5-bc1a-4c3f-87a4-230263c970f0", "ns19:optionName": "contractLiqVis", "ns19:value": "READONLY"}, {"ns19:id": "ead4885d-e395-40d5-86fe-655f901e5baa", "ns19:optionName": "<PERSON><PERSON><PERSON><PERSON>", "ns19:value": "true"}, {"ns19:id": "********-564c-4ea6-87ab-ec290fe3691c", "ns19:optionName": "customerAccounts", "ns19:value": "tw.local.customerAccounts[]", "ns19:valueType": "dynamic"}, {"ns19:id": "43b7030a-9602-4205-846f-e575368d6460", "ns19:optionName": "exchangeRate", "ns19:value": "tw.local.exchangeRate", "ns19:valueType": "dynamic"}, {"ns19:id": "a5f22ec2-ae94-4cff-8369-c4b097bf92d4", "ns19:optionName": "@visibility", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"}], "ns19:viewUUID": "64.14ee925a-157a-48e5-9ab8-7b8879adbe5b", "ns19:binding": "tw.local.odcRequest"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "31e7b723-3cf5-4a93-804e-9c9ab5b49192", "ns19:layoutItemId": "Charges_And_Commissions_CV_21", "ns19:configData": [{"ns19:id": "23964dac-e308-44ea-8165-4c43a1f8ff47", "ns19:optionName": "@label", "ns19:value": "Charges And Commissions"}, {"ns19:id": "81f30782-27a6-420f-8388-d94bfdc1f3b3", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "c1b4e67f-df2b-4fc6-8089-06fcc7235ef1", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "9ff1f270-3485-4cd8-8988-46af88b44ba6", "ns19:optionName": "processType", "ns19:value": "LIQD"}, {"ns19:id": "680e5372-8a34-4ad5-8afa-5171588d0710", "ns19:optionName": "amountCollectableByNBE", "ns19:value": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "d299e9a1-36bf-4f79-82a1-9aad6a9513dd", "ns19:optionName": "exRate", "ns19:value": "tw.local.exchangeRate", "ns19:valueType": "dynamic"}, {"ns19:id": "64b9d179-b599-4577-8831-cc12b2ed57be", "ns19:optionName": "chargesCustomerAccountList", "ns19:value": "tw.local.odcRequest.customerAndPartyAccountList[]", "ns19:valueType": "dynamic"}, {"ns19:id": "e2c9a710-0f1b-4cbe-87c0-ac34b5763a9b", "ns19:optionName": "calculatedChangeAmnt", "ns19:value": "tw.local.calculatedChangeAmnt", "ns19:valueType": "dynamic"}, {"ns19:id": "b07511e5-a6a4-4f47-8b7c-2e5bcfdf58a4", "ns19:optionName": "index", "ns19:value": "tw.local.selectedIndex", "ns19:valueType": "dynamic"}, {"ns19:id": "e5e00c22-c080-4fd5-889f-cbbb1ddb224b", "ns19:optionName": "<PERSON><PERSON><PERSON><PERSON>", "ns19:value": "true", "ns19:valueType": "static"}, {"ns19:id": "fb45e4dd-e215-4835-82a2-93778d224d9b", "ns19:optionName": "@visibility", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"READONLY\"}]}"}], "ns19:viewUUID": "64.1d952add-f5d8-4e17-98e0-cbf0ab7efd52", "ns19:binding": "tw.local.odcRequest.ChargesAndCommissions[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "32cc62b1-b60f-4c30-8026-65e98310588a", "ns19:layoutItemId": "Basic_Details_CV1", "ns19:configData": [{"ns19:id": "a3aaa2a1-ee44-4d9d-8824-23eb37da26c3", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "1045f93f-7633-46a3-845a-f9a44c51a91c", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "e5c4703b-00ea-4d52-857a-bdc0ca27819f", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "b65fdaf2-8e16-43e4-8afa-a8a5c46e3c41", "ns19:optionName": "basicDetailsVIS", "ns19:value": "READONLY"}, {"ns19:id": "1ff89fb6-3018-46ba-881a-6debd4124943", "ns19:optionName": "parentRequestNoVis", "ns19:value": "NONE", "ns19:valueType": "static"}, {"ns19:id": "0d297212-ff38-4e1b-83c5-2bc342800b5c", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "NONE", "ns19:valueType": "static"}, {"ns19:id": "1e02a6cb-ebe7-4dcb-8365-a4446f8d4a32", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "READONLY", "ns19:valueType": "static"}, {"ns19:id": "5cc7ed2e-fb22-4719-8ea8-26f2d1deefe8", "ns19:optionName": "contractStageVIS", "ns19:value": "NONE", "ns19:valueType": "static"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "2a78ab09-a126-4a9e-8f63-6928fc32c284", "ns19:layoutItemId": "Customer_Information_cv1", "ns19:configData": [{"ns19:id": "2fa9bf99-f2c6-4e42-8f74-37a4a0552cbb", "ns19:optionName": "@label", "ns19:value": "Customer Info"}, {"ns19:id": "67b40391-b22f-48eb-8468-7563e05c646c", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "fa93b2fd-5038-4c95-829c-c670080fad95", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "6e0793e9-36b2-44a1-8338-52d78a12011f", "ns19:optionName": "customerInfoVIS", "ns19:value": "READONLY"}, {"ns19:id": "98b48273-2801-4d50-8906-8b93bc5c3bda", "ns19:optionName": "listsVIS", "ns19:value": "READONLY"}], "ns19:viewUUID": "64.a7074b64-1e8b-4e3a-8ea0-0c830359104c", "ns19:binding": "tw.local.odcRequest.CustomerInfo"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "d6432cde-e15c-477a-8d1f-5d34e6e19f55", "ns19:layoutItemId": "Financial_Details_Branch1", "ns19:configData": [{"ns19:id": "87e83398-f02d-4722-8441-31c739a07abb", "ns19:optionName": "@label", "ns19:value": "Financial Details - Branch"}, {"ns19:id": "febdf2a6-00b8-4f8f-8925-960e1e909bce", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "b0b0c0ae-0bde-434b-864c-6f9a422d007f", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "a8744738-c083-4a55-8b32-4a7c37b3b72a", "ns19:optionName": "financialDetailsVis", "ns19:value": "READONLY"}, {"ns19:id": "10d67f78-02b4-4a4b-803d-3c24e29a2bc0", "ns19:optionName": "fcCollectionVis", "ns19:value": "READONLY"}, {"ns19:id": "57eae5b9-e484-4fa8-8f18-ea580f39aae9", "ns19:optionName": "financialDetailsCVVis", "ns19:value": "READONLY"}, {"ns19:id": "94e5fd52-201a-47c3-8fc6-ef56daa0defe", "ns19:optionName": "currencyDocAmountVIS", "ns19:value": "READONLY"}], "ns19:viewUUID": "64.4992aee7-c679-4a00-9de8-49a633cb5edd", "ns19:binding": "tw.local.odcRequest.FinancialDetailsBR"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "8e0a1cd9-1918-48eb-8dcd-1035331d034f", "ns19:layoutItemId": "Attachment1", "ns19:configData": [{"ns19:id": "749cf594-6c7e-4257-8f3d-296f892e7694", "ns19:optionName": "@label", "ns19:value": "Attachments"}, {"ns19:id": "463ad8c0-53d5-4939-8e05-3677b4409193", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "3d3f3f4e-2b60-4344-88ba-543de3ea4380", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "6ba2c701-5b6b-47a6-870a-b16fe5b12673", "ns19:optionName": "ECMProperties", "ns19:value": "tw.local.odcRequest.attachmentDetails.ecmProperties", "ns19:valueType": "dynamic"}, {"ns19:id": "67b489e9-7ce7-4f71-835a-879051d1f3fc", "ns19:optionName": "canUpdate", "ns19:value": "false"}], "ns19:viewUUID": "64.797f9d29-e666-4d5c-ba4b-cf4866173643", "ns19:binding": "tw.local.odcRequest.attachmentDetails.attachment[]"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "a2684b81-8a83-499a-8487-1a38006bef7c", "ns19:layoutItemId": "DC_History1", "ns19:configData": [{"ns19:id": "c5f931e2-b2b0-4eee-8ef6-4a13318c77fb", "ns19:optionName": "@label", "ns19:value": "History"}, {"ns19:id": "fa1f596a-252e-4fdd-8e11-38c208780326", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "97f7ac43-63d3-41f8-8466-70209bae0438", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.5df8245e-3f18-41b6-8394-548397e4652f", "ns19:binding": "tw.local.odcRequest.History[]"}]}}}}}}}}, "ns16:sequenceFlow": [{"sourceRef": "894fc1f2-d233-4560-85e3-621dfbb612f8", "targetRef": "2025.3f741413-b33e-4822-866b-5b54bf5afa54", "name": "Start To Coach", "id": "2027.58004e94-2f91-43dc-8e84-62d25ae95ed7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9", "targetRef": "2025.f6eb724e-739a-4b5d-8cb8-5713123f94da", "name": "To Postpone", "id": "2027.ad77e344-40c4-4ac8-8a2a-b06f39956b46", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:coachEventBinding": {"id": "e0758a07-b5fe-42d8-8807-581bfc793823", "ns3:coachEventPath": "DC_Templete1/saveState"}}}, {"sourceRef": "2025.f6eb724e-739a-4b5d-8cb8-5713123f94da", "targetRef": "2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9", "name": "To Coach", "id": "2027.2ac6f51d-476a-4123-86ed-42f63f0310dd", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "bottomRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}}}, {"sourceRef": "2025.3f741413-b33e-4822-866b-5b54bf5afa54", "targetRef": "2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9", "name": "To Coach", "id": "2027.bc5a001c-5afc-436b-8302-176b81638311", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.b4a84ecd-3235-4f44-82b3-2168f43d47e9", "targetRef": "2025.7107a4cd-2470-4441-8cd4-313006e4821d", "name": "To End", "id": "2027.5d59231e-874c-46b6-837c-6472e43a5100", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "f3abebe9-eaa1-47fa-8f8f-00ad412597c8", "ns3:coachEventPath": "DC_Templete1/submit"}}}, {"sourceRef": "2025.6da03458-78da-4bec-8c2a-f95256955724", "targetRef": "2025.60ecd941-d556-408c-8f34-0035d98eff47", "name": "Yes", "id": "2027.2551382a-fc57-4d3d-8f68-c96c81f76825", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.6da03458-78da-4bec-8c2a-f95256955724", "targetRef": "2025.b3451acc-3e1b-4e4a-85e3-93b142b5fc52", "name": "No", "id": "2027.123d8de3-8a5d-4232-89a5-03c3f9854c14", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  !=\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.7107a4cd-2470-4441-8cd4-313006e4821d", "targetRef": "2025.6da03458-78da-4bec-8c2a-f95256955724", "name": "To Valid?", "id": "2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3", "targetRef": "2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f", "name": "To audited successfully?", "id": "2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.60ecd941-d556-408c-8f34-0035d98eff47", "targetRef": "2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8", "name": "To Update History", "id": "2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}}}, {"sourceRef": "2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8", "targetRef": "2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3", "name": "Authoriza/Cancel", "id": "2027.7198b3b8-fd84-4587-8164-f19a87a7cc76", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.odcRequest.stepLog.action== tw.epv.Col_Actions.authorize) || (tw.local.odcRequest.stepLog.action== tw.epv.Col_Actions.cancel)", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8", "targetRef": "2025.e127d662-e51e-4114-848f-7ba1bcf7a31a", "name": "Return to Maker", "id": "2027.09436183-be9f-450c-8f48-e8b1b6c7bdce", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action== tw.epv.Col_Actions.returnToMaker", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f", "targetRef": "2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e", "name": "Yes", "id": "2027.74b5fcfd-6654-4818-858d-cdeed73fb2a9", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f", "targetRef": "2025.fe547349-0a81-4c67-8b32-109fcde936f8", "name": "No", "id": "2027.e78696d2-ff65-4300-8d1c-deab0f69117a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.errorMessage!= null || tw.local.errorMessage!=\"\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e", "targetRef": "2025.380802ed-0adc-4a70-89bb-9e165e4740da", "name": "To log history?", "id": "2027.26edce82-51c9-4452-800f-c98ec2364e47", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.380802ed-0adc-4a70-89bb-9e165e4740da", "targetRef": "2025.a575820a-e008-4e46-8fd8-cd55730132f9", "name": "Yes", "id": "2027.9e5105d8-922f-4f9c-8480-6f717a29f2b9", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.380802ed-0adc-4a70-89bb-9e165e4740da", "targetRef": "2025.c0f85d51-40b7-4479-8444-5b880ad6b61c", "name": "No", "id": "2027.f4c14674-5505-47b7-8078-2003ae1bb4b6", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage!= null) || (tw.local.errorMessage!= \"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.a575820a-e008-4e46-8fd8-cd55730132f9", "targetRef": "2025.e127d662-e51e-4114-848f-7ba1bcf7a31a", "name": "No", "id": "2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.a575820a-e008-4e46-8fd8-cd55730132f9", "targetRef": "2025.96add09d-2cd1-4fc2-83f7-663321c91f5d", "name": "To cancel request", "id": "2027.12f83726-8c19-40cb-8000-1ffdc6217a54", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.96add09d-2cd1-4fc2-83f7-663321c91f5d", "targetRef": "2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7", "name": "<PERSON><PERSON> of To End", "id": "2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7", "targetRef": "2025.e127d662-e51e-4114-848f-7ba1bcf7a31a", "name": "Copy 2 of To End", "id": "2027.8fd8869c-6940-4680-8971-4adf0eb593dd", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "(tw.local.errorMessage== null || tw.local.errorMessage==\"\")", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7", "targetRef": "2025.15f48c73-0753-4b8b-8f39-489b049f18b9", "name": "Co<PERSON> of To Stay on page 3", "id": "2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.7fcccd4c-cdef-4429-8f89-733b98de3af5"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessageVis", "id": "2056.9752bbb1-7989-4c4b-8129-bdff17ee2cfe"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.a48aab00-b0f3-48ff-8572-26e209ff05b8"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNoVIS", "id": "2056.6929ca73-2efc-4a7b-8ead-e0b217a10c4f"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.d7485c46-63ab-4524-829b-2f8c287a1272"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestTypeVIS", "id": "2056.36a8ed78-9f44-442a-821a-4297a59933b1"}, {"itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "name": "error", "id": "2056.0cf4db66-4747-4e2e-8173-ef55b9b62c3f"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "exchangeRate", "id": "2056.b183ef89-e434-40f2-8821-a3bb142d4af0"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "selectedIndex", "id": "2056.c0572527-8ba1-46c2-8cff-fccee6caa683"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "calculatedChangeAmnt", "id": "2056.a834fb5c-a2f7-4048-8585-f47e18327f1e"}], "ns16:intermediateThrowEvent": [{"name": "Postpone", "id": "2025.f6eb724e-739a-4b5d-8cb8-5713123f94da", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "383", "y": "283", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}, "ns3:default": "2027.2ac6f51d-476a-4123-86ed-42f63f0310dd"}, "ns16:incoming": "2027.ad77e344-40c4-4ac8-8a2a-b06f39956b46", "ns16:outgoing": "2027.2ac6f51d-476a-4123-86ed-42f63f0310dd", "ns3:postponeTaskEventDefinition": ""}, {"name": "Stay on page", "id": "2025.b3451acc-3e1b-4e4a-85e3-93b142b5fc52", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "644", "y": "258", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.123d8de3-8a5d-4232-89a5-03c3f9854c14", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 1", "id": "2025.fe547349-0a81-4c67-8b32-109fcde936f8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1198", "y": "283", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.e78696d2-ff65-4300-8d1c-deab0f69117a", "ns3:stayOnPageEventDefinition": ""}, {"name": "Copy of Stay on page 1", "id": "2025.c0f85d51-40b7-4479-8444-5b880ad6b61c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1443", "y": "256", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.f4c14674-5505-47b7-8078-2003ae1bb4b6", "ns3:stayOnPageEventDefinition": ""}, {"name": "Stay on page 3", "id": "2025.15f48c73-0753-4b8b-8f39-489b049f18b9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1801", "y": "371", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb", "ns3:stayOnPageEventDefinition": ""}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "default": "2027.bc5a001c-5afc-436b-8302-176b81638311", "name": "Init", "id": "2025.3f741413-b33e-4822-866b-5b54bf5afa54", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "125", "y": "165", "width": "95", "height": "70"}}, "ns16:incoming": "2027.58004e94-2f91-43dc-8e84-62d25ae95ed7", "ns16:outgoing": "2027.bc5a001c-5afc-436b-8302-176b81638311", "ns16:script": "tw.local.actionConditions={};\r\r\ntw.local.actionConditions.complianceApproval=false;\r\r\ntw.local.actionConditions.lastStepAction=tw.local.odcRequest.stepLog.action;\r\r\ntw.local.actionConditions.screenName= tw.epv.Col_ScreenNames.ODCCol02;\r\r\ntw.local.actionConditions.userRole=tw.local.odcRequest.initiator;\r\r\ntw.local.actionConditions.subStatus= tw.local.odcRequest.appInfo.subStatus;\t\r\r\n \r\r\n\r\r\n//---------------------------------Init Step log ----------------------------\t \t \r\r\ntw.local.odcRequest.stepLog={};\r\r\ntw.local.odcRequest.stepLog.startTime = new Date();\r\r\ntw.local.odcRequest.appInfo.stepName =tw.epv.Col_ScreenNames.ODCCol02;\r\r\n \r\r\n tw.local.errorMessageVis =\"NONE\"\r\r\n \r\r\n//tw.local.odcRequest.BasicDetails.requestState = tw.epv.RequestState.Final;"}, {"scriptFormat": "text/x-javascript", "default": "2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c", "name": "Validation", "id": "2025.7107a4cd-2470-4441-8cd4-313006e4821d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "507", "y": "164", "width": "95", "height": "70", "color": "#95D087"}, "ns3:preAssignmentScript": ""}, "ns16:incoming": "2027.5d59231e-874c-46b6-837c-6472e43a5100", "ns16:outgoing": "2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c", "ns16:script": "\t tw.local.errorMessage =\"\";\r\r\n\t var mandatoryTriggered = false;\r\r\n\r\r\n\t/************************************************************************************************************************\r\r\n\t/*-------------------------------------------------- All Section's Validation ------------------------------------------\r\r\n\t************************************************************************************************************************/\r\r\n\t//-------------------------------------------------DC TEMPLATE VALIDATION -------------------------------------------\r\r\n\tmandatory(tw.local.odcRequest.stepLog.action, \"tw.local.odcRequest.stepLog.action\");\r\r\n\t\r\r\n\tif(tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.cancel)\r\r\n\t\tmandatory(tw.local.odcRequest.stepLog.comment, \"tw.local.odcRequest.stepLog.comment\");\r\r\n\t\r\r\n\tif(tw.local.odcRequest.stepLog.action == tw.epv.Col_Actions.returnToMaker)\r\r\n\t\tmandatory(tw.local.odcRequest.stepLog.returnReason, \"tw.local.odcRequest.stepLog.returnReason\");\r\r\n\r\r\n\t/************************************************************************************************************************\r\r\n\t/*-------------------------------------------------- Validation Functions ------------------------------------------\r\r\n\t************************************************************************************************************************/\r\r\n\t/*\r\r\n\t* =========================================================================================================\r\r\n\t*  \r\r\n\t* Add a coach validation error \r\r\n\t* \t\t\r\r\n\t* EX:\taddError(tw.local.name , 'this is a control validation message!' , 'this is a validation message!');\r\r\n\t*\r\r\n\t* =========================================================================================================\r\r\n\t*/\r\r\n\tfunction addError(fieldName , controlMessage , validationMessage , fromMandatory)\r\r\n\t{\r\r\n\t\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\t\tfromMandatory && mandatoryTriggered ? \"\" : tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\";\r\r\n\t}\r\r\n\t/*\r\r\n\t* =================================================================================================================================\r\r\n\t*\r\r\n\t* Add a coach validation error if the string length is less than given length\r\r\n\t*\t\r\r\n\t* EX:\tminLength(tw.local.name , 'tw.local.name' , 10 , 'this is a control validation message!' , 'this is a validation message!')\r\r\n\t*\r\r\n\t* =================================================================================================================================\r\r\n\t*/\r\r\n\r\r\n\tfunction minLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n\t{\r\r\n\t\tif (field != null && field != undefined && field.length < len)\r\r\n\t\t{\r\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\t/*\r\r\n\t* =======================================================================================================================\r\r\n\t*\r\r\n\t* Add a coach validation error if the string length is greater than given length\r\r\n\t*\t\r\r\n\t* EX:\tmaxLength(tw.local.name , 'tw.local.name' , 14 , 'this is a validation message!' , 'this is a validation message!')\r\r\n\t*\r\r\n\t* =======================================================================================================================\r\r\n\t*/\r\r\n\tfunction maxLength(field , fieldName , len , controlMessage , validationMessage)\r\r\n\t{\r\r\n\t\tif (field.length > len)\r\r\n\t\t{\r\r\n\t\t\taddError(fieldName , controlMessage , validationMessage);\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\t/*\r\r\n\t* ==================================================================================================================\r\r\n\t*\r\r\n\t* Add a coach validation error if the field is null 'Mandatory'\r\r\n\t*\t\r\r\n\t* EX:\tnotNull(tw.local.name , 'tw.local.name')\r\r\n\t*\r\r\n\t* ==================================================================================================================\r\r\n\t*/\r\r\nfunction mandatory(field , fieldName)\r\r\n\t{\r\r\n\t\tif (field == null || field == undefined )\r\r\n\t\t{\r\r\n\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\tmandatoryTriggered = true;\r\r\n\t\t\treturn false;\r\r\n\t\t}\r\r\n\t\telse\r\r\n\t\t{\t\t\t\r\r\n\t\t\tswitch (typeof field)\r\r\n\t\t\t{\r\r\n\t\t\t\tcase \"string\":\r\r\n\t\t\t\t\tif (field.trim() != undefined && field.trim() != null && field.trim().length == 0)\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\tcase \"number\":\r\r\n\t\t\t\t\tif (field == 0.0)\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\tcase \"boolean\":\r\r\n\t\t\t\t\tif (field == false)\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t\tbreak;\r\r\n\t\t\t\tdefault:\r\r\n\t\t\t\t\t\r\r\n\t\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\t\tif( field && field.getTime && isFinite(field.getTime()) ) {}\r\r\n\t\t\t\t\t\r\r\n\t\t\t\t\telse\r\r\n\t\t\t\t\t{\r\r\n\t\t\t\t\t\taddError(fieldName , tw.resource.ValidationMessages.ThisFieldIsMandatory , tw.resource.ValidationMessages.MandatoryFields , true);\r\r\n\t\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\t\treturn false;\t\r\r\n\t\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\t \r\r\n\t \r\r\n\ttw.local.errorMessage!=null ?  tw.local.errorMessageVis =\"EDITABLE\": tw.local.errorMessageVis =\"NONE\";\t\t"}], "ns16:exclusiveGateway": [{"default": "2027.2551382a-fc57-4d3d-8f68-c96c81f76825", "name": "Valid?", "id": "2025.6da03458-78da-4bec-8c2a-f95256955724", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "629", "y": "183", "width": "32", "height": "32"}}, "ns16:incoming": "2027.3f38f4ce-6e51-40b2-841f-a3cae41b407c", "ns16:outgoing": ["2027.2551382a-fc57-4d3d-8f68-c96c81f76825", "2027.123d8de3-8a5d-4232-89a5-03c3f9854c14"]}, {"default": "2027.09436183-be9f-450c-8f48-e8b1b6c7bdce", "name": "Check Action", "id": "2025.c4adb6b9-59bf-4080-8273-54fe4fc51fe8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "843", "y": "184", "width": "32", "height": "32"}}, "ns16:incoming": "2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec", "ns16:outgoing": ["2027.7198b3b8-fd84-4587-8164-f19a87a7cc76", "2027.09436183-be9f-450c-8f48-e8b1b6c7bdce"]}, {"default": "2027.e78696d2-ff65-4300-8d1c-deab0f69117a", "name": "audited successfully?", "id": "2025.1684d2a9-2de2-4835-84d9-bc5f1ec0864f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1147", "y": "184", "width": "32", "height": "32"}}, "ns16:incoming": "2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0", "ns16:outgoing": ["2027.74b5fcfd-6654-4818-858d-cdeed73fb2a9", "2027.e78696d2-ff65-4300-8d1c-deab0f69117a"]}, {"default": "2027.f4c14674-5505-47b7-8078-2003ae1bb4b6", "name": "log history?", "id": "2025.380802ed-0adc-4a70-89bb-9e165e4740da", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1376", "y": "182", "width": "32", "height": "32"}}, "ns16:incoming": "2027.26edce82-51c9-4452-800f-c98ec2364e47", "ns16:outgoing": ["2027.9e5105d8-922f-4f9c-8480-6f717a29f2b9", "2027.f4c14674-5505-47b7-8078-2003ae1bb4b6"]}, {"default": "2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68", "gatewayDirection": "Unspecified", "name": "cancel", "id": "2025.a575820a-e008-4e46-8fd8-cd55730132f9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1557", "y": "182", "width": "32", "height": "32"}}, "ns16:incoming": "2027.9e5105d8-922f-4f9c-8480-6f717a29f2b9", "ns16:outgoing": ["2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68", "2027.12f83726-8c19-40cb-8000-1ffdc6217a54"]}, {"default": "2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb", "name": "Exclusive Gateway", "id": "2025.74fe2601-9a09-4a04-8745-8313a6e8e2b7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1846", "y": "258", "width": "32", "height": "32"}}, "ns16:incoming": "2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4", "ns16:outgoing": ["2027.8fd8869c-6940-4680-8971-4adf0eb593dd", "2027.ba4a067b-54b2-41c0-8319-0cfb2a5edfdb"]}], "ns16:callActivity": [{"calledElement": "1.81656d33-5348-479b-a7af-5631356d9476", "default": "2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec", "name": "Set Status And Sub Status", "id": "2025.60ecd941-d556-408c-8f34-0035d98eff47", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "728", "y": "165", "width": "95", "height": "70"}}, "ns16:incoming": "2027.2551382a-fc57-4d3d-8f68-c96c81f76825", "ns16:outgoing": "2027.0ad67d81-2322-4cf8-8cd4-90ca1999c0ec", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.dbd9c712-97dd-4e5b-8b6c-5e9703b06b46", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.stepLog.action", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.6010a917-3b0f-4c13-8973-51c4eede4cd9", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.stepName", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.71a67d77-b802-42cf-8cf2-380a9594a9c2", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.ce1f6bfc-b971-4978-80c8-092f5d40c209", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.40b72dbc-5d84-4b6d-9621-4e738d6838a1", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0", "name": "Audit Collection Data", "id": "2025.77d632ae-1b91-4b25-83fb-225fa1b73ff3", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "997", "y": "165", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "\r\r\ntw.local.odcRequest.BasicDetails.requestState=tw.epv.RequestState.Collection ;\r\r\n\r\r\n"}, "ns16:incoming": "2027.7198b3b8-fd84-4587-8164-f19a87a7cc76", "ns16:outgoing": "2027.5aa9a11f-bb28-4522-82d9-ba4689a11be0", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.e39bfaa6-c863-41f9-8061-0e371dff89cb", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.isLiquidated", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:targetRef": "2055.5d4b901c-324e-4bea-8f10-e160a656c696", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.OdcCollection.amount", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}}}, {"ns16:targetRef": "2055.b51575f2-8ce0-48d0-8179-71d12e0440e7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.OdcCollection.currency", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.84daf689-5f95-458f-8b7f-d8c08459d4c1", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.20995cf3-6a12-4378-8292-51106389c796", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestNature.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.9ff18d23-a67e-4f5d-84b3-f8da533f0f43", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestType.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.96239953-d3b8-4d6d-8d53-1ab12cf361c4", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.requestDate", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}}}, {"ns16:targetRef": "2055.85dca7ee-4057-4dcd-878f-b924dff64190", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.BasicDetails.requestState", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.328377fd-ccc9-4119-80ca-435deb518aee", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.status", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.f26fb6de-b6e6-43e0-89c7-3bba915a357c", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.parentRequestNo", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.37b99722-adca-4c0b-8d6d-aa2eeae29994", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b0be0c94-0742-4365-875f-1b01b63caf0c", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.27a871f0-6893-4366-80d9-133f55bffddb", "ns16:assignment": {"ns16:from": {"_": "null", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.ebcd1729-7d20-4759-81b3-e98e9f554767", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.appInfo.subStatus", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.debd9766-ed8e-45c7-8bbb-c471a2567088", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.8d379594-e94f-4a21-8222-396c4ba9b2e1", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}}, {"calledElement": "1.e8e61c7a-dc6d-441e-b350-b583581efb21", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.26edce82-51c9-4452-800f-c98ec2364e47", "name": "Update History", "id": "2025.cf588907-c740-46ae-8e46-e0ac8e2dd22e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1255", "y": "163", "width": "95", "height": "70"}, "ns3:preAssignmentScript": "tw.local.error={};", "ns3:mode": "InvokeService", "ns3:autoMap": "true"}, "ns16:incoming": "2027.74b5fcfd-6654-4818-858d-cdeed73fb2a9", "ns16:outgoing": "2027.26edce82-51c9-4452-800f-c98ec2364e47", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.0827d2f6-a4a9-4bf3-8b25-e78f0ef06d82", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.ba547af3-d09b-4196-816b-653732f6b226", "ns16:assignment": {"ns16:from": {"_": "tw.epv.userRole.RevACt02", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.416d990b-a0aa-4323-8df7-1f58b014c2ba", "ns16:assignment": {"ns16:to": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:sourceRef": "2055.a617c560-c740-484e-89de-0931088cdc6c", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.191ebde4-e9e8-432a-8e65-4c9dddc8c114", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.5cf7e9f4-d78e-4690-8292-d5e64c209ccc", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4", "name": "cancel request", "id": "2025.96add09d-2cd1-4fc2-83f7-663321c91f5d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1640", "y": "245", "width": "95", "height": "70"}}, "ns16:incoming": "2027.12f83726-8c19-40cb-8000-1ffdc6217a54", "ns16:outgoing": "2027.49d34fdf-4990-498c-8eb5-13dd4d5c0bd4", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.55a76aa1-e513-4fd3-835f-7fe160120af7", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"}}}, {"ns16:targetRef": "2055.77d2b3d6-2a2a-4520-ad08-31686157d431", "ns16:assignment": {"ns16:from": {"_": "tw.local.odcRequest.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:targetRef": "2055.d602a747-fb6e-4103-bbc7-d4f4dffdb689", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.af29bf99-5c8c-456f-86a4-dab9c528f3c0", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, {"ns16:sourceRef": "2055.870b6d47-a51f-4132-8ee7-64b9deb6e00a", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMessage", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:endEvent": {"name": "Copy of End", "id": "2025.e127d662-e51e-4114-848f-7ba1bcf7a31a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1884", "y": "186", "width": "24", "height": "44", "color": "#F8F8F8"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": ["2027.09436183-be9f-450c-8f48-e8b1b6c7bdce", "2027.d88510b5-b5dc-4699-8a8a-c62ffb16ec68", "2027.8fd8869c-6940-4680-8971-4adf0eb593dd"]}, "ns3:htmlHeaderTag": {"id": "ea9bc070-b4d0-45dd-8d23-9626283211a3", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.a1a5e8ad-58e7-4bf7-a37c-b4347ea3d3fd", "epvProcessLinkId": "f1aded2b-52c0-44ac-89dd-8c75c4b4021f"}, {"epvId": "21.e22cd1cb-4788-4edd-beb4-825e8f27f335", "epvProcessLinkId": "cdb0ce7c-6d4a-4cf8-801f-1fa1f72111d4"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "21d97b65-f94e-4986-87c0-d21e05373a57"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "c5f1d454-11b1-4a30-8325-770eed01ff5f"}, {"epvId": "21.062854b5-6513-4da8-84ab-0126f90e550d", "epvProcessLinkId": "0d9ef4f2-7c49-4294-89ab-aa90be3443f7"}, {"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "371c7037-03a5-412f-84c3-a940a9941c7d"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": {"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.aaadf3db-e847-41d9-8daf-6dde3d4fd8f1"}}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.14ffb4e3-3fd1-4a06-8a91-aba3683bb95a"}, {"name": "customerAccounts", "itemSubjectRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isCollection": "true", "id": "2055.113fdf1f-7d01-4484-8e7d-5fa4a82ebad6"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.4b069e0a-b2ef-4fe0-80f3-11b29e505018"}], "ns16:dataOutput": {"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.568ffb88-c172-4fff-841d-2e47908795cb"}, "ns16:inputSet": "", "ns16:outputSet": ""}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ec9bd24a-c33a-4d99-aad1-5506a1007183", "processId": "1.429675b0-3048-4910-8b11-e3d3eb0cd480", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8cfd9241-421e-42f7-b45e-ca2a4ba4484c", "2025.8cfd9241-421e-42f7-b45e-ca2a4ba4484c"], "endStateId": "Out", "toProcessItemId": ["2025.036655af-687f-4584-970c-13e97c9e565b", "2025.036655af-687f-4584-970c-13e97c9e565b"], "guid": "f7c29a95-1fb1-44c4-bf61-50abeac654bd", "versionId": "4782af62-7935-49d7-82a7-5eb2c241e503", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}