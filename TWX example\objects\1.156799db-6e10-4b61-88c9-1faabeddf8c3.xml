<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.156799db-6e10-4b61-88c9-1faabeddf8c3" name="Get ODC charges 2">
        <lastModified>1693306531902</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.f198db16-e27b-4457-8ff3-d17f3816b177</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>103d7996-2c10-459c-a58d-01919ca8021d</guid>
        <versionId>33995fae-589a-4b95-bb3c-8abada244c7e</versionId>
        <dependencySummary>&lt;dependencySummary id="c40070e3-4a5c-45b2-9263-66f8bc913c17" /&gt;</dependencySummary>
        <jsonData isNull="true" />
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="productCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.d9d27843-3752-45c9-b944-bf32ce4a3c09</processParameterId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"OUBC"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0764b328-a657-4a28-b78e-47a6157d0539</guid>
            <versionId>4e5be491-1ed2-4af0-8a8f-481aa16e3b98</versionId>
        </processParameter>
        <processParameter name="event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a4842d90-096c-4c24-8a50-166b2dd0c383</processParameterId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"INIT"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d6cf5463-2db3-463f-9697-f9ca0f09d051</guid>
            <versionId>fec7d728-0852-4b17-81a7-26316607ee74</versionId>
        </processParameter>
        <processParameter name="chargesList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.03e7d166-18c9-4081-a6df-231eef8fc59b</processParameterId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>/12.e3018bad-b453-4bf5-96fd-09a5141cd061</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>53df2a52-54c0-48c8-b018-1d4be051139b</guid>
            <versionId>e0e1940a-d4d0-47c5-b716-6e563f6210aa</versionId>
        </processParameter>
        <processVariable name="chargesAndCommisions">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.6479ba79-02a5-444a-8a57-ecdd606dff2e</processVariableId>
            <description isNull="true" />
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>5eca3d64-26fe-4468-91ab-2b076129e9ca</guid>
            <versionId>0a169136-71ed-42e2-bf71-cc04cd392e4c</versionId>
        </processVariable>
        <processVariable name="currencyList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.b270c145-ac59-4987-bb4f-d7360beb1b7e</processVariableId>
            <description isNull="true" />
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fbaad5b5-ef65-46ea-8aed-7ff1200f2fba</guid>
            <versionId>711acca0-d0c2-47f1-92e7-3c18aa775b90</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a17d288c-b00e-47b8-b6ce-31cbbd36e034</processItemId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.00e317e4-ab0a-4aaf-949e-420b60b9db54</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a40cdf641:-6780</guid>
            <versionId>15db84a2-a492-4969-bea3-0cab1e171b0c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.00e317e4-ab0a-4aaf-949e-420b60b9db54</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>1c2a9a86-080d-4915-aba4-8fe8bf0827ef</guid>
                <versionId>2223d9e7-424b-42b3-95a3-47573276b6e8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f198db16-e27b-4457-8ff3-d17f3816b177</processItemId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <name>MW_FC Retrieve Commission and Charges</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a40cdf641:-6781</guid>
            <versionId>1a42ac0a-2401-4689-aeca-8b69ebb73b31</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="122" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.e059295b-f72a-4e32-a329-8d32ebe941de</attachedProcessRef>
                <guid>c98eb3f7-8f9a-4f47-97d0-f0ac861be9bf</guid>
                <versionId>db3d6435-c4c5-4c51-bf41-c9e0946835c6</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.01b18ab4-cc1e-4eb3-aa76-d0a2d1e13dfc</parameterMappingId>
                    <processParameterId>2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5bc2b378-2afd-4048-be04-8d65d4285e42</guid>
                    <versionId>4a06313b-b53d-43d6-94a0-0034b535427d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="event">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d18f9b9d-e7af-4256-ae20-4e8c1164c562</parameterMappingId>
                    <processParameterId>2055.d8783716-2453-46e3-8522-b1b2504092a2</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.event</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d2b9b8f4-a40a-46cf-9318-8a3e1d426f82</guid>
                    <versionId>5b25e961-693b-4f75-bfc8-3d6f239b47fb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e29c9910-e848-4954-80bd-85dd2ef725ee</parameterMappingId>
                    <processParameterId>2055.78c4796d-3236-45f3-883b-556500834b95</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>3c9b8de2-c79d-4e85-8638-c712ecf95e4e</guid>
                    <versionId>79c2429b-7cda-4526-b827-b5816debf03e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e664c246-75f9-4c1c-a49e-af44f22ff824</parameterMappingId>
                    <processParameterId>2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5cd43a04-8630-4a6e-8d28-c39094d61e1c</guid>
                    <versionId>87003212-6fd8-45ec-b595-98ebbdde8c47</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.83bd4c16-d003-4204-b1a9-6f9549255799</parameterMappingId>
                    <processParameterId>2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>79de3eba-d9a8-4e3b-b430-a2641dc4bb6d</guid>
                    <versionId>8ac7aa84-4df0-45af-9cb5-34633551cf82</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d9e7e24c-e9c6-4019-a648-76a31a8846af</parameterMappingId>
                    <processParameterId>2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>fe89ba7c-497a-47ee-95a5-71bf1902117c</guid>
                    <versionId>9044d9d9-4723-4c4f-91ef-22c29624bd00</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b973f7da-71ef-494b-904a-501142b01d47</parameterMappingId>
                    <processParameterId>2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c66cc514-f837-45b7-adfb-fbee1de83720</guid>
                    <versionId>9b1594ef-5300-4ed5-b1d4-b268407e4333</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0fe65a0b-a14e-489b-978d-88fd747fd5b1</parameterMappingId>
                    <processParameterId>2055.b6919232-c019-43a5-8742-9783cfd63371</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>282c82a5-6e78-474b-9b68-0d44a6c86848</guid>
                    <versionId>9cc92e0a-a954-4100-b64d-b804712308d3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.839934a1-d6e3-410f-9746-8b8af75e8df4</parameterMappingId>
                    <processParameterId>2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d6a3a7d3-8a87-4c34-bd78-bdecfa0d01cc</guid>
                    <versionId>b72a8981-828e-46fb-9d1c-6ad1be714c99</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b51fea27-320c-4d08-8f58-a3ab9d2f57f6</parameterMappingId>
                    <processParameterId>2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>*************-4ed6-ac68-fc73d1084ba8</guid>
                    <versionId>cfee57da-c8d7-44bf-81c5-fa0feb7bef3d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="productCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8f5be2b9-e41b-4c93-b1eb-419fa686a37a</parameterMappingId>
                    <processParameterId>2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.productCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>66d6aeb5-75e2-45a4-a315-842547d0537e</guid>
                    <versionId>d4c33880-527e-4fb5-9cfa-1da22e3f483a</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="chargesAndInterest">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.ca883e0d-44ec-4a4d-a95f-a51c9e980c75</parameterMappingId>
                    <processParameterId>2055.dd74dd40-8fa5-4359-8243-08e144b543d2</processParameterId>
                    <parameterMappingParentId>3012.908dd97e-38f4-4eb8-991c-ae38549dd8f0</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.chargesAndCommisions</value>
                    <classRef>/12.e9f65280-afe9-44dc-9616-f95c2a14629e</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>642d4476-d291-4714-80ab-3e44abdec7ec</guid>
                    <versionId>f00b591e-6635-419a-9a8b-0e8bad512248</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6e111222-2d3c-4700-a79e-b42d921042cc</processItemId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <name>Get currency list</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a40cdf641:-6782</guid>
            <versionId>204d1a8d-a1d6-4c94-994e-ec890378aa16</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="257" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180</subProcessId>
                <attachedProcessRef>/1.2f93c4b5-368e-4a13-aff6-b12926260bb3</attachedProcessRef>
                <guid>51f91e03-dc68-4144-b38d-3c628d31aa0f</guid>
                <versionId>e4b0b713-fee1-4b1f-93d9-220f7703d961</versionId>
                <parameterMapping name="data">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.27e579a4-**************-9f5bf6e2f28d</parameterMappingId>
                    <processParameterId>2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e</processParameterId>
                    <parameterMappingParentId>3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"BPM.IDC_CURRENCY"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>a7e4e694-52f1-4254-a35c-967cc5320d19</guid>
                    <versionId>d9251712-18c5-4cde-9b6a-da8bebee3a1c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f75a7e28-65e7-4041-a961-7d663c54efd5</parameterMappingId>
                    <processParameterId>2055.91436cf4-7a08-4359-8624-bbfa8c1147c0</processParameterId>
                    <parameterMappingParentId>3012.ece0d0d0-dab9-47e7-9dbd-d2ab214b6180</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.currencyList</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>b19289cc-3e3e-4dc2-8ce3-0fe09e975528</guid>
                    <versionId>ed63ba78-3aec-44fc-a7b9-73d405fe0e48</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.16945213-3ed7-41f0-96b3-de59196f3eea</processItemId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.9bf5e2eb-7e6b-400a-a14a-a53545ae86e4</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a40cdf641:-6783</guid>
            <versionId>6c00ec3e-9af0-4b61-bba8-470d617abf53</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="391" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.9bf5e2eb-7e6b-400a-a14a-a53545ae86e4</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();&#xD;
if(tw.local.chargesAndCommisions != null)&#xD;
{&#xD;
	for(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == "charge")&#xD;
		{&#xD;
			tw.local.chargesList[i] = new tw.object.ChargesAndCommissions();&#xD;
//			tw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			tw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;&#xD;
			tw.local.chargesList[i].basicAmountCurrency = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
&#xD;
			for(var x=0;x&lt;tw.local.currencyList.listLength;x++)&#xD;
			{&#xD;
				if(tw.local.chargesAndCommisions[i].basisAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())&#xD;
				{&#xD;
					tw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;&#xD;
					tw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;&#xD;
					break;&#xD;
				}&#xD;
			}&#xD;
			//tw.local.chargesList[i].defaultCurrency.name = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;&#xD;
			tw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.value = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.currency.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency.value = "";&#xD;
			tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].rateType.toLowerCase() == "Flat Amount")&#xD;
			{&#xD;
				tw.local.chargesList[i].flatAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);&#xD;
				tw.local.chargesList[i].defaultAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);//added by sg&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].defaultPercentage = parseFloat(tw.local.chargesAndCommisions[i].rate);&#xD;
//				tw.local.chargesList[i].minimumAmount = parseFloat(tw.local.chargesAndCommisions[i].minAmount);&#xD;
			}&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = false;		&#xD;
			}&#xD;
&#xD;
		}&#xD;
				&#xD;
	}&#xD;
}</script>
                <isRule>false</isRule>
                <guid>ea4cfd66-7e7a-4c82-a3ae-9b3b4074b438</guid>
                <versionId>121f0bc5-7987-46c5-9855-dc78d68fdbf5</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.f198db16-e27b-4457-8ff3-d17f3816b177</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get ODC charges 2" id="1.156799db-6e10-4b61-88c9-1faabeddf8c3" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:localizationResourceLinks />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="productCode" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.d9d27843-3752-45c9-b944-bf32ce4a3c09">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"OUBC"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="event" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a4842d90-096c-4c24-8a50-166b2dd0c383">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"INIT"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="chargesList" itemSubjectRef="itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061" isCollection="true" id="2055.03e7d166-18c9-4081-a6df-231eef8fc59b" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.d9d27843-3752-45c9-b944-bf32ce4a3c09</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.a4842d90-096c-4c24-8a50-166b2dd0c383</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.03e7d166-18c9-4081-a6df-231eef8fc59b</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="0f528387-b112-4312-bd59-de7a5c5e376a">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="61d5b9a8-ca40-43d7-b57e-af88bc3b9cce" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>9399e6fa-c175-4250-8801-977a0a63176d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a17d288c-b00e-47b8-b6ce-31cbbd36e034</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f198db16-e27b-4457-8ff3-d17f3816b177</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>16945213-3ed7-41f0-96b3-de59196f3eea</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6e111222-2d3c-4700-a79e-b42d921042cc</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="9399e6fa-c175-4250-8801-977a0a63176d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.931f06d5-aa73-4501-9fba-64a012bf0d11</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="a17d288c-b00e-47b8-b6ce-31cbbd36e034">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a40cdf641:-6780</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>68db8231-6b71-406d-a596-569b13ba6c96</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="9399e6fa-c175-4250-8801-977a0a63176d" targetRef="f198db16-e27b-4457-8ff3-d17f3816b177" name="To MW_FC Retrieve Commission and Charges" id="2027.931f06d5-aa73-4501-9fba-64a012bf0d11">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.e059295b-f72a-4e32-a329-8d32ebe941de" isForCompensation="false" startQuantity="1" completionQuantity="1" name="MW_FC Retrieve Commission and Charges" id="f198db16-e27b-4457-8ff3-d17f3816b177">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="122" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.931f06d5-aa73-4501-9fba-64a012bf0d11</ns16:incoming>
                        
                        
                        <ns16:outgoing>e18f3b9d-1afc-47f9-b5b7-79a315c4131c</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.productCode</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d8783716-2453-46e3-8522-b1b2504092a2</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.event</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.dd74dd40-8fa5-4359-8243-08e144b543d2</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e">tw.local.chargesAndCommisions</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="f198db16-e27b-4457-8ff3-d17f3816b177" targetRef="6e111222-2d3c-4700-a79e-b42d921042cc" name="To Get currency list" id="e18f3b9d-1afc-47f9-b5b7-79a315c4131c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="16945213-3ed7-41f0-96b3-de59196f3eea">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="391" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>3c4e6606-a6ca-40b4-ab18-ea14ab215d6d</ns16:incoming>
                        
                        
                        <ns16:outgoing>68db8231-6b71-406d-a596-569b13ba6c96</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.chargesList = new tw.object.listOf.ChargesAndCommissions();&#xD;
if(tw.local.chargesAndCommisions != null)&#xD;
{&#xD;
	for(var i=0; i&lt;tw.local.chargesAndCommisions.listLength;i++)&#xD;
	{&#xD;
		if(tw.local.chargesAndCommisions[i].ruleType.toLowerCase() == "charge")&#xD;
		{&#xD;
			tw.local.chargesList[i] = new tw.object.ChargesAndCommissions();&#xD;
//			tw.local.chargesList[i].changeAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			tw.local.chargesList[i].component = tw.local.chargesAndCommisions[i].component;&#xD;
			tw.local.chargesList[i].basicAmountCurrency = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].defaultCurrency = new tw.object.NameValuePair();&#xD;
&#xD;
			for(var x=0;x&lt;tw.local.currencyList.listLength;x++)&#xD;
			{&#xD;
				if(tw.local.chargesAndCommisions[i].basisAmountCurrency.toLowerCase() == tw.local.currencyList[x].name.toLowerCase())&#xD;
				{&#xD;
					tw.local.chargesList[i].defaultCurrency.value = tw.local.currencyList[x].value;&#xD;
					tw.local.chargesList[i].defaultCurrency.name = tw.local.currencyList[x].name;&#xD;
					break;&#xD;
				}&#xD;
			}&#xD;
			//tw.local.chargesList[i].defaultCurrency.name = tw.local.chargesAndCommisions[i].basisAmountCurrency;&#xD;
			tw.local.chargesList[i].rateType = tw.local.chargesAndCommisions[i].rateType;&#xD;
			tw.local.chargesList[i].debitedAccount = new tw.object.AccountDetails();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.accountClass.value = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency = new tw.object.NameValuePair();&#xD;
			tw.local.chargesList[i].debitedAccount.currency.name = "";&#xD;
			tw.local.chargesList[i].debitedAccount.currency.value = "";&#xD;
			tw.local.chargesList[i].defaultAmount = tw.local.chargesAndCommisions[i].defaultAmount;&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].rateType.toLowerCase() == "Flat Amount")&#xD;
			{&#xD;
				tw.local.chargesList[i].flatAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);&#xD;
				tw.local.chargesList[i].defaultAmount = parseFloat(tw.local.chargesAndCommisions[i].flatAmount);//added by sg&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].defaultPercentage = parseFloat(tw.local.chargesAndCommisions[i].rate);&#xD;
//				tw.local.chargesList[i].minimumAmount = parseFloat(tw.local.chargesAndCommisions[i].minAmount);&#xD;
			}&#xD;
			&#xD;
			if(tw.local.chargesAndCommisions[i].defaultWaived.toLowerCase() == "y")&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = true;&#xD;
			}&#xD;
			else&#xD;
			{&#xD;
				tw.local.chargesList[i].waiver = false;		&#xD;
			}&#xD;
&#xD;
		}&#xD;
				&#xD;
	}&#xD;
}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="16945213-3ed7-41f0-96b3-de59196f3eea" targetRef="a17d288c-b00e-47b8-b6ce-31cbbd36e034" name="To End" id="68db8231-6b71-406d-a596-569b13ba6c96">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e" isCollection="true" name="chargesAndCommisions" id="2056.6479ba79-02a5-444a-8a57-ecdd606dff2e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13" isCollection="true" name="currencyList" id="2056.b270c145-ac59-4987-bb4f-d7360beb1b7e" />
                    
                    
                    <ns16:callActivity calledElement="1.2f93c4b5-368e-4a13-aff6-b12926260bb3" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get currency list" id="6e111222-2d3c-4700-a79e-b42d921042cc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="257" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e18f3b9d-1afc-47f9-b5b7-79a315c4131c</ns16:incoming>
                        
                        
                        <ns16:outgoing>3c4e6606-a6ca-40b4-ab18-ea14ab215d6d</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.d6efaaf1-9218-4d3b-8035-8769dd533b7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"BPM.IDC_CURRENCY"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.91436cf4-7a08-4359-8624-bbfa8c1147c0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13">tw.local.currencyList</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6e111222-2d3c-4700-a79e-b42d921042cc" targetRef="16945213-3ed7-41f0-96b3-de59196f3eea" name="To Map output" id="3c4e6606-a6ca-40b4-ab18-ea14ab215d6d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.50eb924d-e6dd-476c-a03d-cca0a7f91ba6</processLinkId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.16945213-3ed7-41f0-96b3-de59196f3eea</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.a17d288c-b00e-47b8-b6ce-31cbbd36e034</toProcessItemId>
            <guid>83f80a0a-0d69-4164-a636-61c8092907a7</guid>
            <versionId>2088d9dc-24ed-4a8a-b710-7e35f1ed3953</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.16945213-3ed7-41f0-96b3-de59196f3eea</fromProcessItemId>
            <toProcessItemId>2025.a17d288c-b00e-47b8-b6ce-31cbbd36e034</toProcessItemId>
        </link>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c5cdf520-c688-4e72-9107-781b7125a0fc</processLinkId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6e111222-2d3c-4700-a79e-b42d921042cc</fromProcessItemId>
            <endStateId>guid:d7a662a99ec79f4c:-60dc945f:189bab22bee:1a24</endStateId>
            <toProcessItemId>2025.16945213-3ed7-41f0-96b3-de59196f3eea</toProcessItemId>
            <guid>0565deda-755b-48fc-a935-a1ce4ede572b</guid>
            <versionId>928e3ba0-c548-48b7-bea7-d42fe23f4cb8</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6e111222-2d3c-4700-a79e-b42d921042cc</fromProcessItemId>
            <toProcessItemId>2025.16945213-3ed7-41f0-96b3-de59196f3eea</toProcessItemId>
        </link>
        <link name="To Get currency list">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.30ed345b-e509-432e-a90e-0ec87ab655b6</processLinkId>
            <processId>1.156799db-6e10-4b61-88c9-1faabeddf8c3</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f198db16-e27b-4457-8ff3-d17f3816b177</fromProcessItemId>
            <endStateId>guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20</endStateId>
            <toProcessItemId>2025.6e111222-2d3c-4700-a79e-b42d921042cc</toProcessItemId>
            <guid>8fb422f6-1a51-444b-9ffa-08e36a39a60b</guid>
            <versionId>b040f090-2bf0-4baa-9bd5-2d3432b33e60</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f198db16-e27b-4457-8ff3-d17f3816b177</fromProcessItemId>
            <toProcessItemId>2025.6e111222-2d3c-4700-a79e-b42d921042cc</toProcessItemId>
        </link>
    </process>
</teamworks>

