<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2d3ab562-82df-48a5-9de7-f5d964218191" name="Set document default properties">
        <lastModified>1699559777198</lastModified>
        <lastModifiedBy>somaia</lastModifiedBy>
        <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.020101e9-7d22-4a20-8448-13d64cf7754b</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
        <errorHandlerItemId>2025.0f071bb0-**************-ea2c02b3b2b1</errorHandlerItemId>
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:415d794a2c221205:3dfd662b:18a3676f5a2:57f5</guid>
        <versionId>e1c75fd3-f8b2-4779-a4f1-4aecf972a0bc</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:a6be0630e884ccca:-427a0b52:18bb428309d:-38c8" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.aa92f9b7-f374-4063-8507-a8f92b4bfdfa"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":90,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"8c8aec2a-5fd3-4916-8d0d-3a9c148a07ac"},{"incoming":["10166f91-9ae9-4f8c-8b33-05c32be217b4"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:415d794a2c221205:3dfd662b:18a3676f5a2:57f7"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"611ca7d1-29f9-4eb8-893a-a2a3686c84b1"},{"targetRef":"020101e9-7d22-4a20-8448-13d64cf7754b","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Set ECM default properties","declaredType":"sequenceFlow","id":"2027.aa92f9b7-f374-4063-8507-a8f92b4bfdfa","sourceRef":"8c8aec2a-5fd3-4916-8d0d-3a9c148a07ac"},{"startQuantity":1,"outgoing":["10166f91-9ae9-4f8c-8b33-05c32be217b4"],"incoming":["2027.aa92f9b7-f374-4063-8507-a8f92b4bfdfa"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":266,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set ECM default properties","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"020101e9-7d22-4a20-8448-13d64cf7754b","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.ecmProperties = new tw.object.listOf.ECMDefaultProperty();\r\n\r\ntw.local.ecmProperties[0] = new tw.object.ECMDefaultProperty();\r\ntw.local.ecmProperties[0].name = \"Customer_Number_CIF\";\r\ntw.local.ecmProperties[0].hidden = true;\r\ntw.local.ecmProperties[0].value = tw.local.cif;\r\ntw.local.ecmProperties[0].editable = false;\r\n\r\n\r\ntw.local.ecmProperties[1] = new tw.object.ECMDefaultProperty();\r\ntw.local.ecmProperties[1].name = \"BPM_Case_ID\";\r\ntw.local.ecmProperties[1].value = tw.local.requestNumber;\r\ntw.local.ecmProperties[1].editable = false;\r\ntw.local.ecmProperties[1].hidden = false;\r\n\r\ntw.local.ecmProperties[2] = new tw.object.ECMDefaultProperty();\r\ntw.local.ecmProperties[2].name = \"channel\";\r\ntw.local.ecmProperties[2].value = \"BPM\";\r\ntw.local.ecmProperties[2].editable = false;\r\ntw.local.ecmProperties[2].hidden = true;\r\n\r\ntw.local.ecmProperties[3] = new tw.object.ECMDefaultProperty();\r\ntw.local.ecmProperties[3].name = \"Status\";\r\ntw.local.ecmProperties[3].value = \"Not Approved\";\r\ntw.local.ecmProperties[3].editable = false;\r\ntw.local.ecmProperties[3].hidden = true;\r\n\r\n\/\/tw.local.ECMDocumentProperties[4] = new tw.object.ECMDefaultProperty();\r\n\/\/tw.local.ECMDocumentProperties[4].name = \"Edit_Date\";\r\n\/\/tw.local.ECMDocumentProperties[4].value = new Date();\r\n\/\/tw.local.ECMDocumentProperties[4].editable = false;\r\n\/\/tw.local.ECMDocumentProperties[4].hidden = false;\r\n\r\ntw.local.ecmProperties[4] = new tw.object.ECMDefaultProperty();\r\ntw.local.ecmProperties[4].name = \"Customer_Name\";\r\ntw.local.ecmProperties[4].value = tw.local.customerName;\r\ntw.local.ecmProperties[4].editable = false;\r\ntw.local.ecmProperties[4].hidden = false;\r\n\r\ntw.local.ecmProperties[5] = new tw.object.ECMDefaultProperty();\r\ntw.local.ecmProperties[5].name = \"TaskID\";\r\ntw.local.ecmProperties[5].value = tw.local.taskID;\r\ntw.local.ecmProperties[5].editable = false;\r\ntw.local.ecmProperties[5].hidden = true;\r\n\r\ntw.local.ecmProperties[6] = new tw.object.ECMDefaultProperty();\r\ntw.local.ecmProperties[6].name = \"UserId\";\r\ntw.local.ecmProperties[6].value = tw.local.userID;\r\ntw.local.ecmProperties[6].editable = false;\r\ntw.local.ecmProperties[6].hidden = true;\r\n"]}},{"targetRef":"611ca7d1-29f9-4eb8-893a-a2a3686c84b1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"10166f91-9ae9-4f8c-8b33-05c32be217b4","sourceRef":"020101e9-7d22-4a20-8448-13d64cf7754b"},{"parallelMultiple":false,"outgoing":["a516632b-2d14-4d59-8fa0-9c2fda573ad1"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"eee04804-687d-47bd-8c8e-9db4b3bb3f0e","otherAttributes":{"eventImplId":"17c65ff1-7098-4da9-8c69-b91638ad2548"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":182,"y":193,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error Event","declaredType":"intermediateCatchEvent","id":"6d109973-14e5-4a82-89c6-60c01fd87625"},{"incoming":["500ec071-ba02-4dc1-8de6-da0f1abf1a9a"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"f9b7ebc4-809e-497d-832e-ad54f629b74b","otherAttributes":{"eventImplId":"ccc8dfd3-b0fe-4514-874e-3dd671f0daac"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":496,"y":194,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"22d02988-145f-4c8e-8286-31680eafa330"},{"startQuantity":1,"outgoing":["500ec071-ba02-4dc1-8de6-da0f1abf1a9a"],"incoming":["a516632b-2d14-4d59-8fa0-9c2fda573ad1"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":312,"y":170,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Set document default property\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"0f071bb0-**************-ea2c02b3b2b1","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"targetRef":"0f071bb0-**************-ea2c02b3b2b1","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"a516632b-2d14-4d59-8fa0-9c2fda573ad1","sourceRef":"6d109973-14e5-4a82-89c6-60c01fd87625"},{"targetRef":"22d02988-145f-4c8e-8286-31680eafa330","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"500ec071-ba02-4dc1-8de6-da0f1abf1a9a","sourceRef":"0f071bb0-**************-ea2c02b3b2b1"}],"laneSet":[{"id":"97dcfab1-1e7d-4d44-88c7-3fa23a29fcf9","lane":[{"flowNodeRef":["8c8aec2a-5fd3-4916-8d0d-3a9c148a07ac","611ca7d1-29f9-4eb8-893a-a2a3686c84b1","020101e9-7d22-4a20-8448-13d64cf7754b","6d109973-14e5-4a82-89c6-60c01fd87625","22d02988-145f-4c8e-8286-31680eafa330","0f071bb0-**************-ea2c02b3b2b1"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"155ee4ef-6be8-4289-81b8-9d7e58231be4","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Set document default properties","declaredType":"process","id":"1.2d3ab562-82df-48a5-9de7-f5d964218191","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.eac829db-66fe-43f5-810c-6faa514533a2","name":"ecmProperties","isCollection":true,"id":"2055.81cce609-b3fe-4f11-809f-c3a599908595"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.a1997cf3-423a-4552-89fc-************"}],"inputSet":[{"dataInputRefs":["2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f","2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701","2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b","2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d","2055.6f216377-34de-4f5e-8ab5-adc2796733ee"]}],"outputSet":[{"dataOutputRefs":["2055.81cce609-b3fe-4f11-809f-c3a599908595","2055.a1997cf3-423a-4552-89fc-************"]}],"dataInput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"cif","isCollection":false,"id":"2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNumber","isCollection":false,"id":"2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskID","isCollection":false,"id":"2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"userID","isCollection":false,"id":"2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"customerName","isCollection":false,"id":"2055.6f216377-34de-4f5e-8ab5-adc2796733ee"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="cif">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>85485b17-45e3-460c-b8b2-17ad0c793bf3</guid>
            <versionId>8da37fda-7b38-4ce9-b576-799e9c75778b</versionId>
        </processParameter>
        <processParameter name="requestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f3bbf542-3b24-46e9-bb17-6c4252620d5a</guid>
            <versionId>e001931e-5ecb-44a4-93ed-5ed1f6577770</versionId>
        </processParameter>
        <processParameter name="taskID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>bdb463a6-0354-4283-836d-1bd65c6f2ddd</guid>
            <versionId>d362dda5-50f0-42f8-9719-31253d3e2596</versionId>
        </processParameter>
        <processParameter name="userID">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>fc7e0990-3fe7-4dac-b835-d0fe7f57fba9</guid>
            <versionId>539de465-9778-48f3-996f-b2d3088acdf2</versionId>
        </processParameter>
        <processParameter name="customerName">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.6f216377-34de-4f5e-8ab5-adc2796733ee</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>46103e5a-b033-443e-8b82-21ddf995db0b</guid>
            <versionId>608354ba-e34b-4ff7-87af-8241f7f910d1</versionId>
        </processParameter>
        <processParameter name="ecmProperties">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.81cce609-b3fe-4f11-809f-c3a599908595</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.eac829db-66fe-43f5-810c-6faa514533a2</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>262f59f2-67dc-4389-b8b8-959d02ec5b69</guid>
            <versionId>dd1dfda0-081b-4959-b29c-21a4628b77c8</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.23b0d31b-ed3b-4648-8897-7f13c1f394e8</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>793da1f4-b2ac-476b-ae16-21d827ba75ca</guid>
            <versionId>0764ec50-b857-451c-bbb9-9e077a6e8bd1</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a1997cf3-423a-4552-89fc-************</processParameterId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9de7b052-62e0-40f7-bd65-c7784de6667e</guid>
            <versionId>1212d6b4-515f-4ddd-b264-adc9bb4c9081</versionId>
        </processParameter>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0f071bb0-**************-ea2c02b3b2b1</processItemId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b72b60a7-c071-4bef-b44d-a485d2c983cd</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:-22bb</guid>
            <versionId>142fef5d-2051-4278-9e83-03be3cdb8802</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="312" y="170">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b72b60a7-c071-4bef-b44d-a485d2c983cd</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>a773edbc-6acf-447a-ab56-e209497a7038</guid>
                <versionId>ef482a48-9f1b-4308-95b1-311aac4f5db7</versionId>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.10845865-12fd-49c0-b0c5-129042ce9da9</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.b72b60a7-c071-4bef-b44d-a485d2c983cd</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>71fb6a91-5feb-4332-be14-2ece682d12c5</guid>
                    <versionId>109b367e-451d-4656-8ac3-2f3f9ee92711</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.56b2484b-cc33-40b2-84d8-e391b883a892</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.b72b60a7-c071-4bef-b44d-a485d2c983cd</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>80054188-e90c-4d84-aaa2-e50a1b6683aa</guid>
                    <versionId>9a3659fd-c9af-41b9-99aa-f9049d394244</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.717b9efe-cd4b-4502-b847-ad6d3293403f</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.b72b60a7-c071-4bef-b44d-a485d2c983cd</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Set document default property"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b93da6e3-0b1b-421d-ae16-8769241e2968</guid>
                    <versionId>f9af178c-372e-4236-8444-8d884109234f</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.22d02988-145f-4c8e-8286-31680eafa330</processItemId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.9f7c0f3d-e788-4e80-bfde-8697665f422e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:651a1a6abf396537:64776e00:18baeba64af:-22ba</guid>
            <versionId>61a27a67-e3db-46af-a6d0-24ee60c2b7fa</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="496" y="194">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.9f7c0f3d-e788-4e80-bfde-8697665f422e</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>6e2d9d1d-f6f9-4af2-aef7-f62bc8c865dd</guid>
                <versionId>88892606-c125-453d-bacc-e4bbdf3ffbe2</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f7db0540-2565-4d6d-9309-94e1c012f16b</parameterMappingId>
                    <processParameterId>2055.23b0d31b-ed3b-4648-8897-7f13c1f394e8</processParameterId>
                    <parameterMappingParentId>3007.9f7c0f3d-e788-4e80-bfde-8697665f422e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>24fc1222-1d6d-4c1d-a9b6-e1224c60eddc</guid>
                    <versionId>e51cefed-5671-4a07-87bb-eec62a01995f</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.020101e9-7d22-4a20-8448-13d64cf7754b</processItemId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <name>Set ECM default properties</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.6c071319-e60d-4e8d-906f-0907a9e3a6d0</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a3676f5a2:5800</guid>
            <versionId>93a3a7bb-a507-4fb5-86fc-1d2f7a83d8b2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="266" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.6c071319-e60d-4e8d-906f-0907a9e3a6d0</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.ecmProperties = new tw.object.listOf.ECMDefaultProperty();&#xD;
&#xD;
tw.local.ecmProperties[0] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[0].name = "Customer_Number_CIF";&#xD;
tw.local.ecmProperties[0].hidden = true;&#xD;
tw.local.ecmProperties[0].value = tw.local.cif;&#xD;
tw.local.ecmProperties[0].editable = false;&#xD;
&#xD;
&#xD;
tw.local.ecmProperties[1] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[1].name = "BPM_Case_ID";&#xD;
tw.local.ecmProperties[1].value = tw.local.requestNumber;&#xD;
tw.local.ecmProperties[1].editable = false;&#xD;
tw.local.ecmProperties[1].hidden = false;&#xD;
&#xD;
tw.local.ecmProperties[2] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[2].name = "channel";&#xD;
tw.local.ecmProperties[2].value = "BPM";&#xD;
tw.local.ecmProperties[2].editable = false;&#xD;
tw.local.ecmProperties[2].hidden = true;&#xD;
&#xD;
tw.local.ecmProperties[3] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[3].name = "Status";&#xD;
tw.local.ecmProperties[3].value = "Not Approved";&#xD;
tw.local.ecmProperties[3].editable = false;&#xD;
tw.local.ecmProperties[3].hidden = true;&#xD;
&#xD;
//tw.local.ECMDocumentProperties[4] = new tw.object.ECMDefaultProperty();&#xD;
//tw.local.ECMDocumentProperties[4].name = "Edit_Date";&#xD;
//tw.local.ECMDocumentProperties[4].value = new Date();&#xD;
//tw.local.ECMDocumentProperties[4].editable = false;&#xD;
//tw.local.ECMDocumentProperties[4].hidden = false;&#xD;
&#xD;
tw.local.ecmProperties[4] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[4].name = "Customer_Name";&#xD;
tw.local.ecmProperties[4].value = tw.local.customerName;&#xD;
tw.local.ecmProperties[4].editable = false;&#xD;
tw.local.ecmProperties[4].hidden = false;&#xD;
&#xD;
tw.local.ecmProperties[5] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[5].name = "TaskID";&#xD;
tw.local.ecmProperties[5].value = tw.local.taskID;&#xD;
tw.local.ecmProperties[5].editable = false;&#xD;
tw.local.ecmProperties[5].hidden = true;&#xD;
&#xD;
tw.local.ecmProperties[6] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[6].name = "UserId";&#xD;
tw.local.ecmProperties[6].value = tw.local.userID;&#xD;
tw.local.ecmProperties[6].editable = false;&#xD;
tw.local.ecmProperties[6].hidden = true;&#xD;
</script>
                <isRule>false</isRule>
                <guid>2ba13174-e636-4f02-ba46-fe98cfd1fc73</guid>
                <versionId>1fc08d55-e29a-49bd-8e9c-25de400bc391</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.611ca7d1-29f9-4eb8-893a-a2a3686c84b1</processItemId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.026b7d05-2638-4b89-bd57-a863dd5bf80f</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:415d794a2c221205:3dfd662b:18a3676f5a2:57f7</guid>
            <versionId>f3ca8278-190e-4a9f-acf1-956324e3457e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.026b7d05-2638-4b89-bd57-a863dd5bf80f</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>0791e2c7-7c2d-49fd-ab0f-4c97e8567da4</guid>
                <versionId>84677a74-f3e0-4637-9ceb-b257dceb0a1b</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.020101e9-7d22-4a20-8448-13d64cf7754b</startingProcessItemId>
        <errorHandlerItemId>2025.0f071bb0-**************-ea2c02b3b2b1</errorHandlerItemId>
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="90">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <globalExceptionHandler>
            <layoutData x="182" y="193">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </globalExceptionHandler>
        <globalExceptionLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </globalExceptionLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Set document default properties" id="1.2d3ab562-82df-48a5-9de7-f5d964218191" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="cif" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f" />
                        
                        
                        <ns16:dataInput name="requestNumber" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701" />
                        
                        
                        <ns16:dataInput name="taskID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b" />
                        
                        
                        <ns16:dataInput name="userID" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d" />
                        
                        
                        <ns16:dataInput name="customerName" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.6f216377-34de-4f5e-8ab5-adc2796733ee" />
                        
                        
                        <ns16:dataOutput name="ecmProperties" itemSubjectRef="itm.12.eac829db-66fe-43f5-810c-6faa514533a2" isCollection="true" id="2055.81cce609-b3fe-4f11-809f-c3a599908595" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.a1997cf3-423a-4552-89fc-************" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.af77d84b-4e00-4ffc-8b40-e8143ab7173f</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.b0795b9e-56c2-4a04-8ccb-48947ab1c701</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.5e2e0fcc-e43a-455f-8977-0dc6b0ba581b</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.63151b8e-7a7e-4f1e-8c93-194b585dd93d</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.6f216377-34de-4f5e-8ab5-adc2796733ee</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.81cce609-b3fe-4f11-809f-c3a599908595</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.a1997cf3-423a-4552-89fc-************</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="97dcfab1-1e7d-4d44-88c7-3fa23a29fcf9">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="155ee4ef-6be8-4289-81b8-9d7e58231be4" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>8c8aec2a-5fd3-4916-8d0d-3a9c148a07ac</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>611ca7d1-29f9-4eb8-893a-a2a3686c84b1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>020101e9-7d22-4a20-8448-13d64cf7754b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6d109973-14e5-4a82-89c6-60c01fd87625</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>22d02988-145f-4c8e-8286-31680eafa330</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0f071bb0-**************-ea2c02b3b2b1</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="8c8aec2a-5fd3-4916-8d0d-3a9c148a07ac">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="90" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.aa92f9b7-f374-4063-8507-a8f92b4bfdfa</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="611ca7d1-29f9-4eb8-893a-a2a3686c84b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:415d794a2c221205:3dfd662b:18a3676f5a2:57f7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>10166f91-9ae9-4f8c-8b33-05c32be217b4</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="8c8aec2a-5fd3-4916-8d0d-3a9c148a07ac" targetRef="020101e9-7d22-4a20-8448-13d64cf7754b" name="To Set ECM default properties" id="2027.aa92f9b7-f374-4063-8507-a8f92b4bfdfa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set ECM default properties" id="020101e9-7d22-4a20-8448-13d64cf7754b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="266" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.aa92f9b7-f374-4063-8507-a8f92b4bfdfa</ns16:incoming>
                        
                        
                        <ns16:outgoing>10166f91-9ae9-4f8c-8b33-05c32be217b4</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.ecmProperties = new tw.object.listOf.ECMDefaultProperty();&#xD;
&#xD;
tw.local.ecmProperties[0] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[0].name = "Customer_Number_CIF";&#xD;
tw.local.ecmProperties[0].hidden = true;&#xD;
tw.local.ecmProperties[0].value = tw.local.cif;&#xD;
tw.local.ecmProperties[0].editable = false;&#xD;
&#xD;
&#xD;
tw.local.ecmProperties[1] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[1].name = "BPM_Case_ID";&#xD;
tw.local.ecmProperties[1].value = tw.local.requestNumber;&#xD;
tw.local.ecmProperties[1].editable = false;&#xD;
tw.local.ecmProperties[1].hidden = false;&#xD;
&#xD;
tw.local.ecmProperties[2] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[2].name = "channel";&#xD;
tw.local.ecmProperties[2].value = "BPM";&#xD;
tw.local.ecmProperties[2].editable = false;&#xD;
tw.local.ecmProperties[2].hidden = true;&#xD;
&#xD;
tw.local.ecmProperties[3] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[3].name = "Status";&#xD;
tw.local.ecmProperties[3].value = "Not Approved";&#xD;
tw.local.ecmProperties[3].editable = false;&#xD;
tw.local.ecmProperties[3].hidden = true;&#xD;
&#xD;
//tw.local.ECMDocumentProperties[4] = new tw.object.ECMDefaultProperty();&#xD;
//tw.local.ECMDocumentProperties[4].name = "Edit_Date";&#xD;
//tw.local.ECMDocumentProperties[4].value = new Date();&#xD;
//tw.local.ECMDocumentProperties[4].editable = false;&#xD;
//tw.local.ECMDocumentProperties[4].hidden = false;&#xD;
&#xD;
tw.local.ecmProperties[4] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[4].name = "Customer_Name";&#xD;
tw.local.ecmProperties[4].value = tw.local.customerName;&#xD;
tw.local.ecmProperties[4].editable = false;&#xD;
tw.local.ecmProperties[4].hidden = false;&#xD;
&#xD;
tw.local.ecmProperties[5] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[5].name = "TaskID";&#xD;
tw.local.ecmProperties[5].value = tw.local.taskID;&#xD;
tw.local.ecmProperties[5].editable = false;&#xD;
tw.local.ecmProperties[5].hidden = true;&#xD;
&#xD;
tw.local.ecmProperties[6] = new tw.object.ECMDefaultProperty();&#xD;
tw.local.ecmProperties[6].name = "UserId";&#xD;
tw.local.ecmProperties[6].value = tw.local.userID;&#xD;
tw.local.ecmProperties[6].editable = false;&#xD;
tw.local.ecmProperties[6].hidden = true;&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="020101e9-7d22-4a20-8448-13d64cf7754b" targetRef="611ca7d1-29f9-4eb8-893a-a2a3686c84b1" name="To End" id="10166f91-9ae9-4f8c-8b33-05c32be217b4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:intermediateCatchEvent name="Error Event" id="6d109973-14e5-4a82-89c6-60c01fd87625">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="182" y="193" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>a516632b-2d14-4d59-8fa0-9c2fda573ad1</ns16:outgoing>
                        
                        
                        <ns16:errorEventDefinition id="eee04804-687d-47bd-8c8e-9db4b3bb3f0e" eventImplId="17c65ff1-7098-4da9-8c69-b91638ad2548">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:intermediateCatchEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="22d02988-145f-4c8e-8286-31680eafa330">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="496" y="194" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>500ec071-ba02-4dc1-8de6-da0f1abf1a9a</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="f9b7ebc4-809e-497d-832e-ad54f629b74b" eventImplId="ccc8dfd3-b0fe-4514-874e-3dd671f0daac">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" name="Exception Handling" id="0f071bb0-**************-ea2c02b3b2b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="312" y="170" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a516632b-2d14-4d59-8fa0-9c2fda573ad1</ns16:incoming>
                        
                        
                        <ns16:outgoing>500ec071-ba02-4dc1-8de6-da0f1abf1a9a</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Set document default property"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="6d109973-14e5-4a82-89c6-60c01fd87625" targetRef="0f071bb0-**************-ea2c02b3b2b1" name="To Exception Handling" id="a516632b-2d14-4d59-8fa0-9c2fda573ad1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="0f071bb0-**************-ea2c02b3b2b1" targetRef="22d02988-145f-4c8e-8286-31680eafa330" name="To End Event" id="500ec071-ba02-4dc1-8de6-da0f1abf1a9a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.500ec071-ba02-4dc1-8de6-da0f1abf1a9a</processLinkId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0f071bb0-**************-ea2c02b3b2b1</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.22d02988-145f-4c8e-8286-31680eafa330</toProcessItemId>
            <guid>a03a6986-93d4-4731-a3a1-3d085b2949a4</guid>
            <versionId>8b512116-4afe-4bcd-a109-3217ba9ce058</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.0f071bb0-**************-ea2c02b3b2b1</fromProcessItemId>
            <toProcessItemId>2025.22d02988-145f-4c8e-8286-31680eafa330</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.10166f91-9ae9-4f8c-8b33-05c32be217b4</processLinkId>
            <processId>1.2d3ab562-82df-48a5-9de7-f5d964218191</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.020101e9-7d22-4a20-8448-13d64cf7754b</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.611ca7d1-29f9-4eb8-893a-a2a3686c84b1</toProcessItemId>
            <guid>4e8b2ba8-a022-4fc2-8067-33416df34a3e</guid>
            <versionId>c25ced26-df3a-4406-bab8-7ab276d9a39f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.020101e9-7d22-4a20-8448-13d64cf7754b</fromProcessItemId>
            <toProcessItemId>2025.611ca7d1-29f9-4eb8-893a-a2a3686c84b1</toProcessItemId>
        </link>
    </process>
</teamworks>

