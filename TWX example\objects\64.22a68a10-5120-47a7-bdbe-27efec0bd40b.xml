<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.22a68a10-5120-47a7-bdbe-27efec0bd40b" name="test view 2">
        <lastModified>1735137030489</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.22a68a10-5120-47a7-bdbe-27efec0bd40b</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;3044a8c2-2db2-4d16-8fe3-76245b438af2&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8bc5df8b-5399-40ac-bfc6-764586fb7284&lt;/ns2:id&gt;&lt;ns2:optionName&gt;showOverflowTooltip&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;50b8be08-2f2f-4a49-8c4b-d2a49d92c473&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Plain text&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;ee77deff-4d7b-4e7d-800e-92c6face2aef&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5a4d9ec1-e599-428a-89bf-928caf9ac167&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5ec4c581-c57c-476f-8436-a5dfebd2f4b5&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;//alert( me.ui.getAbsoluteName() )&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.test2&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>false</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>guid:ae773bd79f7c2e1d:-42381511:193ce7040d1:-7c9f</guid>
        <versionId>2991e37c-d215-49ba-a874-3dbff01d5c0a</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="test2">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.9534964f-8332-41ba-8496-21002edd4b54</coachViewBindingTypeId>
            <coachViewId>64.22a68a10-5120-47a7-bdbe-27efec0bd40b</coachViewId>
            <isList>false</isList>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>b001c2d4-35dc-4317-9ad5-6950afd815b8</guid>
            <versionId>d590815c-7ec3-4b51-8904-7b4ed72e4305</versionId>
        </bindingType>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.e9dcf535-c83e-4b66-854b-3bf2bbdbb954</coachViewInlineScriptId>
            <coachViewId>64.22a68a10-5120-47a7-bdbe-27efec0bd40b</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock></scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>97bad323-7dc9-4765-a940-864af11ecb62</guid>
            <versionId>b66e7cb3-df5a-47dd-963f-b0eb945f7279</versionId>
        </inlineScript>
    </coachView>
</teamworks>

