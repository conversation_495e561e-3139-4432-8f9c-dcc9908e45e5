{"id": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "versionId": "69ae4197-2b05-43e5-b85f-c7987517a34b", "name": "ACT04 - ODC Execution Hub Initiation", "type": "process", "typeName": "Process", "details": {"processType": "10", "variables": {"input": [{"name": "odcRequest", "hasDefault": false, "type": "1"}, {"name": "regeneratedRemittanceLetterTitleVIS", "hasDefault": false, "type": "1"}, {"name": "fromExeChecker", "hasDefault": false, "type": "1"}], "output": [{"name": "odcRequest", "hasDefault": false, "type": "2"}, {"name": "compApprovalInit", "hasDefault": false, "type": "2"}, {"name": "lastAction", "hasDefault": false, "type": "2"}], "private": [{"name": "errorMessage", "hasDefault": false}, {"name": "errorPanelVIS", "hasDefault": false}, {"name": "terminateReasonVIS", "hasDefault": false}, {"name": "actionConditions", "hasDefault": false}, {"name": "deliveryterms", "hasDefault": false}, {"name": "paymentTerms", "hasDefault": false}, {"name": "specialInstructions", "hasDefault": false}, {"name": "instructions", "hasDefault": false}, {"name": "flexCubeVIS", "hasDefault": false}, {"name": "requestTypeVIS", "hasDefault": false}, {"name": "contractStageVIS", "hasDefault": false}, {"name": "bankRefVIS", "hasDefault": false}, {"name": "event", "hasDefault": false}, {"name": "currencyList", "hasDefault": false}, {"name": "addchargeBtnVIS", "hasDefault": false}, {"name": "collectingBankInfo", "hasDefault": false}, {"name": "partyTypesInfo", "hasDefault": false}, {"name": "multiTenorVIS", "hasDefault": false}, {"name": "PartyAccountsList", "hasDefault": false}, {"name": "initialAccountList", "hasDefault": false}, {"name": "parentRequestNumberVIS", "hasDefault": false}, {"name": "x", "hasDefault": false}, {"name": "drawerAccountsList", "hasDefault": false}, {"name": "selectedIndex", "hasDefault": false}, {"name": "fromCurrency", "hasDefault": false}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "hasDefault": false}, {"name": "rate", "hasDefault": false}, {"name": "barCodePdf", "hasDefault": false}, {"name": "generationStatus", "hasDefault": false}, {"name": "calculatedChangeAmnt", "hasDefault": false}, {"name": "customerAndPartyCifs", "hasDefault": false}, {"name": "customerFullDetails", "hasDefault": false}, {"name": "addressBICList", "hasDefault": false}, {"name": "partyTypeName", "hasDefault": false}, {"name": "todayDate", "hasDefault": false}, {"name": "invalidTabs", "hasDefault": false}, {"name": "input1", "hasDefault": false}, {"name": "errorMsg", "hasDefault": false}, {"name": "output1", "hasDefault": false}, {"name": "output2", "hasDefault": false}]}, "elements": {"formTasks": [{"name": "Screen", "id": "2025.4695605b-49e8-461c-8104-b8bc0e52442d", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Coach", "id": "2025.06f37113-06bc-4948-8dc6-92db16860efb", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "callActivities": [{"name": "Test Error", "id": "2025.34ee082d-7910-4a7a-8639-b97420edb475", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Create", "id": "2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "exclusiveGateways": [{"name": "<PERSON><PERSON><PERSON><PERSON>", "id": "2025.c83fdd58-5656-45c1-88be-786d246161b5", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}], "scriptTasks": [{"name": "Validation", "id": "2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7", "script": "tw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\nvar tempLength = 0;\r\r\ntw.local.invalidTabs = [];\r\r\ntw.system.coachValidation.clearValidationErrors();\r\r\n\r\r\n////-------------------------------------------BASIC DETAILS VALIDATION -----------------------------------\r\r\nmandatory(                                 \r\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate\"\r\r\n);\r\r\n\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.BasicDetails.flexCubeContractNo,\r\r\n\t\"tw.local.odcRequest.BasicDetails.flexCubeContractNo\",\r\r\n\t16,\r\r\n\ttw.resource.ValidationMessages.MaxLength16,\r\r\n\t\"Flex Cube Contract Number: \" + tw.resource.ValidationMessages.MaxLength16\r\r\n);\r\r\n//add mess 160 to local file\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\",\r\r\n\t160,\r\r\n\t\"Shouldn't be more than 160 character\",\r\r\n\t\"Commodity Description: \" + \"Shouldn't be more than 160 character\"\r\r\n);\r\r\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\r\n\tmaxLength(\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\r\n\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\",\r\r\n\t\t20,\r\r\n\t\t\"Shouldn't be more than 20 character\",\r\r\n\t\t\"invoice Number: \" + \"Shouldn't be more than 20 character\"\r\r\n\t);\r\r\n}\r\r\n\r\r\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceDate\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\"\r\r\n\t\t);\r\r\n\t}\r\r\n}\r\r\n\r\r\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Bills.length; i++) {\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingDate\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingRef\"\r\r\n\t\t);\r\r\n\t}\r\r\n}\r\r\nvalidateTab(0, \"Basic Details Tab\");\r\r\n//-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.FinancialDetailsBR.maxCollectionDate,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate\"\r\r\n);\r\r\n\r\r\nminLength(\r\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\r\n\t2,\r\r\n\t\"Shouldn't be less than 14 character\",\r\r\n\t\"Amount Advanced: Shouldn't be less than 2 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\r\n\t14,\r\r\n\t\"Shouldn't be more than 14 character\",\r\r\n\t\"Amount Advanced:\" + \"Shouldn't be more than 14 character\"\r\r\n);\r\r\nvalidateTab(3, \"Financial Details - Branch Tab\");\r\r\n//-------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------\r\r\nif (tw.local.odcRequest.FinancialDetailsBR.amountAdvanced > 0) {\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.currency.name,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.currency\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.fromDate,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.fromDate\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.ToDate,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.ToDate\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.accountNo.value,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.accountNo\"\r\r\n\t);\r\r\n\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FcCollections != null &&\r\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate != null\r\r\n\t) {\r\r\n\t\tminLength(\r\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\r\n\t\t\t6,\r\r\n\t\t\t\"Shouldn't be less than 6 character\",\r\r\n\t\t\t\" Negotiated Exchange Rate: Shouldn't be less than 6 character\"\r\r\n\t\t);\r\r\n\t\tmaxLength(\r\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\r\n\t\t\t10,\r\r\n\t\t\t\"Shouldn't be more than 10 character\",\r\r\n\t\t\t\" Negotiated Exchange Rate:\" + \"Shouldn't be more than 10 character\"\r\r\n\t\t);\r\r\n\t}\r\r\n\r\r\n\tvalidateTab(4, \"Flexcube collections Tab\");\r\r\n}\r\r\n//-----------------------------------------Financial Details - Trade FO VALIDATION ----------------------------\r\r\n\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.discount,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.discount\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.extraCharges,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.extraCharges\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.ourCharges,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.ourCharges\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\"\r\r\n);\r\r\n\r\r\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\") {\r\r\n\tif (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length == 0) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates,\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates\"\r\r\n\t\t);\r\r\n\t\ttw.local.errorMessage +=\r\r\n\t\t\t\"<li>\" + \"Fill in at least one entry in Multi Tenordates\" + \"</li>\";\r\r\n\t}\r\r\n}\r\r\n\r\r\nvar sum = 0;\r\r\n\r\r\nfor (var i = 0; i < tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length; i++) {\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == \"\" ||\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == null\r\r\n\t) {\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\",\r\r\n\t\t\t\"Mandatory field\"\r\r\n\t\t);\r\r\n\t} else if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount != \"\") {\r\r\n\t\tcheckNegativeValue(\r\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount,\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == \"\" ||\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == null\r\r\n\t) {\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].date\",\r\r\n\t\t\t\"Mandatory field\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tsum = sum + tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount;\r\r\n}\r\r\n\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\"\r\r\n);\r\r\n\r\r\nif (\r\r\n\t!!tw.local.odcRequest.FinancialDetailsFO.collectableAmount &&\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount < 0\r\r\n)\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount != null &&\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount < sum &&\r\r\n\t\ttw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\"\r\r\n\t) {\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\",\r\r\n\t\t\t\"Sum of all Installment Amounts in a request must be <= Amount Collectable by NBE (اجمالى المبالغ المطلوب تحصيلها)\"\r\r\n\t\t);\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length -1 ].date\",\r\r\n\t\t\t\"Mandatory field\"\r\r\n\t\t);\r\r\n\t}\r\r\n\r\r\nif (\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization > 0 &&\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization > 0\r\r\n) {\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\r\n\t\t\"Partial Avalization isn't allowed\"\r\r\n\t);\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\r\n\t\t\"Partial Avalization isn't allowed\"\r\r\n\t);\r\r\n}\r\r\nvar sumAvalization = parseFloat(0);\r\r\nsumAvalization =\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization +\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization +\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight;\r\r\nif (sumAvalization != tw.local.odcRequest.FinancialDetailsFO.collectableAmount) {\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\r\n\t);\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\r\n\t);\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\",\r\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\r\n\t);\r\r\n}\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.maturityDate,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.maturityDate\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\"\r\r\n);\r\r\n\r\r\n//--------------------------------------------Importer Details VALIDATION ----------------------------------\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\"\r\r\n);\r\r\nmandatory(tw.local.odcRequest.ImporterDetails.bank, \"tw.local.odcRequest.ImporterDetails.bank\");\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\"\r\r\n);\r\r\n\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\",\r\r\n\t250,\r\r\n\t\"Shouldn't be more than 250 character\",\r\r\n\t\"Importer Name:\" + \"Shouldn't be more than 250 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\",\r\r\n\t400,\r\r\n\t\"Shouldn't be more than 400 character\",\r\r\n\t\"Importer Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerPhoneNo,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerPhoneNo\",\r\r\n\t20,\r\r\n\t\"Shouldn't be more than 20 character\",\r\r\n\t\"Importer Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.bank,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bank\",\r\r\n\t250,\r\r\n\t\"Shouldn't be more than 250 character\",\r\r\n\t\"Importer Bank:\" + \"Shouldn't be more than 250 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\",\r\r\n\t11,\r\r\n\t\"Shouldn't be more than 11 character\",\r\r\n\t\"Importer Bank BIC Code:\" + \"Shouldn't be more than 11 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.ibanAccount,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.ibanAccount\",\r\r\n\t40,\r\r\n\t\"Shouldn't be more than 40 character\",\r\r\n\t\"Importer Account(IBAN):\" + \"Shouldn't be more than 40 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\",\r\r\n\t400,\r\r\n\t\"Shouldn't be more than 400 character\",\r\r\n\t\"Importer Bank Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.bankPhoneNo,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bankPhoneNo\",\r\r\n\t20,\r\r\n\t\"Shouldn't be more than 20 character\",\r\r\n\t\"Importer Bank Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.collectingBankReference,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.collectingBankReference\",\r\r\n\t30,\r\r\n\t\"Shouldn't be more than 30 character\",\r\r\n\t\"Collecting Bank Reference:\" + \"Shouldn't be more than 30 character\"\r\r\n);\r\r\nvalidateTab(6, \"Importer Details Tab\");\r\r\n\r\r\n//-----------------------------------------Contract Creation VALIDATION -------------------------------------\r\r\n\r\r\n\r\r\nvalidateString(\r\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\",\r\r\n\t16,\r\r\n\t\"Shouldn't be more than 16 character\",\r\r\n\t\"Contract Creation User Reference\" + \"Shouldn't be more than 16 character\"\r\r\n);\r\r\n\r\r\nvalidateTab(8, \"Contract Creation Tab\");\r\r\n//---------------------------------------------//Charges and Commissions VALIDATION -------------------------------------\r\r\nfor (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\r\n\t//Description - Flat Amount\r\r\n\tif (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 ||\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null\r\r\n\t\t) {\r\r\n\t\t\taddError(\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\r\n\t\t\t\t\"Must be >= 0\"\r\r\n\t\t\t);\r\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n\t\t\tvalidateDecimal2(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\r\r\n\t\t//Fixed Rate\r\r\n\t} else {\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 ||\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null\r\r\n\t\t) {\r\r\n\t\t\taddError(\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\r\n\t\t\t\t\"Must be >= 0\"\r\r\n\t\t\t);\r\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\r\r\n\t\t\tvalidateDecimal2(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\t}\r\r\n\r\r\n\t//skip validation if waiver or changeAmnt < 0\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].waiver == false &&\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0\r\r\n\t) {\r\r\n\t\t//GL Account\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value ==\r\r\n\t\t\t\"GL Account\"\r\r\n\t\t) {\r\r\n\t\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\r\n\t\t\t\taddError(\r\r\n\t\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\t\ti +\r\r\n\t\t\t\t\t\t\"].debitedAccount.glAccountNo\",\r\r\n\t\t\t\t\t\"GL Account Not Verified\"\r\r\n\t\t\t\t);\r\r\n\t\t\t}\r\r\n\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.glAccountNo\"\r\r\n\t\t\t);\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.currency.value\"\r\r\n\t\t\t);\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.branchCode\"\r\r\n\t\t\t);\r\r\n\r\r\n\t\t\t//Customer Account\r\r\n\t\t} else {\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount\r\r\n\t\t\t\t\t.customerAccountNo,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.customerAccountNo\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\r\r\n\t\t//DebitedAmount\r\r\n\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\r\r\n\t\t\tvalidateDecimal(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAmount.negotiatedExRate\",\r\r\n\t\t\t\t\"Must be Decimal(16,10)\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\r\r\n\t\t//Correct Validation but Waiting confirmation on what to do if GL account\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance &&\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false\r\r\n\t\t) {\r\r\n\t\t\taddError(\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAmount.amountInAccount\",\r\r\n\t\t\t\t\"ERROR: Must be <= Account Balance\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\nvalidateTab(9, \"Charges and commissions Tab\");\r\r\n//---------------------------------------------//Parties VALIDATION -------------------------------------\r\r\n/////Drawer Section\r\r\nmandatory(tw.local.odcRequest.Parties.Drawer.partyId, \"tw.local.odcRequest.Parties.Drawer.partyId\");\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.Drawer.partyName,\r\r\n\t\"tw.local.odcRequest.Parties.Drawer.partyName\"\r\r\n);\r\r\n\r\r\n/////Drawee Section\r\r\nmandatory(tw.local.odcRequest.Parties.Drawee.partyId, \"tw.local.odcRequest.Parties.Drawee.partyId\");\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.Drawee.partyName,\r\r\n\t\"tw.local.odcRequest.Parties.Drawee.partyName\"\r\r\n);\r\r\n\r\r\n/////Parties Types (Accountee)\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.partyTypes.partyId,\r\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyId\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.partyTypes.partyName,\r\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyName\"\r\r\n);\r\r\n\r\r\nvalidateTab(10, \"Parties Tab\");\r\r\n//---------------------------------------------------------------------------------\r\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered\r\r\n\t\t? \"\"\r\r\n\t\t: (tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\");\r\r\n}\r\r\n\r\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n\tif (field != null && field != undefined && field.length < len) {\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n\tif (field.length > len) {\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction mandatory(field, fieldName, message) {\r\r\n\tif (!message) {\r\r\n\t\tmessage = camelCaseToTitle(fieldName) + \" is Mandatory\";\r\r\n\t}\r\r\n\r\r\n\tif (field == null || field == undefined) {\r\r\n\t\taddError(fieldName, message, message, true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof field) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (\r\r\n\t\t\t\t\tfield.trim() != undefined &&\r\r\n\t\t\t\t\tfield.trim() != null &&\r\r\n\t\t\t\t\tfield.trim().length == 0\r\r\n\t\t\t\t) {\r\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0) {\r\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tif (field < 0) {\r\r\n\t\t\t\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\r\n\t\t\t\t\taddError(fieldName, msg, msg, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif (field && field.getTime && isFinite(field.getTime())) {\r\r\n\t\t\t\t} else {\r\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction sumDates(fromDate, toDate, fieldName, controlMessage, validationMessage) {\r\r\n\tif (toDate - fromDate == 0) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\taddError(fieldName, controlMessage, validationMessage);\r\r\n\treturn false;\r\r\n}\r\r\n//=========================================================\r\r\nfunction checkNegativeValue(field, fieldName) {\r\r\n\tif (field < 0) {\r\r\n\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\r\n\t\taddError(fieldName, msg, msg, true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\r\n\tvar decimalPattern = /^\\d{1,4}(\\.\\d{1,6})?$/;\r\r\n\tif (!decimalPattern.test(field)) {\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n//========================================================================================\r\r\nfunction validateTab(index, tabName) {\r\r\n\tif (tw.system.coachValidation.validationErrors.length > tempLength) {\r\r\n\t\tif (tw.local.errorMessage.length == 0) {\r\r\n\t\t\ttw.local.errorMessage +=\r\r\n\t\t\t\t\"<p>\" + \"Please complete fields in the following tabs:\" + \"</p>\";\r\r\n\t\t}\r\r\n\t\ttw.local.errorMessage += \"<li>\" + tabName + \"</li>\";\r\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length;\r\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\r\n\t}\r\r\n}\r\r\n//==============================================================\r\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\r\n\tvar decimalPattern = /^\\d{1,12}(\\.\\d{1,2})?$/;\r\r\n\tif (!decimalPattern.test(field)) {\r\r\n\t\t// Decimal is valid\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\t// Decimal is invalid\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\nfunction camelCaseToTitle(camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n}\r\r\n\r\r\nfunction validateString(field, fieldName) {\r\r\n      if (!field) return;\r\r\n\t// Regular expression to match only characters (letters)\r\r\n\tvar regex = /^[a-zA-Z]+$/;\r\r\n\r\r\n\t// Test if the inputString matches the regex\r\r\n      if (regex.test(field)) {\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\taddError(fieldName, \"Numbers aren't allowed\", \"Numbers aren't allowed\");\r\r\n\t\treturn false;\r\r\n\t}\r\r\n}\r\r\n//=================================================================================\r\r\ntw.local.errorMessage != null\r\r\n\t? (tw.local.errorPanelVIS = \"EDITABLE\")\r\r\n\t: (tw.local.errorPanelVIS = \"NONE\");\r\r\n//=================================================================================", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Client-<PERSON>", "id": "2025.1830251f-d054-49e9-8954-7562c5ab62d9", "script": "// /DC_Templete1\r\r\n\r\r\nvar lib = bpmext.ui.getView(\"/test_view1\");\r\r\n\r\r\nlib.mandatory(tw.local.deliveryterms, \"tw.local.deliveryterms\");\r\r\n\r\r\n\r\r\nlib.getErrorList( tw.system.coachValidation );", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}, {"name": "Client-Side Script 1", "id": "2025.091f583e-7ba7-4397-816c-2bcd94163432", "script": "tw.system.", "hasPreScript": false, "hasPostScript": false, "preScript": "", "postScript": ""}]}}, "_fullObjectData": {"teamworks": {"process": {"id": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "name": "ACT04 - ODC Execution Hub Initiation", "lastModified": "1748954111587", "lastModifiedBy": "mohamed.reda", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599", "2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599"], "isRootProcess": "false", "processType": "10", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": {"isNull": "true"}, "mobileReady": "false", "sboSyncEnabled": "false", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "false", "description": {"isNull": "true"}, "guid": "42171bf6-dd95-4757-850f-0e7fa1d52dfb", "versionId": "69ae4197-2b05-43e5-b85f-c7987517a34b", "dependencySummary": {"isNull": "true"}, "jsonData": "{\"rootElement\":[{\"extensionElements\":{\"userTaskImplementation\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7\"],\"isInterrupting\":true,\"extensionElements\":{\"postAssignmentScript\":[],\"default\":[\"2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7\"],\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":113,\"y\":111,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"a7b2f73c-28cf-4a6a-9993-f252e17ae70f\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b2b0bdc1-1032-4018-8978-24a052bafe51\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorPanelVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.9af9df34-0783-4778-9dbc-e75ae58da5b6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"terminateReasonVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.7bd925a6-f30d-4822-83e0-1cf0ba29f6cb\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"actionConditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.5d359bdf-d4b5-4dc0-877a-197f885477b6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"deliveryterms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.843ee6c7-a857-4d2c-87b1-c6374e53fba3\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"paymentTerms\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ce89d2d4-ed8d-4d40-8b7d-0890d5ad74a6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"specialInstructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d2579c3c-4e4a-4a1b-8e46-87a45d36bcba\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"instructions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2aaea123-b9bb-409d-8af1-2b3310bdc747\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"flexCubeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.72531c8e-7b17-45d5-883b-c9e2ca4e7092\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestTypeVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.4a865219-2b57-427b-8a39-7df701820161\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"contractStageVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a40cf44c-95f9-431c-863f-e3cf0e04ea9b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"bankRefVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.096c2f16-3cbd-48c1-805b-960b7cf63ee0\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"event\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8be43605-ddb8-4a68-835f-f1ca3b1af4ab\"},{\"itemSubjectRef\":\"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"name\":\"currencyList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.fae4f2a7-8281-424e-87e4-fa1fb316208c\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"addchargeBtnVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2785bfd8-f42e-49dc-8743-e5295f8753d9\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.addressLine1 = \\\"\\\";\\nautoObject.addressLine2 = \\\"\\\";\\nautoObject.addressLine3 = \\\"\\\";\\nautoObject.customerSector = {};\\nautoObject.customerSector.name = \\\"\\\";\\nautoObject.customerSector.value = \\\"\\\";\\nautoObject.customerType = \\\"\\\";\\nautoObject.customerNoCBE = \\\"\\\";\\nautoObject.facilityType = {};\\nautoObject.facilityType.name = \\\"\\\";\\nautoObject.facilityType.value = \\\"\\\";\\nautoObject.commercialRegistrationNo = \\\"\\\";\\nautoObject.commercialRegistrationOffice = \\\"\\\";\\nautoObject.taxCardNo = \\\"\\\";\\nautoObject.importCardNo = \\\"\\\";\\nautoObject.initiationHub = \\\"\\\";\\nautoObject.country = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a\",\"name\":\"collectingBankInfo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.dfe30b45-a52b-4f95-8a2d-6241995032cc\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.addressLine1 = \\\"\\\";\\nautoObject.addressLine2 = \\\"\\\";\\nautoObject.addressLine3 = \\\"\\\";\\nautoObject.customerSector = {};\\nautoObject.customerSector.name = \\\"\\\";\\nautoObject.customerSector.value = \\\"\\\";\\nautoObject.customerType = \\\"\\\";\\nautoObject.customerNoCBE = \\\"\\\";\\nautoObject.facilityType = {};\\nautoObject.facilityType.name = \\\"\\\";\\nautoObject.facilityType.value = \\\"\\\";\\nautoObject.commercialRegistrationNo = \\\"\\\";\\nautoObject.commercialRegistrationOffice = \\\"\\\";\\nautoObject.taxCardNo = \\\"\\\";\\nautoObject.importCardNo = \\\"\\\";\\nautoObject.initiationHub = \\\"\\\";\\nautoObject.country = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a\",\"name\":\"partyTypesInfo\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.af9c4b39-6fcd-4d9f-8eca-f39dd5ab5369\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"multiTenorVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.f28609ef-8dde-4927-864f-fc323f694ba9\"},{\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"PartyAccountsList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.640fa68e-cdb2-4dc0-842a-3d9b44690ddb\"},{\"itemSubjectRef\":\"itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13\",\"name\":\"initialAccountList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.1162617c-dd7d-4ede-83ca-f0ff3a73c4a9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"parentRequestNumberVIS\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.69e6eedd-1a2f-4d00-81d3-4be407a38938\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"x\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.d092fe95-e100-4c09-8a3c-ff77d4c19f7f\"},{\"itemSubjectRef\":\"itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42\",\"name\":\"drawerAccountsList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.9fea3a16-f7b0-4b82-8b84-e34715aaea53\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"selectedIndex\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6ca322b0-f3cc-4e23-8c9c-38a72d23c824\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"fromCurrency\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.0c3150de-f48c-4392-8745-78bf480ec637\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"toCurrency\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.c575aebe-5805-45f1-8658-f4b61135671e\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"rate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.6af17de0-5078-4f22-8c4b-993cf87cace9\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"barCodePdf\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ed625282-4579-4a61-8ea5-612dbaeb5b82\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"generationStatus\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.066a7f3a-f039-4041-8f63-ea92395a16ec\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"calculatedChangeAmnt\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b08e403d-8597-463f-8e22-c776688990c6\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"customerAndPartyCifs\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.474688e9-036c-4acd-858a-98a352832288\"},{\"itemSubjectRef\":\"itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651\",\"name\":\"customerFullDetails\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.015a8404-6af2-4492-81ac-ced4c5cf575d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"addressBICList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.124649bc-dddd-4055-8621-bbd3938104d0\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"partyTypeName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a7233099-da0d-429f-822c-bc1ed9a69ae3\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"new Date()\"}]},\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"todayDate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.f0e1069c-61d1-4f97-89e6-d78c498604f1\"},{\"itemSubjectRef\":\"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d\",\"name\":\"invalidTabs\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.918536a9-7d45-4725-857b-def837024050\"},{\"outgoing\":[\"2027.04017f08-d5e0-446b-8673-524fd01f95e7\",\"2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352\"],\"incoming\":[\"2027.64964c28-a358-40ac-89dd-4f3c8453f4fd\",\"2027.d832440b-ec84-4d61-840a-c6a1e40396d9\",\"2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":65,\"y\":24,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"],\"preAssignmentScript\":[]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"contentBoxContrib\":[{\"contributions\":[{\"layoutItemId\":\"5\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"9b587cf5-02f3-45ec-8539-5acdc3534c9f\",\"optionName\":\"@label\",\"value\":\"Financial Details Trade FO\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ef44c152-c0f1-4dd9-881f-44af781c5381\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2f16c85e-28a7-44ff-8ac7-80daad5bedde\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"efd6cc50-bcaa-4ffc-83be-b501e5247b14\",\"optionName\":\"act3VIS\",\"value\":\"ReadOnly\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ba61ab19-07ac-42b1-8bf3-f78ce5a6cb33\",\"optionName\":\"requestType\",\"value\":\"tw.local.odcRequest.requestType.value\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0e8fc121-c9a2-4445-8fa9-4d113987737f\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"tw.local.multiTenorVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"6212e428-cda6-4f2c-8de3-c2a483bbc367\",\"optionName\":\"documentAmount\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.documentAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"860e1df0-8dce-4110-8125-f195cc249d01\",\"optionName\":\"amountAdvanced\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8835530a-33c5-4cef-8e3d-762bfc5a4ac3\",\"optionName\":\"todayDate\",\"value\":\"tw.local.todayDate\"}],\"viewUUID\":\"64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1\",\"binding\":\"tw.local.odcRequest.FinancialDetailsFO\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"f2dc714e-5c43-4ddc-8e49-8a416fb3f37b\",\"version\":\"8550\"},{\"layoutItemId\":\"0\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fb00f497-826f-46c5-8f7f-7e6c637a3f5c\",\"optionName\":\"@label\",\"value\":\"Basic Details\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ff028ba6-8748-4836-8be8-fbe74ee68484\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"335805ec-15fc-48d0-8d52-2ad758d4d800\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a1028169-fbbf-4004-8dfb-0dae97ebc9c3\",\"optionName\":\"parentRequestNoVis\",\"value\":\"tw.local.parentRequestNumberVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4a1bfea0-8b6a-4cad-835e-1a0401d0bc93\",\"optionName\":\"contractStageVIS\",\"value\":\"tw.local.contractStageVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b24aa2e1-bade-4c12-8820-fc41b6d9d248\",\"optionName\":\"flexCubeContractNoVIS\",\"value\":\"Editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"ce4170a1-ae16-4fad-8015-05205a52f208\",\"optionName\":\"multiTenorDatesVIS\",\"value\":\"tw.local.multiTenorVIS\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"e21ea690-daf9-4ec5-8e73-d72dadc63db2\",\"optionName\":\"basicDetailsCVVIS\",\"value\":\"Editable\"}],\"viewUUID\":\"64.f7009c53-bea2-4a1f-8dc8-db4918768c05\",\"binding\":\"tw.local.odcRequest.BasicDetails\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"bf381ae5-eb28-44fe-8b2c-95fe1b9d60b7\",\"version\":\"8550\"},{\"layoutItemId\":\"8\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d61cc0e6-28ab-45b9-8e66-1401808422ac\",\"optionName\":\"@label\",\"value\":\"Contract Creation\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"099b04ee-f97e-44e0-8721-1b4f69e30b8f\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"219ba03e-7b89-4667-897f-404656354ca9\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"c364e5d8-b768-4044-89f8-a77b2a2406a9\",\"optionName\":\"contractStage\",\"value\":\"tw.local.odcRequest.BasicDetails.contractStage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4e393b5b-13ab-46ef-8fe0-1384cb6e228e\",\"optionName\":\"bpmRequestNumber\",\"value\":\"tw.local.odcRequest.requestNo\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a1d571ea-e2ef-4710-8911-35d4627e0653\",\"optionName\":\"currency\",\"value\":\"tw.local.odcRequest.FinancialDetailsBR.currency.value\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"52946f33-428b-44e4-87aa-31aff2ff4cef\",\"optionName\":\"nbeCollectableAmount\",\"value\":\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7a9ad29f-24b8-4959-83d9-7a3b19a9f2fd\",\"optionName\":\"baseDate\",\"value\":\"tw.local.today\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"19e11f3e-ab78-4e75-8182-b3b640c6d62f\",\"optionName\":\"contractCreationVis\",\"value\":\"editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"18f643d7-0df4-4db6-8b56-ac9008acb7d7\",\"optionName\":\"todayDate\",\"value\":\"tw.local.todayDate\"}],\"viewUUID\":\"64.0f40f56d-733f-4bd5-916c-92ae7dccbb10\",\"binding\":\"tw.local.odcRequest.ContractCreation\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"c1549433-4905-4fc5-877f-a41e227a93d8\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"ca6107ca-bc4b-4305-86bc-8d212b250ce3\"}],\"layoutItemId\":\"Tab_section1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"812e543b-3aba-4db9-89e9-0704be88ff7b\",\"optionName\":\"@label\",\"value\":\"Tab section\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1744ab20-12ea-4f76-88cf-dd1a4140e7b6\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4b329f05-f47d-41fc-8e7a-06a25d18388e\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2e45773a-0f09-4ef8-8d83-653988e6a074\",\"optionName\":\"colorStyle\",\"value\":\"P\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2e436236-eb83-449a-8d6f-17318d446e77\",\"optionName\":\"tabsStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"S\\\"}]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"688cf571-19a2-4bf8-8f5e-995cbbccd4fa\",\"optionName\":\"sizeStyle\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"X\\\"}]}\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"cbd32f5e-b3eb-4587-8b36-82a68f0a982f\",\"optionName\":\"@visibility\",\"value\":\"{\\\"isResponsiveData\\\":true,\\\"values\\\":[{\\\"deviceConfigID\\\":\\\"LargeID\\\",\\\"value\\\":\\\"REQUIRED\\\"}]}\"}],\"viewUUID\":\"64.c05b439f-a4bd-48b0-8644-fa3b59052217\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"3599af2a-192e-46e3-8bca-622ef4ddbeff\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"273a4cab-8194-472d-8d90-50b65d113642\"}],\"layoutItemId\":\"Vertical_layout1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"64b87181-3389-43b0-8f62-b27073e3765e\",\"optionName\":\"@label\",\"value\":\"Vertical layout\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"07e93a36-bb18-439e-8a67-0b8dafce8cdd\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"28e3dffa-508b-43af-8be5-7827b937eb04\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"ae78654d-6e48-4e5b-82e4-8305300fa439\",\"version\":\"8550\"}],\"contentBoxId\":\"ContentBox1\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ContentBoxContrib\",\"id\":\"94aa8cef-ca3b-46f6-8538-7d77edd23b85\"}],\"layoutItemId\":\"DC_Templete1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7a160b51-fe6b-4a7a-824b-c66c05c6f693\",\"optionName\":\"@label\",\"value\":\"DC Templete\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b1ed620c-d310-4ece-84fa-e12c0a26e65b\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"981204cd-12fb-4701-89c1-ef88e7e98c38\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4a33ec08-3168-4539-8b54-276083cf66f3\",\"optionName\":\"stepLog\",\"value\":\"tw.local.odcRequest.stepLog\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"34a2bcf8-ef34-4ab0-88a5-30e63981c2fc\",\"optionName\":\"action\",\"value\":\"tw.local.odcRequest.actions[]\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"399a5351-739a-42e7-811b-32d66d54847f\",\"optionName\":\"selectedAction\",\"value\":\"tw.local.odcRequest.stepLog.action\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5c4e7e20-d117-4fc0-889f-810bc997da20\",\"optionName\":\"complianceApproval\",\"value\":\"tw.local.odcRequest.complianceApproval\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3bd029f5-2465-4bac-8d5e-25454fc139ad\",\"optionName\":\"complianceApprovalVis\",\"value\":\"Editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a2e07ca0-29dc-4389-89f4-26a5c540266a\",\"optionName\":\"errorMsg\",\"value\":\"tw.local.errorMessage\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"14eb0410-73d6-4144-8a7e-d63aaa71ef47\",\"optionName\":\"errorPanelVIS\",\"value\":\"tw.local.errorPanelVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"839364a7-5d2b-46e9-84f2-9a948bcf5884\",\"optionName\":\"terminateReasonVIS\",\"value\":\"tw.local.terminateReasonVIS\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"04487a36-4d5d-4185-803a-e8d2168fdaf7\",\"optionName\":\"actionConditions\",\"value\":\"tw.local.actionConditions\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"0200fe69-af55-4577-8aae-426fbf618115\",\"optionName\":\"approvalCommentVIS\",\"value\":\"NONE\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"2092fe0a-7939-47cd-8f9e-6e4c00a8580c\",\"optionName\":\"tradeFoCommentVis\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"b98e3d8d-3616-471e-8b60-a569760116f6\",\"optionName\":\"exeHubMkrCommentVis\",\"value\":\"Editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f6735176-e7c7-4acf-8153-e5a13b0cfe3b\",\"optionName\":\"tradeFoComment\",\"value\":\"tw.local.odcRequest.tradeFoComment\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5fb9634b-99ac-4bfa-82d3-54895449b89e\",\"optionName\":\"exeHubMkrComment\",\"value\":\"tw.local.odcRequest.exeHubMkrComment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"bd99d6d6-d684-487e-8d45-ab8cdb620741\",\"optionName\":\"returnReasonVIS\",\"value\":\"Editable\"},{\"valueType\":\"dynamic\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"191cf4de-3c5c-4ffc-85d8-fd41a9b64152\",\"optionName\":\"compcheckerComment\",\"value\":\"tw.local.odcRequest.compcheckerComment\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"3d7aa9c2-68a4-45f8-8400-33430377893f\",\"optionName\":\"compcheckerCommentVIS\",\"value\":\"Readonly\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"5003054e-ba80-47d3-8a49-670101356da3\",\"optionName\":\"disableSubmit\",\"value\":\"false\"}],\"viewUUID\":\"64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f\",\"binding\":\"tw.local.odcRequest.appInfo\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"a4e967b5-84f5-44e8-86b0-0dcd259a0ccf\",\"version\":\"8550\"},{\"layoutItemId\":\"Button1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1b743c51-509a-48f9-8bf7-026f654787d9\",\"optionName\":\"@label\",\"value\":\"Button\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"d134b671-591a-47a2-8235-20249b18f14f\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"4c2434aa-5a0d-425d-8aa7-************\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"698d8eb4-67b2-4963-814f-788b4505ef2e\",\"version\":\"8550\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Screen\",\"isForCompensation\":false,\"completionQuantity\":1,\"dataChangeScript\":\"\",\"id\":\"2025.4695605b-49e8-461c-8104-b8bc0e52442d\"},{\"outgoing\":[\"2027.d832440b-ec84-4d61-840a-c6a1e40396d9\",\"2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0\"],\"incoming\":[\"2027.b683912e-2ca5-4470-8164-22f6918b58b8\"],\"default\":\"2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":397,\"y\":42,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"isValid\",\"declaredType\":\"exclusiveGateway\",\"id\":\"2025.c83fdd58-5656-45c1-88be-786d246161b5\"},{\"startQuantity\":1,\"outgoing\":[\"2027.b683912e-2ca5-4470-8164-22f6918b58b8\"],\"incoming\":[\"2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352\"],\"default\":\"2027.b683912e-2ca5-4470-8164-22f6918b58b8\",\"extensionElements\":{\"postAssignmentScript\":[\"console.clear();\\r\\nconsole.dir(bpmext.ui.getInvalidViews());\"],\"nodeVisualInfo\":[{\"color\":\"#95D087\",\"width\":95,\"x\":261,\"y\":23,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Validation\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.errorMessage = \\\"\\\";\\r\\nvar mandatoryTriggered = false;\\r\\nvar tempLength = 0;\\r\\ntw.local.invalidTabs = [];\\r\\ntw.system.coachValidation.clearValidationErrors();\\r\\n\\r\\n\\/\\/\\/\\/-------------------------------------------BASIC DETAILS VALIDATION -----------------------------------\\r\\nmandatory(                                 \\r\\n\\ttw.local.odcRequest.BasicDetails.commodityDescription,\\r\\n\\t\\\"tw.local.odcRequest.BasicDetails.commodityDescription\\\"\\r\\n);\\r\\n\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo,\\r\\n\\t\\\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo\\\"\\r\\n);\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate,\\r\\n\\t\\\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate\\\"\\r\\n);\\r\\n\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef,\\r\\n\\t\\\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\\\"\\r\\n);\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate,\\r\\n\\t\\\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate\\\"\\r\\n);\\r\\n\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.BasicDetails.flexCubeContractNo,\\r\\n\\t\\\"tw.local.odcRequest.BasicDetails.flexCubeContractNo\\\",\\r\\n\\t16,\\r\\n\\ttw.resource.ValidationMessages.MaxLength16,\\r\\n\\t\\\"Flex Cube Contract Number: \\\" + tw.resource.ValidationMessages.MaxLength16\\r\\n);\\r\\n\\/\\/add mess 160 to local file\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.BasicDetails.commodityDescription,\\r\\n\\t\\\"tw.local.odcRequest.BasicDetails.commodityDescription\\\",\\r\\n\\t160,\\r\\n\\t\\\"Shouldn't be more than 160 character\\\",\\r\\n\\t\\\"Commodity Description: \\\" + \\\"Shouldn't be more than 160 character\\\"\\r\\n);\\r\\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\\r\\n\\tmaxLength(\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\\r\\n\\t\\t\\\"tw.local.odcRequest.BasicDetails.Invoice[\\\" + i + \\\"].invoiceNo\\\",\\r\\n\\t\\t20,\\r\\n\\t\\t\\\"Shouldn't be more than 20 character\\\",\\r\\n\\t\\t\\\"invoice Number: \\\" + \\\"Shouldn't be more than 20 character\\\"\\r\\n\\t);\\r\\n}\\r\\n\\r\\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == \\\"\\\" ||\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == null\\r\\n\\t) {\\r\\n\\t\\tmandatory(\\r\\n\\t\\t\\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.BasicDetails.Invoice[\\\" + i + \\\"].invoiceDate\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == \\\"\\\" ||\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == null\\r\\n\\t) {\\r\\n\\t\\tmandatory(\\r\\n\\t\\t\\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.BasicDetails.Invoice[\\\" + i + \\\"].invoiceNo\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n}\\r\\n\\r\\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Bills.length; i++) {\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == \\\"\\\" ||\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == null\\r\\n\\t) {\\r\\n\\t\\tmandatory(\\r\\n\\t\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.BasicDetails.Bills[\\\" + i + \\\"].billOfLadingDate\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == \\\"\\\" ||\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == null\\r\\n\\t) {\\r\\n\\t\\tmandatory(\\r\\n\\t\\t\\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.BasicDetails.Bills[\\\" + i + \\\"].billOfLadingRef\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n}\\r\\nvalidateTab(0, \\\"Basic Details Tab\\\");\\r\\n\\/\\/-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.FinancialDetailsBR.maxCollectionDate,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate\\\"\\r\\n);\\r\\n\\r\\nminLength(\\r\\n\\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\\\",\\r\\n\\t2,\\r\\n\\t\\\"Shouldn't be less than 14 character\\\",\\r\\n\\t\\\"Amount Advanced: Shouldn't be less than 2 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\\\",\\r\\n\\t14,\\r\\n\\t\\\"Shouldn't be more than 14 character\\\",\\r\\n\\t\\\"Amount Advanced:\\\" + \\\"Shouldn't be more than 14 character\\\"\\r\\n);\\r\\nvalidateTab(3, \\\"Financial Details - Branch Tab\\\");\\r\\n\\/\\/-------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------\\r\\nif (tw.local.odcRequest.FinancialDetailsBR.amountAdvanced > 0) {\\r\\n\\tmandatory(\\r\\n\\t\\ttw.local.odcRequest.FcCollections.currency.name,\\r\\n\\t\\t\\\"tw.local.odcRequest.FcCollections.currency\\\"\\r\\n\\t);\\r\\n\\tmandatory(\\r\\n\\t\\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\\r\\n\\t\\t\\\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\\\"\\r\\n\\t);\\r\\n\\tmandatory(\\r\\n\\t\\ttw.local.odcRequest.FcCollections.fromDate,\\r\\n\\t\\t\\\"tw.local.odcRequest.FcCollections.fromDate\\\"\\r\\n\\t);\\r\\n\\tmandatory(\\r\\n\\t\\ttw.local.odcRequest.FcCollections.ToDate,\\r\\n\\t\\t\\\"tw.local.odcRequest.FcCollections.ToDate\\\"\\r\\n\\t);\\r\\n\\tmandatory(\\r\\n\\t\\ttw.local.odcRequest.FcCollections.accountNo.value,\\r\\n\\t\\t\\\"tw.local.odcRequest.FcCollections.accountNo\\\"\\r\\n\\t);\\r\\n\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.FcCollections != null &&\\r\\n\\t\\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate != null\\r\\n\\t) {\\r\\n\\t\\tminLength(\\r\\n\\t\\t\\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\\\",\\r\\n\\t\\t\\t6,\\r\\n\\t\\t\\t\\\"Shouldn't be less than 6 character\\\",\\r\\n\\t\\t\\t\\\" Negotiated Exchange Rate: Shouldn't be less than 6 character\\\"\\r\\n\\t\\t);\\r\\n\\t\\tmaxLength(\\r\\n\\t\\t\\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\\\",\\r\\n\\t\\t\\t10,\\r\\n\\t\\t\\t\\\"Shouldn't be more than 10 character\\\",\\r\\n\\t\\t\\t\\\" Negotiated Exchange Rate:\\\" + \\\"Shouldn't be more than 10 character\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n\\r\\n\\tvalidateTab(4, \\\"Flexcube collections Tab\\\");\\r\\n}\\r\\n\\/\\/-----------------------------------------Financial Details - Trade FO VALIDATION ----------------------------\\r\\n\\r\\ncheckNegativeValue(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.discount,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.discount\\\"\\r\\n);\\r\\ncheckNegativeValue(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.extraCharges,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.extraCharges\\\"\\r\\n);\\r\\ncheckNegativeValue(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.ourCharges,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.ourCharges\\\"\\r\\n);\\r\\ncheckNegativeValue(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountSight,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountSight\\\"\\r\\n);\\r\\n\\r\\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name != \\\"001\\\") {\\r\\n\\tif (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length == 0) {\\r\\n\\t\\tmandatory(\\r\\n\\t\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates\\\"\\r\\n\\t\\t);\\r\\n\\t\\ttw.local.errorMessage +=\\r\\n\\t\\t\\t\\\"<li>\\\" + \\\"Fill in at least one entry in Multi Tenordates\\\" + \\\"<\\/li>\\\";\\r\\n\\t}\\r\\n}\\r\\n\\r\\nvar sum = 0;\\r\\n\\r\\nfor (var i = 0; i < tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length; i++) {\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == \\\"\\\" ||\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == null\\r\\n\\t) {\\r\\n\\t\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\\\" + i + \\\"].amount\\\",\\r\\n\\t\\t\\t\\\"Mandatory field\\\"\\r\\n\\t\\t);\\r\\n\\t} else if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount != \\\"\\\") {\\r\\n\\t\\tcheckNegativeValue(\\r\\n\\t\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount,\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\\\" + i + \\\"].amount\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == \\\"\\\" ||\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == null\\r\\n\\t) {\\r\\n\\t\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\\\" + i + \\\"].date\\\",\\r\\n\\t\\t\\t\\\"Mandatory field\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n\\tsum = sum + tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount;\\r\\n}\\r\\n\\r\\ncheckNegativeValue(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\\\"\\r\\n);\\r\\ncheckNegativeValue(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\\\"\\r\\n);\\r\\ncheckNegativeValue(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\\\"\\r\\n);\\r\\n\\r\\nif (\\r\\n\\t!!tw.local.odcRequest.FinancialDetailsFO.collectableAmount &&\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount < 0\\r\\n)\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount != null &&\\r\\n\\t\\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount < sum &&\\r\\n\\t\\ttw.local.odcRequest.BasicDetails.paymentTerms.name != \\\"001\\\"\\r\\n\\t) {\\r\\n\\t\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\\\",\\r\\n\\t\\t\\t\\\"Sum of all Installment Amounts in a request must be <= Amount Collectable by NBE (\\u0627\\u062c\\u0645\\u0627\\u0644\\u0649 \\u0627\\u0644\\u0645\\u0628\\u0627\\u0644\\u063a \\u0627\\u0644\\u0645\\u0637\\u0644\\u0648\\u0628 \\u062a\\u062d\\u0635\\u064a\\u0644\\u0647\\u0627)\\\"\\r\\n\\t\\t);\\r\\n\\t\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length -1 ].date\\\",\\r\\n\\t\\t\\t\\\"Mandatory field\\\"\\r\\n\\t\\t);\\r\\n\\t}\\r\\n\\r\\nif (\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization > 0 &&\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization > 0\\r\\n) {\\r\\n\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\\\",\\r\\n\\t\\t\\\"Partial Avalization isn't allowed\\\"\\r\\n\\t);\\r\\n\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\\\",\\r\\n\\t\\t\\\"Partial Avalization isn't allowed\\\"\\r\\n\\t);\\r\\n}\\r\\nvar sumAvalization = parseFloat(0);\\r\\nsumAvalization =\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization +\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization +\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.amountSight;\\r\\nif (sumAvalization != tw.local.odcRequest.FinancialDetailsFO.collectableAmount) {\\r\\n\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\\\",\\r\\n\\t\\t\\\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\\\"\\r\\n\\t);\\r\\n\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\\\",\\r\\n\\t\\t\\\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\\\"\\r\\n\\t);\\r\\n\\ttw.system.coachValidation.addValidationError(\\r\\n\\t\\t\\\"tw.local.odcRequest.FinancialDetailsFO.amountSight\\\",\\r\\n\\t\\t\\\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\\\"\\r\\n\\t);\\r\\n}\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.maturityDate,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.maturityDate\\\"\\r\\n);\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity,\\r\\n\\t\\\"tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\\\"\\r\\n);\\r\\n\\r\\n\\/\\/--------------------------------------------Importer Details VALIDATION ----------------------------------\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerName,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.importerName\\\"\\r\\n);\\r\\n\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerAddress,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.importerAddress\\\"\\r\\n);\\r\\nmandatory(tw.local.odcRequest.ImporterDetails.bank, \\\"tw.local.odcRequest.ImporterDetails.bank\\\");\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.ImporterDetails.BICCode,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.BICCode\\\"\\r\\n);\\r\\n\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.ImporterDetails.bankAddress,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.bankAddress\\\"\\r\\n);\\r\\n\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerName,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.importerName\\\",\\r\\n\\t250,\\r\\n\\t\\\"Shouldn't be more than 250 character\\\",\\r\\n\\t\\\"Importer Name:\\\" + \\\"Shouldn't be more than 250 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerAddress,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.importerAddress\\\",\\r\\n\\t400,\\r\\n\\t\\\"Shouldn't be more than 400 character\\\",\\r\\n\\t\\\"Importer Detailed Address:\\\" + \\\"Shouldn't be more than 400 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.importerPhoneNo,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.importerPhoneNo\\\",\\r\\n\\t20,\\r\\n\\t\\\"Shouldn't be more than 20 character\\\",\\r\\n\\t\\\"Importer Telephone No.:\\\" + \\\"Shouldn't be more than 20 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.bank,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.bank\\\",\\r\\n\\t250,\\r\\n\\t\\\"Shouldn't be more than 250 character\\\",\\r\\n\\t\\\"Importer Bank:\\\" + \\\"Shouldn't be more than 250 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.BICCode,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.BICCode\\\",\\r\\n\\t11,\\r\\n\\t\\\"Shouldn't be more than 11 character\\\",\\r\\n\\t\\\"Importer Bank BIC Code:\\\" + \\\"Shouldn't be more than 11 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.ibanAccount,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.ibanAccount\\\",\\r\\n\\t40,\\r\\n\\t\\\"Shouldn't be more than 40 character\\\",\\r\\n\\t\\\"Importer Account(IBAN):\\\" + \\\"Shouldn't be more than 40 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.bankAddress,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.bankAddress\\\",\\r\\n\\t400,\\r\\n\\t\\\"Shouldn't be more than 400 character\\\",\\r\\n\\t\\\"Importer Bank Detailed Address:\\\" + \\\"Shouldn't be more than 400 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.bankPhoneNo,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.bankPhoneNo\\\",\\r\\n\\t20,\\r\\n\\t\\\"Shouldn't be more than 20 character\\\",\\r\\n\\t\\\"Importer Bank Telephone No.:\\\" + \\\"Shouldn't be more than 20 character\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ImporterDetails.collectingBankReference,\\r\\n\\t\\\"tw.local.odcRequest.ImporterDetails.collectingBankReference\\\",\\r\\n\\t30,\\r\\n\\t\\\"Shouldn't be more than 30 character\\\",\\r\\n\\t\\\"Collecting Bank Reference:\\\" + \\\"Shouldn't be more than 30 character\\\"\\r\\n);\\r\\nvalidateTab(6, \\\"Importer Details Tab\\\");\\r\\n\\r\\n\\/\\/-----------------------------------------Contract Creation VALIDATION -------------------------------------\\r\\n\\r\\n\\r\\nvalidateString(\\r\\n\\ttw.local.odcRequest.ContractCreation.userReference,\\r\\n\\t\\\"tw.local.odcRequest.ContractCreation.userReference\\\"\\r\\n);\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.ContractCreation.userReference,\\r\\n\\t\\\"tw.local.odcRequest.ContractCreation.userReference\\\"\\r\\n);\\r\\nmaxLength(\\r\\n\\ttw.local.odcRequest.ContractCreation.userReference,\\r\\n\\t\\\"tw.local.odcRequest.ContractCreation.userReference\\\",\\r\\n\\t16,\\r\\n\\t\\\"Shouldn't be more than 16 character\\\",\\r\\n\\t\\\"Contract Creation User Reference\\\" + \\\"Shouldn't be more than 16 character\\\"\\r\\n);\\r\\n\\r\\nvalidateTab(8, \\\"Contract Creation Tab\\\");\\r\\n\\/\\/---------------------------------------------\\/\\/Charges and Commissions VALIDATION -------------------------------------\\r\\nfor (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\\r\\n\\t\\/\\/Description - Flat Amount\\r\\n\\tif (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \\\"Flat Amount\\\") {\\r\\n\\t\\tif (\\r\\n\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 ||\\r\\n\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null\\r\\n\\t\\t) {\\r\\n\\t\\t\\taddError(\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\",\\r\\n\\t\\t\\t\\t\\\"Must be >= 0\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\\r\\n\\t\\t\\tvalidateDecimal2(\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount,\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changeAmount\\\",\\r\\n\\t\\t\\t\\t\\\"Must be Decimal(14,2)\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t}\\r\\n\\r\\n\\t\\t\\/\\/Fixed Rate\\r\\n\\t} else {\\r\\n\\t\\tif (\\r\\n\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 ||\\r\\n\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null\\r\\n\\t\\t) {\\r\\n\\t\\t\\taddError(\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\",\\r\\n\\t\\t\\t\\t\\\"Must be >= 0\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\\r\\n\\t\\t\\tvalidateDecimal2(\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage,\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" + i + \\\"].changePercentage\\\",\\r\\n\\t\\t\\t\\t\\\"Must be Decimal(14,2)\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\r\\n\\t\\/\\/skip validation if waiver or changeAmnt < 0\\r\\n\\tif (\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].waiver == false &&\\r\\n\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0\\r\\n\\t) {\\r\\n\\t\\t\\/\\/GL Account\\r\\n\\t\\tif (\\r\\n\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value ==\\r\\n\\t\\t\\t\\\"GL Account\\\"\\r\\n\\t\\t) {\\r\\n\\t\\t\\tif (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\\r\\n\\t\\t\\t\\taddError(\\r\\n\\t\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" +\\r\\n\\t\\t\\t\\t\\t\\ti +\\r\\n\\t\\t\\t\\t\\t\\t\\\"].debitedAccount.glAccountNo\\\",\\r\\n\\t\\t\\t\\t\\t\\\"GL Account Not Verified\\\"\\r\\n\\t\\t\\t\\t);\\r\\n\\t\\t\\t}\\r\\n\\r\\n\\t\\t\\tmandatory(\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo,\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" +\\r\\n\\t\\t\\t\\t\\ti +\\r\\n\\t\\t\\t\\t\\t\\\"].debitedAccount.glAccountNo\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t\\tmandatory(\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value,\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" +\\r\\n\\t\\t\\t\\t\\ti +\\r\\n\\t\\t\\t\\t\\t\\\"].debitedAccount.currency.value\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t\\tmandatory(\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode,\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" +\\r\\n\\t\\t\\t\\t\\ti +\\r\\n\\t\\t\\t\\t\\t\\\"].debitedAccount.branchCode\\\"\\r\\n\\t\\t\\t);\\r\\n\\r\\n\\t\\t\\t\\/\\/Customer Account\\r\\n\\t\\t} else {\\r\\n\\t\\t\\tmandatory(\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount\\r\\n\\t\\t\\t\\t\\t.customerAccountNo,\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" +\\r\\n\\t\\t\\t\\t\\ti +\\r\\n\\t\\t\\t\\t\\t\\\"].debitedAccount.customerAccountNo\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t}\\r\\n\\r\\n\\t\\t\\/\\/DebitedAmount\\r\\n\\t\\tif (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\\r\\n\\t\\t\\tvalidateDecimal(\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate,\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" +\\r\\n\\t\\t\\t\\t\\ti +\\r\\n\\t\\t\\t\\t\\t\\\"].debitedAmount.negotiatedExRate\\\",\\r\\n\\t\\t\\t\\t\\\"Must be Decimal(16,10)\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t}\\r\\n\\r\\n\\t\\t\\/\\/Correct Validation but Waiting confirmation on what to do if GL account\\r\\n\\t\\tif (\\r\\n\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\\r\\n\\t\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance &&\\r\\n\\t\\t\\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false\\r\\n\\t\\t) {\\r\\n\\t\\t\\taddError(\\r\\n\\t\\t\\t\\t\\\"tw.local.odcRequest.ChargesAndCommissions[\\\" +\\r\\n\\t\\t\\t\\t\\ti +\\r\\n\\t\\t\\t\\t\\t\\\"].debitedAmount.amountInAccount\\\",\\r\\n\\t\\t\\t\\t\\\"ERROR: Must be <= Account Balance\\\"\\r\\n\\t\\t\\t);\\r\\n\\t\\t}\\r\\n\\t}\\r\\n}\\r\\nvalidateTab(9, \\\"Charges and commissions Tab\\\");\\r\\n\\/\\/---------------------------------------------\\/\\/Parties VALIDATION -------------------------------------\\r\\n\\/\\/\\/\\/\\/Drawer Section\\r\\nmandatory(tw.local.odcRequest.Parties.Drawer.partyId, \\\"tw.local.odcRequest.Parties.Drawer.partyId\\\");\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.Parties.Drawer.partyName,\\r\\n\\t\\\"tw.local.odcRequest.Parties.Drawer.partyName\\\"\\r\\n);\\r\\n\\r\\n\\/\\/\\/\\/\\/Drawee Section\\r\\nmandatory(tw.local.odcRequest.Parties.Drawee.partyId, \\\"tw.local.odcRequest.Parties.Drawee.partyId\\\");\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.Parties.Drawee.partyName,\\r\\n\\t\\\"tw.local.odcRequest.Parties.Drawee.partyName\\\"\\r\\n);\\r\\n\\r\\n\\/\\/\\/\\/\\/Parties Types (Accountee)\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.Parties.partyTypes.partyId,\\r\\n\\t\\\"tw.local.odcRequest.Parties.partyTypes.partyId\\\"\\r\\n);\\r\\nmandatory(\\r\\n\\ttw.local.odcRequest.Parties.partyTypes.partyName,\\r\\n\\t\\\"tw.local.odcRequest.Parties.partyTypes.partyName\\\"\\r\\n);\\r\\n\\r\\nvalidateTab(10, \\\"Parties Tab\\\");\\r\\n\\/\\/---------------------------------------------------------------------------------\\r\\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\\r\\n\\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\\r\\n\\tfromMandatory && mandatoryTriggered\\r\\n\\t\\t? \\\"\\\"\\r\\n\\t\\t: (tw.local.errorMessage += \\\"<li>\\\" + validationMessage + \\\"<\\/li>\\\");\\r\\n}\\r\\n\\r\\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\\r\\n\\tif (field != null && field != undefined && field.length < len) {\\r\\n\\t\\taddError(fieldName, controlMessage, validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\\r\\n\\tif (field.length > len) {\\r\\n\\t\\taddError(fieldName, controlMessage, validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\nfunction mandatory(field, fieldName, message) {\\r\\n\\tif (!message) {\\r\\n\\t\\tmessage = camelCaseToTitle(fieldName) + \\\" is Mandatory\\\";\\r\\n\\t}\\r\\n\\r\\n\\tif (field == null || field == undefined) {\\r\\n\\t\\taddError(fieldName, message, message, true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t} else {\\r\\n\\t\\tswitch (typeof field) {\\r\\n\\t\\t\\tcase \\\"string\\\":\\r\\n\\t\\t\\t\\tif (\\r\\n\\t\\t\\t\\t\\tfield.trim() != undefined &&\\r\\n\\t\\t\\t\\t\\tfield.trim() != null &&\\r\\n\\t\\t\\t\\t\\tfield.trim().length == 0\\r\\n\\t\\t\\t\\t) {\\r\\n\\t\\t\\t\\t\\taddError(fieldName, message, message, true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\t\\t\\tcase \\\"number\\\":\\r\\n\\t\\t\\t\\tif (field == 0.0) {\\r\\n\\t\\t\\t\\t\\taddError(fieldName, message, message, true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tif (field < 0) {\\r\\n\\t\\t\\t\\t\\tvar msg = \\\"Invalid Value, This field can not be negative value.\\\";\\r\\n\\t\\t\\t\\t\\taddError(fieldName, msg, msg, true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t\\t\\tbreak;\\r\\n\\r\\n\\t\\t\\tdefault:\\r\\n\\t\\t\\t\\t\\/\\/ VALIDATE DATE OBJECT\\r\\n\\t\\t\\t\\tif (field && field.getTime && isFinite(field.getTime())) {\\r\\n\\t\\t\\t\\t} else {\\r\\n\\t\\t\\t\\t\\taddError(fieldName, message, message, true);\\r\\n\\t\\t\\t\\t\\tmandatoryTriggered = true;\\r\\n\\t\\t\\t\\t\\treturn false;\\r\\n\\t\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\nfunction sumDates(fromDate, toDate, fieldName, controlMessage, validationMessage) {\\r\\n\\tif (toDate - fromDate == 0) {\\r\\n\\t\\treturn true;\\r\\n\\t}\\r\\n\\taddError(fieldName, controlMessage, validationMessage);\\r\\n\\treturn false;\\r\\n}\\r\\n\\/\\/=========================================================\\r\\nfunction checkNegativeValue(field, fieldName) {\\r\\n\\tif (field < 0) {\\r\\n\\t\\tvar msg = \\\"Invalid Value, This field can not be negative value.\\\";\\r\\n\\t\\taddError(fieldName, msg, msg, true);\\r\\n\\t\\tmandatoryTriggered = true;\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n\\treturn true;\\r\\n}\\r\\n\\r\\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\\r\\n\\tvar decimalPattern = \\/^\\\\d{1,4}(\\\\.\\\\d{1,6})?$\\/;\\r\\n\\tif (!decimalPattern.test(field)) {\\r\\n\\t\\taddError(fieldName, controlMessage, validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t} else {\\r\\n\\t\\treturn true;\\r\\n\\t}\\r\\n}\\r\\n\\/\\/========================================================================================\\r\\nfunction validateTab(index, tabName) {\\r\\n\\tif (tw.system.coachValidation.validationErrors.length > tempLength) {\\r\\n\\t\\tif (tw.local.errorMessage.length == 0) {\\r\\n\\t\\t\\ttw.local.errorMessage +=\\r\\n\\t\\t\\t\\t\\\"<p>\\\" + \\\"Please complete fields in the following tabs:\\\" + \\\"<\\/p>\\\";\\r\\n\\t\\t}\\r\\n\\t\\ttw.local.errorMessage += \\\"<li>\\\" + tabName + \\\"<\\/li>\\\";\\r\\n\\t\\ttempLength = tw.system.coachValidation.validationErrors.length;\\r\\n\\t\\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\\r\\n\\t}\\r\\n}\\r\\n\\/\\/==============================================================\\r\\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\\r\\n\\tvar decimalPattern = \\/^\\\\d{1,12}(\\\\.\\\\d{1,2})?$\\/;\\r\\n\\tif (!decimalPattern.test(field)) {\\r\\n\\t\\t\\/\\/ Decimal is valid\\r\\n\\t\\taddError(fieldName, controlMessage, validationMessage);\\r\\n\\t\\treturn false;\\r\\n\\t} else {\\r\\n\\t\\t\\/\\/ Decimal is invalid\\r\\n\\t\\treturn true;\\r\\n\\t}\\r\\n}\\r\\n\\r\\nfunction camelCaseToTitle(camelCase) {\\r\\n\\tvar fieldName = camelCase.split(\\\".\\\").pop();\\r\\n\\t\\/\\/ Convert camelCase to Title Case\\r\\n\\tvar titleCase = fieldName.replace(\\/([A-Z])\\/g, \\\" $1\\\");\\r\\n\\t\\/\\/ Uppercase the first character\\r\\n\\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\\r\\n\\treturn titleCase;\\r\\n}\\r\\n\\r\\nfunction validateString(field, fieldName) {\\r\\n      if (!field) return;\\r\\n\\t\\/\\/ Regular expression to match only characters (letters)\\r\\n\\tvar regex = \\/^[a-zA-Z]+$\\/;\\r\\n\\r\\n\\t\\/\\/ Test if the inputString matches the regex\\r\\n      if (regex.test(field)) {\\r\\n\\t\\treturn true;\\r\\n\\t} else {\\r\\n\\t\\taddError(fieldName, \\\"Numbers aren't allowed\\\", \\\"Numbers aren't allowed\\\");\\r\\n\\t\\treturn false;\\r\\n\\t}\\r\\n}\\r\\n\\/\\/=================================================================================\\r\\ntw.local.errorMessage != null\\r\\n\\t? (tw.local.errorPanelVIS = \\\"EDITABLE\\\")\\r\\n\\t: (tw.local.errorPanelVIS = \\\"NONE\\\");\\r\\n\\/\\/=================================================================================\\r\\n\"]}},{\"outgoing\":[\"2027.64964c28-a358-40ac-89dd-4f3c8453f4fd\"],\"incoming\":[\"2027.04017f08-d5e0-446b-8673-524fd01f95e7\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TPostponeTaskEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.894a9c37-37a6-4ce7-8330-51f32d9c3104\"],\"nodeVisualInfo\":[{\"width\":24,\"x\":78,\"y\":-42,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"Postpone\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.5de5922f-74f9-4f78-83a8-4955a265ceaa\"},{\"targetRef\":\"2025.c83fdd58-5656-45c1-88be-786d246161b5\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Valid?\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b683912e-2ca5-4470-8164-22f6918b58b8\",\"sourceRef\":\"2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7\"},{\"targetRef\":\"2025.4695605b-49e8-461c-8104-b8bc0e52442d\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Screen\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.64964c28-a358-40ac-89dd-4f3c8453f4fd\",\"sourceRef\":\"2025.5de5922f-74f9-4f78-83a8-4955a265ceaa\"},{\"targetRef\":\"2025.4695605b-49e8-461c-8104-b8bc0e52442d\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  >\\t  0\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"To Screen\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.d832440b-ec84-4d61-840a-c6a1e40396d9\",\"sourceRef\":\"2025.c83fdd58-5656-45c1-88be-786d246161b5\"},{\"targetRef\":\"2025.5de5922f-74f9-4f78-83a8-4955a265ceaa\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"60d64e04-001d-4515-9834-e7d4116fa8b5\",\"coachEventPath\":\"DC_Templete1\\/saveState\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topLeft\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Postpone\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.04017f08-d5e0-446b-8673-524fd01f95e7\",\"sourceRef\":\"2025.4695605b-49e8-461c-8104-b8bc0e52442d\"},{\"targetRef\":\"2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"aed447f0-6b9e-43bd-8d65-bd136cad5f16\",\"coachEventPath\":\"DC_Templete1\\/submit\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352\",\"sourceRef\":\"2025.4695605b-49e8-461c-8104-b8bc0e52442d\"},{\"incoming\":[\"2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":516,\"y\":47,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"Default\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\"}]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"2025.3ff0b976-5220-4b64-80db-00db7f9f28c4\"},{\"targetRef\":\"2025.3ff0b976-5220-4b64-80db-00db7f9f28c4\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.coachValidation.validationErrors.length\\t  ==\\t  0\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0\",\"sourceRef\":\"2025.c83fdd58-5656-45c1-88be-786d246161b5\"},{\"outgoing\":[\"2027.8a760579-2363-4b05-8732-923cffbbb6d7\"],\"incoming\":[\"2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21\",\"2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":858,\"y\":247,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"validationStayOnPagePaths\":[\"okbutton\"]},\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TFormTask\",\"isHeritageCoach\":false,\"cachePage\":false,\"startQuantity\":1,\"formDefinition\":{\"coachDefinition\":{\"layout\":{\"layoutItem\":[{\"layoutItemId\":\"test_view1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"8f5bc17f-751a-4ff1-8d45-856e2ff1582f\",\"optionName\":\"@label\",\"value\":\"test view\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"db47fb73-0c4a-43f1-8a84-8f3bff4bd773\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"fbd4b89c-ed9a-4fd2-87c9-fb63bc8ef7ed\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.98459c6f-cb8f-462d-9fae-63d331db4606\",\"binding\":\"tw.local.deliveryterms\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"65ef7b8c-0fe4-4821-824a-0cb002a1587e\",\"version\":\"8550\"},{\"layoutItemId\":\"test_view_21\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7fbeb4b8-4a79-4d45-8cf9-bff28c7ebe15\",\"optionName\":\"@label\",\"value\":\"test view 2\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"140204a9-1f55-4d0e-8d85-b3cd3e4d0199\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"1e56ed9b-e6dc-438d-8c1f-8129dc096cad\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"}],\"viewUUID\":\"64.22a68a10-5120-47a7-bdbe-27efec0bd40b\",\"binding\":\"tw.local.paymentTerms\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"fb636457-0963-4cd4-849b-828d977b3855\",\"version\":\"8550\"},{\"layoutItemId\":\"Output_Text1\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"7567d6c8-acd8-4290-8cb0-4ca156cd1b05\",\"optionName\":\"@label\",\"value\":\"Display text\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"a9eed448-d760-4107-8876-d8dbed4041db\",\"optionName\":\"@helpText\",\"value\":\"\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"16cec4f5-3acc-434f-8b3a-28ce68e2c2a8\",\"optionName\":\"@labelVisibility\",\"value\":\"SHOW\"},{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"207a4bcc-ef92-43de-8961-826feaffe9da\",\"optionName\":\"colorStyle\",\"value\":\"G\"}],\"viewUUID\":\"64.f634f22e-7800-4bd7-9f1e-87177acfb3bc\",\"binding\":\"tw.local.errorMessage\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"5b56b0ed-0bab-40cb-85eb-c7d0748b907d\",\"version\":\"8550\"},{\"layoutItemId\":\"okbutton\",\"configData\":[{\"declaredType\":\"com.ibm.bpmsdk.model.coach.ConfigData\",\"id\":\"f148d791-209a-4e36-8e0d-e2bf3b41cd2b\",\"optionName\":\"@label\",\"value\":\"OK\"}],\"viewUUID\":\"64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36\",\"declaredType\":\"com.ibm.bpmsdk.model.coach.ViewRef\",\"id\":\"c1344b74-856b-44a5-892c-b49ad277e4bc\"}]},\"declaredType\":\"com.ibm.bpmsdk.model.coach.TCoachDefinition\"}},\"commonLayoutArea\":0,\"name\":\"Coach\",\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.06f37113-06bc-4948-8dc6-92db16860efb\"},{\"startQuantity\":1,\"outgoing\":[\"2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21\"],\"incoming\":[\"2027.8a760579-2363-4b05-8732-923cffbbb6d7\"],\"default\":\"2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":1061,\"y\":248,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Client-Side Script\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.1830251f-d054-49e9-8954-7562c5ab62d9\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"\\/\\/ \\/DC_Templete1\\r\\n\\r\\nvar lib = bpmext.ui.getView(\\\"\\/test_view1\\\");\\r\\n\\r\\nlib.mandatory(tw.local.deliveryterms, \\\"tw.local.deliveryterms\\\");\\r\\n\\r\\n\\r\\nlib.getErrorList( tw.system.coachValidation );\"]}},{\"targetRef\":\"2025.06f37113-06bc-4948-8dc6-92db16860efb\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21\",\"sourceRef\":\"2025.1830251f-d054-49e9-8954-7562c5ab62d9\"},{\"outgoing\":[\"2027.e0e8c13f-f566-4655-843a-caee6c5d70a5\"],\"incoming\":[\"2027.290f916c-9015-4c0d-8e5f-d4883652dc5d\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":443,\"y\":249,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"declaredType\":\"callActivity\",\"startQuantity\":1,\"default\":\"2027.e0e8c13f-f566-4655-843a-caee6c5d70a5\",\"name\":\"Test Error\",\"dataInputAssociation\":[{\"targetRef\":\"2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.input1\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"id\":\"2025.34ee082d-7910-4a7a-8639-b97420edb475\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.output1\"]}}],\"sourceRef\":[\"2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMsg\"]}}],\"sourceRef\":[\"2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.output2\"]}}],\"sourceRef\":[\"2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0\"]}],\"calledElement\":\"1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8\"},{\"targetRef\":\"2025.4695605b-49e8-461c-8104-b8bc0e52442d\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomRight\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Create\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7\",\"sourceRef\":\"a7b2f73c-28cf-4a6a-9993-f252e17ae70f\"},{\"targetRef\":\"2025.1830251f-d054-49e9-8954-7562c5ab62d9\",\"extensionElements\":{\"coachEventBinding\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TCoachEventBinding\",\"id\":\"e7eeff5e-7a0b-46c4-8b9f-6eac4bc4a3f4\",\"coachEventPath\":\"okbutton\"}],\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":true,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Client-Side Script\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.8a760579-2363-4b05-8732-923cffbbb6d7\",\"sourceRef\":\"2025.06f37113-06bc-4948-8dc6-92db16860efb\"},{\"targetRef\":\"2025.091f583e-7ba7-4397-816c-2bcd94163432\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Client-Side Script 1\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e0e8c13f-f566-4655-843a-caee6c5d70a5\",\"sourceRef\":\"2025.34ee082d-7910-4a7a-8639-b97420edb475\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"input1\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b54e68ad-04e6-478c-86d2-305a2a83f840\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMsg\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.32167ef1-4fa5-466f-8702-a225f50215cd\"},{\"itemSubjectRef\":\"itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be\",\"name\":\"output1\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.bdcb8985-ae7b-401f-8383-4ce216166eeb\"},{\"itemSubjectRef\":\"itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6\",\"name\":\"output2\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.b913fb7d-1bbc-4edf-8420-c7b85912449e\"},{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.e2568422-f163-4dbd-8f2c-a782918ddeb3\"],\"isInterrupting\":true,\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\"}],\"extensionElements\":{\"default\":[\"2027.e2568422-f163-4dbd-8f2c-a782918ddeb3\"],\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":50,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"2025.debb1ad5-4b9a-4d03-860c-09cb0a594169\"},{\"incoming\":[\"2027.b2ae1e6e-b5ff-4332-8f78-193de087a034\"],\"eventDefinition\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TStayOnPageEventDefinition\"}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":700,\"y\":200,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"navigationInstructions\":[{\"targetType\":\"ToOtherDashboard\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TNavigationInstructions\",\"targetURL\":\"tw.system.url.bpmDataEndpoint\"}]},\"name\":\"Stay on page\",\"declaredType\":\"intermediateThrowEvent\",\"id\":\"2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c\"},{\"startQuantity\":1,\"outgoing\":[\"2027.b2ae1e6e-b5ff-4332-8f78-193de087a034\"],\"incoming\":[\"2027.e2568422-f163-4dbd-8f2c-a782918ddeb3\"],\"default\":\"2027.b2ae1e6e-b5ff-4332-8f78-193de087a034\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":353,\"y\":175,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Client-Side Script 2\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.060a5457-7180-4157-85b0-242ad81d0129\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.errorMessage = String( tw.error.data )\\r\\nconsole.log(\\\"<<ERROR>>\\\");\\r\\nconsole.log(tw.error)\\r\\n\\r\\ntw.system.url.\"]}},{\"targetRef\":\"2025.060a5457-7180-4157-85b0-242ad81d0129\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Client-Side Script 2\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.e2568422-f163-4dbd-8f2c-a782918ddeb3\",\"sourceRef\":\"2025.debb1ad5-4b9a-4d03-860c-09cb0a594169\"},{\"targetRef\":\"2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To Stay on page\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.b2ae1e6e-b5ff-4332-8f78-193de087a034\",\"sourceRef\":\"2025.060a5457-7180-4157-85b0-242ad81d0129\"}],\"startQuantity\":1,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":687,\"y\":77,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Error\",\"triggeredByEvent\":true,\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"subProcess\",\"id\":\"2025.7f290839-c94b-4fcd-8099-fe07b5ec75c5\"},{\"startQuantity\":1,\"outgoing\":[\"2027.290f916c-9015-4c0d-8e5f-d4883652dc5d\"],\"default\":\"2027.290f916c-9015-4c0d-8e5f-d4883652dc5d\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":264,\"y\":250,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Create\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8\",\"calledElement\":\"1.5d77055c-98a8-4191-9b74-c7120a5823be\"},{\"targetRef\":\"2025.34ee082d-7910-4a7a-8639-b97420edb475\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.290f916c-9015-4c0d-8e5f-d4883652dc5d\",\"sourceRef\":\"2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8\"},{\"startQuantity\":1,\"outgoing\":[\"2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e\"],\"incoming\":[\"2027.e0e8c13f-f566-4655-843a-caee6c5d70a5\"],\"default\":\"2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":652,\"y\":247,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Client-Side Script 1\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"2025.091f583e-7ba7-4397-816c-2bcd94163432\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.system.\"]}},{\"targetRef\":\"2025.06f37113-06bc-4948-8dc6-92db16860efb\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Coach\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e\",\"sourceRef\":\"2025.091f583e-7ba7-4397-816c-2bcd94163432\"}],\"htmlHeaderTag\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.THtmlHeaderTag\",\"id\":\"6d0ba8e0-0d58-434a-89fa-335d347a4c0b\",\"tagName\":\"viewport\",\"content\":\"width=device-width,initial-scale=1.0\",\"enabled\":true}],\"isClosed\":false,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TUserTaskImplementation\",\"id\":\"dbaf8d1e-a616-49a6-8ba6-e0f6b619f57f\",\"processType\":\"None\"}],\"exposedAs\":[\"NotExposed\"]},\"implementation\":\"##unspecified\",\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"ACT04 - ODC Execution Hub Initiation\",\"declaredType\":\"globalUserTask\",\"id\":\"1.9321de6e-e6a5-435e-accb-0fbbd129c48a\",\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.021ed4ee-8b5e-423d-8672-bdcaceed27d8\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"compApprovalInit\",\"isCollection\":false,\"id\":\"2055.********-2486-426c-8701-7fc1c235c6a3\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"lastAction\",\"isCollection\":false,\"id\":\"2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991\"}],\"extensionElements\":{\"localizationResourceLinks\":[{\"resourceRef\":[{\"resourceBundleGroupID\":\"50.72059ba3-20f9-4926-b151-02b418301dd4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.31cfd076-ef2f-4737-85d1-a256beb27f48\"},{\"resourceBundleGroupID\":\"50.41101508-d2e4-4682-b3ef-b9b22266bb5a\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceRef\",\"id\":\"69.217edee5-1f2d-4b8b-8502-8576e027daf4\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TLocalizationResourceLinks\"}],\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"bb759592-f2f7-40f3-8d2c-1489ab500ca9\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"a32c52e1-a276-4e4d-81c2-cd41ab5bc551\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.86dc5c1d-931d-46d2-9be1-12397cc9f048\",\"epvProcessLinkId\":\"9cd57e59-b7e1-4482-8c4c-588e29e57de4\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.769dc134-1d15-4dd4-a967-c5f61cf352dc\",\"epvProcessLinkId\":\"2d23c808-1ed4-4142-8ddb-d0dcba7601f2\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"0632eb96-065b-4557-887c-01e27486455e\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7\",\"epvProcessLinkId\":\"3171034a-3c65-4e0d-8aee-052ffb8cbeba\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"id\":\"20372ac9-5465-4e48-b85b-6617c2365423\"}],\"outputSet\":[{\"id\":\"0c19fa57-ebfe-46ea-a3b8-02de562e4688\"}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = {};\\nautoObject.initiator = \\\"\\\";\\nautoObject.requestNature = {};\\nautoObject.requestNature.name = \\\"\\\";\\nautoObject.requestNature.value = \\\"\\\";\\nautoObject.requestType = {};\\nautoObject.requestType.name = \\\"\\\";\\nautoObject.requestType.value = \\\"\\\";\\nautoObject.cif = \\\"\\\";\\nautoObject.customerName = \\\"\\\";\\nautoObject.parentRequestNo = \\\"\\\";\\nautoObject.requestDate = new Date();\\nautoObject.ImporterName = \\\"\\\";\\nautoObject.appInfo = {};\\nautoObject.appInfo.requestDate = \\\"\\\";\\nautoObject.appInfo.status = \\\"\\\";\\nautoObject.appInfo.subStatus = \\\"\\\";\\nautoObject.appInfo.initiator = \\\"\\\";\\nautoObject.appInfo.branch = {};\\nautoObject.appInfo.branch.name = \\\"\\\";\\nautoObject.appInfo.branch.value = \\\"\\\";\\nautoObject.appInfo.requestName = \\\"\\\";\\nautoObject.appInfo.requestType = \\\"\\\";\\nautoObject.appInfo.stepName = \\\"\\\";\\nautoObject.appInfo.appRef = \\\"\\\";\\nautoObject.appInfo.appID = \\\"\\\";\\nautoObject.appInfo.instanceID = \\\"\\\";\\nautoObject.CustomerInfo = {};\\nautoObject.CustomerInfo.cif = \\\"\\\";\\nautoObject.CustomerInfo.customerName = \\\"\\\";\\nautoObject.CustomerInfo.addressLine1 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine2 = \\\"\\\";\\nautoObject.CustomerInfo.addressLine3 = \\\"\\\";\\nautoObject.CustomerInfo.customerSector = {};\\nautoObject.CustomerInfo.customerSector.name = \\\"\\\";\\nautoObject.CustomerInfo.customerSector.value = \\\"\\\";\\nautoObject.CustomerInfo.customerType = \\\"\\\";\\nautoObject.CustomerInfo.customerNoCBE = \\\"\\\";\\nautoObject.CustomerInfo.facilityType = {};\\nautoObject.CustomerInfo.facilityType.name = \\\"\\\";\\nautoObject.CustomerInfo.facilityType.value = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationNo = \\\"\\\";\\nautoObject.CustomerInfo.commercialRegistrationOffice = \\\"\\\";\\nautoObject.CustomerInfo.taxCardNo = \\\"\\\";\\nautoObject.CustomerInfo.importCardNo = \\\"\\\";\\nautoObject.CustomerInfo.initiationHub = \\\"\\\";\\nautoObject.CustomerInfo.country = \\\"\\\";\\nautoObject.BasicDetails = {};\\nautoObject.BasicDetails.requestNature = \\\"\\\";\\nautoObject.BasicDetails.requestType = \\\"\\\";\\nautoObject.BasicDetails.parentRequestNo = \\\"\\\";\\nautoObject.BasicDetails.requestState = \\\"\\\";\\nautoObject.BasicDetails.flexCubeContractNo = \\\"\\\";\\nautoObject.BasicDetails.contractStage = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose = {};\\nautoObject.BasicDetails.exportPurpose.name = \\\"\\\";\\nautoObject.BasicDetails.exportPurpose.value = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms = {};\\nautoObject.BasicDetails.paymentTerms.name = \\\"\\\";\\nautoObject.BasicDetails.paymentTerms.value = \\\"\\\";\\nautoObject.BasicDetails.productCategory = {};\\nautoObject.BasicDetails.productCategory.name = \\\"\\\";\\nautoObject.BasicDetails.productCategory.value = \\\"\\\";\\nautoObject.BasicDetails.commodityDescription = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin = {};\\nautoObject.BasicDetails.CountryOfOrigin.name = \\\"\\\";\\nautoObject.BasicDetails.CountryOfOrigin.value = \\\"\\\";\\nautoObject.BasicDetails.Bills = [];\\nautoObject.BasicDetails.Bills[0] = {};\\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \\\"\\\";\\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\\nautoObject.BasicDetails.Invoice = [];\\nautoObject.BasicDetails.Invoice[0] = {};\\nautoObject.BasicDetails.Invoice[0].invoiceNo = \\\"\\\";\\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\\nautoObject.GeneratedDocumentInfo = {};\\nautoObject.GeneratedDocumentInfo.customerName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.customerAddress = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.Instructions = [];\\nautoObject.GeneratedDocumentInfo.Instructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \\\"\\\";\\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\\nautoObject.FinancialDetailsBR = {};\\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\\nautoObject.FinancialDetailsBR.currency = {};\\nautoObject.FinancialDetailsBR.currency.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.currency.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount = {};\\nautoObject.FinancialDetailsBR.collectionAccount.name = \\\"\\\";\\nautoObject.FinancialDetailsBR.collectionAccount.value = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts = [];\\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FcCollections = {};\\nautoObject.FcCollections.currency = {};\\nautoObject.FcCollections.currency.name = \\\"\\\";\\nautoObject.FcCollections.currency.value = \\\"\\\";\\nautoObject.FcCollections.standardExchangeRate = 0.0;\\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\\nautoObject.FcCollections.fromDate = new Date();\\nautoObject.FcCollections.ToDate = new Date();\\nautoObject.FcCollections.accountNo = {};\\nautoObject.FcCollections.accountNo.name = \\\"\\\";\\nautoObject.FcCollections.accountNo.value = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions = [];\\nautoObject.FcCollections.retrievedTransactions[0] = {};\\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.selectedTransactions = [];\\nautoObject.FcCollections.selectedTransactions[0] = {};\\nautoObject.FcCollections.selectedTransactions[0].accountNo = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].currency = \\\"\\\";\\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\\nautoObject.FcCollections.isReversed = false;\\nautoObject.FcCollections.usedAmount = 0.0;\\nautoObject.FcCollections.calculatedAmount = 0.0;\\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\\nautoObject.FcCollections.listOfAccounts = [];\\nautoObject.FcCollections.listOfAccounts[0] = {};\\nautoObject.FcCollections.listOfAccounts[0].name = \\\"\\\";\\nautoObject.FcCollections.listOfAccounts[0].value = \\\"\\\";\\nautoObject.FinancialDetailsFO = {};\\nautoObject.FinancialDetailsFO.discount = 0.0;\\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\\nautoObject.FinancialDetailsFO.amountSight = 0.0;\\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\\nautoObject.FinancialDetailsFO.maturityDate = new Date();\\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\\nautoObject.FinancialDetailsFO.referenceNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.financeApprovalNo = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub = {};\\nautoObject.FinancialDetailsFO.executionHub.name = \\\"\\\";\\nautoObject.FinancialDetailsFO.executionHub.value = \\\"\\\";\\nautoObject.FinancialDetailsFO.multiTenorDates = [];\\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\\nautoObject.FinancialDetailsFO.multiTenorDates[0].rebate = 0.0;\\nautoObject.FinancialDetailsFO.multiTenorDates[0].matDate = new Date();\\nautoObject.FinancialDetailsFO.multiTenorDates[0].tenorDays = 0;\\nautoObject.FinancialDetailsFO.rebate = 0.0;\\nautoObject.ImporterDetails = {};\\nautoObject.ImporterDetails.importerName = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry = {};\\nautoObject.ImporterDetails.importerCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.importerCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.importerAddress = \\\"\\\";\\nautoObject.ImporterDetails.importerPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.bank = \\\"\\\";\\nautoObject.ImporterDetails.BICCode = \\\"\\\";\\nautoObject.ImporterDetails.ibanAccount = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry = {};\\nautoObject.ImporterDetails.bankCountry.name = \\\"\\\";\\nautoObject.ImporterDetails.bankCountry.value = \\\"\\\";\\nautoObject.ImporterDetails.bankAddress = \\\"\\\";\\nautoObject.ImporterDetails.bankPhoneNo = \\\"\\\";\\nautoObject.ImporterDetails.collectingBankReference = \\\"\\\";\\nautoObject.ProductShipmentDetails = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \\\"\\\";\\nautoObject.ProductShipmentDetails.shippingDate = new Date();\\nautoObject.ProductShipmentDetails.shipmentMethod = {};\\nautoObject.ProductShipmentDetails.shipmentMethod.name = \\\"\\\";\\nautoObject.ProductShipmentDetails.shipmentMethod.value = \\\"\\\";\\nautoObject.OdcCollection = {};\\nautoObject.OdcCollection.amount = 0.0;\\nautoObject.OdcCollection.currency = \\\"\\\";\\nautoObject.OdcCollection.informCADAboutTheCollection = false;\\nautoObject.ReversalReason = {};\\nautoObject.ReversalReason.reversalReason = \\\"\\\";\\nautoObject.ReversalReason.closureReason = \\\"\\\";\\nautoObject.ReversalReason.executionHub = {};\\nautoObject.ReversalReason.executionHub.name = \\\"\\\";\\nautoObject.ReversalReason.executionHub.value = \\\"\\\";\\nautoObject.ContractCreation = {};\\nautoObject.ContractCreation.productCode = {};\\nautoObject.ContractCreation.productCode.name = \\\"\\\";\\nautoObject.ContractCreation.productCode.value = \\\"\\\";\\nautoObject.ContractCreation.productDescription = \\\"\\\";\\nautoObject.ContractCreation.Stage = \\\"\\\";\\nautoObject.ContractCreation.userReference = \\\"\\\";\\nautoObject.ContractCreation.sourceReference = \\\"\\\";\\nautoObject.ContractCreation.currency = \\\"\\\";\\nautoObject.ContractCreation.amount = 0.0;\\nautoObject.ContractCreation.baseDate = new Date();\\nautoObject.ContractCreation.valueDate = new Date();\\nautoObject.ContractCreation.tenorDays = 0;\\nautoObject.ContractCreation.transitDays = 0;\\nautoObject.ContractCreation.maturityDate = new Date();\\nautoObject.Parties = {};\\nautoObject.Parties.Drawer = {};\\nautoObject.Parties.Drawer.partyId = \\\"\\\";\\nautoObject.Parties.Drawer.partyName = \\\"\\\";\\nautoObject.Parties.Drawer.country = \\\"\\\";\\nautoObject.Parties.Drawer.Language = \\\"\\\";\\nautoObject.Parties.Drawer.Reference = \\\"\\\";\\nautoObject.Parties.Drawer.address1 = \\\"\\\";\\nautoObject.Parties.Drawer.address2 = \\\"\\\";\\nautoObject.Parties.Drawer.address3 = \\\"\\\";\\nautoObject.Parties.Drawee = {};\\nautoObject.Parties.Drawee.partyId = \\\"\\\";\\nautoObject.Parties.Drawee.partyName = \\\"\\\";\\nautoObject.Parties.Drawee.country = \\\"\\\";\\nautoObject.Parties.Drawee.Language = \\\"\\\";\\nautoObject.Parties.Drawee.Reference = \\\"\\\";\\nautoObject.Parties.Drawee.address1 = \\\"\\\";\\nautoObject.Parties.Drawee.address2 = \\\"\\\";\\nautoObject.Parties.Drawee.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank = {};\\nautoObject.Parties.collectingBank.id = \\\"\\\";\\nautoObject.Parties.collectingBank.name = \\\"\\\";\\nautoObject.Parties.collectingBank.country = \\\"\\\";\\nautoObject.Parties.collectingBank.language = \\\"\\\";\\nautoObject.Parties.collectingBank.reference = \\\"\\\";\\nautoObject.Parties.collectingBank.address1 = \\\"\\\";\\nautoObject.Parties.collectingBank.address2 = \\\"\\\";\\nautoObject.Parties.collectingBank.address3 = \\\"\\\";\\nautoObject.Parties.collectingBank.cif = \\\"\\\";\\nautoObject.Parties.collectingBank.media = \\\"\\\";\\nautoObject.Parties.collectingBank.address = \\\"\\\";\\nautoObject.Parties.partyTypes = {};\\nautoObject.Parties.partyTypes.partyCIF = \\\"\\\";\\nautoObject.Parties.partyTypes.partyId = \\\"\\\";\\nautoObject.Parties.partyTypes.partyName = \\\"\\\";\\nautoObject.Parties.partyTypes.country = \\\"\\\";\\nautoObject.Parties.partyTypes.language = \\\"\\\";\\nautoObject.Parties.partyTypes.refrence = \\\"\\\";\\nautoObject.Parties.partyTypes.address1 = \\\"\\\";\\nautoObject.Parties.partyTypes.address2 = \\\"\\\";\\nautoObject.Parties.partyTypes.address3 = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType = {};\\nautoObject.Parties.partyTypes.partyType.name = \\\"\\\";\\nautoObject.Parties.partyTypes.partyType.value = \\\"\\\";\\nautoObject.Parties.caseInNeed = {};\\nautoObject.Parties.caseInNeed.partyCIF = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyId = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyName = \\\"\\\";\\nautoObject.Parties.caseInNeed.country = \\\"\\\";\\nautoObject.Parties.caseInNeed.language = \\\"\\\";\\nautoObject.Parties.caseInNeed.refrence = \\\"\\\";\\nautoObject.Parties.caseInNeed.address1 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address2 = \\\"\\\";\\nautoObject.Parties.caseInNeed.address3 = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType = {};\\nautoObject.Parties.caseInNeed.partyType.name = \\\"\\\";\\nautoObject.Parties.caseInNeed.partyType.value = \\\"\\\";\\nautoObject.ChargesAndCommissions = [];\\nautoObject.ChargesAndCommissions[0] = {};\\nautoObject.ChargesAndCommissions[0].component = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].waiver = false;\\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\\nautoObject.ChargesAndCommissions[0].rateType = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].description = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \\\"\\\";\\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\\nautoObject.ChargesAndCommissions[0].isGLFound = false;\\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \\\"\\\";\\nautoObject.ContractLiquidation = {};\\nautoObject.ContractLiquidation.liqAmount = 0.0;\\nautoObject.ContractLiquidation.liqCurrency = \\\"\\\";\\nautoObject.ContractLiquidation.debitValueDate = new Date();\\nautoObject.ContractLiquidation.creditValueDate = new Date();\\nautoObject.ContractLiquidation.debitedAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.debitedAccountName = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.branchCode = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency = {};\\nautoObject.ContractLiquidation.creditedAccount.currency.name = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.currency.value = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \\\"\\\";\\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\\nautoObject.ContractLiquidation.creditedAmount = {};\\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\\nautoObject.complianceApproval = false;\\nautoObject.stepLog = {};\\nautoObject.stepLog.startTime = new Date();\\nautoObject.stepLog.endTime = new Date();\\nautoObject.stepLog.userName = \\\"\\\";\\nautoObject.stepLog.role = \\\"\\\";\\nautoObject.stepLog.step = \\\"\\\";\\nautoObject.stepLog.action = \\\"\\\";\\nautoObject.stepLog.comment = \\\"\\\";\\nautoObject.stepLog.terminateReason = \\\"\\\";\\nautoObject.stepLog.returnReason = \\\"\\\";\\nautoObject.actions = [];\\nautoObject.actions[0] = \\\"\\\";\\nautoObject.attachmentDetails = {};\\nautoObject.attachmentDetails.folderID = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties = {};\\nautoObject.attachmentDetails.ecmProperties.fullPath = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \\\"\\\";\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\\nautoObject.attachmentDetails.attachment = [];\\nautoObject.attachmentDetails.attachment[0] = {};\\nautoObject.attachmentDetails.attachment[0].name = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].description = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].arabicName = \\\"\\\";\\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\\nautoObject.complianceComments = [];\\nautoObject.complianceComments[0] = {};\\nautoObject.complianceComments[0].startTime = new Date();\\nautoObject.complianceComments[0].endTime = new Date();\\nautoObject.complianceComments[0].userName = \\\"\\\";\\nautoObject.complianceComments[0].role = \\\"\\\";\\nautoObject.complianceComments[0].step = \\\"\\\";\\nautoObject.complianceComments[0].action = \\\"\\\";\\nautoObject.complianceComments[0].comment = \\\"\\\";\\nautoObject.complianceComments[0].terminateReason = \\\"\\\";\\nautoObject.complianceComments[0].returnReason = \\\"\\\";\\nautoObject.History = [];\\nautoObject.History[0] = {};\\nautoObject.History[0].startTime = new Date();\\nautoObject.History[0].endTime = new Date();\\nautoObject.History[0].userName = \\\"\\\";\\nautoObject.History[0].role = \\\"\\\";\\nautoObject.History[0].step = \\\"\\\";\\nautoObject.History[0].action = \\\"\\\";\\nautoObject.History[0].comment = \\\"\\\";\\nautoObject.History[0].terminateReason = \\\"\\\";\\nautoObject.History[0].returnReason = \\\"\\\";\\nautoObject.documentSource = {};\\nautoObject.documentSource.name = \\\"\\\";\\nautoObject.documentSource.value = \\\"\\\";\\nautoObject.folderID = \\\"\\\";\\nautoObject.isLiquidated = false;\\nautoObject.requestNo = \\\"\\\";\\nautoObject.folderPath = \\\"\\\";\\nautoObject.templateDocID = \\\"\\\";\\nautoObject.requestID = 0;\\nautoObject.customerAndPartyAccountList = [];\\nautoObject.customerAndPartyAccountList[0] = {};\\nautoObject.customerAndPartyAccountList[0].accountNO = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].currencyCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].branchCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\\nautoObject.customerAndPartyAccountList[0].typeCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerName = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].customerNo = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].frozen = false;\\nautoObject.customerAndPartyAccountList[0].dormant = false;\\nautoObject.customerAndPartyAccountList[0].noDebit = false;\\nautoObject.customerAndPartyAccountList[0].noCredit = false;\\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountClassCode = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].balanceType = \\\"\\\";\\nautoObject.customerAndPartyAccountList[0].accountStatus = \\\"\\\";\\nautoObject.tradeFoComment = \\\"\\\";\\nautoObject.exeHubMkrComment = \\\"\\\";\\nautoObject.compcheckerComment = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9\",\"name\":\"odcRequest\",\"isCollection\":false,\"id\":\"2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"regeneratedRemittanceLetterTitleVIS\",\"isCollection\":false,\"id\":\"2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"fromExeChecker\",\"isCollection\":false,\"id\":\"2055.21028eba-aa2f-4b93-806b-78b695c14ddf\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\",\"id\":\"bd4929e7-306f-4912-abfc-c1274d288f81\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "parameterType": "1", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "d2ee210b-0e47-4350-b5af-f9738e6e8078", "versionId": "4fbaf903-9454-4881-a8b7-8c2e648b8952"}, {"name": "regeneratedRemittanceLetterTitleVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "a3ca1a79-77da-45f2-8e1e-50962818ab96", "versionId": "fbebf4e5-0d2a-446c-b5b9-9af9bb3276c5"}, {"name": "fromExeChecker", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.21028eba-aa2f-4b93-806b-78b695c14ddf", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "91303e83-8846-47bd-908f-fc1d0775bbb1", "versionId": "bcacf624-9d6e-4aa0-8fe1-e3e34130d471"}, {"name": "odcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.021ed4ee-8b5e-423d-8672-bd<PERSON><PERSON>ed27d8", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "parameterType": "2", "isArrayOf": "false", "classId": "/12.5f8547c1-515a-481c-b74a-f20ec40faff9", "seq": "4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "ccadb9eb-115c-413d-8c65-720d0afcf397", "versionId": "55b5092d-bc7a-4b26-baf2-c206ff3b1df4"}, {"name": "compApprovalInit", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.********-2486-426c-8701-7fc1c235c6a3", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "61819b6e-bcea-42a7-84de-fe6f1002d16d", "versionId": "fb8ac4fc-ca43-44cd-a1d5-fe4a62f5424b"}, {"name": "lastAction", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "15ca4c4b-f404-475b-88ab-549098cb8fb5", "versionId": "a9f8777e-37c9-4613-9517-72c95c16cd14"}], "processVariable": [{"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b2b0bdc1-1032-4018-8978-24a052bafe51", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4738cffa-b0d6-4a69-be08-6fef20b18224", "versionId": "585fde88-f002-44a0-90d9-b597157438e5"}, {"name": "errorPanelVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9af9df34-0783-4778-9dbc-e75ae58da5b6", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f76067a8-ba16-4af7-83ef-0014af9b3ae1", "versionId": "566fe5c0-7450-416e-9d77-fd321b1380aa"}, {"name": "terminateReasonVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.7bd925a6-f30d-4822-83e0-1cf0ba29f6cb", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "eda0b748-0a47-4ffb-a720-2523049dcc85", "versionId": "fb3be65b-e719-4596-a815-22d21510d296"}, {"name": "actionConditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.5d359bdf-d4b5-4dc0-877a-197f885477b6", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fda7f3b6-f1b4-4a07-8e25-24e5241e4133", "versionId": "d3f0a1a0-ffe8-49a6-91f2-c058a6fe319f"}, {"name": "deliveryterms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.843ee6c7-a857-4d2c-87b1-c6374e53fba3", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f5429ea1-2bc8-4d72-84f6-d462ea6a2826", "versionId": "28a8d20f-c206-4b26-adc5-1799ce2a3ca7"}, {"name": "paymentTerms", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ce89d2d4-ed8d-4d40-8b7d-0890d5ad74a6", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "586f4722-6118-40b8-957c-e432ea40fcc4", "versionId": "7708be25-4f00-4d3c-bee6-431f24df4713"}, {"name": "specialInstructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d2579c3c-4e4a-4a1b-8e46-87a45d36bcba", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "e3f9e340-247e-4c5c-a1dc-43d366fc764e", "versionId": "48a01f3f-f57f-4fe0-aabe-aa4d6830ecad"}, {"name": "instructions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2aaea123-b9bb-409d-8af1-2b3310bdc747", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3efcec6a-bad8-4e10-9ee2-25a560d071c0", "versionId": "236f9cf0-c74e-4934-8fab-d4e578da1dab"}, {"name": "flexCubeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.72531c8e-7b17-45d5-883b-c9e2ca4e7092", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a7f30789-ee4a-484c-b0c4-a48d2de00aad", "versionId": "c4248def-e347-4b21-814e-3685903160fa"}, {"name": "requestTypeVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.4a865219-2b57-427b-8a39-7df701820161", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "8319df7a-314b-4baa-a593-ed352e54f138", "versionId": "03f89394-fe98-4dde-a5c8-1c29f82ad0b5"}, {"name": "contractStageVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a40cf44c-95f9-431c-863f-e3cf0e04ea9b", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ab90d1e8-85fc-4c07-b2f9-fe7c8223a9d9", "versionId": "8719079a-2329-4d91-b84f-88e8fac30c7e"}, {"name": "bankRefVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.096c2f16-3cbd-48c1-805b-960b7cf63ee0", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "12", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "40e9f77a-4197-43e8-aa34-e88d17871f5b", "versionId": "96e4a19a-f4b9-4b25-863b-0b190e4a4dc2"}, {"name": "event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8be43605-ddb8-4a68-835f-f1ca3b1af4ab", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "13", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1cf11e9b-a49a-4eb8-b171-f93ae2838789", "versionId": "9b4df392-efe0-4963-97b8-94e62b6c8791"}, {"name": "currencyList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.fae4f2a7-8281-424e-87e4-fa1fb316208c", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "14", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4f8c3da4-dc14-4be7-a7eb-909560c73b68", "versionId": "1a8afada-71f5-4337-83c5-38d78685df7a"}, {"name": "addchargeBtnVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2785bfd8-f42e-49dc-8743-e5295f8753d9", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "15", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a6200f07-b94a-401d-b8c0-69c791f2cad8", "versionId": "73a70f7b-7b02-4fd5-b1a7-0f7c572dd5ef"}, {"name": "collectingBankInfo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.dfe30b45-a52b-4f95-8a2d-6241995032cc", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "16", "isArrayOf": "false", "isTransient": "false", "classId": "/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "e99d0fe7-bf52-4fab-9b4b-0a6384cb46d6", "versionId": "4f447fa7-a009-4cbd-9a64-dde34f72b763"}, {"name": "partyTypesInfo", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.af9c4b39-6fcd-4d9f-8eca-f39dd5ab5369", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "17", "isArrayOf": "false", "isTransient": "false", "classId": "/12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "efcaf508-73bf-4247-9b06-9be55f3811d2", "versionId": "be1739b4-03e0-4a2c-b19b-735390ffe285"}, {"name": "multiTenorVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f28609ef-8dde-4927-864f-fc323f694ba9", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "18", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "2145ba58-964a-4de0-9d4a-f1c20183b582", "versionId": "db9a5a84-fbe3-43f0-8957-69b18b39168f"}, {"name": "PartyAccountsList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.640fa68e-cdb2-4dc0-842a-3d9b44690ddb", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "19", "isArrayOf": "true", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "39bbc2fd-2e12-4ef9-a84f-00d17a4511af", "versionId": "0369353b-1a2c-4941-8007-0c35fb2d6cdd"}, {"name": "initialAccountList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.1162617c-dd7d-4ede-83ca-f0ff3a73c4a9", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "20", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b8283e1f-a79e-4f66-a123-ceef60d5c30e", "versionId": "b0f9a062-bfc0-428d-82b9-3358a9f3e8bf"}, {"name": "parentRequestNumberVIS", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.69e6eedd-1a2f-4d00-81d3-4be407a38938", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "21", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "fc5a6bac-4ae4-4fdc-83df-e6cb7f375798", "versionId": "dccb05ad-24ec-4854-b913-d9f9b621c2e8"}, {"name": "x", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d092fe95-e100-4c09-8a3c-ff77d4c19f7f", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "22", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3dac9c4c-5556-43c3-a536-efb927ee2ba5", "versionId": "9e7628f6-5e11-4f46-b111-548e608d4e26"}, {"name": "drawerAccountsList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.9fea3a16-f7b0-4b82-8b84-e34715aaea53", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "23", "isArrayOf": "true", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b4e59263-482c-41c6-a583-459b88bb2309", "versionId": "9f83aced-0e80-402c-9e0c-9007703c0306"}, {"name": "selectedIndex", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6ca322b0-f3cc-4e23-8c9c-38a72d23c824", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "24", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0acc9861-fb05-444f-b8aa-5760a2a7d9f4", "versionId": "080b71a7-0924-43d1-972a-95e373630c40"}, {"name": "fromCurrency", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0c3150de-f48c-4392-8745-78bf480ec637", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "25", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "0c3ff73e-5c43-4510-8174-5ce125dc0696", "versionId": "78319e2d-6328-45eb-a2ef-86311962f74f"}, {"name": "to<PERSON><PERSON><PERSON><PERSON>", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.c575aebe-5805-45f1-8658-f4b61135671e", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "26", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "96edd91a-3afa-45c2-a78a-09500caaa32b", "versionId": "0fc58914-52f9-47a5-96bc-c20804a99c77"}, {"name": "rate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.6af17de0-5078-4f22-8c4b-993cf87cace9", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "27", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4d8a85ee-2925-4329-8e17-bd73490e58c3", "versionId": "e3667924-3efb-4237-95de-58955718f51c"}, {"name": "barCodePdf", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ed625282-4579-4a61-8ea5-612dbaeb5b82", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "28", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "647d7719-6b9f-41f2-ab07-************", "versionId": "260190a8-0b9b-4925-8dcf-50f175c86bd6"}, {"name": "generationStatus", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.066a7f3a-f039-4041-8f63-ea92395a16ec", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "29", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d9f951ad-f1b2-4b41-a147-615f92307985", "versionId": "08a00a3d-7464-4a03-8f4e-b9d76777dd7c"}, {"name": "calculatedChangeAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b08e403d-8597-463f-8e22-c776688990c6", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "30", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "54f4b18a-20cb-4091-ae51-f7e8de5e998e", "versionId": "f104c5a5-6f70-4f6f-9be1-dafb754b7aa8"}, {"name": "customerAndPartyCifs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.474688e9-036c-4acd-858a-98a352832288", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "31", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1c3baf45-0edd-48a8-805b-f6841366a8cf", "versionId": "dc8e7918-7f2f-4ad6-b367-e8d23e29039a"}, {"name": "customerFullDetails", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.015a8404-6af2-4492-81ac-ced4c5cf575d", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "32", "isArrayOf": "false", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a1526dd1-4d32-4cf4-a0f3-292e8e60afc4", "versionId": "181f1b7d-8d38-478c-8de5-cd7cabcfbd67"}, {"name": "addressBICList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.124649bc-dddd-4055-8621-bbd3938104d0", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "33", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "24cc12c0-2f0a-416f-be1c-0b39ab93acc5", "versionId": "ac3bf277-45d3-4abf-bd5a-13bdb5e6f621"}, {"name": "partyTypeName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a7233099-da0d-429f-822c-bc1ed9a69ae3", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "34", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "67281079-303c-4d82-b662-f57619fc5a37", "versionId": "0c48fef4-51ef-4914-a0a9-e34f9ec37e6c"}, {"name": "todayDate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.f0e1069c-61d1-4f97-89e6-d78c498604f1", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "35", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "cf11e8fa-9dc0-4977-ab8b-c662788f0c2c", "versionId": "a4f6b194-087c-405f-8056-402f3bb9944b"}, {"name": "invalidTabs", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.918536a9-7d45-4725-857b-def837024050", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "36", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "4506038a-acf1-4571-a6cc-a91119f19378", "versionId": "d04c940e-7471-4dc5-b53f-c648d481710d"}, {"name": "input1", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b54e68ad-04e6-478c-86d2-305a2a83f840", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "37", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c825d035-6244-4137-89e3-b92656b49e1f", "versionId": "7f4fa753-5968-402f-a923-3baa3d3fe42b"}, {"name": "errorMsg", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.32167ef1-4fa5-466f-8702-a225f50215cd", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "38", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "73c97dca-114b-45f7-9228-f86eff74388e", "versionId": "310bf520-cc1f-40f8-8559-d5d98d25b794"}, {"name": "output1", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.bdcb8985-ae7b-401f-8383-4ce216166eeb", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "39", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "a23cd5d9-2044-4fe5-b67c-9ee75d7e174d", "versionId": "d6eef00d-5bf2-4f93-827d-17c4bc87d7b1"}, {"name": "output2", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b913fb7d-1bbc-4edf-8420-c7b85912449e", "description": {"isNull": "true"}, "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "namespace": "2", "seq": "40", "isArrayOf": "true", "isTransient": "false", "classId": "/12.90d4772d-4081-4a73-a8c2-e7f904511cd6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "6522136b-8f42-44bd-8303-3cde3ec0df35", "versionId": "159ea413-afc8-42b1-9c9b-9da6ead6c178"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "name": "Create", "tWComponentName": "SubProcess", "tWComponentId": "3012.7ad6fd9f-6cbf-4202-ada8-accc94964004", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:faf129f3cc554db6:5a40ed45:195332c2764:-3b7d", "versionId": "3ea1bfd0-0cb9-4565-b936-8d52d32d3fc2", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.7ad6fd9f-6cbf-4202-ada8-accc94964004", "attachedProcessRef": "/1.5d77055c-98a8-4191-9b74-c7120a5823be", "guid": "da9d07bd-4a07-415a-a7f7-08de72cde53b", "versionId": "ecc41f35-98b5-4129-bacf-eb340de8c205"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.6b1114d2-2fd1-421a-bc5f-7dc264e40eb4", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.5a4089de-b5ef-4f50-a9ad-1eda71c823bb", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-6ca5", "versionId": "5e96b278-f47b-4ae3-808a-5b045963e081", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.5a4089de-b5ef-4f50-a9ad-1eda71c823bb", "haltProcess": "false", "guid": "*************-452f-a375-c77ba7b80fd4", "versionId": "e91e95fb-43f2-4c80-bec9-0c05d4a33a93"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.34ee082d-7910-4a7a-8639-b97420edb475", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "name": "Test Error", "tWComponentName": "SubProcess", "tWComponentId": "3012.3bc1766e-13ff-4c65-93be-52e15083dd99", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:f7f6553919972802:-74de7c2c:193f7b0288a:-604e", "versionId": "a446e410-475f-4e94-88a4-f911eedd1735", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.3bc1766e-13ff-4c65-93be-52e15083dd99", "attachedProcessRef": "/1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8", "guid": "3a6cb695-0066-4c81-8492-5ba3ac40f646", "versionId": "93fa9656-40eb-48c1-8bbc-ca8972ea0a4c"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "name": "CoachFlowWrapper", "tWComponentName": "Coach<PERSON><PERSON>", "tWComponentId": "3032.82455cad-69f8-4d47-922a-f39d28c22a84", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:cfe63f3497e95e89:316ec9ca:189b900a0fa:-6ca7", "versionId": "e9df911d-d608-4cb0-bbf3-5ca440d2c799", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": ""}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.0de9d3e9-**************-8edb7cfe26a9", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "25e60df2-a8a1-40c3-963f-6877889659c9", "versionId": "15e41e4b-aa5e-44a6-ac71-96f4fdb36d15"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.f6959013-fd5d-40c4-b42b-c790abe785bd", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "0e70bd04-f4b6-4a98-9e39-e05be9f99ef6", "versionId": "2822cd9b-6753-46bb-a064-f9cb0d5f7074"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.cda02f04-16b1-4bde-8754-b9aa144fb80c", "epvId": "/21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "e8f2a6e6-c646-489c-b6db-6fb53f8cc69c", "versionId": "4a8c5b94-a1da-43e6-b398-ea864206fb93"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.8f35d556-c161-45e8-8dfc-ca36711eed1f", "epvId": "/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "42859e4c-08d9-458b-945f-c6e0d65bed6c", "versionId": "4addbe82-7906-4fb4-b919-7070fc07bb1b"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.c08c8bb9-8c4b-468f-93fe-7cfaf5a125c5", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "b2ee3ba1-cd63-4fd0-972c-805dee53ae4e", "versionId": "730b152b-8b25-4fbe-864f-6aa1f72e8ba4"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.cc8c8d43-5d7a-46de-8791-5588e2e6d0f9", "epvId": "/21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "f279a4b9-782b-427e-b445-18bade328f71", "versionId": "fb252735-87ce-4d94-b5b4-52619398eaac"}], "RESOURCE_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.94373cb9-c19b-401c-8ce3-0e058a9af245", "resourceBundleGroupId": "/50.72059ba3-20f9-4926-b151-02b418301dd4", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "61d15ede-ff56-453c-9fe6-86d714f89a0a", "versionId": "363685cc-c2e9-456a-abdf-c5607f41a558"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "resourceProcessLinkId": "2059.4cea5a13-bacb-498a-ae63-da89db440683", "resourceBundleGroupId": "/50.41101508-d2e4-4682-b3ef-b9b22266bb5a", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "guid": "dc8c0a3c-864d-4f45-9e28-9e44da2ad533", "versionId": "c327f647-644d-49d7-a265-d17d01b77362"}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "0", "y": "0", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "rightCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "coachflow": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "id": "bd4929e7-306f-4912-abfc-c1274d288f81", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "ns16:globalUserTask": {"name": "ACT04 - ODC Execution Hub Initiation", "id": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "ns16:documentation": "", "ns16:extensionElements": {"ns3:userTaskImplementation": {"id": "dbaf8d1e-a616-49a6-8ba6-e0f6b619f57f", "ns16:startEvent": {"name": "Start", "id": "a7b2f73c-28cf-4a6a-9993-f252e17ae70f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "113", "y": "111", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns3:postAssignmentScript": "", "ns3:default": "2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7"}, "ns16:outgoing": "2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7"}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.b2b0bdc1-1032-4018-8978-24a052bafe51"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorPanelVIS", "id": "2056.9af9df34-0783-4778-9dbc-e75ae58da5b6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "terminateReasonVIS", "id": "2056.7bd925a6-f30d-4822-83e0-1cf0ba29f6cb"}, {"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "actionConditions", "id": "2056.5d359bdf-d4b5-4dc0-877a-197f885477b6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "deliveryterms", "id": "2056.843ee6c7-a857-4d2c-87b1-c6374e53fba3"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "paymentTerms", "id": "2056.ce89d2d4-ed8d-4d40-8b7d-0890d5ad74a6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "specialInstructions", "id": "2056.d2579c3c-4e4a-4a1b-8e46-87a45d36bcba"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "instructions", "id": "2056.2aaea123-b9bb-409d-8af1-2b3310bdc747"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "flexCubeVIS", "id": "2056.72531c8e-7b17-45d5-883b-c9e2ca4e7092"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "requestTypeVIS", "id": "2056.4a865219-2b57-427b-8a39-7df701820161", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"\"", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "contractStageVIS", "id": "2056.a40cf44c-95f9-431c-863f-e3cf0e04ea9b"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "bankRefVIS", "id": "2056.096c2f16-3cbd-48c1-805b-960b7cf63ee0"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "event", "id": "2056.8be43605-ddb8-4a68-835f-f1ca3b1af4ab"}, {"itemSubjectRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "isCollection": "true", "name": "currencyList", "id": "2056.fae4f2a7-8281-424e-87e4-fa1fb316208c"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "addchargeBtnVIS", "id": "2056.2785bfd8-f42e-49dc-8743-e5295f8753d9"}, {"itemSubjectRef": "itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "isCollection": "false", "name": "collectingBankInfo", "id": "2056.dfe30b45-a52b-4f95-8a2d-6241995032cc", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.addressLine1 = \"\";\r\nautoObject.addressLine2 = \"\";\r\nautoObject.addressLine3 = \"\";\r\nautoObject.customerSector = {};\r\nautoObject.customerSector.name = \"\";\r\nautoObject.customerSector.value = \"\";\r\nautoObject.customerType = \"\";\r\nautoObject.customerNoCBE = \"\";\r\nautoObject.facilityType = {};\r\nautoObject.facilityType.name = \"\";\r\nautoObject.facilityType.value = \"\";\r\nautoObject.commercialRegistrationNo = \"\";\r\nautoObject.commercialRegistrationOffice = \"\";\r\nautoObject.taxCardNo = \"\";\r\nautoObject.importCardNo = \"\";\r\nautoObject.initiationHub = \"\";\r\nautoObject.country = \"\";\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.a2774aa5-138f-4cbd-9faf-dbbc1d59309a", "isCollection": "false", "name": "partyTypesInfo", "id": "2056.af9c4b39-6fcd-4d9f-8eca-f39dd5ab5369", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.addressLine1 = \"\";\r\nautoObject.addressLine2 = \"\";\r\nautoObject.addressLine3 = \"\";\r\nautoObject.customerSector = {};\r\nautoObject.customerSector.name = \"\";\r\nautoObject.customerSector.value = \"\";\r\nautoObject.customerType = \"\";\r\nautoObject.customerNoCBE = \"\";\r\nautoObject.facilityType = {};\r\nautoObject.facilityType.name = \"\";\r\nautoObject.facilityType.value = \"\";\r\nautoObject.commercialRegistrationNo = \"\";\r\nautoObject.commercialRegistrationOffice = \"\";\r\nautoObject.taxCardNo = \"\";\r\nautoObject.importCardNo = \"\";\r\nautoObject.initiationHub = \"\";\r\nautoObject.country = \"\";\r\nautoObject", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "multiTenorVIS", "id": "2056.f28609ef-8dde-4927-864f-fc323f694ba9"}, {"itemSubjectRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isCollection": "true", "name": "PartyAccountsList", "id": "2056.640fa68e-cdb2-4dc0-842a-3d9b44690ddb"}, {"itemSubjectRef": "itm.12.d2e5a15a-ea53-4793-9e93-29af5bd80b13", "isCollection": "true", "name": "initialAccountList", "id": "2056.1162617c-dd7d-4ede-83ca-f0ff3a73c4a9"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "parentRequestNumberVIS", "id": "2056.69e6eedd-1a2f-4d00-81d3-4be407a38938"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "x", "id": "2056.d092fe95-e100-4c09-8a3c-ff77d4c19f7f"}, {"itemSubjectRef": "itm.12.b0c377d8-c17f-4d70-9ed0-b60db6773a42", "isCollection": "true", "name": "drawerAccountsList", "id": "2056.9fea3a16-f7b0-4b82-8b84-e34715aaea53"}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "name": "selectedIndex", "id": "2056.6ca322b0-f3cc-4e23-8c9c-38a72d23c824"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "fromCurrency", "id": "2056.0c3150de-f48c-4392-8745-78bf480ec637"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "to<PERSON><PERSON><PERSON><PERSON>", "id": "2056.c575aebe-5805-45f1-8658-f4b61135671e"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "rate", "id": "2056.6af17de0-5078-4f22-8c4b-993cf87cace9"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "barCodePdf", "id": "2056.ed625282-4579-4a61-8ea5-612dbaeb5b82"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "generationStatus", "id": "2056.066a7f3a-f039-4041-8f63-ea92395a16ec"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "name": "calculatedChangeAmnt", "id": "2056.b08e403d-8597-463f-8e22-c776688990c6"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "customerAndPartyCifs", "id": "2056.474688e9-036c-4acd-858a-98a352832288"}, {"itemSubjectRef": "itm.12.373ca36a-6332-4ee2-b5c0-1e6955bb3651", "isCollection": "false", "name": "customerFullDetails", "id": "2056.015a8404-6af2-4492-81ac-ced4c5cf575d"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "addressBICList", "id": "2056.124649bc-dddd-4055-8621-bbd3938104d0"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "partyTypeName", "id": "2056.a7233099-da0d-429f-822c-bc1ed9a69ae3"}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "todayDate", "id": "2056.f0e1069c-61d1-4f97-89e6-d78c498604f1", "ns16:extensionElements": {"ns3:defaultValue": {"_": "new Date()", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "true", "name": "invalidTabs", "id": "2056.918536a9-7d45-4725-857b-def837024050"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "input1", "id": "2056.b54e68ad-04e6-478c-86d2-305a2a83f840"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMsg", "id": "2056.32167ef1-4fa5-466f-8702-a225f50215cd"}, {"itemSubjectRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be", "isCollection": "false", "name": "output1", "id": "2056.bdcb8985-ae7b-401f-8383-4ce216166eeb"}, {"itemSubjectRef": "itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6", "isCollection": "true", "name": "output2", "id": "2056.b913fb7d-1bbc-4edf-8420-c7b85912449e"}], "ns3:formTask": [{"isHeritageCoach": "false", "cachePage": "false", "commonLayoutArea": "0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Screen", "id": "2025.4695605b-49e8-461c-8104-b8bc0e52442d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "65", "y": "24", "width": "95", "height": "70"}, "ns3:validationStayOnPagePaths": "okbutton", "ns3:preAssignmentScript": "", "ns3:postAssignmentScript": ""}, "ns16:incoming": ["2027.64964c28-a358-40ac-89dd-4f3c8453f4fd", "2027.d832440b-ec84-4d61-840a-c6a1e40396d9", "2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7"], "ns16:outgoing": ["2027.04017f08-d5e0-446b-8673-524fd01f95e7", "2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352"], "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": [{"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "a4e967b5-84f5-44e8-86b0-0dcd259a0ccf", "ns19:layoutItemId": "DC_Templete1", "ns19:configData": [{"ns19:id": "7a160b51-fe6b-4a7a-824b-c66c05c6f693", "ns19:optionName": "@label", "ns19:value": "DC Templete"}, {"ns19:id": "b1ed620c-d310-4ece-84fa-e12c0a26e65b", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "981204cd-12fb-4701-89c1-ef88e7e98c38", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "4a33ec08-3168-4539-8b54-276083cf66f3", "ns19:optionName": "stepLog", "ns19:value": "tw.local.odcRequest.stepLog", "ns19:valueType": "dynamic"}, {"ns19:id": "34a2bcf8-ef34-4ab0-88a5-30e63981c2fc", "ns19:optionName": "action", "ns19:value": "tw.local.odcRequest.actions[]", "ns19:valueType": "dynamic"}, {"ns19:id": "399a5351-739a-42e7-811b-32d66d54847f", "ns19:optionName": "selectedAction", "ns19:value": "tw.local.odcRequest.stepLog.action", "ns19:valueType": "dynamic"}, {"ns19:id": "5c4e7e20-d117-4fc0-889f-810bc997da20", "ns19:optionName": "complianceApproval", "ns19:value": "tw.local.odcRequest.complianceApproval", "ns19:valueType": "dynamic"}, {"ns19:id": "3bd029f5-2465-4bac-8d5e-25454fc139ad", "ns19:optionName": "complianceApprovalVis", "ns19:value": "Editable"}, {"ns19:id": "a2e07ca0-29dc-4389-89f4-26a5c540266a", "ns19:optionName": "errorMsg", "ns19:value": "tw.local.errorMessage", "ns19:valueType": "dynamic"}, {"ns19:id": "14eb0410-73d6-4144-8a7e-d63aaa71ef47", "ns19:optionName": "errorPanelVIS", "ns19:value": "tw.local.errorPanelVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "839364a7-5d2b-46e9-84f2-9a948bcf5884", "ns19:optionName": "terminateReasonVIS", "ns19:value": "tw.local.terminateReasonVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "04487a36-4d5d-4185-803a-e8d2168fdaf7", "ns19:optionName": "actionConditions", "ns19:value": "tw.local.actionConditions", "ns19:valueType": "dynamic"}, {"ns19:id": "0200fe69-af55-4577-8aae-426fbf618115", "ns19:optionName": "approvalCommentVIS", "ns19:value": "NONE"}, {"ns19:id": "2092fe0a-7939-47cd-8f9e-6e4c00a8580c", "ns19:optionName": "tradeFoCommentVis", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "b98e3d8d-3616-471e-8b60-a569760116f6", "ns19:optionName": "exeHubMkrCommentVis", "ns19:value": "Editable"}, {"ns19:id": "f6735176-e7c7-4acf-8153-e5a13b0cfe3b", "ns19:optionName": "tradeFoComment", "ns19:value": "tw.local.odcRequest.tradeFoComment", "ns19:valueType": "dynamic"}, {"ns19:id": "5fb9634b-99ac-4bfa-82d3-54895449b89e", "ns19:optionName": "exeHubMkrComment", "ns19:value": "tw.local.odcRequest.exeHubMkrComment", "ns19:valueType": "dynamic"}, {"ns19:id": "bd99d6d6-d684-487e-8d45-ab8cdb620741", "ns19:optionName": "returnReasonVIS", "ns19:value": "Editable"}, {"ns19:id": "191cf4de-3c5c-4ffc-85d8-fd41a9b64152", "ns19:optionName": "compcheckerComment", "ns19:value": "tw.local.odcRequest.compcheckerComment", "ns19:valueType": "dynamic"}, {"ns19:id": "3d7aa9c2-68a4-45f8-8400-33430377893f", "ns19:optionName": "compcheckerCommentVIS", "ns19:value": "<PERSON><PERSON><PERSON>"}, {"ns19:id": "5003054e-ba80-47d3-8a49-670101356da3", "ns19:optionName": "disableSubmit", "ns19:value": "false"}], "ns19:viewUUID": "64.3f6778b0-f49c-45cd-9411-ec0dc8a2a04f", "ns19:binding": "tw.local.odcRequest.appInfo", "ns19:contentBoxContrib": {"ns19:id": "94aa8cef-ca3b-46f6-8538-7d77edd23b85", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "ae78654d-6e48-4e5b-82e4-8305300fa439", "ns19:layoutItemId": "Vertical_layout1", "ns19:configData": [{"ns19:id": "64b87181-3389-43b0-8f62-b27073e3765e", "ns19:optionName": "@label", "ns19:value": "Vertical layout"}, {"ns19:id": "07e93a36-bb18-439e-8a67-0b8dafce8cdd", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "28e3dffa-508b-43af-8be5-7827b937eb04", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.ef447b87-24a2-42a7-b2b9-cd471e9f7b67", "ns19:contentBoxContrib": {"ns19:id": "273a4cab-8194-472d-8d90-50b65d113642", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "3599af2a-192e-46e3-8bca-622ef4ddbeff", "ns19:layoutItemId": "Tab_section1", "ns19:configData": [{"ns19:id": "812e543b-3aba-4db9-89e9-0704be88ff7b", "ns19:optionName": "@label", "ns19:value": "Tab section"}, {"ns19:id": "1744ab20-12ea-4f76-88cf-dd1a4140e7b6", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "4b329f05-f47d-41fc-8e7a-06a25d18388e", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "2e45773a-0f09-4ef8-8d83-653988e6a074", "ns19:optionName": "colorStyle", "ns19:value": "P"}, {"ns19:id": "2e436236-eb83-449a-8d6f-17318d446e77", "ns19:optionName": "tabsStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"S\"}]}"}, {"ns19:id": "688cf571-19a2-4bf8-8f5e-995cbbccd4fa", "ns19:optionName": "sizeStyle", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"X\"}]}"}, {"ns19:id": "cbd32f5e-b3eb-4587-8b36-82a68f0a982f", "ns19:optionName": "@visibility", "ns19:value": "{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"REQUIRED\"}]}"}], "ns19:viewUUID": "64.c05b439f-a4bd-48b0-8644-fa3b59052217", "ns19:contentBoxContrib": {"ns19:id": "ca6107ca-bc4b-4305-86bc-8d212b250ce3", "ns19:contentBoxId": "ContentBox1", "ns19:contributions": [{"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "f2dc714e-5c43-4ddc-8e49-8a416fb3f37b", "ns19:layoutItemId": "5", "ns19:configData": [{"ns19:id": "9b587cf5-02f3-45ec-8539-5acdc3534c9f", "ns19:optionName": "@label", "ns19:value": "Financial Details Trade FO"}, {"ns19:id": "ef44c152-c0f1-4dd9-881f-44af781c5381", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "2f16c85e-28a7-44ff-8ac7-80daad5bedde", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "efd6cc50-bcaa-4ffc-83be-b501e5247b14", "ns19:optionName": "act3VIS", "ns19:value": "Read<PERSON>nly"}, {"ns19:id": "ba61ab19-07ac-42b1-8bf3-f78ce5a6cb33", "ns19:optionName": "requestType", "ns19:value": "tw.local.odcRequest.requestType.value", "ns19:valueType": "dynamic"}, {"ns19:id": "0e8fc121-c9a2-4445-8fa9-4d113987737f", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "tw.local.multiTenorVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "6212e428-cda6-4f2c-8de3-c2a483bbc367", "ns19:optionName": "documentAmount", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.documentAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "860e1df0-8dce-4110-8125-f195cc249d01", "ns19:optionName": "amountAdvanced", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.amountAdvanced", "ns19:valueType": "dynamic"}, {"ns19:id": "8835530a-33c5-4cef-8e3d-762bfc5a4ac3", "ns19:optionName": "todayDate", "ns19:value": "tw.local.todayDate", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.e34c0762-e0e9-4eab-9ae3-3b854bc176f1", "ns19:binding": "tw.local.odcRequest.FinancialDetailsFO"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "bf381ae5-eb28-44fe-8b2c-95fe1b9d60b7", "ns19:layoutItemId": "0", "ns19:configData": [{"ns19:id": "fb00f497-826f-46c5-8f7f-7e6c637a3f5c", "ns19:optionName": "@label", "ns19:value": "Basic Details"}, {"ns19:id": "ff028ba6-8748-4836-8be8-fbe74ee68484", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "335805ec-15fc-48d0-8d52-2ad758d4d800", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "a1028169-fbbf-4004-8dfb-0dae97ebc9c3", "ns19:optionName": "parentRequestNoVis", "ns19:value": "tw.local.parentRequestNumberVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "4a1bfea0-8b6a-4cad-835e-1a0401d0bc93", "ns19:optionName": "contractStageVIS", "ns19:value": "tw.local.contractStageVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "b24aa2e1-bade-4c12-8820-fc41b6d9d248", "ns19:optionName": "flexCubeContractNoVIS", "ns19:value": "Editable"}, {"ns19:id": "ce4170a1-ae16-4fad-8015-05205a52f208", "ns19:optionName": "multiTenorDatesVIS", "ns19:value": "tw.local.multiTenorVIS", "ns19:valueType": "dynamic"}, {"ns19:id": "e21ea690-daf9-4ec5-8e73-d72dadc63db2", "ns19:optionName": "basicDetailsCVVIS", "ns19:value": "Editable"}], "ns19:viewUUID": "64.f7009c53-bea2-4a1f-8dc8-db4918768c05", "ns19:binding": "tw.local.odcRequest.BasicDetails"}, {"xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "c1549433-4905-4fc5-877f-a41e227a93d8", "ns19:layoutItemId": "8", "ns19:configData": [{"ns19:id": "d61cc0e6-28ab-45b9-8e66-1401808422ac", "ns19:optionName": "@label", "ns19:value": "Contract Creation"}, {"ns19:id": "099b04ee-f97e-44e0-8721-1b4f69e30b8f", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "219ba03e-7b89-4667-897f-404656354ca9", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "c364e5d8-b768-4044-89f8-a77b2a2406a9", "ns19:optionName": "contractStage", "ns19:value": "tw.local.odcRequest.BasicDetails.contractStage", "ns19:valueType": "dynamic"}, {"ns19:id": "4e393b5b-13ab-46ef-8fe0-1384cb6e228e", "ns19:optionName": "bpmRequestNumber", "ns19:value": "tw.local.odcRequest.requestNo", "ns19:valueType": "dynamic"}, {"ns19:id": "a1d571ea-e2ef-4710-8911-35d4627e0653", "ns19:optionName": "currency", "ns19:value": "tw.local.odcRequest.FinancialDetailsBR.currency.value", "ns19:valueType": "dynamic"}, {"ns19:id": "52946f33-428b-44e4-87aa-31aff2ff4cef", "ns19:optionName": "nbeCollectableAmount", "ns19:value": "tw.local.odcRequest.FinancialDetailsFO.collectableAmount", "ns19:valueType": "dynamic"}, {"ns19:id": "7a9ad29f-24b8-4959-83d9-7a3b19a9f2fd", "ns19:optionName": "baseDate", "ns19:value": "tw.local.today", "ns19:valueType": "dynamic"}, {"ns19:id": "19e11f3e-ab78-4e75-8182-b3b640c6d62f", "ns19:optionName": "contractCreationVis", "ns19:value": "editable"}, {"ns19:id": "18f643d7-0df4-4db6-8b56-ac9008acb7d7", "ns19:optionName": "todayDate", "ns19:value": "tw.local.todayDate", "ns19:valueType": "dynamic"}], "ns19:viewUUID": "64.0f40f56d-733f-4bd5-916c-92ae7dccbb10", "ns19:binding": "tw.local.odcRequest.ContractCreation"}]}}}}}}, {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "698d8eb4-67b2-4963-814f-788b4505ef2e", "ns19:layoutItemId": "Button1", "ns19:configData": [{"ns19:id": "1b743c51-509a-48f9-8bf7-026f654787d9", "ns19:optionName": "@label", "ns19:value": "<PERSON><PERSON>"}, {"ns19:id": "d134b671-591a-47a2-8235-20249b18f14f", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "4c2434aa-5a0d-425d-8aa7-************", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36"}]}}}, "ns3:dataChangeScript": ""}, {"name": "Coach", "id": "2025.06f37113-06bc-4948-8dc6-92db16860efb", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "858", "y": "247", "width": "95", "height": "70"}, "ns3:validationStayOnPagePaths": "okbutton"}, "ns16:incoming": ["2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21", "2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e"], "ns16:outgoing": "2027.8a760579-2363-4b05-8732-923cffbbb6d7", "ns3:formDefinition": {"ns19:coachDefinition": {"ns19:layout": {"ns19:layoutItem": [{"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "65ef7b8c-0fe4-4821-824a-0cb002a1587e", "ns19:layoutItemId": "test_view1", "ns19:configData": [{"ns19:id": "8f5bc17f-751a-4ff1-8d45-856e2ff1582f", "ns19:optionName": "@label", "ns19:value": "test view"}, {"ns19:id": "db47fb73-0c4a-43f1-8a84-8f3bff4bd773", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "fbd4b89c-ed9a-4fd2-87c9-fb63bc8ef7ed", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.98459c6f-cb8f-462d-9fae-63d331db4606", "ns19:binding": "tw.local.deliveryterms"}, {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "fb636457-0963-4cd4-849b-828d977b3855", "ns19:layoutItemId": "test_view_21", "ns19:configData": [{"ns19:id": "7fbeb4b8-4a79-4d45-8cf9-bff28c7ebe15", "ns19:optionName": "@label", "ns19:value": "test view 2"}, {"ns19:id": "140204a9-1f55-4d0e-8d85-b3cd3e4d0199", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "1e56ed9b-e6dc-438d-8c1f-8129dc096cad", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}], "ns19:viewUUID": "64.22a68a10-5120-47a7-bdbe-27efec0bd40b", "ns19:binding": "tw.local.paymentTerms"}, {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "version": "8550", "ns19:id": "5b56b0ed-0bab-40cb-85eb-c7d0748b907d", "ns19:layoutItemId": "Output_Text1", "ns19:configData": [{"ns19:id": "7567d6c8-acd8-4290-8cb0-4ca156cd1b05", "ns19:optionName": "@label", "ns19:value": "Display text"}, {"ns19:id": "a9eed448-d760-4107-8876-d8dbed4041db", "ns19:optionName": "@helpText", "ns19:value": ""}, {"ns19:id": "16cec4f5-3acc-434f-8b3a-28ce68e2c2a8", "ns19:optionName": "@labelVisibility", "ns19:value": "SHOW"}, {"ns19:id": "207a4bcc-ef92-43de-8961-826feaffe9da", "ns19:optionName": "colorStyle", "ns19:value": "G"}], "ns19:viewUUID": "64.f634f22e-7800-4bd7-9f1e-87177acfb3bc", "ns19:binding": "tw.local.errorMessage"}, {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns19:ViewRef", "ns19:id": "c1344b74-856b-44a5-892c-b49ad277e4bc", "ns19:layoutItemId": "okbutton", "ns19:configData": {"ns19:id": "f148d791-209a-4e36-8e0d-e2bf3b41cd2b", "ns19:optionName": "@label", "ns19:value": "OK"}, "ns19:viewUUID": "64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36"}]}}}}], "ns16:exclusiveGateway": {"default": "2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0", "gatewayDirection": "Unspecified", "name": "<PERSON><PERSON><PERSON><PERSON>", "id": "2025.c83fdd58-5656-45c1-88be-786d246161b5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "397", "y": "42", "width": "32", "height": "32"}}, "ns16:incoming": "2027.b683912e-2ca5-4470-8164-22f6918b58b8", "ns16:outgoing": ["2027.d832440b-ec84-4d61-840a-c6a1e40396d9", "2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0"]}, "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.b683912e-2ca5-4470-8164-22f6918b58b8", "name": "Validation", "id": "2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "261", "y": "23", "width": "95", "height": "70", "color": "#95D087"}, "ns3:postAssignmentScript": "console.clear();\r\r\nconsole.dir(bpmext.ui.getInvalidViews());"}, "ns16:incoming": "2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352", "ns16:outgoing": "2027.b683912e-2ca5-4470-8164-22f6918b58b8", "ns16:script": "tw.local.errorMessage = \"\";\r\r\nvar mandatoryTriggered = false;\r\r\nvar tempLength = 0;\r\r\ntw.local.invalidTabs = [];\r\r\ntw.system.coachValidation.clearValidationErrors();\r\r\n\r\r\n////-------------------------------------------BASIC DETAILS VALIDATION -----------------------------------\r\r\nmandatory(                                 \r\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceNo\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Invoice[0].invoiceDate\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingRef\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate,\r\r\n\t\"tw.local.odcRequest.BasicDetails.Bills[0].billOfLadingDate\"\r\r\n);\r\r\n\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.BasicDetails.flexCubeContractNo,\r\r\n\t\"tw.local.odcRequest.BasicDetails.flexCubeContractNo\",\r\r\n\t16,\r\r\n\ttw.resource.ValidationMessages.MaxLength16,\r\r\n\t\"Flex Cube Contract Number: \" + tw.resource.ValidationMessages.MaxLength16\r\r\n);\r\r\n//add mess 160 to local file\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.BasicDetails.commodityDescription,\r\r\n\t\"tw.local.odcRequest.BasicDetails.commodityDescription\",\r\r\n\t160,\r\r\n\t\"Shouldn't be more than 160 character\",\r\r\n\t\"Commodity Description: \" + \"Shouldn't be more than 160 character\"\r\r\n);\r\r\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\r\n\tmaxLength(\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\r\n\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\",\r\r\n\t\t20,\r\r\n\t\t\"Shouldn't be more than 20 character\",\r\r\n\t\t\"invoice Number: \" + \"Shouldn't be more than 20 character\"\r\r\n\t);\r\r\n}\r\r\n\r\r\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Invoice.length; i++) {\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceDate,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceDate\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Invoice[i].invoiceNo,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Invoice[\" + i + \"].invoiceNo\"\r\r\n\t\t);\r\r\n\t}\r\r\n}\r\r\n\r\r\nfor (var i = 0; i < tw.local.odcRequest.BasicDetails.Bills.length; i++) {\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingDate,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingDate\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == \"\" ||\r\r\n\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef == null\r\r\n\t) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.BasicDetails.Bills[i].billOfLadingRef,\r\r\n\t\t\t\"tw.local.odcRequest.BasicDetails.Bills[\" + i + \"].billOfLadingRef\"\r\r\n\t\t);\r\r\n\t}\r\r\n}\r\r\nvalidateTab(0, \"Basic Details Tab\");\r\r\n//-----------------------------------------------FINANCIAL DETAILS - BRANCH VALIDATION -------------------------------------\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.FinancialDetailsBR.maxCollectionDate,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate\"\r\r\n);\r\r\n\r\r\nminLength(\r\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\r\n\t2,\r\r\n\t\"Shouldn't be less than 14 character\",\r\r\n\t\"Amount Advanced: Shouldn't be less than 2 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.FinancialDetailsBR.amountAdvanced,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsBR.amountAdvanced\",\r\r\n\t14,\r\r\n\t\"Shouldn't be more than 14 character\",\r\r\n\t\"Amount Advanced:\" + \"Shouldn't be more than 14 character\"\r\r\n);\r\r\nvalidateTab(3, \"Financial Details - Branch Tab\");\r\r\n//-------------------------------------FLEX CUBE COLLECTIONS VALIDATION -------------------------------\r\r\nif (tw.local.odcRequest.FinancialDetailsBR.amountAdvanced > 0) {\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.currency.name,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.currency\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.fromDate,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.fromDate\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.ToDate,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.ToDate\"\r\r\n\t);\r\r\n\tmandatory(\r\r\n\t\ttw.local.odcRequest.FcCollections.accountNo.value,\r\r\n\t\t\"tw.local.odcRequest.FcCollections.accountNo\"\r\r\n\t);\r\r\n\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FcCollections != null &&\r\r\n\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate != null\r\r\n\t) {\r\r\n\t\tminLength(\r\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\r\n\t\t\t6,\r\r\n\t\t\t\"Shouldn't be less than 6 character\",\r\r\n\t\t\t\" Negotiated Exchange Rate: Shouldn't be less than 6 character\"\r\r\n\t\t);\r\r\n\t\tmaxLength(\r\r\n\t\t\ttw.local.odcRequest.FcCollections.negotiatedExchangeRate,\r\r\n\t\t\t\"tw.local.odcRequest.FcCollections.negotiatedExchangeRate\",\r\r\n\t\t\t10,\r\r\n\t\t\t\"Shouldn't be more than 10 character\",\r\r\n\t\t\t\" Negotiated Exchange Rate:\" + \"Shouldn't be more than 10 character\"\r\r\n\t\t);\r\r\n\t}\r\r\n\r\r\n\tvalidateTab(4, \"Flexcube collections Tab\");\r\r\n}\r\r\n//-----------------------------------------Financial Details - Trade FO VALIDATION ----------------------------\r\r\n\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.discount,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.discount\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.extraCharges,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.extraCharges\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.ourCharges,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.ourCharges\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\"\r\r\n);\r\r\n\r\r\nif (tw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\") {\r\r\n\tif (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length == 0) {\r\r\n\t\tmandatory(\r\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates,\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates\"\r\r\n\t\t);\r\r\n\t\ttw.local.errorMessage +=\r\r\n\t\t\t\"<li>\" + \"Fill in at least one entry in Multi Tenordates\" + \"</li>\";\r\r\n\t}\r\r\n}\r\r\n\r\r\nvar sum = 0;\r\r\n\r\r\nfor (var i = 0; i < tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length; i++) {\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == \"\" ||\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount == null\r\r\n\t) {\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\",\r\r\n\t\t\t\"Mandatory field\"\r\r\n\t\t);\r\r\n\t} else if (tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount != \"\") {\r\r\n\t\tcheckNegativeValue(\r\r\n\t\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount,\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].amount\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == \"\" ||\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].date == null\r\r\n\t) {\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[\" + i + \"].date\",\r\r\n\t\t\t\"Mandatory field\"\r\r\n\t\t);\r\r\n\t}\r\r\n\tsum = sum + tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[i].amount;\r\r\n}\r\r\n\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\"\r\r\n);\r\r\ncheckNegativeValue(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\"\r\r\n);\r\r\n\r\r\nif (\r\r\n\t!!tw.local.odcRequest.FinancialDetailsFO.collectableAmount &&\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount < 0\r\r\n)\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount != null &&\r\r\n\t\ttw.local.odcRequest.FinancialDetailsFO.collectableAmount < sum &&\r\r\n\t\ttw.local.odcRequest.BasicDetails.paymentTerms.name != \"001\"\r\r\n\t) {\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.collectableAmount\",\r\r\n\t\t\t\"Sum of all Installment Amounts in a request must be <= Amount Collectable by NBE (اجمالى المبالغ المطلوب تحصيلها)\"\r\r\n\t\t);\r\r\n\t\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\t\"tw.local.odcRequest.FinancialDetailsFO.multiTenorDates[tw.local.odcRequest.FinancialDetailsFO.multiTenorDates.length -1 ].date\",\r\r\n\t\t\t\"Mandatory field\"\r\r\n\t\t);\r\r\n\t}\r\r\n\r\r\nif (\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization > 0 &&\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization > 0\r\r\n) {\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\r\n\t\t\"Partial Avalization isn't allowed\"\r\r\n\t);\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\r\n\t\t\"Partial Avalization isn't allowed\"\r\r\n\t);\r\r\n}\r\r\nvar sumAvalization = parseFloat(0);\r\r\nsumAvalization =\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefAvalization +\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization +\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.amountSight;\r\r\nif (sumAvalization != tw.local.odcRequest.FinancialDetailsFO.collectableAmount) {\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefAvalization\",\r\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\r\n\t);\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountDefNoAvalization\",\r\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\r\n\t);\r\r\n\ttw.system.coachValidation.addValidationError(\r\r\n\t\t\"tw.local.odcRequest.FinancialDetailsFO.amountSight\",\r\r\n\t\t\"please ensure that the following fields is equal to Amount Collectable by NBE:Amount Sight,Amount Deferred - No Avalization,Amount Deferred - Avalization\"\r\r\n\t);\r\r\n}\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.maturityDate,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.maturityDate\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity,\r\r\n\t\"tw.local.odcRequest.FinancialDetailsFO.noOfDaysMaturity\"\r\r\n);\r\r\n\r\r\n//--------------------------------------------Importer Details VALIDATION ----------------------------------\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\"\r\r\n);\r\r\nmandatory(tw.local.odcRequest.ImporterDetails.bank, \"tw.local.odcRequest.ImporterDetails.bank\");\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\"\r\r\n);\r\r\n\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\"\r\r\n);\r\r\n\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerName,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerName\",\r\r\n\t250,\r\r\n\t\"Shouldn't be more than 250 character\",\r\r\n\t\"Importer Name:\" + \"Shouldn't be more than 250 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerAddress\",\r\r\n\t400,\r\r\n\t\"Shouldn't be more than 400 character\",\r\r\n\t\"Importer Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.importerPhoneNo,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.importerPhoneNo\",\r\r\n\t20,\r\r\n\t\"Shouldn't be more than 20 character\",\r\r\n\t\"Importer Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.bank,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bank\",\r\r\n\t250,\r\r\n\t\"Shouldn't be more than 250 character\",\r\r\n\t\"Importer Bank:\" + \"Shouldn't be more than 250 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.BICCode,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.BICCode\",\r\r\n\t11,\r\r\n\t\"Shouldn't be more than 11 character\",\r\r\n\t\"Importer Bank BIC Code:\" + \"Shouldn't be more than 11 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.ibanAccount,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.ibanAccount\",\r\r\n\t40,\r\r\n\t\"Shouldn't be more than 40 character\",\r\r\n\t\"Importer Account(IBAN):\" + \"Shouldn't be more than 40 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.bankAddress,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bankAddress\",\r\r\n\t400,\r\r\n\t\"Shouldn't be more than 400 character\",\r\r\n\t\"Importer Bank Detailed Address:\" + \"Shouldn't be more than 400 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.bankPhoneNo,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.bankPhoneNo\",\r\r\n\t20,\r\r\n\t\"Shouldn't be more than 20 character\",\r\r\n\t\"Importer Bank Telephone No.:\" + \"Shouldn't be more than 20 character\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ImporterDetails.collectingBankReference,\r\r\n\t\"tw.local.odcRequest.ImporterDetails.collectingBankReference\",\r\r\n\t30,\r\r\n\t\"Shouldn't be more than 30 character\",\r\r\n\t\"Collecting Bank Reference:\" + \"Shouldn't be more than 30 character\"\r\r\n);\r\r\nvalidateTab(6, \"Importer Details Tab\");\r\r\n\r\r\n//-----------------------------------------Contract Creation VALIDATION -------------------------------------\r\r\n\r\r\n\r\r\nvalidateString(\r\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\"\r\r\n);\r\r\nmaxLength(\r\r\n\ttw.local.odcRequest.ContractCreation.userReference,\r\r\n\t\"tw.local.odcRequest.ContractCreation.userReference\",\r\r\n\t16,\r\r\n\t\"Shouldn't be more than 16 character\",\r\r\n\t\"Contract Creation User Reference\" + \"Shouldn't be more than 16 character\"\r\r\n);\r\r\n\r\r\nvalidateTab(8, \"Contract Creation Tab\");\r\r\n//---------------------------------------------//Charges and Commissions VALIDATION -------------------------------------\r\r\nfor (var i = 0; i < tw.local.odcRequest.ChargesAndCommissions.length; i++) {\r\r\n\t//Description - Flat Amount\r\r\n\tif (tw.local.odcRequest.ChargesAndCommissions[i].rateType == \"Flat Amount\") {\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount < 0 ||\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount == null\r\r\n\t\t) {\r\r\n\t\t\taddError(\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\r\n\t\t\t\t\"Must be >= 0\"\r\r\n\t\t\t);\r\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0) {\r\r\n\t\t\tvalidateDecimal2(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changeAmount\",\r\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\r\r\n\t\t//Fixed Rate\r\r\n\t} else {\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage < 0 ||\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage == null\r\r\n\t\t) {\r\r\n\t\t\taddError(\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\r\n\t\t\t\t\"Must be >= 0\"\r\r\n\t\t\t);\r\r\n\t\t} else if (tw.local.odcRequest.ChargesAndCommissions[i].changePercentage > 0) {\r\r\n\t\t\tvalidateDecimal2(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].changePercentage,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" + i + \"].changePercentage\",\r\r\n\t\t\t\t\"Must be Decimal(14,2)\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\t}\r\r\n\r\r\n\t//skip validation if waiver or changeAmnt < 0\r\r\n\tif (\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].waiver == false &&\r\r\n\t\ttw.local.odcRequest.ChargesAndCommissions[i].changeAmount > 0\r\r\n\t) {\r\r\n\t\t//GL Account\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.accountClass.value ==\r\r\n\t\t\t\"GL Account\"\r\r\n\t\t) {\r\r\n\t\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].isGLFound == false) {\r\r\n\t\t\t\taddError(\r\r\n\t\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\t\ti +\r\r\n\t\t\t\t\t\t\"].debitedAccount.glAccountNo\",\r\r\n\t\t\t\t\t\"GL Account Not Verified\"\r\r\n\t\t\t\t);\r\r\n\t\t\t}\r\r\n\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.glAccountNo,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.glAccountNo\"\r\r\n\t\t\t);\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.currency.value,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.currency.value\"\r\r\n\t\t\t);\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.branchCode,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.branchCode\"\r\r\n\t\t\t);\r\r\n\r\r\n\t\t\t//Customer Account\r\r\n\t\t} else {\r\r\n\t\t\tmandatory(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount\r\r\n\t\t\t\t\t.customerAccountNo,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAccount.customerAccountNo\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\r\r\n\t\t//DebitedAmount\r\r\n\t\tif (tw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate > 0) {\r\r\n\t\t\tvalidateDecimal(\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.negotiatedExRate,\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAmount.negotiatedExRate\",\r\r\n\t\t\t\t\"Must be Decimal(16,10)\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\r\r\n\t\t//Correct Validation but Waiting confirmation on what to do if GL account\r\r\n\t\tif (\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAmount.amountInAccount >\r\r\n\t\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.balance &&\r\r\n\t\t\ttw.local.odcRequest.ChargesAndCommissions[i].debitedAccount.isOverDraft == false\r\r\n\t\t) {\r\r\n\t\t\taddError(\r\r\n\t\t\t\t\"tw.local.odcRequest.ChargesAndCommissions[\" +\r\r\n\t\t\t\t\ti +\r\r\n\t\t\t\t\t\"].debitedAmount.amountInAccount\",\r\r\n\t\t\t\t\"ERROR: Must be <= Account Balance\"\r\r\n\t\t\t);\r\r\n\t\t}\r\r\n\t}\r\r\n}\r\r\nvalidateTab(9, \"Charges and commissions Tab\");\r\r\n//---------------------------------------------//Parties VALIDATION -------------------------------------\r\r\n/////Drawer Section\r\r\nmandatory(tw.local.odcRequest.Parties.Drawer.partyId, \"tw.local.odcRequest.Parties.Drawer.partyId\");\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.Drawer.partyName,\r\r\n\t\"tw.local.odcRequest.Parties.Drawer.partyName\"\r\r\n);\r\r\n\r\r\n/////Drawee Section\r\r\nmandatory(tw.local.odcRequest.Parties.Drawee.partyId, \"tw.local.odcRequest.Parties.Drawee.partyId\");\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.Drawee.partyName,\r\r\n\t\"tw.local.odcRequest.Parties.Drawee.partyName\"\r\r\n);\r\r\n\r\r\n/////Parties Types (Accountee)\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.partyTypes.partyId,\r\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyId\"\r\r\n);\r\r\nmandatory(\r\r\n\ttw.local.odcRequest.Parties.partyTypes.partyName,\r\r\n\t\"tw.local.odcRequest.Parties.partyTypes.partyName\"\r\r\n);\r\r\n\r\r\nvalidateTab(10, \"Parties Tab\");\r\r\n//---------------------------------------------------------------------------------\r\r\nfunction addError(fieldName, controlMessage, validationMessage, fromMandatory) {\r\r\n\ttw.system.coachValidation.addValidationError(fieldName, controlMessage);\r\r\n\tfromMandatory && mandatoryTriggered\r\r\n\t\t? \"\"\r\r\n\t\t: (tw.local.errorMessage += \"<li>\" + validationMessage + \"</li>\");\r\r\n}\r\r\n\r\r\nfunction minLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n\tif (field != null && field != undefined && field.length < len) {\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction maxLength(field, fieldName, len, controlMessage, validationMessage) {\r\r\n\tif (field.length > len) {\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction mandatory(field, fieldName, message) {\r\r\n\tif (!message) {\r\r\n\t\tmessage = camelCaseToTitle(fieldName) + \" is Mandatory\";\r\r\n\t}\r\r\n\r\r\n\tif (field == null || field == undefined) {\r\r\n\t\taddError(fieldName, message, message, true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\tswitch (typeof field) {\r\r\n\t\t\tcase \"string\":\r\r\n\t\t\t\tif (\r\r\n\t\t\t\t\tfield.trim() != undefined &&\r\r\n\t\t\t\t\tfield.trim() != null &&\r\r\n\t\t\t\t\tfield.trim().length == 0\r\r\n\t\t\t\t) {\r\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\t\t\tcase \"number\":\r\r\n\t\t\t\tif (field == 0.0) {\r\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tif (field < 0) {\r\r\n\t\t\t\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\r\n\t\t\t\t\taddError(fieldName, msg, msg, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t\t\tbreak;\r\r\n\r\r\n\t\t\tdefault:\r\r\n\t\t\t\t// VALIDATE DATE OBJECT\r\r\n\t\t\t\tif (field && field.getTime && isFinite(field.getTime())) {\r\r\n\t\t\t\t} else {\r\r\n\t\t\t\t\taddError(fieldName, message, message, true);\r\r\n\t\t\t\t\tmandatoryTriggered = true;\r\r\n\t\t\t\t\treturn false;\r\r\n\t\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction sumDates(fromDate, toDate, fieldName, controlMessage, validationMessage) {\r\r\n\tif (toDate - fromDate == 0) {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n\taddError(fieldName, controlMessage, validationMessage);\r\r\n\treturn false;\r\r\n}\r\r\n//=========================================================\r\r\nfunction checkNegativeValue(field, fieldName) {\r\r\n\tif (field < 0) {\r\r\n\t\tvar msg = \"Invalid Value, This field can not be negative value.\";\r\r\n\t\taddError(fieldName, msg, msg, true);\r\r\n\t\tmandatoryTriggered = true;\r\r\n\t\treturn false;\r\r\n\t}\r\r\n\treturn true;\r\r\n}\r\r\n\r\r\nfunction validateDecimal(field, fieldName, controlMessage, validationMessage) {\r\r\n\tvar decimalPattern = /^\\d{1,4}(\\.\\d{1,6})?$/;\r\r\n\tif (!decimalPattern.test(field)) {\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n//========================================================================================\r\r\nfunction validateTab(index, tabName) {\r\r\n\tif (tw.system.coachValidation.validationErrors.length > tempLength) {\r\r\n\t\tif (tw.local.errorMessage.length == 0) {\r\r\n\t\t\ttw.local.errorMessage +=\r\r\n\t\t\t\t\"<p>\" + \"Please complete fields in the following tabs:\" + \"</p>\";\r\r\n\t\t}\r\r\n\t\ttw.local.errorMessage += \"<li>\" + tabName + \"</li>\";\r\r\n\t\ttempLength = tw.system.coachValidation.validationErrors.length;\r\r\n\t\ttw.local.invalidTabs[tw.local.invalidTabs.length] = index;\r\r\n\t}\r\r\n}\r\r\n//==============================================================\r\r\nfunction validateDecimal2(field, fieldName, controlMessage, validationMessage) {\r\r\n\tvar decimalPattern = /^\\d{1,12}(\\.\\d{1,2})?$/;\r\r\n\tif (!decimalPattern.test(field)) {\r\r\n\t\t// Decimal is valid\r\r\n\t\taddError(fieldName, controlMessage, validationMessage);\r\r\n\t\treturn false;\r\r\n\t} else {\r\r\n\t\t// Decimal is invalid\r\r\n\t\treturn true;\r\r\n\t}\r\r\n}\r\r\n\r\r\nfunction camelCaseToTitle(camelCase) {\r\r\n\tvar fieldName = camelCase.split(\".\").pop();\r\r\n\t// Convert camelCase to Title Case\r\r\n\tvar titleCase = fieldName.replace(/([A-Z])/g, \" $1\");\r\r\n\t// Uppercase the first character\r\r\n\ttitleCase = titleCase.charAt(0).toUpperCase() + titleCase.slice(1);\r\r\n\treturn titleCase;\r\r\n}\r\r\n\r\r\nfunction validateString(field, fieldName) {\r\r\n      if (!field) return;\r\r\n\t// Regular expression to match only characters (letters)\r\r\n\tvar regex = /^[a-zA-Z]+$/;\r\r\n\r\r\n\t// Test if the inputString matches the regex\r\r\n      if (regex.test(field)) {\r\r\n\t\treturn true;\r\r\n\t} else {\r\r\n\t\taddError(fieldName, \"Numbers aren't allowed\", \"Numbers aren't allowed\");\r\r\n\t\treturn false;\r\r\n\t}\r\r\n}\r\r\n//=================================================================================\r\r\ntw.local.errorMessage != null\r\r\n\t? (tw.local.errorPanelVIS = \"EDITABLE\")\r\r\n\t: (tw.local.errorPanelVIS = \"NONE\");\r\r\n//=================================================================================\r\r\n"}, {"scriptFormat": "text/x-javascript", "default": "2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21", "name": "Client-<PERSON>", "id": "2025.1830251f-d054-49e9-8954-7562c5ab62d9", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1061", "y": "248", "width": "95", "height": "70"}}, "ns16:incoming": "2027.8a760579-2363-4b05-8732-923cffbbb6d7", "ns16:outgoing": "2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21", "ns16:script": "// /DC_Templete1\r\r\n\r\r\nvar lib = bpmext.ui.getView(\"/test_view1\");\r\r\n\r\r\nlib.mandatory(tw.local.deliveryterms, \"tw.local.deliveryterms\");\r\r\n\r\r\n\r\r\nlib.getErrorList( tw.system.coachValidation );"}, {"scriptFormat": "text/x-javascript", "default": "2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e", "name": "Client-Side Script 1", "id": "2025.091f583e-7ba7-4397-816c-2bcd94163432", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "652", "y": "247", "width": "95", "height": "70"}}, "ns16:incoming": "2027.e0e8c13f-f566-4655-843a-caee6c5d70a5", "ns16:outgoing": "2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e", "ns16:script": "tw.system."}], "ns16:intermediateThrowEvent": {"name": "Postpone", "id": "2025.5de5922f-74f9-4f78-83a8-4955a265ceaa", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "78", "y": "-42", "width": "24", "height": "24"}, "ns3:default": "2027.894a9c37-37a6-4ce7-8330-51f32d9c3104", "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.04017f08-d5e0-446b-8673-524fd01f95e7", "ns16:outgoing": "2027.64964c28-a358-40ac-89dd-4f3c8453f4fd", "ns3:postponeTaskEventDefinition": ""}, "ns16:sequenceFlow": [{"sourceRef": "2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7", "targetRef": "2025.c83fdd58-5656-45c1-88be-786d246161b5", "name": "To Valid?", "id": "2027.b683912e-2ca5-4470-8164-22f6918b58b8", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.5de5922f-74f9-4f78-83a8-4955a265ceaa", "targetRef": "2025.4695605b-49e8-461c-8104-b8bc0e52442d", "name": "To Screen", "id": "2027.64964c28-a358-40ac-89dd-4f3c8453f4fd", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topLeft", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.c83fdd58-5656-45c1-88be-786d246161b5", "targetRef": "2025.4695605b-49e8-461c-8104-b8bc0e52442d", "name": "To Screen", "id": "2027.d832440b-ec84-4d61-840a-c6a1e40396d9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topRight", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  >\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.4695605b-49e8-461c-8104-b8bc0e52442d", "targetRef": "2025.5de5922f-74f9-4f78-83a8-4955a265ceaa", "name": "To Postpone", "id": "2027.04017f08-d5e0-446b-8673-524fd01f95e7", "ns16:extensionElements": {"ns3:coachEventBinding": {"id": "60d64e04-001d-4515-9834-e7d4116fa8b5", "ns3:coachEventPath": "DC_Templete1/saveState"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topLeft", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "2025.4695605b-49e8-461c-8104-b8bc0e52442d", "targetRef": "2025.c0ee9098-21c0-4b8a-8f6e-c67c95282ad7", "name": "To End", "id": "2027.8844df5c-8a73-49a4-8c6d-705c0f6ae352", "ns16:extensionElements": {"ns3:coachEventBinding": {"id": "aed447f0-6b9e-43bd-8d65-bd136cad5f16", "ns3:coachEventPath": "DC_Templete1/submit"}, "ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.c83fdd58-5656-45c1-88be-786d246161b5", "targetRef": "2025.3ff0b976-5220-4b64-80db-00db7f9f28c4", "name": "To End", "id": "2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.system.coachValidation.validationErrors.length\t  ==\t  0", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "2025.1830251f-d054-49e9-8954-7562c5ab62d9", "targetRef": "2025.06f37113-06bc-4948-8dc6-92db16860efb", "name": "To Coach", "id": "2027.cb248b78-6fcd-45aa-800c-3ae02e08cd21", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a7b2f73c-28cf-4a6a-9993-f252e17ae70f", "targetRef": "2025.4695605b-49e8-461c-8104-b8bc0e52442d", "name": "To Create", "id": "2027.0a5509ee-552d-42b6-8d67-8a842b8ccec7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomRight", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.06f37113-06bc-4948-8dc6-92db16860efb", "targetRef": "2025.1830251f-d054-49e9-8954-7562c5ab62d9", "name": "To Client-<PERSON>", "id": "2027.8a760579-2363-4b05-8732-923cffbbb6d7", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "false", "fireValidation": "Never"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "true", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}, "ns3:coachEventBinding": {"id": "e7eeff5e-7a0b-46c4-8b9f-6eac4bc4a3f4", "ns3:coachEventPath": "okbutton"}}}, {"sourceRef": "2025.34ee082d-7910-4a7a-8639-b97420edb475", "targetRef": "2025.091f583e-7ba7-4397-816c-2bcd94163432", "name": "To Client-Side Script 1", "id": "2027.e0e8c13f-f566-4655-843a-caee6c5d70a5", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8", "targetRef": "2025.34ee082d-7910-4a7a-8639-b97420edb475", "name": "To Coach", "id": "2027.290f916c-9015-4c0d-8e5f-d4883652dc5d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.091f583e-7ba7-4397-816c-2bcd94163432", "targetRef": "2025.06f37113-06bc-4948-8dc6-92db16860efb", "name": "To Coach", "id": "2027.9a33cde7-fc75-4f77-8597-e2dcafc74b6e", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:endEvent": {"name": "End", "id": "2025.3ff0b976-5220-4b64-80db-00db7f9f28c4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "516", "y": "47", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "<PERSON><PERSON><PERSON>"}}, "ns16:incoming": "2027.7b61eae8-410c-4d32-8fc3-210b16fdbbd0"}, "ns16:callActivity": [{"calledElement": "1.2a65d286-d9ed-4fe1-886f-345dfb9c90e8", "default": "2027.e0e8c13f-f566-4655-843a-caee6c5d70a5", "name": "Test Error", "id": "2025.34ee082d-7910-4a7a-8639-b97420edb475", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "443", "y": "249", "width": "95", "height": "70"}}, "ns16:incoming": "2027.290f916c-9015-4c0d-8e5f-d4883652dc5d", "ns16:outgoing": "2027.e0e8c13f-f566-4655-843a-caee6c5d70a5", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.1b479dcf-4c6c-47e2-8500-5724fb9556e5", "ns16:assignment": {"ns16:from": {"_": "tw.local.input1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.3b87cf43-01bd-4c60-8ca4-bc0dd98091c9", "ns16:assignment": {"ns16:to": {"_": "tw.local.output1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}}}, {"ns16:sourceRef": "2055.92d1f62d-b09f-4f23-8af5-93ff29bdc812", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMsg", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.2d4541bf-5bdb-4e48-8da7-46c9c5ed68a0", "ns16:assignment": {"ns16:to": {"_": "tw.local.output2", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.90d4772d-4081-4a73-a8c2-e7f904511cd6"}}}]}, {"calledElement": "1.5d77055c-98a8-4191-9b74-c7120a5823be", "default": "2027.290f916c-9015-4c0d-8e5f-d4883652dc5d", "name": "Create", "id": "2025.1d36cec1-89ec-40cc-8b7e-c1e3248e32c8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "264", "y": "250", "width": "95", "height": "70"}}, "ns16:outgoing": "2027.290f916c-9015-4c0d-8e5f-d4883652dc5d"}], "ns16:subProcess": {"triggeredByEvent": "true", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Error", "id": "2025.7f290839-c94b-4fcd-8099-fe07b5ec75c5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "687", "y": "77", "width": "95", "height": "70", "color": "#FF7782"}}, "ns16:startEvent": {"isInterrupting": "true", "parallelMultiple": "false", "name": "Start", "id": "2025.debb1ad5-4b9a-4d03-860c-09cb0a594169", "ns16:extensionElements": {"ns3:default": "2027.e2568422-f163-4dbd-8f2c-a782918ddeb3", "ns13:nodeVisualInfo": {"x": "50", "y": "200", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.e2568422-f163-4dbd-8f2c-a782918ddeb3", "ns16:errorEventDefinition": {"ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, "ns16:intermediateThrowEvent": {"name": "Stay on page", "id": "2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "700", "y": "200", "width": "24", "height": "24"}, "ns3:navigationInstructions": {"ns3:targetType": "ToOtherDashboard", "ns3:targetURL": "tw.system.url.bpmDataEndpoint"}}, "ns16:incoming": "2027.b2ae1e6e-b5ff-4332-8f78-193de087a034", "ns3:stayOnPageEventDefinition": ""}, "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "default": "2027.b2ae1e6e-b5ff-4332-8f78-193de087a034", "name": "Client-Side Script 2", "id": "2025.060a5457-7180-4157-85b0-242ad81d0129", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "353", "y": "175", "width": "95", "height": "70"}}, "ns16:incoming": "2027.e2568422-f163-4dbd-8f2c-a782918ddeb3", "ns16:outgoing": "2027.b2ae1e6e-b5ff-4332-8f78-193de087a034", "ns16:script": "tw.local.errorMessage = String( tw.error.data )\r\r\nconsole.log(\"<<ERROR>>\");\r\r\nconsole.log(tw.error)\r\r\n\r\r\ntw.system.url."}, "ns16:sequenceFlow": [{"sourceRef": "2025.debb1ad5-4b9a-4d03-860c-09cb0a594169", "targetRef": "2025.060a5457-7180-4157-85b0-242ad81d0129", "name": "To Client-Side Script 2", "id": "2027.e2568422-f163-4dbd-8f2c-a782918ddeb3", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "2025.060a5457-7180-4157-85b0-242ad81d0129", "targetRef": "2025.09b70d2a-a471-44ed-8ea4-3b79aefc180c", "name": "To Stay on page", "id": "2027.b2ae1e6e-b5ff-4332-8f78-193de087a034", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}]}, "ns3:htmlHeaderTag": {"id": "6d0ba8e0-0d58-434a-89fa-335d347a4c0b", "ns3:tagName": "viewport", "ns3:content": "width=device-width,initial-scale=1.0", "ns3:enabled": "true"}}, "ns3:exposedAs": "NotExposed"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "bb759592-f2f7-40f3-8d2c-1489ab500ca9"}, {"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "a32c52e1-a276-4e4d-81c2-cd41ab5bc551"}, {"epvId": "21.86dc5c1d-931d-46d2-9be1-12397cc9f048", "epvProcessLinkId": "9cd57e59-b7e1-4482-8c4c-588e29e57de4"}, {"epvId": "21.769dc134-1d15-4dd4-a967-c5f61cf352dc", "epvProcessLinkId": "2d23c808-1ed4-4142-8ddb-d0dcba7601f2"}, {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "0632eb96-065b-4557-887c-01e27486455e"}, {"epvId": "21.5726d9e1-b1f3-4bef-b682-5243f17f62c7", "epvProcessLinkId": "3171034a-3c65-4e0d-8aee-052ffb8cbeba"}]}, "ns3:localizationResourceLinks": {"ns3:resourceRef": [{"ns3:resourceBundleGroupID": "50.72059ba3-20f9-4926-b151-02b418301dd4", "ns3:id": "69.31cfd076-ef2f-4737-85d1-a256beb27f48"}, {"ns3:resourceBundleGroupID": "50.41101508-d2e4-4682-b3ef-b9b22266bb5a", "ns3:id": "69.217edee5-1f2d-4b8b-8502-8576e027daf4"}]}}, "ns16:dataInput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = {};\r\nautoObject.initiator = \"\";\r\nautoObject.requestNature = {};\r\nautoObject.requestNature.name = \"\";\r\nautoObject.requestNature.value = \"\";\r\nautoObject.requestType = {};\r\nautoObject.requestType.name = \"\";\r\nautoObject.requestType.value = \"\";\r\nautoObject.cif = \"\";\r\nautoObject.customerName = \"\";\r\nautoObject.parentRequestNo = \"\";\r\nautoObject.requestDate = new Date();\r\nautoObject.ImporterName = \"\";\r\nautoObject.appInfo = {};\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = {};\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.CustomerInfo = {};\r\nautoObject.CustomerInfo.cif = \"\";\r\nautoObject.CustomerInfo.customerName = \"\";\r\nautoObject.CustomerInfo.addressLine1 = \"\";\r\nautoObject.CustomerInfo.addressLine2 = \"\";\r\nautoObject.CustomerInfo.addressLine3 = \"\";\r\nautoObject.CustomerInfo.customerSector = {};\r\nautoObject.CustomerInfo.customerSector.name = \"\";\r\nautoObject.CustomerInfo.customerSector.value = \"\";\r\nautoObject.CustomerInfo.customerType = \"\";\r\nautoObject.CustomerInfo.customerNoCBE = \"\";\r\nautoObject.CustomerInfo.facilityType = {};\r\nautoObject.CustomerInfo.facilityType.name = \"\";\r\nautoObject.CustomerInfo.facilityType.value = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\r\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\r\nautoObject.CustomerInfo.taxCardNo = \"\";\r\nautoObject.CustomerInfo.importCardNo = \"\";\r\nautoObject.CustomerInfo.initiationHub = \"\";\r\nautoObject.CustomerInfo.country = \"\";\r\nautoObject.BasicDetails = {};\r\nautoObject.BasicDetails.requestNature = \"\";\r\nautoObject.BasicDetails.requestType = \"\";\r\nautoObject.BasicDetails.parentRequestNo = \"\";\r\nautoObject.BasicDetails.requestState = \"\";\r\nautoObject.BasicDetails.flexCubeContractNo = \"\";\r\nautoObject.BasicDetails.contractStage = \"\";\r\nautoObject.BasicDetails.exportPurpose = {};\r\nautoObject.BasicDetails.exportPurpose.name = \"\";\r\nautoObject.BasicDetails.exportPurpose.value = \"\";\r\nautoObject.BasicDetails.paymentTerms = {};\r\nautoObject.BasicDetails.paymentTerms.name = \"\";\r\nautoObject.BasicDetails.paymentTerms.value = \"\";\r\nautoObject.BasicDetails.productCategory = {};\r\nautoObject.BasicDetails.productCategory.name = \"\";\r\nautoObject.BasicDetails.productCategory.value = \"\";\r\nautoObject.BasicDetails.commodityDescription = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin = {};\r\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\r\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\r\nautoObject.BasicDetails.Bills = [];\r\nautoObject.BasicDetails.Bills[0] = {};\r\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\r\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new Date();\r\nautoObject.BasicDetails.Invoice = [];\r\nautoObject.BasicDetails.Invoice[0] = {};\r\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\r\nautoObject.BasicDetails.Invoice[0].invoiceDate = new Date();\r\nautoObject.GeneratedDocumentInfo = {};\r\nautoObject.GeneratedDocumentInfo.customerName = \"\";\r\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTerms = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = [];\r\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructions = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.Instructions = [];\r\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructions = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = [];\r\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = {};\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\r\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder = {};\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new Date();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new Date();\r\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = [];\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = {};\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\r\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\r\nautoObject.FinancialDetailsBR = {};\r\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\r\nautoObject.FinancialDetailsBR.currency = {};\r\nautoObject.FinancialDetailsBR.currency.name = \"\";\r\nautoObject.FinancialDetailsBR.currency.value = \"\";\r\nautoObject.FinancialDetailsBR.maxCollectionDate = new Date();\r\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = {};\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount = {};\r\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\r\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts = [];\r\nautoObject.FinancialDetailsBR.listOfAccounts[0] = {};\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\r\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\r\nautoObject.FcCollections = {};\r\nautoObject.FcCollections.currency = {};\r\nautoObject.FcCollections.currency.name = \"\";\r\nautoObject.FcCollections.currency.value = \"\";\r\nautoObject.FcCollections.standardExchangeRate = 0.0;\r\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\r\nautoObject.FcCollections.fromDate = new Date();\r\nautoObject.FcCollections.ToDate = new Date();\r\nautoObject.FcCollections.accountNo = {};\r\nautoObject.FcCollections.accountNo.name = \"\";\r\nautoObject.FcCollections.accountNo.value = \"\";\r\nautoObject.FcCollections.retrievedTransactions = [];\r\nautoObject.FcCollections.retrievedTransactions[0] = {};\r\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.selectedTransactions = [];\r\nautoObject.FcCollections.selectedTransactions[0] = {};\r\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].postingDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].valueDate = new Date();\r\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\r\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\r\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\r\nautoObject.FcCollections.isReversed = false;\r\nautoObject.FcCollections.usedAmount = 0.0;\r\nautoObject.FcCollections.calculatedAmount = 0.0;\r\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\r\nautoObject.FcCollections.listOfAccounts = [];\r\nautoObject.FcCollections.listOfAccounts[0] = {};\r\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\r\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\r\nautoObject.FinancialDetailsFO = {};\r\nautoObject.FinancialDetailsFO.discount = 0.0;\r\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\r\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\r\nautoObject.FinancialDetailsFO.amountSight = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\r\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\r\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\r\nautoObject.FinancialDetailsFO.maturityDate = new Date();\r\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\r\nautoObject.FinancialDetailsFO.referenceNo = \"\";\r\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\r\nautoObject.FinancialDetailsFO.executionHub = {};\r\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\r\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\r\nautoObject.FinancialDetailsFO.multiTenorDates = [];\r\nautoObject.FinancialDetailsFO.multiTenorDates[0] = {};\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new Date();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].rebate = 0.0;\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].matDate = new Date();\r\nautoObject.FinancialDetailsFO.multiTenorDates[0].tenorDays = 0;\r\nautoObject.FinancialDetailsFO.rebate = 0.0;\r\nautoObject.ImporterDetails = {};\r\nautoObject.ImporterDetails.importerName = \"\";\r\nautoObject.ImporterDetails.importerCountry = {};\r\nautoObject.ImporterDetails.importerCountry.name = \"\";\r\nautoObject.ImporterDetails.importerCountry.value = \"\";\r\nautoObject.ImporterDetails.importerAddress = \"\";\r\nautoObject.ImporterDetails.importerPhoneNo = \"\";\r\nautoObject.ImporterDetails.bank = \"\";\r\nautoObject.ImporterDetails.BICCode = \"\";\r\nautoObject.ImporterDetails.ibanAccount = \"\";\r\nautoObject.ImporterDetails.bankCountry = {};\r\nautoObject.ImporterDetails.bankCountry.name = \"\";\r\nautoObject.ImporterDetails.bankCountry.value = \"\";\r\nautoObject.ImporterDetails.bankAddress = \"\";\r\nautoObject.ImporterDetails.bankPhoneNo = \"\";\r\nautoObject.ImporterDetails.collectingBankReference = \"\";\r\nautoObject.ProductShipmentDetails = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification = {};\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\r\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\r\nautoObject.ProductShipmentDetails.shippingDate = new Date();\r\nautoObject.ProductShipmentDetails.shipmentMethod = {};\r\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\r\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\r\nautoObject.OdcCollection = {};\r\nautoObject.OdcCollection.amount = 0.0;\r\nautoObject.OdcCollection.currency = \"\";\r\nautoObject.OdcCollection.informCADAboutTheCollection = false;\r\nautoObject.ReversalReason = {};\r\nautoObject.ReversalReason.reversalReason = \"\";\r\nautoObject.ReversalReason.closureReason = \"\";\r\nautoObject.ReversalReason.executionHub = {};\r\nautoObject.ReversalReason.executionHub.name = \"\";\r\nautoObject.ReversalReason.executionHub.value = \"\";\r\nautoObject.ContractCreation = {};\r\nautoObject.ContractCreation.productCode = {};\r\nautoObject.ContractCreation.productCode.name = \"\";\r\nautoObject.ContractCreation.productCode.value = \"\";\r\nautoObject.ContractCreation.productDescription = \"\";\r\nautoObject.ContractCreation.Stage = \"\";\r\nautoObject.ContractCreation.userReference = \"\";\r\nautoObject.ContractCreation.sourceReference = \"\";\r\nautoObject.ContractCreation.currency = \"\";\r\nautoObject.ContractCreation.amount = 0.0;\r\nautoObject.ContractCreation.baseDate = new Date();\r\nautoObject.ContractCreation.valueDate = new Date();\r\nautoObject.ContractCreation.tenorDays = 0;\r\nautoObject.ContractCreation.transitDays = 0;\r\nautoObject.ContractCreation.maturityDate = new Date();\r\nautoObject.Parties = {};\r\nautoObject.Parties.Drawer = {};\r\nautoObject.Parties.Drawer.partyId = \"\";\r\nautoObject.Parties.Drawer.partyName = \"\";\r\nautoObject.Parties.Drawer.country = \"\";\r\nautoObject.Parties.Drawer.Language = \"\";\r\nautoObject.Parties.Drawer.Reference = \"\";\r\nautoObject.Parties.Drawer.address1 = \"\";\r\nautoObject.Parties.Drawer.address2 = \"\";\r\nautoObject.Parties.Drawer.address3 = \"\";\r\nautoObject.Parties.Drawee = {};\r\nautoObject.Parties.Drawee.partyId = \"\";\r\nautoObject.Parties.Drawee.partyName = \"\";\r\nautoObject.Parties.Drawee.country = \"\";\r\nautoObject.Parties.Drawee.Language = \"\";\r\nautoObject.Parties.Drawee.Reference = \"\";\r\nautoObject.Parties.Drawee.address1 = \"\";\r\nautoObject.Parties.Drawee.address2 = \"\";\r\nautoObject.Parties.Drawee.address3 = \"\";\r\nautoObject.Parties.collectingBank = {};\r\nautoObject.Parties.collectingBank.id = \"\";\r\nautoObject.Parties.collectingBank.name = \"\";\r\nautoObject.Parties.collectingBank.country = \"\";\r\nautoObject.Parties.collectingBank.language = \"\";\r\nautoObject.Parties.collectingBank.reference = \"\";\r\nautoObject.Parties.collectingBank.address1 = \"\";\r\nautoObject.Parties.collectingBank.address2 = \"\";\r\nautoObject.Parties.collectingBank.address3 = \"\";\r\nautoObject.Parties.collectingBank.cif = \"\";\r\nautoObject.Parties.collectingBank.media = \"\";\r\nautoObject.Parties.collectingBank.address = \"\";\r\nautoObject.Parties.partyTypes = {};\r\nautoObject.Parties.partyTypes.partyCIF = \"\";\r\nautoObject.Parties.partyTypes.partyId = \"\";\r\nautoObject.Parties.partyTypes.partyName = \"\";\r\nautoObject.Parties.partyTypes.country = \"\";\r\nautoObject.Parties.partyTypes.language = \"\";\r\nautoObject.Parties.partyTypes.refrence = \"\";\r\nautoObject.Parties.partyTypes.address1 = \"\";\r\nautoObject.Parties.partyTypes.address2 = \"\";\r\nautoObject.Parties.partyTypes.address3 = \"\";\r\nautoObject.Parties.partyTypes.partyType = {};\r\nautoObject.Parties.partyTypes.partyType.name = \"\";\r\nautoObject.Parties.partyTypes.partyType.value = \"\";\r\nautoObject.Parties.caseInNeed = {};\r\nautoObject.Parties.caseInNeed.partyCIF = \"\";\r\nautoObject.Parties.caseInNeed.partyId = \"\";\r\nautoObject.Parties.caseInNeed.partyName = \"\";\r\nautoObject.Parties.caseInNeed.country = \"\";\r\nautoObject.Parties.caseInNeed.language = \"\";\r\nautoObject.Parties.caseInNeed.refrence = \"\";\r\nautoObject.Parties.caseInNeed.address1 = \"\";\r\nautoObject.Parties.caseInNeed.address2 = \"\";\r\nautoObject.Parties.caseInNeed.address3 = \"\";\r\nautoObject.Parties.caseInNeed.partyType = {};\r\nautoObject.Parties.caseInNeed.partyType.name = \"\";\r\nautoObject.Parties.caseInNeed.partyType.value = \"\";\r\nautoObject.ChargesAndCommissions = [];\r\nautoObject.ChargesAndCommissions[0] = {};\r\nautoObject.ChargesAndCommissions[0].component = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency = {};\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].waiver = false;\r\nautoObject.ChargesAndCommissions[0].debitedAccount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = {};\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\r\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\r\nautoObject.ChargesAndCommissions[0].debitedAmount = {};\r\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject.ChargesAndCommissions[0].rateType = \"\";\r\nautoObject.ChargesAndCommissions[0].description = \"\";\r\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\r\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\r\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\r\nautoObject.ChargesAndCommissions[0].isGLFound = false;\r\nautoObject.ChargesAndCommissions[0].glVerifyMSG = \"\";\r\nautoObject.ContractLiquidation = {};\r\nautoObject.ContractLiquidation.liqAmount = 0.0;\r\nautoObject.ContractLiquidation.liqCurrency = \"\";\r\nautoObject.ContractLiquidation.debitValueDate = new Date();\r\nautoObject.ContractLiquidation.creditValueDate = new Date();\r\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\r\nautoObject.ContractLiquidation.debitedAccountName = \"\";\r\nautoObject.ContractLiquidation.creditedAccount = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass = {};\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency = {};\r\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\r\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\r\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\r\nautoObject.ContractLiquidation.creditedAmount = {};\r\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\r\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\r\nautoObject.complianceApproval = false;\r\nautoObject.stepLog = {};\r\nautoObject.stepLog.startTime = new Date();\r\nautoObject.stepLog.endTime = new Date();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.actions = [];\r\nautoObject.actions[0] = \"\";\r\nautoObject.attachmentDetails = {};\r\nautoObject.attachmentDetails.folderID = \"\";\r\nautoObject.attachmentDetails.ecmProperties = {};\r\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\r\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties = [];\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = {};\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\r\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\r\nautoObject.attachmentDetails.attachment = [];\r\nautoObject.attachmentDetails.attachment[0] = {};\r\nautoObject.attachmentDetails.attachment[0].name = \"\";\r\nautoObject.attachmentDetails.attachment[0].description = \"\";\r\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\r\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\r\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\r\nautoObject.complianceComments = [];\r\nautoObject.complianceComments[0] = {};\r\nautoObject.complianceComments[0].startTime = new Date();\r\nautoObject.complianceComments[0].endTime = new Date();\r\nautoObject.complianceComments[0].userName = \"\";\r\nautoObject.complianceComments[0].role = \"\";\r\nautoObject.complianceComments[0].step = \"\";\r\nautoObject.complianceComments[0].action = \"\";\r\nautoObject.complianceComments[0].comment = \"\";\r\nautoObject.complianceComments[0].terminateReason = \"\";\r\nautoObject.complianceComments[0].returnReason = \"\";\r\nautoObject.History = [];\r\nautoObject.History[0] = {};\r\nautoObject.History[0].startTime = new Date();\r\nautoObject.History[0].endTime = new Date();\r\nautoObject.History[0].userName = \"\";\r\nautoObject.History[0].role = \"\";\r\nautoObject.History[0].step = \"\";\r\nautoObject.History[0].action = \"\";\r\nautoObject.History[0].comment = \"\";\r\nautoObject.History[0].terminateReason = \"\";\r\nautoObject.History[0].returnReason = \"\";\r\nautoObject.documentSource = {};\r\nautoObject.documentSource.name = \"\";\r\nautoObject.documentSource.value = \"\";\r\nautoObject.folderID = \"\";\r\nautoObject.isLiquidated = false;\r\nautoObject.requestNo = \"\";\r\nautoObject.folderPath = \"\";\r\nautoObject.templateDocID = \"\";\r\nautoObject.requestID = 0;\r\nautoObject.customerAndPartyAccountList = [];\r\nautoObject.customerAndPartyAccountList[0] = {};\r\nautoObject.customerAndPartyAccountList[0].accountNO = \"\";\r\nautoObject.customerAndPartyAccountList[0].currencyCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].branchCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balance = 0.0;\r\nautoObject.customerAndPartyAccountList[0].typeCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerName = \"\";\r\nautoObject.customerAndPartyAccountList[0].customerNo = \"\";\r\nautoObject.customerAndPartyAccountList[0].frozen = false;\r\nautoObject.customerAndPartyAccountList[0].dormant = false;\r\nautoObject.customerAndPartyAccountList[0].noDebit = false;\r\nautoObject.customerAndPartyAccountList[0].noCredit = false;\r\nautoObject.customerAndPartyAccountList[0].postingAllowed = false;\r\nautoObject.customerAndPartyAccountList[0].ibanAccountNumber = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountClassCode = \"\";\r\nautoObject.customerAndPartyAccountList[0].balanceType = \"\";\r\nautoObject.customerAndPartyAccountList[0].accountStatus = \"\";\r\nautoObject.tradeFoComment = \"\";\r\nautoObject.exeHubMkrComment = \"\";\r\nautoObject.compcheckerComment = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "regeneratedRemittanceLetterTitleVIS", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e"}, {"name": "fromExeChecker", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.21028eba-aa2f-4b93-806b-78b695c14ddf"}], "ns16:dataOutput": [{"name": "odcRequest", "itemSubjectRef": "itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9", "isCollection": "false", "id": "2055.021ed4ee-8b5e-423d-8672-bd<PERSON><PERSON>ed27d8"}, {"name": "compApprovalInit", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.********-2486-426c-8701-7fc1c235c6a3"}, {"name": "lastAction", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991"}], "ns16:inputSet": {"id": "20372ac9-5465-4e48-b85b-6617c2365423"}, "ns16:outputSet": {"id": "0c19fa57-ebfe-46ea-a3b8-02de562e4688"}}}}}, "link": {"name": "Untitled", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d247280b-01cb-41cc-a99d-bf3710f72a5f", "processId": "1.9321de6e-e6a5-435e-accb-0fbbd129c48a", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599", "2025.ee5a06e9-5fb5-4670-9b94-f6070af5f599"], "endStateId": "Out", "toProcessItemId": ["2025.6b1114d2-2fd1-421a-bc5f-7dc264e40eb4", "2025.6b1114d2-2fd1-421a-bc5f-7dc264e40eb4"], "guid": "124522bd-34b1-4f74-afcb-8bf97ebee9ef", "versionId": "e05d7668-eef0-431b-b8a5-8f808ad05820", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "rightCenter", "portType": "2"}}}}}, "subType": "10", "hasDetails": true}