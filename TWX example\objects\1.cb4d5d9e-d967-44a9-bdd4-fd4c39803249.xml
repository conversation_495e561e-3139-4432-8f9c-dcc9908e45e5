<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249" name="Create amend audit service">
        <lastModified>1698318251227</lastModified>
        <lastModifiedBy>fatma</lastModifiedBy>
        <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.f9db43af-0083-4085-8e05-252c7bc1525c</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7089</guid>
        <versionId>2f282072-cc7e-4f91-a680-b115235ae194</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:266f6f4955d8489f:7b0b9b81:18b66504d45:3d74" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.60dc1e7b-3895-478c-82bb-f29568774e84"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"ba1977c7-1543-4f12-8801-3fae56f9e277"},{"incoming":["d7d4d18a-bd71-4c6b-860c-41797c800e2f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7087"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"01516e5f-85fc-45a4-8336-888f9e7ec3b1"},{"targetRef":"f9db43af-0083-4085-8e05-252c7bc1525c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To parent request no not empty?","declaredType":"sequenceFlow","id":"2027.60dc1e7b-3895-478c-82bb-f29568774e84","sourceRef":"ba1977c7-1543-4f12-8801-3fae56f9e277"},{"startQuantity":1,"outgoing":["eda2dc71-b433-4552-89dd-e2801b59e36d"],"incoming":["47bfefe4-7514-4e92-8fa7-053ab8bf00c4"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":285,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Insert query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"6cccd4e9-cc7c-4062-85b2-a10af0901632","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\"\r\n+\"values('\"+tw.local.odcRequest.requestNo+\"','\"+tw.local.odcRequest.requestNature.name+\"','\"+tw.local.odcRequest.requestType.name+\"',sysdate,'\"+tw.local.odcRequest.BasicDetails.requestState+\"','\"+tw.local.odcRequest.appInfo.status+\"','\"+tw.local.odcRequest.appInfo.subStatus+\"','\"+tw.local.odcRequest.BasicDetails.contractStage+\"','\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\"','\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\"','\"+tw.local.odcRequest.BasicDetails.productCategory.value+\"','\"+tw.local.odcRequest.BasicDetails.commodityDescription+\"','\"+false+\"','\"+tw.local.odcRequest.CustomerInfo.cif+\"','\"+tw.local.odcRequest.CustomerInfo.customerName+\"','\"+tw.local.odcRequest.FcCollections.currency.name+\"','\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\"',sysdate,'\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\"','\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\"','\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\"','\"+tw.local.odcRequest.appInfo.stepName+\"','\"+tw.local.odcRequest.appInfo.instanceID+\"');\"\r\n\/\/'\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\"'"]}},{"targetRef":"1615fc68-5350-4fd2-8dee-a6115f36a883","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Execute statement","declaredType":"sequenceFlow","id":"eda2dc71-b433-4552-89dd-e2801b59e36d","sourceRef":"6cccd4e9-cc7c-4062-85b2-a10af0901632"},{"startQuantity":1,"outgoing":["d7d4d18a-bd71-4c6b-860c-41797c800e2f"],"incoming":["eda2dc71-b433-4552-89dd-e2801b59e36d","6b928f1a-fc75-4129-8300-e6290bacf2e9"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":512,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Execute statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.query"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["null"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"1615fc68-5350-4fd2-8dee-a6115f36a883","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.sqlResults"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"01516e5f-85fc-45a4-8336-888f9e7ec3b1","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"d7d4d18a-bd71-4c6b-860c-41797c800e2f","sourceRef":"1615fc68-5350-4fd2-8dee-a6115f36a883"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"query","isCollection":false,"declaredType":"dataObject","id":"2056.c1a9df3f-d507-4b27-8766-04c7f72168aa"},{"outgoing":["47bfefe4-7514-4e92-8fa7-053ab8bf00c4","7d2d0ca6-22ca-439c-801f-3d5f9504608f"],"incoming":["2027.60dc1e7b-3895-478c-82bb-f29568774e84"],"default":"47bfefe4-7514-4e92-8fa7-053ab8bf00c4","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":100,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"is Update?","declaredType":"exclusiveGateway","id":"f9db43af-0083-4085-8e05-252c7bc1525c"},{"targetRef":"6cccd4e9-cc7c-4062-85b2-a10af0901632","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"No","declaredType":"sequenceFlow","id":"47bfefe4-7514-4e92-8fa7-053ab8bf00c4","sourceRef":"f9db43af-0083-4085-8e05-252c7bc1525c"},{"startQuantity":1,"outgoing":["6b928f1a-fc75-4129-8300-e6290bacf2e9"],"incoming":["7d2d0ca6-22ca-439c-801f-3d5f9504608f"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":283,"y":184,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":[]},"name":"Update query","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"98e51491-7304-42a1-8af6-bc22fe4af2ef","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,parentRequestNo,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)\"\r\n+\"values('\"+tw.local.odcRequest.requestNo+\"','\"+tw.local.odcRequest.requestNature.name+\"','\"+tw.local.odcRequest.requestType.name+\"',sysdate,'\"+tw.local.odcRequest.BasicDetails.requestState+\"','\"+tw.local.odcRequest.appInfo.status+\"','\"+tw.local.odcRequest.appInfo.subStatus+\"','\"+tw.local.odcRequest.parentRequestNo+\"','\"+tw.local.odcRequest.BasicDetails.contractStage+\"','\"+tw.local.odcRequest.BasicDetails.exportPurpose.value+\"','\"+tw.local.odcRequest.BasicDetails.paymentTerms.value+\"','\"+tw.local.odcRequest.BasicDetails.productCategory.value+\"','\"+tw.local.odcRequest.BasicDetails.commodityDescription+\"','\"+false+\"','\"+tw.local.odcRequest.CustomerInfo.cif+\"','\"+tw.local.odcRequest.CustomerInfo.customerName+\"','\"+tw.local.odcRequest.FcCollections.currency.name+\"','\"+tw.local.odcRequest.FcCollections.standardExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+\"','\"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.currency.name+\"',sysdate,'\"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+\"','\"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+\"','\"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+\"','\"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+\"','\"+tw.local.odcRequest.appInfo.stepName+\"','\"+tw.local.odcRequest.appInfo.instanceID+\"');\"\r\n\/\/'\"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +\"'"]}},{"targetRef":"98e51491-7304-42a1-8af6-bc22fe4af2ef","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.requestNature != null &amp;&amp; tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"Yes","declaredType":"sequenceFlow","id":"7d2d0ca6-22ca-439c-801f-3d5f9504608f","sourceRef":"f9db43af-0083-4085-8e05-252c7bc1525c"},{"targetRef":"1615fc68-5350-4fd2-8dee-a6115f36a883","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Execute statement","declaredType":"sequenceFlow","id":"6b928f1a-fc75-4129-8300-e6290bacf2e9","sourceRef":"98e51491-7304-42a1-8af6-bc22fe4af2ef"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNumber","isCollection":false,"declaredType":"dataObject","id":"2056.cc74ca5d-a7f0-4f86-8b0e-7ee1692b27ab"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"sqlResults","isCollection":true,"declaredType":"dataObject","id":"2056.c2286d23-6bc2-4bba-8cbf-36aada72929a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\nautoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();\nautoObject[0].sql = \"\";\nautoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\nautoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\nautoObject[0].parameters[0].value = null;\nautoObject[0].parameters[0].type = \"\";\nautoObject[0].parameters[0].mode = \"\";\nautoObject[0].maxRows = 0;\nautoObject"}]},"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"queryList","isCollection":true,"declaredType":"dataObject","id":"2056.166981a3-1293-4eac-84d5-ca8099ce579f"},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":949,"y":107,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Execute multiple statements","dataInputAssociation":[{"targetRef":"2055.9695b715-db44-410b-8a74-65cf1d31d8ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","declaredType":"TFormalExpression","content":["tw.local.queryList"]}}]},{"targetRef":"2055.a634f531-c979-476c-91db-a17bf5e55c90","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"ab3ca23b-bc15-4cc6-86f4-5443af54d24b","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.sqlResults"]}}],"sourceRef":["2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e"]}],"calledElement":"1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7"},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":742,"y":98,"declaredType":"TNodeVisualInfo","height":70}]},"name":"multiple statements","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"27152ee5-08fa-43cf-8397-429860e1b017","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.queryList = new tw.object.listOf.toolkit.TWSYS.SQLStatement();\r\ntw.local.queryList[0] = new tw.object.toolkit.TWSYS.SQLStatement();\r\ntw.local.queryList[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[0].value = tw.local.odcRequest.requestNo;\r\n\r\ntw.local.queryList[0].parameters[1] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[1].value = tw.local.odcRequest.requestNature.name;\r\n\r\ntw.local.queryList[0].parameters[2] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[2].value = tw.local.odcRequest.requestType.name;\r\n\r\ntw.local.queryList[0].parameters[3] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[3].value = tw.local.odcRequest.requestDate;\r\n\r\n\r\ntw.local.queryList[0].parameters[4] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[4].value = tw.local.odcRequest.BasicDetails.requestState;\r\n\r\ntw.local.queryList[0].parameters[5] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[5].value = tw.local.odcRequest.appInfo.status;\r\n\r\n\r\ntw.local.queryList[0].parameters[6] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[6].value = tw.local.odcRequest.appInfo.subStatus;\r\n\r\ntw.local.queryList[0].parameters[7] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[7].value = tw.local.odcRequest.BasicDetails.contractStage;\r\n\r\ntw.local.queryList[0].parameters[8] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[8].value = tw.local.odcRequest.BasicDetails.exportPurpose.value;\r\n\r\ntw.local.queryList[0].parameters[9] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[9].value = tw.local.odcRequest.BasicDetails.paymentTerms.value;\r\n\r\ntw.local.queryList[0].parameters[10] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[10].value = tw.local.odcRequest.BasicDetails.productCategory.value;\r\n\r\ntw.local.queryList[0].parameters[11] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[11].value = tw.local.odcRequest.BasicDetails.commodityDescription;\r\n\r\ntw.local.queryList[0].parameters[12] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[12].value = false;\r\n\r\ntw.local.queryList[0].parameters[13] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[13].value = tw.local.odcRequest.CustomerInfo.cif;\r\n\r\ntw.local.queryList[0].parameters[14] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[14].value = tw.local.odcRequest.CustomerInfo.customerName;\r\n\r\ntw.local.queryList[0].parameters[15] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[15].value = tw.local.odcRequest.FcCollections.currency.name;\r\n\r\ntw.local.queryList[0].parameters[16] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[16].value = tw.local.odcRequest.FcCollections.standardExchangeRate;\r\n\r\ntw.local.queryList[0].parameters[17] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[17].value = tw.local.odcRequest.FcCollections.negotiatedExchangeRate;\r\n\r\ntw.local.queryList[0].parameters[18] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[18].value = tw.local.odcRequest.FcCollections.totalAllocatedAmount;\r\n\r\ntw.local.queryList[0].parameters[19] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[19].value = tw.local.odcRequest.FinancialDetailsBR.currency.name;\r\n\r\ntw.local.queryList[0].parameters[20] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[20].value = sysdate;\r\n\r\ntw.local.queryList[0].parameters[21] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[21].value = tw.local.odcRequest.FinancialDetailsBR.documentAmount;\r\n\r\ntw.local.queryList[0].parameters[22] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[22].value = tw.local.odcRequest.FinancialDetailsBR.amountAdvanced;\r\n\r\ntw.local.queryList[0].parameters[23] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[23].value = tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value;\r\n\r\ntw.local.queryList[0].parameters[24] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[24].value = tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value;\r\n\r\ntw.local.queryList[0].parameters[25] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[25].value = tw.local.odcRequest.appInfo.stepName;\r\n\r\ntw.local.queryList[0].parameters[26] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[0].parameters[26].value = tw.local.odcRequest.appInfo.instanceID;\r\n\r\ntw.local.queryList[0].sql = tw.local.query = \"insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);\";\r\ntw.local.queryList[0].maxRows = -1;\r\n\r\nif(tw.local.odcRequest.BasicDetails.Bills.listLength != 0)\r\n{\r\n\ttw.local.queryList[1] = new tw.object.toolkit.TWSYS.SQLStatement();\r\n\ttw.local.queryList[1].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();\r\n\tfor(var i=0;i&lt;tw.local.)\r\n\t{\r\n\t\r\n\t}\r\n}\r\ntw.local.queryList[1].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();\r\ntw.local.queryList[1].parameters[0].value = tw.local.odcRequest.requestNo;\r\n\r\ntw.local.queryList[1].sql = tw.local.query = \"insert into ODC_REQUESTINFO ();\";\r\ntw.local.queryList[1].maxRows = -1;\r\n"]}},{"startQuantity":1,"extensionElements":{"nodeVisualInfo":[{"width":95,"x":691,"y":225,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Insert parties","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"937313a5-c693-4dbe-87ff-5bc4931cc1c4","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sqlStatements = new tw.object.listOf.SQLStatement();\r\nvar par = 0;\r\nvar ind = 0;\r\nfunction initSql(){\r\n\tvar sqlsttmnt = new tw.object.SQLStatement();\r\n\tsqlsttmnt.parameters = new tw.object.listOf.SQLParameter();\r\n\treturn sqlsttmnt;\r\n}\r\n\r\ntw.local.sqlStatements[ind] = initSql();\r\n\/\/ adding the sql statement abdelrahman wrote temporarily - to be restructured\r\n\r\ntw.local.sqlStatements[ind++].sql = tw.local.query;\r\n\r\n\/\/inserting parties\r\n\/\/to be changed after changing parties to list)\r\n\/\/for(var p=0 ; p&lt; 3 ; p++){\r\ntw.local.sqlStatements[ind] = initSql();\r\ntw.local.sqlStatements[ind].sql =\"Insert into ODC_PARTIESINFO (PARTYID , PARTYTYPE, CIF, REQUESRID) VALUES (?, ?, ?, ? );\"\r\n\r\ntw.local.sqlStatements[ind].parameters[par++] = \r\n \r\n\/\/ }\r\n"]}},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.ee764bf6-eb5f-4208-8320-828cbf64c02e"},{"itemSubjectRef":"itm.12.3990d8b4-983d-4250-bd06-3600f527fed0","name":"sqlStatements","isCollection":true,"declaredType":"dataObject","id":"2056.ce6180b9-2169-4336-865f-c352a900e59c"}],"laneSet":[{"id":"2e483409-9a1d-4bdc-8d79-e232083cae7e","lane":[{"flowNodeRef":["ba1977c7-1543-4f12-8801-3fae56f9e277","01516e5f-85fc-45a4-8336-888f9e7ec3b1","6cccd4e9-cc7c-4062-85b2-a10af0901632","1615fc68-5350-4fd2-8dee-a6115f36a883","f9db43af-0083-4085-8e05-252c7bc1525c","98e51491-7304-42a1-8af6-bc22fe4af2ef","ab3ca23b-bc15-4cc6-86f4-5443af54d24b","27152ee5-08fa-43cf-8397-429860e1b017","937313a5-c693-4dbe-87ff-5bc4931cc1c4"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"48b52bdb-ee44-455d-8fc1-49b2264b356f","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Create amend audit service","declaredType":"process","id":"1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.5726d9e1-b1f3-4bef-b682-5243f17f62c7","epvProcessLinkId":"c58e638c-e5f0-4675-8baa-96b8fd0432f8","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{"dataInputRefs":["2055.96457aad-7a8d-49ff-80b0-2a989d85568f"]}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"new\";\nautoObject.requestNature.value = \"new\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.InstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();\nautoObject.GeneratedDocumentInfo.destinationFolder.objectId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.serverName = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.path = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.parentId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.name = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.createdBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();\nautoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = \"\";\nautoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ReversalReason.executionHub.name = \"\";\nautoObject.ReversalReason.executionHub.value = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.party = new tw.object.listOf.partyTypes();\nautoObject.Parties.party[0] = new tw.object.partyTypes();\nautoObject.Parties.party[0].partyCIF = \"\";\nautoObject.Parties.party[0].partyId = \"\";\nautoObject.Parties.party[0].partyName = \"\";\nautoObject.Parties.party[0].country = \"\";\nautoObject.Parties.party[0].language = \"\";\nautoObject.Parties.party[0].refrence = \"\";\nautoObject.Parties.party[0].address1 = \"\";\nautoObject.Parties.party[0].address2 = \"\";\nautoObject.Parties.party[0].address3 = \"\";\nautoObject.Parties.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.party[0].partyType.name = \"\";\nautoObject.Parties.party[0].partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.attachmentDetails.attachment[0].numOfOriginals = 0;\nautoObject.attachmentDetails.attachment[0].numOfCopies = 0;\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject.requestNo = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2055.96457aad-7a8d-49ff-80b0-2a989d85568f"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.96457aad-7a8d-49ff-80b0-2a989d85568f</processParameterId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "new";
autoObject.requestNature.value = "new";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.party = new tw.object.listOf.partyTypes();
autoObject.Parties.party[0] = new tw.object.partyTypes();
autoObject.Parties.party[0].partyCIF = "";
autoObject.Parties.party[0].partyId = "";
autoObject.Parties.party[0].partyName = "";
autoObject.Parties.party[0].country = "";
autoObject.Parties.party[0].language = "";
autoObject.Parties.party[0].refrence = "";
autoObject.Parties.party[0].address1 = "";
autoObject.Parties.party[0].address2 = "";
autoObject.Parties.party[0].address3 = "";
autoObject.Parties.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.party[0].partyType.name = "";
autoObject.Parties.party[0].partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>9454736b-3870-4ad5-b58a-9e1f3fd18884</guid>
            <versionId>fe4cd7db-7451-41ca-b9bf-0dd61005b05a</versionId>
        </processParameter>
        <processVariable name="query">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c1a9df3f-d507-4b27-8766-04c7f72168aa</processVariableId>
            <description isNull="true" />
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>66390904-be44-4491-81e1-195ae5b27bd8</guid>
            <versionId>399c1ae2-c283-4617-9911-a6f26af0cf7b</versionId>
        </processVariable>
        <processVariable name="requestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cc74ca5d-a7f0-4f86-8b0e-7ee1692b27ab</processVariableId>
            <description isNull="true" />
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>ada00d44-87b1-4860-a36c-54e36d26da57</guid>
            <versionId>415b72d2-d7d6-4f03-b7ab-eb68cee6b9ce</versionId>
        </processVariable>
        <processVariable name="sqlResults">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c2286d23-6bc2-4bba-8cbf-36aada72929a</processVariableId>
            <description isNull="true" />
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>f3451482-fa61-4dab-93ff-f57436a5c49c</guid>
            <versionId>eab80d88-b2c6-4d64-8d93-0e69c1ff287f</versionId>
        </processVariable>
        <processVariable name="queryList">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.166981a3-1293-4eac-84d5-ca8099ce579f</processVariableId>
            <description isNull="true" />
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>31c2a901-d34b-4e01-9cda-9a0408a1c633</guid>
            <versionId>d88c8abd-a6a9-4640-be7d-3311ce8dedd0</versionId>
        </processVariable>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ee764bf6-eb5f-4208-8320-828cbf64c02e</processVariableId>
            <description isNull="true" />
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>fb367b97-fe79-4a09-b826-11b9422b90f2</guid>
            <versionId>123999d5-1e8a-4d80-b3f0-aab83fa852f0</versionId>
        </processVariable>
        <processVariable name="sqlStatements">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.ce6180b9-2169-4336-865f-c352a900e59c</processVariableId>
            <description isNull="true" />
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <namespace>2</namespace>
            <seq>6</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3990d8b4-983d-4250-bd06-3600f527fed0</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>0f4d2e16-1cfc-4a3d-892c-be3cc98f594f</guid>
            <versionId>b2d085c6-82f5-4bf0-939c-3bddd8ecc0a3</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.98e51491-7304-42a1-8af6-bc22fe4af2ef</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>Update query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.a72299b3-805e-4be2-910f-468768ccddf6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:18b7</guid>
            <versionId>1355e904-0b7a-4b9c-8f3a-a2ed40769d7b</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.a5b7fca0-77ac-4dd2-97cf-5c7d29a48867</processItemPrePostId>
                <processItemId>2025.98e51491-7304-42a1-8af6-bc22fe4af2ef</processItemId>
                <location>1</location>
                <script isNull="true" />
                <guid>bf066835-6dff-422f-9abb-257e1b67d560</guid>
                <versionId>71eb4d62-df79-4b8c-9140-621bf364210e</versionId>
            </processPrePosts>
            <layoutData x="283" y="184">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.a72299b3-805e-4be2-910f-468768ccddf6</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.query = "insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,parentRequestNo,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)"&#xD;
+"values('"+tw.local.odcRequest.requestNo+"','"+tw.local.odcRequest.requestNature.name+"','"+tw.local.odcRequest.requestType.name+"',sysdate,'"+tw.local.odcRequest.BasicDetails.requestState+"','"+tw.local.odcRequest.appInfo.status+"','"+tw.local.odcRequest.appInfo.subStatus+"','"+tw.local.odcRequest.parentRequestNo+"','"+tw.local.odcRequest.BasicDetails.contractStage+"','"+tw.local.odcRequest.BasicDetails.exportPurpose.value+"','"+tw.local.odcRequest.BasicDetails.paymentTerms.value+"','"+tw.local.odcRequest.BasicDetails.productCategory.value+"','"+tw.local.odcRequest.BasicDetails.commodityDescription+"','"+false+"','"+tw.local.odcRequest.CustomerInfo.cif+"','"+tw.local.odcRequest.CustomerInfo.customerName+"','"+tw.local.odcRequest.FcCollections.currency.name+"','"+tw.local.odcRequest.FcCollections.standardExchangeRate+"','"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+"','"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.currency.name+"',sysdate,'"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+"','"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+"','"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+"','"+tw.local.odcRequest.appInfo.stepName+"','"+tw.local.odcRequest.appInfo.instanceID+"');"&#xD;
//'"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +"'</script>
                <isRule>false</isRule>
                <guid>f5f5c181-90a5-4018-8a80-d1af846596ab</guid>
                <versionId>a67c079d-90cb-4a5f-89f0-1be8292c64c0</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f9db43af-0083-4085-8e05-252c7bc1525c</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>is Update?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.5aaf2482-10f3-4c39-9953-360815311d0c</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:1820</guid>
            <versionId>348f5661-818e-471e-a135-66b9d3b2ab5c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="100" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.5aaf2482-10f3-4c39-9953-360815311d0c</switchId>
                <guid>93dfdda5-6c02-498c-a3eb-31a2fc640d9a</guid>
                <versionId>ea5bdd63-4fd7-4eae-93b8-4760521e69a9</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.f6187c31-bc6d-4236-a38f-f420f2b2e72f</switchConditionId>
                    <switchId>3013.5aaf2482-10f3-4c39-9953-360815311d0c</switchId>
                    <seq>1</seq>
                    <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:3d73</endStateId>
                    <condition>tw.local.odcRequest.requestNature != null &amp;&amp; tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest</condition>
                    <guid>82e42e36-45fc-4cf1-9f06-a2794224bc4c</guid>
                    <versionId>fa486926-1a3e-466f-8cee-4f3ce21a3909</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1615fc68-5350-4fd2-8dee-a6115f36a883</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>Execute statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.b454fbd5-43cb-49bd-9e10-fea81a634496</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-691</guid>
            <versionId>3a5160fe-8a18-407e-85eb-7d64ea4422f0</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="512" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.b454fbd5-43cb-49bd-9e10-fea81a634496</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>10949f11-0664-44d4-96e3-6a6f90450382</guid>
                <versionId>df1aa44d-741d-43ac-8746-3a6d8943566a</versionId>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.be29e4b1-1e89-4406-b5c8-f681d3031f45</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.b454fbd5-43cb-49bd-9e10-fea81a634496</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlResults</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>80819e27-6b52-42b4-99b5-1017920e0ee8</guid>
                    <versionId>51991954-de03-4b3c-8a6d-d784c1a90404</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e391d95c-7704-459b-9509-3ea0fcee6e90</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.b454fbd5-43cb-49bd-9e10-fea81a634496</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>null</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>6a7ae82c-7130-48ea-b0e2-f051dc904b7f</guid>
                    <versionId>523056ce-755f-4233-bdb2-38032333d51e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.09ba5c47-34ca-4981-8f1e-f37c83ab7510</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.b454fbd5-43cb-49bd-9e10-fea81a634496</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>4f8f796d-3486-4541-ab59-440b6c4976ec</guid>
                    <versionId>7b461df4-c82c-4c01-a7f5-0ecbedd89cb8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8007495c-1f83-4ad5-bd81-7d47277155d1</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.b454fbd5-43cb-49bd-9e10-fea81a634496</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ddf39a99-a8ed-4611-a98c-e167f115cab9</guid>
                    <versionId>dffed26e-fd34-486a-a462-63b71c56be5d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1062f248-d609-41ca-8209-213be2ea9cf6</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.b454fbd5-43cb-49bd-9e10-fea81a634496</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.query</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>63a274f1-afef-46cb-809c-dac48197c5c3</guid>
                    <versionId>fcf80c05-6fa7-49a5-b64e-a011643f5a8c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.6cccd4e9-cc7c-4062-85b2-a10af0901632</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>Insert query</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.*************-45d5-b0a5-c5ed45eb12fc</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-692</guid>
            <versionId>868a2ff7-1961-4190-a53d-************</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="285" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.*************-45d5-b0a5-c5ed45eb12fc</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.query = "insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)"&#xD;
+"values('"+tw.local.odcRequest.requestNo+"','"+tw.local.odcRequest.requestNature.name+"','"+tw.local.odcRequest.requestType.name+"',sysdate,'"+tw.local.odcRequest.BasicDetails.requestState+"','"+tw.local.odcRequest.appInfo.status+"','"+tw.local.odcRequest.appInfo.subStatus+"','"+tw.local.odcRequest.BasicDetails.contractStage+"','"+tw.local.odcRequest.BasicDetails.exportPurpose.value+"','"+tw.local.odcRequest.BasicDetails.paymentTerms.value+"','"+tw.local.odcRequest.BasicDetails.productCategory.value+"','"+tw.local.odcRequest.BasicDetails.commodityDescription+"','"+false+"','"+tw.local.odcRequest.CustomerInfo.cif+"','"+tw.local.odcRequest.CustomerInfo.customerName+"','"+tw.local.odcRequest.FcCollections.currency.name+"','"+tw.local.odcRequest.FcCollections.standardExchangeRate+"','"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+"','"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.currency.name+"',sysdate,'"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+"','"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+"','"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+"','"+tw.local.odcRequest.appInfo.stepName+"','"+tw.local.odcRequest.appInfo.instanceID+"');"&#xD;
//'"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +"'</script>
                <isRule>false</isRule>
                <guid>cb4650c6-4697-4f48-99c2-b7210ac64a38</guid>
                <versionId>270f6a53-e69e-4c5b-9e7e-fb078e774e50</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ab3ca23b-bc15-4cc6-86f4-5443af54d24b</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>Execute multiple statements</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.2072bba9-e300-4f9a-b4ed-570ba97f0607</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:aff098473ecd546d:1d42df0a:18b1b2b8841:1c83</guid>
            <versionId>883498f9-31ca-41f6-83ca-65752a741093</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="949" y="107">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.2072bba9-e300-4f9a-b4ed-570ba97f0607</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7</attachedProcessRef>
                <guid>f72277f0-eb14-44ca-9468-335b4d452ef8</guid>
                <versionId>a282b8f5-3cd5-46ca-b4a5-f89bbc35491d</versionId>
                <parameterMapping name="sqlStatements">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.497a896b-cbc7-4ffd-8c2c-203f7100497e</parameterMappingId>
                    <processParameterId>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</processParameterId>
                    <parameterMappingParentId>3012.2072bba9-e300-4f9a-b4ed-570ba97f0607</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.queryList</value>
                    <classRef>/12.3990d8b4-983d-4250-bd06-3600f527fed0</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>a50bd39c-f1a4-49f0-978c-13aeaa0f2130</guid>
                    <versionId>4df9d5f6-e4ff-4edb-acf0-249d802f795e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e7fc6dab-6c48-481c-b999-acaf2ddc22b6</parameterMappingId>
                    <processParameterId>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</processParameterId>
                    <parameterMappingParentId>3012.2072bba9-e300-4f9a-b4ed-570ba97f0607</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sqlResults</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>62c19069-0119-4491-a097-462ff40e817b</guid>
                    <versionId>610b29e6-752d-48c7-a726-432037f52f66</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.9bb11a65-31fd-4707-bc23-889871f2d3cc</parameterMappingId>
                    <processParameterId>2055.a634f531-c979-476c-91db-a17bf5e55c90</processParameterId>
                    <parameterMappingParentId>3012.2072bba9-e300-4f9a-b4ed-570ba97f0607</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>93fa5b09-bb77-429f-a301-c08c9ca17314</guid>
                    <versionId>a40d1287-bc9b-4168-afbe-70135dd29317</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.27152ee5-08fa-43cf-8397-429860e1b017</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>multiple statements</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.830687d0-62fc-486f-ad98-f675dedbdc5a</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:4a051bcdb927c971:-1aedb8c3:18b41a2c30b:-87a</guid>
            <versionId>9b76e029-1cf6-4605-bba1-31125c7f767e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="742" y="98">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.830687d0-62fc-486f-ad98-f675dedbdc5a</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.queryList = new tw.object.listOf.toolkit.TWSYS.SQLStatement();&#xD;
tw.local.queryList[0] = new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
tw.local.queryList[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[0].value = tw.local.odcRequest.requestNo;&#xD;
&#xD;
tw.local.queryList[0].parameters[1] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[1].value = tw.local.odcRequest.requestNature.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[2] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[2].value = tw.local.odcRequest.requestType.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[3] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[3].value = tw.local.odcRequest.requestDate;&#xD;
&#xD;
&#xD;
tw.local.queryList[0].parameters[4] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[4].value = tw.local.odcRequest.BasicDetails.requestState;&#xD;
&#xD;
tw.local.queryList[0].parameters[5] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[5].value = tw.local.odcRequest.appInfo.status;&#xD;
&#xD;
&#xD;
tw.local.queryList[0].parameters[6] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[6].value = tw.local.odcRequest.appInfo.subStatus;&#xD;
&#xD;
tw.local.queryList[0].parameters[7] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[7].value = tw.local.odcRequest.BasicDetails.contractStage;&#xD;
&#xD;
tw.local.queryList[0].parameters[8] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[8].value = tw.local.odcRequest.BasicDetails.exportPurpose.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[9] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[9].value = tw.local.odcRequest.BasicDetails.paymentTerms.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[10] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[10].value = tw.local.odcRequest.BasicDetails.productCategory.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[11] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[11].value = tw.local.odcRequest.BasicDetails.commodityDescription;&#xD;
&#xD;
tw.local.queryList[0].parameters[12] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[12].value = false;&#xD;
&#xD;
tw.local.queryList[0].parameters[13] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[13].value = tw.local.odcRequest.CustomerInfo.cif;&#xD;
&#xD;
tw.local.queryList[0].parameters[14] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[14].value = tw.local.odcRequest.CustomerInfo.customerName;&#xD;
&#xD;
tw.local.queryList[0].parameters[15] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[15].value = tw.local.odcRequest.FcCollections.currency.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[16] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[16].value = tw.local.odcRequest.FcCollections.standardExchangeRate;&#xD;
&#xD;
tw.local.queryList[0].parameters[17] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[17].value = tw.local.odcRequest.FcCollections.negotiatedExchangeRate;&#xD;
&#xD;
tw.local.queryList[0].parameters[18] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[18].value = tw.local.odcRequest.FcCollections.totalAllocatedAmount;&#xD;
&#xD;
tw.local.queryList[0].parameters[19] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[19].value = tw.local.odcRequest.FinancialDetailsBR.currency.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[20] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[20].value = sysdate;&#xD;
&#xD;
tw.local.queryList[0].parameters[21] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[21].value = tw.local.odcRequest.FinancialDetailsBR.documentAmount;&#xD;
&#xD;
tw.local.queryList[0].parameters[22] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[22].value = tw.local.odcRequest.FinancialDetailsBR.amountAdvanced;&#xD;
&#xD;
tw.local.queryList[0].parameters[23] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[23].value = tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[24] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[24].value = tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[25] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[25].value = tw.local.odcRequest.appInfo.stepName;&#xD;
&#xD;
tw.local.queryList[0].parameters[26] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[26].value = tw.local.odcRequest.appInfo.instanceID;&#xD;
&#xD;
tw.local.queryList[0].sql = tw.local.query = "insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";&#xD;
tw.local.queryList[0].maxRows = -1;&#xD;
&#xD;
if(tw.local.odcRequest.BasicDetails.Bills.listLength != 0)&#xD;
{&#xD;
	tw.local.queryList[1] = new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	tw.local.queryList[1].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	for(var i=0;i&lt;tw.local.)&#xD;
	{&#xD;
	&#xD;
	}&#xD;
}&#xD;
tw.local.queryList[1].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[1].parameters[0].value = tw.local.odcRequest.requestNo;&#xD;
&#xD;
tw.local.queryList[1].sql = tw.local.query = "insert into ODC_REQUESTINFO ();";&#xD;
tw.local.queryList[1].maxRows = -1;&#xD;
</script>
                <isRule>false</isRule>
                <guid>ba792b92-25bf-4c2a-ac2f-6b3b9c570e62</guid>
                <versionId>99a52c8b-bffe-4404-b4e3-6ff89af5d2ef</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.01516e5f-85fc-45a4-8336-888f9e7ec3b1</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.6d34dc3a-ef29-4073-a9ed-d18c491b5874</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7087</guid>
            <versionId>a34ac8d6-2071-4aa0-b1ff-780f6afe6112</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.6d34dc3a-ef29-4073-a9ed-d18c491b5874</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>a46c6783-eb6e-4e7e-b593-5da82ffb7dcd</guid>
                <versionId>32f8adde-c7b3-4b09-9862-bf00d44a935c</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.937313a5-c693-4dbe-87ff-5bc4931cc1c4</processItemId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <name>Insert parties</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.d936282c-cc4b-4420-9a8f-fe42e580b5f7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:266f6f4955d8489f:7b0b9b81:18b5b81b903:-49e0</guid>
            <versionId>bdfbc7f9-4213-4f5a-8271-421e0274259f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="691" y="225">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.d936282c-cc4b-4420-9a8f-fe42e580b5f7</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var par = 0;&#xD;
var ind = 0;&#xD;
function initSql(){&#xD;
	var sqlsttmnt = new tw.object.SQLStatement();&#xD;
	sqlsttmnt.parameters = new tw.object.listOf.SQLParameter();&#xD;
	return sqlsttmnt;&#xD;
}&#xD;
&#xD;
tw.local.sqlStatements[ind] = initSql();&#xD;
// adding the sql statement abdelrahman wrote temporarily - to be restructured&#xD;
&#xD;
tw.local.sqlStatements[ind++].sql = tw.local.query;&#xD;
&#xD;
//inserting parties&#xD;
//to be changed after changing parties to list)&#xD;
//for(var p=0 ; p&lt; 3 ; p++){&#xD;
tw.local.sqlStatements[ind] = initSql();&#xD;
tw.local.sqlStatements[ind].sql ="Insert into ODC_PARTIESINFO (PARTYID , PARTYTYPE, CIF, REQUESRID) VALUES (?, ?, ?, ? );"&#xD;
&#xD;
tw.local.sqlStatements[ind].parameters[par++] = &#xD;
 &#xD;
// }&#xD;
</script>
                <isRule>false</isRule>
                <guid>b4cd7c47-1399-497c-8809-691d1a82cda3</guid>
                <versionId>f9278a22-7f40-4a1b-a419-0d208d06a626</versionId>
            </TWComponent>
        </item>
        <EPV_PROCESS_LINK>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <epvProcessLinkId>2058.70b7c9bb-1800-424e-abbc-fde9aaad04bd</epvProcessLinkId>
            <epvId>/21.5726d9e1-b1f3-4bef-b682-5243f17f62c7</epvId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <guid>04895450-0edd-40ea-a731-8afd7c54dfb8</guid>
            <versionId>77e68121-d9da-4fd6-ba71-a8427e40f941</versionId>
        </EPV_PROCESS_LINK>
        <startingProcessItemId>2025.f9db43af-0083-4085-8e05-252c7bc1525c</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Create amend audit service" id="1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:epvProcessLinks>
                                
                                
                                <ns3:epvProcessLinkRef epvId="21.5726d9e1-b1f3-4bef-b682-5243f17f62c7" epvProcessLinkId="c58e638c-e5f0-4675-8baa-96b8fd0432f8" />
                                
                            
                            </ns3:epvProcessLinks>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2055.96457aad-7a8d-49ff-80b0-2a989d85568f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "new";
autoObject.requestNature.value = "new";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTermsExtra[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.InstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructionsExtra[0] = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.GeneratedDocumentInfo.destinationFolder = new tw.object.toolkit.SYSCM.ECMFolder();
autoObject.GeneratedDocumentInfo.destinationFolder.objectId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.serverName = "";
autoObject.GeneratedDocumentInfo.destinationFolder.repositoryId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.path = "";
autoObject.GeneratedDocumentInfo.destinationFolder.parentId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.name = "";
autoObject.GeneratedDocumentInfo.destinationFolder.creationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.createdBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.lastModificationDate = new TWDate();
autoObject.GeneratedDocumentInfo.destinationFolder.lastModifiedBy = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties = new tw.object.listOf.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0] = new tw.object.toolkit.SYSCM.ECMProperty();
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].objectTypeId = "";
autoObject.GeneratedDocumentInfo.destinationFolder.properties[0].value = null;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ReversalReason.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ReversalReason.executionHub.name = "";
autoObject.ReversalReason.executionHub.value = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.party = new tw.object.listOf.partyTypes();
autoObject.Parties.party[0] = new tw.object.partyTypes();
autoObject.Parties.party[0].partyCIF = "";
autoObject.Parties.party[0].partyId = "";
autoObject.Parties.party[0].partyName = "";
autoObject.Parties.party[0].country = "";
autoObject.Parties.party[0].language = "";
autoObject.Parties.party[0].refrence = "";
autoObject.Parties.party[0].address1 = "";
autoObject.Parties.party[0].address2 = "";
autoObject.Parties.party[0].address3 = "";
autoObject.Parties.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.party[0].partyType.name = "";
autoObject.Parties.party[0].partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.attachmentDetails.attachment[0].numOfOriginals = 0;
autoObject.attachmentDetails.attachment[0].numOfCopies = 0;
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject.requestNo = "";
autoObject</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.96457aad-7a8d-49ff-80b0-2a989d85568f</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="2e483409-9a1d-4bdc-8d79-e232083cae7e">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="48b52bdb-ee44-455d-8fc1-49b2264b356f" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>ba1977c7-1543-4f12-8801-3fae56f9e277</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>01516e5f-85fc-45a4-8336-888f9e7ec3b1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6cccd4e9-cc7c-4062-85b2-a10af0901632</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1615fc68-5350-4fd2-8dee-a6115f36a883</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f9db43af-0083-4085-8e05-252c7bc1525c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>98e51491-7304-42a1-8af6-bc22fe4af2ef</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ab3ca23b-bc15-4cc6-86f4-5443af54d24b</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>27152ee5-08fa-43cf-8397-429860e1b017</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>937313a5-c693-4dbe-87ff-5bc4931cc1c4</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="ba1977c7-1543-4f12-8801-3fae56f9e277">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.60dc1e7b-3895-478c-82bb-f29568774e84</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="01516e5f-85fc-45a4-8336-888f9e7ec3b1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:463bd94950db5ff9:-60e5c5b3:18b0ed6e574:-7087</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>d7d4d18a-bd71-4c6b-860c-41797c800e2f</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="ba1977c7-1543-4f12-8801-3fae56f9e277" targetRef="f9db43af-0083-4085-8e05-252c7bc1525c" name="To parent request no not empty?" id="2027.60dc1e7b-3895-478c-82bb-f29568774e84">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert query" id="6cccd4e9-cc7c-4062-85b2-a10af0901632">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="285" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>47bfefe4-7514-4e92-8fa7-053ab8bf00c4</ns16:incoming>
                        
                        
                        <ns16:outgoing>eda2dc71-b433-4552-89dd-e2801b59e36d</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.query = "insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)"&#xD;
+"values('"+tw.local.odcRequest.requestNo+"','"+tw.local.odcRequest.requestNature.name+"','"+tw.local.odcRequest.requestType.name+"',sysdate,'"+tw.local.odcRequest.BasicDetails.requestState+"','"+tw.local.odcRequest.appInfo.status+"','"+tw.local.odcRequest.appInfo.subStatus+"','"+tw.local.odcRequest.BasicDetails.contractStage+"','"+tw.local.odcRequest.BasicDetails.exportPurpose.value+"','"+tw.local.odcRequest.BasicDetails.paymentTerms.value+"','"+tw.local.odcRequest.BasicDetails.productCategory.value+"','"+tw.local.odcRequest.BasicDetails.commodityDescription+"','"+false+"','"+tw.local.odcRequest.CustomerInfo.cif+"','"+tw.local.odcRequest.CustomerInfo.customerName+"','"+tw.local.odcRequest.FcCollections.currency.name+"','"+tw.local.odcRequest.FcCollections.standardExchangeRate+"','"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+"','"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.currency.name+"',sysdate,'"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+"','"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+"','"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+"','"+tw.local.odcRequest.appInfo.stepName+"','"+tw.local.odcRequest.appInfo.instanceID+"');"&#xD;
//'"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +"'</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="6cccd4e9-cc7c-4062-85b2-a10af0901632" targetRef="1615fc68-5350-4fd2-8dee-a6115f36a883" name="To Execute statement" id="eda2dc71-b433-4552-89dd-e2801b59e36d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Execute statement" id="1615fc68-5350-4fd2-8dee-a6115f36a883">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="512" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>eda2dc71-b433-4552-89dd-e2801b59e36d</ns16:incoming>
                        
                        
                        <ns16:incoming>6b928f1a-fc75-4129-8300-e6290bacf2e9</ns16:incoming>
                        
                        
                        <ns16:outgoing>d7d4d18a-bd71-4c6b-860c-41797c800e2f</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.query</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">null</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.sqlResults</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="1615fc68-5350-4fd2-8dee-a6115f36a883" targetRef="01516e5f-85fc-45a4-8336-888f9e7ec3b1" name="To End" id="d7d4d18a-bd71-4c6b-860c-41797c800e2f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="query" id="2056.c1a9df3f-d507-4b27-8766-04c7f72168aa" />
                    
                    
                    <ns16:exclusiveGateway default="47bfefe4-7514-4e92-8fa7-053ab8bf00c4" name="is Update?" id="f9db43af-0083-4085-8e05-252c7bc1525c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="100" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.60dc1e7b-3895-478c-82bb-f29568774e84</ns16:incoming>
                        
                        
                        <ns16:outgoing>47bfefe4-7514-4e92-8fa7-053ab8bf00c4</ns16:outgoing>
                        
                        
                        <ns16:outgoing>7d2d0ca6-22ca-439c-801f-3d5f9504608f</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="f9db43af-0083-4085-8e05-252c7bc1525c" targetRef="6cccd4e9-cc7c-4062-85b2-a10af0901632" name="No" id="47bfefe4-7514-4e92-8fa7-053ab8bf00c4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Update query" id="98e51491-7304-42a1-8af6-bc22fe4af2ef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="283" y="184" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>7d2d0ca6-22ca-439c-801f-3d5f9504608f</ns16:incoming>
                        
                        
                        <ns16:outgoing>6b928f1a-fc75-4129-8300-e6290bacf2e9</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.query = "insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,parentRequestNo,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber)"&#xD;
+"values('"+tw.local.odcRequest.requestNo+"','"+tw.local.odcRequest.requestNature.name+"','"+tw.local.odcRequest.requestType.name+"',sysdate,'"+tw.local.odcRequest.BasicDetails.requestState+"','"+tw.local.odcRequest.appInfo.status+"','"+tw.local.odcRequest.appInfo.subStatus+"','"+tw.local.odcRequest.parentRequestNo+"','"+tw.local.odcRequest.BasicDetails.contractStage+"','"+tw.local.odcRequest.BasicDetails.exportPurpose.value+"','"+tw.local.odcRequest.BasicDetails.paymentTerms.value+"','"+tw.local.odcRequest.BasicDetails.productCategory.value+"','"+tw.local.odcRequest.BasicDetails.commodityDescription+"','"+false+"','"+tw.local.odcRequest.CustomerInfo.cif+"','"+tw.local.odcRequest.CustomerInfo.customerName+"','"+tw.local.odcRequest.FcCollections.currency.name+"','"+tw.local.odcRequest.FcCollections.standardExchangeRate+"','"+tw.local.odcRequest.FcCollections.negotiatedExchangeRate+"','"+tw.local.odcRequest.FcCollections.totalAllocatedAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.currency.name+"',sysdate,'"+tw.local.odcRequest.FinancialDetailsBR.documentAmount+"','"+tw.local.odcRequest.FinancialDetailsBR.amountAdvanced+"','"+tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value+"','"+tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value+"','"+tw.local.odcRequest.appInfo.stepName+"','"+tw.local.odcRequest.appInfo.instanceID+"');"&#xD;
//'"+tw.local.odcRequest.FinancialDetailsBR.maxCollectionDate +"'</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="f9db43af-0083-4085-8e05-252c7bc1525c" targetRef="98e51491-7304-42a1-8af6-bc22fe4af2ef" name="Yes" id="7d2d0ca6-22ca-439c-801f-3d5f9504608f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.odcRequest.requestNature != null &amp;&amp; tw.local.odcRequest.requestNature.value == tw.epv.RequestNature.UpdateRequest</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="98e51491-7304-42a1-8af6-bc22fe4af2ef" targetRef="1615fc68-5350-4fd2-8dee-a6115f36a883" name="To Execute statement" id="6b928f1a-fc75-4129-8300-e6290bacf2e9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestNumber" id="2056.cc74ca5d-a7f0-4f86-8b0e-7ee1692b27ab" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="sqlResults" id="2056.c2286d23-6bc2-4bba-8cbf-36aada72929a" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="queryList" id="2056.166981a3-1293-4eac-84d5-ca8099ce579f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="false">var autoObject = new tw.object.listOf.toolkit.TWSYS.SQLStatement();
autoObject[0] = new tw.object.toolkit.TWSYS.SQLStatement();
autoObject[0].sql = "";
autoObject[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();
autoObject[0].parameters[0].value = null;
autoObject[0].parameters[0].type = "";
autoObject[0].parameters[0].mode = "";
autoObject[0].maxRows = 0;
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:callActivity calledElement="1.012e1a27-1a81-4e0e-97d9-8a2d8976dda7" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Execute multiple statements" id="ab3ca23b-bc15-4cc6-86f4-5443af54d24b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="949" y="107" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9695b715-db44-410b-8a74-65cf1d31d8ab</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0">tw.local.queryList</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a634f531-c979-476c-91db-a17bf5e55c90</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.921cdfd7-ed55-4dca-a71d-e4401a90c57e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.sqlResults</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="multiple statements" id="27152ee5-08fa-43cf-8397-429860e1b017">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="742" y="98" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:script>tw.local.queryList = new tw.object.listOf.toolkit.TWSYS.SQLStatement();&#xD;
tw.local.queryList[0] = new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
tw.local.queryList[0].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[0].value = tw.local.odcRequest.requestNo;&#xD;
&#xD;
tw.local.queryList[0].parameters[1] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[1].value = tw.local.odcRequest.requestNature.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[2] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[2].value = tw.local.odcRequest.requestType.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[3] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[3].value = tw.local.odcRequest.requestDate;&#xD;
&#xD;
&#xD;
tw.local.queryList[0].parameters[4] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[4].value = tw.local.odcRequest.BasicDetails.requestState;&#xD;
&#xD;
tw.local.queryList[0].parameters[5] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[5].value = tw.local.odcRequest.appInfo.status;&#xD;
&#xD;
&#xD;
tw.local.queryList[0].parameters[6] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[6].value = tw.local.odcRequest.appInfo.subStatus;&#xD;
&#xD;
tw.local.queryList[0].parameters[7] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[7].value = tw.local.odcRequest.BasicDetails.contractStage;&#xD;
&#xD;
tw.local.queryList[0].parameters[8] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[8].value = tw.local.odcRequest.BasicDetails.exportPurpose.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[9] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[9].value = tw.local.odcRequest.BasicDetails.paymentTerms.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[10] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[10].value = tw.local.odcRequest.BasicDetails.productCategory.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[11] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[11].value = tw.local.odcRequest.BasicDetails.commodityDescription;&#xD;
&#xD;
tw.local.queryList[0].parameters[12] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[12].value = false;&#xD;
&#xD;
tw.local.queryList[0].parameters[13] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[13].value = tw.local.odcRequest.CustomerInfo.cif;&#xD;
&#xD;
tw.local.queryList[0].parameters[14] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[14].value = tw.local.odcRequest.CustomerInfo.customerName;&#xD;
&#xD;
tw.local.queryList[0].parameters[15] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[15].value = tw.local.odcRequest.FcCollections.currency.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[16] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[16].value = tw.local.odcRequest.FcCollections.standardExchangeRate;&#xD;
&#xD;
tw.local.queryList[0].parameters[17] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[17].value = tw.local.odcRequest.FcCollections.negotiatedExchangeRate;&#xD;
&#xD;
tw.local.queryList[0].parameters[18] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[18].value = tw.local.odcRequest.FcCollections.totalAllocatedAmount;&#xD;
&#xD;
tw.local.queryList[0].parameters[19] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[19].value = tw.local.odcRequest.FinancialDetailsBR.currency.name;&#xD;
&#xD;
tw.local.queryList[0].parameters[20] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[20].value = sysdate;&#xD;
&#xD;
tw.local.queryList[0].parameters[21] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[21].value = tw.local.odcRequest.FinancialDetailsBR.documentAmount;&#xD;
&#xD;
tw.local.queryList[0].parameters[22] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[22].value = tw.local.odcRequest.FinancialDetailsBR.amountAdvanced;&#xD;
&#xD;
tw.local.queryList[0].parameters[23] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[23].value = tw.local.odcRequest.FinancialDetailsBR.chargesAndCommissionsAccount.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[24] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[24].value = tw.local.odcRequest.FinancialDetailsBR.collectionAccount.value;&#xD;
&#xD;
tw.local.queryList[0].parameters[25] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[25].value = tw.local.odcRequest.appInfo.stepName;&#xD;
&#xD;
tw.local.queryList[0].parameters[26] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[0].parameters[26].value = tw.local.odcRequest.appInfo.instanceID;&#xD;
&#xD;
tw.local.queryList[0].sql = tw.local.query = "insert into ODC_REQUESTINFO (requestNo,requestNature,requestType,requestDate,requestState,requestStatus,subStatus,contractStage,exportPurpose,paymentTerms,productCategory,commodityDescription,isliquidated,cif,customerName,collectionCurrency,standardExRate,negotiatedExRate,totalAllocatedAmount,requestCurrency,maximumCollectionDate,documentAmount,advanceAmount,chargesAccount,collectionAccount,currentStepName,BPMInstanceNumber) values (?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?,?);";&#xD;
tw.local.queryList[0].maxRows = -1;&#xD;
&#xD;
if(tw.local.odcRequest.BasicDetails.Bills.listLength != 0)&#xD;
{&#xD;
	tw.local.queryList[1] = new tw.object.toolkit.TWSYS.SQLStatement();&#xD;
	tw.local.queryList[1].parameters = new tw.object.listOf.toolkit.TWSYS.SQLParameter();&#xD;
	for(var i=0;i&lt;tw.local.)&#xD;
	{&#xD;
	&#xD;
	}&#xD;
}&#xD;
tw.local.queryList[1].parameters[0] = new tw.object.toolkit.TWSYS.SQLParameter();&#xD;
tw.local.queryList[1].parameters[0].value = tw.local.odcRequest.requestNo;&#xD;
&#xD;
tw.local.queryList[1].sql = tw.local.query = "insert into ODC_REQUESTINFO ();";&#xD;
tw.local.queryList[1].maxRows = -1;&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Insert parties" id="937313a5-c693-4dbe-87ff-5bc4931cc1c4">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="691" y="225" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:script>tw.local.sqlStatements = new tw.object.listOf.SQLStatement();&#xD;
var par = 0;&#xD;
var ind = 0;&#xD;
function initSql(){&#xD;
	var sqlsttmnt = new tw.object.SQLStatement();&#xD;
	sqlsttmnt.parameters = new tw.object.listOf.SQLParameter();&#xD;
	return sqlsttmnt;&#xD;
}&#xD;
&#xD;
tw.local.sqlStatements[ind] = initSql();&#xD;
// adding the sql statement abdelrahman wrote temporarily - to be restructured&#xD;
&#xD;
tw.local.sqlStatements[ind++].sql = tw.local.query;&#xD;
&#xD;
//inserting parties&#xD;
//to be changed after changing parties to list)&#xD;
//for(var p=0 ; p&lt; 3 ; p++){&#xD;
tw.local.sqlStatements[ind] = initSql();&#xD;
tw.local.sqlStatements[ind].sql ="Insert into ODC_PARTIESINFO (PARTYID , PARTYTYPE, CIF, REQUESRID) VALUES (?, ?, ?, ? );"&#xD;
&#xD;
tw.local.sqlStatements[ind].parameters[par++] = &#xD;
 &#xD;
// }&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.ee764bf6-eb5f-4208-8320-828cbf64c02e" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3990d8b4-983d-4250-bd06-3600f527fed0" isCollection="true" name="sqlStatements" id="2056.ce6180b9-2169-4336-865f-c352a900e59c" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.d7d4d18a-bd71-4c6b-860c-41797c800e2f</processLinkId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.1615fc68-5350-4fd2-8dee-a6115f36a883</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.01516e5f-85fc-45a4-8336-888f9e7ec3b1</toProcessItemId>
            <guid>da3b239c-474e-43fc-9d76-ca35b177a81b</guid>
            <versionId>01c6d24e-ff15-461e-a0e3-3b655b4a1840</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.1615fc68-5350-4fd2-8dee-a6115f36a883</fromProcessItemId>
            <toProcessItemId>2025.01516e5f-85fc-45a4-8336-888f9e7ec3b1</toProcessItemId>
        </link>
        <link name="Yes">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.7d2d0ca6-22ca-439c-801f-3d5f9504608f</processLinkId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f9db43af-0083-4085-8e05-252c7bc1525c</fromProcessItemId>
            <endStateId>guid:266f6f4955d8489f:7b0b9b81:18b66504d45:3d73</endStateId>
            <toProcessItemId>2025.98e51491-7304-42a1-8af6-bc22fe4af2ef</toProcessItemId>
            <guid>841f5c29-92f1-48fe-990d-84674f75fb1e</guid>
            <versionId>71bc6cb7-90bd-4d62-b161-5ef30b1339ed</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f9db43af-0083-4085-8e05-252c7bc1525c</fromProcessItemId>
            <toProcessItemId>2025.98e51491-7304-42a1-8af6-bc22fe4af2ef</toProcessItemId>
        </link>
        <link name="No">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.47bfefe4-7514-4e92-8fa7-053ab8bf00c4</processLinkId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f9db43af-0083-4085-8e05-252c7bc1525c</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.6cccd4e9-cc7c-4062-85b2-a10af0901632</toProcessItemId>
            <guid>e98893f9-9778-4a50-b327-8fd61a0618f0</guid>
            <versionId>812a8eb1-a0e3-44c8-accd-243bba265363</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f9db43af-0083-4085-8e05-252c7bc1525c</fromProcessItemId>
            <toProcessItemId>2025.6cccd4e9-cc7c-4062-85b2-a10af0901632</toProcessItemId>
        </link>
        <link name="To Execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.eda2dc71-b433-4552-89dd-e2801b59e36d</processLinkId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.6cccd4e9-cc7c-4062-85b2-a10af0901632</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1615fc68-5350-4fd2-8dee-a6115f36a883</toProcessItemId>
            <guid>e48383ba-c6a0-4ab1-9441-baff03e8f629</guid>
            <versionId>e2f4b98f-d4f7-4ebb-a805-351d5297d4f7</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.6cccd4e9-cc7c-4062-85b2-a10af0901632</fromProcessItemId>
            <toProcessItemId>2025.1615fc68-5350-4fd2-8dee-a6115f36a883</toProcessItemId>
        </link>
        <link name="To Execute statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.6b928f1a-fc75-4129-8300-e6290bacf2e9</processLinkId>
            <processId>1.cb4d5d9e-d967-44a9-bdd4-fd4c39803249</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.98e51491-7304-42a1-8af6-bc22fe4af2ef</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1615fc68-5350-4fd2-8dee-a6115f36a883</toProcessItemId>
            <guid>5966c8ae-5a15-4fa0-bc96-e96a61ad80dc</guid>
            <versionId>e340a06a-0a6d-4987-ae47-7d2eba9cf0b1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.98e51491-7304-42a1-8af6-bc22fe4af2ef</fromProcessItemId>
            <toProcessItemId>2025.1615fc68-5350-4fd2-8dee-a6115f36a883</toProcessItemId>
        </link>
    </process>
</teamworks>

