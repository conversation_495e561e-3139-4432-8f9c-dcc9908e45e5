{"id": "64.63a48491-564d-4a28-972c-df08820c76ff", "versionId": "9038759a-d909-4706-88ae-ae7a4ed18868", "name": "functionSearch", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "// Constants\r\r\n\r\r\nconst EVENT_TYPES = [\r\r\n\t\"eventON_LOAD\",\r\r\n\t\"eventON_CHANGE\",\r\r\n\t\"eventON_INPUT\",\r\r\n\t\"eventON_BLUR\",\r\r\n\t\"eventON_FOCUS\",\r\r\n\t\"eventON_SVCERROR\",\r\r\n\t\"eventON_SVCITEMS\",\r\r\n\t\"eventON_BEFORE_SHOW_DAY\"\r\r\n];\r\r\nconst SEARCH_OPTIONS = {\r\r\n\tALL: \"all\",\r\r\n\tFUNCTION_NAME: \"funName\",\r\r\n\tNOT_USED: \"notUsed\"\r\r\n};\r\r\nconst SYSTEM_PROPERTIES = ['constructor', 'prototype', '__proto__'];\r\r\n\r\r\n/**\r\r\n * Builds a map of control labels to their bound object values for specified events\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {Array<string>} eventTypes - Array of event types to check for\r\r\n * @returns {Map<string, Array<{eventType: string, value: string}>} Map of control labels to arrays of event type and bound object value pairs\r\r\n */\r\r\nfunction buildBoundObjectMap(viewName, eventTypes) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst boundObjectMap = new Map();\r\r\n\tconsole.dir(view);\r\r\n\tif (!view || !view._bpmextViewNode || !view._bpmextViewNode._children) {\r\r\n\t\tconsole.error(\"View or view children not found\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\r\r\n\tconst children = view._bpmextViewNode._children;\r\r\n\tif (!children || Object.keys(children).length === 0) {\r\r\n\t\tconsole.error(\"No children found in the view\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\t// Iterate through all child controls\r\r\n\tfor (const controlType in children) {\r\r\n\t\tif (Object.prototype.hasOwnProperty.call(children, controlType)) {\r\r\n\t\t\tconst controls = children[controlType];\r\r\n\r\r\n\t\t\t// Process each control of this type\r\r\n\t\t\tfor (const control of controls) {\r\r\n\t\t\t\tif (!control._data) continue;\r\r\n\r\r\n\t\t\t\tconst label = control._data.getLabel ? control._data.getLabel() : controlType;\r\r\n\r\r\n\t\t\t\t// Check for each event type\r\r\n\t\t\t\tfor (const eventType of eventTypes) {\r\r\n\t\t\t\t\tif (control._data.context && control._data.context.options && control._data.context.options[eventType] && control._data.context.options[eventType].boundObject && control._data.context.options[eventType].boundObject.value) {\r\r\n\t\t\t\t\t\t// Get the bound object value\r\r\n\t\t\t\t\t\tconst value = control._data.context.options[eventType].boundObject.value;\r\r\n\r\r\n\t\t\t\t\t\t// Initialize array for this label if it doesn't exist\r\r\n\t\t\t\t\t\tif (!boundObjectMap.has(label)) {\r\r\n\t\t\t\t\t\t\tboundObjectMap.set(label, []);\r\r\n\t\t\t\t\t\t}\r\r\n\r\r\n\t\t\t\t\t\t// Add event type and value to the array for this label\r\r\n\t\t\t\t\t\tboundObjectMap.get(label).push({\r\r\n\t\t\t\t\t\t\teventType: eventType,\r\r\n\t\t\t\t\t\t\tvalue: value,\r\r\n\t\t\t\t\t\t});\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn boundObjectMap;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for a string in the bound object values and returns matching control labels\r\r\n * @param {Map<string, Array<{eventType: string, value: string}>} boundObjectMap - Map of control labels to arrays of event type and bound object value pairs\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchBoundObjects(boundObjectMap, searchString) {\r\r\n\tif (!searchString || typeof searchString !== \"string\") {\r\r\n\t\treturn [];\r\r\n\t}\r\r\n\r\r\n\tconst matchingResults = [];\r\r\n\r\r\n\t// Convert search string to lowercase for case-insensitive search\r\r\n\tconst searchLower = searchString.toLowerCase();\r\r\n\r\r\n\t// Search through all values in the map\r\r\n\tboundObjectMap.forEach((eventArray, label) => {\r\r\n\t\t// Find matching events for this control\r\r\n\t\tconst matchingEvents = eventArray.filter((event) => typeof event.value === \"string\" && event.value.toLowerCase().includes(searchLower));\r\r\n\r\r\n\t\t// If we found any matches, add this control to the results\r\r\n\t\tif (matchingEvents.length > 0) {\r\r\n\t\t\tmatchingResults.push({\r\r\n\t\t\t\tlabel: label,\r\r\n\t\t\t\tevents: matchingEvents,\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn matchingResults;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Main function to search for bound objects in a view\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchFunctionsInView(viewName, searchString) {\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\treturn searchBoundObjects(boundObjectMap, searchString);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets just the labels of controls that match the search string\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<string>} Array of distinct control labels that match the search\r\r\n */\r\r\nfunction getMatchingControlLabels(viewName, searchString) {\r\r\n\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\treturn results.map((result) => result.label);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats search results as an HTML table\r\r\n * @param {Array<{label: string, events: Array<{eventType: string, value: string}>}>} results - Search results from searchFunctionsInView\r\r\n * @param {string} searchString - The search string used (for highlighting)\r\r\n * @returns {string} HTML table representation of the search results\r\r\n */\r\r\nfunction formatResultsAsHtml(results, searchString) {\r\r\n\tif (!results || results.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No matching controls found</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn text.replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Highlight the search string in the value\r\r\n\tfunction highlightSearchString(text, search) {\r\r\n\t\tif (!search || !text.includes(search)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(search), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with unified design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each result\r\r\n\tresults.forEach((result) => {\r\r\n\t\tresult.events.forEach((event, index) => {\r\r\n\t\t\thtml += `\r\r\n            <tr>\r\r\n                <td>${escapeHtml(searchString)}</td>\r\r\n                <td>${index === 0 ? escapeHtml(result.label) : \"\"}</td>\r\r\n                <td class=\"event-type\">${escapeHtml(event.eventType.replace(\"event\", \"\"))}</td>\r\r\n                <td>${highlightSearchString(event.value, searchString)}</td>\r\r\n            </tr>\r\r\n            `;\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Checks if a function name is a system function\r\r\n * @param {string} functionName - The function name to check\r\r\n * @returns {boolean} True if it's a system function\r\r\n */\r\r\nfunction isSystemFunction(functionName) {\r\r\n\treturn functionName.startsWith('event') ||\r\r\n\t\t   functionName.includes('ON_') ||\r\r\n\t\t   functionName.startsWith('get') ||\r\r\n\t\t   functionName.startsWith('set') ||\r\r\n\t\t   functionName.startsWith('_');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets all user-defined functions from a view\r\r\n * @param {Object} view - The view object\r\r\n * @returns {Array<string>} Array of user function names\r\r\n */\r\r\nfunction getUserFunctions(view) {\r\r\n\tconst userFunctions = [];\r\r\n\tconst ownPropertyNames = Object.getOwnPropertyNames(view);\r\r\n\r\r\n\tfor (const key of ownPropertyNames) {\r\r\n\t\ttry {\r\r\n\t\t\t// Skip system properties\r\r\n\t\t\tif (SYSTEM_PROPERTIES.includes(key)) {\r\r\n\t\t\t\tcontinue;\r\r\n\t\t\t}\r\r\n\r\r\n\t\t\tconst value = view[key];\r\r\n\r\r\n\t\t\t// Check if it's a user-defined function\r\r\n\t\t\tif (typeof value === 'function' && !isSystemFunction(key)) {\r\r\n\t\t\t\tuserFunctions.push(key);\r\r\n\t\t\t}\r\r\n\t\t} catch (e) {\r\r\n\t\t\tconsole.log(`Error accessing property ${key}: ${e.message}`);\r\r\n\t\t}\r\r\n\t}\r\r\n\r\r\n\treturn userFunctions;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Extracts all user-defined function names from a view and finds their usage in controls\r\r\n * @param {string} viewName - The name of the view to extract functions from\r\r\n * @returns {Array<{name: string, controller: string, eventType: string, context: string}>} Array of function details found in the view\r\r\n */\r\r\nfunction extractFunctionNamesFromView(viewName) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst functionDetails = [];\r\r\n\r\r\n\tif (!view) {\r\r\n\t\tconsole.error(\"View not found\");\r\r\n\t\treturn functionDetails;\r\r\n\t}\r\r\n\r\r\n\tconsole.dir(view); // Debug logging\r\r\n\r\r\n\t// Get all user-defined functions\r\r\n\tconst userFunctions = getUserFunctions(view);\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\r\r\n\t// Find usage for each function\r\r\n\tuserFunctions.forEach(functionName => {\r\r\n\t\tconst usageResults = searchBoundObjects(boundObjectMap, functionName);\r\r\n\r\r\n\t\tif (usageResults.length > 0) {\r\r\n\t\t\t// Function is used in controls\r\r\n\t\t\tusageResults.forEach(result => {\r\r\n\t\t\t\tresult.events.forEach(event => {\r\r\n\t\t\t\t\tfunctionDetails.push({\r\r\n\t\t\t\t\t\tname: functionName,\r\r\n\t\t\t\t\t\tcontroller: result.label,\r\r\n\t\t\t\t\t\teventType: event.eventType.replace(\"event\", \"\"),\r\r\n\t\t\t\t\t\tcontext: event.value || functionName\r\r\n\t\t\t\t\t});\r\r\n\t\t\t\t});\r\r\n\t\t\t});\r\r\n\t\t} else {\r\r\n\t\t\t// Function exists but not used\r\r\n\t\t\tfunctionDetails.push({\r\r\n\t\t\t\tname: functionName,\r\r\n\t\t\t\tcontroller: 'Not Used',\r\r\n\t\t\t\teventType: 'Available',\r\r\n\t\t\t\tcontext: 'Function not bound to any control'\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn functionDetails;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Filters function details to show only unused functions\r\r\n * @param {Array<{name: string, controller: string, eventType: string}>} functionDetails - All function details\r\r\n * @returns {Array<{name: string, controller: string, eventType: string}>} Filtered function details\r\r\n */\r\r\nfunction getNotUsedFunctions(functionDetails) {\r\r\n\treturn functionDetails.filter(detail => detail.controller === 'Not Used');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats function details as an HTML table with grouping\r\r\n * @param {Array<{name: string, controller: string, eventType: string, context: string}>} functionDetails - Array of function details\r\r\n * @returns {string} HTML table representation of the function details\r\r\n */\r\r\nfunction formatFunctionNamesAsHtml(functionDetails) {\r\r\n\tif (!functionDetails || functionDetails.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No functions found in the view</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn String(text).replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Group by function name first, then by controller\r\r\n\tconst groupedByFunction = {};\r\r\n\tfunctionDetails.forEach(detail => {\r\r\n\t\tif (!groupedByFunction[detail.name]) {\r\r\n\t\t\tgroupedByFunction[detail.name] = {};\r\r\n\t\t}\r\r\n\t\tif (!groupedByFunction[detail.name][detail.controller]) {\r\r\n\t\t\tgroupedByFunction[detail.name][detail.controller] = [];\r\r\n\t\t}\r\r\n\t\tgroupedByFunction[detail.name][detail.controller].push({\r\r\n\t\t\teventType: detail.eventType,\r\r\n\t\t\tcontext: detail.context\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\t// Sort function names alphabetically\r\r\n\tconst sortedFunctionNames = Object.keys(groupedByFunction).sort((a, b) =>\r\r\n\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t);\r\r\n\r\r\n\t// Highlight function name in context\r\r\n\tfunction highlightFunctionInContext(text, functionName) {\r\r\n\t\tif (!functionName || !text.includes(functionName)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(functionName), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with simple, clean design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each function group\r\r\n\tsortedFunctionNames.forEach((functionName) => {\r\r\n\t\tconst controllers = groupedByFunction[functionName];\r\r\n\t\tconst sortedControllers = Object.keys(controllers).sort((a, b) =>\r\r\n\t\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t\t);\r\r\n\r\r\n\t\tlet isFirstRowForFunction = true;\r\r\n\r\r\n\t\tsortedControllers.forEach((controller) => {\r\r\n\t\t\tconst eventDetails = controllers[controller];\r\r\n\t\t\tlet isFirstRowForController = true;\r\r\n\r\r\n\t\t\teventDetails.forEach((detail) => {\r\r\n\t\t\t\tconst functionGroupClass = isFirstRowForFunction ? 'function-group' : '';\r\r\n\t\t\t\tconst controllerGroupClass = isFirstRowForController ? 'controller-group' : '';\r\r\n\r\r\n\t\t\t\thtml += `\r\r\n                <tr class=\"${functionGroupClass}\">\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForFunction ? escapeHtml(functionName) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForController ? escapeHtml(controller) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"event-type\">${escapeHtml(detail.eventType)}</td>\r\r\n                    <td>${highlightFunctionInContext(detail.context, functionName)}</td>\r\r\n                </tr>\r\r\n                `;\r\r\n\r\r\n\t\t\t\tisFirstRowForFunction = false;\r\r\n\t\t\t\tisFirstRowForController = false;\r\r\n\t\t\t});\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for functions in a view and returns the results as HTML\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchFor - Search option (all, funName, notUsed)\r\r\n * @param {string} searchString - String to search for in bound object values (used when searchFor is funName)\r\r\n * @returns {string} HTML representation of the search results\r\r\n */\r\r\nfunction searchFunctionsAndFormatAsHtml(viewName, searchFor, searchString) {\r\r\n\tswitch (searchFor) {\r\r\n\t\tcase SEARCH_OPTIONS.ALL:\r\r\n\t\t\t// Show all user-defined functions\r\r\n\t\t\tconst allFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(allFunctionDetails);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.FUNCTION_NAME:\r\r\n\t\t\t// Search for specific function name\r\r\n\t\t\tif (!searchString) {\r\r\n\t\t\t\treturn \"<div class='no-results'>Please enter a function name to search for</div>\";\r\r\n\t\t\t}\r\r\n\t\t\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\t\t\treturn formatResultsAsHtml(results, searchString);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.NOT_USED:\r\r\n\t\t\t// Show only unused functions\r\r\n\t\t\tconst allFunctions = extractFunctionNamesFromView(viewName);\r\r\n\t\t\tconst notUsedFunctions = getNotUsedFunctions(allFunctions);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(notUsedFunctions);\r\r\n\r\r\n\t\tdefault:\r\r\n\t\t\t// Default to showing all functions\r\r\n\t\t\tconst defaultFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(defaultFunctionDetails);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.executeSearch = function () {\r\r\n\tvar viewName = this.ui.get(\"viewName\").getData();\r\r\n\tif (!viewName) {\r\r\n\t\tconsole.error(\"View name is empty\");\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tvar searchForVal = this.ui.get(\"searchFor\").getData();\r\r\n\tvar funName = this.ui.get(\"functionName\").getData();\r\r\n\r\r\n\tconsole.log(`Search type: ${searchForVal}, Function name: ${funName || 'N/A'}, View: ${viewName}`);\r\r\n\r\r\n\tvar htmlResults = searchFunctionsAndFormatAsHtml(viewName, searchForVal, funName);\r\r\n\tif (!!htmlResults) {\r\r\n\t\tthis.ui.get(\"result\").setData(htmlResults);\r\r\n\t}\r\r\n};"}]}, "_fullObjectData": {"teamworks": {"coachView": {"id": "64.63a48491-564d-4a28-972c-df08820c76ff", "name": "functionSearch", "lastModified": "1748106880183", "lastModifiedBy": "mohamed.reda", "coachViewId": "64.63a48491-564d-4a28-972c-df08820c76ff", "isTemplate": "false", "layout": "<?xml version=\"1.0\" encoding=\"UTF-8\" standalone=\"yes\"?><ns2:layout xmlns:ns2=\"http://www.ibm.com/bpm/CoachDesignerNG\" xmlns:ns3=\"http://www.ibm.com/bpm/coachview\"><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>e353ad9e-91fa-4c7f-88c6-b3d4ba816967</ns2:id><ns2:layoutItemId>Horizontal_Layout1</ns2:layoutItemId><ns2:configData><ns2:id>2fc2b01b-5e65-4996-8179-0bc15609ab32</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Horizontal layout</ns2:value></ns2:configData><ns2:configData><ns2:id>364ba5e2-e3cf-4d07-8f26-e315ed5632a3</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>8bc5c073-d698-4d60-84b9-************</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.44f463cc-615b-43d0-834f-c398a82e0363</ns2:viewUUID><ns2:contentBoxContrib><ns2:id>c72fc52b-8733-408a-8672-a1ac2c9e367b</ns2:id><ns2:contentBoxId>ContentBox1</ns2:contentBoxId><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>84802a72-ea5e-44b2-806a-2cdd843954dc</ns2:id><ns2:layoutItemId>searchFor</ns2:layoutItemId><ns2:configData><ns2:id>81bd2e0b-98df-4d3a-8c3c-928ac394f20e</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Search For</ns2:value></ns2:configData><ns2:configData><ns2:id>615a5bfd-7dac-4bbe-83dd-493ea74856b2</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>4fd440d6-d65b-427d-8bc9-5d2403248dbd</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>91b975e8-092a-42cb-8930-38c3bba61697</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"33%\"}]}</ns2:value></ns2:configData><ns2:configData><ns2:id>56ef8ccb-da1d-4aca-84c9-57b0c1081c9f</ns2:id><ns2:optionName>itemLookupMode</ns2:optionName><ns2:value>L</ns2:value></ns2:configData><ns2:configData><ns2:id>19636b43-b21e-41a9-8969-1a64a2d97113</ns2:id><ns2:optionName>staticList</ns2:optionName><ns2:value>[{\"name\":\"all\",\"value\":\"All Functions\"},{\"name\":\"notUsed\",\"value\":\"Not Used Functions\"},{\"name\":\"funName\",\"value\":\"Function By Name\"}]</ns2:value></ns2:configData><ns2:configData><ns2:id>558850f5-171b-429a-8afe-b4700e2d04c7</ns2:id><ns2:optionName>dataMapping</ns2:optionName><ns2:value>{\"optionValueProperty\":\"\",\"optionDisplayProperty\":\"\"}</ns2:value></ns2:configData><ns2:viewUUID>64.fd4da558-40d8-47be-92ca-c305708dc7b7</ns2:viewUUID></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>6918e865-3573-4e1e-87a9-cd17a524008f</ns2:id><ns2:layoutItemId>viewName</ns2:layoutItemId><ns2:configData><ns2:id>7de1eca6-0c8e-4af3-8084-652ab0b0c924</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>3d96fe9e-f782-44de-8136-eaf60110648f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>View Name</ns2:value></ns2:configData><ns2:configData><ns2:id>a1ec9c23-8d97-465a-8eee-09a5f1af4305</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>75b65af5-b3b8-4a6f-8c89-00f542ddc49b</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>c3325ce8-**************-63be05b15833</ns2:id><ns2:optionName>width</ns2:optionName><ns2:value>{\"isResponsiveData\":true,\"values\":[{\"deviceConfigID\":\"LargeID\",\"value\":\"33%\"}]}</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID></ns2:contributions><ns2:contributions xsi:type=\"ns2:ViewRef\" version=\"8550\"><ns2:id>4c36a3c1-070b-4447-8fef-b7383604c59d</ns2:id><ns2:layoutItemId>functionName</ns2:layoutItemId><ns2:configData><ns2:id>d2e987b2-4b48-481a-8b88-a5d78787e3fa</ns2:id><ns2:optionName>showOverflowTooltip</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:configData><ns2:id>1dc35ba8-f00e-43b7-8f2a-0a652abfae0b</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Function Name</ns2:value></ns2:configData><ns2:configData><ns2:id>fb53141d-5811-4d13-8071-8426a345a496</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>167ec80a-f763-4684-8582-c429f9b449f6</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:viewUUID>64.5663dd71-ff18-4d33-bea0-468d0b869816</ns2:viewUUID></ns2:contributions></ns2:contentBoxContrib></ns2:layoutItem><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>e9a06057-cba8-4ebc-84b6-a20dcb514e48</ns2:id><ns2:layoutItemId>searchBtn</ns2:layoutItemId><ns2:configData><ns2:id>01c45bdc-49fe-4906-8037-6b10152b1f39</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Search</ns2:value></ns2:configData><ns2:configData><ns2:id>3b9a31dd-fe77-45a1-85ad-08007b964c80</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>fde0f087-961b-4620-8984-b444735bbb59</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>4edd7221-1a10-40db-8387-6cae83c48134</ns2:id><ns2:optionName>eventON_CLICK</ns2:optionName><ns2:value>view.executeSearch();</ns2:value></ns2:configData><ns2:viewUUID>64.7133c7d4-1a54-45c8-89cd-a8e8fa4a8e36</ns2:viewUUID></ns2:layoutItem><ns2:layoutItem xsi:type=\"ns2:ViewRef\" version=\"8550\" xmlns:xsi=\"http://www.w3.org/2001/XMLSchema-instance\"><ns2:id>ad0d88e8-d952-46ae-89ae-76269f15487f</ns2:id><ns2:layoutItemId>result</ns2:layoutItemId><ns2:configData><ns2:id>900fd2ed-6e41-4ec2-87be-42333e2d7f6f</ns2:id><ns2:optionName>@label</ns2:optionName><ns2:value>Result</ns2:value></ns2:configData><ns2:configData><ns2:id>92be0e96-3d86-4b22-82e1-253b8a329f0a</ns2:id><ns2:optionName>@helpText</ns2:optionName><ns2:value></ns2:value></ns2:configData><ns2:configData><ns2:id>e1c3eab2-c5a4-495c-8597-00234783436f</ns2:id><ns2:optionName>@labelVisibility</ns2:optionName><ns2:value>SHOW</ns2:value></ns2:configData><ns2:configData><ns2:id>be88fcb8-045e-4b99-8858-0825ca103ecc</ns2:id><ns2:optionName>allowHTML</ns2:optionName><ns2:value>true</ns2:value></ns2:configData><ns2:viewUUID>64.f634f22e-7800-4bd7-9f1e-87177acfb3bc</ns2:viewUUID></ns2:layoutItem></ns2:layout>", "paletteIcon": {"isNull": "true"}, "previewImage": {"isNull": "true"}, "hasLabel": "false", "labelPosition": "0", "nineSliceX1Coord": "0", "nineSliceX2Coord": "0", "nineSliceY1Coord": "0", "nineSliceY2Coord": "0", "emitBoundary": "false", "isPrototypeFunc": "false", "enableDevMode": "false", "isMobileReady": "true", "loadJsFunction": {"isNull": "true"}, "unloadJsFunction": {"isNull": "true"}, "viewJsFunction": {"isNull": "true"}, "changeJsFunction": {"isNull": "true"}, "collaborationJsFunction": {"isNull": "true"}, "description": "", "validateJsFunction": {"isNull": "true"}, "previewAdvHtml": {"isNull": "true"}, "previewAdvJs": {"isNull": "true"}, "useUrlBinding": "false", "guid": "e69bf87c-ec2e-4527-b98e-465bdda7f625", "versionId": "9038759a-d909-4706-88ae-ae7a4ed18868", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "inlineScript": {"name": "Inline Javascript", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "coachViewInlineScriptId": "68.7758c51c-f0fc-438a-ab32-360a0d72d706", "coachViewId": "64.63a48491-564d-4a28-972c-df08820c76ff", "scriptType": "JS", "scriptBlock": "// Constants\r\r\n\r\r\nconst EVENT_TYPES = [\r\r\n\t\"eventON_LOAD\",\r\r\n\t\"eventON_CHANGE\",\r\r\n\t\"eventON_INPUT\",\r\r\n\t\"eventON_BLUR\",\r\r\n\t\"eventON_FOCUS\",\r\r\n\t\"eventON_SVCERROR\",\r\r\n\t\"eventON_SVCITEMS\",\r\r\n\t\"eventON_BEFORE_SHOW_DAY\"\r\r\n];\r\r\nconst SEARCH_OPTIONS = {\r\r\n\tALL: \"all\",\r\r\n\tFUNCTION_NAME: \"funName\",\r\r\n\tNOT_USED: \"notUsed\"\r\r\n};\r\r\nconst SYSTEM_PROPERTIES = ['constructor', 'prototype', '__proto__'];\r\r\n\r\r\n/**\r\r\n * Builds a map of control labels to their bound object values for specified events\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {Array<string>} eventTypes - Array of event types to check for\r\r\n * @returns {Map<string, Array<{eventType: string, value: string}>} Map of control labels to arrays of event type and bound object value pairs\r\r\n */\r\r\nfunction buildBoundObjectMap(viewName, eventTypes) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst boundObjectMap = new Map();\r\r\n\tconsole.dir(view);\r\r\n\tif (!view || !view._bpmextViewNode || !view._bpmextViewNode._children) {\r\r\n\t\tconsole.error(\"View or view children not found\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\r\r\n\tconst children = view._bpmextViewNode._children;\r\r\n\tif (!children || Object.keys(children).length === 0) {\r\r\n\t\tconsole.error(\"No children found in the view\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\t// Iterate through all child controls\r\r\n\tfor (const controlType in children) {\r\r\n\t\tif (Object.prototype.hasOwnProperty.call(children, controlType)) {\r\r\n\t\t\tconst controls = children[controlType];\r\r\n\r\r\n\t\t\t// Process each control of this type\r\r\n\t\t\tfor (const control of controls) {\r\r\n\t\t\t\tif (!control._data) continue;\r\r\n\r\r\n\t\t\t\tconst label = control._data.getLabel ? control._data.getLabel() : controlType;\r\r\n\r\r\n\t\t\t\t// Check for each event type\r\r\n\t\t\t\tfor (const eventType of eventTypes) {\r\r\n\t\t\t\t\tif (control._data.context && control._data.context.options && control._data.context.options[eventType] && control._data.context.options[eventType].boundObject && control._data.context.options[eventType].boundObject.value) {\r\r\n\t\t\t\t\t\t// Get the bound object value\r\r\n\t\t\t\t\t\tconst value = control._data.context.options[eventType].boundObject.value;\r\r\n\r\r\n\t\t\t\t\t\t// Initialize array for this label if it doesn't exist\r\r\n\t\t\t\t\t\tif (!boundObjectMap.has(label)) {\r\r\n\t\t\t\t\t\t\tboundObjectMap.set(label, []);\r\r\n\t\t\t\t\t\t}\r\r\n\r\r\n\t\t\t\t\t\t// Add event type and value to the array for this label\r\r\n\t\t\t\t\t\tboundObjectMap.get(label).push({\r\r\n\t\t\t\t\t\t\teventType: eventType,\r\r\n\t\t\t\t\t\t\tvalue: value,\r\r\n\t\t\t\t\t\t});\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn boundObjectMap;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for a string in the bound object values and returns matching control labels\r\r\n * @param {Map<string, Array<{eventType: string, value: string}>} boundObjectMap - Map of control labels to arrays of event type and bound object value pairs\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchBoundObjects(boundObjectMap, searchString) {\r\r\n\tif (!searchString || typeof searchString !== \"string\") {\r\r\n\t\treturn [];\r\r\n\t}\r\r\n\r\r\n\tconst matchingResults = [];\r\r\n\r\r\n\t// Convert search string to lowercase for case-insensitive search\r\r\n\tconst searchLower = searchString.toLowerCase();\r\r\n\r\r\n\t// Search through all values in the map\r\r\n\tboundObjectMap.forEach((eventArray, label) => {\r\r\n\t\t// Find matching events for this control\r\r\n\t\tconst matchingEvents = eventArray.filter((event) => typeof event.value === \"string\" && event.value.toLowerCase().includes(searchLower));\r\r\n\r\r\n\t\t// If we found any matches, add this control to the results\r\r\n\t\tif (matchingEvents.length > 0) {\r\r\n\t\t\tmatchingResults.push({\r\r\n\t\t\t\tlabel: label,\r\r\n\t\t\t\tevents: matchingEvents,\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn matchingResults;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Main function to search for bound objects in a view\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchFunctionsInView(viewName, searchString) {\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\treturn searchBoundObjects(boundObjectMap, searchString);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets just the labels of controls that match the search string\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<string>} Array of distinct control labels that match the search\r\r\n */\r\r\nfunction getMatchingControlLabels(viewName, searchString) {\r\r\n\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\treturn results.map((result) => result.label);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats search results as an HTML table\r\r\n * @param {Array<{label: string, events: Array<{eventType: string, value: string}>}>} results - Search results from searchFunctionsInView\r\r\n * @param {string} searchString - The search string used (for highlighting)\r\r\n * @returns {string} HTML table representation of the search results\r\r\n */\r\r\nfunction formatResultsAsHtml(results, searchString) {\r\r\n\tif (!results || results.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No matching controls found</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn text.replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Highlight the search string in the value\r\r\n\tfunction highlightSearchString(text, search) {\r\r\n\t\tif (!search || !text.includes(search)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(search), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with unified design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each result\r\r\n\tresults.forEach((result) => {\r\r\n\t\tresult.events.forEach((event, index) => {\r\r\n\t\t\thtml += `\r\r\n            <tr>\r\r\n                <td>${escapeHtml(searchString)}</td>\r\r\n                <td>${index === 0 ? escapeHtml(result.label) : \"\"}</td>\r\r\n                <td class=\"event-type\">${escapeHtml(event.eventType.replace(\"event\", \"\"))}</td>\r\r\n                <td>${highlightSearchString(event.value, searchString)}</td>\r\r\n            </tr>\r\r\n            `;\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Checks if a function name is a system function\r\r\n * @param {string} functionName - The function name to check\r\r\n * @returns {boolean} True if it's a system function\r\r\n */\r\r\nfunction isSystemFunction(functionName) {\r\r\n\treturn functionName.startsWith('event') ||\r\r\n\t\t   functionName.includes('ON_') ||\r\r\n\t\t   functionName.startsWith('get') ||\r\r\n\t\t   functionName.startsWith('set') ||\r\r\n\t\t   functionName.startsWith('_');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets all user-defined functions from a view\r\r\n * @param {Object} view - The view object\r\r\n * @returns {Array<string>} Array of user function names\r\r\n */\r\r\nfunction getUserFunctions(view) {\r\r\n\tconst userFunctions = [];\r\r\n\tconst ownPropertyNames = Object.getOwnPropertyNames(view);\r\r\n\r\r\n\tfor (const key of ownPropertyNames) {\r\r\n\t\ttry {\r\r\n\t\t\t// Skip system properties\r\r\n\t\t\tif (SYSTEM_PROPERTIES.includes(key)) {\r\r\n\t\t\t\tcontinue;\r\r\n\t\t\t}\r\r\n\r\r\n\t\t\tconst value = view[key];\r\r\n\r\r\n\t\t\t// Check if it's a user-defined function\r\r\n\t\t\tif (typeof value === 'function' && !isSystemFunction(key)) {\r\r\n\t\t\t\tuserFunctions.push(key);\r\r\n\t\t\t}\r\r\n\t\t} catch (e) {\r\r\n\t\t\tconsole.log(`Error accessing property ${key}: ${e.message}`);\r\r\n\t\t}\r\r\n\t}\r\r\n\r\r\n\treturn userFunctions;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Extracts all user-defined function names from a view and finds their usage in controls\r\r\n * @param {string} viewName - The name of the view to extract functions from\r\r\n * @returns {Array<{name: string, controller: string, eventType: string, context: string}>} Array of function details found in the view\r\r\n */\r\r\nfunction extractFunctionNamesFromView(viewName) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst functionDetails = [];\r\r\n\r\r\n\tif (!view) {\r\r\n\t\tconsole.error(\"View not found\");\r\r\n\t\treturn functionDetails;\r\r\n\t}\r\r\n\r\r\n\tconsole.dir(view); // Debug logging\r\r\n\r\r\n\t// Get all user-defined functions\r\r\n\tconst userFunctions = getUserFunctions(view);\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\r\r\n\t// Find usage for each function\r\r\n\tuserFunctions.forEach(functionName => {\r\r\n\t\tconst usageResults = searchBoundObjects(boundObjectMap, functionName);\r\r\n\r\r\n\t\tif (usageResults.length > 0) {\r\r\n\t\t\t// Function is used in controls\r\r\n\t\t\tusageResults.forEach(result => {\r\r\n\t\t\t\tresult.events.forEach(event => {\r\r\n\t\t\t\t\tfunctionDetails.push({\r\r\n\t\t\t\t\t\tname: functionName,\r\r\n\t\t\t\t\t\tcontroller: result.label,\r\r\n\t\t\t\t\t\teventType: event.eventType.replace(\"event\", \"\"),\r\r\n\t\t\t\t\t\tcontext: event.value || functionName\r\r\n\t\t\t\t\t});\r\r\n\t\t\t\t});\r\r\n\t\t\t});\r\r\n\t\t} else {\r\r\n\t\t\t// Function exists but not used\r\r\n\t\t\tfunctionDetails.push({\r\r\n\t\t\t\tname: functionName,\r\r\n\t\t\t\tcontroller: 'Not Used',\r\r\n\t\t\t\teventType: 'Available',\r\r\n\t\t\t\tcontext: 'Function not bound to any control'\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn functionDetails;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Filters function details to show only unused functions\r\r\n * @param {Array<{name: string, controller: string, eventType: string}>} functionDetails - All function details\r\r\n * @returns {Array<{name: string, controller: string, eventType: string}>} Filtered function details\r\r\n */\r\r\nfunction getNotUsedFunctions(functionDetails) {\r\r\n\treturn functionDetails.filter(detail => detail.controller === 'Not Used');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats function details as an HTML table with grouping\r\r\n * @param {Array<{name: string, controller: string, eventType: string, context: string}>} functionDetails - Array of function details\r\r\n * @returns {string} HTML table representation of the function details\r\r\n */\r\r\nfunction formatFunctionNamesAsHtml(functionDetails) {\r\r\n\tif (!functionDetails || functionDetails.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No functions found in the view</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn String(text).replace(/&/g, \"&amp;\").replace(/</g, \"&lt;\").replace(/>/g, \"&gt;\").replace(/\"/g, \"&quot;\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Group by function name first, then by controller\r\r\n\tconst groupedByFunction = {};\r\r\n\tfunctionDetails.forEach(detail => {\r\r\n\t\tif (!groupedByFunction[detail.name]) {\r\r\n\t\t\tgroupedByFunction[detail.name] = {};\r\r\n\t\t}\r\r\n\t\tif (!groupedByFunction[detail.name][detail.controller]) {\r\r\n\t\t\tgroupedByFunction[detail.name][detail.controller] = [];\r\r\n\t\t}\r\r\n\t\tgroupedByFunction[detail.name][detail.controller].push({\r\r\n\t\t\teventType: detail.eventType,\r\r\n\t\t\tcontext: detail.context\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\t// Sort function names alphabetically\r\r\n\tconst sortedFunctionNames = Object.keys(groupedByFunction).sort((a, b) =>\r\r\n\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t);\r\r\n\r\r\n\t// Highlight function name in context\r\r\n\tfunction highlightFunctionInContext(text, functionName) {\r\r\n\t\tif (!functionName || !text.includes(functionName)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(functionName), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with simple, clean design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each function group\r\r\n\tsortedFunctionNames.forEach((functionName) => {\r\r\n\t\tconst controllers = groupedByFunction[functionName];\r\r\n\t\tconst sortedControllers = Object.keys(controllers).sort((a, b) =>\r\r\n\t\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t\t);\r\r\n\r\r\n\t\tlet isFirstRowForFunction = true;\r\r\n\r\r\n\t\tsortedControllers.forEach((controller) => {\r\r\n\t\t\tconst eventDetails = controllers[controller];\r\r\n\t\t\tlet isFirstRowForController = true;\r\r\n\r\r\n\t\t\teventDetails.forEach((detail) => {\r\r\n\t\t\t\tconst functionGroupClass = isFirstRowForFunction ? 'function-group' : '';\r\r\n\t\t\t\tconst controllerGroupClass = isFirstRowForController ? 'controller-group' : '';\r\r\n\r\r\n\t\t\t\thtml += `\r\r\n                <tr class=\"${functionGroupClass}\">\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForFunction ? escapeHtml(functionName) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForController ? escapeHtml(controller) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"event-type\">${escapeHtml(detail.eventType)}</td>\r\r\n                    <td>${highlightFunctionInContext(detail.context, functionName)}</td>\r\r\n                </tr>\r\r\n                `;\r\r\n\r\r\n\t\t\t\tisFirstRowForFunction = false;\r\r\n\t\t\t\tisFirstRowForController = false;\r\r\n\t\t\t});\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for functions in a view and returns the results as HTML\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchFor - Search option (all, funName, notUsed)\r\r\n * @param {string} searchString - String to search for in bound object values (used when searchFor is funName)\r\r\n * @returns {string} HTML representation of the search results\r\r\n */\r\r\nfunction searchFunctionsAndFormatAsHtml(viewName, searchFor, searchString) {\r\r\n\tswitch (searchFor) {\r\r\n\t\tcase SEARCH_OPTIONS.ALL:\r\r\n\t\t\t// Show all user-defined functions\r\r\n\t\t\tconst allFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(allFunctionDetails);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.FUNCTION_NAME:\r\r\n\t\t\t// Search for specific function name\r\r\n\t\t\tif (!searchString) {\r\r\n\t\t\t\treturn \"<div class='no-results'>Please enter a function name to search for</div>\";\r\r\n\t\t\t}\r\r\n\t\t\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\t\t\treturn formatResultsAsHtml(results, searchString);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.NOT_USED:\r\r\n\t\t\t// Show only unused functions\r\r\n\t\t\tconst allFunctions = extractFunctionNamesFromView(viewName);\r\r\n\t\t\tconst notUsedFunctions = getNotUsedFunctions(allFunctions);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(notUsedFunctions);\r\r\n\r\r\n\t\tdefault:\r\r\n\t\t\t// Default to showing all functions\r\r\n\t\t\tconst defaultFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(defaultFunctionDetails);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.executeSearch = function () {\r\r\n\tvar viewName = this.ui.get(\"viewName\").getData();\r\r\n\tif (!viewName) {\r\r\n\t\tconsole.error(\"View name is empty\");\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tvar searchForVal = this.ui.get(\"searchFor\").getData();\r\r\n\tvar funName = this.ui.get(\"functionName\").getData();\r\r\n\r\r\n\tconsole.log(`Search type: ${searchForVal}, Function name: ${funName || 'N/A'}, View: ${viewName}`);\r\r\n\r\r\n\tvar htmlResults = searchFunctionsAndFormatAsHtml(viewName, searchForVal, funName);\r\r\n\tif (!!htmlResults) {\r\r\n\t\tthis.ui.get(\"result\").setData(htmlResults);\r\r\n\t}\r\r\n};\r\r\n", "seq": "0", "description": "", "guid": "04a536d7-3d61-48b4-b446-9cf3cac51632", "versionId": "15340370-7a36-43b8-99fa-d6b1a4c04fac"}}}}, "hasDetails": true}