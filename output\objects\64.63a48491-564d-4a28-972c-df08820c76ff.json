{"id": "64.63a48491-564d-4a28-972c-df08820c76ff", "versionId": "9038759a-d909-4706-88ae-ae7a4ed18868", "name": "functionSearch", "type": "<PERSON><PERSON><PERSON><PERSON>", "typeName": "Coach <PERSON>", "details": {"inlineScripts": [{"name": "Inline Javascript", "scriptType": "JS", "scriptBlock": "// Constants\r\r\n\r\r\nconst EVENT_TYPES = [\r\r\n\t\"eventON_LOAD\",\r\r\n\t\"eventON_CHANGE\",\r\r\n\t\"eventON_INPUT\",\r\r\n\t\"eventON_BLUR\",\r\r\n\t\"eventON_FOCUS\",\r\r\n\t\"eventON_SVCERROR\",\r\r\n\t\"eventON_SVCITEMS\",\r\r\n\t\"eventON_BEFORE_SHOW_DAY\"\r\r\n];\r\r\nconst SEARCH_OPTIONS = {\r\r\n\tALL: \"all\",\r\r\n\tFUNCTION_NAME: \"funName\",\r\r\n\tNOT_USED: \"notUsed\"\r\r\n};\r\r\nconst SYSTEM_PROPERTIES = ['constructor', 'prototype', '__proto__'];\r\r\n\r\r\n/**\r\r\n * Builds a map of control labels to their bound object values for specified events\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {Array<string>} eventTypes - Array of event types to check for\r\r\n * @returns {Map<string, Array<{eventType: string, value: string}>} Map of control labels to arrays of event type and bound object value pairs\r\r\n */\r\r\nfunction buildBoundObjectMap(viewName, eventTypes) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst boundObjectMap = new Map();\r\r\n\tconsole.dir(view);\r\r\n\tif (!view || !view._bpmextViewNode || !view._bpmextViewNode._children) {\r\r\n\t\tconsole.error(\"View or view children not found\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\r\r\n\tconst children = view._bpmextViewNode._children;\r\r\n\tif (!children || Object.keys(children).length === 0) {\r\r\n\t\tconsole.error(\"No children found in the view\");\r\r\n\t\treturn boundObjectMap;\r\r\n\t}\r\r\n\t// Iterate through all child controls\r\r\n\tfor (const controlType in children) {\r\r\n\t\tif (Object.prototype.hasOwnProperty.call(children, controlType)) {\r\r\n\t\t\tconst controls = children[controlType];\r\r\n\r\r\n\t\t\t// Process each control of this type\r\r\n\t\t\tfor (const control of controls) {\r\r\n\t\t\t\tif (!control._data) continue;\r\r\n\r\r\n\t\t\t\tconst label = control._data.getLabel ? control._data.getLabel() : controlType;\r\r\n\r\r\n\t\t\t\t// Check for each event type\r\r\n\t\t\t\tfor (const eventType of eventTypes) {\r\r\n\t\t\t\t\tif (control._data.context && control._data.context.options && control._data.context.options[eventType] && control._data.context.options[eventType].boundObject && control._data.context.options[eventType].boundObject.value) {\r\r\n\t\t\t\t\t\t// Get the bound object value\r\r\n\t\t\t\t\t\tconst value = control._data.context.options[eventType].boundObject.value;\r\r\n\r\r\n\t\t\t\t\t\t// Initialize array for this label if it doesn't exist\r\r\n\t\t\t\t\t\tif (!boundObjectMap.has(label)) {\r\r\n\t\t\t\t\t\t\tboundObjectMap.set(label, []);\r\r\n\t\t\t\t\t\t}\r\r\n\r\r\n\t\t\t\t\t\t// Add event type and value to the array for this label\r\r\n\t\t\t\t\t\tboundObjectMap.get(label).push({\r\r\n\t\t\t\t\t\t\teventType: eventType,\r\r\n\t\t\t\t\t\t\tvalue: value,\r\r\n\t\t\t\t\t\t});\r\r\n\t\t\t\t\t}\r\r\n\t\t\t\t}\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t}\r\r\n\treturn boundObjectMap;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for a string in the bound object values and returns matching control labels\r\r\n * @param {Map<string, Array<{eventType: string, value: string}>} boundObjectMap - Map of control labels to arrays of event type and bound object value pairs\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchBoundObjects(boundObjectMap, searchString) {\r\r\n\tif (!searchString || typeof searchString !== \"string\") {\r\r\n\t\treturn [];\r\r\n\t}\r\r\n\r\r\n\tconst matchingResults = [];\r\r\n\r\r\n\t// Convert search string to lowercase for case-insensitive search\r\r\n\tconst searchLower = searchString.toLowerCase();\r\r\n\r\r\n\t// Search through all values in the map\r\r\n\tboundObjectMap.forEach((eventArray, label) => {\r\r\n\t\t// Find matching events for this control\r\r\n\t\tconst matchingEvents = eventArray.filter((event) => typeof event.value === \"string\" && event.value.toLowerCase().includes(searchLower));\r\r\n\r\r\n\t\t// If we found any matches, add this control to the results\r\r\n\t\tif (matchingEvents.length > 0) {\r\r\n\t\t\tmatchingResults.push({\r\r\n\t\t\t\tlabel: label,\r\r\n\t\t\t\tevents: matchingEvents,\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn matchingResults;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Main function to search for bound objects in a view\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<{label: string, events: Array<{eventType: string, value: string}>}>} Array of objects containing matching control labels and their events\r\r\n */\r\r\nfunction searchFunctionsInView(viewName, searchString) {\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\treturn searchBoundObjects(boundObjectMap, searchString);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets just the labels of controls that match the search string\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchString - String to search for in bound object values\r\r\n * @returns {Array<string>} Array of distinct control labels that match the search\r\r\n */\r\r\nfunction getMatchingControlLabels(viewName, searchString) {\r\r\n\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\treturn results.map((result) => result.label);\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats search results as an HTML table\r\r\n * @param {Array<{label: string, events: Array<{eventType: string, value: string}>}>} results - Search results from searchFunctionsInView\r\r\n * @param {string} searchString - The search string used (for highlighting)\r\r\n * @returns {string} HTML table representation of the search results\r\r\n */\r\r\nfunction formatResultsAsHtml(results, searchString) {\r\r\n\tif (!results || results.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No matching controls found</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn text.replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Highlight the search string in the value\r\r\n\tfunction highlightSearchString(text, search) {\r\r\n\t\tif (!search || !text.includes(search)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(search), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with unified design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each result\r\r\n\tresults.forEach((result) => {\r\r\n\t\tresult.events.forEach((event, index) => {\r\r\n\t\t\thtml += `\r\r\n            <tr>\r\r\n                <td>${escapeHtml(searchString)}</td>\r\r\n                <td>${index === 0 ? escapeHtml(result.label) : \"\"}</td>\r\r\n                <td class=\"event-type\">${escapeHtml(event.eventType.replace(\"event\", \"\"))}</td>\r\r\n                <td>${highlightSearchString(event.value, searchString)}</td>\r\r\n            </tr>\r\r\n            `;\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Checks if a function name is a system function\r\r\n * @param {string} functionName - The function name to check\r\r\n * @returns {boolean} True if it's a system function\r\r\n */\r\r\nfunction isSystemFunction(functionName) {\r\r\n\treturn functionName.startsWith('event') ||\r\r\n\t\t   functionName.includes('ON_') ||\r\r\n\t\t   functionName.startsWith('get') ||\r\r\n\t\t   functionName.startsWith('set') ||\r\r\n\t\t   functionName.startsWith('_');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Gets all user-defined functions from a view\r\r\n * @param {Object} view - The view object\r\r\n * @returns {Array<string>} Array of user function names\r\r\n */\r\r\nfunction getUserFunctions(view) {\r\r\n\tconst userFunctions = [];\r\r\n\tconst ownPropertyNames = Object.getOwnPropertyNames(view);\r\r\n\r\r\n\tfor (const key of ownPropertyNames) {\r\r\n\t\ttry {\r\r\n\t\t\t// Skip system properties\r\r\n\t\t\tif (SYSTEM_PROPERTIES.includes(key)) {\r\r\n\t\t\t\tcontinue;\r\r\n\t\t\t}\r\r\n\r\r\n\t\t\tconst value = view[key];\r\r\n\r\r\n\t\t\t// Check if it's a user-defined function\r\r\n\t\t\tif (typeof value === 'function' && !isSystemFunction(key)) {\r\r\n\t\t\t\tuserFunctions.push(key);\r\r\n\t\t\t}\r\r\n\t\t} catch (e) {\r\r\n\t\t\tconsole.log(`Error accessing property ${key}: ${e.message}`);\r\r\n\t\t}\r\r\n\t}\r\r\n\r\r\n\treturn userFunctions;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Extracts all user-defined function names from a view and finds their usage in controls\r\r\n * @param {string} viewName - The name of the view to extract functions from\r\r\n * @returns {Array<{name: string, controller: string, eventType: string, context: string}>} Array of function details found in the view\r\r\n */\r\r\nfunction extractFunctionNamesFromView(viewName) {\r\r\n\tconst view = bpmext.ui.getView(viewName);\r\r\n\tconst functionDetails = [];\r\r\n\r\r\n\tif (!view) {\r\r\n\t\tconsole.error(\"View not found\");\r\r\n\t\treturn functionDetails;\r\r\n\t}\r\r\n\r\r\n\tconsole.dir(view); // Debug logging\r\r\n\r\r\n\t// Get all user-defined functions\r\r\n\tconst userFunctions = getUserFunctions(view);\r\r\n\tconst boundObjectMap = buildBoundObjectMap(viewName, EVENT_TYPES);\r\r\n\r\r\n\t// Find usage for each function\r\r\n\tuserFunctions.forEach(functionName => {\r\r\n\t\tconst usageResults = searchBoundObjects(boundObjectMap, functionName);\r\r\n\r\r\n\t\tif (usageResults.length > 0) {\r\r\n\t\t\t// Function is used in controls\r\r\n\t\t\tusageResults.forEach(result => {\r\r\n\t\t\t\tresult.events.forEach(event => {\r\r\n\t\t\t\t\tfunctionDetails.push({\r\r\n\t\t\t\t\t\tname: functionName,\r\r\n\t\t\t\t\t\tcontroller: result.label,\r\r\n\t\t\t\t\t\teventType: event.eventType.replace(\"event\", \"\"),\r\r\n\t\t\t\t\t\tcontext: event.value || functionName\r\r\n\t\t\t\t\t});\r\r\n\t\t\t\t});\r\r\n\t\t\t});\r\r\n\t\t} else {\r\r\n\t\t\t// Function exists but not used\r\r\n\t\t\tfunctionDetails.push({\r\r\n\t\t\t\tname: functionName,\r\r\n\t\t\t\tcontroller: 'Not Used',\r\r\n\t\t\t\teventType: 'Available',\r\r\n\t\t\t\tcontext: 'Function not bound to any control'\r\r\n\t\t\t});\r\r\n\t\t}\r\r\n\t});\r\r\n\r\r\n\treturn functionDetails;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Filters function details to show only unused functions\r\r\n * @param {Array<{name: string, controller: string, eventType: string}>} functionDetails - All function details\r\r\n * @returns {Array<{name: string, controller: string, eventType: string}>} Filtered function details\r\r\n */\r\r\nfunction getNotUsedFunctions(functionDetails) {\r\r\n\treturn functionDetails.filter(detail => detail.controller === 'Not Used');\r\r\n}\r\r\n\r\r\n/**\r\r\n * Formats function details as an HTML table with grouping\r\r\n * @param {Array<{name: string, controller: string, eventType: string, context: string}>} functionDetails - Array of function details\r\r\n * @returns {string} HTML table representation of the function details\r\r\n */\r\r\nfunction formatFunctionNamesAsHtml(functionDetails) {\r\r\n\tif (!functionDetails || functionDetails.length === 0) {\r\r\n\t\treturn \"<div class='no-results'>No functions found in the view</div>\";\r\r\n\t}\r\r\n\r\r\n\t// Escape HTML special characters to prevent XSS\r\r\n\tfunction escapeHtml(text) {\r\r\n\t\treturn String(text).replace(/&/g, \"&\").replace(/</g, \"<\").replace(/>/g, \">\").replace(/\"/g, \"\"\").replace(/'/g, \"&#039;\");\r\r\n\t}\r\r\n\r\r\n\t// Group by function name first, then by controller\r\r\n\tconst groupedByFunction = {};\r\r\n\tfunctionDetails.forEach(detail => {\r\r\n\t\tif (!groupedByFunction[detail.name]) {\r\r\n\t\t\tgroupedByFunction[detail.name] = {};\r\r\n\t\t}\r\r\n\t\tif (!groupedByFunction[detail.name][detail.controller]) {\r\r\n\t\t\tgroupedByFunction[detail.name][detail.controller] = [];\r\r\n\t\t}\r\r\n\t\tgroupedByFunction[detail.name][detail.controller].push({\r\r\n\t\t\teventType: detail.eventType,\r\r\n\t\t\tcontext: detail.context\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\t// Sort function names alphabetically\r\r\n\tconst sortedFunctionNames = Object.keys(groupedByFunction).sort((a, b) =>\r\r\n\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t);\r\r\n\r\r\n\t// Highlight function name in context\r\r\n\tfunction highlightFunctionInContext(text, functionName) {\r\r\n\t\tif (!functionName || !text.includes(functionName)) {\r\r\n\t\t\treturn escapeHtml(text);\r\r\n\t\t}\r\r\n\r\r\n\t\tconst escapedText = escapeHtml(text);\r\r\n\t\tconst regex = new RegExp(escapeHtml(functionName), \"gi\");\r\r\n\t\treturn escapedText.replace(regex, (match) => `<span class=\"highlight\">${match}</span>`);\r\r\n\t}\r\r\n\r\r\n\t// Build the HTML table with simple, clean design\r\r\n\tlet html = `\r\r\n    <style>\r\r\n        .search-results-table {\r\r\n            border-collapse: collapse;\r\r\n            width: 100%;\r\r\n            font-family: Arial, sans-serif;\r\r\n            margin-bottom: 20px;\r\r\n            table-layout: fixed;\r\r\n        }\r\r\n        .search-results-table th, .search-results-table td {\r\r\n            border: 1px solid #ddd;\r\r\n            padding: 8px;\r\r\n            text-align: left;\r\r\n        }\r\r\n        .search-results-table th {\r\r\n            background-color: #f2f2f2;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .search-results-table th:nth-child(1), .search-results-table td:nth-child(1) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(2), .search-results-table td:nth-child(2) {\r\r\n            width: 20%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(3), .search-results-table td:nth-child(3) {\r\r\n            width: 15%;\r\r\n            white-space: nowrap;\r\r\n            overflow: hidden;\r\r\n            text-overflow: ellipsis;\r\r\n        }\r\r\n        .search-results-table th:nth-child(4), .search-results-table td:nth-child(4) {\r\r\n            width: 45%;\r\r\n            word-wrap: break-word;\r\r\n            white-space: normal;\r\r\n        }\r\r\n        .search-results-table tr:nth-child(even) {\r\r\n            background-color: #f9f9f9;\r\r\n        }\r\r\n        .search-results-table tr:hover {\r\r\n            background-color: #f5f5f5;\r\r\n        }\r\r\n        .highlight {\r\r\n            background-color: yellow;\r\r\n            font-weight: bold;\r\r\n        }\r\r\n        .event-type {\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .no-results {\r\r\n            padding: 20px;\r\r\n            text-align: center;\r\r\n            font-style: italic;\r\r\n            color: #666;\r\r\n        }\r\r\n        .function-group {\r\r\n            border-top: 2px solid #0066cc;\r\r\n        }\r\r\n        .controller-group {\r\r\n            border-left: 3px solid #f0f0f0;\r\r\n        }\r\r\n    </style>\r\r\n    <table class=\"search-results-table\">\r\r\n        <thead>\r\r\n            <tr>\r\r\n                <th>Function Name</th>\r\r\n                <th>Used by Control</th>\r\r\n                <th>Event Type</th>\r\r\n                <th>Context</th>\r\r\n            </tr>\r\r\n        </thead>\r\r\n        <tbody>\r\r\n    `;\r\r\n\r\r\n\t// Add rows for each function group\r\r\n\tsortedFunctionNames.forEach((functionName) => {\r\r\n\t\tconst controllers = groupedByFunction[functionName];\r\r\n\t\tconst sortedControllers = Object.keys(controllers).sort((a, b) =>\r\r\n\t\t\ta.toLowerCase().localeCompare(b.toLowerCase())\r\r\n\t\t);\r\r\n\r\r\n\t\tlet isFirstRowForFunction = true;\r\r\n\r\r\n\t\tsortedControllers.forEach((controller) => {\r\r\n\t\t\tconst eventDetails = controllers[controller];\r\r\n\t\t\tlet isFirstRowForController = true;\r\r\n\r\r\n\t\t\teventDetails.forEach((detail) => {\r\r\n\t\t\t\tconst functionGroupClass = isFirstRowForFunction ? 'function-group' : '';\r\r\n\t\t\t\tconst controllerGroupClass = isFirstRowForController ? 'controller-group' : '';\r\r\n\r\r\n\t\t\t\thtml += `\r\r\n                <tr class=\"${functionGroupClass}\">\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForFunction ? escapeHtml(functionName) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"${controllerGroupClass}\">\r\r\n                        ${isFirstRowForController ? escapeHtml(controller) : ''}\r\r\n                    </td>\r\r\n                    <td class=\"event-type\">${escapeHtml(detail.eventType)}</td>\r\r\n                    <td>${highlightFunctionInContext(detail.context, functionName)}</td>\r\r\n                </tr>\r\r\n                `;\r\r\n\r\r\n\t\t\t\tisFirstRowForFunction = false;\r\r\n\t\t\t\tisFirstRowForController = false;\r\r\n\t\t\t});\r\r\n\t\t});\r\r\n\t});\r\r\n\r\r\n\thtml += `\r\r\n        </tbody>\r\r\n    </table>\r\r\n    `;\r\r\n\r\r\n\treturn html;\r\r\n}\r\r\n\r\r\n/**\r\r\n * Searches for functions in a view and returns the results as HTML\r\r\n * @param {string} viewName - The name of the view to search\r\r\n * @param {string} searchFor - Search option (all, funName, notUsed)\r\r\n * @param {string} searchString - String to search for in bound object values (used when searchFor is funName)\r\r\n * @returns {string} HTML representation of the search results\r\r\n */\r\r\nfunction searchFunctionsAndFormatAsHtml(viewName, searchFor, searchString) {\r\r\n\tswitch (searchFor) {\r\r\n\t\tcase SEARCH_OPTIONS.ALL:\r\r\n\t\t\t// Show all user-defined functions\r\r\n\t\t\tconst allFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(allFunctionDetails);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.FUNCTION_NAME:\r\r\n\t\t\t// Search for specific function name\r\r\n\t\t\tif (!searchString) {\r\r\n\t\t\t\treturn \"<div class='no-results'>Please enter a function name to search for</div>\";\r\r\n\t\t\t}\r\r\n\t\t\tconst results = searchFunctionsInView(viewName, searchString);\r\r\n\t\t\treturn formatResultsAsHtml(results, searchString);\r\r\n\r\r\n\t\tcase SEARCH_OPTIONS.NOT_USED:\r\r\n\t\t\t// Show only unused functions\r\r\n\t\t\tconst allFunctions = extractFunctionNamesFromView(viewName);\r\r\n\t\t\tconst notUsedFunctions = getNotUsedFunctions(allFunctions);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(notUsedFunctions);\r\r\n\r\r\n\t\tdefault:\r\r\n\t\t\t// Default to showing all functions\r\r\n\t\t\tconst defaultFunctionDetails = extractFunctionNamesFromView(viewName);\r\r\n\t\t\treturn formatFunctionNamesAsHtml(defaultFunctionDetails);\r\r\n\t}\r\r\n}\r\r\n\r\r\nthis.executeSearch = function () {\r\r\n\tvar viewName = this.ui.get(\"viewName\").getData();\r\r\n\tif (!viewName) {\r\r\n\t\tconsole.error(\"View name is empty\");\r\r\n\t\treturn;\r\r\n\t}\r\r\n\r\r\n\tvar searchForVal = this.ui.get(\"searchFor\").getData();\r\r\n\tvar funName = this.ui.get(\"functionName\").getData();\r\r\n\r\r\n\tconsole.log(`Search type: ${searchForVal}, Function name: ${funName || 'N/A'}, View: ${viewName}`);\r\r\n\r\r\n\tvar htmlResults = searchFunctionsAndFormatAsHtml(viewName, searchForVal, funName);\r\r\n\tif (!!htmlResults) {\r\r\n\t\tthis.ui.get(\"result\").setData(htmlResults);\r\r\n\t}\r\r\n};"}]}, "hasDetails": true}