<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <twClass id="12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134" name="FCTransactions">
        <lastModified>1691929972765</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <classId>12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134</classId>
        <type>1</type>
        <isSystem>false</isSystem>
        <shared>false</shared>
        <isShadow>false</isShadow>
        <globalLifetime>false</globalLifetime>
        <internalName isNull="true" />
        <extensionType isNull="true" />
        <saveServiceRef isNull="true" />
        <bpmn2Data isNull="true" />
        <externalId>itm.12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134</externalId>
        <dependencySummary isNull="true" />
        <jsonData>{"attributeFormDefault":"unqualified","elementFormDefault":"unqualified","targetNamespace":"http:\/\/NBEODCR","complexType":[{"annotation":{"documentation":[{}],"appinfo":[{"shared":[false],"advancedProperties":[{}],"shadow":[false]}]},"sequence":{"element":[{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["accountNo"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"accountNo","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["referenceNumber"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"referenceNumber","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["postingDate"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"postingDate","type":"{http:\/\/lombardi.ibm.com\/schema\/}Date","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["valueDate"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"valueDate","type":"{http:\/\/lombardi.ibm.com\/schema\/}Date","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["transactionAmount"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"transactionAmount","type":"{http:\/\/lombardi.ibm.com\/schema\/}Decimal","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["existAmount"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"existAmount","type":"{http:\/\/lombardi.ibm.com\/schema\/}Decimal","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["currency"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"currency","type":"{http:\/\/lombardi.ibm.com\/schema\/}String","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["amountAllocatedForCurrencyRequest"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"amountAllocatedForCurrencyRequest","type":"{http:\/\/lombardi.ibm.com\/schema\/}Decimal","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}},{"annotation":{"documentation":[{}],"appinfo":[{"propertyName":["allocatedAmountInRequestCurrency"],"propertyRequired":[false],"propertyHidden":[false],"advancedParameterProperties":[{}]}]},"name":"allocatedAmountInRequestCurrency","type":"{http:\/\/lombardi.ibm.com\/schema\/}Decimal","otherAttributes":{"{http:\/\/www.ibm.com\/bpmsdk}refid":"12.536b2aa5-a30f-4eca-87fa-3a28066753ee"}}]},"name":"FCTransactions"}],"id":"_12.4f8cbf10-1e9b-4a7c-90b9-9641d40fa134"}</jsonData>
        <description isNull="true" />
        <guid>guid:d694a63221635d5b:6baf87c4:1896d4865c2:-160c</guid>
        <versionId>e8a18f78-609f-4ebd-b237-0c1e584d8df0</versionId>
        <definition>
            <property>
                <name>accountNo</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>referenceNumber</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>postingDate</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>valueDate</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.68474ab0-d56f-47ee-b7e9-510b45a2a8be</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>transactionAmount</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>existAmount</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>currency</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>amountAllocatedForCurrencyRequest</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <property>
                <name>allocatedAmountInRequestCurrency</name>
                <description isNull="true" />
                <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                <arrayProperty>false</arrayProperty>
                <propertyDefault isNull="true" />
                <propertyRequired>false</propertyRequired>
                <propertyHidden>false</propertyHidden>
                <annotation type="com.lombardisoftware.core.xml.XMLFieldAnnotation" version="2.0">
                    <exclude isNull="true" />
                    <nodeType isNull="true" />
                    <name isNull="true" />
                    <namespace isNull="true" />
                    <typeName isNull="true" />
                    <typeNamespace isNull="true" />
                    <minOccurs isNull="true" />
                    <maxOccurs isNull="true" />
                    <nillable isNull="true" />
                    <order isNull="true" />
                    <wrapArray isNull="true" />
                    <arrayTypeName isNull="true" />
                    <arrayTypeAnonymous isNull="true" />
                    <arrayItemName isNull="true" />
                    <arrayItemWildcard isNull="true" />
                    <wildcard isNull="true" />
                    <wildcardVariety isNull="true" />
                    <wildcardMode isNull="true" />
                    <wildcardNamespace isNull="true" />
                    <parentModelGroupCompositor isNull="true" />
                    <timeZone isNull="true" />
                </annotation>
            </property>
            <validator>
                <className isNull="true" />
                <errorMessage isNull="true" />
                <webWidgetJavaClass isNull="true" />
                <externalType isNull="true" />
                <configData>
                    <schema>
                        <simpleType name="FCTransactions">
                            <restriction base="String" />
                        </simpleType>
                    </schema>
                </configData>
            </validator>
            <annotation type="com.lombardisoftware.core.xml.XMLTypeAnnotation" version="2.0">
                <exclude isNull="true" />
                <anonymous isNull="true" />
                <local isNull="true" />
                <name isNull="true" />
                <namespace isNull="true" />
                <elementName isNull="true" />
                <elementNamespace isNull="true" />
                <protoTypeName isNull="true" />
                <baseTypeName isNull="true" />
                <specialType isNull="true" />
                <contentTypeVariety isNull="true" />
                <xscRef isNull="true" />
            </annotation>
        </definition>
    </twClass>
</teamworks>

