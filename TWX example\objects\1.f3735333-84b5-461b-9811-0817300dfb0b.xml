<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.f3735333-84b5-461b-9811-0817300dfb0b" name="Get bank BIC codes">
        <lastModified>*************</lastModified>
        <lastModifiedBy>abdelrahman.saleh</lastModifiedBy>
        <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.7ae56528-be45-461b-8366-bd4f74202552</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5805</guid>
        <versionId>51adad51-fef8-40a1-bfc8-cdc9146187c5</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:415d794a2c221205:3dfd662b:18a375ad0dc:-2e00" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b74db939-5f25-4f31-860a-2afc7b2a9d64"},{"incoming":["e03acc50-4b43-462b-809c-d797923ccf73"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5803"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"1a9523ef-4729-404d-88fc-10eaebdff708"},{"targetRef":"7ae56528-be45-461b-8366-bd4f74202552","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To get list of bic codes","declaredType":"sequenceFlow","id":"2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d","sourceRef":"b74db939-5f25-4f31-860a-2afc7b2a9d64"},{"startQuantity":1,"outgoing":["acae3377-def0-4e0c-8bb9-0dd367e18a45"],"incoming":["2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":162,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"get list of bic codes","dataInputAssociation":[{"targetRef":"2055.4ad7fd40-8451-480a-840d-e4350d23f2ba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.1094baed-e916-4772-8bd8-91a41441f7ad","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.*************-4d10-8b61-5e94d3ad31bd","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC creation and amendment\""]}}]},{"targetRef":"2055.a5891acc-745c-47e0-825a-7e040254d8ec","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.034a46fd-24a2-4243-84ce-38fb46ffc959","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.21defa96-4307-47f8-8cb7-e0bc7234481b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"7ae56528-be45-461b-8366-bd4f74202552","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.listOfValues"]}}],"sourceRef":["2055.bb8dd17c-590d-440f-8545-364fdb26328e"]}],"calledElement":"1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62"},{"targetRef":"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To Map output","declaredType":"sequenceFlow","id":"acae3377-def0-4e0c-8bb9-0dd367e18a45","sourceRef":"7ae56528-be45-461b-8366-bd4f74202552"},{"startQuantity":1,"outgoing":["e03acc50-4b43-462b-809c-d797923ccf73"],"incoming":["acae3377-def0-4e0c-8bb9-0dd367e18a45"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":415,"y":57,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.results = new tw.object.listOf.String();\r\nif(tw.local.listOfValues != null)\r\n{\r\n\tfor(var i=0;i&lt;tw.local.listOfValues.listLength;i++)\r\n\t{\r\n\t\ttw.local.results[i] = \"\";\r\n\t\ttw.local.results[i] = tw.local.listOfValues[i];\r\n\t}\r\n}\r\n"]}},{"targetRef":"1a9523ef-4729-404d-88fc-10eaebdff708","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"e03acc50-4b43-462b-809c-d797923ccf73","sourceRef":"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"var autoObject = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject[0] = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"listOfValues","isCollection":true,"declaredType":"dataObject","id":"2056.f5dee66e-6b9e-46be-8d29-874648095e3d"},{"incoming":["82ba0295-2ba9-470d-81b6-ef511e423068","42c654ba-fcee-4940-8ff4-36237aea2653"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"abf8ccbb-47b9-49bf-8345-125ebddc5e72","otherAttributes":{"eventImplId":"95fe0dd6-856c-4b5a-8b7f-8cd74a17a168"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":290,"y":214,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"62081ca6-1b8d-4c67-81a8-1cf00f284a2a"},{"parallelMultiple":false,"outgoing":["42c654ba-fcee-4940-8ff4-36237aea2653"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"a724f10c-ddb3-4b91-8565-083f4cac09e8"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"f49a74c3-b8fb-495e-82bb-4f9de383efb3","otherAttributes":{"eventImplId":"bb2aefb4-85e6-45ba-8761-2b73e2f887c0"}}],"attachedToRef":"ca7ab7c3-fed9-4930-86e4-d484be8d4e6d","extensionElements":{"nodeVisualInfo":[{"width":24,"x":450,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"e80a291b-dd2b-47af-8304-9b4cb2195ff8","outputSet":{}},{"parallelMultiple":false,"outgoing":["82ba0295-2ba9-470d-81b6-ef511e423068"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"337db7fe-9920-42d9-8a37-a580e02e1a26"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"d391214b-9104-43a2-83ab-6d225d047c3f","otherAttributes":{"eventImplId":"676dd3d3-749f-4e9d-8eb8-7e3ac66d3978"}}],"attachedToRef":"7ae56528-be45-461b-8366-bd4f74202552","extensionElements":{"nodeVisualInfo":[{"width":24,"x":171,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f","outputSet":{}},{"targetRef":"62081ca6-1b8d-4c67-81a8-1cf00f284a2a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"82ba0295-2ba9-470d-81b6-ef511e423068","sourceRef":"dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f"},{"targetRef":"62081ca6-1b8d-4c67-81a8-1cf00f284a2a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"42c654ba-fcee-4940-8ff4-36237aea2653","sourceRef":"e80a291b-dd2b-47af-8304-9b4cb2195ff8"}],"laneSet":[{"id":"7c93cb9a-9d31-4de8-8cb1-63da23bec364","lane":[{"flowNodeRef":["b74db939-5f25-4f31-860a-2afc7b2a9d64","1a9523ef-4729-404d-88fc-10eaebdff708","7ae56528-be45-461b-8366-bd4f74202552","ca7ab7c3-fed9-4930-86e4-d484be8d4e6d","62081ca6-1b8d-4c67-81a8-1cf00f284a2a","e80a291b-dd2b-47af-8304-9b4cb2195ff8","dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"c9cc53cd-ec4b-460c-8b79-e8fd70bbeb8f","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get bank BIC codes","declaredType":"process","id":"1.f3735333-84b5-461b-9811-0817300dfb0b","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":true,"id":"2055.5c07015a-cecb-4b9d-8228-49a177850bf6"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"********\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"data","isCollection":false,"id":"2055.a7371130-64dd-40de-8d61-bedd5c7a412b"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a7371130-64dd-40de-8d61-bedd5c7a412b</processParameterId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"********"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f69b94ae-1be9-46e3-bb44-b823a8495ab9</guid>
            <versionId>6da7e77d-e344-43c1-9758-7775dabdfa64</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.5c07015a-cecb-4b9d-8228-49a177850bf6</processParameterId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <parameterType>2</parameterType>
            <isArrayOf>true</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>dfe007d2-c0f9-4852-9ad2-ddb490f285ce</guid>
            <versionId>e02902ba-5e25-41c9-be2f-1e11c74d57bf</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.880b4f86-b85c-4e62-9cc8-7e3dacb5c162</processParameterId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>698dd17e-55ef-448b-a199-8956d7d12572</guid>
            <versionId>41fbc600-bd3f-44da-8f26-5706aa14ad8e</versionId>
        </processParameter>
        <processVariable name="listOfValues">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.f5dee66e-6b9e-46be-8d29-874648095e3d</processVariableId>
            <description isNull="true" />
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "";
autoObject</defaultValue>
            <guid>14049fe2-64ff-4678-8368-3ed5067a1444</guid>
            <versionId>eda03136-db1a-4008-b372-40f664e54508</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.1a9523ef-4729-404d-88fc-10eaebdff708</processItemId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.b24cbe5a-322c-4722-960b-5a596ca09a05</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5803</guid>
            <versionId>0d36980a-9cf4-4a55-a329-f4be6eca1dc5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.b24cbe5a-322c-4722-960b-5a596ca09a05</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>d9a37e16-c361-4c8d-b7ce-74a1b9741532</guid>
                <versionId>10a31c12-344d-4612-94dc-cfc61cbba707</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a</processItemId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.47b14728-7d8a-4424-ac2d-bf47f0b8e353</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5685</guid>
            <versionId>5e041cd7-7448-4df5-a5af-79685077c003</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="290" y="214">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.47b14728-7d8a-4424-ac2d-bf47f0b8e353</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>4d7d52d8-c7b3-4263-8881-fd115773655e</guid>
                <versionId>dd83b077-2724-4689-b06a-48d7c2211816</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.65b86ba3-cf43-4c2e-a922-6980e5623c7c</parameterMappingId>
                    <processParameterId>2055.880b4f86-b85c-4e62-9cc8-7e3dacb5c162</processParameterId>
                    <parameterMappingParentId>3007.47b14728-7d8a-4424-ac2d-bf47f0b8e353</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>cb3ebf65-2e76-4f41-8bcc-57a31bf91578</guid>
                    <versionId>2ea9a718-0dc0-487f-aca3-747a46dbfa4c</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.7ae56528-be45-461b-8366-bd4f74202552</processItemId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <name>get list of bic codes</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a</errorHandlerItemId>
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-57ec</guid>
            <versionId>5f96e2be-3f3e-4418-8f79-3cdfb6ab7be5</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="162" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error1</name>
                <locationId>bottomLeft</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5685</errorHandlerItem>
                <errorHandlerItemId>2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62</attachedProcessRef>
                <guid>d4651a6e-5408-4be3-8c0f-f26bca980328</guid>
                <versionId>b67b89b9-a1ea-404e-921e-fee2e3c4cdb0</versionId>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.be44b871-28e8-4456-b6ce-d954f67034b6</parameterMappingId>
                    <processParameterId>2055.a5891acc-745c-47e0-825a-7e040254d8ec</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>65238cad-15af-4c9c-ab90-184db627a864</guid>
                    <versionId>04449fe1-6dc0-4899-ac84-61c14fae956e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="bicCodes">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.01afab78-98f5-4c93-a3dc-261ef476f104</parameterMappingId>
                    <processParameterId>2055.bb8dd17c-590d-440f-8545-364fdb26328e</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.listOfValues</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>2d0301cf-e4ae-4cd7-94a5-6056d0d10f1a</guid>
                    <versionId>10f84cef-b2b1-4704-ba06-a61635e250ad</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c84aa355-7219-4d80-8a1a-b0e2798dca78</parameterMappingId>
                    <processParameterId>2055.1094baed-e916-4772-8bd8-91a41441f7ad</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b8aa884a-dacf-48bf-bb48-d37a80f64c17</guid>
                    <versionId>2196b854-1655-4266-8609-889656d7d6bd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.bc90b3a3-c22d-4b56-84d6-d9810cb3e55f</parameterMappingId>
                    <processParameterId>2055.21defa96-4307-47f8-8cb7-e0bc7234481b</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>124694e4-7a67-4649-af81-3fe8e3e2a9ee</guid>
                    <versionId>431a8c25-e265-47ae-8b5e-6eb60738cd7f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a867490b-b8ab-436f-8a78-3b84e92099a9</parameterMappingId>
                    <processParameterId>2055.034a46fd-24a2-4243-84ce-38fb46ffc959</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>8bff6d4e-f2a5-40c7-a8f3-49f13b87fb7a</guid>
                    <versionId>46dceaff-f1f2-4ff6-9adb-2244e074d7dd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c65b35e6-9d91-4540-9757-76ad0c0531b7</parameterMappingId>
                    <processParameterId>2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>08af194b-eecc-4273-986f-163ab6bac521</guid>
                    <versionId>4ed072f3-de2d-49d7-8354-3bb6d54bf8f7</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5517de03-88db-4006-8c2c-be7aad7d6f76</parameterMappingId>
                    <processParameterId>2055.3dfd9149-fb93-49c0-8d6c-3bb6a31c868b</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b7708845-90f4-43f1-b58f-6804841812d0</guid>
                    <versionId>64f08c15-4199-46ec-a459-00d6f18ac3e8</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.d0d0d033-27f9-4e6d-88db-642dc106a9bf</parameterMappingId>
                    <processParameterId>2055.*************-4d10-8b61-5e94d3ad31bd</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC creation and amendment"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>67ba4dec-a4d5-4ef3-a44e-512dce8b0ac0</guid>
                    <versionId>7291425c-dcbc-4c5b-98e3-5d7c134669de</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.a3274fa2-445d-4279-8e2f-747c085794c7</parameterMappingId>
                    <processParameterId>2055.390182d1-c178-4449-8178-edd289314ab2</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>84e932df-f9b8-4c4e-83b1-8532c151860a</guid>
                    <versionId>832c8d37-b8c5-48ab-868f-fac6c8dc9d45</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.8c4d93f3-86d0-4e23-b2a6-450042d50b59</parameterMappingId>
                    <processParameterId>2055.9a271b13-fe7a-4bc9-8573-99028a4ff122</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>da11a6de-8cda-4ddb-aa25-d89eca41ad5e</guid>
                    <versionId>a9329376-d373-4c51-a4e3-c0189472d514</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="customerNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.87ed2962-4611-45bb-85c5-ab5beda7b038</parameterMappingId>
                    <processParameterId>2055.4ad7fd40-8451-480a-840d-e4350d23f2ba</processParameterId>
                    <parameterMappingParentId>3012.ea885079-dccb-4176-8201-1fbb0ace9852</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d507806e-8fcb-419c-ab6e-8b7e6204e9e4</guid>
                    <versionId>f69f2b52-134b-45f1-9b07-3cc700f15a31</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d</processItemId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.cfa93689-0513-4dc7-8192-6ca2aebfea48</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a</errorHandlerItemId>
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-57ed</guid>
            <versionId>e46eae10-8f34-4426-a7e8-36fa6b1cfe1f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="415" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5685</errorHandlerItem>
                <errorHandlerItemId>2025.62081ca6-1b8d-4c67-81a8-1cf00f284a2a</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="rightCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.cfa93689-0513-4dc7-8192-6ca2aebfea48</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.results = new tw.object.listOf.String();&#xD;
if(tw.local.listOfValues != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.listOfValues.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = "";&#xD;
		tw.local.results[i] = tw.local.listOfValues[i];&#xD;
	}&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>3813dc94-6b4d-46fe-915c-acec7c59a1f0</guid>
                <versionId>9ab8b2f3-1871-46ca-8c68-49298c01bf00</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.7ae56528-be45-461b-8366-bd4f74202552</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get bank BIC codes" id="1.f3735333-84b5-461b-9811-0817300dfb0b" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.a7371130-64dd-40de-8d61-bedd5c7a412b">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"********"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="true" id="2055.5c07015a-cecb-4b9d-8228-49a177850bf6" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="7c93cb9a-9d31-4de8-8cb1-63da23bec364">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="c9cc53cd-ec4b-460c-8b79-e8fd70bbeb8f" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b74db939-5f25-4f31-860a-2afc7b2a9d64</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>1a9523ef-4729-404d-88fc-10eaebdff708</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7ae56528-be45-461b-8366-bd4f74202552</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>ca7ab7c3-fed9-4930-86e4-d484be8d4e6d</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>62081ca6-1b8d-4c67-81a8-1cf00f284a2a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e80a291b-dd2b-47af-8304-9b4cb2195ff8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b74db939-5f25-4f31-860a-2afc7b2a9d64">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="1a9523ef-4729-404d-88fc-10eaebdff708">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:9df5bce005da774d:-5bc6ffcb:18a2db8ad4b:-5803</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>e03acc50-4b43-462b-809c-d797923ccf73</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b74db939-5f25-4f31-860a-2afc7b2a9d64" targetRef="7ae56528-be45-461b-8366-bd4f74202552" name="To get list of bic codes" id="2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.12285efa-55e1-4b26-a3e7-0b8ec7cb6f62" isForCompensation="false" startQuantity="1" completionQuantity="1" name="get list of bic codes" id="7ae56528-be45-461b-8366-bd4f74202552">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="162" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.47556708-f8cd-4ea8-8eb6-e0a208d3b43d</ns16:incoming>
                        
                        
                        <ns16:outgoing>acae3377-def0-4e0c-8bb9-0dd367e18a45</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4ad7fd40-8451-480a-840d-e4350d23f2ba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.1094baed-e916-4772-8bd8-91a41441f7ad</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4d10-8b61-5e94d3ad31bd</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC creation and amendment"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a5891acc-745c-47e0-825a-7e040254d8ec</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.034a46fd-24a2-4243-84ce-38fb46ffc959</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ce5a4152-49c6-4bf0-8ad0-af9afb12ebf0</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.21defa96-4307-47f8-8cb7-e0bc7234481b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.bb8dd17c-590d-440f-8545-364fdb26328e</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.listOfValues</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="7ae56528-be45-461b-8366-bd4f74202552" targetRef="ca7ab7c3-fed9-4930-86e4-d484be8d4e6d" name="To Map output" id="acae3377-def0-4e0c-8bb9-0dd367e18a45">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="ca7ab7c3-fed9-4930-86e4-d484be8d4e6d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="415" y="57" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>acae3377-def0-4e0c-8bb9-0dd367e18a45</ns16:incoming>
                        
                        
                        <ns16:outgoing>e03acc50-4b43-462b-809c-d797923ccf73</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.results = new tw.object.listOf.String();&#xD;
if(tw.local.listOfValues != null)&#xD;
{&#xD;
	for(var i=0;i&lt;tw.local.listOfValues.listLength;i++)&#xD;
	{&#xD;
		tw.local.results[i] = "";&#xD;
		tw.local.results[i] = tw.local.listOfValues[i];&#xD;
	}&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="ca7ab7c3-fed9-4930-86e4-d484be8d4e6d" targetRef="1a9523ef-4729-404d-88fc-10eaebdff708" name="To End" id="e03acc50-4b43-462b-809c-d797923ccf73">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="true" name="listOfValues" id="2056.f5dee66e-6b9e-46be-8d29-874648095e3d">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">var autoObject = new tw.object.listOf.toolkit.TWSYS.String();
autoObject[0] = "";
autoObject</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:endEvent name="End Event" id="62081ca6-1b8d-4c67-81a8-1cf00f284a2a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="290" y="214" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>82ba0295-2ba9-470d-81b6-ef511e423068</ns16:incoming>
                        
                        
                        <ns16:incoming>42c654ba-fcee-4940-8ff4-36237aea2653</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="abf8ccbb-47b9-49bf-8345-125ebddc5e72" eventImplId="95fe0dd6-856c-4b5a-8b7f-8cd74a17a168">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="ca7ab7c3-fed9-4930-86e4-d484be8d4e6d" parallelMultiple="false" name="Error" id="e80a291b-dd2b-47af-8304-9b4cb2195ff8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="450" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>42c654ba-fcee-4940-8ff4-36237aea2653</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="a724f10c-ddb3-4b91-8565-083f4cac09e8" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="f49a74c3-b8fb-495e-82bb-4f9de383efb3" eventImplId="bb2aefb4-85e6-45ba-8761-2b73e2f887c0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="7ae56528-be45-461b-8366-bd4f74202552" parallelMultiple="false" name="Error1" id="dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="171" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>82ba0295-2ba9-470d-81b6-ef511e423068</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="337db7fe-9920-42d9-8a37-a580e02e1a26" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="d391214b-9104-43a2-83ab-6d225d047c3f" eventImplId="676dd3d3-749f-4e9d-8eb8-7e3ac66d3978">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="dd1c9c2b-dba7-4781-8a8c-9818c6b9c82f" targetRef="62081ca6-1b8d-4c67-81a8-1cf00f284a2a" name="To End Event" id="82ba0295-2ba9-470d-81b6-ef511e423068">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="e80a291b-dd2b-47af-8304-9b4cb2195ff8" targetRef="62081ca6-1b8d-4c67-81a8-1cf00f284a2a" name="To End Event" id="42c654ba-fcee-4940-8ff4-36237aea2653">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>rightCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Map output">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.acae3377-def0-4e0c-8bb9-0dd367e18a45</processLinkId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.7ae56528-be45-461b-8366-bd4f74202552</fromProcessItemId>
            <endStateId>guid:4a4dc2e3dad408dc:-15ab7c7b:188a9fa6c36:6b9a</endStateId>
            <toProcessItemId>2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d</toProcessItemId>
            <guid>fb506d55-d85f-4816-8b49-11d74df6e7f0</guid>
            <versionId>8d117563-29ee-4a5a-b4f7-6f824d5f9590</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.7ae56528-be45-461b-8366-bd4f74202552</fromProcessItemId>
            <toProcessItemId>2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.e03acc50-4b43-462b-809c-d797923ccf73</processLinkId>
            <processId>1.f3735333-84b5-461b-9811-0817300dfb0b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.1a9523ef-4729-404d-88fc-10eaebdff708</toProcessItemId>
            <guid>ecaa523a-1321-4ba8-8042-c3624b92710d</guid>
            <versionId>fd66e52b-2604-4316-aba5-087ed7a9cb36</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.ca7ab7c3-fed9-4930-86e4-d484be8d4e6d</fromProcessItemId>
            <toProcessItemId>2025.1a9523ef-4729-404d-88fc-10eaebdff708</toProcessItemId>
        </link>
    </process>
</teamworks>

