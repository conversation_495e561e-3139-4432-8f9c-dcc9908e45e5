<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9" name="ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير">
        <bpdParameter name="ODCRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.494433ff-7b97-4905-897c-46b7b7ddef4a</bpdParameterId>
            <bpdId>25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>0</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isReadOnly>false</isReadOnly>
            <guid>6e44e1ac-bdd0-4dbc-8c26-33c28176aa8f</guid>
            <versionId>0c84c3b8-0133-4233-bf90-1fffc27aa8aa</versionId>
        </bpdParameter>
        <bpdParameter name="routingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.21e93e70-eac8-465f-8355-23d0627dcd57</bpdParameterId>
            <bpdId>25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
            <seq>1</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue>var autoObject = new tw.object.odcRoutingDetails();
autoObject.hubCode = "";
autoObject.branchCode = "";
autoObject.initiatorUser = "";
autoObject.branchName = "";
autoObject.hubName = "";
autoObject.branchSeq = "";
autoObject</defaultValue>
            <isReadOnly>false</isReadOnly>
            <guid>56e27da4-52af-43dc-a842-d5935dbdb3a4</guid>
            <versionId>0fbc33bb-1eed-4bd3-85a4-0d3246202da5</versionId>
        </bpdParameter>
        <lastModified>*************</lastModified>
        <lastModifiedBy>bawadmin</lastModifiedBy>
        <bpdId>25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.e0fee5e6-fd77-44ca-9ee6-dbcb6dbff7d7</perfMetricParticipantRef>
        <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.227b3aa9-3a4a-47d4-8696-83ca726c27ab</ownerTeamParticipantRef>
        <timeScheduleType isNull="true" />
        <timeScheduleName>NBEWork</timeScheduleName>
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName>NBEHoliday</holidayScheduleName>
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone>Africa/Cairo</timezone>
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="10a2a33e-c5ad-450c-847a-92088e7bf3fd" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير" id="25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation /&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot; Request Type: &amp;quot;+ tw.local.ODCRequest.requestType.name +&amp;quot; , &amp;quot;+&amp;quot; CIF: &amp;quot;+ tw.local.ODCRequest.cif +&amp;quot; , &amp;quot; +&amp;quot;Request Number: &amp;quot; +tw.system.process.instanceId" dueDateEnabled="false" atRiskCalcEnabled="false" enableTracking="true" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at16896764894071689755418046"&gt;&lt;ns4:defaultInstanceUI serviceRef="1.a3badb88-0400-49ae-ac1d-0e0ace050eb7"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.ee3d4996-a568-4b68-8e0c-52ab22272c00&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.fa98b43d-d8a9-468a-8bdd-d4ffc9b3ae54&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"&gt;tw.local.routingDetails&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.4ea51b66-9b82-4428-8cd6-2e036716f837&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.regeneratedRemittanceLetterTitleVIS&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.af46a6ca-649c-4e5f-88f0-0ac184ba3164&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.loggedInUser&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.097535cf-d177-4dec-8f63-720ff6076b41&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3dc2865b-a6c8-41bf-8ff8-92060bcb614f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a66486cc-b4c3-4304-8a3e-672183d52d76&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.28f04f83-9bff-431c-8908-eaf9ad987427&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.fromTradeFo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.6fe78734-b040-40f1-86c9-d5b9be0b52f7&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.compApprovalInit&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.54a14133-823f-4dc0-8c51-5bcafa1cd70c&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.6a35b7cb-f8b8-49a9-8647-5d6778a89a36&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.fromExechecker&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.2f5d1275-bf3e-4237-8b05-394793647357&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.role&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.d4b2b6f1-6f73-49bb-8543-eb2c908768f4&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:defaultInstanceUI&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:timezone&gt;Africa/Cairo&lt;/ns4:timezone&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="bcd34da4-3c2d-425d-9155-12b12732b272" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:extensionElements&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.dbbaa047-8f02-4397-b1b5-41f11b0256b3" epvProcessLinkId="cda44512-ca02-4564-8db5-c334afd741f0" /&gt;&lt;ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="855cdf49-51b0-49e6-8ed5-e254ca0760ca" /&gt;&lt;ns3:epvProcessLinkRef epvId="21.062854b5-6513-4da8-84ab-0126f90e550d" epvProcessLinkId="f26601ea-3952-407f-82b8-58b3559b19fc" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:dataInput name="ODCRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2007.494433ff-7b97-4905-897c-46b7b7ddef4a"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:searchableField id="aa2ba285-68fc-4db6-887a-98157eea19bc" alias="ParentRequestNumber" path=".parentRequestNo" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="33d7b82e-c34d-40d1-8582-f4aaae708391" alias="RequestDate" path=".appInfo.requestDate" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="bef40a0e-a592-44c7-852d-2ffc579d842e" alias="RequestStatus" path=".appInfo.status" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns3:defaultValue useDefault="false" /&gt;&lt;ns4:searchableField id="470c65d8-d6fe-4bdc-8f4e-3bbd995d3801" alias="InitiatorHubBranch" path=".initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="a9775f48-3dc0-418e-8c2a-4811f69fdeae" alias="CustomerCIF" path=".cif" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="0d27e84c-381d-4224-8650-1d8613ab3dbb" alias="CustomerName" path=".customerName" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="8db758e8-53b3-4d02-8e8d-9f01835a111c" alias="InitiatorUserName" path=".appInfo.initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataInput&gt;&lt;ns15:dataInput name="routingDetails" itemSubjectRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727" isCollection="false" id="2007.21e93e70-eac8-465f-8355-23d0627dcd57"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:defaultValue useDefault="false"&gt;var autoObject = new tw.object.odcRoutingDetails();&#xD;
autoObject.hubCode = "";&#xD;
autoObject.branchCode = "";&#xD;
autoObject.initiatorUser = "";&#xD;
autoObject.branchName = "";&#xD;
autoObject.hubName = "";&#xD;
autoObject.branchSeq = "";&#xD;
autoObject&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataInput&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="e73d1395-8b43-4a94-9bc4-4082717d5d15" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="e2a9c4d9-7902-498d-8878-675b500e124d" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="0ddd9c24-1288-4323-89f1-22d5846ccfc5"&gt;&lt;ns15:lane name="Branch / Hub Maker" partitionElementRef="24.caa7093a-16aa-42da-b2f5-0770fabe40ea" id="0108b8f0-a19a-42a7-8eb9-3d676f5357e5" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;ad74e9b7-aca8-47bb-bdd4-b851c1e1eb66&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;4b4b129a-b268-4428-b193-f9f6d8e5c929&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;287e7737-bed8-434a-807f-3797db93e815&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;8b0ddbba-564f-4b55-8826-d9aa92c61f99&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;b5b64d78-cdaa-435a-8363-9a761092db3c&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;9b4225c3-d93b-46d8-84b4-540d959b0610&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;326824ab-2799-49e6-8872-aebb656ee215&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;a7d6cba3-a58d-4563-8023-24a437383585&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;8c8e382b-2494-478d-806d-3df09d620dd3&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;8bc1cd19-36b6-47e8-8256-4e8e30cda2fe&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;9fb986f2-208c-4dbe-8f23-0e203af7536a&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Branch / Hub compliance Rep." partitionElementRef="24.b1b8b540-604c-41d9-8fd7-6253273000f8" id="7cf73b9e-bbb6-487c-aeac-246c93572b11" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;db23215a-a1e0-49ab-b38e-3edcc5d7ba2c&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;5226f795-1fb6-4834-8181-f98a858d441f&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;3dce5eb9-7e21-4bf0-ba6d-479c1e68ff49&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;17e7e782-08b2-46b6-8b8d-caa96ae3b253&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Trade Front Office" partitionElementRef="24.e92aee43-caab-4c22-88e1-3f7edf14b1ba" id="59dab8ae-dc5e-4e82-9393-ac6d8a009b79" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;dad60095-00d8-4276-b6db-83336ef5acac&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;65b61876-d96c-474b-b1e2-2fd925caebee&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;5cf8c7a9-7f20-42f7-8569-ff341d853fa2&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Compliance" partitionElementRef="24.6b09bd69-9498-4f11-beaf-41aba731a014" id="092f9ca7-4802-4949-ae45-1296d468956b" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="603" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;96c4a0c7-8055-4af4-8adb-e0ff90566b97&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Execution Hub Maker" partitionElementRef="24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce" id="d3721311-e835-4954-b408-382d3e3abb75" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="804" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;5baef4dc-ae97-4dde-9e23-d644b4267e2d&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;a8cd289b-88bc-4eda-84be-c4498bf6740f&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;eab0f5ea-2c91-4bb7-84db-78a5cb1144e6&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Execution Hub Checker" partitionElementRef="24.8e005024-3fe0-4848-8c3c-f1e9483900c6" id="90756fec-b829-4497-9edd-d26584678c04" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="1005" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;9e44cde9-a325-4fa2-a58c-5eb215044d87&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;5585ce17-20f3-4fc4-8f33-ca744aa7e44a&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;764873e6-cc4a-4397-8760-cf7a29db05c5&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;5548e0ee-6b10-45b7-8a85-94e950182ce3&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="121232be-1044-4536-9224-6b3eb0346f9a" ns4:isSystemLane="true"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="1206" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;042391f2-3dba-4a35-839b-ae74a7f49733&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;90150625-9d40-41d0-815e-977855cf26f3&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="ad74e9b7-aca8-47bb-bdd4-b851c1e1eb66"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="38" y="32" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:default&gt;53e3c85e-3389-4ca6-8c0c-14b9c1522adb&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;true&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;53e3c85e-3389-4ca6-8c0c-14b9c1522adb&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:callActivity calledElement="1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1" default="e06a7a55-c1ab-45b6-aa93-41fd9ec977f9" name="ACT01-Create/Amend ODC Request" id="4b4b129a-b268-4428-b193-f9f6d8e5c929"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="227" y="26" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Create / Amend ODC Request - إصدار /تعديل تحصيل مستندي تصدير&amp;lt;#= tw.system.process.instanceId #&amp;gt;&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.CACT01)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;false&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;0ea2a405-a03f-45bc-bc39-30305c53a60a&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;53e3c85e-3389-4ca6-8c0c-14b9c1522adb&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;e06a7a55-c1ab-45b6-aa93-41fd9ec977f9&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.52b23fff-ad18-4c9c-8d4b-da25fa353d74&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3db632ba-2dff-49ab-81f3-ecc3512ed61a&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.e60856e2-88c3-449a-8b95-682aa6f088ab&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.branchSeq&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c17f6b1c-420e-4316-8602-278ac2e5f98d&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.branchName&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.262e9fc1-8a4f-4bee-81b6-3ebbcd392f6d&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.df6d7638-3ff3-45a9-817e-e243fd7e7807&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.initiatorUser&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.5e731f35-27b7-4ff1-852c-7479e82b9115&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.c6e44d18-bb22-407c-8c94-33e273a84088&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.17b2fc8d-62c3-4cbc-8046-1daaee9f0658&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.regeneratedRemittanceLetterTitleVIS&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.4371b561-70e9-4ac1-898e-91948aadac07&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.42d6ab5b-c60b-4768-89d1-cabaa3f9dd78&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.role&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.1ae8623e-a0d0-455e-8199-92f720530682&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="LastUser" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b849887a-3e1e-4633-be89-6e2adce84383&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.branchCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"Branch_Operation_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.d5717fc2-69b2-4168-a2af-80632cedf962&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_CR_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="ad74e9b7-aca8-47bb-bdd4-b851c1e1eb66" targetRef="4b4b129a-b268-4428-b193-f9f6d8e5c929" name="To ACT01-Create/Amend ODC Request" id="53e3c85e-3389-4ca6-8c0c-14b9c1522adb"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="4eee37d5-da40-4f4d-8074-fae3c69b1c1a" name="DecisionACT01?" id="287e7737-bed8-434a-807f-3797db93e815"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="388" y="117" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;e06a7a55-c1ab-45b6-aa93-41fd9ec977f9&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;4eee37d5-da40-4f4d-8074-fae3c69b1c1a&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;93b2c7ce-e1fd-4ef8-932c-390cd287de99&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="4b4b129a-b268-4428-b193-f9f6d8e5c929" targetRef="287e7737-bed8-434a-807f-3797db93e815" name="To DecisionACT01?" id="e06a7a55-c1ab-45b6-aa93-41fd9ec977f9"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightBottom&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.e31385fc-75d6-4686-b548-3d16aa42adf3" default="c71d715f-783a-41f8-8118-b13a12e84b50" name="ACT02-Review ODC Request By Compliace Rep." id="db23215a-a1e0-49ab-b38e-3edcc5d7ba2c"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="373" y="44" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review ODC Request  By Compliance Rep - مراجعه طلب تحصيل مستندي تصدير من إدارة الالتزام &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.CACT02)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;4eee37d5-da40-4f4d-8074-fae3c69b1c1a&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;c71d715f-783a-41f8-8118-b13a12e84b50&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.69a1c013-d5ea-4d9f-8e7a-253a4c53ee83&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.85bb1b3b-6aff-440d-8a91-036bad32bb3a&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.regeneratedRemittanceLetterTitleVIS&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.06e83dcd-aab9-473a-8905-39ca48c61d56&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.2447e3bc-7548-4065-8bbf-89b5de603e41&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.role&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.ac5cb29b-06ab-412f-8129-5bd7fb6dc779&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.22fa5dfa-ab1c-4768-82c7-52b63dfc9e88&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b849887a-3e1e-4633-be89-6e2adce84383&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.branchCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"Compliance_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.d5717fc2-69b2-4168-a2af-80632cedf962&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_COMP_CHKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="287e7737-bed8-434a-807f-3797db93e815" targetRef="db23215a-a1e0-49ab-b38e-3edcc5d7ba2c" name="To ACT02-Review ODC Request By Compliace Rep." id="4eee37d5-da40-4f4d-8074-fae3c69b1c1a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="54e8901a-d22f-4539-a2b4-b20a59db335e" name="DecisionACT02?" id="5226f795-1fb6-4834-8181-f98a858d441f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="528" y="117" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;c71d715f-783a-41f8-8118-b13a12e84b50&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;54e8901a-d22f-4539-a2b4-b20a59db335e&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;b48c134b-0a40-4b3a-a3e2-49ef381c0ed0&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="db23215a-a1e0-49ab-b38e-3edcc5d7ba2c" targetRef="5226f795-1fb6-4834-8181-f98a858d441f" name="To Review ODC Request By Compliace Rep." id="c71d715f-783a-41f8-8118-b13a12e84b50"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="5226f795-1fb6-4834-8181-f98a858d441f" targetRef="4b4b129a-b268-4428-b193-f9f6d8e5c929" name="Back To Initiator" id="c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046" default="7b55ff18-3427-41ed-85c4-33a0b76256d0" name="ACT03-Review ODC Request By Trade FO" id="dad60095-00d8-4276-b6db-83336ef5acac"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="512" y="33" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt; Review ODC Request By Trade FO&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.CACT03)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;54e8901a-d22f-4539-a2b4-b20a59db335e&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;52ddc0dd-c87e-48d3-812a-b3628fedad36&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;527dc6df-f888-4cb1-841f-3bfd1d2a1363&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;b6c9ad87-602f-4a3d-8445-3d850bf99cff&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;7b55ff18-3427-41ed-85c4-33a0b76256d0&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c3e60b22-5baf-4042-9000-ca67a0ed85e2&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.9a7f935d-4a17-4085-8d13-ccfa64144506&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.regeneratedRemittanceLetterTitleVIS&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.876fefd1-1853-43e6-855e-a6d443bcc55d&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.approvalComment&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.2371dd53-f0ea-4b5e-8cf9-155e1386a13f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.ddf3e65a-a820-4c08-9ab8-7d8c06e7eb7d&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.b65aba1a-dd56-4a0c-8bd9-ecc16e9d6d70&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.fromTradeFo&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.2c437293-db72-4278-8707-cc40bf84c167&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.compApprovalInit&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.6bb45aa3-0cb2-4088-8331-380885045bd5&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.approvalComment&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="5226f795-1fb6-4834-8181-f98a858d441f" targetRef="dad60095-00d8-4276-b6db-83336ef5acac" name="Approve Request" id="54e8901a-d22f-4539-a2b4-b20a59db335e"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="TerminateAct02" id="3dce5eb9-7e21-4bf0-ba6d-479c1e68ff49"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="718" y="122" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;b48c134b-0a40-4b3a-a3e2-49ef381c0ed0&lt;/ns15:incoming&gt;&lt;ns15:terminateEventDefinition id="5374f911-4741-4b31-8ac4-5bb2b54b5585" eventImplId="05eea146-9054-4e70-84ba-ac623c1ea211"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:terminateEventSettings&gt;&lt;ns4:terminateEntireProcess&gt;false&lt;/ns4:terminateEntireProcess&gt;&lt;ns4:deleteProcessInstance&gt;false&lt;/ns4:deleteProcessInstance&gt;&lt;ns4:deleteCaseFolder&gt;false&lt;/ns4:deleteCaseFolder&gt;&lt;/ns4:terminateEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:terminateEventDefinition&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="5226f795-1fb6-4834-8181-f98a858d441f" targetRef="3dce5eb9-7e21-4bf0-ba6d-479c1e68ff49" name="Cancel" id="b48c134b-0a40-4b3a-a3e2-49ef381c0ed0"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.cancelRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.9321de6e-e6a5-435e-accb-0fbbd129c48a" default="40508b5e-6bda-444f-862e-31945299b061" name="ODC Execution - Initiation" id="5baef4dc-ae97-4dde-9e23-d644b4267e2d"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="900" y="44" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt; ODC Execution Hub - Initiation&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.CACT04)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.fromTradeFo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;false&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;93b2c7ce-e1fd-4ef8-932c-390cd287de99&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;184b3a6c-c429-41b1-b3b8-343ceb41569f&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;41f89811-100f-4299-8d85-be58356b40b1&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;418bd03c-835e-4213-8eb0-78580737aad5&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;40508b5e-6bda-444f-862e-31945299b061&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.regeneratedRemittanceLetterTitleVIS&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.21028eba-aa2f-4b93-806b-78b695c14ddf&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.fromExechecker&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.021ed4ee-8b5e-423d-8672-bdcaceed27d8&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.73739682-2486-426c-8701-7fc1c235c6a3&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.compApprovalInit&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.ODCRequest.FinancialDetailsFO.executionHub.value+"_EXE_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.6d4e9c1d-5624-494c-96fb-3bd94584f975" default="5b813e0d-568a-468f-86a5-36b7fd5bcdfa" name="Act05 - ODC Execution - Initiation - review" id="9e44cde9-a325-4fa2-a58c-5eb215044d87"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="960" y="49" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;ODC Execution Hub - Initiation Review&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.CACT05)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;40508b5e-6bda-444f-862e-31945299b061&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;5b813e0d-568a-468f-86a5-36b7fd5bcdfa&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.43609737-d968-42ad-9a14-2da88598005a&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.e3114267-71c4-4261-8f02-f45b08a80f8c&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.regeneratedRemittanceLetterTitleVIS&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.09800ab8-e6d8-4ee9-8954-dc0c77df48f0&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a532e1f6-27cc-44ab-8ba6-1ab2ea92ec2d&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.43126547-b78b-4e92-bab9-47081d86a36a&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.6b42b31e-df5e-4c9e-8914-5bda302e956c&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.fromExechecker&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.ODCRequest.FinancialDetailsFO.executionHub.value+"_EXE_CHKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="5baef4dc-ae97-4dde-9e23-d644b4267e2d" targetRef="9e44cde9-a325-4fa2-a58c-5eb215044d87" name="To Act05 - ODC Execution - Initiation - review" id="40508b5e-6bda-444f-862e-31945299b061"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomLeft&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:subProcess triggeredByEvent="false" default="6959e67f-fc93-4a5a-85eb-bbaf481c6675" name="Trade Compliance Unified subprocess" id="96c4a0c7-8055-4af4-8adb-e0ff90566b97"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="621" y="58" width="95" height="70" /&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.dbbaa047-8f02-4397-b1b5-41f11b0256b3" epvProcessLinkId="c755e699-8102-4d4d-8601-2fa0747feec6" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;6f9b07d5-973a-4ce7-9e30-8d07577aa333&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;048b1307-eea2-4c66-8a5f-3760004a8369&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;6959e67f-fc93-4a5a-85eb-bbaf481c6675&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;52ddc0dd-c87e-48d3-812a-b3628fedad36&lt;/ns15:outgoing&gt;&lt;ns15:laneSet id="4f8f2faa-bc76-45fe-86e2-fd76b446f7c5"&gt;&lt;ns15:lane name="Compliance" partitionElementRef="24.6b09bd69-9498-4f11-beaf-41aba731a014" id="dee7eb59-f491-4346-a98b-08a5b3b848cb" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;90571470-899e-44db-b0da-bb2842927ca3&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;75ad6090-c994-431b-90d3-803dc6b15c5a&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;bc337dcc-6688-472a-8487-e99cca9b9d71&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;f3fc74cf-9590-49b2-8297-07492e9ce8f3&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="system" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="1a0244d0-d147-40ea-84e4-d4ff72885a2e" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;726c29b6-ca92-4806-8dc0-3cea258f1845&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;2285ed84-ab47-40b1-8b3e-5545fc5020f8&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent isInterrupting="true" parallelMultiple="false" name="Subprocess Start" id="90571470-899e-44db-b0da-bb2842927ca3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="25" y="80" width="24" height="24" /&gt;&lt;ns3:default&gt;a74def41-38fa-4044-872e-87314182eafc&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;a74def41-38fa-4044-872e-87314182eafc&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:endEvent name="Subprocess End" id="75ad6090-c994-431b-90d3-803dc6b15c5a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="650" y="80" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;324570fb-767a-41e6-82b1-4caf0784ec32&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:callActivity calledElement="1.7276be8c-00f4-4766-949c-bcd033b050c3" default="324570fb-767a-41e6-82b1-4caf0784ec32" name="ACT07- Review ODC By Compliance" id="bc337dcc-6688-472a-8487-e99cca9b9d71"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="224" y="38" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:narrative /&gt;&lt;ns4:subject&gt; Review Request by Trade Compliance  – مراجعة طلب تحصيل مستندى تصدير من إدارة الالتزام &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;Number(tw.epv.ODCCreationSLA.CACT07)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;a74def41-38fa-4044-872e-87314182eafc&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;324570fb-767a-41e6-82b1-4caf0784ec32&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.6102a850-faf2-401e-9c7a-d8f3a6215da0&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3502bdff-abe8-4815-b2bb-71d90d6236ce&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.b698dbfb-84da-40a5-9db3-676815055e65"&gt;tw.local.ODCRequest.attachmentDetails.ecmProperties&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.3212d5b3-f692-41b3-a893-343dc5c3df01"&gt;tw.local.ODCRequest.attachmentDetails.folderID&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.80cf1e33-9711-481a-aaa4-e9508bea4af8&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.ODCRequest.initiator&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.6c11359f-601f-4a8a-adab-80428b282316&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.07383e61-26ca-4c95-9b61-00fbdbc9735e"&gt;tw.local.ODCRequest.attachmentDetails.attachment&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.compApprovalInit&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.1642e459-0600-4fe1-86f1-cead2df234da&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.approvalComment&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.dd027994-1915-4bca-8452-c0b7021d8794&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.fromTradeFo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.0f59046c-cb7e-4393-bec7-d27739cecd93&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.36778ac9-0d81-4996-991e-f909c6f6aa5a&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.e4654440-58a7-47b2-8f98-3eaa9cccad49"&gt;tw.local.ODCRequest.complianceComments&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.f89a842a-9d83-45bf-adeb-2837b1beb056&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.07383e61-26ca-4c95-9b61-00fbdbc9735e"&gt;tw.local.ODCRequest.attachmentDetails.attachment&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.4dfe48dc-794c-4131-85b0-9ac2fe08a1a5&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.approvalComment&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="90571470-899e-44db-b0da-bb2842927ca3" targetRef="bc337dcc-6688-472a-8487-e99cca9b9d71" name="To ACT07- Review ODC By Compliance" id="a74def41-38fa-4044-872e-87314182eafc"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="bc337dcc-6688-472a-8487-e99cca9b9d71" targetRef="75ad6090-c994-431b-90d3-803dc6b15c5a" name="To Subprocess End" id="324570fb-767a-41e6-82b1-4caf0784ec32"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.d7acf968-6740-4e52-b037-2049466eeeb2" default="700a2c9b-fbcb-49f8-8691-dd9afe661086" name="Escalation email service" id="726c29b6-ca92-4806-8dc0-3cea258f1845"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="482" y="42" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;ns4:deleteTaskOnCompletion&gt;false&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;853aa405-4ef8-4ca4-89a1-6264a3ae8c29&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;700a2c9b-fbcb-49f8-8691-dd9afe661086&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.9771d7e8-ca59-430e-8b1a-194cf04c1182&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="bc337dcc-6688-472a-8487-e99cca9b9d71" parallelMultiple="false" name="Error" id="f3fc74cf-9590-49b2-8297-07492e9ce8f3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="259" y="96" width="24" height="24" /&gt;&lt;ns3:default&gt;853aa405-4ef8-4ca4-89a1-6264a3ae8c29&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;853aa405-4ef8-4ca4-89a1-6264a3ae8c29&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="364c3569-8bef-4100-8997-cc68e719b1ca" eventImplId="b6a28059-e9a1-48d4-8661-e1d2216f08fd"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="f3fc74cf-9590-49b2-8297-07492e9ce8f3" targetRef="726c29b6-ca92-4806-8dc0-3cea258f1845" name="To Escalation email service" id="853aa405-4ef8-4ca4-89a1-6264a3ae8c29"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="End Event2" id="2285ed84-ab47-40b1-8b3e-5545fc5020f8"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="662" y="65" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;700a2c9b-fbcb-49f8-8691-dd9afe661086&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="726c29b6-ca92-4806-8dc0-3cea258f1845" targetRef="2285ed84-ab47-40b1-8b3e-5545fc5020f8" name="To End Event2" id="700a2c9b-fbcb-49f8-8691-dd9afe661086"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;/ns15:subProcess&gt;&lt;ns15:exclusiveGateway default="184b3a6c-c429-41b1-b3b8-343ceb41569f" name="Decision03?" id="6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="614" y="119" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;7b55ff18-3427-41ed-85c4-33a0b76256d0&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;6f9b07d5-973a-4ce7-9e30-8d07577aa333&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;0ea2a405-a03f-45bc-bc39-30305c53a60a&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;b4907ccb-6469-4e3b-bf69-f60ffb7d0b80&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;184b3a6c-c429-41b1-b3b8-343ceb41569f&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="dad60095-00d8-4276-b6db-83336ef5acac" targetRef="6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0" name="To Decision03?" id="7b55ff18-3427-41ed-85c4-33a0b76256d0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomRight&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0" targetRef="96c4a0c7-8055-4af4-8adb-e0ff90566b97" name="Approve" id="6f9b07d5-973a-4ce7-9e30-8d07577aa333"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.obtainApprovals&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0" targetRef="4b4b129a-b268-4428-b193-f9f6d8e5c929" name="Back to Initiator" id="0ea2a405-a03f-45bc-bc39-30305c53a60a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="TerminateACt03" id="65b61876-d96c-474b-b1e2-2fd925caebee"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="756" y="74" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;b4907ccb-6469-4e3b-bf69-f60ffb7d0b80&lt;/ns15:incoming&gt;&lt;ns15:terminateEventDefinition id="a67a95ad-7556-4f8c-a54e-e47a6a11e270" eventImplId="95908c5a-cf9f-4af9-8912-ace60122ffcf"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:terminateEventSettings&gt;&lt;ns4:terminateEntireProcess&gt;false&lt;/ns4:terminateEntireProcess&gt;&lt;ns4:deleteProcessInstance&gt;false&lt;/ns4:deleteProcessInstance&gt;&lt;ns4:deleteCaseFolder&gt;false&lt;/ns4:deleteCaseFolder&gt;&lt;/ns4:terminateEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:terminateEventDefinition&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0" targetRef="65b61876-d96c-474b-b1e2-2fd925caebee" name="Terminate" id="b4907ccb-6469-4e3b-bf69-f60ffb7d0b80"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.terminateRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="96c4a0c7-8055-4af4-8adb-e0ff90566b97" targetRef="eab0f5ea-2c91-4bb7-84db-78a5cb1144e6" name="To Compliance requested from FO?" id="6959e67f-fc93-4a5a-85eb-bbaf481c6675"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomRight&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="96c4a0c7-8055-4af4-8adb-e0ff90566b97" targetRef="dad60095-00d8-4276-b6db-83336ef5acac" name="To ACT03-Review ODC Request By Trade FO" id="52ddc0dd-c87e-48d3-812a-b3628fedad36"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topLeft&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="287e7737-bed8-434a-807f-3797db93e815" targetRef="5baef4dc-ae97-4dde-9e23-d644b4267e2d" name="To ODC Execution - Initiation" id="93b2c7ce-e1fd-4ef8-932c-390cd287de99"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topRight&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.submitRequestToHubDirectory&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0" targetRef="5baef4dc-ae97-4dde-9e23-d644b4267e2d" name="To ODC Execution - Initiation" id="184b3a6c-c429-41b1-b3b8-343ceb41569f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9" default="e2d7fff9-82e6-426f-84bc-5ee9713f7fce" name="Print Remittance Letter" id="8b0ddbba-564f-4b55-8826-d9aa92c61f99"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="1420" y="88" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Print Remittance Letter&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.CACT06)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;dd1ea0d1-61b8-4fc4-8e24-33e52806920f&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;ba01c515-0007-49ce-863f-df588771ad22&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;e2d7fff9-82e6-426f-84bc-5ee9713f7fce&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.8cdb34c3-63b3-4765-83e8-d4f953fdc147&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="LastUser" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b849887a-3e1e-4633-be89-6e2adce84383&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.branchCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"Branch_Operation_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.d5717fc2-69b2-4168-a2af-80632cedf962&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_CR_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:endEvent name="End Event" id="b5b64d78-cdaa-435a-8363-9a761092db3c"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1549" y="111" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;e2d7fff9-82e6-426f-84bc-5ee9713f7fce&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:endEvent name="End Event1" id="9b4225c3-d93b-46d8-84b4-540d959b0610"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1388" y="31" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;77c79597-0942-4363-85b5-30769aa3abac&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:exclusiveGateway default="85af5c40-ec42-44f5-81bd-5dd068397c0b" name="Decision?" id="326824ab-2799-49e6-8872-aebb656ee215"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1105" y="66" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;3af4c704-17b2-4434-810d-9af8cc29a315&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;77c79597-0942-4363-85b5-30769aa3abac&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;85af5c40-ec42-44f5-81bd-5dd068397c0b&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="326824ab-2799-49e6-8872-aebb656ee215" targetRef="9b4225c3-d93b-46d8-84b4-540d959b0610" name="To End Event1" id="77c79597-0942-4363-85b5-30769aa3abac"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;(tw.local.ODCRequest.requestType.value == "amend "|| tw.local.ODCRequest.requestType.value == "recreate") &amp;amp;&amp;amp; tw.local.ODCRequest.GeneratedDocumentInfo.regenerateRemLetter == false&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="326824ab-2799-49e6-8872-aebb656ee215" targetRef="8bc1cd19-36b6-47e8-8256-4e8e30cda2fe" name="To Print Remittance Letter" id="85af5c40-ec42-44f5-81bd-5dd068397c0b"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" /&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="8b0ddbba-564f-4b55-8826-d9aa92c61f99" targetRef="b5b64d78-cdaa-435a-8363-9a761092db3c" name="To End Event" id="e2d7fff9-82e6-426f-84bc-5ee9713f7fce"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="3af4c704-17b2-4434-810d-9af8cc29a315" name="Exclusive Gateway" id="5585ce17-20f3-4fc4-8f33-ca744aa7e44a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1154" y="68" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;5b813e0d-568a-468f-86a5-36b7fd5bcdfa&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;034dd239-4f64-4b84-83ee-537f60f00db8&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;3af4c704-17b2-4434-810d-9af8cc29a315&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;527dc6df-f888-4cb1-841f-3bfd1d2a1363&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;41f89811-100f-4299-8d85-be58356b40b1&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;048b1307-eea2-4c66-8a5f-3760004a8369&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="9e44cde9-a325-4fa2-a58c-5eb215044d87" targetRef="5585ce17-20f3-4fc4-8f33-ca744aa7e44a" name="To Exclusive Gateway" id="5b813e0d-568a-468f-86a5-36b7fd5bcdfa"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="Terminate" id="764873e6-cc4a-4397-8760-cf7a29db05c5"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1287" y="72" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;034dd239-4f64-4b84-83ee-537f60f00db8&lt;/ns15:incoming&gt;&lt;ns15:terminateEventDefinition id="f0901d7d-12b1-43d7-8044-cf84569f60c8" eventImplId="079c60e2-d093-4f03-84e8-2a4771b0aaab"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:terminateEventSettings&gt;&lt;ns4:terminateEntireProcess&gt;false&lt;/ns4:terminateEntireProcess&gt;&lt;ns4:deleteProcessInstance&gt;false&lt;/ns4:deleteProcessInstance&gt;&lt;ns4:deleteCaseFolder&gt;false&lt;/ns4:deleteCaseFolder&gt;&lt;/ns4:terminateEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:terminateEventDefinition&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="5585ce17-20f3-4fc4-8f33-ca744aa7e44a" targetRef="764873e6-cc4a-4397-8760-cf7a29db05c5" name="To Terminate" id="034dd239-4f64-4b84-83ee-537f60f00db8"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.terminateRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="5585ce17-20f3-4fc4-8f33-ca744aa7e44a" targetRef="326824ab-2799-49e6-8872-aebb656ee215" name="To Decision?" id="3af4c704-17b2-4434-810d-9af8cc29a315"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.actions&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="5585ce17-20f3-4fc4-8f33-ca744aa7e44a" targetRef="dad60095-00d8-4276-b6db-83336ef5acac" name="Return To trade FO" id="527dc6df-f888-4cb1-841f-3bfd1d2a1363"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topRight&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToTradeFo&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="5585ce17-20f3-4fc4-8f33-ca744aa7e44a" targetRef="5baef4dc-ae97-4dde-9e23-d644b4267e2d" name="To ODC Execution - Initiation" id="41f89811-100f-4299-8d85-be58356b40b1"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightBottom&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="5585ce17-20f3-4fc4-8f33-ca744aa7e44a" targetRef="96c4a0c7-8055-4af4-8adb-e0ff90566b97" name="To Trade Compliance Unified subprocess" id="048b1307-eea2-4c66-8a5f-3760004a8369"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.obtainApprovals&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="regeneratedRemittanceLetterTitleVIS" id="690e48ed-1f7c-452f-8502-72461df9d4af" /&gt;&lt;ns15:callActivity calledElement="1.d7acf968-6740-4e52-b037-2049466eeeb2" default="56471f53-a9ce-4217-8032-8ebf13d5f834" name="Send Escalation mail" id="042391f2-3dba-4a35-839b-ae74a7f49733"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns13:nodeVisualInfo x="570" y="53" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Send Escalation Mail&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;533c642c-1d7c-44bb-845d-fc943e47f04e&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;4330bbe8-450e-4a9b-8916-06fee1808fbb&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;4f5df130-36bb-4a04-81cb-c630b8c3dddf&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;13eb3134-bb49-491e-8582-7fa4e8b3ff6e&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;ae4b1759-0297-4a76-8d84-01a20709fcf0&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;f0ec9f29-a988-4fbc-8fc1-57cd9f749112&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;56471f53-a9ce-4217-8032-8ebf13d5f834&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.9771d7e8-ca59-430e-8b1a-194cf04c1182&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:endEvent name="End " id="90150625-9d40-41d0-815e-977855cf26f3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="737" y="76" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;56471f53-a9ce-4217-8032-8ebf13d5f834&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="042391f2-3dba-4a35-839b-ae74a7f49733" targetRef="90150625-9d40-41d0-815e-977855cf26f3" name="To End " id="56471f53-a9ce-4217-8032-8ebf13d5f834"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="loggedInUser" id="3cc52b44-591e-4e62-8ecd-7c36f13af5ad" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="taskID" id="c6c9cd0e-8d6f-4358-8c08-6e47407d2290" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="2756a3f6-ffa2-4919-80eb-27f080c2f8bf" /&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="4b4b129a-b268-4428-b193-f9f6d8e5c929" parallelMultiple="false" name="Boundary Event" id="a7d6cba3-a58d-4563-8023-24a437383585"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="215" y="67" width="24" height="24" /&gt;&lt;ns3:default&gt;533c642c-1d7c-44bb-845d-fc943e47f04e&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;533c642c-1d7c-44bb-845d-fc943e47f04e&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="3c99e982-128c-4b12-8e19-144013911c1e" eventImplId="205dee7b-99f1-4154-8584-54bcb3c7f4b5"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="a7d6cba3-a58d-4563-8023-24a437383585" targetRef="042391f2-3dba-4a35-839b-ae74a7f49733" name="To Send Escalation mail" id="533c642c-1d7c-44bb-845d-fc943e47f04e"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftBottom&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="db23215a-a1e0-49ab-b38e-3edcc5d7ba2c" parallelMultiple="false" name="Boundary Event1" id="17e7e782-08b2-46b6-8b8d-caa96ae3b253"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="361" y="67" width="24" height="24" /&gt;&lt;ns3:default&gt;4330bbe8-450e-4a9b-8916-06fee1808fbb&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;4330bbe8-450e-4a9b-8916-06fee1808fbb&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="289be63d-e71a-4782-8611-04f334668eca" eventImplId="dacb3eb1-a0f1-469c-82c7-4bd1447c0eec"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="17e7e782-08b2-46b6-8b8d-caa96ae3b253" targetRef="042391f2-3dba-4a35-839b-ae74a7f49733" name="To Send Escalation mail" id="4330bbe8-450e-4a9b-8916-06fee1808fbb"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="dad60095-00d8-4276-b6db-83336ef5acac" parallelMultiple="false" name="Boundary Event2" id="5cf8c7a9-7f20-42f7-8569-ff341d853fa2"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="547" y="91" width="24" height="24" /&gt;&lt;ns3:default&gt;4f5df130-36bb-4a04-81cb-c630b8c3dddf&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;4f5df130-36bb-4a04-81cb-c630b8c3dddf&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="dd94798f-d6b0-4043-8596-56fdb0ef9227" eventImplId="413f3908-4714-4b6d-806a-a1ac58209f9e"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="5cf8c7a9-7f20-42f7-8569-ff341d853fa2" targetRef="042391f2-3dba-4a35-839b-ae74a7f49733" name="To Send Escalation mail" id="4f5df130-36bb-4a04-81cb-c630b8c3dddf"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="5baef4dc-ae97-4dde-9e23-d644b4267e2d" parallelMultiple="false" name="Boundary Event4" id="a8cd289b-88bc-4eda-84be-c4498bf6740f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="888" y="67" width="24" height="24" /&gt;&lt;ns3:default&gt;13eb3134-bb49-491e-8582-7fa4e8b3ff6e&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;13eb3134-bb49-491e-8582-7fa4e8b3ff6e&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="71322bc9-35b4-476f-84f0-5b605aeb8337" eventImplId="b5070636-c4c0-4256-886b-67b34d474243"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="a8cd289b-88bc-4eda-84be-c4498bf6740f" targetRef="042391f2-3dba-4a35-839b-ae74a7f49733" name="To Send Escalation mail" id="13eb3134-bb49-491e-8582-7fa4e8b3ff6e"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="9e44cde9-a325-4fa2-a58c-5eb215044d87" parallelMultiple="false" name="Boundary Event5" id="5548e0ee-6b10-45b7-8a85-94e950182ce3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="969" y="37" width="24" height="24" /&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:default&gt;f0ec9f29-a988-4fbc-8fc1-57cd9f749112&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;f0ec9f29-a988-4fbc-8fc1-57cd9f749112&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="bd8c14dd-a92a-49bc-82cf-470d3eadd510" eventImplId="971aa396-cb0e-4cc5-87eb-a737e5a71dd2"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="8b0ddbba-564f-4b55-8826-d9aa92c61f99" parallelMultiple="false" name="Boundary Event6" id="8c8e382b-2494-478d-806d-3df09d620dd3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1455" y="146" width="24" height="24" /&gt;&lt;ns3:default&gt;ae4b1759-0297-4a76-8d84-01a20709fcf0&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskID&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;ae4b1759-0297-4a76-8d84-01a20709fcf0&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="c6e18889-dde2-469e-830a-e8d0d87b2ced" eventImplId="9d42266c-6445-4042-87a2-461d6ef1a7fa"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="8c8e382b-2494-478d-806d-3df09d620dd3" targetRef="042391f2-3dba-4a35-839b-ae74a7f49733" name="To Send Escalation mail" id="ae4b1759-0297-4a76-8d84-01a20709fcf0"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isFirstTime" id="66ed4df6-e7f9-464e-8162-9fc04c701f95"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:defaultValue useDefault="true"&gt;true&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataObject&gt;&lt;ns15:sequenceFlow sourceRef="5548e0ee-6b10-45b7-8a85-94e950182ce3" targetRef="042391f2-3dba-4a35-839b-ae74a7f49733" name="To Send Escalation mail" id="f0ec9f29-a988-4fbc-8fc1-57cd9f749112"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topRight&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="418bd03c-835e-4213-8eb0-78580737aad5" name="Compliance requested from FO?" id="eab0f5ea-2c91-4bb7-84db-78a5cb1144e6"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="742" y="45" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;6959e67f-fc93-4a5a-85eb-bbaf481c6675&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;418bd03c-835e-4213-8eb0-78580737aad5&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;b6c9ad87-602f-4a3d-8445-3d850bf99cff&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="eab0f5ea-2c91-4bb7-84db-78a5cb1144e6" targetRef="5baef4dc-ae97-4dde-9e23-d644b4267e2d" name="To ODC Execution - Initiation" id="418bd03c-835e-4213-8eb0-78580737aad5"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftTop&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="eab0f5ea-2c91-4bb7-84db-78a5cb1144e6" targetRef="dad60095-00d8-4276-b6db-83336ef5acac" name="To ACT03-Review ODC Request By Trade FO" id="b6c9ad87-602f-4a3d-8445-3d850bf99cff"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;leftCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.fromTradeFo	  ==	  true&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="fromTradeFo" id="1e9771d1-1635-4710-8340-47579a43385e"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:defaultValue useDefault="true"&gt;false&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataObject&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="compApprovalInit" id="fef3dff1-3e79-4f13-86fa-dd6c8622179d" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="lastAction" id="d96caf43-a401-4d33-8eec-82a770a271f4" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="fromExechecker" id="c86d712d-70dc-4170-8453-fb19532ffc2f"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:defaultValue useDefault="true"&gt;false&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataObject&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="role" id="da6b3f68-a1cc-43c0-89dc-aee40b531346" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentPath" id="e1250e3f-ac84-4f5f-8467-b6d3f62878fd" /&gt;&lt;ns15:callActivity calledElement="1.e6908115-4586-455f-bf73-b96b60019972" default="dd1ea0d1-61b8-4fc4-8e24-33e52806920f" name="Send Mail to Initiator/CAD team " id="8bc1cd19-36b6-47e8-8256-4e8e30cda2fe"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns13:nodeVisualInfo x="1300" y="88" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Send Rem Letter Mail&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;85af5c40-ec42-44f5-81bd-5dd068397c0b&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;dd1ea0d1-61b8-4fc4-8e24-33e52806920f&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c6a5147d-b288-4b5d-bb7d-66ec7cb1433c&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.ODCRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.1fa01c17-0c42-4789-8cdd-bc3e2afe1c54&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"&gt;tw.local.routingDetails&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="8bc1cd19-36b6-47e8-8256-4e8e30cda2fe" targetRef="8b0ddbba-564f-4b55-8826-d9aa92c61f99" name="To Print Remittance Letter" id="dd1ea0d1-61b8-4fc4-8e24-33e52806920f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:boundaryEvent cancelActivity="true" attachedToRef="8bc1cd19-36b6-47e8-8256-4e8e30cda2fe" parallelMultiple="false" name="Error1" id="9fb986f2-208c-4dbe-8f23-0e203af7536a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="1335" y="146" width="24" height="24" /&gt;&lt;ns3:default&gt;ba01c515-0007-49ce-863f-df588771ad22&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;ba01c515-0007-49ce-863f-df588771ad22&lt;/ns15:outgoing&gt;&lt;ns15:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="4a495bbf-0bc7-46f6-8de8-10388e9837b1" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:errorEventDefinition id="83078788-3542-4c02-8cd8-c2793c9fc339" eventImplId="9753810e-e99b-4884-86b9-3df06059001e"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:errorEventSettings&gt;&lt;ns4:catchAll&gt;true&lt;/ns4:catchAll&gt;&lt;/ns4:errorEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:errorEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="9fb986f2-208c-4dbe-8f23-0e203af7536a" targetRef="8b0ddbba-564f-4b55-8826-d9aa92c61f99" name="To Print Remittance Letter" id="ba01c515-0007-49ce-863f-df588771ad22"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;bottomLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.e0fee5e6-fd77-44ca-9ee6-dbcb6dbff7d7&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.227b3aa9-3a4a-47d4-8696-83ca726c27ab&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="Outward Documentary CollectionInterface" id="23f474e9-d2e3-405c-8861-aef33cc73c68" /&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["53e3c85e-3389-4ca6-8c0c-14b9c1522adb"],"isInterrupting":true,"extensionElements":{"default":["53e3c85e-3389-4ca6-8c0c-14b9c1522adb"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":38,"y":32,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["true"]}}]},"name":"Start","declaredType":"startEvent","id":"ad74e9b7-aca8-47bb-bdd4-b851c1e1eb66"},{"outgoing":["e06a7a55-c1ab-45b6-aa93-41fd9ec977f9"],"incoming":["c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5","0ea2a405-a03f-45bc-bc39-30305c53a60a","53e3c85e-3389-4ca6-8c0c-14b9c1522adb"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":227,"y":26,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Create \/ Amend ODC Request - \u0625\u0635\u062f\u0627\u0631 \/\u062a\u0639\u062f\u064a\u0644 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u064a \u062a\u0635\u062f\u064a\u0631&lt;#= tw.system.process.instanceId #&gt;","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.CACT01)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["false"]}}]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"LastUser","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b849887a-3e1e-4633-be89-6e2adce84383","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.branchCode"]}}]},{"targetRef":"2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Branch_Operation_MKR\""]}}]},{"targetRef":"2055.d5717fc2-69b2-4168-a2af-80632cedf962","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_CR_MKR\""]}}]}],"serviceRef":"1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"e06a7a55-c1ab-45b6-aa93-41fd9ec977f9","name":"ACT01-Create\/Amend ODC Request","dataInputAssociation":[{"targetRef":"2055.52b23fff-ad18-4c9c-8d4b-da25fa353d74","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.3db632ba-2dff-49ab-81f3-ecc3512ed61a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.e60856e2-88c3-449a-8b95-682aa6f088ab","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.branchSeq"]}}]},{"targetRef":"2055.c17f6b1c-420e-4316-8602-278ac2e5f98d","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.branchName"]}}]},{"targetRef":"2055.262e9fc1-8a4f-4bee-81b6-3ebbcd392f6d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]}}]},{"targetRef":"2055.df6d7638-3ff3-45a9-817e-e243fd7e7807","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.initiatorUser"]}}]},{"targetRef":"2055.5e731f35-27b7-4ff1-852c-7479e82b9115","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"4b4b129a-b268-4428-b193-f9f6d8e5c929","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}],"sourceRef":["2055.c6e44d18-bb22-407c-8c94-33e273a84088"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.regeneratedRemittanceLetterTitleVIS"]}}],"sourceRef":["2055.17b2fc8d-62c3-4cbc-8046-1daaee9f0658"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}],"sourceRef":["2055.4371b561-70e9-4ac1-898e-91948aadac07"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.role"]}}],"sourceRef":["2055.42d6ab5b-c60b-4768-89d1-cabaa3f9dd78"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.1ae8623e-a0d0-455e-8199-92f720530682"]}],"calledElement":"1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1"},{"targetRef":"4b4b129a-b268-4428-b193-f9f6d8e5c929","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT01-Create\/Amend ODC Request","declaredType":"sequenceFlow","id":"53e3c85e-3389-4ca6-8c0c-14b9c1522adb","sourceRef":"ad74e9b7-aca8-47bb-bdd4-b851c1e1eb66"},{"outgoing":["4eee37d5-da40-4f4d-8074-fae3c69b1c1a","93b2c7ce-e1fd-4ef8-932c-390cd287de99"],"incoming":["e06a7a55-c1ab-45b6-aa93-41fd9ec977f9"],"default":"4eee37d5-da40-4f4d-8074-fae3c69b1c1a","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":388,"y":117,"declaredType":"TNodeVisualInfo","height":32}]},"name":"DecisionACT01?","declaredType":"exclusiveGateway","id":"287e7737-bed8-434a-807f-3797db93e815"},{"targetRef":"287e7737-bed8-434a-807f-3797db93e815","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightBottom","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To DecisionACT01?","declaredType":"sequenceFlow","id":"e06a7a55-c1ab-45b6-aa93-41fd9ec977f9","sourceRef":"4b4b129a-b268-4428-b193-f9f6d8e5c929"},{"outgoing":["c71d715f-783a-41f8-8118-b13a12e84b50"],"incoming":["4eee37d5-da40-4f4d-8074-fae3c69b1c1a"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":373,"y":44,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review ODC Request  By Compliance Rep - \u0645\u0631\u0627\u062c\u0639\u0647 \u0637\u0644\u0628 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u064a \u062a\u0635\u062f\u064a\u0631 \u0645\u0646 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0627\u0644\u062a\u0632\u0627\u0645 ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.CACT02)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.b849887a-3e1e-4633-be89-6e2adce84383","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.branchCode"]}}]},{"targetRef":"2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Compliance_MKR\""]}}]},{"targetRef":"2055.d5717fc2-69b2-4168-a2af-80632cedf962","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_COMP_CHKR\""]}}]}],"serviceRef":"1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"c71d715f-783a-41f8-8118-b13a12e84b50","name":"ACT02-Review ODC Request By Compliace Rep.","dataInputAssociation":[{"targetRef":"2055.69a1c013-d5ea-4d9f-8e7a-253a4c53ee83","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.85bb1b3b-6aff-440d-8a91-036bad32bb3a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.regeneratedRemittanceLetterTitleVIS"]}}]},{"targetRef":"2055.06e83dcd-aab9-473a-8905-39ca48c61d56","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]},{"targetRef":"2055.2447e3bc-7548-4065-8bbf-89b5de603e41","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.role"]}}]},{"targetRef":"2055.ac5cb29b-06ab-412f-8129-5bd7fb6dc779","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"db23215a-a1e0-49ab-b38e-3edcc5d7ba2c","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}],"sourceRef":["2055.22fa5dfa-ab1c-4768-82c7-52b63dfc9e88"]}],"calledElement":"1.e31385fc-75d6-4686-b548-3d16aa42adf3"},{"targetRef":"db23215a-a1e0-49ab-b38e-3edcc5d7ba2c","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To ACT02-Review ODC Request By Compliace Rep.","declaredType":"sequenceFlow","id":"4eee37d5-da40-4f4d-8074-fae3c69b1c1a","sourceRef":"287e7737-bed8-434a-807f-3797db93e815"},{"outgoing":["c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5","54e8901a-d22f-4539-a2b4-b20a59db335e","b48c134b-0a40-4b3a-a3e2-49ef381c0ed0"],"incoming":["c71d715f-783a-41f8-8118-b13a12e84b50"],"default":"54e8901a-d22f-4539-a2b4-b20a59db335e","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":528,"y":117,"declaredType":"TNodeVisualInfo","height":32}]},"name":"DecisionACT02?","declaredType":"exclusiveGateway","id":"5226f795-1fb6-4834-8181-f98a858d441f"},{"targetRef":"5226f795-1fb6-4834-8181-f98a858d441f","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Review ODC Request By Compliace Rep.","declaredType":"sequenceFlow","id":"c71d715f-783a-41f8-8118-b13a12e84b50","sourceRef":"db23215a-a1e0-49ab-b38e-3edcc5d7ba2c"},{"targetRef":"4b4b129a-b268-4428-b193-f9f6d8e5c929","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToInitiator"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Back To Initiator","declaredType":"sequenceFlow","id":"c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5","sourceRef":"5226f795-1fb6-4834-8181-f98a858d441f"},{"outgoing":["7b55ff18-3427-41ed-85c4-33a0b76256d0"],"incoming":["54e8901a-d22f-4539-a2b4-b20a59db335e","52ddc0dd-c87e-48d3-812a-b3628fedad36","527dc6df-f888-4cb1-841f-3bfd1d2a1363","b6c9ad87-602f-4a3d-8445-3d850bf99cff"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":512,"y":33,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":" Review ODC Request By Trade FO","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.CACT03)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"7b55ff18-3427-41ed-85c4-33a0b76256d0","name":"ACT03-Review ODC Request By Trade FO","dataInputAssociation":[{"targetRef":"2055.c3e60b22-5baf-4042-9000-ca67a0ed85e2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.9a7f935d-4a17-4085-8d13-ccfa64144506","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.regeneratedRemittanceLetterTitleVIS"]}}]},{"targetRef":"2055.876fefd1-1853-43e6-855e-a6d443bcc55d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.approvalComment"]}}]},{"targetRef":"2055.2371dd53-f0ea-4b5e-8cf9-155e1386a13f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"dad60095-00d8-4276-b6db-83336ef5acac","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}],"sourceRef":["2055.ddf3e65a-a820-4c08-9ab8-7d8c06e7eb7d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.fromTradeFo"]}}],"sourceRef":["2055.b65aba1a-dd56-4a0c-8bd9-ecc16e9d6d70"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.compApprovalInit"]}}],"sourceRef":["2055.2c437293-db72-4278-8707-cc40bf84c167"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.approvalComment"]}}],"sourceRef":["2055.6bb45aa3-0cb2-4088-8331-380885045bd5"]}],"calledElement":"1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046"},{"targetRef":"dad60095-00d8-4276-b6db-83336ef5acac","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Approve Request","declaredType":"sequenceFlow","id":"54e8901a-d22f-4539-a2b4-b20a59db335e","sourceRef":"5226f795-1fb6-4834-8181-f98a858d441f"},{"incoming":["b48c134b-0a40-4b3a-a3e2-49ef381c0ed0"],"eventDefinition":[{"extensionElements":{"terminateEventSettings":[{"terminateEntireProcess":false,"deleteCaseFolder":false,"deleteProcessInstance":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTerminateEventSettings"}]},"declaredType":"terminateEventDefinition","id":"5374f911-4741-4b31-8ac4-5bb2b54b5585","otherAttributes":{"eventImplId":"05eea146-9054-4e70-84ba-ac623c1ea211"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":718,"y":122,"declaredType":"TNodeVisualInfo","height":24}]},"name":"TerminateAct02","declaredType":"endEvent","id":"3dce5eb9-7e21-4bf0-ba6d-479c1e68ff49"},{"targetRef":"3dce5eb9-7e21-4bf0-ba6d-479c1e68ff49","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"Cancel","declaredType":"sequenceFlow","id":"b48c134b-0a40-4b3a-a3e2-49ef381c0ed0","sourceRef":"5226f795-1fb6-4834-8181-f98a858d441f"},{"outgoing":["40508b5e-6bda-444f-862e-31945299b061"],"incoming":["93b2c7ce-e1fd-4ef8-932c-390cd287de99","184b3a6c-c429-41b1-b3b8-343ceb41569f","41f89811-100f-4299-8d85-be58356b40b1","418bd03c-835e-4213-8eb0-78580737aad5"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":900,"y":44,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":" ODC Execution Hub - Initiation","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.CACT04)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.fromTradeFo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["false"]}}]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.ODCRequest.FinancialDetailsFO.executionHub.value+\"_EXE_MKR\""]}}]}],"serviceRef":"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"40508b5e-6bda-444f-862e-31945299b061","name":"ODC Execution - Initiation","dataInputAssociation":[{"targetRef":"2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.regeneratedRemittanceLetterTitleVIS"]}}]},{"targetRef":"2055.21028eba-aa2f-4b93-806b-78b695c14ddf","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.fromExechecker"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"5baef4dc-ae97-4dde-9e23-d644b4267e2d","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}],"sourceRef":["2055.021ed4ee-8b5e-423d-8672-bdcaceed27d8"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.compApprovalInit"]}}],"sourceRef":["2055.73739682-2486-426c-8701-7fc1c235c6a3"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}],"sourceRef":["2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991"]}],"calledElement":"1.9321de6e-e6a5-435e-accb-0fbbd129c48a"},{"outgoing":["5b813e0d-568a-468f-86a5-36b7fd5bcdfa"],"incoming":["40508b5e-6bda-444f-862e-31945299b061"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":960,"y":49,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"ODC Execution Hub - Initiation Review","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.CACT05)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.ODCRequest.FinancialDetailsFO.executionHub.value+\"_EXE_CHKR\""]}}]}],"serviceRef":"1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"5b813e0d-568a-468f-86a5-36b7fd5bcdfa","name":"Act05 - ODC Execution - Initiation - review","dataInputAssociation":[{"targetRef":"2055.43609737-d968-42ad-9a14-2da88598005a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.e3114267-71c4-4261-8f02-f45b08a80f8c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.regeneratedRemittanceLetterTitleVIS"]}}]},{"targetRef":"2055.09800ab8-e6d8-4ee9-8954-dc0c77df48f0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]},{"targetRef":"2055.a532e1f6-27cc-44ab-8ba6-1ab2ea92ec2d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"9e44cde9-a325-4fa2-a58c-5eb215044d87","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}],"sourceRef":["2055.43126547-b78b-4e92-bab9-47081d86a36a"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.fromExechecker"]}}],"sourceRef":["2055.6b42b31e-df5e-4c9e-8914-5bda302e956c"]}],"calledElement":"1.6d4e9c1d-5624-494c-96fb-3bd94584f975"},{"targetRef":"9e44cde9-a325-4fa2-a58c-5eb215044d87","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomLeft","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Act05 - ODC Execution - Initiation - review","declaredType":"sequenceFlow","id":"40508b5e-6bda-444f-862e-31945299b061","sourceRef":"5baef4dc-ae97-4dde-9e23-d644b4267e2d"},{"flowElement":[{"parallelMultiple":false,"outgoing":["a74def41-38fa-4044-872e-87314182eafc"],"isInterrupting":true,"extensionElements":{"default":["a74def41-38fa-4044-872e-87314182eafc"],"nodeVisualInfo":[{"width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Subprocess Start","declaredType":"startEvent","id":"90571470-899e-44db-b0da-bb2842927ca3"},{"incoming":["324570fb-767a-41e6-82b1-4caf0784ec32"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Subprocess End","declaredType":"endEvent","id":"75ad6090-c994-431b-90d3-803dc6b15c5a"},{"outgoing":["324570fb-767a-41e6-82b1-4caf0784ec32"],"incoming":["a74def41-38fa-4044-872e-87314182eafc"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":224,"y":38,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":" Review Request by Trade Compliance  \u2013 \u0645\u0631\u0627\u062c\u0639\u0629 \u0637\u0644\u0628 \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631 \u0645\u0646 \u0625\u062f\u0627\u0631\u0629 \u0627\u0644\u0627\u0644\u062a\u0632\u0627\u0645 ","narrative":"","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"Number(tw.epv.ODCCreationSLA.CACT07)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"324570fb-767a-41e6-82b1-4caf0784ec32","name":"ACT07- Review ODC By Compliance","dataInputAssociation":[{"targetRef":"2055.6102a850-faf2-401e-9c7a-d8f3a6215da0","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.3502bdff-abe8-4815-b2bb-71d90d6236ce","assignment":[{"from":{"evaluatesToTypeRef":"12.b698dbfb-84da-40a5-9db3-676815055e65","declaredType":"TFormalExpression","content":["tw.local.ODCRequest.attachmentDetails.ecmProperties"]}}]},{"targetRef":"2055.45e2d2af-554c-402b-9a2c-225dfd4b1fe7","assignment":[{"from":{"evaluatesToTypeRef":"12.3212d5b3-f692-41b3-a893-343dc5c3df01","declaredType":"TFormalExpression","content":["tw.local.ODCRequest.attachmentDetails.folderID"]}}]},{"targetRef":"2055.80cf1e33-9711-481a-aaa4-e9508bea4af8","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.ODCRequest.initiator"]}}]},{"targetRef":"2055.6c11359f-601f-4a8a-adab-80428b282316","assignment":[{"from":{"evaluatesToTypeRef":"12.07383e61-26ca-4c95-9b61-00fbdbc9735e","declaredType":"TFormalExpression","content":["tw.local.ODCRequest.attachmentDetails.attachment"]}}]},{"targetRef":"2055.f67d7aab-e345-4d7d-80cb-5feba5fb114c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.compApprovalInit"]}}]},{"targetRef":"2055.1642e459-0600-4fe1-86f1-cead2df234da","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.approvalComment"]}}]},{"targetRef":"2055.dd027994-1915-4bca-8452-c0b7021d8794","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.fromTradeFo"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"bc337dcc-6688-472a-8487-e99cca9b9d71","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}],"sourceRef":["2055.0f59046c-cb7e-4393-bec7-d27739cecd93"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.e4654440-58a7-47b2-8f98-3eaa9cccad49","declaredType":"TFormalExpression","content":["tw.local.ODCRequest.complianceComments"]}}],"sourceRef":["2055.36778ac9-0d81-4996-991e-f909c6f6aa5a"]},{"assignment":[{"to":{"evaluatesToTypeRef":"12.07383e61-26ca-4c95-9b61-00fbdbc9735e","declaredType":"TFormalExpression","content":["tw.local.ODCRequest.attachmentDetails.attachment"]}}],"sourceRef":["2055.f89a842a-9d83-45bf-adeb-2837b1beb056"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.approvalComment"]}}],"sourceRef":["2055.4dfe48dc-794c-4131-85b0-9ac2fe08a1a5"]}],"calledElement":"1.7276be8c-00f4-4766-949c-bcd033b050c3"},{"targetRef":"bc337dcc-6688-472a-8487-e99cca9b9d71","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT07- Review ODC By Compliance","declaredType":"sequenceFlow","id":"a74def41-38fa-4044-872e-87314182eafc","sourceRef":"90571470-899e-44db-b0da-bb2842927ca3"},{"targetRef":"75ad6090-c994-431b-90d3-803dc6b15c5a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Subprocess End","declaredType":"sequenceFlow","id":"324570fb-767a-41e6-82b1-4caf0784ec32","sourceRef":"bc337dcc-6688-472a-8487-e99cca9b9d71"},{"outgoing":["700a2c9b-fbcb-49f8-8691-dd9afe661086"],"incoming":["853aa405-4ef8-4ca4-89a1-6264a3ae8c29"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":482,"y":42,"declaredType":"TNodeVisualInfo","height":70}],"deleteTaskOnCompletion":[false],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"700a2c9b-fbcb-49f8-8691-dd9afe661086","name":"Escalation email service","dataInputAssociation":[{"targetRef":"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.taskID"]}}]},{"targetRef":"2055.9771d7e8-ca59-430e-8b1a-194cf04c1182","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"726c29b6-ca92-4806-8dc0-3cea258f1845","calledElement":"1.d7acf968-6740-4e52-b037-2049466eeeb2"},{"parallelMultiple":false,"outgoing":["853aa405-4ef8-4ca4-89a1-6264a3ae8c29"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"364c3569-8bef-4100-8997-cc68e719b1ca","otherAttributes":{"eventImplId":"b6a28059-e9a1-48d4-8661-e1d2216f08fd"}}],"attachedToRef":"bc337dcc-6688-472a-8487-e99cca9b9d71","extensionElements":{"default":["853aa405-4ef8-4ca4-89a1-6264a3ae8c29"],"nodeVisualInfo":[{"width":24,"x":259,"y":96,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Error","declaredType":"boundaryEvent","id":"f3fc74cf-9590-49b2-8297-07492e9ce8f3"},{"targetRef":"726c29b6-ca92-4806-8dc0-3cea258f1845","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Escalation email service","declaredType":"sequenceFlow","id":"853aa405-4ef8-4ca4-89a1-6264a3ae8c29","sourceRef":"f3fc74cf-9590-49b2-8297-07492e9ce8f3"},{"incoming":["700a2c9b-fbcb-49f8-8691-dd9afe661086"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":662,"y":65,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event2","declaredType":"endEvent","id":"2285ed84-ab47-40b1-8b3e-5545fc5020f8"},{"targetRef":"2285ed84-ab47-40b1-8b3e-5545fc5020f8","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To End Event2","declaredType":"sequenceFlow","id":"700a2c9b-fbcb-49f8-8691-dd9afe661086","sourceRef":"726c29b6-ca92-4806-8dc0-3cea258f1845"}],"outgoing":["6959e67f-fc93-4a5a-85eb-bbaf481c6675","52ddc0dd-c87e-48d3-812a-b3628fedad36"],"incoming":["6f9b07d5-973a-4ce7-9e30-8d07577aa333","048b1307-eea2-4c66-8a5f-3760004a8369"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":621,"y":58,"declaredType":"TNodeVisualInfo","height":70}],"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.dbbaa047-8f02-4397-b1b5-41f11b0256b3","epvProcessLinkId":"c755e699-8102-4d4d-8601-2fa0747feec6","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"declaredType":"subProcess","startQuantity":1,"laneSet":[{"id":"4f8f2faa-bc76-45fe-86e2-fd76b446f7c5","lane":[{"flowNodeRef":["90571470-899e-44db-b0da-bb2842927ca3","75ad6090-c994-431b-90d3-803dc6b15c5a","bc337dcc-6688-472a-8487-e99cca9b9d71","f3fc74cf-9590-49b2-8297-07492e9ce8f3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Compliance","partitionElementRef":"24.6b09bd69-9498-4f11-beaf-41aba731a014","declaredType":"lane","id":"dee7eb59-f491-4346-a98b-08a5b3b848cb","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["726c29b6-ca92-4806-8dc0-3cea258f1845","2285ed84-ab47-40b1-8b3e-5545fc5020f8"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"system","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"1a0244d0-d147-40ea-84e4-d4ff72885a2e","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}}]}],"default":"6959e67f-fc93-4a5a-85eb-bbaf481c6675","name":"Trade Compliance Unified subprocess","triggeredByEvent":false,"isForCompensation":false,"completionQuantity":1,"id":"96c4a0c7-8055-4af4-8adb-e0ff90566b97"},{"outgoing":["6f9b07d5-973a-4ce7-9e30-8d07577aa333","0ea2a405-a03f-45bc-bc39-30305c53a60a","b4907ccb-6469-4e3b-bf69-f60ffb7d0b80","184b3a6c-c429-41b1-b3b8-343ceb41569f"],"incoming":["7b55ff18-3427-41ed-85c4-33a0b76256d0"],"default":"184b3a6c-c429-41b1-b3b8-343ceb41569f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":614,"y":119,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Decision03?","declaredType":"exclusiveGateway","id":"6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0"},{"targetRef":"6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomRight","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Decision03?","declaredType":"sequenceFlow","id":"7b55ff18-3427-41ed-85c4-33a0b76256d0","sourceRef":"dad60095-00d8-4276-b6db-83336ef5acac"},{"targetRef":"96c4a0c7-8055-4af4-8adb-e0ff90566b97","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.obtainApprovals"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Approve","declaredType":"sequenceFlow","id":"6f9b07d5-973a-4ce7-9e30-8d07577aa333","sourceRef":"6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0"},{"targetRef":"4b4b129a-b268-4428-b193-f9f6d8e5c929","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToInitiator"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Back to Initiator","declaredType":"sequenceFlow","id":"0ea2a405-a03f-45bc-bc39-30305c53a60a","sourceRef":"6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0"},{"incoming":["b4907ccb-6469-4e3b-bf69-f60ffb7d0b80"],"eventDefinition":[{"extensionElements":{"terminateEventSettings":[{"terminateEntireProcess":false,"deleteCaseFolder":false,"deleteProcessInstance":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTerminateEventSettings"}]},"declaredType":"terminateEventDefinition","id":"a67a95ad-7556-4f8c-a54e-e47a6a11e270","otherAttributes":{"eventImplId":"95908c5a-cf9f-4af9-8912-ace60122ffcf"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":756,"y":74,"declaredType":"TNodeVisualInfo","height":24}]},"name":"TerminateACt03","declaredType":"endEvent","id":"65b61876-d96c-474b-b1e2-2fd925caebee"},{"targetRef":"65b61876-d96c-474b-b1e2-2fd925caebee","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.terminateRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"Terminate","declaredType":"sequenceFlow","id":"b4907ccb-6469-4e3b-bf69-f60ffb7d0b80","sourceRef":"6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0"},{"targetRef":"eab0f5ea-2c91-4bb7-84db-78a5cb1144e6","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomRight","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Compliance requested from FO?","declaredType":"sequenceFlow","id":"6959e67f-fc93-4a5a-85eb-bbaf481c6675","sourceRef":"96c4a0c7-8055-4af4-8adb-e0ff90566b97"},{"targetRef":"dad60095-00d8-4276-b6db-83336ef5acac","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToMaker"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topLeft","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT03-Review ODC Request By Trade FO","declaredType":"sequenceFlow","id":"52ddc0dd-c87e-48d3-812a-b3628fedad36","sourceRef":"96c4a0c7-8055-4af4-8adb-e0ff90566b97"},{"targetRef":"5baef4dc-ae97-4dde-9e23-d644b4267e2d","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.submitRequestToHubDirectory"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To ODC Execution - Initiation","declaredType":"sequenceFlow","id":"93b2c7ce-e1fd-4ef8-932c-390cd287de99","sourceRef":"287e7737-bed8-434a-807f-3797db93e815"},{"targetRef":"5baef4dc-ae97-4dde-9e23-d644b4267e2d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To ODC Execution - Initiation","declaredType":"sequenceFlow","id":"184b3a6c-c429-41b1-b3b8-343ceb41569f","sourceRef":"6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0"},{"outgoing":["e2d7fff9-82e6-426f-84bc-5ee9713f7fce"],"incoming":["dd1ea0d1-61b8-4fc4-8e24-33e52806920f","ba01c515-0007-49ce-863f-df588771ad22"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":1420,"y":88,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Print Remittance Letter","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.CACT06)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"LastUser","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b849887a-3e1e-4633-be89-6e2adce84383","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.branchCode"]}}]},{"targetRef":"2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Branch_Operation_MKR\""]}}]},{"targetRef":"2055.d5717fc2-69b2-4168-a2af-80632cedf962","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_CR_MKR\""]}}]}],"serviceRef":"1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"e2d7fff9-82e6-426f-84bc-5ee9713f7fce","name":"Print Remittance Letter","dataInputAssociation":[{"targetRef":"2055.8cdb34c3-63b3-4765-83e8-d4f953fdc147","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"8b0ddbba-564f-4b55-8826-d9aa92c61f99","calledElement":"1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9"},{"incoming":["e2d7fff9-82e6-426f-84bc-5ee9713f7fce"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1549,"y":111,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"b5b64d78-cdaa-435a-8363-9a761092db3c"},{"incoming":["77c79597-0942-4363-85b5-30769aa3abac"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1388,"y":31,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event1","declaredType":"endEvent","id":"9b4225c3-d93b-46d8-84b4-540d959b0610"},{"outgoing":["77c79597-0942-4363-85b5-30769aa3abac","85af5c40-ec42-44f5-81bd-5dd068397c0b"],"incoming":["3af4c704-17b2-4434-810d-9af8cc29a315"],"default":"85af5c40-ec42-44f5-81bd-5dd068397c0b","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1105,"y":66,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Decision?","declaredType":"exclusiveGateway","id":"326824ab-2799-49e6-8872-aebb656ee215"},{"targetRef":"9b4225c3-d93b-46d8-84b4-540d959b0610","conditionExpression":{"declaredType":"TFormalExpression","content":["(tw.local.ODCRequest.requestType.value == \"amend \"|| tw.local.ODCRequest.requestType.value == \"recreate\") &amp;&amp; tw.local.ODCRequest.GeneratedDocumentInfo.regenerateRemLetter == false"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"To End Event1","declaredType":"sequenceFlow","id":"77c79597-0942-4363-85b5-30769aa3abac","sourceRef":"326824ab-2799-49e6-8872-aebb656ee215"},{"targetRef":"8bc1cd19-36b6-47e8-8256-4e8e30cda2fe","conditionExpression":{"declaredType":"TFormalExpression"},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To Print Remittance Letter","declaredType":"sequenceFlow","id":"85af5c40-ec42-44f5-81bd-5dd068397c0b","sourceRef":"326824ab-2799-49e6-8872-aebb656ee215"},{"targetRef":"b5b64d78-cdaa-435a-8363-9a761092db3c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End Event","declaredType":"sequenceFlow","id":"e2d7fff9-82e6-426f-84bc-5ee9713f7fce","sourceRef":"8b0ddbba-564f-4b55-8826-d9aa92c61f99"},{"outgoing":["034dd239-4f64-4b84-83ee-537f60f00db8","3af4c704-17b2-4434-810d-9af8cc29a315","527dc6df-f888-4cb1-841f-3bfd1d2a1363","41f89811-100f-4299-8d85-be58356b40b1","048b1307-eea2-4c66-8a5f-3760004a8369"],"incoming":["5b813e0d-568a-468f-86a5-36b7fd5bcdfa"],"default":"3af4c704-17b2-4434-810d-9af8cc29a315","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":1154,"y":68,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Exclusive Gateway","declaredType":"exclusiveGateway","id":"5585ce17-20f3-4fc4-8f33-ca744aa7e44a"},{"targetRef":"5585ce17-20f3-4fc4-8f33-ca744aa7e44a","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Exclusive Gateway","declaredType":"sequenceFlow","id":"5b813e0d-568a-468f-86a5-36b7fd5bcdfa","sourceRef":"9e44cde9-a325-4fa2-a58c-5eb215044d87"},{"incoming":["034dd239-4f64-4b84-83ee-537f60f00db8"],"eventDefinition":[{"extensionElements":{"terminateEventSettings":[{"terminateEntireProcess":false,"deleteCaseFolder":false,"deleteProcessInstance":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTerminateEventSettings"}]},"declaredType":"terminateEventDefinition","id":"f0901d7d-12b1-43d7-8044-cf84569f60c8","otherAttributes":{"eventImplId":"079c60e2-d093-4f03-84e8-2a4771b0aaab"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":1287,"y":72,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Terminate","declaredType":"endEvent","id":"764873e6-cc4a-4397-8760-cf7a29db05c5"},{"targetRef":"764873e6-cc4a-4397-8760-cf7a29db05c5","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.terminateRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To Terminate","declaredType":"sequenceFlow","id":"034dd239-4f64-4b84-83ee-537f60f00db8","sourceRef":"5585ce17-20f3-4fc4-8f33-ca744aa7e44a"},{"targetRef":"326824ab-2799-49e6-8872-aebb656ee215","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.actions"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To Decision?","declaredType":"sequenceFlow","id":"3af4c704-17b2-4434-810d-9af8cc29a315","sourceRef":"5585ce17-20f3-4fc4-8f33-ca744aa7e44a"},{"targetRef":"dad60095-00d8-4276-b6db-83336ef5acac","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToTradeFo"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Return To trade FO","declaredType":"sequenceFlow","id":"527dc6df-f888-4cb1-841f-3bfd1d2a1363","sourceRef":"5585ce17-20f3-4fc4-8f33-ca744aa7e44a"},{"targetRef":"5baef4dc-ae97-4dde-9e23-d644b4267e2d","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.returnToMaker"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To ODC Execution - Initiation","declaredType":"sequenceFlow","id":"41f89811-100f-4299-8d85-be58356b40b1","sourceRef":"5585ce17-20f3-4fc4-8f33-ca744aa7e44a"},{"targetRef":"96c4a0c7-8055-4af4-8adb-e0ff90566b97","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.ODCRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.obtainApprovals"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To Trade Compliance Unified subprocess","declaredType":"sequenceFlow","id":"048b1307-eea2-4c66-8a5f-3760004a8369","sourceRef":"5585ce17-20f3-4fc4-8f33-ca744aa7e44a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"regeneratedRemittanceLetterTitleVIS","isCollection":false,"declaredType":"dataObject","id":"690e48ed-1f7c-452f-8502-72461df9d4af"},{"outgoing":["56471f53-a9ce-4217-8032-8ebf13d5f834"],"incoming":["533c642c-1d7c-44bb-845d-fc943e47f04e","4330bbe8-450e-4a9b-8916-06fee1808fbb","4f5df130-36bb-4a04-81cb-c630b8c3dddf","13eb3134-bb49-491e-8582-7fa4e8b3ff6e","ae4b1759-0297-4a76-8d84-01a20709fcf0","f0ec9f29-a988-4fbc-8fc1-57cd9f749112"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"deleteTaskOnCompletion":[true],"nodeVisualInfo":[{"width":95,"x":570,"y":53,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Send Escalation Mail","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"56471f53-a9ce-4217-8032-8ebf13d5f834","name":"Send Escalation mail","dataInputAssociation":[{"targetRef":"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.taskID"]}}]},{"targetRef":"2055.9771d7e8-ca59-430e-8b1a-194cf04c1182","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"042391f2-3dba-4a35-839b-ae74a7f49733","calledElement":"1.d7acf968-6740-4e52-b037-2049466eeeb2"},{"incoming":["56471f53-a9ce-4217-8032-8ebf13d5f834"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":737,"y":76,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End ","declaredType":"endEvent","id":"90150625-9d40-41d0-815e-977855cf26f3"},{"targetRef":"90150625-9d40-41d0-815e-977855cf26f3","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End ","declaredType":"sequenceFlow","id":"56471f53-a9ce-4217-8032-8ebf13d5f834","sourceRef":"042391f2-3dba-4a35-839b-ae74a7f49733"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"loggedInUser","isCollection":false,"declaredType":"dataObject","id":"3cc52b44-591e-4e62-8ecd-7c36f13af5ad"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskID","isCollection":false,"declaredType":"dataObject","id":"c6c9cd0e-8d6f-4358-8c08-6e47407d2290"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"declaredType":"dataObject","id":"2756a3f6-ffa2-4919-80eb-27f080c2f8bf"},{"parallelMultiple":false,"outgoing":["533c642c-1d7c-44bb-845d-fc943e47f04e"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"3c99e982-128c-4b12-8e19-144013911c1e","otherAttributes":{"eventImplId":"205dee7b-99f1-4154-8584-54bcb3c7f4b5"}}],"attachedToRef":"4b4b129a-b268-4428-b193-f9f6d8e5c929","extensionElements":{"default":["533c642c-1d7c-44bb-845d-fc943e47f04e"],"nodeVisualInfo":[{"width":24,"x":215,"y":67,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event","declaredType":"boundaryEvent","id":"a7d6cba3-a58d-4563-8023-24a437383585"},{"targetRef":"042391f2-3dba-4a35-839b-ae74a7f49733","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftBottom","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"533c642c-1d7c-44bb-845d-fc943e47f04e","sourceRef":"a7d6cba3-a58d-4563-8023-24a437383585"},{"parallelMultiple":false,"outgoing":["4330bbe8-450e-4a9b-8916-06fee1808fbb"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"289be63d-e71a-4782-8611-04f334668eca","otherAttributes":{"eventImplId":"dacb3eb1-a0f1-469c-82c7-4bd1447c0eec"}}],"attachedToRef":"db23215a-a1e0-49ab-b38e-3edcc5d7ba2c","extensionElements":{"default":["4330bbe8-450e-4a9b-8916-06fee1808fbb"],"nodeVisualInfo":[{"width":24,"x":361,"y":67,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event1","declaredType":"boundaryEvent","id":"17e7e782-08b2-46b6-8b8d-caa96ae3b253"},{"targetRef":"042391f2-3dba-4a35-839b-ae74a7f49733","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"4330bbe8-450e-4a9b-8916-06fee1808fbb","sourceRef":"17e7e782-08b2-46b6-8b8d-caa96ae3b253"},{"parallelMultiple":false,"outgoing":["4f5df130-36bb-4a04-81cb-c630b8c3dddf"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"dd94798f-d6b0-4043-8596-56fdb0ef9227","otherAttributes":{"eventImplId":"413f3908-4714-4b6d-806a-a1ac58209f9e"}}],"attachedToRef":"dad60095-00d8-4276-b6db-83336ef5acac","extensionElements":{"default":["4f5df130-36bb-4a04-81cb-c630b8c3dddf"],"nodeVisualInfo":[{"width":24,"x":547,"y":91,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event2","declaredType":"boundaryEvent","id":"5cf8c7a9-7f20-42f7-8569-ff341d853fa2"},{"targetRef":"042391f2-3dba-4a35-839b-ae74a7f49733","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"4f5df130-36bb-4a04-81cb-c630b8c3dddf","sourceRef":"5cf8c7a9-7f20-42f7-8569-ff341d853fa2"},{"parallelMultiple":false,"outgoing":["13eb3134-bb49-491e-8582-7fa4e8b3ff6e"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"71322bc9-35b4-476f-84f0-5b605aeb8337","otherAttributes":{"eventImplId":"b5070636-c4c0-4256-886b-67b34d474243"}}],"attachedToRef":"5baef4dc-ae97-4dde-9e23-d644b4267e2d","extensionElements":{"default":["13eb3134-bb49-491e-8582-7fa4e8b3ff6e"],"nodeVisualInfo":[{"width":24,"x":888,"y":67,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event4","declaredType":"boundaryEvent","id":"a8cd289b-88bc-4eda-84be-c4498bf6740f"},{"targetRef":"042391f2-3dba-4a35-839b-ae74a7f49733","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"13eb3134-bb49-491e-8582-7fa4e8b3ff6e","sourceRef":"a8cd289b-88bc-4eda-84be-c4498bf6740f"},{"parallelMultiple":false,"outgoing":["f0ec9f29-a988-4fbc-8fc1-57cd9f749112"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"bd8c14dd-a92a-49bc-82cf-470d3eadd510","otherAttributes":{"eventImplId":"971aa396-cb0e-4cc5-87eb-a737e5a71dd2"}}],"attachedToRef":"9e44cde9-a325-4fa2-a58c-5eb215044d87","extensionElements":{"default":["f0ec9f29-a988-4fbc-8fc1-57cd9f749112"],"nodeVisualInfo":[{"width":24,"x":969,"y":37,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event5","declaredType":"boundaryEvent","id":"5548e0ee-6b10-45b7-8a85-94e950182ce3"},{"parallelMultiple":false,"outgoing":["ae4b1759-0297-4a76-8d84-01a20709fcf0"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"c6e18889-dde2-469e-830a-e8d0d87b2ced","otherAttributes":{"eventImplId":"9d42266c-6445-4042-87a2-461d6ef1a7fa"}}],"attachedToRef":"8b0ddbba-564f-4b55-8826-d9aa92c61f99","extensionElements":{"default":["ae4b1759-0297-4a76-8d84-01a20709fcf0"],"nodeVisualInfo":[{"width":24,"x":1455,"y":146,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskID"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event6","declaredType":"boundaryEvent","id":"8c8e382b-2494-478d-806d-3df09d620dd3"},{"targetRef":"042391f2-3dba-4a35-839b-ae74a7f49733","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"ae4b1759-0297-4a76-8d84-01a20709fcf0","sourceRef":"8c8e382b-2494-478d-806d-3df09d620dd3"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isFirstTime","isCollection":false,"declaredType":"dataObject","id":"66ed4df6-e7f9-464e-8162-9fc04c701f95"},{"targetRef":"042391f2-3dba-4a35-839b-ae74a7f49733","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topRight","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"f0ec9f29-a988-4fbc-8fc1-57cd9f749112","sourceRef":"5548e0ee-6b10-45b7-8a85-94e950182ce3"},{"outgoing":["418bd03c-835e-4213-8eb0-78580737aad5","b6c9ad87-602f-4a3d-8445-3d850bf99cff"],"incoming":["6959e67f-fc93-4a5a-85eb-bbaf481c6675"],"default":"418bd03c-835e-4213-8eb0-78580737aad5","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":742,"y":45,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Compliance requested from FO?","declaredType":"exclusiveGateway","id":"eab0f5ea-2c91-4bb7-84db-78a5cb1144e6"},{"targetRef":"5baef4dc-ae97-4dde-9e23-d644b4267e2d","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftTop","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ODC Execution - Initiation","declaredType":"sequenceFlow","id":"418bd03c-835e-4213-8eb0-78580737aad5","sourceRef":"eab0f5ea-2c91-4bb7-84db-78a5cb1144e6"},{"targetRef":"dad60095-00d8-4276-b6db-83336ef5acac","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.fromTradeFo\t  ==\t  true"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"leftCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"To ACT03-Review ODC Request By Trade FO","declaredType":"sequenceFlow","id":"b6c9ad87-602f-4a3d-8445-3d850bf99cff","sourceRef":"eab0f5ea-2c91-4bb7-84db-78a5cb1144e6"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"fromTradeFo","isCollection":false,"declaredType":"dataObject","id":"1e9771d1-1635-4710-8340-47579a43385e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"compApprovalInit","isCollection":false,"declaredType":"dataObject","id":"fef3dff1-3e79-4f13-86fa-dd6c8622179d"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"declaredType":"dataObject","id":"d96caf43-a401-4d33-8eec-82a770a271f4"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"false"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"fromExechecker","isCollection":false,"declaredType":"dataObject","id":"c86d712d-70dc-4170-8453-fb19532ffc2f"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"role","isCollection":false,"declaredType":"dataObject","id":"da6b3f68-a1cc-43c0-89dc-aee40b531346"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"declaredType":"dataObject","id":"e1250e3f-ac84-4f5f-8467-b6d3f62878fd"},{"outgoing":["dd1ea0d1-61b8-4fc4-8e24-33e52806920f"],"incoming":["85af5c40-ec42-44f5-81bd-5dd068397c0b"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"deleteTaskOnCompletion":[true],"nodeVisualInfo":[{"width":95,"x":1300,"y":88,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Send Rem Letter Mail","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"dd1ea0d1-61b8-4fc4-8e24-33e52806920f","name":"Send Mail to Initiator\/CAD team ","dataInputAssociation":[{"targetRef":"2055.c6a5147d-b288-4b5d-bb7d-66ec7cb1433c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.1fa01c17-0c42-4789-8cdd-bc3e2afe1c54","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","declaredType":"TFormalExpression","content":["tw.local.routingDetails"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"8bc1cd19-36b6-47e8-8256-4e8e30cda2fe","calledElement":"1.e6908115-4586-455f-bf73-b96b60019972"},{"targetRef":"8b0ddbba-564f-4b55-8826-d9aa92c61f99","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Print Remittance Letter","declaredType":"sequenceFlow","id":"dd1ea0d1-61b8-4fc4-8e24-33e52806920f","sourceRef":"8bc1cd19-36b6-47e8-8256-4e8e30cda2fe"},{"parallelMultiple":false,"outgoing":["ba01c515-0007-49ce-863f-df588771ad22"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"4a495bbf-0bc7-46f6-8de8-10388e9837b1"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"83078788-3542-4c02-8cd8-c2793c9fc339","otherAttributes":{"eventImplId":"9753810e-e99b-4884-86b9-3df06059001e"}}],"attachedToRef":"8bc1cd19-36b6-47e8-8256-4e8e30cda2fe","extensionElements":{"default":["ba01c515-0007-49ce-863f-df588771ad22"],"nodeVisualInfo":[{"width":24,"x":1335,"y":146,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error1","declaredType":"boundaryEvent","id":"9fb986f2-208c-4dbe-8f23-0e203af7536a","outputSet":{}},{"targetRef":"8b0ddbba-564f-4b55-8826-d9aa92c61f99","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Print Remittance Letter","declaredType":"sequenceFlow","id":"ba01c515-0007-49ce-863f-df588771ad22","sourceRef":"9fb986f2-208c-4dbe-8f23-0e203af7536a"}],"laneSet":[{"id":"0ddd9c24-1288-4323-89f1-22d5846ccfc5","lane":[{"flowNodeRef":["ad74e9b7-aca8-47bb-bdd4-b851c1e1eb66","4b4b129a-b268-4428-b193-f9f6d8e5c929","287e7737-bed8-434a-807f-3797db93e815","8b0ddbba-564f-4b55-8826-d9aa92c61f99","b5b64d78-cdaa-435a-8363-9a761092db3c","9b4225c3-d93b-46d8-84b4-540d959b0610","326824ab-2799-49e6-8872-aebb656ee215","a7d6cba3-a58d-4563-8023-24a437383585","8c8e382b-2494-478d-806d-3df09d620dd3","8bc1cd19-36b6-47e8-8256-4e8e30cda2fe","9fb986f2-208c-4dbe-8f23-0e203af7536a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Branch \/ Hub Maker","partitionElementRef":"24.caa7093a-16aa-42da-b2f5-0770fabe40ea","declaredType":"lane","id":"0108b8f0-a19a-42a7-8eb9-3d676f5357e5","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["db23215a-a1e0-49ab-b38e-3edcc5d7ba2c","5226f795-1fb6-4834-8181-f98a858d441f","3dce5eb9-7e21-4bf0-ba6d-479c1e68ff49","17e7e782-08b2-46b6-8b8d-caa96ae3b253"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Branch \/ Hub compliance Rep.","partitionElementRef":"24.b1b8b540-604c-41d9-8fd7-6253273000f8","declaredType":"lane","id":"7cf73b9e-bbb6-487c-aeac-246c93572b11","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["dad60095-00d8-4276-b6db-83336ef5acac","6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0","65b61876-d96c-474b-b1e2-2fd925caebee","5cf8c7a9-7f20-42f7-8569-ff341d853fa2"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":402,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Trade Front Office","partitionElementRef":"24.e92aee43-caab-4c22-88e1-3f7edf14b1ba","declaredType":"lane","id":"59dab8ae-dc5e-4e82-9393-ac6d8a009b79","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["96c4a0c7-8055-4af4-8adb-e0ff90566b97"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":603,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Compliance","partitionElementRef":"24.6b09bd69-9498-4f11-beaf-41aba731a014","declaredType":"lane","id":"092f9ca7-4802-4949-ae45-1296d468956b","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["5baef4dc-ae97-4dde-9e23-d644b4267e2d","a8cd289b-88bc-4eda-84be-c4498bf6740f","eab0f5ea-2c91-4bb7-84db-78a5cb1144e6"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":804,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Execution Hub Maker","partitionElementRef":"24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce","declaredType":"lane","id":"d3721311-e835-4954-b408-382d3e3abb75","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["9e44cde9-a325-4fa2-a58c-5eb215044d87","5585ce17-20f3-4fc4-8f33-ca744aa7e44a","764873e6-cc4a-4397-8760-cf7a29db05c5","5548e0ee-6b10-45b7-8a85-94e950182ce3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":1005,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Execution Hub Checker","partitionElementRef":"24.8e005024-3fe0-4848-8c3c-f1e9483900c6","declaredType":"lane","id":"90756fec-b829-4497-9edd-d26584678c04","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["042391f2-3dba-4a35-839b-ae74a7f49733","90150625-9d40-41d0-815e-977855cf26f3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":1206,"declaredType":"TNodeVisualInfo","height":200}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"121232be-1044-4536-9224-6b3eb0346f9a","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"resourceRole":[{"name":"participantRef","declaredType":"resourceRole"},{"name":"businessDataParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.e0fee5e6-fd77-44ca-9ee6-dbcb6dbff7d7","name":"perfMetricParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.227b3aa9-3a4a-47d4-8696-83ca726c27ab","name":"ownerTeamParticipantRef","declaredType":"resourceRole"}],"isClosed":false,"extensionElements":{"bpdExtension":[{"allowContentOperations":false,"enableTracking":true,"workSchedule":{"timezoneType":0,"timeScheduleType":0,"timezone":"Africa\/Cairo","timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"instanceName":"\" Request Type: \"+ tw.local.ODCRequest.requestType.name +\" , \"+\" CIF: \"+ tw.local.ODCRequest.cif +\" , \" +\"Request Number: \" +tw.system.process.instanceId","dueDateSettings":{"dueDate":{"unit":"Hours","value":"8","timeOfDay":"00:00"},"type":"TimeCalculation"},"autoTrackingName":"at16896764894071689755418046","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension","optimizeExecForLatency":false,"defaultInstanceUI":{"dataInputAssociation":[{"targetRef":"2055.ee3d4996-a568-4b68-8e0c-52ab22272c00","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.ODCRequest"]}}]},{"targetRef":"2055.fa98b43d-d8a9-468a-8bdd-d4ffc9b3ae54","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","declaredType":"TFormalExpression","content":["tw.local.routingDetails"]}}]},{"targetRef":"2055.4ea51b66-9b82-4428-8cd6-2e036716f837","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.regeneratedRemittanceLetterTitleVIS"]}}]},{"targetRef":"2055.af46a6ca-649c-4e5f-88f0-0ac184ba3164","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.loggedInUser"]}}]},{"targetRef":"2055.097535cf-d177-4dec-8f63-720ff6076b41","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.taskID"]}}]},{"targetRef":"2055.3dc2865b-a6c8-41bf-8ff8-92060bcb614f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]},{"targetRef":"2055.a66486cc-b4c3-4304-8a3e-672183d52d76","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]}}]},{"targetRef":"2055.28f04f83-9bff-431c-8908-eaf9ad987427","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.fromTradeFo"]}}]},{"targetRef":"2055.6fe78734-b040-40f1-86c9-d5b9be0b52f7","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.compApprovalInit"]}}]},{"targetRef":"2055.54a14133-823f-4dc0-8c51-5bcafa1cd70c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]},{"targetRef":"2055.6a35b7cb-f8b8-49a9-8647-5d6778a89a36","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.fromExechecker"]}}]},{"targetRef":"2055.2f5d1275-bf3e-4237-8b05-394793647357","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.role"]}}]},{"targetRef":"2055.d4b2b6f1-6f73-49bb-8543-eb2c908768f4","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"serviceRef":"1.a3badb88-0400-49ae-ac1d-0e0ace050eb7"},"dueDateEnabled":false,"atRiskCalcEnabled":false,"allowProjectedPathManagement":false,"autoTrackingEnabled":false,"sboSyncEnabled":true}],"caseExtension":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension","caseFolder":{"allowSubfoldersCreation":false,"allowLocalDoc":false,"id":"bcd34da4-3c2d-425d-9155-12b12732b272","allowExternalFolder":false,"allowExternalDoc":false}}],"isConvergedProcess":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"ODC Creation and Amendment \u0627\u0646\u0634\u0627\u0621 \u0648 \u062a\u062d\u062f\u064a\u062b \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631","declaredType":"process","id":"25.82af5d84-ef2a-4494-91b9-42f60fbfd8d9","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"longRunning"},"ioSpecification":{"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.dbbaa047-8f02-4397-b1b5-41f11b0256b3","epvProcessLinkId":"cda44512-ca02-4564-8db5-c334afd741f0","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"855cdf49-51b0-49e6-8ed5-e254ca0760ca","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.062854b5-6513-4da8-84ab-0126f90e550d","epvProcessLinkId":"f26601ea-3952-407f-82b8-58b3559b19fc","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{},{"id":"e73d1395-8b43-4a94-9bc4-4082717d5d15"}],"outputSet":[{},{"id":"e2a9c4d9-7902-498d-8878-675b500e124d"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":""}],"searchableField":[{"path":".parentRequestNo","alias":"ParentRequestNumber","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"aa2ba285-68fc-4db6-887a-98157eea19bc","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.requestDate","alias":"RequestDate","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"33d7b82e-c34d-40d1-8582-f4aaae708391","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.status","alias":"RequestStatus","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"bef40a0e-a592-44c7-852d-2ffc579d842e","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".initiator","alias":"InitiatorHubBranch","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"470c65d8-d6fe-4bdc-8f4e-3bbd995d3801","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".cif","alias":"CustomerCIF","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"a9775f48-3dc0-418e-8c2a-4811f69fdeae","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".customerName","alias":"CustomerName","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"0d27e84c-381d-4224-8650-1d8613ab3dbb","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.initiator","alias":"InitiatorUserName","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"8db758e8-53b3-4d02-8e8d-9f01835a111c","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"ODCRequest","isCollection":false,"id":"2007.494433ff-7b97-4905-897c-46b7b7ddef4a"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.odcRoutingDetails();\nautoObject.hubCode = \"\";\nautoObject.branchCode = \"\";\nautoObject.initiatorUser = \"\";\nautoObject.branchName = \"\";\nautoObject.hubName = \"\";\nautoObject.branchSeq = \"\";\nautoObject"}]},"itemSubjectRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","name":"routingDetails","isCollection":false,"id":"2007.21e93e70-eac8-465f-8355-23d0627dcd57"}]}},{"name":"Outward Documentary CollectionInterface","declaredType":"interface","id":"23f474e9-d2e3-405c-8861-aef33cc73c68"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"10a2a33e-c5ad-450c-847a-92088e7bf3fd"}</jsonData>
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:d694a63221635d5b:6baf87c4:18969a9a6e2:7e2d</guid>
        <versionId>8af1b4b7-9041-46d7-a78e-959b105f2457</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-184">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير</name>
            <documentation></documentation>
            <name>ODC Creation and Amendment انشاء و تحديث تحصيل مستندى تصدير</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>bawadmin</author>
            <isTrackingEnabled>true</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>false</isDueDateEnabled>
            <isAtRiskCalcEnabled>false</isAtRiskCalcEnabled>
            <creationDate>1689755418055</creationDate>
            <modificationDate>*************</modificationDate>
            <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.e0fee5e6-fd77-44ca-9ee6-dbcb6dbff7d7</perfMetricParticipantRef>
            <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.227b3aa9-3a4a-47d4-8696-83ca726c27ab</ownerTeamParticipantRef>
            <metricSettings itemType="2" />
            <instanceNameExpression>" Request Type: "+ tw.local.ODCRequest.requestType.name +" , "+" CIF: "+ tw.local.ODCRequest.cif +" , " +"Request Number: " +tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>2</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <dueDateCustom></dueDateCustom>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://NBEdevBAW:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <timeScheduleName>NBEWork</timeScheduleName>
            <holidayScheduleName>NBEHoliday</holidayScheduleName>
            <holidayScheduleType>0</holidayScheduleType>
            <timezone>Africa/Cairo</timezone>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="0ddd9c24-1288-4323-89f1-22d5846ccfc5" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e5">
                <attachedActivityId>/1.a3badb88-0400-49ae-ac1d-0e0ace050eb7</attachedActivityId>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e4">
                    <name>ODCRequest</name>
                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                    <input>true</input>
                    <value>tw.local.ODCRequest</value>
                    <parameterId>2055.ee3d4996-a568-4b68-8e0c-52ab22272c00</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e3">
                    <name>routingDetails</name>
                    <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
                    <input>true</input>
                    <value>tw.local.routingDetails</value>
                    <parameterId>2055.fa98b43d-d8a9-468a-8bdd-d4ffc9b3ae54</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e2">
                    <name>regeneratedRemittanceLetterTitleVIS</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.regeneratedRemittanceLetterTitleVIS</value>
                    <parameterId>2055.4ea51b66-9b82-4428-8cd6-2e036716f837</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e1">
                    <name>loggedInUser</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.loggedInUser</value>
                    <parameterId>2055.af46a6ca-649c-4e5f-88f0-0ac184ba3164</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e0">
                    <name>taskID</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.taskID</value>
                    <parameterId>2055.097535cf-d177-4dec-8f63-720ff6076b41</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8df">
                    <name>mailTo</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.mailTo</value>
                    <parameterId>2055.3dc2865b-a6c8-41bf-8ff8-92060bcb614f</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8de">
                    <name>isFirstTime</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <input>true</input>
                    <value>tw.local.isFirstTime</value>
                    <parameterId>2055.a66486cc-b4c3-4304-8a3e-672183d52d76</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8dd">
                    <name>fromTradeFo</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <input>true</input>
                    <value>tw.local.fromTradeFo</value>
                    <parameterId>2055.28f04f83-9bff-431c-8908-eaf9ad987427</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8dc">
                    <name>compApprovalInit</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.compApprovalInit</value>
                    <parameterId>2055.6fe78734-b040-40f1-86c9-d5b9be0b52f7</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8db">
                    <name>lastAction</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.lastAction</value>
                    <parameterId>2055.54a14133-823f-4dc0-8c51-5bcafa1cd70c</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8da">
                    <name>fromExechecker</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <input>true</input>
                    <value>tw.local.fromExechecker</value>
                    <parameterId>2055.6a35b7cb-f8b8-49a9-8647-5d6778a89a36</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d9">
                    <name>role</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.role</value>
                    <parameterId>2055.2f5d1275-bf3e-4237-8b05-394793647357</parameterId>
                </inputActivityParameterMapping>
                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d8">
                    <name>parentPath</name>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <input>true</input>
                    <value>tw.local.parentPath</value>
                    <parameterId>2055.d4b2b6f1-6f73-49bb-8543-eb2c908768f4</parameterId>
                </inputActivityParameterMapping>
            </defaultInstanceUI>
            <ownerTeamInstanceUI id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9e4" />
            <simulationScenario id="bpdid:d694a63221635d5b:6baf87c4:18969a9a6e2:7e6d">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1689755418100</startTime>
            </simulationScenario>
            <flow id="b6c9ad87-602f-4a3d-8445-3d850bf99cff" connectionType="SequenceFlow">
                <name>To ACT03-Review ODC Request By Trade FO</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8eb">
                        <expression>tw.local.fromTradeFo	  ==	  true</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="0ea2a405-a03f-45bc-bc39-30305c53a60a" connectionType="SequenceFlow">
                <name>Back to Initiator</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-920">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="85af5c40-ec42-44f5-81bd-5dd068397c0b" connectionType="SequenceFlow">
                <name>To Print Remittance Letter</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d6" />
                </connection>
            </flow>
            <flow id="b48c134b-0a40-4b3a-a3e2-49ef381c0ed0" connectionType="SequenceFlow">
                <name>Cancel</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-92c">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.cancelRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="41f89811-100f-4299-8d85-be58356b40b1" connectionType="SequenceFlow">
                <name>To ODC Execution - Initiation</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-900">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="77c79597-0942-4363-85b5-30769aa3abac" connectionType="SequenceFlow">
                <name>To End Event1</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-912">
                        <expression>(tw.local.ODCRequest.requestType.value == "amend "|| tw.local.ODCRequest.requestType.value == "recreate") &amp;&amp; tw.local.ODCRequest.GeneratedDocumentInfo.regenerateRemLetter == false</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="418bd03c-835e-4213-8eb0-78580737aad5" connectionType="SequenceFlow">
                <name>To ODC Execution - Initiation</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d5" />
                </connection>
            </flow>
            <flow id="5b813e0d-568a-468f-86a5-36b7fd5bcdfa" connectionType="SequenceFlow">
                <name>To Exclusive Gateway</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d4" />
                </connection>
            </flow>
            <flow id="13eb3134-bb49-491e-8582-7fa4e8b3ff6e" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d3" />
                </connection>
            </flow>
            <flow id="4330bbe8-450e-4a9b-8916-06fee1808fbb" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d2" />
                </connection>
            </flow>
            <flow id="b4907ccb-6469-4e3b-bf69-f60ffb7d0b80" connectionType="SequenceFlow">
                <name>Terminate</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-91e">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.terminateRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="6959e67f-fc93-4a5a-85eb-bbaf481c6675" connectionType="SequenceFlow">
                <name>To Compliance requested from FO?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d1" />
                </connection>
            </flow>
            <flow id="40508b5e-6bda-444f-862e-31945299b061" connectionType="SequenceFlow">
                <name>To Act05 - ODC Execution - Initiation - review</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d0" />
                </connection>
            </flow>
            <flow id="533c642c-1d7c-44bb-845d-fc943e47f04e" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8cf" />
                </connection>
            </flow>
            <flow id="e06a7a55-c1ab-45b6-aa93-41fd9ec977f9" connectionType="SequenceFlow">
                <name>To DecisionACT01?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ce" />
                </connection>
            </flow>
            <flow id="53e3c85e-3389-4ca6-8c0c-14b9c1522adb" connectionType="SequenceFlow">
                <name>To ACT01-Create/Amend ODC Request</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8cd" />
                </connection>
            </flow>
            <flow id="6f9b07d5-973a-4ce7-9e30-8d07577aa333" connectionType="SequenceFlow">
                <name>Approve</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-922">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.obtainApprovals</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="56471f53-a9ce-4217-8032-8ebf13d5f834" connectionType="SequenceFlow">
                <name>To End </name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8cc" />
                </connection>
            </flow>
            <flow id="dd1ea0d1-61b8-4fc4-8e24-33e52806920f" connectionType="SequenceFlow">
                <name>To Print Remittance Letter</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8cb" />
                </connection>
            </flow>
            <flow id="54e8901a-d22f-4539-a2b4-b20a59db335e" connectionType="SequenceFlow">
                <name>Approve Request</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ca" />
                </connection>
            </flow>
            <flow id="e2d7fff9-82e6-426f-84bc-5ee9713f7fce" connectionType="SequenceFlow">
                <name>To End Event</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c9" />
                </connection>
            </flow>
            <flow id="4eee37d5-da40-4f4d-8074-fae3c69b1c1a" connectionType="SequenceFlow">
                <name>To ACT02-Review ODC Request By Compliace Rep.</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c8" />
                </connection>
            </flow>
            <flow id="c71d715f-783a-41f8-8118-b13a12e84b50" connectionType="SequenceFlow">
                <name>To Review ODC Request By Compliace Rep.</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c7" />
                </connection>
            </flow>
            <flow id="048b1307-eea2-4c66-8a5f-3760004a8369" connectionType="SequenceFlow">
                <name>To Trade Compliance Unified subprocess</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8fe">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.obtainApprovals</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="ba01c515-0007-49ce-863f-df588771ad22" connectionType="SequenceFlow">
                <name>To Print Remittance Letter</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c6" />
                </connection>
            </flow>
            <flow id="034dd239-4f64-4b84-83ee-537f60f00db8" connectionType="SequenceFlow">
                <name>To Terminate</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-906">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.terminateRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="4f5df130-36bb-4a04-81cb-c630b8c3dddf" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c5" />
                </connection>
            </flow>
            <flow id="93b2c7ce-e1fd-4ef8-932c-390cd287de99" connectionType="SequenceFlow">
                <name>To ODC Execution - Initiation</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-917">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.submitRequestToHubDirectory</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5" connectionType="SequenceFlow">
                <name>Back To Initiator</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-92f">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToInitiator</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="184b3a6c-c429-41b1-b3b8-343ceb41569f" connectionType="SequenceFlow">
                <name>To ODC Execution - Initiation</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c4" />
                </connection>
            </flow>
            <flow id="3af4c704-17b2-4434-810d-9af8cc29a315" connectionType="SequenceFlow">
                <name>To Decision?</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-904">
                        <expression>tw.local.actions</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="ae4b1759-0297-4a76-8d84-01a20709fcf0" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c3" />
                </connection>
            </flow>
            <flow id="f0ec9f29-a988-4fbc-8fc1-57cd9f749112" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c2" />
                </connection>
            </flow>
            <flow id="7b55ff18-3427-41ed-85c4-33a0b76256d0" connectionType="SequenceFlow">
                <name>To Decision03?</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8c1" />
                </connection>
            </flow>
            <flow id="52ddc0dd-c87e-48d3-812a-b3628fedad36" connectionType="SequenceFlow">
                <name>To ACT03-Review ODC Request By Trade FO</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-919">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToMaker</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="527dc6df-f888-4cb1-841f-3bfd1d2a1363" connectionType="SequenceFlow">
                <name>Return To trade FO</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-902">
                        <expression>tw.local.ODCRequest.stepLog.action	  ==	  tw.epv.CreationActions.returnToTradeFo</expression>
                    </condition>
                </connection>
            </flow>
            <pool id="0ddd9c24-1288-4323-89f1-22d5846ccfc5">
                <name>Pool</name>
                <documentation></documentation>
                <restrictedName>at16896764894071689755418046</restrictedName>
                <dimension>
                    <size w="3000" h="1400" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="0108b8f0-a19a-42a7-8eb9-3d676f5357e5">
                    <name>Branch / Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.caa7093a-16aa-42da-b2f5-0770fabe40ea</attachedParticipant>
                    <flowObject id="4b4b129a-b268-4428-b193-f9f6d8e5c929" componentType="Activity">
                        <name>ACT01-Create/Amend ODC Request</name>
                        <position>
                            <location x="227" y="26" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.b1c1ff54-ed9e-4a3e-9cad-934dea1b2fd1</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>3</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.CACT01)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Create / Amend ODC Request - إصدار /تعديل تحصيل مستندي تصدير&lt;#= tw.system.process.instanceId #&gt;</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9ac">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.52b23fff-ad18-4c9c-8d4b-da25fa353d74</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9ab">
                                    <name>hubCode</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails.hubCode</value>
                                    <parameterId>2055.3db632ba-2dff-49ab-81f3-ecc3512ed61a</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9aa">
                                    <name>branchCode</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails.branchSeq</value>
                                    <parameterId>2055.e60856e2-88c3-449a-8b95-682aa6f088ab</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a9">
                                    <name>branchName</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails.branchName</value>
                                    <parameterId>2055.c17f6b1c-420e-4316-8602-278ac2e5f98d</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a8">
                                    <name>isFirstTime</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                                    <input>true</input>
                                    <value>tw.local.isFirstTime</value>
                                    <parameterId>2055.262e9fc1-8a4f-4bee-81b6-3ebbcd392f6d</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a7">
                                    <name>initiator</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails.initiatorUser</value>
                                    <parameterId>2055.df6d7638-3ff3-45a9-817e-e243fd7e7807</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a6">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.5e731f35-27b7-4ff1-852c-7479e82b9115</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a5">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.c6e44d18-bb22-407c-8c94-33e273a84088</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a4">
                                    <name>regeneratedRemittanceLetterTitleVIS</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.regeneratedRemittanceLetterTitleVIS</value>
                                    <parameterId>2055.17b2fc8d-62c3-4cbc-8046-1daaee9f0658</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a3">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.4371b561-70e9-4ac1-898e-91948aadac07</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a2">
                                    <name>role</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.role</value>
                                    <parameterId>2055.42d6ab5b-c60b-4768-89d1-cabaa3f9dd78</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a1">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.1ae8623e-a0d0-455e-8199-92f720530682</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9a0">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.caa7093a-16aa-42da-b2f5-0770fabe40ea</teamRef>
                                    <attachedActivityId>/1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-99f">
                                        <name>branchCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.branchCode</value>
                                        <parameterId>2055.b849887a-3e1e-4633-be89-6e2adce84383</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-99e">
                                        <name>hubCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.hubCode</value>
                                        <parameterId>2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-99d">
                                        <name>branchGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"Branch_Operation_MKR"</value>
                                        <parameterId>2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-99c">
                                        <name>hubGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_CR_MKR"</value>
                                        <parameterId>2055.d5717fc2-69b2-4168-a2af-80632cedf962</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-99b">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-93b">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="53e3c85e-3389-4ca6-8c0c-14b9c1522adb" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-930">
                            <positionId>bottomCenter</positionId>
                            <input>true</input>
                            <flow ref="c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-921">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="0ea2a405-a03f-45bc-bc39-30305c53a60a" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-93a">
                            <positionId>rightBottom</positionId>
                            <flow ref="e06a7a55-c1ab-45b6-aa93-41fd9ec977f9" />
                        </outputPort>
                        <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9ae">
                            <assignTime>2</assignTime>
                            <to>false</to>
                            <from>tw.local.isFirstTime</from>
                        </assignment>
                        <attachedEvent id="a7d6cba3-a58d-4563-8023-24a437383585" componentType="Event">
                            <name>Boundary Event</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>leftBottom</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="3c99e982-128c-4b12-8e19-144013911c1e">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="205dee7b-99f1-4154-8584-54bcb3c7f4b5">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8fb">
                                <positionId>bottomCenter</positionId>
                                <flow ref="533c642c-1d7c-44bb-845d-fc943e47f04e" />
                            </outputPort>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-956">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-955">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="8b0ddbba-564f-4b55-8826-d9aa92c61f99" componentType="Activity">
                        <name>Print Remittance Letter</name>
                        <position>
                            <location x="1420" y="88" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.bded59b5-cf5e-4dd0-91a9-dcaa169248e9</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>3</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.CACT06)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Print Remittance Letter</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-96e">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.8cdb34c3-63b3-4765-83e8-d4f953fdc147</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-96d">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.caa7093a-16aa-42da-b2f5-0770fabe40ea</teamRef>
                                    <attachedActivityId>/1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-96c">
                                        <name>branchCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.branchCode</value>
                                        <parameterId>2055.b849887a-3e1e-4633-be89-6e2adce84383</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-96b">
                                        <name>hubCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.hubCode</value>
                                        <parameterId>2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-96a">
                                        <name>branchGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"Branch_Operation_MKR"</value>
                                        <parameterId>2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-969">
                                        <name>hubGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_CR_MKR"</value>
                                        <parameterId>2055.d5717fc2-69b2-4168-a2af-80632cedf962</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-968">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e9">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="dd1ea0d1-61b8-4fc4-8e24-33e52806920f" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e7">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="ba01c515-0007-49ce-863f-df588771ad22" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-910">
                            <positionId>rightCenter</positionId>
                            <flow ref="e2d7fff9-82e6-426f-84bc-5ee9713f7fce" />
                        </outputPort>
                        <attachedEvent id="8c8e382b-2494-478d-806d-3df09d620dd3" componentType="Event">
                            <name>Boundary Event6</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="c6e18889-dde2-469e-830a-e8d0d87b2ced">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="9d42266c-6445-4042-87a2-461d6ef1a7fa">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f3">
                                <positionId>bottomCenter</positionId>
                                <flow ref="ae4b1759-0297-4a76-8d84-01a20709fcf0" />
                            </outputPort>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-942">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-941">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="8bc1cd19-36b6-47e8-8256-4e8e30cda2fe" componentType="Activity">
                        <name>Send Mail to Initiator/CAD team </name>
                        <documentation>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</documentation>
                        <position>
                            <location x="1300" y="88" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.e6908115-4586-455f-bf73-b96b60019972</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Send Rem Letter Mail</subject>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-961">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.c6a5147d-b288-4b5d-bb7d-66ec7cb1433c</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-960">
                                    <name>routingDetails</name>
                                    <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails</value>
                                    <parameterId>2055.1fa01c17-0c42-4789-8cdd-bc3e2afe1c54</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-95f">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.caa7093a-16aa-42da-b2f5-0770fabe40ea</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-95e">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-911">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="85af5c40-ec42-44f5-81bd-5dd068397c0b" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ea">
                            <positionId>rightCenter</positionId>
                            <flow ref="dd1ea0d1-61b8-4fc4-8e24-33e52806920f" />
                        </outputPort>
                        <attachedEvent id="9fb986f2-208c-4dbe-8f23-0e203af7536a" componentType="Event">
                            <name>Error1</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>true</doCloseTask>
                                <EventAction id="83078788-3542-4c02-8cd8-c2793c9fc339">
                                    <actionType>5</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="9753810e-e99b-4884-86b9-3df06059001e">
                                        <faultStyle>1</faultStyle>
                                        <isCatchAll>true</isCatchAll>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8e8">
                                <positionId>bottomCenter</positionId>
                                <flow ref="ba01c515-0007-49ce-863f-df588771ad22" />
                            </outputPort>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="ad74e9b7-aca8-47bb-bdd4-b851c1e1eb66" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="38" y="32" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-93c">
                            <positionId>rightCenter</positionId>
                            <flow ref="53e3c85e-3389-4ca6-8c0c-14b9c1522adb" />
                        </outputPort>
                        <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-95d">
                            <assignTime>2</assignTime>
                            <to>true</to>
                            <from>tw.local.isFirstTime</from>
                        </assignment>
                    </flowObject>
                    <flowObject id="b5b64d78-cdaa-435a-8363-9a761092db3c" componentType="Event">
                        <name>End Event</name>
                        <documentation></documentation>
                        <position>
                            <location x="1549" y="111" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-90f">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="e2d7fff9-82e6-426f-84bc-5ee9713f7fce" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="9b4225c3-d93b-46d8-84b4-540d959b0610" componentType="Event">
                        <name>End Event1</name>
                        <documentation></documentation>
                        <position>
                            <location x="1388" y="31" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-913">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="77c79597-0942-4363-85b5-30769aa3abac" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="287e7737-bed8-434a-807f-3797db93e815" componentType="Gateway">
                        <name>DecisionACT01?</name>
                        <documentation></documentation>
                        <position>
                            <location x="388" y="117" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-939">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="e06a7a55-c1ab-45b6-aa93-41fd9ec977f9" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-937">
                            <positionId>rightCenter</positionId>
                            <flow ref="93b2c7ce-e1fd-4ef8-932c-390cd287de99" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-938">
                            <positionId>bottomCenter</positionId>
                            <flow ref="4eee37d5-da40-4f4d-8074-fae3c69b1c1a" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="326824ab-2799-49e6-8872-aebb656ee215" componentType="Gateway">
                        <name>Decision?</name>
                        <documentation></documentation>
                        <position>
                            <location x="1105" y="66" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-905">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="3af4c704-17b2-4434-810d-9af8cc29a315" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-915">
                            <positionId>topCenter</positionId>
                            <flow ref="77c79597-0942-4363-85b5-30769aa3abac" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-914">
                            <positionId>bottomCenter</positionId>
                            <flow ref="85af5c40-ec42-44f5-81bd-5dd068397c0b" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="7cf73b9e-bbb6-487c-aeac-246c93572b11">
                    <name>Branch / Hub compliance Rep.</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.b1b8b540-604c-41d9-8fd7-6253273000f8</attachedParticipant>
                    <flowObject id="db23215a-a1e0-49ab-b38e-3edcc5d7ba2c" componentType="Activity">
                        <name>ACT02-Review ODC Request By Compliace Rep.</name>
                        <position>
                            <location x="373" y="44" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.e31385fc-75d6-4686-b548-3d16aa42adf3</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.CACT02)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review ODC Request  By Compliance Rep - مراجعه طلب تحصيل مستندي تصدير من إدارة الالتزام </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-999">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.69a1c013-d5ea-4d9f-8e7a-253a4c53ee83</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-998">
                                    <name>regeneratedRemittanceLetterTitleVIS</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.regeneratedRemittanceLetterTitleVIS</value>
                                    <parameterId>2055.85bb1b3b-6aff-440d-8a91-036bad32bb3a</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-997">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.06e83dcd-aab9-473a-8905-39ca48c61d56</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-996">
                                    <name>role</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.role</value>
                                    <parameterId>2055.2447e3bc-7548-4065-8bbf-89b5de603e41</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-995">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.ac5cb29b-06ab-412f-8129-5bd7fb6dc779</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-994">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.22fa5dfa-ab1c-4768-82c7-52b63dfc9e88</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-993">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.b1b8b540-604c-41d9-8fd7-6253273000f8</teamRef>
                                    <attachedActivityId>/1.f76d9bc1-7d6b-4a30-a957-eefc4c73f1e8</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-992">
                                        <name>hubCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.hubCode</value>
                                        <parameterId>2055.c1f6cb67-ef09-457b-87d0-cb34ae2ee857</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-991">
                                        <name>branchCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.branchCode</value>
                                        <parameterId>2055.b849887a-3e1e-4633-be89-6e2adce84383</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-990">
                                        <name>branchGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"Compliance_MKR"</value>
                                        <parameterId>2055.3990b876-3a6d-4d51-af2b-d6ebacf834a6</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-98f">
                                        <name>hubGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_COMP_CHKR"</value>
                                        <parameterId>2055.d5717fc2-69b2-4168-a2af-80632cedf962</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-98e">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-936">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="4eee37d5-da40-4f4d-8074-fae3c69b1c1a" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-935">
                            <positionId>bottomCenter</positionId>
                            <flow ref="c71d715f-783a-41f8-8118-b13a12e84b50" />
                        </outputPort>
                        <attachedEvent id="17e7e782-08b2-46b6-8b8d-caa96ae3b253" componentType="Event">
                            <name>Boundary Event1</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>leftCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="289be63d-e71a-4782-8611-04f334668eca">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="dacb3eb1-a0f1-469c-82c7-4bd1447c0eec">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f9">
                                <positionId>leftCenter</positionId>
                                <flow ref="4330bbe8-450e-4a9b-8916-06fee1808fbb" />
                            </outputPort>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-952">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-951">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="3dce5eb9-7e21-4bf0-ba6d-479c1e68ff49" componentType="Event">
                        <name>TerminateAct02</name>
                        <documentation></documentation>
                        <position>
                            <location x="718" y="122" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                            <EventAction id="5374f911-4741-4b31-8ac4-5bb2b54b5585">
                                <actionType>8</actionType>
                                <actionSubType>0</actionSubType>
                                <EventActionImplementation id="05eea146-9054-4e70-84ba-ac623c1ea211">
                                    <terminateEntireProcess>false</terminateEntireProcess>
                                    <deleteProcessInstance>false</deleteProcessInstance>
                                    <deleteCaseFolder>false</deleteCaseFolder>
                                </EventActionImplementation>
                            </EventAction>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-92d">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="b48c134b-0a40-4b3a-a3e2-49ef381c0ed0" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="5226f795-1fb6-4834-8181-f98a858d441f" componentType="Gateway">
                        <name>DecisionACT02?</name>
                        <documentation></documentation>
                        <position>
                            <location x="528" y="117" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-934">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="c71d715f-783a-41f8-8118-b13a12e84b50" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-933">
                            <positionId>bottomCenter</positionId>
                            <flow ref="c2b99505-5c0b-4d3c-ab61-d7c304f4b1a5" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-931">
                            <positionId>rightCenter</positionId>
                            <flow ref="b48c134b-0a40-4b3a-a3e2-49ef381c0ed0" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-932">
                            <positionId>bottomCenter</positionId>
                            <flow ref="54e8901a-d22f-4539-a2b4-b20a59db335e" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="59dab8ae-dc5e-4e82-9393-ac6d8a009b79">
                    <name>Trade Front Office</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.e92aee43-caab-4c22-88e1-3f7edf14b1ba</attachedParticipant>
                    <flowObject id="dad60095-00d8-4276-b6db-83336ef5acac" componentType="Activity">
                        <name>ACT03-Review ODC Request By Trade FO</name>
                        <position>
                            <location x="512" y="33" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.bf8bbf75-6f66-437d-a0fd-8d9ce53f1046</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.CACT03)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject> Review ODC Request By Trade FO</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-98c">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.c3e60b22-5baf-4042-9000-ca67a0ed85e2</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-98b">
                                    <name>regeneratedRemittanceLetterTitleVIS</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.regeneratedRemittanceLetterTitleVIS</value>
                                    <parameterId>2055.9a7f935d-4a17-4085-8d13-ccfa64144506</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-98a">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.2371dd53-f0ea-4b5e-8cf9-155e1386a13f</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-989">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.ddf3e65a-a820-4c08-9ab8-7d8c06e7eb7d</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-988">
                                    <name>isTradeFo</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                                    <input>true</input>
                                    <value>tw.local.fromTradeFo</value>
                                    <parameterId>2055.b65aba1a-dd56-4a0c-8bd9-ecc16e9d6d70</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-987">
                                    <name>complianceApprovalInit</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.compApprovalInit</value>
                                    <parameterId>2055.2c437293-db72-4278-8707-cc40bf84c167</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-986">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.e92aee43-caab-4c22-88e1-3f7edf14b1ba</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-985">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-92e">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="54e8901a-d22f-4539-a2b4-b20a59db335e" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-91a">
                            <positionId>bottomLeft</positionId>
                            <input>true</input>
                            <flow ref="52ddc0dd-c87e-48d3-812a-b3628fedad36" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-903">
                            <positionId>topRight</positionId>
                            <input>true</input>
                            <flow ref="527dc6df-f888-4cb1-841f-3bfd1d2a1363" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ec">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="b6c9ad87-602f-4a3d-8445-3d850bf99cff" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-929">
                            <positionId>bottomRight</positionId>
                            <flow ref="7b55ff18-3427-41ed-85c4-33a0b76256d0" />
                        </outputPort>
                        <attachedEvent id="5cf8c7a9-7f20-42f7-8569-ff341d853fa2" componentType="Event">
                            <name>Boundary Event2</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="dd94798f-d6b0-4043-8596-56fdb0ef9227">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="413f3908-4714-4b6d-806a-a1ac58209f9e">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f7">
                                <positionId>bottomCenter</positionId>
                                <flow ref="4f5df130-36bb-4a04-81cb-c630b8c3dddf" />
                            </outputPort>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-94e">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-94d">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="65b61876-d96c-474b-b1e2-2fd925caebee" componentType="Event">
                        <name>TerminateACt03</name>
                        <documentation></documentation>
                        <position>
                            <location x="756" y="74" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                            <EventAction id="a67a95ad-7556-4f8c-a54e-e47a6a11e270">
                                <actionType>8</actionType>
                                <actionSubType>0</actionSubType>
                                <EventActionImplementation id="95908c5a-cf9f-4af9-8912-ace60122ffcf">
                                    <terminateEntireProcess>false</terminateEntireProcess>
                                    <deleteProcessInstance>false</deleteProcessInstance>
                                    <deleteCaseFolder>false</deleteCaseFolder>
                                </EventActionImplementation>
                            </EventAction>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-91f">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="b4907ccb-6469-4e3b-bf69-f60ffb7d0b80" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="6636bd3f-83bc-4c06-a6fa-ddd5ea4b7ad0" componentType="Gateway">
                        <name>Decision03?</name>
                        <documentation></documentation>
                        <position>
                            <location x="614" y="119" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-928">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="7b55ff18-3427-41ed-85c4-33a0b76256d0" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-927">
                            <positionId>rightCenter</positionId>
                            <flow ref="6f9b07d5-973a-4ce7-9e30-8d07577aa333" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-926">
                            <positionId>topCenter</positionId>
                            <flow ref="0ea2a405-a03f-45bc-bc39-30305c53a60a" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-925">
                            <positionId>topCenter</positionId>
                            <flow ref="b4907ccb-6469-4e3b-bf69-f60ffb7d0b80" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-924">
                            <positionId>rightCenter</positionId>
                            <flow ref="184b3a6c-c429-41b1-b3b8-343ceb41569f" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="092f9ca7-4802-4949-ae45-1296d468956b">
                    <name>Compliance</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b09bd69-9498-4f11-beaf-41aba731a014</attachedParticipant>
                    <flowObject id="96c4a0c7-8055-4af4-8adb-e0ff90566b97" componentType="Activity">
                        <name>Trade Compliance Unified subprocess</name>
                        <documentation></documentation>
                        <position>
                            <location x="621" y="58" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>11</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>6</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <embeddedProcessId>25.96c4a0c7-8055-4af4-8adb-e0ff90566b97</embeddedProcessId>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-923">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="6f9b07d5-973a-4ce7-9e30-8d07577aa333" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ff">
                            <positionId>bottomCenter</positionId>
                            <input>true</input>
                            <flow ref="048b1307-eea2-4c66-8a5f-3760004a8369" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-91c">
                            <positionId>topLeft</positionId>
                            <flow ref="52ddc0dd-c87e-48d3-812a-b3628fedad36" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-91d">
                            <positionId>bottomRight</positionId>
                            <flow ref="6959e67f-fc93-4a5a-85eb-bbaf481c6675" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="d3721311-e835-4954-b408-382d3e3abb75">
                    <name>Execution Hub Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce</attachedParticipant>
                    <flowObject id="5baef4dc-ae97-4dde-9e23-d644b4267e2d" componentType="Activity">
                        <name>ODC Execution - Initiation</name>
                        <position>
                            <location x="900" y="44" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.9321de6e-e6a5-435e-accb-0fbbd129c48a</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.CACT04)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject> ODC Execution Hub - Initiation</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-982">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.b51ed76d-4a75-4213-b0d6-0c81e2ce9f3d</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-981">
                                    <name>regeneratedRemittanceLetterTitleVIS</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.regeneratedRemittanceLetterTitleVIS</value>
                                    <parameterId>2055.a4d65dc8-0469-4102-8e18-4d9d5d13086e</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-980">
                                    <name>fromExeChecker</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                                    <input>true</input>
                                    <value>tw.local.fromExechecker</value>
                                    <parameterId>2055.21028eba-aa2f-4b93-806b-78b695c14ddf</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-97f">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.021ed4ee-8b5e-423d-8672-bdcaceed27d8</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-97e">
                                    <name>compApprovalInit</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.compApprovalInit</value>
                                    <parameterId>2055.73739682-2486-426c-8701-7fc1c235c6a3</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-97d">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.2ae03a9c-94c2-4725-81ba-b05fcddf0991</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-97c">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce</teamRef>
                                    <attachedActivityId>/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-97b">
                                        <name>groupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <useDefault>true</useDefault>
                                        <value>"BPM_IDC_HUB_"+tw.local.ODCRequest.FinancialDetailsFO.executionHub.value+"_EXE_MKR"</value>
                                        <parameterId>2055.b95f0910-f183-4dab-9065-28297a14ceef</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-97a">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-918">
                            <positionId>topRight</positionId>
                            <input>true</input>
                            <flow ref="93b2c7ce-e1fd-4ef8-932c-390cd287de99" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-916">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="184b3a6c-c429-41b1-b3b8-343ceb41569f" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-901">
                            <positionId>rightBottom</positionId>
                            <input>true</input>
                            <flow ref="41f89811-100f-4299-8d85-be58356b40b1" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ed">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="418bd03c-835e-4213-8eb0-78580737aad5" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-92b">
                            <positionId>bottomLeft</positionId>
                            <flow ref="40508b5e-6bda-444f-862e-31945299b061" />
                        </outputPort>
                        <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-984">
                            <assignTime>2</assignTime>
                            <to>false</to>
                            <from>tw.local.fromTradeFo</from>
                        </assignment>
                        <attachedEvent id="a8cd289b-88bc-4eda-84be-c4498bf6740f" componentType="Event">
                            <name>Boundary Event4</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>leftCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="71322bc9-35b4-476f-84f0-5b605aeb8337">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="b5070636-c4c0-4256-886b-67b34d474243">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f5">
                                <positionId>leftCenter</positionId>
                                <flow ref="13eb3134-bb49-491e-8582-7fa4e8b3ff6e" />
                            </outputPort>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-94a">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-949">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="eab0f5ea-2c91-4bb7-84db-78a5cb1144e6" componentType="Gateway">
                        <name>Compliance requested from FO?</name>
                        <documentation></documentation>
                        <position>
                            <location x="742" y="45" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-91b">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="6959e67f-fc93-4a5a-85eb-bbaf481c6675" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ee">
                            <positionId>leftCenter</positionId>
                            <flow ref="b6c9ad87-602f-4a3d-8445-3d850bf99cff" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8ef">
                            <positionId>rightCenter</positionId>
                            <flow ref="418bd03c-835e-4213-8eb0-78580737aad5" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="90756fec-b829-4497-9edd-d26584678c04">
                    <name>Execution Hub Checker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</attachedParticipant>
                    <flowObject id="9e44cde9-a325-4fa2-a58c-5eb215044d87" componentType="Activity">
                        <name>Act05 - ODC Execution - Initiation - review</name>
                        <position>
                            <location x="960" y="49" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.6d4e9c1d-5624-494c-96fb-3bd94584f975</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.CACT05)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>ODC Execution Hub - Initiation Review</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-978">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.43609737-d968-42ad-9a14-2da88598005a</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-977">
                                    <name>regeneratedRemittanceLetterTitleVIS</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.regeneratedRemittanceLetterTitleVIS</value>
                                    <parameterId>2055.e3114267-71c4-4261-8f02-f45b08a80f8c</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-976">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.09800ab8-e6d8-4ee9-8954-dc0c77df48f0</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-975">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.a532e1f6-27cc-44ab-8ba6-1ab2ea92ec2d</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-974">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.43126547-b78b-4e92-bab9-47081d86a36a</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-973">
                                    <name>fromExechecker</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                                    <input>true</input>
                                    <value>tw.local.fromExechecker</value>
                                    <parameterId>2055.6b42b31e-df5e-4c9e-8914-5bda302e956c</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-972">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</teamRef>
                                    <attachedActivityId>/1.b7ee23fa-fe45-4dd7-8ced-56b1d6cafab9</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-971">
                                        <name>groupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <useDefault>true</useDefault>
                                        <value>"BPM_IDC_HUB_"+tw.local.ODCRequest.FinancialDetailsFO.executionHub.value+"_EXE_CHKR"</value>
                                        <parameterId>2055.b95f0910-f183-4dab-9065-28297a14ceef</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-970">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-92a">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="40508b5e-6bda-444f-862e-31945299b061" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-90e">
                            <positionId>rightCenter</positionId>
                            <flow ref="5b813e0d-568a-468f-86a5-36b7fd5bcdfa" />
                        </outputPort>
                        <attachedEvent id="5548e0ee-6b10-45b7-8a85-94e950182ce3" componentType="Event">
                            <name>Boundary Event5</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>topLeft</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="bd8c14dd-a92a-49bc-82cf-470d3eadd510">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="971aa396-cb0e-4cc5-87eb-a737e5a71dd2">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f1">
                                <positionId>topCenter</positionId>
                                <flow ref="f0ec9f29-a988-4fbc-8fc1-57cd9f749112" />
                            </outputPort>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-946">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskID</from>
                            </assignment>
                            <assignment id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-945">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="764873e6-cc4a-4397-8760-cf7a29db05c5" componentType="Event">
                        <name>Terminate</name>
                        <documentation></documentation>
                        <position>
                            <location x="1287" y="72" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                            <EventAction id="f0901d7d-12b1-43d7-8044-cf84569f60c8">
                                <actionType>8</actionType>
                                <actionSubType>0</actionSubType>
                                <EventActionImplementation id="079c60e2-d093-4f03-84e8-2a4771b0aaab">
                                    <terminateEntireProcess>false</terminateEntireProcess>
                                    <deleteProcessInstance>false</deleteProcessInstance>
                                    <deleteCaseFolder>false</deleteCaseFolder>
                                </EventActionImplementation>
                            </EventAction>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-907">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="034dd239-4f64-4b84-83ee-537f60f00db8" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="5585ce17-20f3-4fc4-8f33-ca744aa7e44a" componentType="Gateway">
                        <name>Exclusive Gateway</name>
                        <documentation></documentation>
                        <position>
                            <location x="1154" y="68" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-90d">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="5b813e0d-568a-468f-86a5-36b7fd5bcdfa" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-90c">
                            <positionId>rightCenter</positionId>
                            <flow ref="034dd239-4f64-4b84-83ee-537f60f00db8" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-90a">
                            <positionId>topCenter</positionId>
                            <flow ref="527dc6df-f888-4cb1-841f-3bfd1d2a1363" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-909">
                            <positionId>topCenter</positionId>
                            <flow ref="41f89811-100f-4299-8d85-be58356b40b1" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-908">
                            <positionId>bottomCenter</positionId>
                            <flow ref="048b1307-eea2-4c66-8a5f-3760004a8369" />
                        </outputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-90b">
                            <positionId>rightCenter</positionId>
                            <flow ref="3af4c704-17b2-4434-810d-9af8cc29a315" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="121232be-1044-4536-9224-6b3eb0346f9a">
                    <name>System</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>true</systemLane>
                    <attachedParticipant>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</attachedParticipant>
                    <flowObject id="042391f2-3dba-4a35-839b-ae74a7f49733" componentType="Activity">
                        <name>Send Escalation mail</name>
                        <documentation>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</documentation>
                        <position>
                            <location x="570" y="53" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.d7acf968-6740-4e52-b037-2049466eeeb2</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Send Escalation Mail</subject>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-966">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.ODCRequest</value>
                                    <parameterId>2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-965">
                                    <name>taskId</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <useDefault>true</useDefault>
                                    <value>tw.local.taskID</value>
                                    <parameterId>2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-964">
                                    <serviceType>1</serviceType>
                                    <teamRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-963">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8fa">
                            <positionId>leftBottom</positionId>
                            <input>true</input>
                            <flow ref="533c642c-1d7c-44bb-845d-fc943e47f04e" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f8">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="4330bbe8-450e-4a9b-8916-06fee1808fbb" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f6">
                            <positionId>leftTop</positionId>
                            <input>true</input>
                            <flow ref="4f5df130-36bb-4a04-81cb-c630b8c3dddf" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f4">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="13eb3134-bb49-491e-8582-7fa4e8b3ff6e" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f2">
                            <positionId>rightTop</positionId>
                            <input>true</input>
                            <flow ref="ae4b1759-0297-4a76-8d84-01a20709fcf0" />
                        </inputPort>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8f0">
                            <positionId>topRight</positionId>
                            <input>true</input>
                            <flow ref="f0ec9f29-a988-4fbc-8fc1-57cd9f749112" />
                        </inputPort>
                        <outputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8fd">
                            <positionId>rightCenter</positionId>
                            <flow ref="56471f53-a9ce-4217-8032-8ebf13d5f834" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="90150625-9d40-41d0-815e-977855cf26f3" componentType="Event">
                        <name>End </name>
                        <documentation></documentation>
                        <position>
                            <location x="737" y="76" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8fc">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="56471f53-a9ce-4217-8032-8ebf13d5f834" />
                        </inputPort>
                    </flowObject>
                </lane>
                <inputParameter id="494433ff-7b97-4905-897c-46b7b7ddef4a">
                    <bpdParameterId>2007.494433ff-7b97-4905-897c-46b7b7ddef4a</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <inputParameter id="21e93e70-eac8-465f-8355-23d0627dcd57">
                    <bpdParameterId>2007.21e93e70-eac8-465f-8355-23d0627dcd57</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <privateVariable id="690e48ed-1f7c-452f-8502-72461df9d4af">
                    <name>regeneratedRemittanceLetterTitleVIS</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="3cc52b44-591e-4e62-8ecd-7c36f13af5ad">
                    <name>loggedInUser</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="c6c9cd0e-8d6f-4358-8c08-6e47407d2290">
                    <name>taskID</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="2756a3f6-ffa2-4919-80eb-27f080c2f8bf">
                    <name>mailTo</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="66ed4df6-e7f9-464e-8162-9fc04c701f95">
                    <name>isFirstTime</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>true</hasDefault>
                    <defaultValue>true</defaultValue>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="1e9771d1-1635-4710-8340-47579a43385e">
                    <name>fromTradeFo</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>true</hasDefault>
                    <defaultValue>false</defaultValue>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="fef3dff1-3e79-4f13-86fa-dd6c8622179d">
                    <name>compApprovalInit</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="d96caf43-a401-4d33-8eec-82a770a271f4">
                    <name>lastAction</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="c86d712d-70dc-4170-8453-fb19532ffc2f">
                    <name>fromExechecker</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>true</hasDefault>
                    <defaultValue>false</defaultValue>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="da6b3f68-a1cc-43c0-89dc-aee40b531346">
                    <name>role</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="e1250e3f-ac84-4f5f-8467-b6d3f62878fd">
                    <name>parentPath</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <epv id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9d4">
                    <epvId>/21.dbbaa047-8f02-4397-b1b5-41f11b0256b3</epvId>
                </epv>
                <epv id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9d3">
                    <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
                </epv>
                <epv id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-9d2">
                    <epvId>/21.062854b5-6513-4da8-84ab-0126f90e550d</epvId>
                </epv>
                <searchableField id="aa2ba285-68fc-4db6-887a-98157eea19bc">
                    <name>ParentRequestNumber</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4868">
                        <expression>tw.local.ODCRequest.parentRequestNo</expression>
                    </expression>
                </searchableField>
                <searchableField id="33d7b82e-c34d-40d1-8582-f4aaae708391">
                    <name>RequestDate</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4867">
                        <expression>tw.local.ODCRequest.appInfo.requestDate</expression>
                    </expression>
                </searchableField>
                <searchableField id="bef40a0e-a592-44c7-852d-2ffc579d842e">
                    <name>RequestStatus</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4866">
                        <expression>tw.local.ODCRequest.appInfo.status</expression>
                    </expression>
                </searchableField>
                <searchableField id="470c65d8-d6fe-4bdc-8f4e-3bbd995d3801">
                    <name>InitiatorHubBranch</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4865">
                        <expression>tw.local.ODCRequest.initiator</expression>
                    </expression>
                </searchableField>
                <searchableField id="a9775f48-3dc0-418e-8c2a-4811f69fdeae">
                    <name>CustomerCIF</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4864">
                        <expression>tw.local.ODCRequest.cif</expression>
                    </expression>
                </searchableField>
                <searchableField id="0d27e84c-381d-4224-8650-1d8613ab3dbb">
                    <name>CustomerName</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4863">
                        <expression>tw.local.ODCRequest.customerName</expression>
                    </expression>
                </searchableField>
                <searchableField id="8db758e8-53b3-4d02-8e8d-9f01835a111c">
                    <name>InitiatorUserName</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4862">
                        <expression>tw.local.ODCRequest.appInfo.initiator</expression>
                    </expression>
                </searchableField>
            </pool>
            <extension id="bpdid:f7f6553919972802:-74de7c2c:194d5ff8341:-8d7" type="CASE">
                <caseFolder id="bcd34da4-3c2d-425d-9155-12b12732b272">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

