{"id": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "versionId": "481d6826-1a12-4925-b771-84e41e86c71b", "name": "Get Actions for screens", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "name": "Get Actions for screens", "lastModified": "1691583686643", "lastModifiedBy": "heba", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6", "2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b2", "versionId": "481d6826-1a12-4925-b771-84e41e86c71b", "dependencySummary": "<dependencySummary id=\"bpdid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4d86\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"f72e8ef9-4258-4573-843a-fb62026cd288\"},{\"incoming\":[\"e55fd0e4-29a9-46ce-8133-76db774847ff\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":650,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b4\"],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"9a566538-58e3-4581-88e6-edf4be6e49bc\"},{\"targetRef\":\"a0025a1a-cc7b-49bf-8c92-08a1abb366b6\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Script Task\",\"declaredType\":\"sequenceFlow\",\"id\":\"2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4\",\"sourceRef\":\"f72e8ef9-4258-4573-843a-fb62026cd288\"},{\"startQuantity\":1,\"outgoing\":[\"e55fd0e4-29a9-46ce-8133-76db774847ff\"],\"incoming\":[\"2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":323,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Script Task\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"a0025a1a-cc7b-49bf-8c92-08a1abb366b6\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"tw.local.conditions = tw.local.data;\\r\\n\\r\\ntw.local.actions =[];\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT01;\\r\\n\\/\\/Set Actions for Act01\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n\\ttw.local.action = tw.epv.CreationActions.submitRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.submitRequestToHubDirectory;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.cancelRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT02;\\r\\n\\/\\/Set Actions for Act02\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n \\r\\n \\ttw.local.action = tw.epv.CreationActions.submitRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.cancelRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n \\r\\n}\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT03;\\r\\n\\/\\/Set Actions for Act03\\r\\nif(tw.local.conditions.screenName == tw.local.screenName) \\r\\n{\\r\\n\\tif(tw.local.conditions.complianceApproval){\\r\\n \\t\\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\\r\\n\\t\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\t}\\r\\n\\ttw.local.action = tw.epv.CreationActions.approveRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.terminateRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action; \\r\\n\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT04;\\r\\n\\/\\/Set Actions for Act04\\r\\n\\r\\nif( (tw.local.conditions.screenName == tw.local.screenName) && tw.local.conditions.complianceApproval)\\r\\n{\\r\\n\\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\t\\r\\n\\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\t\\r\\n\\ttw.local.action = tw.epv.CreationActions.terminateRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n}\\r\\nelse{\\r\\n\\ttw.local.action = tw.epv.CreationActions.createContract;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.amendContract;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\t\\r\\n\\ttw.local.action = tw.epv.CreationActions.recreateContract;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\t\\r\\n\\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\t\\r\\n\\ttw.local.action = tw.epv.CreationActions.terminateRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT05;\\r\\n\\/\\/Set Actions for Act05\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n tw.local.action = tw.epv.CreationActions.authorize;\\r\\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\ntw.local.action = tw.epv.CreationActions.returnToTradeFo;\\r\\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\ntw.local.action = tw.epv.CreationActions.obtainApprovals;\\r\\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n\\r\\ntw.local.action = tw.epv.CreationActions.terminateRequest;\\r\\ntw.local.actions[tw.local.actions.listLength]= tw.local.action; \\r\\n\\r\\n}\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT06;\\r\\n\\/\\/Set Actions for Act06\\r\\nif(tw.local.conditions.screenName == tw.local.screenName){\\r\\n\\r\\n\\ttw.local.action = tw.epv.CreationActions.completeRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\\r\\n}\\r\\n\\r\\ntw.local.screenName = tw.epv.ScreenNames.CACT07;\\r\\nif(tw.local.conditions.screenName == tw.local.screenName)\\r\\n{\\r\\n\\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\\r\\n\\t\\r\\n\\ttw.local.action = tw.epv.CreationActions.approveRequest;\\r\\n\\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\\r\\n}\\r\\n\\r\\n\\r\\n\\/\\/--------------------------\\r\\ntw.local.results= tw.local.actions;\\r\\n\\r\\n\\r\\n\\r\\n\\r\\n\"]}},{\"targetRef\":\"9a566538-58e3-4581-88e6-edf4be6e49bc\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"e55fd0e4-29a9-46ce-8133-76db774847ff\",\"sourceRef\":\"a0025a1a-cc7b-49bf-8c92-08a1abb366b6\"},{\"itemSubjectRef\":\"itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4\",\"name\":\"conditions\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.318dc4e9-4eb6-4e8e-8b56-dc88c4ad0962\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"actions\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.838388be-bd17-434d-8b8a-ad6aac77c935\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"screenName\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.98462802-fd55-40c9-8636-b1864fbce829\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"action\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.30286102-8bb2-4bb3-829e-4c92c14e6370\"}],\"laneSet\":[{\"id\":\"be36812a-be1b-4767-8eef-427db2088665\",\"lane\":[{\"flowNodeRef\":[\"f72e8ef9-4258-4573-843a-fb62026cd288\",\"9a566538-58e3-4581-88e6-edf4be6e49bc\",\"a0025a1a-cc7b-49bf-8c92-08a1abb366b6\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"af7fc447-d364-4d9c-8711-cdf60cb994b1\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[true],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get Actions for screens\",\"declaredType\":\"process\",\"id\":\"1.2ab87399-906f-4f13-8778-10a62c41cfa4\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"results\",\"isCollection\":false,\"id\":\"2055.a693188f-ec7f-494e-8da9-9c0e2c42eb1b\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2055.5760666d-a1fd-4fb5-8a8f-8418b12c05d4\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.bed40437-f5de-4b1c-a063-7040de4075df\",\"epvProcessLinkId\":\"7f2b8603-7a3d-4b2f-8d10-b450158b2485\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"},{\"epvId\":\"21.daf76aa9-3be6-432b-b228-01c27b3012d3\",\"epvProcessLinkId\":\"4b794af4-5543-4b55-89b4-8b4fd549be73\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{}],\"outputSet\":[{}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"Review ODC Request by Trade FO,userRole,true,lastAction\\\"\"}]},\"itemSubjectRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"name\":\"data\",\"isCollection\":false,\"id\":\"2055.662bc25b-05e7-4912-8c9c-28deeda4dfc9\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "data", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.662bc25b-05e7-4912-8c9c-28deeda4dfc9", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "1", "hasDefault": "true", "defaultValue": "\"Review ODC Request by Trade FO,userRole,true,lastAction\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "c4f3673e-3a6d-4ff3-b843-1d42bc2eddc8", "versionId": "5e056444-0e42-4137-b85e-0e354be2b5ed"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.a693188f-ec7f-494e-8da9-9c0e2c42eb1b", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297", "seq": "2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "5ae94a7d-ef68-4b2c-bdf8-a243bd9a23e2", "versionId": "e7d54e71-1dc8-4d3f-97b2-0b3e7675eb32"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5760666d-a1fd-4fb5-8a8f-8418b12c05d4", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "parameterType": "2", "isArrayOf": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "085ee811-cbf4-465b-a564-bfbc48b7b21a", "versionId": "77901c83-4b6a-4f70-bdc0-1b56440f3ee0"}], "processVariable": [{"name": "conditions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.318dc4e9-4eb6-4e8e-8b56-dc88c4ad0962", "description": {"isNull": "true"}, "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "/12.004a1efa-0a17-40a6-a5b9-125042216ff4", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f4dd0f71-0423-4648-8e60-e030d4c06cc3", "versionId": "5ecdb192-73cb-4be2-8656-11a3e9471c9e"}, {"name": "actions", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.838388be-bd17-434d-8b8a-ad6aac77c935", "description": {"isNull": "true"}, "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "namespace": "2", "seq": "2", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "ca127a98-1a0f-490a-8acb-37db5712c8e1", "versionId": "b0bb1003-b9f9-4c4c-9cd4-9f4765c00adb"}, {"name": "screenName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.98462802-fd55-40c9-8636-b1864fbce829", "description": {"isNull": "true"}, "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "namespace": "2", "seq": "3", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9423a3f7-3678-4a81-9336-cac873bd257f", "versionId": "218ba86f-ef53-4cb0-b910-4a1906e0590c"}, {"name": "action", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.30286102-8bb2-4bb3-829e-4c92c14e6370", "description": {"isNull": "true"}, "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "c1a37bbb-1a37-4ea4-aedc-944a5d72a918", "versionId": "02533462-9eb0-49d6-9354-e123f613039f"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9a566538-58e3-4581-88e6-edf4be6e49bc", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.94cb2aa3-41ad-438a-9975-487f666d2338", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b4", "versionId": "2573c3cc-9c53-4dfd-984e-ea6cc415a32c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "650", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.94cb2aa3-41ad-438a-9975-487f666d2338", "haltProcess": "false", "guid": "83cfcc7c-6f59-4a14-8717-213b7d9bf284", "versionId": "e7469c6c-9bf1-424b-9363-aef7939f5e7e"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "name": "Script Task", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.5dc52de5-61b2-4d73-a78d-31ecd4f71d5b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4468", "versionId": "c1c82b19-2a93-4f20-8fb5-783c905d1624", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "323", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.5dc52de5-61b2-4d73-a78d-31ecd4f71d5b", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.conditions = tw.local.data;\r\r\n\r\r\ntw.local.actions =[];\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT01;\r\r\n//Set Actions for Act01\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\ttw.local.action = tw.epv.CreationActions.submitRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.submitRequestToHubDirectory;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.cancelRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT02;\r\r\n//Set Actions for Act02\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n \r\r\n \ttw.local.action = tw.epv.CreationActions.submitRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.cancelRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n \r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT03;\r\r\n//Set Actions for Act03\r\r\nif(tw.local.conditions.screenName == tw.local.screenName) \r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval){\r\r\n \t\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t}\r\r\n\ttw.local.action = tw.epv.CreationActions.approveRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action; \r\r\n\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT04;\r\r\n//Set Actions for Act04\r\r\n\r\r\nif( (tw.local.conditions.screenName == tw.local.screenName) && tw.local.conditions.complianceApproval)\r\r\n{\r\r\n\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n}\r\r\nelse{\r\r\n\ttw.local.action = tw.epv.CreationActions.createContract;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.amendContract;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.recreateContract;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT05;\r\r\n//Set Actions for Act05\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n tw.local.action = tw.epv.CreationActions.authorize;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\ntw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\ntw.local.action = tw.epv.CreationActions.obtainApprovals;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\ntw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action; \r\r\n\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT06;\r\r\n//Set Actions for Act06\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.completeRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT07;\r\r\nif(tw.local.conditions.screenName == tw.local.screenName)\r\r\n{\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.approveRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\r\r\n}\r\r\n\r\r\n\r\r\n//--------------------------\r\r\ntw.local.results= tw.local.actions;\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n", "isRule": "false", "guid": "627da6ad-f5d3-4ac4-b938-3916aece2a8a", "versionId": "21a0e545-8644-4932-8edb-80d8ae6e7396"}}], "EPV_PROCESS_LINK": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.9c4c8352-d4f4-4815-8221-c91f51e5acf8", "epvId": "/21.daf76aa9-3be6-432b-b228-01c27b3012d3", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "guid": "9cbdc4f0-9a9e-4289-810a-f1d44eb25b31", "versionId": "2ddd8ea1-28ad-4d0b-a0fd-551bb60db9d0"}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.9d6b18f2-f65b-484e-8a9e-87e35bcb2097", "epvId": "/21.bed40437-f5de-4b1c-a063-7040de4075df", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "guid": "0bcebb04-77f9-4b93-9049-d89e631b0184", "versionId": "94326003-fdee-4ed4-96aa-7a8f6899ec02"}], "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Actions for screens", "id": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": [{"epvId": "21.bed40437-f5de-4b1c-a063-7040de4075df", "epvProcessLinkId": "7f2b8603-7a3d-4b2f-8d10-b450158b2485"}, {"epvId": "21.daf76aa9-3be6-432b-b228-01c27b3012d3", "epvProcessLinkId": "4b794af4-5543-4b55-89b4-8b4fd549be73"}]}}, "ns16:dataInput": {"name": "data", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.662bc25b-05e7-4912-8c9c-28deeda4dfc9", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"Review ODC Request by Trade FO,userRole,true,lastAction\"", "useDefault": "true"}}}, "ns16:dataOutput": [{"name": "results", "itemSubjectRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297", "isCollection": "false", "id": "2055.a693188f-ec7f-494e-8da9-9c0e2c42eb1b"}, {"name": "error", "itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "id": "2055.5760666d-a1fd-4fb5-8a8f-8418b12c05d4"}], "ns16:inputSet": "", "ns16:outputSet": ""}, "ns16:laneSet": {"id": "be36812a-be1b-4767-8eef-427db2088665", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "af7fc447-d364-4d9c-8711-cdf60cb994b1", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["f72e8ef9-4258-4573-843a-fb62026cd288", "9a566538-58e3-4581-88e6-edf4be6e49bc", "a0025a1a-cc7b-49bf-8c92-08a1abb366b6"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "f72e8ef9-4258-4573-843a-fb62026cd288", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4"}, "ns16:endEvent": {"name": "End", "id": "9a566538-58e3-4581-88e6-edf4be6e49bc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "650", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:35b4"}, "ns16:incoming": "e55fd0e4-29a9-46ce-8133-76db774847ff"}, "ns16:sequenceFlow": [{"sourceRef": "f72e8ef9-4258-4573-843a-fb62026cd288", "targetRef": "a0025a1a-cc7b-49bf-8c92-08a1abb366b6", "name": "To <PERSON>ript Task", "id": "2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a0025a1a-cc7b-49bf-8c92-08a1abb366b6", "targetRef": "9a566538-58e3-4581-88e6-edf4be6e49bc", "name": "To End", "id": "e55fd0e4-29a9-46ce-8133-76db774847ff", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}], "ns16:scriptTask": {"scriptFormat": "text/x-javascript", "name": "Script Task", "id": "a0025a1a-cc7b-49bf-8c92-08a1abb366b6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "323", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "2027.1dcbdb36-af68-4219-8ce6-b01cc1a1e7a4", "ns16:outgoing": "e55fd0e4-29a9-46ce-8133-76db774847ff", "ns16:script": "tw.local.conditions = tw.local.data;\r\r\n\r\r\ntw.local.actions =[];\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT01;\r\r\n//Set Actions for Act01\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\ttw.local.action = tw.epv.CreationActions.submitRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.submitRequestToHubDirectory;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.cancelRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT02;\r\r\n//Set Actions for Act02\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n \r\r\n \ttw.local.action = tw.epv.CreationActions.submitRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.cancelRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n \r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT03;\r\r\n//Set Actions for Act03\r\r\nif(tw.local.conditions.screenName == tw.local.screenName) \r\r\n{\r\r\n\tif(tw.local.conditions.complianceApproval){\r\r\n \t\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\r\r\n\t\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t}\r\r\n\ttw.local.action = tw.epv.CreationActions.approveRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action; \r\r\n\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT04;\r\r\n//Set Actions for Act04\r\r\n\r\r\nif( (tw.local.conditions.screenName == tw.local.screenName) && tw.local.conditions.complianceApproval)\r\r\n{\r\r\n\ttw.local.action = tw.epv.CreationActions.obtainApprovals;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n}\r\r\nelse{\r\r\n\ttw.local.action = tw.epv.CreationActions.createContract;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.amendContract;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.recreateContract;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT05;\r\r\n//Set Actions for Act05\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n tw.local.action = tw.epv.CreationActions.authorize;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\ntw.local.action = tw.epv.CreationActions.returnToTradeFo;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\ntw.local.action = tw.epv.CreationActions.obtainApprovals;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n\r\r\ntw.local.action = tw.epv.CreationActions.terminateRequest;\r\r\ntw.local.actions[tw.local.actions.listLength]= tw.local.action; \r\r\n\r\r\n}\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT06;\r\r\n//Set Actions for Act06\r\r\nif(tw.local.conditions.screenName == tw.local.screenName){\r\r\n\r\r\n\ttw.local.action = tw.epv.CreationActions.completeRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength]= tw.local.action;\r\r\n}\r\r\n\r\r\ntw.local.screenName = tw.epv.ScreenNames.CACT07;\r\r\nif(tw.local.conditions.screenName == tw.local.screenName)\r\r\n{\r\r\n\ttw.local.action = tw.epv.CreationActions.returnToInitiator;\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\r\r\n\t\r\r\n\ttw.local.action = tw.epv.CreationActions.approveRequest;\r\r\n\ttw.local.actions[tw.local.actions.listLength] = tw.local.action;\r\r\n}\r\r\n\r\r\n\r\r\n//--------------------------\r\r\ntw.local.results= tw.local.actions;\r\r\n\r\r\n\r\r\n\r\r\n\r\r\n"}, "ns16:dataObject": [{"itemSubjectRef": "itm.12.004a1efa-0a17-40a6-a5b9-125042216ff4", "isCollection": "false", "name": "conditions", "id": "2056.318dc4e9-4eb6-4e8e-8b56-dc88c4ad0962"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "actions", "id": "2056.838388be-bd17-434d-8b8a-ad6aac77c935"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "screenName", "id": "2056.98462802-fd55-40c9-8636-b1864fbce829"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "action", "id": "2056.30286102-8bb2-4bb3-829e-4c92c14e6370"}]}}}, "link": {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e55fd0e4-29a9-46ce-8133-76db774847ff", "processId": "1.2ab87399-906f-4f13-8778-10a62c41cfa4", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6", "2025.a0025a1a-cc7b-49bf-8c92-08a1abb366b6"], "endStateId": "Out", "toProcessItemId": ["2025.9a566538-58e3-4581-88e6-edf4be6e49bc", "2025.9a566538-58e3-4581-88e6-edf4be6e49bc"], "guid": "54b99463-c790-43dc-bcee-8fc8c750aa64", "versionId": "c4a4bc6c-d0c3-4a22-9338-7b8eda5c15f6", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}}}}, "subType": "12", "hasDetails": false}