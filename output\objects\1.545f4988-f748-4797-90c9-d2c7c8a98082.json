{"id": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "versionId": "c1d19b2a-90ea-4231-9cca-065d226aa8e1", "name": "Check Parent Request", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Check Parent Request", "lastModified": "1698558555458", "lastModifiedBy": "<PERSON><PERSON>", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7", "2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "false", "errorHandlerItemId": [{"isNull": "true"}, {"isNull": "true"}], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "true", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "8265d9e1-33ae-42f3-a4b2-1df7c7bfba0c", "versionId": "c1d19b2a-90ea-4231-9cca-065d226aa8e1", "dependencySummary": "<dependencySummary id=\"5820306d-14ec-4a6f-ac6b-b0d0efb94743\" />", "jsonData": {"isNull": "true"}, "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "requestNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.52711b62-7df7-40b3-aa2d-0338fa7284dd", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "true", "defaultValue": "\"**************\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "d2b6ba47-c492-4f12-9f0d-41ee4294ea86", "versionId": "12fdf79c-95cd-4cf1-a71f-a1e557e16432"}, {"name": "idcContract", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.82d72677-767a-4f2c-bc85-8e8dcdebf086", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "1", "isArrayOf": "false", "classId": "/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "seq": "2", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.IDCContract();\r\nautoObject.collateralAmount = 0.0;\r\nautoObject.userReference = \"\";\r\nautoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();\r\nautoObject.settlementAccounts[0] = new tw.object.SettlementAccount();\r\nautoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();\r\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\r\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\r\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\r\nautoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;\r\nautoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();\r\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\r\nautoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\r\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\r\nautoObject.settlementAccounts[0].settCIF = \"\";\r\nautoObject.billAmount = 0.0;\r\nautoObject.billCurrency = new tw.object.DBLookup();\r\nautoObject.billCurrency.id = 0;\r\nautoObject.billCurrency.code = \"\";\r\nautoObject.billCurrency.arabicdescription = \"\";\r\nautoObject.billCurrency.englishdescription = \"\";\r\nautoObject.party = new tw.object.listOf.Parties();\r\nautoObject.party[0] = new tw.object.Parties();\r\nautoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.party[0].partyType.name = \"\";\r\nautoObject.party[0].partyType.value = \"\";\r\nautoObject.party[0].partyId = \"\";\r\nautoObject.party[0].name = \"\";\r\nautoObject.party[0].country = \"\";\r\nautoObject.party[0].reference = \"\";\r\nautoObject.party[0].address1 = \"\";\r\nautoObject.party[0].address2 = \"\";\r\nautoObject.party[0].address3 = \"\";\r\nautoObject.party[0].address4 = \"\";\r\nautoObject.party[0].media = \"\";\r\nautoObject.party[0].address = \"\";\r\nautoObject.party[0].phone = \"\";\r\nautoObject.party[0].fax = \"\";\r\nautoObject.party[0].email = \"\";\r\nautoObject.party[0].contactPersonName = \"\";\r\nautoObject.party[0].mobile = \"\";\r\nautoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.party[0].branch.name = \"\";\r\nautoObject.party[0].branch.value = \"\";\r\nautoObject.party[0].language = \"\";\r\nautoObject.party[0].partyCIF = \"\";\r\nautoObject.party[0].isNbeCustomer = false;\r\nautoObject.party[0].isRetrived = false;\r\nautoObject.party[0].cifVis = \"\";\r\nautoObject.sourceReference = \"\";\r\nautoObject.isLimitsTrackingRequired = false;\r\nautoObject.liquidationSummary = new tw.object.LiquidationSummary();\r\nautoObject.liquidationSummary.liquidationCurrency = \"\";\r\nautoObject.liquidationSummary.debitBasisby = \"\";\r\nautoObject.liquidationSummary.liquidationAmt = 0.0;\r\nautoObject.liquidationSummary.debitValueDate = new TWDate();\r\nautoObject.liquidationSummary.creditValueDate = new TWDate();\r\nautoObject.IDCProduct = new tw.object.DBLookup();\r\nautoObject.IDCProduct.id = 0;\r\nautoObject.IDCProduct.code = \"\";\r\nautoObject.IDCProduct.arabicdescription = \"\";\r\nautoObject.IDCProduct.englishdescription = \"\";\r\nautoObject.interestToDate = new TWDate();\r\nautoObject.transactionMaturityDate = new TWDate();\r\nautoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();\r\nautoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();\r\nautoObject.commissionsAndCharges[0].component = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();\r\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\r\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\r\nautoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;\r\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\r\nautoObject.commissionsAndCharges[0].waiver = false;\r\nautoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();\r\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\r\nautoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();\r\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\r\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\r\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\r\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\r\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\r\nautoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\r\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\r\nautoObject.commissionsAndCharges[0].defaultPercentage = 0.0;\r\nautoObject.commissionsAndCharges[0].changePercentage = 0.0;\r\nautoObject.commissionsAndCharges[0].description = \"\";\r\nautoObject.commissionsAndCharges[0].rateType = \"\";\r\nautoObject.commissionsAndCharges[0].percentageVis = \"\";\r\nautoObject.commissionsAndCharges[0].changeAmountVis = \"\";\r\nautoObject.transactionBaseDate = new TWDate();\r\nautoObject.tradeFinanceApprovalNumber = \"\";\r\nautoObject.FCContractNumber = \"\";\r\nautoObject.collateralCurrency = new tw.object.DBLookup();\r\nautoObject.collateralCurrency.id = 0;\r\nautoObject.collateralCurrency.code = \"\";\r\nautoObject.collateralCurrency.arabicdescription = \"\";\r\nautoObject.collateralCurrency.englishdescription = \"\";\r\nautoObject.interestRate = 0.0;\r\nautoObject.transactionTransitDays = 0;\r\nautoObject.swiftMessageData = new tw.object.SwiftMessageData();\r\nautoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.intermediary.line1 = \"\";\r\nautoObject.swiftMessageData.intermediary.line2 = \"\";\r\nautoObject.swiftMessageData.intermediary.line3 = \"\";\r\nautoObject.swiftMessageData.intermediary.line4 = \"\";\r\nautoObject.swiftMessageData.intermediary.line5 = \"\";\r\nautoObject.swiftMessageData.intermediary.line6 = \"\";\r\nautoObject.swiftMessageData.detailsOfCharge = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.receiver = \"\";\r\nautoObject.swiftMessageData.swiftMessageOption = \"\";\r\nautoObject.swiftMessageData.coverRequired = \"\";\r\nautoObject.swiftMessageData.transferType = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.receiverOfCover = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\r\nautoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\r\nautoObject.swiftMessageData.RTGS = \"\";\r\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\r\nautoObject.advices = new tw.object.listOf.ContractAdvice();\r\nautoObject.advices[0] = new tw.object.ContractAdvice();\r\nautoObject.advices[0].adviceCode = \"\";\r\nautoObject.advices[0].suppressed = false;\r\nautoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();\r\nautoObject.advices[0].advicelines.line1 = \"\";\r\nautoObject.advices[0].advicelines.line2 = \"\";\r\nautoObject.advices[0].advicelines.line3 = \"\";\r\nautoObject.advices[0].advicelines.line4 = \"\";\r\nautoObject.advices[0].advicelines.line5 = \"\";\r\nautoObject.advices[0].advicelines.line6 = \"\";\r\nautoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();\r\nautoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();\r\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\r\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\r\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\r\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\r\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\r\nautoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\r\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\r\nautoObject.cashCollateralAccounts[0].isGLFound = false;\r\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\r\nautoObject.IDCRequestStage = \"\";\r\nautoObject.transactionValueDate = new TWDate();\r\nautoObject.transactionTenorDays = 0;\r\nautoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();\r\nautoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();\r\nautoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\r\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\r\nautoObject.contractLimitsTracking[0].type = \"\";\r\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\r\nautoObject.contractLimitsTracking[0].customerNo = \"\";\r\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\r\nautoObject.contractLimitsTracking[0].amountTag = \"\";\r\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\r\nautoObject.contractLimitsTracking[0].isCIFfound = false;\r\nautoObject.interestFromDate = new TWDate();\r\nautoObject.interestAmount = 0.0;\r\nautoObject.haveInterest = false;\r\nautoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.accountNumberList[0].name = \"\";\r\nautoObject.accountNumberList[0].value = \"\";\r\nautoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();\r\nautoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();\r\nautoObject.facilities[0].facilityCode = \"\";\r\nautoObject.facilities[0].overallLimit = 0.0;\r\nautoObject.facilities[0].limitAmount = 0.0;\r\nautoObject.facilities[0].effectiveLimitAmount = 0.0;\r\nautoObject.facilities[0].availableAmount = 0.0;\r\nautoObject.facilities[0].expiryDate = new TWDate();\r\nautoObject.facilities[0].availableFlag = false;\r\nautoObject.facilities[0].authorizedFlag = false;\r\nautoObject.facilities[0].Utilization = 0.0;\r\nautoObject.facilities[0].returnCode = \"\";\r\nautoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();\r\nautoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();\r\nautoObject.facilities[0].facilityLines[0].lineCode = \"\";\r\nautoObject.facilities[0].facilityLines[0].lineAmount = 0.0;\r\nautoObject.facilities[0].facilityLines[0].availableAmount = 0.0;\r\nautoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;\r\nautoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();\r\nautoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.facilities[0].facilityLines[0].facilityBranch.name = \"\";\r\nautoObject.facilities[0].facilityLines[0].facilityBranch.value = \"\";\r\nautoObject.facilities[0].facilityLines[0].availableFlag = false;\r\nautoObject.facilities[0].facilityLines[0].authorizedFlag = false;\r\nautoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;\r\nautoObject.facilities[0].facilityLines[0].internalRemarks = \"\";\r\nautoObject.facilities[0].facilityLines[0].purpose = \"\";\r\nautoObject.facilities[0].facilityLines[0].LCCommissionPercentage = \"\";\r\nautoObject.facilities[0].facilityLines[0].LCDef = \"\";\r\nautoObject.facilities[0].facilityLines[0].LCCashCover = \"\";\r\nautoObject.facilities[0].facilityLines[0].IDCCommission = \"\";\r\nautoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = \"\";\r\nautoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = \"\";\r\nautoObject.facilities[0].facilityLines[0].debitAccountNumber = \"\";\r\nautoObject.facilities[0].facilityLines[0].lineCurrency = \"\";\r\nautoObject.facilities[0].facilityLines[0].lineSerialNumber = \"\";\r\nautoObject.facilities[0].facilityLines[0].returnCode = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCommission = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCashCover_Performance = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].CIF = \"\";\r\nautoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.facilities[0].facilityLines[0].partyType.name = \"\";\r\nautoObject.facilities[0].facilityLines[0].partyType.value = \"\";\r\nautoObject.facilities[0].facilityLines[0].facilityID = \"\";\r\nautoObject.facilities[0].status = \"\";\r\nautoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.facilities[0].facilityCurrency.name = \"\";\r\nautoObject.facilities[0].facilityCurrency.value = \"\";\r\nautoObject.facilities[0].facilityID = \"\";\r\nautoObject.limitsTrackingVIs = \"\";\r\nautoObject.interestVisibility = \"\";\r\nautoObject.adviceVis = \"\";\r\nautoObject.outstandingAmount = 0.0;\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "0c23bd51-310e-4373-9164-1acb3d189b9c", "versionId": "c46082f9-a80a-4d90-965e-61caeeb39396"}, {"name": "IDCRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.88e74584-565a-40c2-a722-ade695030bbd", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "1", "isArrayOf": "false", "classId": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "seq": "3", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.IDCRequest();\r\nautoObject.IDCRequestState = \"\";\r\nautoObject.commodityDescription = \"\";\r\nautoObject.countryOfOrigin = new tw.object.DBLookup();\r\nautoObject.countryOfOrigin.id = 0;\r\nautoObject.countryOfOrigin.code = \"\";\r\nautoObject.countryOfOrigin.arabicdescription = \"\";\r\nautoObject.countryOfOrigin.englishdescription = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.productsDetails = new tw.object.ProductsDetails();\r\nautoObject.productsDetails.destinationPort = \"\";\r\nautoObject.productsDetails.shippingDate = new TWDate();\r\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\r\nautoObject.productsDetails.HSProduct.id = 0;\r\nautoObject.productsDetails.HSProduct.code = \"\";\r\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\r\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\r\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\r\nautoObject.productsDetails.incoterms.id = 0;\r\nautoObject.productsDetails.incoterms.code = \"\";\r\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\r\nautoObject.productsDetails.incoterms.englishdescription = \"\";\r\nautoObject.productsDetails.ACID = \"\";\r\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\nautoObject.productsDetails.CBECommodityClassification.id = 0;\r\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\nautoObject.productsDetails.shipmentMethod.id = 0;\r\nautoObject.productsDetails.shipmentMethod.code = \"\";\r\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\r\nautoObject.financialDetails = new tw.object.FinancialDetails();\r\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\r\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\r\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\r\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\r\nautoObject.financialDetails.discountAmt = 0.0;\r\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\r\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\r\nautoObject.financialDetails.amtSight = 0.0;\r\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\r\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\r\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\r\nautoObject.financialDetails.amtPayableByNBE = 0.0;\r\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\r\nautoObject.financialDetails.executionHub.id = 0;\r\nautoObject.financialDetails.executionHub.code = \"\";\r\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\r\nautoObject.financialDetails.executionHub.englishdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\r\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.documentCurrency.id = 0;\r\nautoObject.financialDetails.documentCurrency.code = \"\";\r\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.daysTillMaturity = 0;\r\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\r\nautoObject.financialDetails.amountAdvanced = 0.0;\r\nautoObject.financialDetails.paymentAccount = \"\";\r\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\r\nautoObject.financialDetails.documentAmount = 0.0;\r\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfFunds.id = 0;\r\nautoObject.financialDetails.sourceOfFunds.code = \"\";\r\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\r\nautoObject.financialDetails.chargesAccount = \"\";\r\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\r\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\r\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\r\nautoObject.IDCRequestType = new tw.object.DBLookup();\r\nautoObject.IDCRequestType.id = 0;\r\nautoObject.IDCRequestType.code = \"\";\r\nautoObject.IDCRequestType.arabicdescription = \"\";\r\nautoObject.IDCRequestType.englishdescription = \"\";\r\nautoObject.isIDCWithdrawn = false;\r\nautoObject.IDCRequestStage = \"\";\r\nautoObject.FCContractNumber = \"1234\";\r\nautoObject.billOfLading = new tw.object.listOf.Invoice();\r\nautoObject.billOfLading[0] = new tw.object.Invoice();\r\nautoObject.billOfLading[0].date = new TWDate();\r\nautoObject.billOfLading[0].number = \"\";\r\nautoObject.importPurpose = new tw.object.DBLookup();\r\nautoObject.importPurpose.id = 0;\r\nautoObject.importPurpose.code = \"\";\r\nautoObject.importPurpose.arabicdescription = \"\";\r\nautoObject.importPurpose.englishdescription = \"\";\r\nautoObject.IDCRequestNature = new tw.object.DBLookup();\r\nautoObject.IDCRequestNature.id = 0;\r\nautoObject.IDCRequestNature.code = \"\";\r\nautoObject.IDCRequestNature.arabicdescription = \"\";\r\nautoObject.IDCRequestNature.englishdescription = \"\";\r\nautoObject.customerInformation = new tw.object.CustomerInformation();\r\nautoObject.customerInformation.CIFNumber = \"\";\r\nautoObject.customerInformation.importCardNumber = \"\";\r\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\r\nautoObject.customerInformation.customerName = \"\";\r\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\r\nautoObject.customerInformation.CBENumber = \"\";\r\nautoObject.customerInformation.customerSector = \"\";\r\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\r\nautoObject.customerInformation.facilityType.id = 0;\r\nautoObject.customerInformation.facilityType.code = \"\";\r\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\r\nautoObject.customerInformation.facilityType.englishdescription = \"\";\r\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\r\nautoObject.customerInformation.taxCardNumber = \"\";\r\nautoObject.customerInformation.customerType = \"\";\r\nautoObject.customerInformation.addressLine1 = \"\";\r\nautoObject.customerInformation.addressLine2 = \"\";\r\nautoObject.invoices = new tw.object.listOf.Invoice();\r\nautoObject.invoices[0] = new tw.object.Invoice();\r\nautoObject.invoices[0].date = new TWDate();\r\nautoObject.invoices[0].number = \"\";\r\nautoObject.productCategory = new tw.object.DBLookup();\r\nautoObject.productCategory.id = 0;\r\nautoObject.productCategory.code = \"\";\r\nautoObject.productCategory.arabicdescription = \"\";\r\nautoObject.productCategory.englishdescription = \"\";\r\nautoObject.documentsSource = new tw.object.DBLookup();\r\nautoObject.documentsSource.id = 0;\r\nautoObject.documentsSource.code = \"\";\r\nautoObject.documentsSource.arabicdescription = \"\";\r\nautoObject.documentsSource.englishdescription = \"\";\r\nautoObject.ParentIDCRequestNumber = \"00102230001070\";\r\nautoObject.paymentTerms = new tw.object.DBLookup();\r\nautoObject.paymentTerms.id = 0;\r\nautoObject.paymentTerms.code = \"\";\r\nautoObject.paymentTerms.arabicdescription = \"\";\r\nautoObject.paymentTerms.englishdescription = \"\";\r\nautoObject.approvals = new tw.object.Approvals();\r\nautoObject.approvals.CAD = false;\r\nautoObject.approvals.treasury = false;\r\nautoObject.approvals.compliance = false;\r\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0].startTime = new TWDate();\r\nautoObject.appLog[0].endTime = new TWDate();\r\nautoObject.appLog[0].userName = \"\";\r\nautoObject.appLog[0].role = \"\";\r\nautoObject.appLog[0].step = \"\";\r\nautoObject.appLog[0].action = \"\";\r\nautoObject.appLog[0].comment = \"\";\r\nautoObject.appLog[0].terminateReason = \"\";\r\nautoObject.appLog[0].returnReason = \"\";\r\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.DBID = 0;\r\n\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "c077a444-2ce2-4f8c-b6fd-da04dccee6e7", "versionId": "f68829f8-5b87-4567-b6ed-457006ce98b7"}, {"name": "parentIDC", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.455bc6b1-7ba6-4720-94d5-a300a02c504f", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "1", "isArrayOf": "false", "classId": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "seq": "4", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.IDCRequest();\r\nautoObject.IDCRequestState = \"\";\r\nautoObject.commodityDescription = \"\";\r\nautoObject.countryOfOrigin = new tw.object.DBLookup();\r\nautoObject.countryOfOrigin.id = 0;\r\nautoObject.countryOfOrigin.code = \"\";\r\nautoObject.countryOfOrigin.arabicdescription = \"\";\r\nautoObject.countryOfOrigin.englishdescription = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"00102230001070\";\r\nautoObject.productsDetails = new tw.object.ProductsDetails();\r\nautoObject.productsDetails.destinationPort = \"\";\r\nautoObject.productsDetails.shippingDate = new TWDate();\r\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\r\nautoObject.productsDetails.HSProduct.id = 0;\r\nautoObject.productsDetails.HSProduct.code = \"\";\r\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\r\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\r\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\r\nautoObject.productsDetails.incoterms.id = 0;\r\nautoObject.productsDetails.incoterms.code = \"\";\r\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\r\nautoObject.productsDetails.incoterms.englishdescription = \"\";\r\nautoObject.productsDetails.ACID = \"\";\r\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\nautoObject.productsDetails.CBECommodityClassification.id = 0;\r\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\nautoObject.productsDetails.shipmentMethod.id = 0;\r\nautoObject.productsDetails.shipmentMethod.code = \"\";\r\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\r\nautoObject.financialDetails = new tw.object.FinancialDetails();\r\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\r\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\r\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\r\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\r\nautoObject.financialDetails.discountAmt = 0.0;\r\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\r\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\r\nautoObject.financialDetails.amtSight = 0.0;\r\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\r\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\r\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\r\nautoObject.financialDetails.amtPayableByNBE = 0.0;\r\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\r\nautoObject.financialDetails.executionHub.id = 0;\r\nautoObject.financialDetails.executionHub.code = \"\";\r\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\r\nautoObject.financialDetails.executionHub.englishdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\r\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.documentCurrency.id = 0;\r\nautoObject.financialDetails.documentCurrency.code = \"\";\r\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.daysTillMaturity = 0;\r\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\r\nautoObject.financialDetails.amountAdvanced = 0.0;\r\nautoObject.financialDetails.paymentAccount = \"\";\r\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\r\nautoObject.financialDetails.documentAmount = 0.0;\r\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfFunds.id = 0;\r\nautoObject.financialDetails.sourceOfFunds.code = \"\";\r\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\r\nautoObject.financialDetails.chargesAccount = \"\";\r\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\r\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\r\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\r\nautoObject.IDCRequestType = new tw.object.DBLookup();\r\nautoObject.IDCRequestType.id = 0;\r\nautoObject.IDCRequestType.code = \"\";\r\nautoObject.IDCRequestType.arabicdescription = \"\";\r\nautoObject.IDCRequestType.englishdescription = \"\";\r\nautoObject.isIDCWithdrawn = false;\r\nautoObject.IDCRequestStage = \"\";\r\nautoObject.FCContractNumber = \"123\";\r\nautoObject.billOfLading = new tw.object.listOf.Invoice();\r\nautoObject.billOfLading[0] = new tw.object.Invoice();\r\nautoObject.billOfLading[0].date = new TWDate();\r\nautoObject.billOfLading[0].number = \"\";\r\nautoObject.importPurpose = new tw.object.DBLookup();\r\nautoObject.importPurpose.id = 0;\r\nautoObject.importPurpose.code = \"\";\r\nautoObject.importPurpose.arabicdescription = \"\";\r\nautoObject.importPurpose.englishdescription = \"\";\r\nautoObject.IDCRequestNature = new tw.object.DBLookup();\r\nautoObject.IDCRequestNature.id = 0;\r\nautoObject.IDCRequestNature.code = \"\";\r\nautoObject.IDCRequestNature.arabicdescription = \"\";\r\nautoObject.IDCRequestNature.englishdescription = \"\";\r\nautoObject.customerInformation = new tw.object.CustomerInformation();\r\nautoObject.customerInformation.CIFNumber = \"\";\r\nautoObject.customerInformation.importCardNumber = \"\";\r\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\r\nautoObject.customerInformation.customerName = \"\";\r\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\r\nautoObject.customerInformation.CBENumber = \"\";\r\nautoObject.customerInformation.customerSector = \"\";\r\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\r\nautoObject.customerInformation.facilityType.id = 0;\r\nautoObject.customerInformation.facilityType.code = \"\";\r\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\r\nautoObject.customerInformation.facilityType.englishdescription = \"\";\r\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\r\nautoObject.customerInformation.taxCardNumber = \"\";\r\nautoObject.customerInformation.customerType = \"\";\r\nautoObject.customerInformation.addressLine1 = \"\";\r\nautoObject.customerInformation.addressLine2 = \"\";\r\nautoObject.invoices = new tw.object.listOf.Invoice();\r\nautoObject.invoices[0] = new tw.object.Invoice();\r\nautoObject.invoices[0].date = new TWDate();\r\nautoObject.invoices[0].number = \"\";\r\nautoObject.productCategory = new tw.object.DBLookup();\r\nautoObject.productCategory.id = 0;\r\nautoObject.productCategory.code = \"\";\r\nautoObject.productCategory.arabicdescription = \"\";\r\nautoObject.productCategory.englishdescription = \"\";\r\nautoObject.documentsSource = new tw.object.DBLookup();\r\nautoObject.documentsSource.id = 0;\r\nautoObject.documentsSource.code = \"\";\r\nautoObject.documentsSource.arabicdescription = \"\";\r\nautoObject.documentsSource.englishdescription = \"\";\r\nautoObject.ParentIDCRequestNumber = \"\";\r\nautoObject.paymentTerms = new tw.object.DBLookup();\r\nautoObject.paymentTerms.id = 0;\r\nautoObject.paymentTerms.code = \"\";\r\nautoObject.paymentTerms.arabicdescription = \"\";\r\nautoObject.paymentTerms.englishdescription = \"\";\r\nautoObject.approvals = new tw.object.Approvals();\r\nautoObject.approvals.CAD = false;\r\nautoObject.approvals.treasury = false;\r\nautoObject.approvals.compliance = false;\r\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0].startTime = new TWDate();\r\nautoObject.appLog[0].endTime = new TWDate();\r\nautoObject.appLog[0].userName = \"\";\r\nautoObject.appLog[0].role = \"\";\r\nautoObject.appLog[0].step = \"\";\r\nautoObject.appLog[0].action = \"\";\r\nautoObject.appLog[0].comment = \"\";\r\nautoObject.appLog[0].terminateReason = \"\";\r\nautoObject.appLog[0].returnReason = \"\";\r\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.DBID = 0;\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "f8316a3e-42c9-4d9f-8a2e-9cfc972e745e", "versionId": "3b8e9b6e-5ad2-4762-b0cd-187abea988d9"}, {"name": "isvalid", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2df15530-52f0-41a9-9c25-bae06fa34398", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "5", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7b223b02-48e8-44e8-be27-150a1c53743f", "versionId": "b1d26cd7-3d40-4e37-a497-2ac38a6928c0"}, {"name": "massege", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0d917db9-6dd3-40cd-963c-f56294d058e5", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7413d1c0-f338-472f-93fa-5639b497b6ee", "versionId": "33fedca2-d6c0-412f-95f3-7bbe16b45098"}, {"name": "DBID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2164b222-615a-4a75-a5d5-98b99470a6ff", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "seq": "7", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "84264581-9d3e-4b18-a6eb-b63d11d70128", "versionId": "bb2d2412-937c-4b61-aa92-fee019479bfc"}, {"name": "idcContract", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.60d0706b-a7ec-4ef7-93af-f527d8da9f48", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "373f5c96-0a4d-4447-826a-44b0c2bcc455", "versionId": "c3af8c21-8f1b-4037-984d-e4d1be20c3de"}, {"name": "parentIDCContract", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ef212b69-feda-46ce-97d9-c2a5b4fe91f3", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "seq": "9", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "5011e813-e938-4e0b-bef3-f396136491f8", "versionId": "91f8dc18-4074-4547-b306-da8f23766204"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.5612afbe-e447-4c02-8202-fd20453fd03f", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "seq": "10", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1d04bca1-a7dd-482e-a403-f833ca6a7215", "versionId": "5317fa36-6037-4178-bb3c-cc40f10076e6"}, {"name": "IDCRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.9a3d0a85-297e-4663-93c4-721d815eb332", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "seq": "11", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7a071b20-e0d4-4f1b-80b8-2050de62e30a", "versionId": "0346d27f-303b-4f7d-9bb5-e9330e85a08d"}, {"name": "folderID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.9bd55f86-fcc3-485e-b9f0-c45dede585e9", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "seq": "12", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "4756dd56-95c9-4ae6-8b93-494d7c6d4d2a", "versionId": "462dbeb2-3138-4437-a2b5-e5588a676632"}, {"name": "fullPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.1f4f4e36-97e7-43ae-b14a-21ca79480c5f", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "13", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "b7c606a3-b14e-4dbb-a4c4-76674ed7e0d4", "versionId": "970d8d42-94f1-4cf6-9f90-a2357b8ab68f"}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.0d63f311-de13-4c02-9731-3cae95646591", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "14", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "a69fcb30-fc54-42af-a685-c48e974a47da", "versionId": "6f20a5aa-2757-431c-ad97-2a0dc4d5567e"}, {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.6db4acfa-0ea2-4e89-ba02-4abf1ab6a431", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "parameterType": "3", "isArrayOf": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "seq": "15", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "7d892549-cc81-4c34-88e0-d3448d2dda21", "versionId": "786b82c9-ecfc-4447-8fa1-c64095c218c3"}], "processVariable": [{"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ff8aa7a3-bc58-45dc-a662-d32c344a8f11", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "1", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "b0136a31-56e2-47f4-af73-ecb68abba192", "versionId": "83a00039-523f-4b36-932d-31edce4df529"}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.736a5ca8-5151-467e-9d77-576f7c5e0200", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d3b81230-b933-463a-885e-34ef1c5fb1db", "versionId": "bdd7e978-731d-42aa-b5e7-6f8335d583ae"}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3455980e-a47c-48bb-b1cc-3073d098fa0b", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "e6c53d94-5ef3-48a0-8f3d-c39c98c7f2b2", "versionId": "48b71595-f479-4e8d-8b7f-0fc1bb9db122"}, {"name": "haveRunningRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0bf11c4f-4221-4ab4-8f45-da3ecef66b36", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "969f2002-cf3c-4de6-8a53-e992ff31cdd9", "versionId": "fbd73a32-9173-472a-b9d9-03293eaa6f1d"}, {"name": "isTotalyPaid", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.07190f21-b578-4bde-a51e-36031244b366", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "d16f7875-ee25-4541-b27c-************", "versionId": "933bbe41-d7a3-4d8b-9f4a-f7a2f756acb7"}, {"name": "errorMSG", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.baa7b86f-9ab2-4e31-91aa-68fe1d638c3e", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "83a599d4-458c-42bc-985f-b98db4c73647", "versionId": "3a63b6de-422e-4bfe-b034-3c62506b0f12"}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b8aad2ab-921e-4505-bbd0-aa2a6d94e28e", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "92d6c766-f2cd-46e7-bf29-3d99f99216ac", "versionId": "dd3fd7cd-ec76-4ca3-8f2b-42ae73083633"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.fda30d62-86a6-461e-85ac-************", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "87ccf7ec-e7ff-4c9a-89b7-49799ce05136", "versionId": "cbdff536-c842-4cec-b9de-44662761403d"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.0ccabf04-2650-48f4-a1ca-b18f255675c7", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "9", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "511e6517-fcc6-4606-8d48-d7f3f5f482c0", "versionId": "865bce3c-50a9-4496-bd2d-12cc1d94fa35"}, {"name": "SCQueryBCContractRes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.d5d0cbde-a3d5-4a67-99e3-0b43e353f916", "description": {"isNull": "true"}, "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "namespace": "2", "seq": "10", "isArrayOf": "false", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "9229332e-1708-4f3c-9a14-de75fd6fff8c", "versionId": "7f4218ae-c31d-4080-90d8-26f6a494add7"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "has running request", "tWComponentName": "Switch", "tWComponentId": "3013.967f4214-6e41-4ee0-bcb6-6c84724105b3", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:468", "versionId": "09f742eb-c147-4d20-8862-5b306ac64478", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1302", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.967f4214-6e41-4ee0-bcb6-6c84724105b3", "guid": "27d03987-d9aa-429f-b953-8681296c26ee", "versionId": "5fd9aec7-19b8-4a4c-9a88-aca995d6c32c", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.fdcc59c0-e79a-4853-979e-de103323605d", "switchId": "3013.967f4214-6e41-4ee0-bcb6-6c84724105b3", "seq": "1", "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709f", "condition": "tw.local.haveRunningRequest\t  ==\t  false", "guid": "a1814963-f09e-40f5-8239-fe12b46f5839", "versionId": "7f705e25-eb9c-4873-b412-bed2b3900283"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.9f0a234f-f85c-4b82-90d7-323282f28c0e", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "End Event", "tWComponentName": "Exception", "tWComponentId": "3007.41c59d17-8ff0-4089-b883-2f51695f394a", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:462", "versionId": "11ba0ef7-ea27-40f9-9092-2054af6a3d79", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1858", "y": "221", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exceptionId": "3007.41c59d17-8ff0-4089-b883-2f51695f394a", "message": "", "faultStyle": "1", "guid": "b8556a7d-ef9d-4aeb-8b04-4a95a1e5213d", "versionId": "56421de7-1cf5-4e2c-9a78-c3508fcca4a1", "parameterMapping": {"name": "", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ecf0e07d-07e7-4f78-9357-d8c0e8ea579f", "processParameterId": "2055.6db4acfa-0ea2-4e89-ba02-4abf1ab6a431", "parameterMappingParentId": "3007.41c59d17-8ff0-4089-b883-2f51695f394a", "useDefault": "false", "value": "tw.local.error", "classRef": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isList": "false", "isInput": "false", "guid": "3f10f1c0-6a8f-43ad-83a8-63848c8a23f4", "versionId": "64208d4c-b0a6-4d17-9440-936fd0cd3725", "description": {"isNull": "true"}}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.21bf961b-7097-4d5b-b1b0-25a6ab444787", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Get Request Number And CBE", "tWComponentName": "SubProcess", "tWComponentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:464", "versionId": "1cf80d2e-12ef-4f97-be1f-c6121af629ca", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1425", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error8", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "attachedProcessRef": "/1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13", "guid": "5f134062-5fb6-43ad-aa5f-aa65894b6141", "versionId": "d2993d87-e8bb-4269-bdc0-591bcd27112a", "parameterMapping": [{"name": "result", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.048f91a0-73fe-4835-b363-3741f10f0fc5", "processParameterId": "2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.IDCRequest.customerInformation.isCustomeSanctionedbyCBE", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "1a0b4f58-12c0-4495-909e-a78a4a8b8854", "versionId": "24a22901-4170-4e72-ba44-c7b7e2885d0f", "description": {"isNull": "true"}}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0a747e30-0a66-470f-9f51-9afae31225fb", "processParameterId": "2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.error", "classRef": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isList": "false", "isInput": "false", "guid": "c8e450c8-7c2f-4b3a-9df4-79829a018f99", "versionId": "2db0b1df-515e-4d57-b4a0-61f1d0d6500c", "description": {"isNull": "true"}}, {"name": "BPM_Request_Number", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.33f65645-5b9f-49fa-8947-08728adcf039", "processParameterId": "2055.d22b4a18-865c-4b84-b879-26ee9e3f322f", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.IDCRequest.appInfo.instanceID", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "549663a3-cf40-4534-9cf9-b0f4dc9d6e8b", "versionId": "494b9831-f2c9-4a1b-8d7f-f497cbcd0391", "description": {"isNull": "true"}}, {"name": "idcRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.174046cb-2eaa-4183-9cba-13096ea8b597", "processParameterId": "2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.IDCRequest", "classRef": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isList": "false", "isInput": "true", "guid": "e1118f3d-1f19-479b-9c6e-82ff49b52f48", "versionId": "8e3c152a-19fe-4f86-89c9-0326e24b442a", "description": {"isNull": "true"}}, {"name": "folderID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.3d39c62c-1dcb-46df-8830-e35b8611024b", "processParameterId": "2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.folderID", "classRef": "30ff1a81-49af-43c7-b3f4-9f36d5bc8365/12.3212d5b3-f692-41b3-a893-343dc5c3df01", "isList": "false", "isInput": "false", "guid": "370e20eb-6c44-48c7-8fbe-9a5728871982", "versionId": "c33656ff-aff0-4b93-af4f-047ef5d18b82", "description": {"isNull": "true"}}, {"name": "parentPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b2ab6cfb-348e-4f41-bc7f-42a765e1e411", "processParameterId": "2055.31e2d85c-18d8-4756-82c5-efad7879177e", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.parentPath", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "e8849b45-045a-4c64-99b5-580e08366839", "versionId": "eda5a1b7-1f25-42ed-91ef-01c08949d00a", "description": {"isNull": "true"}}, {"name": "fullPath", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ad46f971-ba6a-4d52-b8a5-49f5f8af58d3", "processParameterId": "2055.b304617e-98f3-4070-b457-59e470497a2f", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.fullPath", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "ee2fee33-f616-446b-ac35-5f8b49d9d35c", "versionId": "efcdcc52-3dde-4e29-9609-2a26677a5463", "description": {"isNull": "true"}}, {"name": "parentIDC", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.acb7af5d-2399-4ce6-8cba-a86554ccf1ab", "processParameterId": "2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441", "parameterMappingParentId": "3012.c32a9ea1-84fc-449c-8c3c-b03b72f3697c", "useDefault": "false", "value": "tw.local.parentIDC", "classRef": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isList": "false", "isInput": "true", "guid": "4d91c8bb-f81f-4648-824e-e68bfcd781b9", "versionId": "f16fc945-a7ce-45a3-b718-a326a85ae6ac", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f1ee4e76-20d9-4426-a039-72de129ca5a0", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Insert IDC Request", "tWComponentName": "SubProcess", "tWComponentId": "3012.480e0f91-d28e-45da-ba32-47d7705f0718", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:460", "versionId": "20195e9f-3002-40db-9cb9-db6e1dc3790a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1590", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error6", "locationId": "bottomLeft", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.480e0f91-d28e-45da-ba32-47d7705f0718", "attachedProcessRef": "/1.06eaabde-33db-480a-9d65-982fa27c2eac", "guid": "035e1b0a-d994-464a-86ed-************", "versionId": "de345546-be3f-413c-8bc2-2750e6ec4765", "parameterMapping": [{"name": "DBID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.606dba56-b0c0-4055-8539-339e7615e2a4", "processParameterId": "2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb", "parameterMappingParentId": "3012.480e0f91-d28e-45da-ba32-47d7705f0718", "useDefault": "false", "value": "tw.local.DBID", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "false", "guid": "5952b563-9679-4c20-8429-4a695bdb7c16", "versionId": "23f30b63-6d3d-49a4-b85e-702547979f71", "description": {"isNull": "true"}}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f3a5bef2-6085-48c5-ae63-74a5e350d631", "processParameterId": "2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01", "parameterMappingParentId": "3012.480e0f91-d28e-45da-ba32-47d7705f0718", "useDefault": "false", "value": "tw.local.error", "classRef": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isList": "false", "isInput": "false", "guid": "1c470618-db2a-42a7-80ae-f340f68b3dd8", "versionId": "9484b5ba-5f59-4c6f-9a08-89b652f95385", "description": {"isNull": "true"}}, {"name": "IDCRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.f353e9e8-564b-435a-81a1-d224fecf6410", "processParameterId": "2055.6287bfa2-c4da-4ec3-842c-a164e045a461", "parameterMappingParentId": "3012.480e0f91-d28e-45da-ba32-47d7705f0718", "useDefault": "false", "value": "tw.local.IDCRequest", "classRef": "/12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isList": "false", "isInput": "true", "guid": "c2c1bf50-**************-e8a00fb7f821", "versionId": "976b55f0-f556-4c39-9a79-854f92414265", "description": {"isNull": "true"}}, {"name": "idcContract", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.8d62b427-ae2c-4d1a-af15-36a47412819f", "processParameterId": "2055.e4ea1176-3273-4117-87c0-b882c7df46c4", "parameterMappingParentId": "3012.480e0f91-d28e-45da-ba32-47d7705f0718", "useDefault": "false", "value": "tw.local.idcContract", "classRef": "/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "isList": "false", "isInput": "true", "guid": "ba174035-7844-4db7-b39d-2ef9dc9319a4", "versionId": "b80dd2d3-0ab7-4c88-bdb9-4a2a5831321b", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a6129d38-34a3-4623-9a9e-d8e404c126d8", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "is totally paid", "tWComponentName": "Switch", "tWComponentId": "3013.d58b5f22-6762-44f2-90c8-7849d5ffab31", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:46c", "versionId": "20ce5d64-b05c-4b7c-a751-7f9df8a6bc59", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "871", "y": "79", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.d58b5f22-6762-44f2-90c8-7849d5ffab31", "guid": "a499ee9b-a15e-44dc-977d-4254dac8edd1", "versionId": "4cca14d1-dd0b-428a-986b-827aab9528b1", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.777d977d-5594-4aaf-ad1a-69ec1d0e12e7", "switchId": "3013.d58b5f22-6762-44f2-90c8-7849d5ffab31", "seq": "1", "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-70a0", "condition": "tw.local.isTotalyPaid\t  ==\t  false", "guid": "19286535-359b-4952-8de3-bd2ca6544862", "versionId": "f5da11ba-dba5-4a8c-8bd3-60d9e008a26b"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "MW_FC Query DC Contract", "tWComponentName": "SubProcess", "tWComponentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:45e", "versionId": "3c82e144-f586-408e-a55b-06e0badc2830", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.8d2636e8-46d1-44b1-b0ef-59eb2e54de63", "processItemId": "2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc", "location": "1", "script": {"isNull": "true"}, "guid": "e5f18774-c9d0-43db-acfa-392d312c46d9", "versionId": "bb1a50a1-29ad-4e68-8d53-536536e05da0"}, "layoutData": {"x": "369", "y": "58", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error7", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.3875c748-15fc-40ef-bef1-ea4d905d7f75", "guid": "5d7274f8-5917-47d2-8383-4b7248b3bd1c", "versionId": "de45752c-4816-4201-aec5-deabd8ba3ace", "parameterMapping": [{"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.574f439a-6edd-4df4-ad12-0d9ff97dd1cb", "processParameterId": "2055.95835270-515f-4794-a207-a5e2aa301c0e", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "8c70e41c-5135-4e9e-b0e8-1587f9deeb57", "versionId": "0bfb5ba9-19fd-47f2-9cb6-f5c7e0d2f36b", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c83d1875-9333-465c-9dc4-ce6c517d674a", "processParameterId": "2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.local.errorCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "cedc58c4-f553-462c-9c19-101e0ffebdd4", "versionId": "1c9e0689-0223-48f7-ad25-34a4d0e8b404", "description": {"isNull": "true"}}, {"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.738ebb65-2c32-4a12-af51-42d6e9c87163", "processParameterId": "2055.42effea6-8d8b-437c-958a-da5cf9119674", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "7d84d764-4684-46ec-b2b0-9997113ea376", "versionId": "33b91614-25eb-4d0c-b023-56fb544ce2f9", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e64631ef-f052-47bf-8945-4864a57c4f7e", "processParameterId": "2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.system.currentProcessInstanceID", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "506640df-7ea7-4bbc-b1c6-f522d217edb2", "versionId": "3775bdf1-6af3-4fa5-ab7a-2cf82f3157ea", "description": {"isNull": "true"}}, {"name": "contractNumber", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b60a85ad-8b83-452b-9721-62113969b2ef", "processParameterId": "2055.8f786b77-ae66-4547-a444-d6ccb8969c42", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.local.parentIDC.FCContractNumber", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "6ea50abc-d5ee-4b9c-856f-f7e92fdca057", "versionId": "4d9f03a9-cf05-4c4b-bcdc-f0249a65e45c", "description": {"isNull": "true"}}, {"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.cfb4af85-1aa3-4615-b107-afa462b7598e", "processParameterId": "2055.4ff12b5f-6b69-4504-a730-046ee5c2000a", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.system.user_id", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "fb135835-6db9-44c7-9af0-96a3385776b2", "versionId": "5f0543d1-a741-4920-8a14-5719bb875d1d", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.6013b4fb-95d8-426d-9016-a4a87431ca78", "processParameterId": "2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "5cb2590e-a5e8-4104-9bd5-565a18414d6a", "versionId": "9b0adfd2-0c5e-4cb2-8c96-02ecb3ec85d7", "description": {"isNull": "true"}}, {"name": "SCQueryBCContractRes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.8de140ca-2d4d-4f9e-a551-ce14f232d16e", "processParameterId": "2055.004a9996-2e6b-4d60-821e-5db8e4ba2271", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.local.SCQueryBCContractRes", "classRef": "/12.*************-4cbb-a781-44d233d577c6", "isList": "false", "isInput": "false", "guid": "717780f6-6ec2-4762-9057-d7682496fc86", "versionId": "bb3062bf-a53c-42f5-a253-4128359866ba", "description": {"isNull": "true"}}, {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1f377485-115b-4527-8294-034273c42c45", "processParameterId": "2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "\"INWARD DOCUMENTARY COLLECTION\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "b7a9b573-f445-4b68-a480-d6b3eee52cd9", "versionId": "c4ad8e5b-ee29-4e07-8614-477d63e2eea1", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.056fa3d4-ffe7-4906-80e2-7f8d024a98df", "processParameterId": "2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "e376fd1f-0b52-4452-aa51-973f310be611", "versionId": "c79947c4-663a-40fe-88cd-c04da7ebb3bf", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.26b77f4d-b5b5-4183-b4ff-1b54a76b6541", "processParameterId": "2055.ee7811d9-22b1-4727-9148-bcac74c306af", "parameterMappingParentId": "3012.a16ca1d2-019c-459f-ba86-5992e8f58f7c", "useDefault": "false", "value": "tw.local.errorMSG", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "7f3ca078-883e-43d8-bc41-2e292d8227a7", "versionId": "ece0718d-a600-41c4-b6a3-707f4c8a158d", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "check has running request", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.b05e3bee-734e-4167-9b85-05f73d545d90", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:461", "versionId": "714a3801-70f0-49a6-a385-309ea0f1cf55", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1179", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error5", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.b05e3bee-734e-4167-9b85-05f73d545d90", "scriptTypeId": "2", "isActive": "true", "script": "if (tw.local.results[0].rows[0].data[0] == null) {\r\r\n\ttw.local.haveRunningRequest = false;\r\r\n\ttw.local.isvalid = true;\r\r\n\t\r\r\n}\r\r\nelse{\r\r\n\ttw.local.haveRunningRequest = true;\r\r\n\ttw.local.isvalid = false;\r\r\n\ttw.local.massege = \"this request number has Running Request\";\r\r\n\t\r\r\n}", "isRule": "false", "guid": "230e600b-e7fd-4169-9748-6ef019df0c97", "versionId": "c702172f-c060-4e29-a155-dfdb5b4f5842"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "check idc type", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.55bc5585-b15d-4c7f-9b12-c9031438532b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:45f", "versionId": "727bdb25-df18-4f74-ba8a-dd047b6cff6b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "59", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.55bc5585-b15d-4c7f-9b12-c9031438532b", "scriptTypeId": "2", "isActive": "true", "script": "if (tw.local.IDCRequest.IDCRequestType.englishdescription ==\"IDC Completion\" ) {\r\r\n\r\r\n\tif (tw.local.parentIDC.IDCRequestStage ==\"Final\") {\r\r\n\t\ttw.local.massege = \"can not make IDC Completion request for request its stage Final\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}\r\r\n\telse{\r\r\n\t\ttw.local.isvalid = true;\r\r\n\t}\r\r\n\t\r\r\n}else if (tw.local.IDCRequest.IDCRequestType.englishdescription ==\"IDC Payment\" ) {\r\r\n\r\r\n\tif (tw.local.parentIDC.IDCRequestStage != \"Final\") {\r\r\n\t\ttw.local.massege = \"can not make IDC Payment request for request its stage initial\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}\r\r\n\telse if(tw.local.parentIDC.paymentTerms.englishdescription == \"Sight\"){\r\r\n\t\ttw.local.massege = \"can not make IDC Payment request for request its payment Terms Sight\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}else if(tw.local.parentIDC.financialDetails.amtSight > 0 ){\r\r\n\t\ttw.local.massege = \"can not make IDC Payment request for request its sight amount more than 0\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}\r\r\n\telse{\r\r\n\t\ttw.local.isvalid = true;\r\r\n\t}\r\r\n}\r\r\nelse{\r\r\n\ttw.local.isvalid = true;\r\r\n}", "isRule": "false", "guid": "2c354606-cc3e-4791-9efd-b0ef3ec38b1a", "versionId": "2962045e-8976-4932-a4db-f5372e4238cb"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Exclusive Gateway", "tWComponentName": "Switch", "tWComponentId": "3013.ae759aeb-1b3e-43b7-8854-f030d4a7b11f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:466", "versionId": "94746ce8-75fb-487b-a4d4-502839e83406", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "161", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.ae759aeb-1b3e-43b7-8854-f030d4a7b11f", "guid": "31637303-56cb-496a-8907-68970f8abb55", "versionId": "84f94434-9193-4c51-8574-aba9845aaddb", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.f4871456-120d-4758-a266-e103f6cbd58c", "switchId": "3013.ae759aeb-1b3e-43b7-8854-f030d4a7b11f", "seq": "1", "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709e", "condition": "tw.local.isvalid\t  ==\t  false", "guid": "b74e6f87-937e-485c-971e-1c89b6c5091e", "versionId": "c61d957d-17bc-4f9a-99ab-d7774ea9d2e8"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.e363cb85-8a6a-4690-a741-9dfb5644a68c", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Init SQL", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.bdcd45c3-a548-427c-a22a-f92e0fb95d3e", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:46e", "versionId": "9e4d8bb1-1f04-4bf6-9158-36108d07faf0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "945", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error3", "locationId": "bottomRight", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.bdcd45c3-a548-427c-a22a-f92e0fb95d3e", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.sql = \"SELECT max(ID) FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER like ? AND REQUEST_STATUS != 'Completed' AND REQUEST_STATUS != 'Terminated' AND REQUEST_STATUS != 'Canceled'\";\r\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.parameters[0]  = new tw.object.SQLParameter();\r\r\ntw.local.parameters[0].value = tw.local.requestNumber+\"%\";", "isRule": "false", "guid": "5210f982-2e5d-4062-aed5-9755594493a1", "versionId": "2daf86ec-7646-458c-99ae-73d255ba99f7"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.11362e14-55ce-442b-9b59-474f682d1754", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:469", "versionId": "9fdaa578-1439-4bd5-bec5-f089f2bc2078", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1724", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.11362e14-55ce-442b-9b59-474f682d1754", "haltProcess": "false", "guid": "752905ce-a912-4423-b531-ab25848a9a4b", "versionId": "86a4b4fc-f9b9-4e48-bc97-fbfa18a292d2"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Parse IDC Request", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.bd2fd443-cc6c-43f5-a7b2-92f90c48f314", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:465", "versionId": "a615f1bb-0c50-4f29-bf05-82b9655de2ee", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "669", "y": "58", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.bd2fd443-cc6c-43f5-a7b2-92f90c48f314", "scriptTypeId": "2", "isActive": "true", "script": "\r\r\ntw.local.isTotalyPaid = false;\r\r\nif (tw.local.SCQueryBCContractRes.OutstandingAmount != \"\" && tw.local.SCQueryBCContractRes.OutstandingAmount!=null && tw.local.SCQueryBCContractRes.OutstandingAmount != undefined) {\r\r\n\ttw.local.idcContract.outstandingAmount = parseFloat(tw.local.SCQueryBCContractRes.OutstandingAmount);\r\r\n}\r\r\n\r\r\nif (tw.local.idcContract.outstandingAmount == 0) {\r\r\n\ttw.local.isvalid = false;\r\r\n\ttw.local.isTotalyPaid = true;\r\r\n\ttw.local.massege = \"this request is totally paid\";\r\r\n}else{\r\r\n\ttw.local.isvalid = true;\r\r\n\r\r\n \ttw.local.idcContract.IDCProduct.code = tw.local.SCQueryBCContractRes.ProductCode;\r\r\n \ttw.local.idcContract.IDCProduct.englishdescription = tw.local.SCQueryBCContractRes.ProductDesc;\r\r\n\t \r\r\n//\ttw.local.idcContract.IDCRequestStage = tw.local.SCQueryBCContractRes.Stag;\r\r\n\ttw.local.idcContract.userReference = tw.local.SCQueryBCContractRes.UseRefNum;\r\r\n\tif (tw.local.SCQueryBCContractRes.Amount != \"\" && tw.local.SCQueryBCContractRes.Amount != null && tw.local.SCQueryBCContractRes.Amount != undefined) {\r\r\n\t\ttw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);\r\r\n\t}\r\r\n\t\r\r\n//\tif (tw.local.SCQueryBCContractRes.Amount != \"\" && tw.local.SCQueryBCContractRes.Amount != null && tw.local.SCQueryBCContractRes.Amount != undefined) {\r\r\n//\t\ttw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);\r\r\n//\t}\r\r\n\r\r\n\ttw.local.idcContract.billCurrency.code = tw.local.SCQueryBCContractRes.Currency;\r\r\n\r\r\n\tif (tw.local.SCQueryBCContractRes.BaseDate != \"\" && tw.local.SCQueryBCContractRes.BaseDate != null && tw.local.SCQueryBCContractRes.BaseDate != undefined) {\r\r\n\t\ttw.local.idcContract.transactionBaseDate = new Date();\r\r\n\t\ttw.local.idcContract.transactionBaseDate.parse(tw.local.SCQueryBCContractRes.BaseDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\t\r\r\n\tif (tw.local.SCQueryBCContractRes.ValueDate != \"\" && tw.local.SCQueryBCContractRes.ValueDate != null && tw.local.SCQueryBCContractRes.ValueDate != undefined) {\r\r\n\t\ttw.local.idcContract.transactionValueDate = new Date();\r\r\n\t\ttw.local.idcContract.transactionValueDate.parse(tw.local.SCQueryBCContractRes.ValueDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\t\r\r\n\tif (tw.local.SCQueryBCContractRes.TenorDays != \"\" && tw.local.SCQueryBCContractRes.TenorDays != null && tw.local.SCQueryBCContractRes.TenorDays != undefined) {\r\r\n\t\ttw.local.idcContract.transactionTenorDays = parseInt(tw.local.SCQueryBCContractRes.TenorDays);\r\r\n\t}\r\r\n\r\r\n\tif (tw.local.SCQueryBCContractRes.TransitDays != \"\" && tw.local.SCQueryBCContractRes.TransitDays != null && tw.local.SCQueryBCContractRes.TransitDays != undefined) {\r\r\n\t\ttw.local.idcContract.transactionTransitDays = parseInt(tw.local.SCQueryBCContractRes.TransitDays);\r\r\n\t}\r\r\n\t\r\r\n\tif (tw.local.SCQueryBCContractRes.MaturityDate != \"\" && tw.local.SCQueryBCContractRes.MaturityDate != null && tw.local.SCQueryBCContractRes.MaturityDate != undefined) {\r\r\n\t\ttw.local.idcContract.transactionMaturityDate = new Date();\r\r\n\t\ttw.local.idcContract.transactionMaturityDate.parse(tw.local.SCQueryBCContractRes.MaturityDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\r\r\n\r\r\n\tfor (var i=0; i<tw.local.SCQueryBCContractRes.Contract_Parties.listLength; i++) {\r\r\n\t\ttw.local.idcContract.party[i] = new tw.object.Parties();\r\r\n\t\ttw.local.idcContract.party[i].partyCIF = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;\r\r\n\t\ttw.local.idcContract.party[i].partyId = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;\r\r\n\t\ttw.local.idcContract.party[i].partyType = new tw.object.NameValuePair();\r\r\n\t\ttw.local.idcContract.party[i].partyType.name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;\r\r\n\t\ttw.local.idcContract.party[i].partyType.value = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;\r\r\n\t\ttw.local.idcContract.party[i].name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyName;\r\r\n\t\ttw.local.idcContract.party[i].country = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyCountryCode;\r\r\n\t\ttw.local.idcContract.party[i].language = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyLang;\r\r\n\t\ttw.local.idcContract.party[i].reference = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyRefNum;\r\r\n\t\ttw.local.idcContract.party[i].address1 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress1;\r\r\n\t\ttw.local.idcContract.party[i].address2 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress2;\r\r\n\t\ttw.local.idcContract.party[i].address3 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress3;\r\r\n\t\ttw.local.idcContract.party[i].address4 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress4;\r\r\n\t\tif(tw.local.idcContract.party[i].partyType.name == \"REMITTING BANK\" && tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr!=null && tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0] !=null )\t\t \r\r\n\t\t{\r\r\n\t\t   tw.local.idcContract.party[i].media = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].MEDIA;\r\r\n\t\t   tw.local.idcContract.party[i].address = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].ADDR;\r\r\n\t\t}\r\r\n\t\tif(tw.local.idcContract.party[i].partyType.name == \"REMITTING BANK\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Remitting Bank\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Remitting Bank\";\r\r\n\t\t}\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"DRAWEE\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Drawee\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Drawee\";\r\r\n\t\t}\r\r\n\t\t\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"DRAWER\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Drawer\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Drawer\";\r\r\n\t\t}\r\r\n\t\t\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"ACCOUNTEE\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Accountee\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Accountee\";\r\r\n\t\t}\r\r\n\t\t\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"CASE IN NEED\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Case in Need\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Case in Need\";\r\r\n\t\t}\r\r\n\t}\r\r\n\t\r\r\n\tif(tw.local.SCQueryBCContractRes.FromDate != null && tw.local.SCQueryBCContractRes.FromDate != \"\" )\r\r\n\t{\r\r\n\t    tw.local.idcContract.interestFromDate = new Date();\r\r\n\t    tw.local.idcContract.interestFromDate.parse(tw.local.SCQueryBCContractRes.FromDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\tif(tw.local.SCQueryBCContractRes.Todate != null && tw.local.SCQueryBCContractRes.Todate != \"\" )\r\r\n\t{\r\r\n\t     tw.local.idcContract.interestToDate = new Date();\r\r\n\t     tw.local.idcContract.interestToDate.parse(tw.local.SCQueryBCContractRes.Todate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\t\r\r\n//\tif (tw.local.SCQueryBCContractRes.Collateral_details != null && tw.local.SCQueryBCContractRes.Collateral_details[0] !=null && tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral !=null) {\r\r\n//\t      \r\r\n//\t   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount != null && tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount !=\"\" )\r\r\n//\t      tw.local.idcContract.collateralAmount = parseFloat(tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount);\r\r\n//\t   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY != null && tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY !=\"\" )\r\r\n//\t      tw.local.idcContract.collateralCurrency.code = tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY;\r\r\n//\t\t\r\r\n//\t}\r\r\n\t\r\r\n\ttw.local.idcContract.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountBranchCode =\"\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountClass = \"Customer Account\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountCurrency =\"\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountNumber = new tw.object.NameValuePair();\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountNumber.name =\"\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountNumber.value = \"\"; \r\r\n\t\r\r\n\t\r\r\n\ttw.local.parentIDCContract = new tw.object.IDCContract();\r\r\n\ttw.local.parentIDCContract = tw.local.idcContract;\r\r\n}", "isRule": "false", "guid": "213e1bc9-44b9-4b44-b6e6-4728ed0a74de", "versionId": "e852b106-fa1f-4a41-94dd-22e32b8873f8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.668a7876-1d21-4b1f-b557-61caca4c591d", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Script Task", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.30ead00c-943e-4266-ad67-1aad972f8049", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:46a", "versionId": "ceca3c33-ed45-42d3-9371-6d746220e56c", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "245", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.30ead00c-943e-4266-ad67-1aad972f8049", "scriptTypeId": "2", "isActive": "true", "script": "tw.local.idcContract= new tw.object.IDCContract();\r\r\n\r\r\n//tw.local.idcContract.collateralAmount = 0;\r\r\n\r\r\ntw.local.idcContract.settlementAccounts = new tw.object.listOf.SettlementAccount();\r\r\ntw.local.idcContract.settlementAccounts[0] = new tw.object.SettlementAccount();\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = \"Customer Account\";\r\r\ntw.local.idcContract.settlementAccounts[0].accountNumberList = new tw.object.listOf.NameValuePair();\r\r\n\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.currency = new tw.object.NameValuePair();\r\r\n\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();\r\r\n\r\r\ntw.local.idcContract.swiftMessageData = {};\r\r\ntw.local.idcContract.swiftMessageData.intermediary = {};\r\r\ntw.local.idcContract.swiftMessageData.accountWithInstitution = {};\r\r\ntw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};\r\r\ntw.local.idcContract.swiftMessageData.receiverCorrespondent = {};\r\r\ntw.local.idcContract.swiftMessageData.detailsOfPayment = {};\r\r\n\r\r\ntw.local.idcContract.swiftMessageData.orderingInstitution = {};\r\r\n\r\r\ntw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};\r\r\ntw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};\r\r\ntw.local.idcContract.swiftMessageData.orderingCustomer = {};\r\r\ntw.local.idcContract.swiftMessageData.senderToReciever = {};\r\r\n \r\r\ntw.local.idcContract.IDCProduct= new tw.object.DBLookup();\r\r\n//tw.local.idcContract.IDCProduct.code = \"IAVC\";\r\r\n//tw.local.idcContract.IDCProduct.englishdescription = \"INCOMING AVALIZED BILLS UNDER COLLECTION\";\r\r\n//tw.local.idcContract.userReference = \"*********\";\r\r\n//tw.local.idcContract.billAmount = 100;\r\r\ntw.local.idcContract.billCurrency = new tw.object.DBLookup();\r\r\n//tw.local.idcContract.billCurrency.code = \"EGP\";\r\r\n//tw.local.idcContract.sourceReference = \"1111111111111111\";\r\r\n//tw.local.idcContract.isLimitsTrackingRequired = false;\r\r\ntw.local.idcContract.interestToDate = new TWDate();\r\r\ntw.local.idcContract.transactionMaturityDate = new TWDate();\r\r\ntw.local.idcContract.transactionBaseDate = new TWDate();\r\r\n//tw.local.idcContract.tradeFinanceApprovalNumber = \"12345\";\r\r\ntw.local.idcContract.FCContractNumber = tw.local.IDCRequest.FCContractNumber;\r\r\ntw.local.idcContract.collateralCurrency = new tw.object.DBLookup();\r\r\n//tw.local.idcContract.collateralCurrency.code = \"\";\r\r\n//tw.local.idcContract.interestRate = 44.0;\r\r\n//tw.local.idcContract.transactionTransitDays = 44;\r\r\n\r\r\ntw.local.idcContract.advices = new tw.object.listOf.ContractAdvice();\r\r\n//tw.local.idcContract.advices[0] = new tw.object.ContractAdvice();\r\r\n//tw.local.idcContract.advices[0].adviceCode = \"FCEWNKMC\";\r\r\n//tw.local.idcContract.advices[0].suppressed = true;\r\r\n//tw.local.idcContract.advices[0].advicelines = new tw.object.SwiftMessagePart();\r\r\n//tw.local.idcContract.advices[0].advicelines.line1 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line2 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line3 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line4 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line5 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line6 = \"test\";\r\r\n\r\r\n\r\r\ntw.local.idcContract.party = new tw.object.listOf.Parties();\r\r\ntw.local.idcContract.party[0] = new tw.object.Parties();\r\r\ntw.local.idcContract.party[0].partyType = {};\r\r\ntw.local.idcContract.party[0].partyType.name = \"Drawee\";\r\r\ntw.local.idcContract.party[0].partyType.value = \"Drawee\";\r\r\ntw.local.idcContract.party[0].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\ntw.local.idcContract.party[0].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\ntw.local.idcContract.party[0].name = tw.local.IDCRequest.customerInformation.customerName;\r\r\ntw.local.idcContract.party[0].country = \"EG\";\r\r\ntw.local.idcContract.party[0].reference = \"NO REF\";\r\r\ntw.local.idcContract.party[0].address1 = tw.local.IDCRequest.customerInformation.addressLine1;\r\r\ntw.local.idcContract.party[0].address2 =tw.local.IDCRequest.customerInformation.addressLine2;\r\r\ntw.local.idcContract.party[0].address3 = tw.local.IDCRequest.customerInformation.addressLine3;\r\r\ntw.local.idcContract.party[0].address4 = \"\";\r\r\ntw.local.idcContract.party[0].media = \"\";\r\r\n\r\r\ntw.local.idcContract.party[0].phone = \"\";\r\r\ntw.local.idcContract.party[0].fax = \"\";\r\r\ntw.local.idcContract.party[0].email = \"\";\r\r\ntw.local.idcContract.party[0].contactPersonName = \"\";\r\r\ntw.local.idcContract.party[0].mobile = \"\";\r\r\ntw.local.idcContract.party[0].branch = {}\r\r\ntw.local.idcContract.party[0].branch.name = \"\";\r\r\ntw.local.idcContract.party[0].branch.value = \"\";\r\r\ntw.local.idcContract.party[0].language = \"\";\r\r\n\r\r\ntw.local.idcContract.party[0].isNbeCustomer = true;\r\r\ntw.local.idcContract.party[1] = new tw.object.Parties();\r\r\ntw.local.idcContract.party[1].partyType = {};\r\r\ntw.local.idcContract.party[1].partyType.name = \"Drawer\";\r\r\ntw.local.idcContract.party[1].partyType.value = \"Drawer\";\r\r\n//tw.local.idcContract.party[1].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\n//tw.local.idcContract.party[1].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\n//tw.local.idcContract.party[1].name = tw.local.IDCRequest.customerInformation.customerName;\r\r\n//tw.local.idcContract.party[1].country = \"EG\";\r\r\n//tw.local.idcContract.party[1].reference = \"NO REF\";\r\r\n//tw.local.idcContract.party[1].address1 = tw.local.IDCRequest.customerInformation.addressLine1;\r\r\n//tw.local.idcContract.party[1].address2 =tw.local.IDCRequest.customerInformation.addressLine2;\r\r\n//tw.local.idcContract.party[1].address3 = \"\";\r\r\n//tw.local.idcContract.party[1].address4 = \"\";\r\r\n//tw.local.idcContract.party[1].media = \"\";\r\r\n\r\r\n//tw.local.idcContract.party[1].phone = \"\";\r\r\n//tw.local.idcContract.party[1].fax = \"\";\r\r\n//tw.local.idcContract.party[1].email = \"\";\r\r\n//tw.local.idcContract.party[1].contactPersonName = \"\";\r\r\n//tw.local.idcContract.party[1].mobile = \"\";\r\r\n//tw.local.idcContract.party[1].branch = {}\r\r\n//tw.local.idcContract.party[1].branch.name = \"\";\r\r\n//tw.local.idcContract.party[1].branch.value = \"\";\r\r\n//tw.local.idcContract.party[1].language = \"\";\r\r\n//tw.local.idcContract.party[1].isNbeCustomer = true;\r\r\n\r\r\n//tw.local.idcContract.IDCRequestStage = \"Finial\";\r\r\ntw.local.idcContract.transactionValueDate = new TWDate();\r\r\n//tw.local.idcContract.transactionTenorDays = 5;\r\r\n\r\r\ntw.local.idcContract.interestFromDate = new TWDate();\r\r\n//tw.local.idcContract.interestAmount = 100;\r\r\n//tw.local.idcContract.haveInterest = true;\r\r\ntw.local.idcContract.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.accountNumberList[0].name = \"12345\";\r\r\n//tw.local.idcContract.accountNumberList[0].value = \"12345\";\r\r\ntw.local.idcContract.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();\r\r\n//tw.local.idcContract.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();\r\r\n//tw.local.idcContract.facilities[0].facilityCode = \"MMNUFY\";\r\r\n//tw.local.idcContract.facilities[0].overallLimit = 11.0;\r\r\n//tw.local.idcContract.facilities[0].limitAmount = 110;\r\r\n//tw.local.idcContract.facilities[0].effectiveLimitAmount = 11.0;\r\r\n//tw.local.idcContract.facilities[0].availableAmount = 13.0;\r\r\n//tw.local.idcContract.facilities[0].expiryDate = new TWDate();\r\r\n//tw.local.idcContract.facilities[0].availableFlag = true;\r\r\n//tw.local.idcContract.facilities[0].authorizedFlag = true;\r\r\n//tw.local.idcContract.facilities[0].Utilization = 0.0;\r\r\n//tw.local.idcContract.facilities[0].returnCode = \"12345\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineCode = \"fdewa\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineAmount = 3452.0;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].availableAmount = 876.0;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].effectiveLineAmount = 234560.0;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].expiryDate = new TWDate();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.name = \"001\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.value = \"001\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].availableFlag = true;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].authorizedFlag = true;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityPercentageToBook = 100;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].internalRemarks = \"gwd\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].purpose = \"ekwsaa\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LCCommissionPercentage = \"112\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LCDef = \"eell\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LCCashCover = \"mcdms\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].IDCCommission = \"dkmc\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].IDCAvalCommPercentage = \"elm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].IDCCashCoverPercentage = \"csa\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].debitAccountNumber = \"dlm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineCurrency = \"EGP\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineSerialNumber = \"eele\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].returnCode = \"4343\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCommission = \"433\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_BidBond = \"434\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_Performance = \"434\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = \"1234\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"ewdew\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"ewd\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"kmk\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"dkkd,\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].name = \"ked\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].code = \"kee\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].source = \"iinkl\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].status = \"eoeo\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].name = \"mdmdm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].code = \"ieii\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].source = \"nsamls\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].status = \"wodmow,\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"wmd;wmq\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"lkwmdw\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"alwmda;lm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"aswdcnk\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"iuyt\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"lkjh\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].source = \",mnbv\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"ujmtgv\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].CIF = \"12345678\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].partyType.name = \"ddddmm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].partyType.value = \"ujm tg\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityID = \"ertyuio\";\r\r\n//tw.local.idcContract.facilities[0].status = \"rururur\";\r\r\n//tw.local.idcContract.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.facilities[0].facilityCurrency.name = \"rjrjrj\";\r\r\n//tw.local.idcContract.facilities[0].facilityCurrency.value = \"enene\";\r\r\n//tw.local.idcContract.facilities[0].facilityID = \"11111111\";\r\r\n//tw.local.idcContract.limitsTrackingVIs = \"DEFAULT\";\r\r\n//tw.local.idcContract.interestVisibility = \"DEFAULT\";\r\r\n//tw.local.idcContract.adviceVis = \"DEFAULT\";\r\r\n//tw.local.idcContract.outstandingAmount = 100.0;", "isRule": "false", "guid": "c991f97f-71da-4fb6-ada9-297effc7dcd8", "versionId": "cdb77e74-4ae3-4020-a8f8-def355dbfede"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Catch Errors", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.0187f9c3-bf28-41ae-a7e6-b3c354c869d3", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "versionId": "e1b7eb61-f334-4d06-a4ca-ab5b5567b5c0", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "1690", "y": "267", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.0187f9c3-bf28-41ae-a7e6-b3c354c869d3", "scriptTypeId": "2", "isActive": "true", "script": "if (tw.local.error != undefined && !!tw.local.error.errorText) {\r\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\r\n}else if (tw.system.error != undefined && tw.system.error != null) {\r\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}else{\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}\r\r\n\r\r\ntw.local.error.errorText = \"Error Occured<br> Service Name : \"+tw.system.serviceFlow.name+\"<br> Error Message : \"+tw.local.errorMSG;\r\r\n", "isRule": "false", "guid": "6310493e-2bac-4414-9c09-77185f37c8f4", "versionId": "b021ad52-ae08-4859-b57f-1586c9c1ec4f"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.83b89cca-8a42-4643-a747-1ac33270f64f", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "Get IDC Contract Facilities", "tWComponentName": "SubProcess", "tWComponentId": "3012.7539663d-c24b-42cf-b3b6-add6e6f675d4", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:46d", "versionId": "e3d1d202-2928-4e68-9815-0b0e2280ac0b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "779", "y": "60", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error9", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftTop", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.7539663d-c24b-42cf-b3b6-add6e6f675d4", "attachedProcessRef": "/1.42f20f4f-289f-432d-9bd8-3837be1dc61d", "guid": "3b90266e-cfa6-4dad-a396-d0d01285bdd3", "versionId": "eb609455-04a5-4000-bd28-43ac36669952", "parameterMapping": [{"name": "SCQueryBCContractRes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.256c0a40-3e73-4b9a-b4aa-79a7919727e7", "processParameterId": "2055.5ee29ebe-48c3-4dc5-8fda-5433c9d3151d", "parameterMappingParentId": "3012.7539663d-c24b-42cf-b3b6-add6e6f675d4", "useDefault": "false", "value": "tw.local.SCQueryBCContractRes", "classRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.*************-4cbb-a781-44d233d577c6", "isList": "false", "isInput": "true", "guid": "18f91d3e-79db-4f7f-b8a2-17173b2c6815", "versionId": "364221c3-ca41-48a4-b78c-454fe0b3d161", "description": {"isNull": "true"}}, {"name": "idcContract", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.7ecb4a57-647e-4819-9ff7-f4d3053ce8c2", "processParameterId": "2055.e36b1711-d92f-4f79-8134-45f6ebe54fe7", "parameterMappingParentId": "3012.7539663d-c24b-42cf-b3b6-add6e6f675d4", "useDefault": "false", "value": "tw.local.idcContract", "classRef": "/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "isList": "false", "isInput": "true", "guid": "404af59e-de91-47d8-b5fa-8fd578b45a96", "versionId": "36533d2d-4518-47ae-a8c0-213e30a3f620", "description": {"isNull": "true"}}, {"name": "idcContract", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.a3ca88a0-5d18-4ee5-8b24-857f81a9bb07", "processParameterId": "2055.10751aed-d9b5-4b76-804f-3a33b2a7eb76", "parameterMappingParentId": "3012.7539663d-c24b-42cf-b3b6-add6e6f675d4", "useDefault": "false", "value": "tw.local.idcContract", "classRef": "/12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "isList": "false", "isInput": "false", "guid": "01b7cb3f-fbc7-48f8-8283-893dad259dcc", "versionId": "d9a36bc9-96bc-4a6b-a92c-8079735fef08", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.b493bed2-487f-4a47-abb3-d51787b640ad", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "SQL Execute Statement (SQLResult)", "tWComponentName": "SubProcess", "tWComponentId": "3012.5b45e4b7-705e-4862-8053-8302de42427c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:467", "versionId": "ec05dfa1-cfe0-48c0-963d-504ffadb4eda", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "1060", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error4", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:056f27db08707381:36b20ea0:18b6c1db471:463", "errorHandlerItemId": "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "errorHandlerLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.5b45e4b7-705e-4862-8053-8302de42427c", "attachedProcessRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "guid": "aef03b02-3781-4cd8-8d17-af8aa49beadd", "versionId": "a165a8e9-749b-4bea-bd35-b06b35383ade", "parameterMapping": [{"name": "parameters", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.349cc1e5-fb6b-4228-a37e-e8a047653171", "processParameterId": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "parameterMappingParentId": "3012.5b45e4b7-705e-4862-8053-8302de42427c", "useDefault": "false", "value": "tw.local.parameters", "classRef": "/12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isList": "true", "isInput": "true", "guid": "a22b0e80-2488-44c5-a9bb-5f7242402ddd", "versionId": "301050e4-2c3b-42df-9a18-0127db2375f4", "description": {"isNull": "true"}}, {"name": "sql", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5451706f-294e-44db-a324-df7ed0ceb918", "processParameterId": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "parameterMappingParentId": "3012.5b45e4b7-705e-4862-8053-8302de42427c", "useDefault": "false", "value": "tw.local.sql", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "6c895a9f-a1c0-4cbe-953d-c8899bc6bd01", "versionId": "99c1e5ad-**************-da4faff50e78", "description": {"isNull": "true"}}, {"name": "results", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.52c10f11-6ebb-44a6-bf82-2670731c0caf", "processParameterId": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "parameterMappingParentId": "3012.5b45e4b7-705e-4862-8053-8302de42427c", "useDefault": "false", "value": "tw.local.results", "classRef": "/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isList": "true", "isInput": "false", "guid": "a1885bd2-afa0-4791-8b48-eeffff68fbeb", "versionId": "9e1c7e67-f064-4c2e-930a-86aef92e51a3", "description": {"isNull": "true"}}, {"name": "dataSourceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.bf2fdc9d-b624-4250-a37f-fe1cd646e42b", "processParameterId": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "parameterMappingParentId": "3012.5b45e4b7-705e-4862-8053-8302de42427c", "useDefault": "false", "value": "tw.env.DATASOURCE", "classRef": "/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "cd8dcfb1-5b5b-4e10-9809-333ba97feb89", "versionId": "b761b87a-0fe6-4c79-805d-a104201d5458", "description": {"isNull": "true"}}, {"name": "maxRows", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.32a47ce2-ec5b-417e-8edf-13ea248717bf", "processParameterId": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "parameterMappingParentId": "3012.5b45e4b7-705e-4862-8053-8302de42427c", "useDefault": "false", "value": "-1", "classRef": "/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isList": "false", "isInput": "true", "guid": "84c91326-2205-4a05-976d-c695aa4e6d37", "versionId": "d8457a08-86fc-4bb8-9692-7b291a203c1a", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a054c73c-8d25-4717-b21d-e82248d07389", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "name": "isSuccess?", "tWComponentName": "Switch", "tWComponentId": "3013.52021517-60b7-42af-9ca5-9932c15def1f", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:056f27db08707381:36b20ea0:18b6c1db471:46b", "versionId": "fee674f7-fd27-4ea3-b662-e865ca78e43f", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "511", "y": "77", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.52021517-60b7-42af-9ca5-9932c15def1f", "guid": "d07f0568-d8cc-480e-8cc4-e5c189757166", "versionId": "c9c81ac0-90d7-4377-9440-4e746ecc192a", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.7c46ab86-5230-4e18-b528-c68bd49d9b6f", "switchId": "3013.52021517-60b7-42af-9ca5-9932c15def1f", "seq": "1", "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709d", "condition": "tw.local.isSuccessful\t  ==\t  false", "guid": "2990acbc-3882-419e-a2bb-cb86a47777a7", "versionId": "f236f6a4-2dfb-4b45-8ad9-035bfba4f88a"}}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.0c9cea64-512a-42fa-83d6-c989ea8cc75f", "epvId": "/21.0bb89e09-258a-4bd1-b3a7-137a3219209f", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "guid": "d4c947a5-ae29-469d-b693-924fdead9925", "versionId": "5d81f3bf-7fb9-44ef-94de-ead1656aa346"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "5", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Check Parent Request", "id": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:isSecured": "true", "ns3:isAjaxExposed": "true", "ns3:sboSyncEnabled": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.0bb89e09-258a-4bd1-b3a7-137a3219209f", "epvProcessLinkId": "0e8c06c1-98e7-4683-823d-ff68ddde78ce"}}}, "ns16:dataInput": [{"name": "requestNumber", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.52711b62-7df7-40b3-aa2d-0338fa7284dd", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"**************\"", "useDefault": "true"}}}, {"name": "idcContract", "itemSubjectRef": "itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "isCollection": "false", "id": "2055.82d72677-767a-4f2c-bc85-8e8dcdebf086", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.IDCContract();\r\nautoObject.collateralAmount = 0.0;\r\nautoObject.userReference = \"\";\r\nautoObject.settlementAccounts = new tw.object.listOf.SettlementAccount();\r\nautoObject.settlementAccounts[0] = new tw.object.SettlementAccount();\r\nautoObject.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();\r\nautoObject.settlementAccounts[0].debitedAccount.balanceSign = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.GLAccountNumber = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.id = 0;\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.code = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.arabicdescription = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountCurrency.englishdescription = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountBranchCode = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountBalance = 0.0;\r\nautoObject.settlementAccounts[0].debitedAccount.accountNumber = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.accountClass = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.ownerAccounts = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.settlementAccounts[0].debitedAccount.currency.name = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.currency.value = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.isGLFoundC = false;\r\nautoObject.settlementAccounts[0].debitedAccount.commCIF = \"\";\r\nautoObject.settlementAccounts[0].debitedAccount.isGLVerifiedC = false;\r\nautoObject.settlementAccounts[0].debitedAccount.isOverDraft = false;\r\nautoObject.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();\r\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.debitPercentage = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.standardExchangeRate = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\r\nautoObject.settlementAccounts[0].debitedAmount.negotiatedExchangeRate = 0.0;\r\nautoObject.settlementAccounts[0].accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.settlementAccounts[0].accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.settlementAccounts[0].accountNumberList[0].name = \"\";\r\nautoObject.settlementAccounts[0].accountNumberList[0].value = \"\";\r\nautoObject.settlementAccounts[0].settCIF = \"\";\r\nautoObject.billAmount = 0.0;\r\nautoObject.billCurrency = new tw.object.DBLookup();\r\nautoObject.billCurrency.id = 0;\r\nautoObject.billCurrency.code = \"\";\r\nautoObject.billCurrency.arabicdescription = \"\";\r\nautoObject.billCurrency.englishdescription = \"\";\r\nautoObject.party = new tw.object.listOf.Parties();\r\nautoObject.party[0] = new tw.object.Parties();\r\nautoObject.party[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.party[0].partyType.name = \"\";\r\nautoObject.party[0].partyType.value = \"\";\r\nautoObject.party[0].partyId = \"\";\r\nautoObject.party[0].name = \"\";\r\nautoObject.party[0].country = \"\";\r\nautoObject.party[0].reference = \"\";\r\nautoObject.party[0].address1 = \"\";\r\nautoObject.party[0].address2 = \"\";\r\nautoObject.party[0].address3 = \"\";\r\nautoObject.party[0].address4 = \"\";\r\nautoObject.party[0].media = \"\";\r\nautoObject.party[0].address = \"\";\r\nautoObject.party[0].phone = \"\";\r\nautoObject.party[0].fax = \"\";\r\nautoObject.party[0].email = \"\";\r\nautoObject.party[0].contactPersonName = \"\";\r\nautoObject.party[0].mobile = \"\";\r\nautoObject.party[0].branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.party[0].branch.name = \"\";\r\nautoObject.party[0].branch.value = \"\";\r\nautoObject.party[0].language = \"\";\r\nautoObject.party[0].partyCIF = \"\";\r\nautoObject.party[0].isNbeCustomer = false;\r\nautoObject.party[0].isRetrived = false;\r\nautoObject.party[0].cifVis = \"\";\r\nautoObject.sourceReference = \"\";\r\nautoObject.isLimitsTrackingRequired = false;\r\nautoObject.liquidationSummary = new tw.object.LiquidationSummary();\r\nautoObject.liquidationSummary.liquidationCurrency = \"\";\r\nautoObject.liquidationSummary.debitBasisby = \"\";\r\nautoObject.liquidationSummary.liquidationAmt = 0.0;\r\nautoObject.liquidationSummary.debitValueDate = new TWDate();\r\nautoObject.liquidationSummary.creditValueDate = new TWDate();\r\nautoObject.IDCProduct = new tw.object.DBLookup();\r\nautoObject.IDCProduct.id = 0;\r\nautoObject.IDCProduct.code = \"\";\r\nautoObject.IDCProduct.arabicdescription = \"\";\r\nautoObject.IDCProduct.englishdescription = \"\";\r\nautoObject.interestToDate = new TWDate();\r\nautoObject.transactionMaturityDate = new TWDate();\r\nautoObject.commissionsAndCharges = new tw.object.listOf.CommissionsAndChargesDetails();\r\nautoObject.commissionsAndCharges[0] = new tw.object.CommissionsAndChargesDetails();\r\nautoObject.commissionsAndCharges[0].component = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount = new tw.object.DebitedAccount();\r\nautoObject.commissionsAndCharges[0].debitedAccount.balanceSign = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.GLAccountNumber = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.id = 0;\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.code = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.arabicdescription = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountCurrency.englishdescription = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountBranchCode = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountBalance = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountNumber = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.accountClass = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.ownerAccounts = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.commissionsAndCharges[0].debitedAccount.currency.name = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.currency.value = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.isGLFoundC = false;\r\nautoObject.commissionsAndCharges[0].debitedAccount.commCIF = \"\";\r\nautoObject.commissionsAndCharges[0].debitedAccount.isGLVerifiedC = false;\r\nautoObject.commissionsAndCharges[0].debitedAccount.isOverDraft = false;\r\nautoObject.commissionsAndCharges[0].chargeAmount = 0.0;\r\nautoObject.commissionsAndCharges[0].waiver = false;\r\nautoObject.commissionsAndCharges[0].debitedAmount = new tw.object.DebitedAmount();\r\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinAccountCurrency = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.debitPercentage = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.standardExchangeRate = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.debitedAmtinLiquidationCurrency = 0.0;\r\nautoObject.commissionsAndCharges[0].debitedAmount.negotiatedExchangeRate = 0.0;\r\nautoObject.commissionsAndCharges[0].defaultCurrency = new tw.object.DBLookup();\r\nautoObject.commissionsAndCharges[0].defaultCurrency.id = 0;\r\nautoObject.commissionsAndCharges[0].defaultCurrency.code = \"\";\r\nautoObject.commissionsAndCharges[0].defaultCurrency.arabicdescription = \"\";\r\nautoObject.commissionsAndCharges[0].defaultCurrency.englishdescription = \"\";\r\nautoObject.commissionsAndCharges[0].defaultAmount = 0.0;\r\nautoObject.commissionsAndCharges[0].commAccountList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.commissionsAndCharges[0].commAccountList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.commissionsAndCharges[0].commAccountList[0].name = \"\";\r\nautoObject.commissionsAndCharges[0].commAccountList[0].value = \"\";\r\nautoObject.commissionsAndCharges[0].defaultPercentage = 0.0;\r\nautoObject.commissionsAndCharges[0].changePercentage = 0.0;\r\nautoObject.commissionsAndCharges[0].description = \"\";\r\nautoObject.commissionsAndCharges[0].rateType = \"\";\r\nautoObject.commissionsAndCharges[0].percentageVis = \"\";\r\nautoObject.commissionsAndCharges[0].changeAmountVis = \"\";\r\nautoObject.transactionBaseDate = new TWDate();\r\nautoObject.tradeFinanceApprovalNumber = \"\";\r\nautoObject.FCContractNumber = \"\";\r\nautoObject.collateralCurrency = new tw.object.DBLookup();\r\nautoObject.collateralCurrency.id = 0;\r\nautoObject.collateralCurrency.code = \"\";\r\nautoObject.collateralCurrency.arabicdescription = \"\";\r\nautoObject.collateralCurrency.englishdescription = \"\";\r\nautoObject.interestRate = 0.0;\r\nautoObject.transactionTransitDays = 0;\r\nautoObject.swiftMessageData = new tw.object.SwiftMessageData();\r\nautoObject.swiftMessageData.intermediary = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.intermediary.line1 = \"\";\r\nautoObject.swiftMessageData.intermediary.line2 = \"\";\r\nautoObject.swiftMessageData.intermediary.line3 = \"\";\r\nautoObject.swiftMessageData.intermediary.line4 = \"\";\r\nautoObject.swiftMessageData.intermediary.line5 = \"\";\r\nautoObject.swiftMessageData.intermediary.line6 = \"\";\r\nautoObject.swiftMessageData.detailsOfCharge = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.accountWithInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.accountWithInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.receiver = \"\";\r\nautoObject.swiftMessageData.swiftMessageOption = \"\";\r\nautoObject.swiftMessageData.coverRequired = \"\";\r\nautoObject.swiftMessageData.transferType = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.intermediaryReimbursementInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.receiverCorrespondent.line1 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line2 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line3 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line4 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line5 = \"\";\r\nautoObject.swiftMessageData.receiverCorrespondent.line6 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.detailsOfPayment.line1 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line2 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line3 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line4 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line5 = \"\";\r\nautoObject.swiftMessageData.detailsOfPayment.line6 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.orderingInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.orderingInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.beneficiaryInstitution.line1 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line2 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line3 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line4 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line5 = \"\";\r\nautoObject.swiftMessageData.beneficiaryInstitution.line6 = \"\";\r\nautoObject.swiftMessageData.receiverOfCover = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.ultimateBeneficiary.line1 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line2 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line3 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line4 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line5 = \"\";\r\nautoObject.swiftMessageData.ultimateBeneficiary.line6 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.orderingCustomer.line1 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line2 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line3 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line4 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line5 = \"\";\r\nautoObject.swiftMessageData.orderingCustomer.line6 = \"\";\r\nautoObject.swiftMessageData.senderToReciever = new tw.object.SwiftMessagePart();\r\nautoObject.swiftMessageData.senderToReciever.line1 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line2 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line3 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line4 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line5 = \"\";\r\nautoObject.swiftMessageData.senderToReciever.line6 = \"\";\r\nautoObject.swiftMessageData.RTGS = \"\";\r\nautoObject.swiftMessageData.RTGSNetworkType = \"\";\r\nautoObject.advices = new tw.object.listOf.ContractAdvice();\r\nautoObject.advices[0] = new tw.object.ContractAdvice();\r\nautoObject.advices[0].adviceCode = \"\";\r\nautoObject.advices[0].suppressed = false;\r\nautoObject.advices[0].advicelines = new tw.object.SwiftMessagePart();\r\nautoObject.advices[0].advicelines.line1 = \"\";\r\nautoObject.advices[0].advicelines.line2 = \"\";\r\nautoObject.advices[0].advicelines.line3 = \"\";\r\nautoObject.advices[0].advicelines.line4 = \"\";\r\nautoObject.advices[0].advicelines.line5 = \"\";\r\nautoObject.advices[0].advicelines.line6 = \"\";\r\nautoObject.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();\r\nautoObject.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();\r\nautoObject.cashCollateralAccounts[0].accountCurrency = \"\";\r\nautoObject.cashCollateralAccounts[0].accountClass = \"\";\r\nautoObject.cashCollateralAccounts[0].debitedAmtinCollateralCurrency = 0.0;\r\nautoObject.cashCollateralAccounts[0].GLAccountNumber = \"\";\r\nautoObject.cashCollateralAccounts[0].accountBranchCode = \"\";\r\nautoObject.cashCollateralAccounts[0].accountNumber = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.cashCollateralAccounts[0].accountNumber.name = \"\";\r\nautoObject.cashCollateralAccounts[0].accountNumber.value = \"\";\r\nautoObject.cashCollateralAccounts[0].isGLFound = false;\r\nautoObject.cashCollateralAccounts[0].isGLVerified = false;\r\nautoObject.IDCRequestStage = \"\";\r\nautoObject.transactionValueDate = new TWDate();\r\nautoObject.transactionTenorDays = 0;\r\nautoObject.contractLimitsTracking = new tw.object.listOf.ContractLimitTracking();\r\nautoObject.contractLimitsTracking[0] = new tw.object.ContractLimitTracking();\r\nautoObject.contractLimitsTracking[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.contractLimitsTracking[0].partyType.name = \"\";\r\nautoObject.contractLimitsTracking[0].partyType.value = \"\";\r\nautoObject.contractLimitsTracking[0].type = \"\";\r\nautoObject.contractLimitsTracking[0].jointVentureParent = \"\";\r\nautoObject.contractLimitsTracking[0].customerNo = \"\";\r\nautoObject.contractLimitsTracking[0].linkageRefNum = \"\";\r\nautoObject.contractLimitsTracking[0].amountTag = \"\";\r\nautoObject.contractLimitsTracking[0].contributionPercentage = 0.0;\r\nautoObject.contractLimitsTracking[0].isCIFfound = false;\r\nautoObject.interestFromDate = new TWDate();\r\nautoObject.interestAmount = 0.0;\r\nautoObject.haveInterest = false;\r\nautoObject.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\nautoObject.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.accountNumberList[0].name = \"\";\r\nautoObject.accountNumberList[0].value = \"\";\r\nautoObject.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();\r\nautoObject.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();\r\nautoObject.facilities[0].facilityCode = \"\";\r\nautoObject.facilities[0].overallLimit = 0.0;\r\nautoObject.facilities[0].limitAmount = 0.0;\r\nautoObject.facilities[0].effectiveLimitAmount = 0.0;\r\nautoObject.facilities[0].availableAmount = 0.0;\r\nautoObject.facilities[0].expiryDate = new TWDate();\r\nautoObject.facilities[0].availableFlag = false;\r\nautoObject.facilities[0].authorizedFlag = false;\r\nautoObject.facilities[0].Utilization = 0.0;\r\nautoObject.facilities[0].returnCode = \"\";\r\nautoObject.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();\r\nautoObject.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();\r\nautoObject.facilities[0].facilityLines[0].lineCode = \"\";\r\nautoObject.facilities[0].facilityLines[0].lineAmount = 0.0;\r\nautoObject.facilities[0].facilityLines[0].availableAmount = 0.0;\r\nautoObject.facilities[0].facilityLines[0].effectiveLineAmount = 0.0;\r\nautoObject.facilities[0].facilityLines[0].expiryDate = new TWDate();\r\nautoObject.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.facilities[0].facilityLines[0].facilityBranch.name = \"\";\r\nautoObject.facilities[0].facilityLines[0].facilityBranch.value = \"\";\r\nautoObject.facilities[0].facilityLines[0].availableFlag = false;\r\nautoObject.facilities[0].facilityLines[0].authorizedFlag = false;\r\nautoObject.facilities[0].facilityLines[0].facilityPercentageToBook = 0;\r\nautoObject.facilities[0].facilityLines[0].internalRemarks = \"\";\r\nautoObject.facilities[0].facilityLines[0].purpose = \"\";\r\nautoObject.facilities[0].facilityLines[0].LCCommissionPercentage = \"\";\r\nautoObject.facilities[0].facilityLines[0].LCDef = \"\";\r\nautoObject.facilities[0].facilityLines[0].LCCashCover = \"\";\r\nautoObject.facilities[0].facilityLines[0].IDCCommission = \"\";\r\nautoObject.facilities[0].facilityLines[0].IDCAvalCommPercentage = \"\";\r\nautoObject.facilities[0].facilityLines[0].IDCCashCoverPercentage = \"\";\r\nautoObject.facilities[0].facilityLines[0].debitAccountNumber = \"\";\r\nautoObject.facilities[0].facilityLines[0].lineCurrency = \"\";\r\nautoObject.facilities[0].facilityLines[0].lineSerialNumber = \"\";\r\nautoObject.facilities[0].facilityLines[0].returnCode = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCommission = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCashCover_BidBond = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCashCover_Performance = \"\";\r\nautoObject.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedBranches[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedProducts[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].source = \"\";\r\nautoObject.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"\";\r\nautoObject.facilities[0].facilityLines[0].CIF = \"\";\r\nautoObject.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.facilities[0].facilityLines[0].partyType.name = \"\";\r\nautoObject.facilities[0].facilityLines[0].partyType.value = \"\";\r\nautoObject.facilities[0].facilityLines[0].facilityID = \"\";\r\nautoObject.facilities[0].status = \"\";\r\nautoObject.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.facilities[0].facilityCurrency.name = \"\";\r\nautoObject.facilities[0].facilityCurrency.value = \"\";\r\nautoObject.facilities[0].facilityID = \"\";\r\nautoObject.limitsTrackingVIs = \"\";\r\nautoObject.interestVisibility = \"\";\r\nautoObject.adviceVis = \"\";\r\nautoObject.outstandingAmount = 0.0;\r\nautoObject", "useDefault": "true"}}}, {"name": "IDCRequest", "itemSubjectRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isCollection": "false", "id": "2055.88e74584-565a-40c2-a722-ade695030bbd", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.IDCRequest();\r\nautoObject.IDCRequestState = \"\";\r\nautoObject.commodityDescription = \"\";\r\nautoObject.countryOfOrigin = new tw.object.DBLookup();\r\nautoObject.countryOfOrigin.id = 0;\r\nautoObject.countryOfOrigin.code = \"\";\r\nautoObject.countryOfOrigin.arabicdescription = \"\";\r\nautoObject.countryOfOrigin.englishdescription = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"\";\r\nautoObject.productsDetails = new tw.object.ProductsDetails();\r\nautoObject.productsDetails.destinationPort = \"\";\r\nautoObject.productsDetails.shippingDate = new TWDate();\r\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\r\nautoObject.productsDetails.HSProduct.id = 0;\r\nautoObject.productsDetails.HSProduct.code = \"\";\r\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\r\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\r\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\r\nautoObject.productsDetails.incoterms.id = 0;\r\nautoObject.productsDetails.incoterms.code = \"\";\r\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\r\nautoObject.productsDetails.incoterms.englishdescription = \"\";\r\nautoObject.productsDetails.ACID = \"\";\r\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\nautoObject.productsDetails.CBECommodityClassification.id = 0;\r\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\nautoObject.productsDetails.shipmentMethod.id = 0;\r\nautoObject.productsDetails.shipmentMethod.code = \"\";\r\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\r\nautoObject.financialDetails = new tw.object.FinancialDetails();\r\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\r\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\r\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\r\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\r\nautoObject.financialDetails.discountAmt = 0.0;\r\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\r\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\r\nautoObject.financialDetails.amtSight = 0.0;\r\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\r\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\r\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\r\nautoObject.financialDetails.amtPayableByNBE = 0.0;\r\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\r\nautoObject.financialDetails.executionHub.id = 0;\r\nautoObject.financialDetails.executionHub.code = \"\";\r\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\r\nautoObject.financialDetails.executionHub.englishdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\r\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.documentCurrency.id = 0;\r\nautoObject.financialDetails.documentCurrency.code = \"\";\r\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.daysTillMaturity = 0;\r\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\r\nautoObject.financialDetails.amountAdvanced = 0.0;\r\nautoObject.financialDetails.paymentAccount = \"\";\r\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\r\nautoObject.financialDetails.documentAmount = 0.0;\r\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfFunds.id = 0;\r\nautoObject.financialDetails.sourceOfFunds.code = \"\";\r\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\r\nautoObject.financialDetails.chargesAccount = \"\";\r\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\r\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\r\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\r\nautoObject.IDCRequestType = new tw.object.DBLookup();\r\nautoObject.IDCRequestType.id = 0;\r\nautoObject.IDCRequestType.code = \"\";\r\nautoObject.IDCRequestType.arabicdescription = \"\";\r\nautoObject.IDCRequestType.englishdescription = \"\";\r\nautoObject.isIDCWithdrawn = false;\r\nautoObject.IDCRequestStage = \"\";\r\nautoObject.FCContractNumber = \"1234\";\r\nautoObject.billOfLading = new tw.object.listOf.Invoice();\r\nautoObject.billOfLading[0] = new tw.object.Invoice();\r\nautoObject.billOfLading[0].date = new TWDate();\r\nautoObject.billOfLading[0].number = \"\";\r\nautoObject.importPurpose = new tw.object.DBLookup();\r\nautoObject.importPurpose.id = 0;\r\nautoObject.importPurpose.code = \"\";\r\nautoObject.importPurpose.arabicdescription = \"\";\r\nautoObject.importPurpose.englishdescription = \"\";\r\nautoObject.IDCRequestNature = new tw.object.DBLookup();\r\nautoObject.IDCRequestNature.id = 0;\r\nautoObject.IDCRequestNature.code = \"\";\r\nautoObject.IDCRequestNature.arabicdescription = \"\";\r\nautoObject.IDCRequestNature.englishdescription = \"\";\r\nautoObject.customerInformation = new tw.object.CustomerInformation();\r\nautoObject.customerInformation.CIFNumber = \"\";\r\nautoObject.customerInformation.importCardNumber = \"\";\r\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\r\nautoObject.customerInformation.customerName = \"\";\r\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\r\nautoObject.customerInformation.CBENumber = \"\";\r\nautoObject.customerInformation.customerSector = \"\";\r\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\r\nautoObject.customerInformation.facilityType.id = 0;\r\nautoObject.customerInformation.facilityType.code = \"\";\r\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\r\nautoObject.customerInformation.facilityType.englishdescription = \"\";\r\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\r\nautoObject.customerInformation.taxCardNumber = \"\";\r\nautoObject.customerInformation.customerType = \"\";\r\nautoObject.customerInformation.addressLine1 = \"\";\r\nautoObject.customerInformation.addressLine2 = \"\";\r\nautoObject.invoices = new tw.object.listOf.Invoice();\r\nautoObject.invoices[0] = new tw.object.Invoice();\r\nautoObject.invoices[0].date = new TWDate();\r\nautoObject.invoices[0].number = \"\";\r\nautoObject.productCategory = new tw.object.DBLookup();\r\nautoObject.productCategory.id = 0;\r\nautoObject.productCategory.code = \"\";\r\nautoObject.productCategory.arabicdescription = \"\";\r\nautoObject.productCategory.englishdescription = \"\";\r\nautoObject.documentsSource = new tw.object.DBLookup();\r\nautoObject.documentsSource.id = 0;\r\nautoObject.documentsSource.code = \"\";\r\nautoObject.documentsSource.arabicdescription = \"\";\r\nautoObject.documentsSource.englishdescription = \"\";\r\nautoObject.ParentIDCRequestNumber = \"00102230001070\";\r\nautoObject.paymentTerms = new tw.object.DBLookup();\r\nautoObject.paymentTerms.id = 0;\r\nautoObject.paymentTerms.code = \"\";\r\nautoObject.paymentTerms.arabicdescription = \"\";\r\nautoObject.paymentTerms.englishdescription = \"\";\r\nautoObject.approvals = new tw.object.Approvals();\r\nautoObject.approvals.CAD = false;\r\nautoObject.approvals.treasury = false;\r\nautoObject.approvals.compliance = false;\r\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0].startTime = new TWDate();\r\nautoObject.appLog[0].endTime = new TWDate();\r\nautoObject.appLog[0].userName = \"\";\r\nautoObject.appLog[0].role = \"\";\r\nautoObject.appLog[0].step = \"\";\r\nautoObject.appLog[0].action = \"\";\r\nautoObject.appLog[0].comment = \"\";\r\nautoObject.appLog[0].terminateReason = \"\";\r\nautoObject.appLog[0].returnReason = \"\";\r\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.DBID = 0;\r\n\r\nautoObject", "useDefault": "true"}}}, {"name": "parentIDC", "itemSubjectRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isCollection": "false", "id": "2055.455bc6b1-7ba6-4720-94d5-a300a02c504f", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.IDCRequest();\r\nautoObject.IDCRequestState = \"\";\r\nautoObject.commodityDescription = \"\";\r\nautoObject.countryOfOrigin = new tw.object.DBLookup();\r\nautoObject.countryOfOrigin.id = 0;\r\nautoObject.countryOfOrigin.code = \"\";\r\nautoObject.countryOfOrigin.arabicdescription = \"\";\r\nautoObject.countryOfOrigin.englishdescription = \"\";\r\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\r\nautoObject.appInfo.requestDate = \"\";\r\nautoObject.appInfo.status = \"\";\r\nautoObject.appInfo.subStatus = \"\";\r\nautoObject.appInfo.initiator = \"\";\r\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject.appInfo.branch.name = \"\";\r\nautoObject.appInfo.branch.value = \"\";\r\nautoObject.appInfo.requestName = \"\";\r\nautoObject.appInfo.requestType = \"\";\r\nautoObject.appInfo.stepName = \"\";\r\nautoObject.appInfo.appRef = \"\";\r\nautoObject.appInfo.appID = \"\";\r\nautoObject.appInfo.instanceID = \"00102230001070\";\r\nautoObject.productsDetails = new tw.object.ProductsDetails();\r\nautoObject.productsDetails.destinationPort = \"\";\r\nautoObject.productsDetails.shippingDate = new TWDate();\r\nautoObject.productsDetails.HSProduct = new tw.object.DBLookup();\r\nautoObject.productsDetails.HSProduct.id = 0;\r\nautoObject.productsDetails.HSProduct.code = \"\";\r\nautoObject.productsDetails.HSProduct.arabicdescription = \"\";\r\nautoObject.productsDetails.HSProduct.englishdescription = \"\";\r\nautoObject.productsDetails.incoterms = new tw.object.DBLookup();\r\nautoObject.productsDetails.incoterms.id = 0;\r\nautoObject.productsDetails.incoterms.code = \"\";\r\nautoObject.productsDetails.incoterms.arabicdescription = \"\";\r\nautoObject.productsDetails.incoterms.englishdescription = \"\";\r\nautoObject.productsDetails.ACID = \"\";\r\nautoObject.productsDetails.CBECommodityClassification = new tw.object.DBLookup();\r\nautoObject.productsDetails.CBECommodityClassification.id = 0;\r\nautoObject.productsDetails.CBECommodityClassification.code = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.arabicdescription = \"\";\r\nautoObject.productsDetails.CBECommodityClassification.englishdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod = new tw.object.DBLookup();\r\nautoObject.productsDetails.shipmentMethod.id = 0;\r\nautoObject.productsDetails.shipmentMethod.code = \"\";\r\nautoObject.productsDetails.shipmentMethod.arabicdescription = \"\";\r\nautoObject.productsDetails.shipmentMethod.englishdescription = \"\";\r\nautoObject.financialDetails = new tw.object.FinancialDetails();\r\nautoObject.financialDetails.isAdvancePaymentsUsed = false;\r\nautoObject.financialDetails.paymentTerms = new tw.object.listOf.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0] = new tw.object.PaymentTerm();\r\nautoObject.financialDetails.paymentTerms[0].installmentAmount = 0.0;\r\nautoObject.financialDetails.paymentTerms[0].installmentDate = new TWDate();\r\nautoObject.financialDetails.usedAdvancePayment = new tw.object.listOf.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0] = new tw.object.UsedAdvancePayment();\r\nautoObject.financialDetails.usedAdvancePayment[0].AllocatedAmountinRequestCurrency = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.id = 0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.code = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].AmountAllocated = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].advancePaymentRequestNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].outstandingAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].invoiceNumber = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].paidAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].beneficiaryName = \"\";\r\nautoObject.financialDetails.usedAdvancePayment[0].documentAmount = 0.0;\r\nautoObject.financialDetails.usedAdvancePayment[0].DBID = 0;\r\nautoObject.financialDetails.discountAmt = 0.0;\r\nautoObject.financialDetails.firstInstallementMaturityDate = new TWDate();\r\nautoObject.financialDetails.facilityAmtInDocCurrency = 0.0;\r\nautoObject.financialDetails.amtSight = 0.0;\r\nautoObject.financialDetails.beneficiaryDetails = new tw.object.BeneficiaryDetails();\r\nautoObject.financialDetails.beneficiaryDetails.account = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.bank = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.correspondentRefNum = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.name = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country = new tw.object.DBLookup();\r\nautoObject.financialDetails.beneficiaryDetails.country.id = 0;\r\nautoObject.financialDetails.beneficiaryDetails.country.code = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.arabicdescription = \"\";\r\nautoObject.financialDetails.beneficiaryDetails.country.englishdescription = \"\";\r\nautoObject.financialDetails.amtDeferredNoAvalized = 0.0;\r\nautoObject.financialDetails.amtPayableByNBE = 0.0;\r\nautoObject.financialDetails.executionHub = new tw.object.DBLookup();\r\nautoObject.financialDetails.executionHub.id = 0;\r\nautoObject.financialDetails.executionHub.code = \"\";\r\nautoObject.financialDetails.executionHub.arabicdescription = \"\";\r\nautoObject.financialDetails.executionHub.englishdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfForeignCurrency.id = 0;\r\nautoObject.financialDetails.sourceOfForeignCurrency.code = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfForeignCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.facilityAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.documentCurrency = new tw.object.DBLookup();\r\nautoObject.financialDetails.documentCurrency.id = 0;\r\nautoObject.financialDetails.documentCurrency.code = \"\";\r\nautoObject.financialDetails.documentCurrency.arabicdescription = \"\";\r\nautoObject.financialDetails.documentCurrency.englishdescription = \"\";\r\nautoObject.financialDetails.daysTillMaturity = 0;\r\nautoObject.financialDetails.tradeFinanceApprovalNumber = \"\";\r\nautoObject.financialDetails.amountAdvanced = 0.0;\r\nautoObject.financialDetails.paymentAccount = \"\";\r\nautoObject.financialDetails.tradeFOReferenceNumber = \"\";\r\nautoObject.financialDetails.documentAmount = 0.0;\r\nautoObject.financialDetails.sourceOfFunds = new tw.object.DBLookup();\r\nautoObject.financialDetails.sourceOfFunds.id = 0;\r\nautoObject.financialDetails.sourceOfFunds.code = \"\";\r\nautoObject.financialDetails.sourceOfFunds.arabicdescription = \"\";\r\nautoObject.financialDetails.sourceOfFunds.englishdescription = \"\";\r\nautoObject.financialDetails.chargesAccount = \"\";\r\nautoObject.financialDetails.CashAmtWithNoCurrency = 0.0;\r\nautoObject.financialDetails.amtDeferredAvalized = 0.0;\r\nautoObject.financialDetails.amtPaidbyOtherBanks = 0.0;\r\nautoObject.financialDetails.cashAmtInDocCurrency = 0.0;\r\nautoObject.IDCRequestType = new tw.object.DBLookup();\r\nautoObject.IDCRequestType.id = 0;\r\nautoObject.IDCRequestType.code = \"\";\r\nautoObject.IDCRequestType.arabicdescription = \"\";\r\nautoObject.IDCRequestType.englishdescription = \"\";\r\nautoObject.isIDCWithdrawn = false;\r\nautoObject.IDCRequestStage = \"\";\r\nautoObject.FCContractNumber = \"123\";\r\nautoObject.billOfLading = new tw.object.listOf.Invoice();\r\nautoObject.billOfLading[0] = new tw.object.Invoice();\r\nautoObject.billOfLading[0].date = new TWDate();\r\nautoObject.billOfLading[0].number = \"\";\r\nautoObject.importPurpose = new tw.object.DBLookup();\r\nautoObject.importPurpose.id = 0;\r\nautoObject.importPurpose.code = \"\";\r\nautoObject.importPurpose.arabicdescription = \"\";\r\nautoObject.importPurpose.englishdescription = \"\";\r\nautoObject.IDCRequestNature = new tw.object.DBLookup();\r\nautoObject.IDCRequestNature.id = 0;\r\nautoObject.IDCRequestNature.code = \"\";\r\nautoObject.IDCRequestNature.arabicdescription = \"\";\r\nautoObject.IDCRequestNature.englishdescription = \"\";\r\nautoObject.customerInformation = new tw.object.CustomerInformation();\r\nautoObject.customerInformation.CIFNumber = \"\";\r\nautoObject.customerInformation.importCardNumber = \"\";\r\nautoObject.customerInformation.commercialRegistrationNumber = \"\";\r\nautoObject.customerInformation.customerName = \"\";\r\nautoObject.customerInformation.isCustomeSanctionedbyCBE = \"\";\r\nautoObject.customerInformation.CBENumber = \"\";\r\nautoObject.customerInformation.customerSector = \"\";\r\nautoObject.customerInformation.facilityType = new tw.object.DBLookup();\r\nautoObject.customerInformation.facilityType.id = 0;\r\nautoObject.customerInformation.facilityType.code = \"\";\r\nautoObject.customerInformation.facilityType.arabicdescription = \"\";\r\nautoObject.customerInformation.facilityType.englishdescription = \"\";\r\nautoObject.customerInformation.commercialRegistrationOffice = \"\";\r\nautoObject.customerInformation.taxCardNumber = \"\";\r\nautoObject.customerInformation.customerType = \"\";\r\nautoObject.customerInformation.addressLine1 = \"\";\r\nautoObject.customerInformation.addressLine2 = \"\";\r\nautoObject.invoices = new tw.object.listOf.Invoice();\r\nautoObject.invoices[0] = new tw.object.Invoice();\r\nautoObject.invoices[0].date = new TWDate();\r\nautoObject.invoices[0].number = \"\";\r\nautoObject.productCategory = new tw.object.DBLookup();\r\nautoObject.productCategory.id = 0;\r\nautoObject.productCategory.code = \"\";\r\nautoObject.productCategory.arabicdescription = \"\";\r\nautoObject.productCategory.englishdescription = \"\";\r\nautoObject.documentsSource = new tw.object.DBLookup();\r\nautoObject.documentsSource.id = 0;\r\nautoObject.documentsSource.code = \"\";\r\nautoObject.documentsSource.arabicdescription = \"\";\r\nautoObject.documentsSource.englishdescription = \"\";\r\nautoObject.ParentIDCRequestNumber = \"\";\r\nautoObject.paymentTerms = new tw.object.DBLookup();\r\nautoObject.paymentTerms.id = 0;\r\nautoObject.paymentTerms.code = \"\";\r\nautoObject.paymentTerms.arabicdescription = \"\";\r\nautoObject.paymentTerms.englishdescription = \"\";\r\nautoObject.approvals = new tw.object.Approvals();\r\nautoObject.approvals.CAD = false;\r\nautoObject.approvals.treasury = false;\r\nautoObject.approvals.compliance = false;\r\nautoObject.appLog = new tw.object.listOf.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0] = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.appLog[0].startTime = new TWDate();\r\nautoObject.appLog[0].endTime = new TWDate();\r\nautoObject.appLog[0].userName = \"\";\r\nautoObject.appLog[0].role = \"\";\r\nautoObject.appLog[0].step = \"\";\r\nautoObject.appLog[0].action = \"\";\r\nautoObject.appLog[0].comment = \"\";\r\nautoObject.appLog[0].terminateReason = \"\";\r\nautoObject.appLog[0].returnReason = \"\";\r\nautoObject.stepLog = new tw.object.toolkit.NBEC.AppLog();\r\nautoObject.stepLog.startTime = new TWDate();\r\nautoObject.stepLog.endTime = new TWDate();\r\nautoObject.stepLog.userName = \"\";\r\nautoObject.stepLog.role = \"\";\r\nautoObject.stepLog.step = \"\";\r\nautoObject.stepLog.action = \"\";\r\nautoObject.stepLog.comment = \"\";\r\nautoObject.stepLog.terminateReason = \"\";\r\nautoObject.stepLog.returnReason = \"\";\r\nautoObject.DBID = 0;\r\nautoObject", "useDefault": "true"}}}], "ns16:dataOutput": [{"name": "isvalid", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.2df15530-52f0-41a9-9c25-bae06fa34398"}, {"name": "massege", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.0d917db9-6dd3-40cd-963c-f56294d058e5"}, {"name": "DBID", "itemSubjectRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d", "isCollection": "false", "id": "2055.2164b222-615a-4a75-a5d5-98b99470a6ff"}, {"name": "idcContract", "itemSubjectRef": "itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "isCollection": "false", "id": "2055.60d0706b-a7ec-4ef7-93af-f527d8da9f48"}, {"name": "parentIDCContract", "itemSubjectRef": "itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1", "isCollection": "false", "id": "2055.ef212b69-feda-46ce-97d9-c2a5b4fe91f3"}, {"name": "error", "itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "id": "2055.5612afbe-e447-4c02-8202-fd20453fd03f"}, {"name": "IDCRequest", "itemSubjectRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67", "isCollection": "false", "id": "2055.9a3d0a85-297e-4663-93c4-721d815eb332"}, {"name": "folderID", "itemSubjectRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01", "isCollection": "false", "id": "2055.9bd55f86-fcc3-485e-b9f0-c45dede585e9"}, {"name": "fullPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.1f4f4e36-97e7-43ae-b14a-21ca79480c5f"}, {"name": "parentPath", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.0d63f311-de13-4c02-9731-3cae95646591"}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.52711b62-7df7-40b3-aa2d-0338fa7284dd", "2055.82d72677-767a-4f2c-bc85-8e8dcdebf086", "2055.88e74584-565a-40c2-a722-ade695030bbd", "2055.455bc6b1-7ba6-4720-94d5-a300a02c504f"]}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.2df15530-52f0-41a9-9c25-bae06fa34398", "2055.0d917db9-6dd3-40cd-963c-f56294d058e5", "2055.2164b222-615a-4a75-a5d5-98b99470a6ff", "2055.60d0706b-a7ec-4ef7-93af-f527d8da9f48", "2055.ef212b69-feda-46ce-97d9-c2a5b4fe91f3", "2055.5612afbe-e447-4c02-8202-fd20453fd03f", "2055.9a3d0a85-297e-4663-93c4-721d815eb332", "2055.9bd55f86-fcc3-485e-b9f0-c45dede585e9", "2055.1f4f4e36-97e7-43ae-b14a-21ca79480c5f", "2055.0d63f311-de13-4c02-9731-3cae95646591"]}}, "ns16:laneSet": {"id": "c356b96d-9bea-4510-846d-7550da16deaa", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "71e12162-71a9-477f-bd67-96f18c746db8", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["fdaa298b-f679-42de-a255-0af145d71dc0", "c2d05175-f6ad-4008-ae10-591e4ee7d02f", "8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "a6129d38-34a3-4623-9a9e-d8e404c126d8", "f1ee4e76-20d9-4426-a039-72de129ca5a0", "e363cb85-8a6a-4690-a741-9dfb5644a68c", "b493bed2-487f-4a47-abb3-d51787b640ad", "36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "668a7876-1d21-4b1f-b557-61caca4c591d", "b5e33c56-0336-49a0-a352-58d4bc39d4b7", "f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "ae1216ae-bd08-45fb-aef8-bce65e2f6426", "0f5e586d-0779-4485-b42a-d58b9bbe59f8", "699b9c4a-04db-420d-87b3-e8cb1c286a0d", "e212cedc-cf7c-49ca-9c81-7484a80f478c", "086277b6-1233-4e1f-8b7d-f4d962b10be5", "99072e7b-f2b1-40e9-82b2-c038ce2bcc02", "59a87880-1c45-4052-ba87-8e54a3b76b24", "1460dc3d-355d-44a6-8b93-3fb0763acfcc", "6553d6c2-8d1e-4b49-818c-8a3ee5d79a93", "21bf961b-7097-4d5b-b1b0-25a6ab444787", "4e8a7062-5cbe-4975-850b-bc22276b37c8", "83b89cca-8a42-4643-a747-1ac33270f64f", "f0d946e2-e9b4-4ea8-97f4-478c1d077ee4", "a054c73c-8d25-4717-b21d-e82248d07389", "9f0a234f-f85c-4b82-90d7-323282f28c0e"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "fdaa298b-f679-42de-a255-0af145d71dc0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "5", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "194ecb4f-5819-4668-94d2-e221b974d994"}, "ns16:endEvent": [{"name": "End", "id": "c2d05175-f6ad-4008-ae10-591e4ee7d02f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1724", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:056f27db08707381:36b20ea0:18b6c1db471:469"}, "ns16:incoming": ["63acd7d4-c71d-4752-80cf-e5876026bcc9", "fbaa7d14-34b1-42fc-bf90-a11f92481e01", "ed712f21-360d-4f13-b6c4-65bf051fe108", "08d3d573-ea3c-4f3b-b713-45fcb07bc0f5"]}, {"name": "End Event", "id": "9f0a234f-f85c-4b82-90d7-323282f28c0e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1858", "y": "221", "width": "24", "height": "24"}}, "ns16:incoming": "6ade1896-2e98-499d-a764-59458e2c352a", "ns16:dataInputAssociation": {"ns16:assignment": {"ns16:from": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}, "ns16:errorEventDefinition": {"id": "dd839a48-10ff-4cdd-b868-fe7d4b73835d", "eventImplId": "c553fdc0-a824-4d92-84fb-b3d31cc2bc60", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true", "ns4:errorCode": ""}}}}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Parse IDC Request", "id": "8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "669", "y": "58", "width": "95", "height": "70"}}, "ns16:incoming": "ba83e2db-be94-49cc-9b97-4f9ff3e479ee", "ns16:outgoing": "8ea32b17-d31c-45fc-992d-2e54348e8c9c", "ns16:script": "\r\r\ntw.local.isTotalyPaid = false;\r\r\nif (tw.local.SCQueryBCContractRes.OutstandingAmount != \"\" && tw.local.SCQueryBCContractRes.OutstandingAmount!=null && tw.local.SCQueryBCContractRes.OutstandingAmount != undefined) {\r\r\n\ttw.local.idcContract.outstandingAmount = parseFloat(tw.local.SCQueryBCContractRes.OutstandingAmount);\r\r\n}\r\r\n\r\r\nif (tw.local.idcContract.outstandingAmount == 0) {\r\r\n\ttw.local.isvalid = false;\r\r\n\ttw.local.isTotalyPaid = true;\r\r\n\ttw.local.massege = \"this request is totally paid\";\r\r\n}else{\r\r\n\ttw.local.isvalid = true;\r\r\n\r\r\n \ttw.local.idcContract.IDCProduct.code = tw.local.SCQueryBCContractRes.ProductCode;\r\r\n \ttw.local.idcContract.IDCProduct.englishdescription = tw.local.SCQueryBCContractRes.ProductDesc;\r\r\n\t \r\r\n//\ttw.local.idcContract.IDCRequestStage = tw.local.SCQueryBCContractRes.Stag;\r\r\n\ttw.local.idcContract.userReference = tw.local.SCQueryBCContractRes.UseRefNum;\r\r\n\tif (tw.local.SCQueryBCContractRes.Amount != \"\" && tw.local.SCQueryBCContractRes.Amount != null && tw.local.SCQueryBCContractRes.Amount != undefined) {\r\r\n\t\ttw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);\r\r\n\t}\r\r\n\t\r\r\n//\tif (tw.local.SCQueryBCContractRes.Amount != \"\" && tw.local.SCQueryBCContractRes.Amount != null && tw.local.SCQueryBCContractRes.Amount != undefined) {\r\r\n//\t\ttw.local.idcContract.billAmount = parseFloat(tw.local.SCQueryBCContractRes.Amount);\r\r\n//\t}\r\r\n\r\r\n\ttw.local.idcContract.billCurrency.code = tw.local.SCQueryBCContractRes.Currency;\r\r\n\r\r\n\tif (tw.local.SCQueryBCContractRes.BaseDate != \"\" && tw.local.SCQueryBCContractRes.BaseDate != null && tw.local.SCQueryBCContractRes.BaseDate != undefined) {\r\r\n\t\ttw.local.idcContract.transactionBaseDate = new Date();\r\r\n\t\ttw.local.idcContract.transactionBaseDate.parse(tw.local.SCQueryBCContractRes.BaseDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\t\r\r\n\tif (tw.local.SCQueryBCContractRes.ValueDate != \"\" && tw.local.SCQueryBCContractRes.ValueDate != null && tw.local.SCQueryBCContractRes.ValueDate != undefined) {\r\r\n\t\ttw.local.idcContract.transactionValueDate = new Date();\r\r\n\t\ttw.local.idcContract.transactionValueDate.parse(tw.local.SCQueryBCContractRes.ValueDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\t\r\r\n\tif (tw.local.SCQueryBCContractRes.TenorDays != \"\" && tw.local.SCQueryBCContractRes.TenorDays != null && tw.local.SCQueryBCContractRes.TenorDays != undefined) {\r\r\n\t\ttw.local.idcContract.transactionTenorDays = parseInt(tw.local.SCQueryBCContractRes.TenorDays);\r\r\n\t}\r\r\n\r\r\n\tif (tw.local.SCQueryBCContractRes.TransitDays != \"\" && tw.local.SCQueryBCContractRes.TransitDays != null && tw.local.SCQueryBCContractRes.TransitDays != undefined) {\r\r\n\t\ttw.local.idcContract.transactionTransitDays = parseInt(tw.local.SCQueryBCContractRes.TransitDays);\r\r\n\t}\r\r\n\t\r\r\n\tif (tw.local.SCQueryBCContractRes.MaturityDate != \"\" && tw.local.SCQueryBCContractRes.MaturityDate != null && tw.local.SCQueryBCContractRes.MaturityDate != undefined) {\r\r\n\t\ttw.local.idcContract.transactionMaturityDate = new Date();\r\r\n\t\ttw.local.idcContract.transactionMaturityDate.parse(tw.local.SCQueryBCContractRes.MaturityDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\r\r\n\r\r\n\tfor (var i=0; i<tw.local.SCQueryBCContractRes.Contract_Parties.listLength; i++) {\r\r\n\t\ttw.local.idcContract.party[i] = new tw.object.Parties();\r\r\n\t\ttw.local.idcContract.party[i].partyCIF = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;\r\r\n\t\ttw.local.idcContract.party[i].partyId = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyId;\r\r\n\t\ttw.local.idcContract.party[i].partyType = new tw.object.NameValuePair();\r\r\n\t\ttw.local.idcContract.party[i].partyType.name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;\r\r\n\t\ttw.local.idcContract.party[i].partyType.value = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyType;\r\r\n\t\ttw.local.idcContract.party[i].name = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyName;\r\r\n\t\ttw.local.idcContract.party[i].country = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyCountryCode;\r\r\n\t\ttw.local.idcContract.party[i].language = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyLang;\r\r\n\t\ttw.local.idcContract.party[i].reference = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyRefNum;\r\r\n\t\ttw.local.idcContract.party[i].address1 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress1;\r\r\n\t\ttw.local.idcContract.party[i].address2 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress2;\r\r\n\t\ttw.local.idcContract.party[i].address3 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress3;\r\r\n\t\ttw.local.idcContract.party[i].address4 = tw.local.SCQueryBCContractRes.Contract_Parties[i].PartyAddress4;\r\r\n\t\tif(tw.local.idcContract.party[i].partyType.name == \"REMITTING BANK\" && tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr!=null && tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0] !=null )\t\t \r\r\n\t\t{\r\r\n\t\t   tw.local.idcContract.party[i].media = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].MEDIA;\r\r\n\t\t   tw.local.idcContract.party[i].address = tw.local.SCQueryBCContractRes.Contract_Parties[i].Party_Addr[0].ADDR;\r\r\n\t\t}\r\r\n\t\tif(tw.local.idcContract.party[i].partyType.name == \"REMITTING BANK\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Remitting Bank\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Remitting Bank\";\r\r\n\t\t}\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"DRAWEE\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Drawee\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Drawee\";\r\r\n\t\t}\r\r\n\t\t\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"DRAWER\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Drawer\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Drawer\";\r\r\n\t\t}\r\r\n\t\t\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"ACCOUNTEE\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Accountee\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Accountee\";\r\r\n\t\t}\r\r\n\t\t\r\r\n\t\telse if(tw.local.idcContract.party[i].partyType.name == \"CASE IN NEED\")\t\t \r\r\n\t\t{\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.name = \"Case in Need\";\r\r\n\t\t\ttw.local.idcContract.party[i].partyType.value = \"Case in Need\";\r\r\n\t\t}\r\r\n\t}\r\r\n\t\r\r\n\tif(tw.local.SCQueryBCContractRes.FromDate != null && tw.local.SCQueryBCContractRes.FromDate != \"\" )\r\r\n\t{\r\r\n\t    tw.local.idcContract.interestFromDate = new Date();\r\r\n\t    tw.local.idcContract.interestFromDate.parse(tw.local.SCQueryBCContractRes.FromDate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\tif(tw.local.SCQueryBCContractRes.Todate != null && tw.local.SCQueryBCContractRes.Todate != \"\" )\r\r\n\t{\r\r\n\t     tw.local.idcContract.interestToDate = new Date();\r\r\n\t     tw.local.idcContract.interestToDate.parse(tw.local.SCQueryBCContractRes.Todate, \"yyyy-MM-dd\");\r\r\n\t}\r\r\n\t\r\r\n//\tif (tw.local.SCQueryBCContractRes.Collateral_details != null && tw.local.SCQueryBCContractRes.Collateral_details[0] !=null && tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral !=null) {\r\r\n//\t      \r\r\n//\t   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount != null && tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount !=\"\" )\r\r\n//\t      tw.local.idcContract.collateralAmount = parseFloat(tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTAmount);\r\r\n//\t   if (tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY != null && tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY !=\"\" )\r\r\n//\t      tw.local.idcContract.collateralCurrency.code = tw.local.SCQueryBCContractRes.Collateral_details[0].Collateral.CollateralTCCY;\r\r\n//\t\t\r\r\n//\t}\r\r\n\t\r\r\n\ttw.local.idcContract.cashCollateralAccounts = new tw.object.listOf.CashCollateralAccount();\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0] = new tw.object.CashCollateralAccount();\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountBranchCode =\"\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountClass = \"Customer Account\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountCurrency =\"\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountNumber = new tw.object.NameValuePair();\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountNumber.name =\"\";\r\r\n\ttw.local.idcContract.cashCollateralAccounts[0].accountNumber.value = \"\"; \r\r\n\t\r\r\n\t\r\r\n\ttw.local.parentIDCContract = new tw.object.IDCContract();\r\r\n\ttw.local.parentIDCContract = tw.local.idcContract;\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "Init SQL", "id": "e363cb85-8a6a-4690-a741-9dfb5644a68c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "945", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "943a0fa8-bd2e-4ade-a9af-6a5e2e825fb2", "ns16:outgoing": "d3709240-2966-4eb0-b216-690f7e146630", "ns16:script": "tw.local.sql = \"SELECT max(ID) FROM BPM.IDC_REQUEST_DETAILS WHERE REQUEST_NUMBER like ? AND REQUEST_STATUS != 'Completed' AND REQUEST_STATUS != 'Terminated' AND REQUEST_STATUS != 'Canceled'\";\r\r\ntw.local.parameters = new tw.object.listOf.SQLParameter();\r\r\ntw.local.parameters[0]  = new tw.object.SQLParameter();\r\r\ntw.local.parameters[0].value = tw.local.requestNumber+\"%\";"}, {"scriptFormat": "text/x-javascript", "name": "check has running request", "id": "d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1179", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "06eb160f-9e72-4e88-b5a4-92ece00e13b3", "ns16:outgoing": "b05c7e80-32fd-48b7-93d0-3aa671ac2cf7", "ns16:script": "if (tw.local.results[0].rows[0].data[0] == null) {\r\r\n\ttw.local.haveRunningRequest = false;\r\r\n\ttw.local.isvalid = true;\r\r\n\t\r\r\n}\r\r\nelse{\r\r\n\ttw.local.haveRunningRequest = true;\r\r\n\ttw.local.isvalid = false;\r\r\n\ttw.local.massege = \"this request number has Running Request\";\r\r\n\t\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "Script Task", "id": "668a7876-1d21-4b1f-b557-61caca4c591d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "245", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "22b076c2-6552-454c-b4bc-39a71ac38b45", "ns16:outgoing": "55b7edaf-f016-482d-aece-0a99ea7f9422", "ns16:script": "tw.local.idcContract= new tw.object.IDCContract();\r\r\n\r\r\n//tw.local.idcContract.collateralAmount = 0;\r\r\n\r\r\ntw.local.idcContract.settlementAccounts = new tw.object.listOf.SettlementAccount();\r\r\ntw.local.idcContract.settlementAccounts[0] = new tw.object.SettlementAccount();\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount = new tw.object.DebitedAccount();\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountClass = \"Customer Account\";\r\r\ntw.local.idcContract.settlementAccounts[0].accountNumberList = new tw.object.listOf.NameValuePair();\r\r\n\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.accountCurrency = new tw.object.DBLookup();\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAccount.currency = new tw.object.NameValuePair();\r\r\n\r\r\ntw.local.idcContract.settlementAccounts[0].debitedAmount = new tw.object.DebitedAmount();\r\r\n\r\r\ntw.local.idcContract.swiftMessageData = {};\r\r\ntw.local.idcContract.swiftMessageData.intermediary = {};\r\r\ntw.local.idcContract.swiftMessageData.accountWithInstitution = {};\r\r\ntw.local.idcContract.swiftMessageData.intermediaryReimbursementInstitution = {};\r\r\ntw.local.idcContract.swiftMessageData.receiverCorrespondent = {};\r\r\ntw.local.idcContract.swiftMessageData.detailsOfPayment = {};\r\r\n\r\r\ntw.local.idcContract.swiftMessageData.orderingInstitution = {};\r\r\n\r\r\ntw.local.idcContract.swiftMessageData.beneficiaryInstitution = {};\r\r\ntw.local.idcContract.swiftMessageData.ultimateBeneficiary = {};\r\r\ntw.local.idcContract.swiftMessageData.orderingCustomer = {};\r\r\ntw.local.idcContract.swiftMessageData.senderToReciever = {};\r\r\n \r\r\ntw.local.idcContract.IDCProduct= new tw.object.DBLookup();\r\r\n//tw.local.idcContract.IDCProduct.code = \"IAVC\";\r\r\n//tw.local.idcContract.IDCProduct.englishdescription = \"INCOMING AVALIZED BILLS UNDER COLLECTION\";\r\r\n//tw.local.idcContract.userReference = \"*********\";\r\r\n//tw.local.idcContract.billAmount = 100;\r\r\ntw.local.idcContract.billCurrency = new tw.object.DBLookup();\r\r\n//tw.local.idcContract.billCurrency.code = \"EGP\";\r\r\n//tw.local.idcContract.sourceReference = \"1111111111111111\";\r\r\n//tw.local.idcContract.isLimitsTrackingRequired = false;\r\r\ntw.local.idcContract.interestToDate = new TWDate();\r\r\ntw.local.idcContract.transactionMaturityDate = new TWDate();\r\r\ntw.local.idcContract.transactionBaseDate = new TWDate();\r\r\n//tw.local.idcContract.tradeFinanceApprovalNumber = \"12345\";\r\r\ntw.local.idcContract.FCContractNumber = tw.local.IDCRequest.FCContractNumber;\r\r\ntw.local.idcContract.collateralCurrency = new tw.object.DBLookup();\r\r\n//tw.local.idcContract.collateralCurrency.code = \"\";\r\r\n//tw.local.idcContract.interestRate = 44.0;\r\r\n//tw.local.idcContract.transactionTransitDays = 44;\r\r\n\r\r\ntw.local.idcContract.advices = new tw.object.listOf.ContractAdvice();\r\r\n//tw.local.idcContract.advices[0] = new tw.object.ContractAdvice();\r\r\n//tw.local.idcContract.advices[0].adviceCode = \"FCEWNKMC\";\r\r\n//tw.local.idcContract.advices[0].suppressed = true;\r\r\n//tw.local.idcContract.advices[0].advicelines = new tw.object.SwiftMessagePart();\r\r\n//tw.local.idcContract.advices[0].advicelines.line1 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line2 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line3 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line4 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line5 = \"test\";\r\r\n//tw.local.idcContract.advices[0].advicelines.line6 = \"test\";\r\r\n\r\r\n\r\r\ntw.local.idcContract.party = new tw.object.listOf.Parties();\r\r\ntw.local.idcContract.party[0] = new tw.object.Parties();\r\r\ntw.local.idcContract.party[0].partyType = {};\r\r\ntw.local.idcContract.party[0].partyType.name = \"Drawee\";\r\r\ntw.local.idcContract.party[0].partyType.value = \"Drawee\";\r\r\ntw.local.idcContract.party[0].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\ntw.local.idcContract.party[0].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\ntw.local.idcContract.party[0].name = tw.local.IDCRequest.customerInformation.customerName;\r\r\ntw.local.idcContract.party[0].country = \"EG\";\r\r\ntw.local.idcContract.party[0].reference = \"NO REF\";\r\r\ntw.local.idcContract.party[0].address1 = tw.local.IDCRequest.customerInformation.addressLine1;\r\r\ntw.local.idcContract.party[0].address2 =tw.local.IDCRequest.customerInformation.addressLine2;\r\r\ntw.local.idcContract.party[0].address3 = tw.local.IDCRequest.customerInformation.addressLine3;\r\r\ntw.local.idcContract.party[0].address4 = \"\";\r\r\ntw.local.idcContract.party[0].media = \"\";\r\r\n\r\r\ntw.local.idcContract.party[0].phone = \"\";\r\r\ntw.local.idcContract.party[0].fax = \"\";\r\r\ntw.local.idcContract.party[0].email = \"\";\r\r\ntw.local.idcContract.party[0].contactPersonName = \"\";\r\r\ntw.local.idcContract.party[0].mobile = \"\";\r\r\ntw.local.idcContract.party[0].branch = {}\r\r\ntw.local.idcContract.party[0].branch.name = \"\";\r\r\ntw.local.idcContract.party[0].branch.value = \"\";\r\r\ntw.local.idcContract.party[0].language = \"\";\r\r\n\r\r\ntw.local.idcContract.party[0].isNbeCustomer = true;\r\r\ntw.local.idcContract.party[1] = new tw.object.Parties();\r\r\ntw.local.idcContract.party[1].partyType = {};\r\r\ntw.local.idcContract.party[1].partyType.name = \"Drawer\";\r\r\ntw.local.idcContract.party[1].partyType.value = \"Drawer\";\r\r\n//tw.local.idcContract.party[1].partyId = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\n//tw.local.idcContract.party[1].partyCIF = tw.local.IDCRequest.customerInformation.CIFNumber;\r\r\n//tw.local.idcContract.party[1].name = tw.local.IDCRequest.customerInformation.customerName;\r\r\n//tw.local.idcContract.party[1].country = \"EG\";\r\r\n//tw.local.idcContract.party[1].reference = \"NO REF\";\r\r\n//tw.local.idcContract.party[1].address1 = tw.local.IDCRequest.customerInformation.addressLine1;\r\r\n//tw.local.idcContract.party[1].address2 =tw.local.IDCRequest.customerInformation.addressLine2;\r\r\n//tw.local.idcContract.party[1].address3 = \"\";\r\r\n//tw.local.idcContract.party[1].address4 = \"\";\r\r\n//tw.local.idcContract.party[1].media = \"\";\r\r\n\r\r\n//tw.local.idcContract.party[1].phone = \"\";\r\r\n//tw.local.idcContract.party[1].fax = \"\";\r\r\n//tw.local.idcContract.party[1].email = \"\";\r\r\n//tw.local.idcContract.party[1].contactPersonName = \"\";\r\r\n//tw.local.idcContract.party[1].mobile = \"\";\r\r\n//tw.local.idcContract.party[1].branch = {}\r\r\n//tw.local.idcContract.party[1].branch.name = \"\";\r\r\n//tw.local.idcContract.party[1].branch.value = \"\";\r\r\n//tw.local.idcContract.party[1].language = \"\";\r\r\n//tw.local.idcContract.party[1].isNbeCustomer = true;\r\r\n\r\r\n//tw.local.idcContract.IDCRequestStage = \"Finial\";\r\r\ntw.local.idcContract.transactionValueDate = new TWDate();\r\r\n//tw.local.idcContract.transactionTenorDays = 5;\r\r\n\r\r\ntw.local.idcContract.interestFromDate = new TWDate();\r\r\n//tw.local.idcContract.interestAmount = 100;\r\r\n//tw.local.idcContract.haveInterest = true;\r\r\ntw.local.idcContract.accountNumberList = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.accountNumberList[0] = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.accountNumberList[0].name = \"12345\";\r\r\n//tw.local.idcContract.accountNumberList[0].value = \"12345\";\r\r\ntw.local.idcContract.facilities = new tw.object.listOf.toolkit.NBEC.creditFacilityInformation();\r\r\n//tw.local.idcContract.facilities[0] = new tw.object.toolkit.NBEC.creditFacilityInformation();\r\r\n//tw.local.idcContract.facilities[0].facilityCode = \"MMNUFY\";\r\r\n//tw.local.idcContract.facilities[0].overallLimit = 11.0;\r\r\n//tw.local.idcContract.facilities[0].limitAmount = 110;\r\r\n//tw.local.idcContract.facilities[0].effectiveLimitAmount = 11.0;\r\r\n//tw.local.idcContract.facilities[0].availableAmount = 13.0;\r\r\n//tw.local.idcContract.facilities[0].expiryDate = new TWDate();\r\r\n//tw.local.idcContract.facilities[0].availableFlag = true;\r\r\n//tw.local.idcContract.facilities[0].authorizedFlag = true;\r\r\n//tw.local.idcContract.facilities[0].Utilization = 0.0;\r\r\n//tw.local.idcContract.facilities[0].returnCode = \"12345\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines = new tw.object.listOf.toolkit.NBEC.FacilityLinesDetails();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0] = new tw.object.toolkit.NBEC.FacilityLinesDetails();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineCode = \"fdewa\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineAmount = 3452.0;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].availableAmount = 876.0;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].effectiveLineAmount = 234560.0;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].expiryDate = new TWDate();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.name = \"001\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityBranch.value = \"001\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].availableFlag = true;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].authorizedFlag = true;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityPercentageToBook = 100;\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].internalRemarks = \"gwd\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].purpose = \"ekwsaa\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LCCommissionPercentage = \"112\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LCDef = \"eell\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LCCashCover = \"mcdms\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].IDCCommission = \"dkmc\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].IDCAvalCommPercentage = \"elm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].IDCCashCoverPercentage = \"csa\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].debitAccountNumber = \"dlm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineCurrency = \"EGP\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].lineSerialNumber = \"eele\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].returnCode = \"4343\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCommission = \"433\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_BidBond = \"434\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_Performance = \"434\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].LGCashCover_AdvancedPayment = \"1234\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].name = \"ewdew\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].code = \"ewd\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].source = \"kmk\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCurrencies[0].status = \"dkkd,\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].name = \"ked\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].code = \"kee\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].source = \"iinkl\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedBranches[0].status = \"eoeo\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].name = \"mdmdm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].code = \"ieii\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].source = \"nsamls\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedProducts[0].status = \"wodmow,\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].name = \"wmd;wmq\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].code = \"lkwmdw\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].source = \"alwmda;lm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].restrictedCustomers[0].status = \"aswdcnk\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions = new tw.object.listOf.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0] = new tw.object.toolkit.NBEC.restrictedItem();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].name = \"iuyt\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].code = \"lkjh\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].source = \",mnbv\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].exposureRestrictions[0].status = \"ujmtgv\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].CIF = \"12345678\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].partyType = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].partyType.name = \"ddddmm\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].partyType.value = \"ujm tg\";\r\r\n//tw.local.idcContract.facilities[0].facilityLines[0].facilityID = \"ertyuio\";\r\r\n//tw.local.idcContract.facilities[0].status = \"rururur\";\r\r\n//tw.local.idcContract.facilities[0].facilityCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\r\n//tw.local.idcContract.facilities[0].facilityCurrency.name = \"rjrjrj\";\r\r\n//tw.local.idcContract.facilities[0].facilityCurrency.value = \"enene\";\r\r\n//tw.local.idcContract.facilities[0].facilityID = \"11111111\";\r\r\n//tw.local.idcContract.limitsTrackingVIs = \"DEFAULT\";\r\r\n//tw.local.idcContract.interestVisibility = \"DEFAULT\";\r\r\n//tw.local.idcContract.adviceVis = \"DEFAULT\";\r\r\n//tw.local.idcContract.outstandingAmount = 100.0;"}, {"scriptFormat": "text/x-javascript", "name": "check idc type", "id": "b5e33c56-0336-49a0-a352-58d4bc39d4b7", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "59", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "194ecb4f-5819-4668-94d2-e221b974d994", "ns16:outgoing": "8ce516ac-97b8-4121-9aa4-da3463ee2923", "ns16:script": "if (tw.local.IDCRequest.IDCRequestType.englishdescription ==\"IDC Completion\" ) {\r\r\n\r\r\n\tif (tw.local.parentIDC.IDCRequestStage ==\"Final\") {\r\r\n\t\ttw.local.massege = \"can not make IDC Completion request for request its stage Final\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}\r\r\n\telse{\r\r\n\t\ttw.local.isvalid = true;\r\r\n\t}\r\r\n\t\r\r\n}else if (tw.local.IDCRequest.IDCRequestType.englishdescription ==\"IDC Payment\" ) {\r\r\n\r\r\n\tif (tw.local.parentIDC.IDCRequestStage != \"Final\") {\r\r\n\t\ttw.local.massege = \"can not make IDC Payment request for request its stage initial\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}\r\r\n\telse if(tw.local.parentIDC.paymentTerms.englishdescription == \"Sight\"){\r\r\n\t\ttw.local.massege = \"can not make IDC Payment request for request its payment Terms Sight\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}else if(tw.local.parentIDC.financialDetails.amtSight > 0 ){\r\r\n\t\ttw.local.massege = \"can not make IDC Payment request for request its sight amount more than 0\";\r\r\n\t\ttw.local.isvalid = false;\r\r\n\t}\r\r\n\telse{\r\r\n\t\ttw.local.isvalid = true;\r\r\n\t}\r\r\n}\r\r\nelse{\r\r\n\ttw.local.isvalid = true;\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "Catch Errors", "id": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1690", "y": "267", "width": "95", "height": "70", "color": "#FF7782"}}, "ns16:incoming": ["ff5d24ef-a747-483b-9309-b526fc5720fc", "f3a9e7e6-6200-4285-a465-6ec73a529420", "83d7717c-7f4e-45f3-9afa-d8bd30cc257c", "d4f2c5e2-5ce5-4ad7-8e5f-df0260ca6dfa", "1aa354ec-c58b-4e68-bf75-0a755f429f2f", "b498b616-8aa5-4c6a-9469-61d6731f9a3d", "9fbee0ac-15d3-4584-a7cf-0ef52e5fe901", "8e964d0e-172a-4924-bc9b-0ee9272df726", "351e7b05-e245-4427-b61b-e1c5a43ca3a3", "c4af2a6b-38bf-4b1e-b32b-8de6b2045d93", "3f074fdf-69e4-47d6-8ec3-e4c7cfa1a995"], "ns16:outgoing": "6ade1896-2e98-499d-a764-59458e2c352a", "ns16:script": "if (tw.local.error != undefined && !!tw.local.error.errorText) {\r\r\n\ttw.local.errorMSG = tw.local.error.errorText;\r\r\n}else if (tw.system.error != undefined && tw.system.error != null) {\r\r\n\ttw.local.errorMSG = String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}else{\r\r\n\ttw.local.error = new tw.object.AjaxError();\r\r\n}\r\r\n\r\r\ntw.local.error.errorText = \"Error Occured<br> Service Name : \"+tw.system.serviceFlow.name+\"<br> Error Message : \"+tw.local.errorMSG;\r\r\n"}], "ns16:sequenceFlow": [{"sourceRef": "8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "targetRef": "83b89cca-8a42-4643-a747-1ac33270f64f", "name": "To Get IDC Contract Facilities", "id": "8ea32b17-d31c-45fc-992d-2e54348e8c9c", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "a6129d38-34a3-4623-9a9e-d8e404c126d8", "targetRef": "c2d05175-f6ad-4008-ae10-591e4ee7d02f", "name": "To End", "id": "63acd7d4-c71d-4752-80cf-e5876026bcc9", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "971", "y": "23"}, "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.isTotalyPaid\t  ==\t  true", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "a6129d38-34a3-4623-9a9e-d8e404c126d8", "targetRef": "e363cb85-8a6a-4690-a741-9dfb5644a68c", "name": "To Init SQL", "id": "943a0fa8-bd2e-4ade-a9af-6a5e2e825fb2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.isTotalyPaid\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "f1ee4e76-20d9-4426-a039-72de129ca5a0", "targetRef": "c2d05175-f6ad-4008-ae10-591e4ee7d02f", "name": "To End", "id": "fbaa7d14-34b1-42fc-bf90-a11f92481e01", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac1", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "e363cb85-8a6a-4690-a741-9dfb5644a68c", "targetRef": "b493bed2-487f-4a47-abb3-d51787b640ad", "name": "To SQL Execute Statement (SQLResult)", "id": "d3709240-2966-4eb0-b216-690f7e146630", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "b493bed2-487f-4a47-abb3-d51787b640ad", "targetRef": "d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "name": "To check has running request", "id": "06eb160f-9e72-4e88-b5a4-92ece00e13b3", "ns16:extensionElements": {"ns3:endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "targetRef": "21bf961b-7097-4d5b-b1b0-25a6ab444787", "name": "To Get Request Number And CBE", "id": "*************-4c36-b0eb-ee75ff33da57", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.haveRunningRequest\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "targetRef": "c2d05175-f6ad-4008-ae10-591e4ee7d02f", "name": "To End", "id": "ed712f21-360d-4f13-b6c4-65bf051fe108", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "1432", "y": "163"}, "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "targetRef": "36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "name": "To has running request", "id": "b05c7e80-32fd-48b7-93d0-3aa671ac2cf7", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "668a7876-1d21-4b1f-b557-61caca4c591d", "targetRef": "1460dc3d-355d-44a6-8b93-3fb0763acfcc", "name": "To MW_FC Query DC Contract", "id": "55b7edaf-f016-482d-aece-0a99ea7f9422", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "b5e33c56-0336-49a0-a352-58d4bc39d4b7", "targetRef": "f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "name": "To Exclusive Gateway", "id": "8ce516ac-97b8-4121-9aa4-da3463ee2923", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "targetRef": "668a7876-1d21-4b1f-b557-61caca4c591d", "name": "To <PERSON>ript Task", "id": "22b076c2-6552-454c-b4bc-39a71ac38b45", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "targetRef": "c2d05175-f6ad-4008-ae10-591e4ee7d02f", "name": "To End", "id": "08d3d573-ea3c-4f3b-b713-45fcb07bc0f5", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "1252", "y": "216"}, "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.isvalid\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "ae1216ae-bd08-45fb-aef8-bce65e2f6426", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "ff5d24ef-a747-483b-9309-b526fc5720fc", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "0f5e586d-0779-4485-b42a-d58b9bbe59f8", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "f3a9e7e6-6200-4285-a465-6ec73a529420", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "699b9c4a-04db-420d-87b3-e8cb1c286a0d", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "83d7717c-7f4e-45f3-9afa-d8bd30cc257c", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "e212cedc-cf7c-49ca-9c81-7484a80f478c", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "d4f2c5e2-5ce5-4ad7-8e5f-df0260ca6dfa", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "086277b6-1233-4e1f-8b7d-f4d962b10be5", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "1aa354ec-c58b-4e68-bf75-0a755f429f2f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "99072e7b-f2b1-40e9-82b2-c038ce2bcc02", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "b498b616-8aa5-4c6a-9469-61d6731f9a3d", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "59a87880-1c45-4052-ba87-8e54a3b76b24", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "9fbee0ac-15d3-4584-a7cf-0ef52e5fe901", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "targetRef": "9f0a234f-f85c-4b82-90d7-323282f28c0e", "name": "To End", "id": "6ade1896-2e98-499d-a764-59458e2c352a", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "1460dc3d-355d-44a6-8b93-3fb0763acfcc", "targetRef": "a054c73c-8d25-4717-b21d-e82248d07389", "name": "To isSuccess?", "id": "9427dfdf-0b8a-4476-9315-19c753730904", "ns16:extensionElements": {"ns3:endStateId": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "21bf961b-7097-4d5b-b1b0-25a6ab444787", "targetRef": "f1ee4e76-20d9-4426-a039-72de129ca5a0", "name": "To Insert IDC Request", "id": "a75df045-58b4-48cd-8b18-d73f73b65378", "ns16:extensionElements": {"ns3:endStateId": "guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c7", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "6553d6c2-8d1e-4b49-818c-8a3ee5d79a93", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "8e964d0e-172a-4924-bc9b-0ee9272df726", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "4e8a7062-5cbe-4975-850b-bc22276b37c8", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "351e7b05-e245-4427-b61b-e1c5a43ca3a3", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "83b89cca-8a42-4643-a747-1ac33270f64f", "targetRef": "a6129d38-34a3-4623-9a9e-d8e404c126d8", "name": "To is totally paid", "id": "b4cb8b21-b895-4651-9bcd-b3ca74a232c6", "ns16:extensionElements": {"ns3:endStateId": "guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:7c3a", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fdaa298b-f679-42de-a255-0af145d71dc0", "targetRef": "b5e33c56-0336-49a0-a352-58d4bc39d4b7", "name": "To check idc type", "id": "194ecb4f-5819-4668-94d2-e221b974d994", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "f0d946e2-e9b4-4ea8-97f4-478c1d077ee4", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "To Catch Errors", "id": "c4af2a6b-38bf-4b1e-b32b-8de6b2045d93", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftTop", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a054c73c-8d25-4717-b21d-e82248d07389", "targetRef": "8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "name": "Yes", "id": "ba83e2db-be94-49cc-9b97-4f9ff3e479ee", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a054c73c-8d25-4717-b21d-e82248d07389", "targetRef": "dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "name": "No", "id": "3f074fdf-69e4-47d6-8ec3-e4c7cfa1a995", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.isSuccessful\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}], "ns16:exclusiveGateway": [{"default": "63acd7d4-c71d-4752-80cf-e5876026bcc9", "name": "is totally paid", "id": "a6129d38-34a3-4623-9a9e-d8e404c126d8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "871", "y": "79", "width": "32", "height": "32"}}, "ns16:incoming": "b4cb8b21-b895-4651-9bcd-b3ca74a232c6", "ns16:outgoing": ["63acd7d4-c71d-4752-80cf-e5876026bcc9", "943a0fa8-bd2e-4ade-a9af-6a5e2e825fb2"]}, {"default": "ed712f21-360d-4f13-b6c4-65bf051fe108", "name": "has running request", "id": "36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1302", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "b05c7e80-32fd-48b7-93d0-3aa671ac2cf7", "ns16:outgoing": ["*************-4c36-b0eb-ee75ff33da57", "ed712f21-360d-4f13-b6c4-65bf051fe108"]}, {"default": "22b076c2-6552-454c-b4bc-39a71ac38b45", "name": "Exclusive Gateway", "id": "f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "161", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "8ce516ac-97b8-4121-9aa4-da3463ee2923", "ns16:outgoing": ["22b076c2-6552-454c-b4bc-39a71ac38b45", "08d3d573-ea3c-4f3b-b713-45fcb07bc0f5"]}, {"default": "ba83e2db-be94-49cc-9b97-4f9ff3e479ee", "name": "isSuccess?", "id": "a054c73c-8d25-4717-b21d-e82248d07389", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "511", "y": "77", "width": "32", "height": "32"}}, "ns16:incoming": "9427dfdf-0b8a-4476-9315-19c753730904", "ns16:outgoing": ["ba83e2db-be94-49cc-9b97-4f9ff3e479ee", "3f074fdf-69e4-47d6-8ec3-e4c7cfa1a995"]}], "ns16:callActivity": [{"calledElement": "1.06eaabde-33db-480a-9d65-982fa27c2eac", "name": "Insert IDC Request", "id": "f1ee4e76-20d9-4426-a039-72de129ca5a0", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1590", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "a75df045-58b4-48cd-8b18-d73f73b65378", "ns16:outgoing": "fbaa7d14-34b1-42fc-bf90-a11f92481e01", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.6287bfa2-c4da-4ec3-842c-a164e045a461", "ns16:assignment": {"ns16:from": {"_": "tw.local.IDCRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67"}}}, {"ns16:targetRef": "2055.e4ea1176-3273-4117-87c0-b882c7df46c4", "ns16:assignment": {"ns16:from": {"_": "tw.local.idcContract", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.2fadd4c3-4741-4fce-8d56-2d8b8fbc8ccb", "ns16:assignment": {"ns16:to": {"_": "tw.local.DBID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}, {"ns16:sourceRef": "2055.5d7794f2-995a-47eb-8a0c-755c73dc5d01", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}]}, {"calledElement": "1.4e480adb-5741-4f5e-aead-27b3654e9cd2", "name": "SQL Execute Statement (SQLResult)", "id": "b493bed2-487f-4a47-abb3-d51787b640ad", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1060", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "d3709240-2966-4eb0-b216-690f7e146630", "ns16:outgoing": "06eb160f-9e72-4e88-b5a4-92ece00e13b3", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.25f5990f-9abf-4cf6-9101-497d43dee141", "ns16:assignment": {"ns16:from": {"_": "tw.local.sql", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43", "ns16:assignment": {"ns16:from": {"_": "tw.local.parameters", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c"}}}, {"ns16:targetRef": "2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e", "ns16:assignment": {"ns16:from": {"_": "tw.env.DATASOURCE", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.53ee0328-837f-427d-b0a1-0255f36f7e0c", "ns16:assignment": {"ns16:from": {"_": "-1", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764", "ns16:assignment": {"ns16:to": {"_": "tw.local.results", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1"}}}}, {"calledElement": "1.3875c748-15fc-40ef-bef1-ea4d905d7f75", "name": "MW_FC Query DC Contract", "id": "1460dc3d-355d-44a6-8b93-3fb0763acfcc", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "369", "y": "58", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:preAssignmentScript": ""}, "ns16:incoming": "55b7edaf-f016-482d-aece-0a99ea7f9422", "ns16:outgoing": "9427dfdf-0b8a-4476-9315-19c753730904", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.8f786b77-ae66-4547-a444-d6ccb8969c42", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentIDC.FCContractNumber", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.5c2849be-c329-4d8d-8ae5-dbee56bdb753", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.4ff12b5f-6b69-4504-a730-046ee5c2000a", "ns16:assignment": {"ns16:from": {"_": "tw.system.user_id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.5f2bf85c-cc00-48c8-a7a3-e540ed2fb5f9", "ns16:assignment": {"ns16:from": {"_": "tw.system.currentProcessInstanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.5d8af3bb-43f2-4fdb-ab6a-790ef67a95ea", "ns16:assignment": {"ns16:from": {"_": "\"INWARD DOCUMENTARY COLLECTION\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.82de65ee-21b4-4333-9eb2-c5cc7df13e75", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.42effea6-8d8b-437c-958a-da5cf9119674", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.95835270-515f-4794-a207-a5e2aa301c0e", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.c74a4913-3af0-43b4-a5e1-8d3c893002c4", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.004a9996-2e6b-4d60-821e-5db8e4ba2271", "ns16:assignment": {"ns16:to": {"_": "tw.local.SCQueryBCContractRes", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.*************-4cbb-a781-44d233d577c6"}}}, {"ns16:sourceRef": "2055.ee7811d9-22b1-4727-9148-bcac74c306af", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMSG", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.4cba4a0e-1cea-4df8-ab8c-fa24e0c88c13", "name": "Get Request Number And CBE", "id": "21bf961b-7097-4d5b-b1b0-25a6ab444787", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1425", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "*************-4c36-b0eb-ee75ff33da57", "ns16:outgoing": "a75df045-58b4-48cd-8b18-d73f73b65378", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.9eabaff4-3e88-47e4-bbbe-5cc34cf37cb2", "ns16:assignment": {"ns16:from": {"_": "tw.local.IDCRequest", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67"}}}, {"ns16:targetRef": "2055.6e39d6c4-152e-4681-9ccb-b3d2ea326441", "ns16:assignment": {"ns16:from": {"_": "tw.local.parentIDC", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.7f4fdce8-dfe9-472a-a501-530e9a76af67"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.f4a7754b-fcbe-4f1f-aaaf-65abfffad2aa", "ns16:assignment": {"ns16:to": {"_": "tw.local.IDCRequest.customerInformation.isCustomeSanctionedbyCBE", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.d22b4a18-865c-4b84-b879-26ee9e3f322f", "ns16:assignment": {"ns16:to": {"_": "tw.local.IDCRequest.appInfo.instanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.b304617e-98f3-4070-b457-59e470497a2f", "ns16:assignment": {"ns16:to": {"_": "tw.local.fullPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.77c3cc2f-ce0f-473c-8551-a2f1093abb8f", "ns16:assignment": {"ns16:to": {"_": "tw.local.folderID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.3212d5b3-f692-41b3-a893-343dc5c3df01"}}}, {"ns16:sourceRef": "2055.31e2d85c-18d8-4756-82c5-efad7879177e", "ns16:assignment": {"ns16:to": {"_": "tw.local.parentPath", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.4ae2afa0-cdd1-4d71-869e-7ea34e15f8aa", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}]}, {"calledElement": "1.42f20f4f-289f-432d-9bd8-3837be1dc61d", "name": "Get IDC Contract Facilities", "id": "83b89cca-8a42-4643-a747-1ac33270f64f", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "779", "y": "60", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": "8ea32b17-d31c-45fc-992d-2e54348e8c9c", "ns16:outgoing": "b4cb8b21-b895-4651-9bcd-b3ca74a232c6", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.e36b1711-d92f-4f79-8134-45f6ebe54fe7", "ns16:assignment": {"ns16:from": {"_": "tw.local.idcContract", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1"}}}, {"ns16:targetRef": "2055.5ee29ebe-48c3-4dc5-8fda-5433c9d3151d", "ns16:assignment": {"ns16:from": {"_": "tw.local.SCQueryBCContractRes", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.*************-4cbb-a781-44d233d577c6"}}}], "ns16:dataOutputAssociation": {"ns16:sourceRef": "2055.10751aed-d9b5-4b76-804f-3a33b2a7eb76", "ns16:assignment": {"ns16:to": {"_": "tw.local.idcContract", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.7209faed-a82e-4fd1-a6a0-1b0d0d7445f1"}}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c", "isCollection": "true", "name": "parameters", "id": "2056.ff8aa7a3-bc58-45dc-a662-d32c344a8f11"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "sql", "id": "2056.736a5ca8-5151-467e-9d77-576f7c5e0200"}, {"itemSubjectRef": "itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1", "isCollection": "true", "name": "results", "id": "2056.3455980e-a47c-48bb-b1cc-3073d098fa0b"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "haveRunningRequest", "id": "2056.0bf11c4f-4221-4ab4-8f45-da3ecef66b36"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isTotalyPaid", "id": "2056.07190f21-b578-4bde-a51e-36031244b366"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMSG", "id": "2056.baa7b86f-9ab2-4e31-91aa-68fe1d638c3e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorCode", "id": "2056.b8aad2ab-921e-4505-bbd0-aa2a6d94e28e"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.fda30d62-86a6-461e-85ac-************"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.0ccabf04-2650-48f4-a1ca-b18f255675c7"}, {"itemSubjectRef": "itm.12.*************-4cbb-a781-44d233d577c6", "isCollection": "false", "name": "SCQueryBCContractRes", "id": "2056.d5d0cbde-a3d5-4a67-99e3-0b43e353f916"}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "b5e33c56-0336-49a0-a352-58d4bc39d4b7", "parallelMultiple": "false", "name": "Error", "id": "ae1216ae-bd08-45fb-aef8-bce65e2f6426", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "94", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "ff5d24ef-a747-483b-9309-b526fc5720fc", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "07ab6351-264a-47c0-a5ea-c647666862b7"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "52c6f7ed-9e5f-441f-85f1-2181fc1bfd58", "eventImplId": "ef1e110f-6772-47d2-8bf3-ad47dcc32920", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "668a7876-1d21-4b1f-b557-61caca4c591d", "parallelMultiple": "false", "name": "Error1", "id": "0f5e586d-0779-4485-b42a-d58b9bbe59f8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "280", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "f3a9e7e6-6200-4285-a465-6ec73a529420", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "ed9458ee-5205-4c56-9801-1fe9288050ca"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "456ff78e-d003-44e1-b96e-8397f14ad851", "eventImplId": "72e804b8-5444-4d02-89cf-001e15608cd1", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "parallelMultiple": "false", "name": "Error2", "id": "699b9c4a-04db-420d-87b3-e8cb1c286a0d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "704", "y": "116", "width": "24", "height": "24"}}, "ns16:outgoing": "83d7717c-7f4e-45f3-9afa-d8bd30cc257c", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "27a58723-eedf-4fd7-96c0-eff8c28ccff1"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "dab45dea-d869-4b33-97a2-c166f0b40105", "eventImplId": "eda24b30-bfc1-45b4-8b15-a412c4053e41", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "e363cb85-8a6a-4690-a741-9dfb5644a68c", "parallelMultiple": "false", "name": "Error3", "id": "e212cedc-cf7c-49ca-9c81-7484a80f478c", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1006", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "d4f2c5e2-5ce5-4ad7-8e5f-df0260ca6dfa", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "25bc3dc6-28b9-4ea5-92ec-e2dd62b6c6c3"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "9033a645-c427-4f10-bcb0-48860fb9203b", "eventImplId": "0a8c35e9-6abe-46d2-84aa-47895034dd3c", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "b493bed2-487f-4a47-abb3-d51787b640ad", "parallelMultiple": "false", "name": "Error4", "id": "086277b6-1233-4e1f-8b7d-f4d962b10be5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1095", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "1aa354ec-c58b-4e68-bf75-0a755f429f2f", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "17ff3a7c-f2d5-4718-9738-2ed44f8f46a9"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "e21c5d6c-626e-445a-b490-9ebeeaa1b4b9", "eventImplId": "c0a3f1f0-261d-4893-85e2-99260bbd60b0", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "parallelMultiple": "false", "name": "Error5", "id": "99072e7b-f2b1-40e9-82b2-c038ce2bcc02", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1214", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "b498b616-8aa5-4c6a-9469-61d6731f9a3d", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "641abe04-3021-4703-973b-c2eb1f174e92"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "16041c58-26df-4b6b-a765-bd9cf8a6c0be", "eventImplId": "fc527377-1c49-4407-81f1-9fc30f1f4625", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "f1ee4e76-20d9-4426-a039-72de129ca5a0", "parallelMultiple": "false", "name": "Error6", "id": "59a87880-1c45-4052-ba87-8e54a3b76b24", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1599", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "9fbee0ac-15d3-4584-a7cf-0ef52e5fe901", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "c9dd502d-5ac0-4af4-b801-fceaec0e802a"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "80f48fb7-5d4c-4655-8d8a-bae3880f9163", "eventImplId": "37ace88b-a60c-4ed0-8437-fb6b4d48699e", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "1460dc3d-355d-44a6-8b93-3fb0763acfcc", "parallelMultiple": "false", "name": "Error7", "id": "6553d6c2-8d1e-4b49-818c-8a3ee5d79a93", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "404", "y": "116", "width": "24", "height": "24"}}, "ns16:outgoing": "8e964d0e-172a-4924-bc9b-0ee9272df726", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "e0f92768-0b1d-4714-8693-1a9cae9bed20"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "8893d422-4952-4bcc-aab7-0aff49921d7a", "eventImplId": "d5b91c3b-d4d9-45b0-89ae-47d11ba0b04d", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "21bf961b-7097-4d5b-b1b0-25a6ab444787", "parallelMultiple": "false", "name": "Error8", "id": "4e8a7062-5cbe-4975-850b-bc22276b37c8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "1460", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "351e7b05-e245-4427-b61b-e1c5a43ca3a3", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "0636619d-2cc1-4226-8280-8b7fe62157a0"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "30b08339-3d7b-4934-87c3-dce2a94daf85", "eventImplId": "f105d181-50df-4a4e-87b5-96ec91b70c2d", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "83b89cca-8a42-4643-a747-1ac33270f64f", "parallelMultiple": "false", "name": "Error9", "id": "f0d946e2-e9b4-4ea8-97f4-478c1d077ee4", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "814", "y": "118", "width": "24", "height": "24"}}, "ns16:outgoing": "c4af2a6b-38bf-4b1e-b32b-8de6b2045d93", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "9ced3e41-258a-483f-9995-7b256ae6a65e"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "05c95ef5-5538-47a6-aa04-00ede8ddf598", "eventImplId": "ef9add1b-96a4-41cd-83bb-65f53dc41c51", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}]}}}, "link": [{"name": "To isSuccess?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.05331bbb-5dd1-4f07-8757-5d1ef080c629", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc", "2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc"], "endStateId": "guid:e6d52ec61513ed03:232a663a:189f6396e2d:4754", "toProcessItemId": ["2025.a054c73c-8d25-4717-b21d-e82248d07389", "2025.a054c73c-8d25-4717-b21d-e82248d07389"], "guid": "3f014205-1fe5-484c-9d75-878232df77bb", "versionId": "0d06c039-b197-421e-8a0e-665060f2c3f2", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get IDC Contract Facilities", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7914af6f-eefd-408a-8b28-3dcbf187b684", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e"], "endStateId": "Out", "toProcessItemId": ["2025.83b89cca-8a42-4643-a747-1ac33270f64f", "2025.83b89cca-8a42-4643-a747-1ac33270f64f"], "guid": "954b4857-9e2e-4240-8991-41361ba80bcd", "versionId": "2de9ca47-9199-4950-bdff-d54aad822799", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Exclusive Gateway", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.b42a8a97-0fbc-4e9e-b3aa-fa5fa94964a5", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7", "2025.b5e33c56-0336-49a0-a352-58d4bc39d4b7"], "endStateId": "Out", "toProcessItemId": ["2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0"], "guid": "b0b0bc70-c8a0-4300-9af3-47b67105b4ce", "versionId": "36eb5f90-e726-4682-a0af-51fd4f4ea92f", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Insert IDC Request", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.856de0e2-987a-4b3e-ba5b-3172df5ff33e", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.21bf961b-7097-4d5b-b1b0-25a6ab444787", "2025.21bf961b-7097-4d5b-b1b0-25a6ab444787"], "endStateId": "guid:246979e51bde3d7d:-3bedc06d:1892bf1f059:-61c7", "toProcessItemId": ["2025.f1ee4e76-20d9-4426-a039-72de129ca5a0", "2025.f1ee4e76-20d9-4426-a039-72de129ca5a0"], "guid": "339c77a5-6c7d-41f2-8a1a-df2dbc8ed8d1", "versionId": "3ad0d2a3-99de-4097-bc14-f9d70c00dd26", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.b7d497d1-7083-442e-9a0e-0048b81fd316", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f1ee4e76-20d9-4426-a039-72de129ca5a0", "2025.f1ee4e76-20d9-4426-a039-72de129ca5a0"], "endStateId": "guid:7ffed11b1cbfb184:129346e9:189b62f5bd4:-3ac1", "toProcessItemId": ["2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f", "2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f"], "guid": "51935698-444c-4608-8c17-166f7cb7f0e1", "versionId": "4257034c-5b06-4999-aea8-2fee9bfcb47e", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.cbcc71bc-ad42-4696-a1e4-1b0d15f17f2c", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a6129d38-34a3-4623-9a9e-d8e404c126d8", "2025.a6129d38-34a3-4623-9a9e-d8e404c126d8"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f", "2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f"], "guid": "89d65f15-1a0e-46ab-b0f5-50b0718acbbe", "versionId": "57375eae-5f0a-482b-8a61-4b2ccf73c0e0", "layoutData": {"controlPoints": {"controlPoint": {"x": "971", "y": "23"}}, "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1de82572-a47e-49a0-ae09-87293b8ab224", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5"], "endStateId": "Out", "toProcessItemId": ["2025.9f0a234f-f85c-4b82-90d7-323282f28c0e", "2025.9f0a234f-f85c-4b82-90d7-323282f28c0e"], "guid": "1ed3b8e1-09a8-454e-ae58-526acd8e8894", "versionId": "590c1751-aa6d-40a0-acef-bfd2f37285b1", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.5f92f1eb-0911-45a3-a710-db8036768738", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0"], "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709e", "toProcessItemId": ["2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f", "2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f"], "guid": "eca200b9-f093-455c-a514-f2d8337bb92f", "versionId": "5ad0e383-dd49-4ebd-af6b-c4b316a9e113", "layoutData": {"controlPoints": {"controlPoint": {"x": "1252", "y": "216"}}, "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}, {"name": "To <PERSON>ript Task", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.7133d5ec-1b05-4fef-9bc9-1d234<PERSON><PERSON><PERSON>", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0", "2025.f43dfa61-f3b5-40a6-8c9f-d036f3dc39f0"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.668a7876-1d21-4b1f-b557-61caca4c591d", "2025.668a7876-1d21-4b1f-b557-61caca4c591d"], "guid": "59f7c263-4614-449e-9a89-e9bc8bafb001", "versionId": "7e213ffc-8b4e-4bcc-9be0-5392b1acea1f", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To MW_FC Query DC Contract", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.fce978f8-1812-4233-b4f7-1a57738f9411", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.668a7876-1d21-4b1f-b557-61caca4c591d", "2025.668a7876-1d21-4b1f-b557-61caca4c591d"], "endStateId": "Out", "toProcessItemId": ["2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc", "2025.1460dc3d-355d-44a6-8b93-3fb0763acfcc"], "guid": "ec8980f6-1d23-4a85-91e0-e5c389693be2", "versionId": "806f2dd5-842b-4130-9e71-56614aa1adfd", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "Yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.0e508126-287c-4818-a4d5-9ed719aff795", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a054c73c-8d25-4717-b21d-e82248d07389", "2025.a054c73c-8d25-4717-b21d-e82248d07389"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e", "2025.8e8fc976-8cfe-45cb-ba5e-ab164ab7313e"], "guid": "*************-41a3-ab22-897784d01617", "versionId": "87a13b2c-f4b9-4584-8d8c-6e51ec405750", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Init SQL", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.579663fd-3375-4b37-b504-6a9b1c6b4a0d", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a6129d38-34a3-4623-9a9e-d8e404c126d8", "2025.a6129d38-34a3-4623-9a9e-d8e404c126d8"], "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-70a0", "toProcessItemId": ["2025.e363cb85-8a6a-4690-a741-9dfb5644a68c", "2025.e363cb85-8a6a-4690-a741-9dfb5644a68c"], "guid": "ae069443-4028-417b-aa8a-e746e317bd33", "versionId": "8fd2aa5b-8db8-4b8a-ac11-ff526027055c", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To is totally paid", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.e5b594bb-1c9d-43af-869b-a5c91f34841a", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.83b89cca-8a42-4643-a747-1ac33270f64f", "2025.83b89cca-8a42-4643-a747-1ac33270f64f"], "endStateId": "guid:a91d9eefd459fbb7:-4c57f51d:18a93023968:7c3a", "toProcessItemId": ["2025.a6129d38-34a3-4623-9a9e-d8e404c126d8", "2025.a6129d38-34a3-4623-9a9e-d8e404c126d8"], "guid": "537f8c65-d814-408a-b162-422c67747fd6", "versionId": "91a43ad9-1083-47fa-8ce4-10a8377c6a61", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To has running request", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.275c1f3e-9dfd-4b82-9fe4-bb0bd920b35c", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc"], "endStateId": "Out", "toProcessItemId": ["2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6"], "guid": "ae22bd8d-4ea7-476d-8268-a605a891ee96", "versionId": "a7c64e21-4af0-45bf-86e3-80b1f4fc967d", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To Get Request Number And CBE", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.71d3970a-26ec-4593-9335-1668563981c9", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6"], "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709f", "toProcessItemId": ["2025.21bf961b-7097-4d5b-b1b0-25a6ab444787", "2025.21bf961b-7097-4d5b-b1b0-25a6ab444787"], "guid": "60a9cf11-347f-4210-a065-ae4438331b7a", "versionId": "b7f9c566-4896-4a82-ae0a-02c0ed56cde3", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "No", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.677b0d10-7d5a-47fe-9d7e-d1a82ee93bb5", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a054c73c-8d25-4717-b21d-e82248d07389", "2025.a054c73c-8d25-4717-b21d-e82248d07389"], "endStateId": "guid:266f6f4955d8489f:7b0b9b81:18b66504d45:-709d", "toProcessItemId": ["2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5", "2025.dd0c11e8-8975-4abd-9d0a-a7d03a5534b5"], "guid": "8813801b-390f-4554-98c8-4c23b783279a", "versionId": "c656d09e-0b00-4f48-ac6c-c600485901ba", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To SQL Execute Statement (SQLResult)", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.58fe3ee2-f994-474d-b7fa-d4d109f8f618", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.e363cb85-8a6a-4690-a741-9dfb5644a68c", "2025.e363cb85-8a6a-4690-a741-9dfb5644a68c"], "endStateId": "Out", "toProcessItemId": ["2025.b493bed2-487f-4a47-abb3-d51787b640ad", "2025.b493bed2-487f-4a47-abb3-d51787b640ad"], "guid": "85d4020a-eeb3-433d-999e-b2a163eb69b5", "versionId": "cd8c92c0-ec47-422b-9815-21a97b3bdf17", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To check has running request", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.0988eeb5-ca4e-483f-9ca9-db7465042886", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.b493bed2-487f-4a47-abb3-d51787b640ad", "2025.b493bed2-487f-4a47-abb3-d51787b640ad"], "endStateId": "guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7", "toProcessItemId": ["2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc", "2025.d08974c7-c6bd-4c5f-b01a-19033aea0dbc"], "guid": "38dcdabf-978a-4c28-962f-0028a71423be", "versionId": "e6a17a52-3c0f-4134-b6c0-ba90954a40f1", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.ae512094-37f9-40cd-b0af-aad4c8973892", "processId": "1.545f4988-f748-4797-90c9-d2c7c8a98082", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6", "2025.36856bd5-8a6d-4350-8553-79c8ef9bd4b6"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f", "2025.c2d05175-f6ad-4008-ae10-591e4ee7d02f"], "guid": "ae879bdf-734e-48fa-b8b6-192d53657bd6", "versionId": "efb56890-001f-4108-97ef-ac1a4f2ac88e", "layoutData": {"controlPoints": {"controlPoint": {"x": "1432", "y": "163"}}, "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "bottomCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}