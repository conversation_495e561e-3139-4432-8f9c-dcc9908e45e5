<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.2f8e9d68-4adb-42dd-a9f6-634969db03dc" name="Check GL Account">
        <lastModified>*************</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.030712f1-8181-4f28-8e6a-67f9083be3ef</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>false</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:1168</guid>
        <versionId>b6b36f0f-af11-4144-aa46-d39750902231</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:6f76" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.13bceef6-2832-40b0-81ae-b3b449b79b00"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"1c3dd1df-23e0-47d1-861d-fcb30256828f"},{"incoming":["880f83fc-c19b-4283-82a8-e4d6366b29aa","766d034e-d7cc-4745-8394-4704eedca435"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:40c0eab6badb64a3:-743a715:18a5af65fe2:116a"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"********-55ce-4007-8b78-a41563dd607a"},{"targetRef":"030712f1-8181-4f28-8e6a-67f9083be3ef","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Check GL Account","declaredType":"sequenceFlow","id":"2027.13bceef6-2832-40b0-81ae-b3b449b79b00","sourceRef":"1c3dd1df-23e0-47d1-861d-fcb30256828f"},{"startQuantity":1,"outgoing":["880f83fc-c19b-4283-82a8-e4d6366b29aa"],"incoming":["2027.13bceef6-2832-40b0-81ae-b3b449b79b00"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":268,"y":57,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Check GL Account","dataInputAssociation":[{"targetRef":"2055.4d5b4910-bd3a-47f4-8c11-66d8090a2e50","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.c18a6899-6013-4345-8ce9-739a16ed9634","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.data"]}}]},{"targetRef":"2055.0126d85c-445e-4538-8a6e-601eb37de77a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user_id"]}}]},{"targetRef":"2055.9f586901-6b8c-4c19-83f1-35cea5db49bb","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"OUTWARD DOCUMENT COLLECTION\""]}}]},{"targetRef":"2055.a520c6ba-6e3b-4054-8f5f-1988df4c9f7c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression"}}]},{"targetRef":"2055.ed1918e4-41d8-403a-8849-7849e628fc5f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.9b6a1701-f2e0-4f28-8be3-23c434bd009b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.model.processApp.currentSnapshot.name"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"030712f1-8181-4f28-8e6a-67f9083be3ef","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.c2601476-984a-4451-8b74-9e89fd877490"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMessage"]}}],"sourceRef":["2055.90a47e75-99e0-4027-8e62-c28e5c779d17"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorCode"]}}],"sourceRef":["2055.dc3d81e0-f34c-4006-8a4a-9b4a01525cf0"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isSuccessful"]}}],"sourceRef":["2055.f2d60f9e-6b2b-4c46-8e1e-92afe56fe880"]}],"calledElement":"1.8d5bbf92-4c72-4227-82b8-0d9a3444fcf2"},{"targetRef":"********-55ce-4007-8b78-a41563dd607a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:4532aa1aa99c10af:490ccb33:188befc8bcd:-218"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"880f83fc-c19b-4283-82a8-e4d6366b29aa","sourceRef":"030712f1-8181-4f28-8e6a-67f9083be3ef"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMessage","isCollection":false,"declaredType":"dataObject","id":"2056.c82db5c8-9708-4619-8e1b-af62a7b949bd"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorCode","isCollection":false,"declaredType":"dataObject","id":"2056.05407489-af34-4484-8e44-8f68cb4528f4"},{"parallelMultiple":false,"outgoing":["aa112340-9533-477e-820c-fbda03419288"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"e7ea8ea8-a23f-4736-82f3-198e6b4f3297"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"c2c52c8c-7191-43cf-8efd-3bdbdfa563e1","otherAttributes":{"eventImplId":"7601ddee-8031-4853-81bd-052fb4fe5caa"}}],"attachedToRef":"030712f1-8181-4f28-8e6a-67f9083be3ef","extensionElements":{"nodeVisualInfo":[{"width":24,"x":303,"y":115,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"e14ff068-3b41-42dd-8d64-d96ede80b711","outputSet":{}},{"startQuantity":1,"outgoing":["766d034e-d7cc-4745-8394-4704eedca435"],"incoming":["aa112340-9533-477e-820c-fbda03419288"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":440,"y":176,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"0a444c69-41c2-43ad-86ae-e07a341c3606","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.errorMessage!=null)\r\n{\t\r\n\tlog.info(\"=============================ODC ================================================================\");\r\n\tlog.info(\"=============================Start Service -&gt;  Check GL Account ===========================================\");\r\n\tlog.info(\"=======================Instace Id= \"+tw.system.currentProcessInstanceID +\" :: Error Message: \"+ tw.local.errorMessage);\r\n\tlog.info(\"=============================End Service -&gt;  Check GL Account ===========================================\");\r\n\t}"]}},{"targetRef":"0a444c69-41c2-43ad-86ae-e07a341c3606","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"aa112340-9533-477e-820c-fbda03419288","sourceRef":"e14ff068-3b41-42dd-8d64-d96ede80b711"},{"targetRef":"********-55ce-4007-8b78-a41563dd607a","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"766d034e-d7cc-4745-8394-4704eedca435","sourceRef":"0a444c69-41c2-43ad-86ae-e07a341c3606"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isSuccessful","isCollection":false,"declaredType":"dataObject","id":"2056.33a2430f-e2bc-4362-8cf1-ffbbeb333a0a"}],"laneSet":[{"id":"7ef638ee-097a-48ba-831d-c4e9ff3bd3ea","lane":[{"flowNodeRef":["1c3dd1df-23e0-47d1-861d-fcb30256828f","********-55ce-4007-8b78-a41563dd607a","030712f1-8181-4f28-8e6a-67f9083be3ef","e14ff068-3b41-42dd-8d64-d96ede80b711","0a444c69-41c2-43ad-86ae-e07a341c3606"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"40818d79-73ed-4998-84e4-502fd709e300","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[false],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Check GL Account","declaredType":"process","id":"1.2f8e9d68-4adb-42dd-a9f6-634969db03dc","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","name":"results","isCollection":false,"id":"2055.764e5fb7-c759-42cb-8ea5-89dfcdc52ea8"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"id":"2055.27ced369-7d58-452e-81f2-453694a2aa50"}],"inputSet":[{}],"outputSet":[{}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\/\/\"11111LC05\"\r\n\"222BILC05\""}]},"itemSubjectRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","documentation":[{"content":["&amp;quot;222BILC05&amp;quot;"],"textFormat":"text\/plain"}],"name":"data","isCollection":false,"id":"2055.02936e54-2f72-43eb-891c-4ca9d49a0705"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="data">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.02936e54-2f72-43eb-891c-4ca9d49a0705</processParameterId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>//"11111LC05"&#xD;
"222BILC05"</defaultValue>
            <isLocked>false</isLocked>
            <description>&amp;quot;222BILC05&amp;quot;</description>
            <guid>9d9bd32b-56a5-47cc-906a-fb4fdb120309</guid>
            <versionId>86e03939-4394-4e17-ac01-17538295148a</versionId>
        </processParameter>
        <processParameter name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.764e5fb7-c759-42cb-8ea5-89dfcdc52ea8</processParameterId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.c09c9b6e-aabd-4897-bef2-ed61db106297</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>0c67ebd1-de9a-458f-8f89-959b0d69a6fc</guid>
            <versionId>d06e69b8-8ca3-4f90-b0f0-8896ec194b7a</versionId>
        </processParameter>
        <processParameter name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.27ced369-7d58-452e-81f2-453694a2aa50</processParameterId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b6402a98-de28-4d2b-bd71-64e608eefda8</guid>
            <versionId>ada8f68f-6c9f-4b92-842c-62a067745d5f</versionId>
        </processParameter>
        <processVariable name="errorMessage">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.c82db5c8-9708-4619-8e1b-af62a7b949bd</processVariableId>
            <description isNull="true" />
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bc5c7901-13ba-467f-af12-15fc77f0012e</guid>
            <versionId>852c9303-864e-41c6-b703-043874271152</versionId>
        </processVariable>
        <processVariable name="errorCode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.05407489-af34-4484-8e44-8f68cb4528f4</processVariableId>
            <description isNull="true" />
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>219cf0ef-7733-463a-8f34-c38cd317e879</guid>
            <versionId>ae868106-62c6-4e9d-a1b1-f8fef50b237b</versionId>
        </processVariable>
        <processVariable name="isSuccessful">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.33a2430f-e2bc-4362-8cf1-ffbbeb333a0a</processVariableId>
            <description isNull="true" />
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a3b45bcd-3fc0-451a-a743-4aee802546b0</guid>
            <versionId>e2d930f2-3840-4465-a146-2798f78b645c</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.********-55ce-4007-8b78-a41563dd607a</processItemId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.d1b6cee7-85d6-451f-9199-5b91a822832e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:116a</guid>
            <versionId>244ff341-dc98-4dcc-b1a2-9f19db89baa7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.d1b6cee7-85d6-451f-9199-5b91a822832e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>35c2c754-e9e7-4336-acf9-1671a46d59a2</guid>
                <versionId>********-cc8f-4a5a-afd7-7eae92064135</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.030712f1-8181-4f28-8e6a-67f9083be3ef</processItemId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <name>Check GL Account</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.0a444c69-41c2-43ad-86ae-e07a341c3606</errorHandlerItemId>
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:117c</guid>
            <versionId>40a5b6f7-9208-474b-b621-92e60ba31066</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="268" y="57">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:1284</errorHandlerItem>
                <errorHandlerItemId>2025.0a444c69-41c2-43ad-86ae-e07a341c3606</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="leftCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.8d5bbf92-4c72-4227-82b8-0d9a3444fcf2</attachedProcessRef>
                <guid>ab952030-184c-4fad-9a78-5972f0f64ee3</guid>
                <versionId>b8ca766c-4e09-4068-b2e3-4328d8e59827</versionId>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.29454ef7-2b62-4bfd-97ac-6b31c0c6670d</parameterMappingId>
                    <processParameterId>2055.9f586901-6b8c-4c19-83f1-35cea5db49bb</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"OUTWARD DOCUMENT COLLECTION"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>baf552e6-bf66-4027-ae5b-7f95bf7bd3b0</guid>
                    <versionId>0e2e8d1c-5e15-4be7-b266-686b3b0811bb</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c35f9069-fa62-41ad-b9ea-e1752de2bb0b</parameterMappingId>
                    <processParameterId>2055.dc3d81e0-f34c-4006-8a4a-9b4a01525cf0</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorCode</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>4833b313-7667-4575-b32a-ba36b52e129d</guid>
                    <versionId>0f8936f1-aaec-4be0-9477-23c31ee1925f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e48f00c9-49b0-4b0a-b4ef-35cf19690e3c</parameterMappingId>
                    <processParameterId>2055.a520c6ba-6e3b-4054-8f5f-1988df4c9f7c</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>true</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d206abbd-6bdb-4bbe-b06f-1ad56e04dd31</guid>
                    <versionId>28d361b7-daa1-46de-be0e-e6e11e1b30a9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0702d371-512c-408d-a6c2-f4474150ea36</parameterMappingId>
                    <processParameterId>2055.4d5b4910-bd3a-47f4-8c11-66d8090a2e50</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9a43f7bf-ec12-4315-8355-f93a4c294ae5</guid>
                    <versionId>41cbc2ec-06bd-4e96-88a3-fa6862fae618</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isFound">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6fc1aa81-03fc-4a67-afbb-f8b96d6fcccf</parameterMappingId>
                    <processParameterId>2055.c2601476-984a-4451-8b74-9e89fd877490</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>b7414577-c9b2-41a5-af2c-423198e95ab1</guid>
                    <versionId>7a4788e3-8b20-4528-be08-73a0f8658341</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f69b22dc-34b7-45fe-b056-9c18977da2a1</parameterMappingId>
                    <processParameterId>2055.0126d85c-445e-4538-8a6e-601eb37de77a</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>6aa3a1ac-acea-45ed-b24c-aac369da11a6</guid>
                    <versionId>c814c7d7-161a-41cb-be6b-4c44db137a40</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="glAccountNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7928c033-19e5-43ec-8594-c4dbc1a81fa4</parameterMappingId>
                    <processParameterId>2055.c18a6899-6013-4345-8ce9-739a16ed9634</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.data</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>c8b31afc-38a9-4267-bb7e-883698bbc24d</guid>
                    <versionId>e2ab1208-e49e-4333-9c1e-d82bf8fd9315</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.7ae4889d-2ca2-4127-b913-4e9e6feff658</parameterMappingId>
                    <processParameterId>2055.ed1918e4-41d8-403a-8849-7849e628fc5f</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>2eff64ca-152a-4c3c-ad6a-a233e29fb3df</guid>
                    <versionId>e33757fe-3a06-4e95-a472-2e471a8d7a4f</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.884a7360-b375-457b-b0db-1a2d59ced532</parameterMappingId>
                    <processParameterId>2055.f2d60f9e-6b2b-4c46-8e1e-92afe56fe880</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.isSuccessful</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>df47d2a6-398d-4ab6-8c47-8376d55d8297</guid>
                    <versionId>e953b587-1fce-4707-96d2-823fa3049015</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f01ae081-1505-4a14-a0d9-ec4b2f9d2abf</parameterMappingId>
                    <processParameterId>2055.9b6a1701-f2e0-4f28-8be3-23c434bd009b</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f83d0aca-b635-405b-8ea1-b99951af5c58</guid>
                    <versionId>eb03ba1d-72e8-4b4f-adae-bcf211b1df89</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c2bfc8fa-dcc3-42e4-b2d1-111044b9e501</parameterMappingId>
                    <processParameterId>2055.90a47e75-99e0-4027-8e62-c28e5c779d17</processParameterId>
                    <parameterMappingParentId>3012.c674f2fb-c549-456b-9af4-04e39c14a224</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMessage</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>c4009770-e529-44d5-b29e-9b7b06b1d26c</guid>
                    <versionId>f328afd0-a599-4cef-8fa5-69ff6eaf2cbb</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.0a444c69-41c2-43ad-86ae-e07a341c3606</processItemId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.0e1a7799-3feb-4179-9622-a98d5360d506</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:1284</guid>
            <versionId>f1182561-5d00-4961-8184-e7781992c307</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="440" y="176">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.0e1a7799-3feb-4179-9622-a98d5360d506</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.errorMessage!=null)&#xD;
{	&#xD;
	log.info("=============================ODC ================================================================");&#xD;
	log.info("=============================Start Service -&gt;  Check GL Account ===========================================");&#xD;
	log.info("=======================Instace Id= "+tw.system.currentProcessInstanceID +" :: Error Message: "+ tw.local.errorMessage);&#xD;
	log.info("=============================End Service -&gt;  Check GL Account ===========================================");&#xD;
	}</script>
                <isRule>false</isRule>
                <guid>4ac4bd23-6287-4e11-9127-84eea83684d3</guid>
                <versionId>fe626637-7fd1-4831-910c-e66e8b3fbe4c</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.030712f1-8181-4f28-8e6a-67f9083be3ef</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Check GL Account" id="1.2f8e9d68-4adb-42dd-a9f6-634969db03dc" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                        
                        <ns3:isSecured>false</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="data" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.02936e54-2f72-43eb-891c-4ca9d49a0705">
                            
                            
                            <ns16:documentation textFormat="text/plain">&amp;quot;222BILC05&amp;quot;</ns16:documentation>
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">//"11111LC05"&#xD;
"222BILC05"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="results" itemSubjectRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" isCollection="false" id="2055.764e5fb7-c759-42cb-8ea5-89dfcdc52ea8" />
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" id="2055.27ced369-7d58-452e-81f2-453694a2aa50" />
                        
                        
                        <ns16:inputSet />
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="7ef638ee-097a-48ba-831d-c4e9ff3bd3ea">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="40818d79-73ed-4998-84e4-502fd709e300" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>1c3dd1df-23e0-47d1-861d-fcb30256828f</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>********-55ce-4007-8b78-a41563dd607a</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>030712f1-8181-4f28-8e6a-67f9083be3ef</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>e14ff068-3b41-42dd-8d64-d96ede80b711</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>0a444c69-41c2-43ad-86ae-e07a341c3606</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="1c3dd1df-23e0-47d1-861d-fcb30256828f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.13bceef6-2832-40b0-81ae-b3b449b79b00</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="********-55ce-4007-8b78-a41563dd607a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:116a</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>880f83fc-c19b-4283-82a8-e4d6366b29aa</ns16:incoming>
                        
                        
                        <ns16:incoming>766d034e-d7cc-4745-8394-4704eedca435</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="1c3dd1df-23e0-47d1-861d-fcb30256828f" targetRef="030712f1-8181-4f28-8e6a-67f9083be3ef" name="To Check GL Account" id="2027.13bceef6-2832-40b0-81ae-b3b449b79b00">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.8d5bbf92-4c72-4227-82b8-0d9a3444fcf2" name="Check GL Account" id="030712f1-8181-4f28-8e6a-67f9083be3ef">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="268" y="57" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.13bceef6-2832-40b0-81ae-b3b449b79b00</ns16:incoming>
                        
                        
                        <ns16:outgoing>880f83fc-c19b-4283-82a8-e4d6366b29aa</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.4d5b4910-bd3a-47f4-8c11-66d8090a2e50</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.c18a6899-6013-4345-8ce9-739a16ed9634</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.data</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0126d85c-445e-4538-8a6e-601eb37de77a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9f586901-6b8c-4c19-83f1-35cea5db49bb</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"OUTWARD DOCUMENT COLLECTION"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.a520c6ba-6e3b-4054-8f5f-1988df4c9f7c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297" />
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.ed1918e4-41d8-403a-8849-7849e628fc5f</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.9b6a1701-f2e0-4f28-8be3-23c434bd009b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.c2601476-984a-4451-8b74-9e89fd877490</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.90a47e75-99e0-4027-8e62-c28e5c779d17</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMessage</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.dc3d81e0-f34c-4006-8a4a-9b4a01525cf0</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorCode</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.f2d60f9e-6b2b-4c46-8e1e-92afe56fe880</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2">tw.local.isSuccessful</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="030712f1-8181-4f28-8e6a-67f9083be3ef" targetRef="********-55ce-4007-8b78-a41563dd607a" name="To End" id="880f83fc-c19b-4283-82a8-e4d6366b29aa">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:4532aa1aa99c10af:490ccb33:188befc8bcd:-218</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMessage" id="2056.c82db5c8-9708-4619-8e1b-af62a7b949bd" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorCode" id="2056.05407489-af34-4484-8e44-8f68cb4528f4" />
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="030712f1-8181-4f28-8e6a-67f9083be3ef" parallelMultiple="false" name="Error" id="e14ff068-3b41-42dd-8d64-d96ede80b711">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="303" y="115" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>aa112340-9533-477e-820c-fbda03419288</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="e7ea8ea8-a23f-4736-82f3-198e6b4f3297" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="c2c52c8c-7191-43cf-8efd-3bdbdfa563e1" eventImplId="7601ddee-8031-4853-81bd-052fb4fe5caa">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="0a444c69-41c2-43ad-86ae-e07a341c3606">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="440" y="176" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>aa112340-9533-477e-820c-fbda03419288</ns16:incoming>
                        
                        
                        <ns16:outgoing>766d034e-d7cc-4745-8394-4704eedca435</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.errorMessage!=null)&#xD;
{	&#xD;
	log.info("=============================ODC ================================================================");&#xD;
	log.info("=============================Start Service -&gt;  Check GL Account ===========================================");&#xD;
	log.info("=======================Instace Id= "+tw.system.currentProcessInstanceID +" :: Error Message: "+ tw.local.errorMessage);&#xD;
	log.info("=============================End Service -&gt;  Check GL Account ===========================================");&#xD;
	}</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="e14ff068-3b41-42dd-8d64-d96ede80b711" targetRef="0a444c69-41c2-43ad-86ae-e07a341c3606" name="To Script Task" id="aa112340-9533-477e-820c-fbda03419288">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="0a444c69-41c2-43ad-86ae-e07a341c3606" targetRef="********-55ce-4007-8b78-a41563dd607a" name="To End" id="766d034e-d7cc-4745-8394-4704eedca435">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isSuccessful" id="2056.33a2430f-e2bc-4362-8cf1-ffbbeb333a0a" />
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.880f83fc-c19b-4283-82a8-e4d6366b29aa</processLinkId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.030712f1-8181-4f28-8e6a-67f9083be3ef</fromProcessItemId>
            <endStateId>guid:4532aa1aa99c10af:490ccb33:188befc8bcd:-218</endStateId>
            <toProcessItemId>2025.********-55ce-4007-8b78-a41563dd607a</toProcessItemId>
            <guid>2cf24d12-a414-444a-bcf0-a0d3750e1319</guid>
            <versionId>99de676a-9e65-4783-bfe1-8f5aedd3d94f</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.030712f1-8181-4f28-8e6a-67f9083be3ef</fromProcessItemId>
            <toProcessItemId>2025.********-55ce-4007-8b78-a41563dd607a</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.766d034e-d7cc-4745-8394-4704eedca435</processLinkId>
            <processId>1.2f8e9d68-4adb-42dd-a9f6-634969db03dc</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.0a444c69-41c2-43ad-86ae-e07a341c3606</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.********-55ce-4007-8b78-a41563dd607a</toProcessItemId>
            <guid>aa47ec1a-f7f9-4dc6-aff6-cfebcb7c5563</guid>
            <versionId>ac2254e8-1240-4acc-8f86-7e311ab71605</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.0a444c69-41c2-43ad-86ae-e07a341c3606</fromProcessItemId>
            <toProcessItemId>2025.********-55ce-4007-8b78-a41563dd607a</toProcessItemId>
        </link>
    </process>
</teamworks>

