<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f" name="Get Exchange Rate">
        <lastModified>1699441074769</lastModified>
        <lastModifiedBy>heba</lastModifiedBy>
        <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.2c787a27-c641-4f46-8452-98687e350d32</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
        <errorHandlerItemId>2025.9d74f5c4-b95e-4f30-81d3-2b6bd12177f3</errorHandlerItemId>
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:5566</guid>
        <versionId>b9d3a129-d9ba-4e97-91d9-e9e67aa01b6d</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:651a1a6abf396537:64776e00:18baeba64af:-7b35" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.a0d330ec-3bbb-40e5-8e8e-b1ab040a34e2"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":25,"y":80,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"f5ec89c6-adc8-4ca4-804c-3010ccee8062"},{"incoming":["815febda-5b69-46fc-8238-************","b9b15ff9-de80-4487-862f-770285e7677a"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":650,"y":80,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:5568"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"3f377147-f1f0-4a04-8f60-dfe3e9e6004e"},{"targetRef":"2c787a27-c641-4f46-8452-98687e350d32","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To currencies are the same?","declaredType":"sequenceFlow","id":"2027.a0d330ec-3bbb-40e5-8e8e-b1ab040a34e2","sourceRef":"f5ec89c6-adc8-4ca4-804c-3010ccee8062"},{"startQuantity":1,"outgoing":["815febda-5b69-46fc-8238-************"],"incoming":["c93ce659-7a03-4e3a-85eb-4b8dbec32f62"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":455,"y":104,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Map output","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"615de557-0068-4ad6-887a-1552a432c4d8","scriptFormat":"text\/x-javascript","script":{"content":["if(tw.local.convertedAmount != null)\r\n{\r\ntw.local.rate = tw.local.convertedAmount\/tw.local.initialAmount;\r\n}\r\n"]}},{"targetRef":"3f377147-f1f0-4a04-8f60-dfe3e9e6004e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"bottomCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"815febda-5b69-46fc-8238-************","sourceRef":"615de557-0068-4ad6-887a-1552a432c4d8"},{"itemSubjectRef":"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45","name":"error","isCollection":false,"declaredType":"dataObject","id":"2056.883b34b1-3d47-40d9-86c3-8c278c0f0a2f"},{"startQuantity":1,"outgoing":["c93ce659-7a03-4e3a-85eb-4b8dbec32f62"],"incoming":["63438792-f3ba-4bdd-801c-96a140a22ce6"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":172,"y":104,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Get equivilant amount","dataInputAssociation":[{"targetRef":"2055.f532fd4f-b514-4927-8cce-fd794f488e0d","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.prefix"]}}]},{"targetRef":"2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.fromCurrency"]}}]},{"targetRef":"2055.282350b1-2727-4aa1-8118-3d5c3316136a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.toCurrency"]}}]},{"targetRef":"2055.497359ed-14f4-4269-82b7-f09ee83dd3dc","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\/\/tw.system.user_id"]}}]},{"targetRef":"2055.94fc6012-982f-4612-82c9-77bc1256e0b1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\/\/tw.system.currentProcessInstanceID"]}}]},{"targetRef":"2055.293d7351-d3dc-48dc-896c-14e707fd7cba","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"ODC creation and amendment\""]}}]},{"targetRef":"2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\/\/tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.359d19b4-5948-47d2-814d-d3bbc031e650","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["\/\/tw.system.model.processApp.currentSnapshot.name"]}}]},{"targetRef":"2055.6acacd41-404d-41cd-8b73-a6a3f53de59c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.rateType"]}}]},{"targetRef":"2055.*************-4e4c-8a23-3a8758392285","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.rateSubType"]}}]},{"targetRef":"2055.0427603a-263e-49f1-8ebc-12396435a8f9","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["String(tw.local.initialAmount)"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"a8536eb1-c1d0-44eb-8bba-aa44ae7a8248","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","declaredType":"TFormalExpression","content":[" tw.local.convertedAmount"]}}],"sourceRef":["2055.122b436b-8719-4a3f-81ab-c7897c8c2554"]}],"calledElement":"1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de"},{"targetRef":"615de557-0068-4ad6-887a-1552a432c4d8","extensionElements":{"endStateId":["guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To get exchange rate","declaredType":"sequenceFlow","id":"c93ce659-7a03-4e3a-85eb-4b8dbec32f62","sourceRef":"a8536eb1-c1d0-44eb-8bba-aa44ae7a8248"},{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"convertedAmount","isCollection":false,"declaredType":"dataObject","id":"2056.472f9158-8433-4ad7-82e0-6fe3879ed862"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"TRANSFER\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateType","isCollection":false,"declaredType":"dataObject","id":"2056.cb8589eb-101e-49dd-8ffa-2288f701d4f1"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"S\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"rateSubType","isCollection":false,"declaredType":"dataObject","id":"2056.08511d76-76e7-47cd-8cda-5e0402e5ee11"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"100"}]},"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"initialAmount","isCollection":false,"declaredType":"dataObject","id":"2056.069f6cae-9bed-4704-8185-8cf932e33cfc"},{"outgoing":["63438792-f3ba-4bdd-801c-96a140a22ce6","a28d4fd9-1c2e-45f6-81d5-43be248dd6b2"],"incoming":["2027.a0d330ec-3bbb-40e5-8e8e-b1ab040a34e2"],"default":"63438792-f3ba-4bdd-801c-96a140a22ce6","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":72,"y":76,"declaredType":"TNodeVisualInfo","height":32}]},"name":"currencies are the same?","declaredType":"exclusiveGateway","id":"2c787a27-c641-4f46-8452-98687e350d32"},{"targetRef":"a8536eb1-c1d0-44eb-8bba-aa44ae7a8248","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Get equivilant amount","declaredType":"sequenceFlow","id":"63438792-f3ba-4bdd-801c-96a140a22ce6","sourceRef":"2c787a27-c641-4f46-8452-98687e350d32"},{"startQuantity":1,"outgoing":["b9b15ff9-de80-4487-862f-770285e7677a"],"incoming":["a28d4fd9-1c2e-45f6-81d5-43be248dd6b2"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":301,"y":12,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Set rate","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"c28f21a6-126e-4ff6-81b7-db8230367867","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.rate = 1.0;"]}},{"targetRef":"c28f21a6-126e-4ff6-81b7-db8230367867","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.fromCurrency\t  ==\t  tw.local.toCurrency"]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}]},"name":"To Set rate","declaredType":"sequenceFlow","id":"a28d4fd9-1c2e-45f6-81d5-43be248dd6b2","sourceRef":"2c787a27-c641-4f46-8452-98687e350d32"},{"targetRef":"3f377147-f1f0-4a04-8f60-dfe3e9e6004e","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"Set rate","declaredType":"sequenceFlow","id":"b9b15ff9-de80-4487-862f-770285e7677a","sourceRef":"c28f21a6-126e-4ff6-81b7-db8230367867"},{"startQuantity":1,"outgoing":["bb0944b8-a5de-4cb9-845e-2455fe481edf"],"incoming":["799d512d-3793-4a4d-856d-7fd6420a15a1"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":330,"y":195,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Exception Handling","dataInputAssociation":[{"targetRef":"2055.5ea77901-7a17-422a-8958-67bb0e9c991b","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"Get Exchange Rate\""]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"9d74f5c4-b95e-4f30-81d3-2b6bd12177f3","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}],"sourceRef":["2055.81b82125-a5aa-45f7-8c0f-4870667eebbf"]}],"calledElement":"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0"},{"parallelMultiple":false,"outgoing":["799d512d-3793-4a4d-856d-7fd6420a15a1"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"67c532b6-9557-4655-8490-968c5da002f8","otherAttributes":{"eventImplId":"377507bc-b4aa-4dbc-8f66-aa198c403b6f"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":213,"y":218,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Error Event","declaredType":"intermediateCatchEvent","id":"7ad706f3-5dae-4356-8721-1440c59c5b3e"},{"incoming":["bb0944b8-a5de-4cb9-845e-2455fe481edf"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"1cf79145-962e-472c-84f8-fc3c309e8664","otherAttributes":{"eventImplId":"9ebfb275-c9c4-4acf-8d9e-fef59c7d0af0"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":490,"y":218,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"88ec9872-d5bb-4982-819c-72397bc01ddf"},{"targetRef":"9d74f5c4-b95e-4f30-81d3-2b6bd12177f3","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Exception Handling","declaredType":"sequenceFlow","id":"799d512d-3793-4a4d-856d-7fd6420a15a1","sourceRef":"7ad706f3-5dae-4356-8721-1440c59c5b3e"},{"targetRef":"88ec9872-d5bb-4982-819c-72397bc01ddf","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"bb0944b8-a5de-4cb9-845e-2455fe481edf","sourceRef":"9d74f5c4-b95e-4f30-81d3-2b6bd12177f3"}],"laneSet":[{"id":"87e2fb34-9f8d-46af-8d51-e4eae2ccc020","lane":[{"flowNodeRef":["f5ec89c6-adc8-4ca4-804c-3010ccee8062","3f377147-f1f0-4a04-8f60-dfe3e9e6004e","615de557-0068-4ad6-887a-1552a432c4d8","a8536eb1-c1d0-44eb-8bba-aa44ae7a8248","2c787a27-c641-4f46-8452-98687e350d32","c28f21a6-126e-4ff6-81b7-db8230367867","9d74f5c4-b95e-4f30-81d3-2b6bd12177f3","7ad706f3-5dae-4356-8721-1440c59c5b3e","88ec9872-d5bb-4982-819c-72397bc01ddf"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"a0dd3a5e-becf-49f9-8cd9-ac050a4e6f50","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Get Exchange Rate","declaredType":"process","id":"1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"dataOutput":[{"itemSubjectRef":"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee","name":"rate","isCollection":false,"id":"2055.a1cc30d0-50bc-4feb-8427-9f8aafe9ef96"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"id":"2055.7e0973d8-72ed-4b76-81df-4cda85422d2c"}],"inputSet":[{"dataInputRefs":["2055.4f75807d-191a-4553-809f-630208b0d737","2055.cc3906a0-dd9d-4580-8485-14caef94ac8d"]}],"outputSet":[{"dataOutputRefs":["2055.a1cc30d0-50bc-4feb-8427-9f8aafe9ef96","2055.7e0973d8-72ed-4b76-81df-4cda85422d2c"]}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"USD\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"fromCurrency","isCollection":false,"id":"2055.4f75807d-191a-4553-809f-630208b0d737"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"EGP\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"toCurrency","isCollection":false,"id":"2055.cc3906a0-dd9d-4580-8485-14caef94ac8d"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="fromCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.4f75807d-191a-4553-809f-630208b0d737</processParameterId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"USD"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d6bd5f4b-e7e7-4020-8f08-6b6506a3647d</guid>
            <versionId>918899fa-9cc6-4a51-a380-************</versionId>
        </processParameter>
        <processParameter name="toCurrency">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.cc3906a0-dd9d-4580-8485-14caef94ac8d</processParameterId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>"EGP"</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>d87393af-7abc-4ac1-9986-bee06a8413d2</guid>
            <versionId>ef86f3ab-36a6-48a2-9f12-dc58bc9d2b4f</versionId>
        </processParameter>
        <processParameter name="rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a1cc30d0-50bc-4feb-8427-9f8aafe9ef96</processParameterId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <seq>3</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>907da855-e0a7-4940-af1b-104d64302500</guid>
            <versionId>5c281d97-918d-4fbe-874a-f2525ccc90ac</versionId>
        </processParameter>
        <processParameter name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7e0973d8-72ed-4b76-81df-4cda85422d2c</processParameterId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <parameterType>2</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2b4270c7-14b1-4c7f-af5d-a80dcba792ca</guid>
            <versionId>da079c4e-f829-4396-aa15-75ee6e629870</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.60951883-bddb-435f-aee5-fa7887f420ef</processParameterId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>f999442f-a290-4a64-ad8e-fa62bb24b582</guid>
            <versionId>23217cb0-c356-44f9-8e4e-abe59a659cf4</versionId>
        </processParameter>
        <processVariable name="error">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.883b34b1-3d47-40d9-86c3-8c278c0f0a2f</processVariableId>
            <description isNull="true" />
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>2aa43fbc-ee93-4c0a-ad91-6e9d16d433db</guid>
            <versionId>05ed1d47-c366-42ea-9dcf-b4013709beca</versionId>
        </processVariable>
        <processVariable name="convertedAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.472f9158-8433-4ad7-82e0-6fe3879ed862</processVariableId>
            <description isNull="true" />
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4296657f-1d00-4296-a2b0-fc35b631f3eb</guid>
            <versionId>f762eb25-90f5-48cb-a7d4-628ccc0e355b</versionId>
        </processVariable>
        <processVariable name="rateType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.cb8589eb-101e-49dd-8ffa-2288f701d4f1</processVariableId>
            <description isNull="true" />
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>"TRANSFER"</defaultValue>
            <guid>9a2d4ed0-6a6e-4aee-9a05-76b38042e22b</guid>
            <versionId>c4bfb482-8d1b-4982-894c-059bbe269908</versionId>
        </processVariable>
        <processVariable name="rateSubType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.08511d76-76e7-47cd-8cda-5e0402e5ee11</processVariableId>
            <description isNull="true" />
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>"S"</defaultValue>
            <guid>962b046f-0484-4468-b3b3-491f4ba6347a</guid>
            <versionId>0f16be08-5265-4057-ae2c-6ffa11b0af71</versionId>
        </processVariable>
        <processVariable name="initialAmount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.069f6cae-9bed-4704-8185-8cf932e33cfc</processVariableId>
            <description isNull="true" />
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classId>
            <hasDefault>true</hasDefault>
            <defaultValue>100</defaultValue>
            <guid>913b49a0-0293-4103-b202-223b73abec3a</guid>
            <versionId>43a9585a-aa1a-4efc-93cc-67686e034440</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.9d74f5c4-b95e-4f30-81d3-2b6bd12177f3</processItemId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <name>Exception Handling</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.475e3993-2106-4323-adec-68c81ca6049e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cde91268c0d1a44e:-10e36a59:18badede6df:-3c2d</guid>
            <versionId>2b1f9911-466e-4e0b-a66f-f010aac75ac1</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="330" y="195">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.475e3993-2106-4323-adec-68c81ca6049e</subProcessId>
                <attachedProcessRef>/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0</attachedProcessRef>
                <guid>48f08e66-f1f8-436d-b99d-f2005efe55ce</guid>
                <versionId>da371fb5-**************-91e2e1711751</versionId>
                <parameterMapping name="serviceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.77a7130f-1c55-4d0b-9a38-43ae87be5f74</parameterMappingId>
                    <processParameterId>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</processParameterId>
                    <parameterMappingParentId>3012.475e3993-2106-4323-adec-68c81ca6049e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"Get Exchange Rate"</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>f18e139d-7087-48b2-b855-48490134d0b2</guid>
                    <versionId>27e46d75-a36b-4252-9ce1-d095463399fe</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e2486781-17ec-4dc5-a41e-a50b267fe8ef</parameterMappingId>
                    <processParameterId>2055.99eb514d-4b62-401e-8cdb-7edc096adffd</processParameterId>
                    <parameterMappingParentId>3012.475e3993-2106-4323-adec-68c81ca6049e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>e035096d-8ac4-4363-9f0a-4477d7679795</guid>
                    <versionId>70f5a50e-fecf-4f81-908a-bcde89e995a1</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMessage">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.324126a0-a4d2-4911-a173-e8c82d80d566</parameterMappingId>
                    <processParameterId>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</processParameterId>
                    <parameterMappingParentId>3012.475e3993-2106-4323-adec-68c81ca6049e</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>7365c873-fd24-4995-9168-676a2db43d5e</guid>
                    <versionId>7d423d12-e9fb-4a7b-9a51-d49a748c9df9</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.3f377147-f1f0-4a04-8f60-dfe3e9e6004e</processItemId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.25b6d4e8-1dd5-46b3-ad48-aea4d02d404e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:5568</guid>
            <versionId>3fc2221c-bfd1-41bc-8b40-cb2410fcc31c</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="650" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.25b6d4e8-1dd5-46b3-ad48-aea4d02d404e</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>b325d3b8-961e-4294-a6f2-df29eb7bfa2b</guid>
                <versionId>9dcc071f-01d0-4e22-ac8f-437ff1809e65</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.615de557-0068-4ad6-887a-1552a432c4d8</processItemId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <name>Map output</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.a564acbe-e3d4-4d76-a34c-a50a997a501b</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:9df5bce005da774d:-5bc6ffcb:18a1f9979de:3e7a</guid>
            <versionId>458f8be3-b660-4ed3-8bee-81fbc487c17e</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="455" y="104">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.a564acbe-e3d4-4d76-a34c-a50a997a501b</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>if(tw.local.convertedAmount != null)&#xD;
{&#xD;
tw.local.rate = tw.local.convertedAmount/tw.local.initialAmount;&#xD;
}&#xD;
</script>
                <isRule>false</isRule>
                <guid>06a8db01-55db-41cc-961f-1ddc59df5b03</guid>
                <versionId>e26709a6-1ef3-49c5-91cd-e5891c414ac8</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2c787a27-c641-4f46-8452-98687e350d32</processItemId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <name>currencies are the same?</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.ccfbef16-a264-4a99-bc38-1d1af085db7e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fec1e4ce890d6f10:-74e1bcc8:18acc97f8a5:7db9</guid>
            <versionId>4a78f592-e39e-44a8-941d-7d4bf715a514</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="72" y="76">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.ccfbef16-a264-4a99-bc38-1d1af085db7e</switchId>
                <guid>f37a6ed8-a175-49c5-8045-d864b30f98e1</guid>
                <versionId>09be9d54-cb32-4501-8bea-880ad432920e</versionId>
                <SwitchCondition>
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <switchConditionId>3014.dad88219-f84b-4290-95f3-9c6906108ea6</switchConditionId>
                    <switchId>3013.ccfbef16-a264-4a99-bc38-1d1af085db7e</switchId>
                    <seq>1</seq>
                    <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:-7b36</endStateId>
                    <condition>tw.local.fromCurrency	  ==	  tw.local.toCurrency</condition>
                    <guid>f32d79ee-a807-41fa-ad37-fae010a5bf5f</guid>
                    <versionId>cb59a4fc-125c-4275-84bd-50cf937b6887</versionId>
                </SwitchCondition>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.88ec9872-d5bb-4982-819c-72397bc01ddf</processItemId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.6a2eb2eb-**************-4c03df586453</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:cde91268c0d1a44e:-10e36a59:18badede6df:-3c2b</guid>
            <versionId>6f28e580-**************-99f9c25a27e9</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="490" y="218">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.6a2eb2eb-**************-4c03df586453</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>f6cf6787-69f6-4234-a88a-6f363ddbdcab</guid>
                <versionId>91b70c16-0ca3-4138-bab2-22adea991753</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6bc20230-**************-673a60706f89</parameterMappingId>
                    <processParameterId>2055.60951883-bddb-435f-aee5-fa7887f420ef</processParameterId>
                    <parameterMappingParentId>3007.6a2eb2eb-**************-4c03df586453</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>/12.46ab33d6-3cf8-4cd3-bb72-8258fbc340c6</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>cd60094b-70ab-4b41-b837-d8dcaeea079e</guid>
                    <versionId>11a4ec50-b0be-44c8-bd42-8497d6a915b6</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.a8536eb1-c1d0-44eb-8bba-aa44ae7a8248</processItemId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <name>Get equivilant amount</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:40c0eab6badb64a3:-743a715:18a5af65fe2:5bb</guid>
            <versionId>993450a5-5bbf-465e-903b-409a08d6bb79</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="172" y="104">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</subProcessId>
                <attachedProcessRef>e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de</attachedProcessRef>
                <guid>a5e49184-ef77-43b7-81da-a62d660c5214</guid>
                <versionId>c66bdad6-cb0d-45e9-aa80-3c02c62d0a72</versionId>
                <parameterMapping name="amount">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.239a25b2-609e-4671-99f0-a9c9f36951a1</parameterMappingId>
                    <processParameterId>2055.0427603a-263e-49f1-8ebc-12396435a8f9</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>String(tw.local.initialAmount)</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>712b0306-f13c-493e-a307-a02efef097ec</guid>
                    <versionId>345134f1-1fa6-4d59-9ae5-34c3b033f0a6</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="prefix">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.c02de3bc-971f-4c89-9233-524643013fbc</parameterMappingId>
                    <processParameterId>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.prefix</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>5f96dc3f-2bea-4996-8f7d-00e71ceea3ac</guid>
                    <versionId>352f43f9-f805-4dd8-84b1-14f7b86c289c</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="isSuccessful">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.464533fb-568b-4920-84ce-28c74ff268a6</parameterMappingId>
                    <processParameterId>2055.39a425bf-e5eb-4f6d-8588-f4d307e88aaf</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>26d2c14a-2941-4348-8a1c-d343767cbff5</guid>
                    <versionId>44787fb0-f130-483e-9f57-482963b6f5b4</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="amountResult">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.0f9777f3-54ad-4488-baf1-880f4cb7d11c</parameterMappingId>
                    <processParameterId>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value> tw.local.convertedAmount</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.536b2aa5-a30f-4eca-87fa-3a28066753ee</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>233102b9-ba1b-4a03-b20d-a6566078ec01</guid>
                    <versionId>5ae8f7a2-ec33-4ffc-b0d3-88eea7737009</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorCode">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.35013467-31cc-4ee2-932d-bdca6fff4995</parameterMappingId>
                    <processParameterId>2055.d2d11f9f-5670-4bca-8e94-a2b4bcda9a68</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>5bccb812-9561-421e-8be5-c27da5b33325</guid>
                    <versionId>6690452e-ad1f-49d8-aee7-997762f4b5c0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency2">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.457afc38-4ea2-4485-9ee5-a8bb4453041b</parameterMappingId>
                    <processParameterId>2055.282350b1-2727-4aa1-8118-3d5c3316136a</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.toCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b727e952-04b0-46f5-8716-10bfccf67fb3</guid>
                    <versionId>71666f8b-1e83-4ca4-a7fd-d0c4ba23c6da</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="errorMsg">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.cc7178b9-267e-47cc-9dab-069636b579a6</parameterMappingId>
                    <processParameterId>2055.9c0a9ef5-fc53-427e-8fc6-a7565a180d71</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value isNull="true" />
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d4996e5e-7e8a-4b1a-9252-d83e879d804e</guid>
                    <versionId>73099395-4b6a-496f-a654-df6a7c346ddd</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="processName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.b1aecb19-79a8-4c9b-8a3e-025aa4cc8dac</parameterMappingId>
                    <processParameterId>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>"ODC creation and amendment"</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>bffdf5dc-938b-4b82-b674-c6b3c9a81ce2</guid>
                    <versionId>8ee7748a-05c9-4533-b666-ff72010d4160</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e5f8ae38-7dd0-48ff-8609-145f5bfa3f62</parameterMappingId>
                    <processParameterId>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.rateType</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>51dc010f-a55d-403f-a5ce-45e2ec91e1a0</guid>
                    <versionId>9f8b206e-4f06-4d83-9342-ff67bdadfcb3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="snapshot">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.1ad45f2a-1673-45d7-9a28-0b033e5f0055</parameterMappingId>
                    <processParameterId>2055.359d19b4-5948-47d2-814d-d3bbc031e650</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>//tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9d6ce52a-3fcd-4138-bf24-20418be5b2d5</guid>
                    <versionId>ae5cc2b1-69a1-4935-8575-8beb1b76dfa9</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="instanceID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f4527d66-8018-4f80-a30d-4de2508fce58</parameterMappingId>
                    <processParameterId>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>//tw.system.currentProcessInstanceID</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>321e2ab4-1c8e-467f-9a18-61243f9a3240</guid>
                    <versionId>ba1228c7-5cc7-4f48-929f-a4ea5f48dd9e</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="requestAppID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fdad74dd-0388-433d-a356-9d69454e4604</parameterMappingId>
                    <processParameterId>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>//tw.system.model.processApp.currentSnapshot.name</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>9cdf3eec-9d8f-4ff6-aee2-8f40d5d59d0b</guid>
                    <versionId>cd42b81e-c66d-4a4a-aa5f-8ddb7c7f5472</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="rateSubType">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5b1c1c61-eddd-4380-bf74-11bfbfc15a7b</parameterMappingId>
                    <processParameterId>2055.*************-4e4c-8a23-3a8758392285</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.rateSubType</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>60fd6c8f-c9c6-4693-b330-33c846f9c2e4</guid>
                    <versionId>d6139d1b-fe31-4dcd-a319-77db622659b2</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="userID">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.fd9cf944-cb9a-4ae0-80aa-11bcfad887ca</parameterMappingId>
                    <processParameterId>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>//tw.system.user_id</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>d7f85779-0f5b-4db5-92c0-b60f85be1720</guid>
                    <versionId>dd260bab-f87a-4272-ac67-f4eff4645d6d</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="currency1">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.f085c762-be36-408c-9b54-c09efd1ad2d2</parameterMappingId>
                    <processParameterId>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</processParameterId>
                    <parameterMappingParentId>3012.11f35e13-9de8-436b-b4f4-abcb4c441ce6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.fromCurrency</value>
                    <classRef>99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>1f7faa40-ad6a-41e0-825a-691ad6289365</guid>
                    <versionId>ea77d4e9-869d-4b8c-a281-59575e6e5d52</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.c28f21a6-126e-4ff6-81b7-db8230367867</processItemId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <name>Set rate</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.67faf5a9-6f56-4761-8da5-ce4e9d86e142</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:fec1e4ce890d6f10:-74e1bcc8:18acc97f8a5:7db8</guid>
            <versionId>ee2fc2b2-1be5-4942-8f75-e41778cfb981</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="301" y="12">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.67faf5a9-6f56-4761-8da5-ce4e9d86e142</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.rate = 1.0;</script>
                <isRule>false</isRule>
                <guid>442201cd-bd57-4176-853b-d13d4cb7dace</guid>
                <versionId>020d4cfb-a8ef-427c-8aac-a8f540906f44</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.2c787a27-c641-4f46-8452-98687e350d32</startingProcessItemId>
        <errorHandlerItemId>2025.9d74f5c4-b95e-4f30-81d3-2b6bd12177f3</errorHandlerItemId>
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="25" y="80">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <globalExceptionHandler>
            <layoutData x="213" y="218">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </globalExceptionHandler>
        <globalExceptionLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </globalExceptionLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Get Exchange Rate" id="1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="fromCurrency" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.4f75807d-191a-4553-809f-630208b0d737">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"USD"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="toCurrency" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.cc3906a0-dd9d-4580-8485-14caef94ac8d">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">"EGP"</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataOutput name="rate" itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" id="2055.a1cc30d0-50bc-4feb-8427-9f8aafe9ef96" />
                        
                        
                        <ns16:dataOutput name="errorMsg" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.7e0973d8-72ed-4b76-81df-4cda85422d2c" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.4f75807d-191a-4553-809f-630208b0d737</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.cc3906a0-dd9d-4580-8485-14caef94ac8d</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet>
                            
                            
                            <ns16:dataOutputRefs>2055.a1cc30d0-50bc-4feb-8427-9f8aafe9ef96</ns16:dataOutputRefs>
                            
                            
                            <ns16:dataOutputRefs>2055.7e0973d8-72ed-4b76-81df-4cda85422d2c</ns16:dataOutputRefs>
                            
                        
                        </ns16:outputSet>
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="87e2fb34-9f8d-46af-8d51-e4eae2ccc020">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="a0dd3a5e-becf-49f9-8cd9-ac050a4e6f50" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>f5ec89c6-adc8-4ca4-804c-3010ccee8062</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>3f377147-f1f0-4a04-8f60-dfe3e9e6004e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>615de557-0068-4ad6-887a-1552a432c4d8</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>a8536eb1-c1d0-44eb-8bba-aa44ae7a8248</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2c787a27-c641-4f46-8452-98687e350d32</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>c28f21a6-126e-4ff6-81b7-db8230367867</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>9d74f5c4-b95e-4f30-81d3-2b6bd12177f3</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>7ad706f3-5dae-4356-8721-1440c59c5b3e</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>88ec9872-d5bb-4982-819c-72397bc01ddf</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="f5ec89c6-adc8-4ca4-804c-3010ccee8062">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="25" y="80" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.a0d330ec-3bbb-40e5-8e8e-b1ab040a34e2</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="3f377147-f1f0-4a04-8f60-dfe3e9e6004e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="650" y="80" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:f8bb29012246e138:-2ddc0b8b:18a17cdc259:5568</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>815febda-5b69-46fc-8238-************</ns16:incoming>
                        
                        
                        <ns16:incoming>b9b15ff9-de80-4487-862f-770285e7677a</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="f5ec89c6-adc8-4ca4-804c-3010ccee8062" targetRef="2c787a27-c641-4f46-8452-98687e350d32" name="To currencies are the same?" id="2027.a0d330ec-3bbb-40e5-8e8e-b1ab040a34e2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Map output" id="615de557-0068-4ad6-887a-1552a432c4d8">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="455" y="104" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>c93ce659-7a03-4e3a-85eb-4b8dbec32f62</ns16:incoming>
                        
                        
                        <ns16:outgoing>815febda-5b69-46fc-8238-************</ns16:outgoing>
                        
                        
                        <ns16:script>if(tw.local.convertedAmount != null)&#xD;
{&#xD;
tw.local.rate = tw.local.convertedAmount/tw.local.initialAmount;&#xD;
}&#xD;
</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="615de557-0068-4ad6-887a-1552a432c4d8" targetRef="3f377147-f1f0-4a04-8f60-dfe3e9e6004e" name="To End" id="815febda-5b69-46fc-8238-************">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>bottomCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45" isCollection="false" name="error" id="2056.883b34b1-3d47-40d9-86c3-8c278c0f0a2f" />
                    
                    
                    <ns16:callActivity calledElement="1.c7e0c045-65b7-47f5-90a2-1a5bd59cf3de" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Get equivilant amount" id="a8536eb1-c1d0-44eb-8bba-aa44ae7a8248">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="172" y="104" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>63438792-f3ba-4bdd-801c-96a140a22ce6</ns16:incoming>
                        
                        
                        <ns16:outgoing>c93ce659-7a03-4e3a-85eb-4b8dbec32f62</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.f532fd4f-b514-4927-8cce-fd794f488e0d</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.prefix</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.35a79c0a-af0b-4d3d-81a4-c3b66c792f05</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.fromCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.282350b1-2727-4aa1-8118-3d5c3316136a</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.toCurrency</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.497359ed-14f4-4269-82b7-f09ee83dd3dc</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">//tw.system.user_id</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.94fc6012-982f-4612-82c9-77bc1256e0b1</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">//tw.system.currentProcessInstanceID</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.293d7351-d3dc-48dc-896c-14e707fd7cba</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"ODC creation and amendment"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6a5ef2b3-c44c-4c2f-8c15-3ba43ba2b307</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">//tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.359d19b4-5948-47d2-814d-d3bbc031e650</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">//tw.system.model.processApp.currentSnapshot.name</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.6acacd41-404d-41cd-8b73-a6a3f53de59c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.rateType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.*************-4e4c-8a23-3a8758392285</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.rateSubType</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.0427603a-263e-49f1-8ebc-12396435a8f9</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">String(tw.local.initialAmount)</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.122b436b-8719-4a3f-81ab-c7897c8c2554</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee"> tw.local.convertedAmount</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="a8536eb1-c1d0-44eb-8bba-aa44ae7a8248" targetRef="615de557-0068-4ad6-887a-1552a432c4d8" name="To get exchange rate" id="c93ce659-7a03-4e3a-85eb-4b8dbec32f62">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="convertedAmount" id="2056.472f9158-8433-4ad7-82e0-6fe3879ed862" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateType" id="2056.cb8589eb-101e-49dd-8ffa-2288f701d4f1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">"TRANSFER"</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="rateSubType" id="2056.08511d76-76e7-47cd-8cda-5e0402e5ee11">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">"S"</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee" isCollection="false" name="initialAmount" id="2056.069f6cae-9bed-4704-8185-8cf932e33cfc">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:defaultValue useDefault="true">100</ns3:defaultValue>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:dataObject>
                    
                    
                    <ns16:exclusiveGateway default="63438792-f3ba-4bdd-801c-96a140a22ce6" name="currencies are the same?" id="2c787a27-c641-4f46-8452-98687e350d32">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="72" y="76" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.a0d330ec-3bbb-40e5-8e8e-b1ab040a34e2</ns16:incoming>
                        
                        
                        <ns16:outgoing>63438792-f3ba-4bdd-801c-96a140a22ce6</ns16:outgoing>
                        
                        
                        <ns16:outgoing>a28d4fd9-1c2e-45f6-81d5-43be248dd6b2</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="2c787a27-c641-4f46-8452-98687e350d32" targetRef="a8536eb1-c1d0-44eb-8bba-aa44ae7a8248" name="To Get equivilant amount" id="63438792-f3ba-4bdd-801c-96a140a22ce6">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Set rate" id="c28f21a6-126e-4ff6-81b7-db8230367867">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="301" y="12" width="95" height="70" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>a28d4fd9-1c2e-45f6-81d5-43be248dd6b2</ns16:incoming>
                        
                        
                        <ns16:outgoing>b9b15ff9-de80-4487-862f-770285e7677a</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.rate = 1.0;</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:sequenceFlow sourceRef="2c787a27-c641-4f46-8452-98687e350d32" targetRef="c28f21a6-126e-4ff6-81b7-db8230367867" name="To Set rate" id="a28d4fd9-1c2e-45f6-81d5-43be248dd6b2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.fromCurrency	  ==	  tw.local.toCurrency</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="c28f21a6-126e-4ff6-81b7-db8230367867" targetRef="3f377147-f1f0-4a04-8f60-dfe3e9e6004e" name="Set rate" id="b9b15ff9-de80-4487-862f-770285e7677a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Exception Handling" id="9d74f5c4-b95e-4f30-81d3-2b6bd12177f3">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="330" y="195" width="95" height="70" color="#FF7782" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>799d512d-3793-4a4d-856d-7fd6420a15a1</ns16:incoming>
                        
                        
                        <ns16:outgoing>bb0944b8-a5de-4cb9-845e-2455fe481edf</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.5ea77901-7a17-422a-8958-67bb0e9c991b</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">"Get Exchange Rate"</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.81b82125-a5aa-45f7-8c0f-4870667eebbf</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:intermediateCatchEvent name="Error Event" id="7ad706f3-5dae-4356-8721-1440c59c5b3e">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="213" y="218" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>799d512d-3793-4a4d-856d-7fd6420a15a1</ns16:outgoing>
                        
                        
                        <ns16:errorEventDefinition id="67c532b6-9557-4655-8490-968c5da002f8" eventImplId="377507bc-b4aa-4dbc-8f66-aa198c403b6f">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:intermediateCatchEvent>
                    
                    
                    <ns16:endEvent name="End Event" id="88ec9872-d5bb-4982-819c-72397bc01ddf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="490" y="218" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>bb0944b8-a5de-4cb9-845e-2455fe481edf</ns16:incoming>
                        
                        
                        <ns16:errorEventDefinition id="1cf79145-962e-472c-84f8-fc3c309e8664" eventImplId="9ebfb275-c9c4-4acf-8d9e-fef59c7d0af0">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="7ad706f3-5dae-4356-8721-1440c59c5b3e" targetRef="9d74f5c4-b95e-4f30-81d3-2b6bd12177f3" name="To Exception Handling" id="799d512d-3793-4a4d-856d-7fd6420a15a1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="9d74f5c4-b95e-4f30-81d3-2b6bd12177f3" targetRef="88ec9872-d5bb-4982-819c-72397bc01ddf" name="To End Event" id="bb0944b8-a5de-4cb9-845e-2455fe481edf">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.bb0944b8-a5de-4cb9-845e-2455fe481edf</processLinkId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.9d74f5c4-b95e-4f30-81d3-2b6bd12177f3</fromProcessItemId>
            <endStateId>guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83</endStateId>
            <toProcessItemId>2025.88ec9872-d5bb-4982-819c-72397bc01ddf</toProcessItemId>
            <guid>6b1bb627-681f-4802-b99f-177d9f56b755</guid>
            <versionId>22cafe79-d2fe-41b7-82b4-199cdb2e0f8a</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.9d74f5c4-b95e-4f30-81d3-2b6bd12177f3</fromProcessItemId>
            <toProcessItemId>2025.88ec9872-d5bb-4982-819c-72397bc01ddf</toProcessItemId>
        </link>
        <link name="To get exchange rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.c93ce659-7a03-4e3a-85eb-4b8dbec32f62</processLinkId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.a8536eb1-c1d0-44eb-8bba-aa44ae7a8248</fromProcessItemId>
            <endStateId>guid:5a8b778ff99a34f5:-6ba84e90:1895f58d65c:4384</endStateId>
            <toProcessItemId>2025.615de557-0068-4ad6-887a-1552a432c4d8</toProcessItemId>
            <guid>53c7a605-7a42-492d-b49d-f4ec55de5bf6</guid>
            <versionId>3a473472-1cc9-4c3c-8864-e11eb04f4436</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.a8536eb1-c1d0-44eb-8bba-aa44ae7a8248</fromProcessItemId>
            <toProcessItemId>2025.615de557-0068-4ad6-887a-1552a432c4d8</toProcessItemId>
        </link>
        <link name="To Get equivilant amount">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.63438792-f3ba-4bdd-801c-96a140a22ce6</processLinkId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2c787a27-c641-4f46-8452-98687e350d32</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.a8536eb1-c1d0-44eb-8bba-aa44ae7a8248</toProcessItemId>
            <guid>aa09c72c-0de9-44ac-90f6-c8ae3338cad4</guid>
            <versionId>c152f2dc-142d-48b7-9490-97473f452dc3</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="bottomCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2c787a27-c641-4f46-8452-98687e350d32</fromProcessItemId>
            <toProcessItemId>2025.a8536eb1-c1d0-44eb-8bba-aa44ae7a8248</toProcessItemId>
        </link>
        <link name="To Set rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.a28d4fd9-1c2e-45f6-81d5-43be248dd6b2</processLinkId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.2c787a27-c641-4f46-8452-98687e350d32</fromProcessItemId>
            <endStateId>guid:651a1a6abf396537:64776e00:18baeba64af:-7b36</endStateId>
            <toProcessItemId>2025.c28f21a6-126e-4ff6-81b7-db8230367867</toProcessItemId>
            <guid>6646e6cd-f6f3-4aa7-b8d9-03de1f999ca2</guid>
            <versionId>dbc5513b-fdba-4160-a8a8-6811f4d2f287</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.2c787a27-c641-4f46-8452-98687e350d32</fromProcessItemId>
            <toProcessItemId>2025.c28f21a6-126e-4ff6-81b7-db8230367867</toProcessItemId>
        </link>
        <link name="Set rate">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.b9b15ff9-de80-4487-862f-770285e7677a</processLinkId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.c28f21a6-126e-4ff6-81b7-db8230367867</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3f377147-f1f0-4a04-8f60-dfe3e9e6004e</toProcessItemId>
            <guid>3995c91c-e9d0-416c-ac28-4439c4a661d4</guid>
            <versionId>f612fad4-c1bb-40d9-9748-0183b8fbbcbf</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.c28f21a6-126e-4ff6-81b7-db8230367867</fromProcessItemId>
            <toProcessItemId>2025.3f377147-f1f0-4a04-8f60-dfe3e9e6004e</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.815febda-5b69-46fc-8238-************</processLinkId>
            <processId>1.b5f9b6a1-9abe-435d-9494-18875b5c5b3f</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.615de557-0068-4ad6-887a-1552a432c4d8</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.3f377147-f1f0-4a04-8f60-dfe3e9e6004e</toProcessItemId>
            <guid>129dbedf-f507-4e41-beea-2bc1bc80eed9</guid>
            <versionId>f962449a-1830-4467-a641-a3b39893ffdd</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="bottomCenter" portType="2" />
            <fromProcessItemId>2025.615de557-0068-4ad6-887a-1552a432c4d8</fromProcessItemId>
            <toProcessItemId>2025.3f377147-f1f0-4a04-8f60-dfe3e9e6004e</toProcessItemId>
        </link>
    </process>
</teamworks>

