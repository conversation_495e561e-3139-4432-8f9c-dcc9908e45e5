<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.436a7747-a90a-4b7e-8d02-ff4803d46ce0" name="testTable">
        <lastModified>1733820095602</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.436a7747-a90a-4b7e-8d02-ff4803d46ce0</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"/&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>false</isMobileReady>
        <loadJsFunction></loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>guid:ae773bd79f7c2e1d:-42381511:19396c41403:-e9d</guid>
        <versionId>15d228f6-0bd1-4af6-88b3-1af012b927a5</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="fo">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.1b6e11ca-f5b6-48c0-b452-de4956be5cae</coachViewBindingTypeId>
            <coachViewId>64.436a7747-a90a-4b7e-8d02-ff4803d46ce0</coachViewId>
            <isList>false</isList>
            <classId>/12.65f0e3ad-27ed-4e8c-9713-56c6ccbffff5</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>593e9beb-07c6-4144-8803-b461699ec8ae</guid>
            <versionId>8cc1d036-6cab-4d73-afa6-e888a70fd44a</versionId>
        </bindingType>
    </coachView>
</teamworks>

