<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.5510de3d-75a5-4353-8d6d-82f540b6d556" name="Print Barcode">
        <lastModified>1697974904695</lastModified>
        <lastModifiedBy>reham.mounir</lastModifiedBy>
        <coachViewId>64.5510de3d-75a5-4353-8d6d-82f540b6d556</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;756c2036-8488-4ab2-827c-b84715e46886&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Data1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;5b4a6739-0c25-4e85-8320-8f88b5633e54&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Data&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0fee4c53-bff1-480a-8fa2-f2dfa2feef85&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;0e89c2e7-4848-41c8-83e6-e68afb2b5761&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;f4c7777d-8904-4973-819b-d9b88e66d54d&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@visibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"EDITABLE"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;d5b2a665-7ca8-45e7-89c2-b3018b1c3fbc&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()!=null &amp;amp;&amp;amp; me.getData()!=''){
document.getElementById('tdbarcode').innerHTML=me.getData();
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;1af81cb1-6650-4d9f-8317-031f09368738&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_CHANGE&lt;/ns2:optionName&gt;&lt;ns2:value&gt;if(me.getData()!=null &amp;amp;&amp;amp; me.getData()!=''){
document.getElementById('tdbarcode').innerHTML=me.getData();
}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.5663dd71-ff18-4d33-bea0-468d0b869816&lt;/ns2:viewUUID&gt;&lt;ns2:binding&gt;tw.businessData.barcode&lt;/ns2:binding&gt;&lt;/ns2:layoutItem&gt;&lt;ns2:layoutItem xsi:type="ns2:CustomHTML" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;6ec715a0-7dfa-4540-8812-62560fb8f894&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;CustomHTML1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;666153a2-0799-46da-8554-de499f3a5dbb&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@customHTML.contentType&lt;/ns2:optionName&gt;&lt;ns2:value&gt;TEXT&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;cc429023-f947-4aa7-8136-1fc82bceb207&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@customHTML.textContent&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&amp;lt;style&amp;gt;

.hide{
  display: none;
}


@page { size: auto;  margin: 0mm; }

&amp;lt;/style&amp;gt;
&amp;lt;div id="barCodeID"&amp;gt;
&amp;lt;div style=' width: 100%; height: 100% ; text-align:left;margin-top:100px'&amp;gt;
	&amp;lt;div style=' width=180px ; height: 60px; vertical-align:top;padding-top:1px;'&amp;gt;	
		&amp;lt;span id="tdbarcode" style="float:left ;margin-left :100px"&amp;gt;
		&amp;lt;/span&amp;gt;
	&amp;lt;/div&amp;gt;
		
&amp;lt;/div&amp;gt;
&amp;lt;/div&amp;gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction isNull="true" />
        <unloadJsFunction isNull="true" />
        <viewJsFunction isNull="true" />
        <changeJsFunction isNull="true" />
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction isNull="true" />
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>guid:83e1efe624431d49:-7084ad56:189d9c8d4c1:4f68</guid>
        <versionId>8e1130c2-4503-4eb0-a825-dd9c0bdb85c5</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <bindingType name="barcode">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewBindingTypeId>65.23d88024-0ad9-43aa-b40b-f873648128a3</coachViewBindingTypeId>
            <coachViewId>64.5510de3d-75a5-4353-8d6d-82f540b6d556</coachViewId>
            <isList>false</isList>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>0</seq>
            <description isNull="true" />
            <guid>e324bced-710d-4ebf-a382-47aaf2bbeb88</guid>
            <versionId>a67bca64-bede-44e6-9cf2-515cab27fecc</versionId>
        </bindingType>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.987cf90d-fb29-408f-b376-cc0d10b309bb</coachViewInlineScriptId>
            <coachViewId>64.5510de3d-75a5-4353-8d6d-82f540b6d556</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>this.load=function(){&#xD;
&#xD;
var generatedBarCode=this.ui.get("Data1");&#xD;
&#xD;
&#xD;
if(generatedBarCode!=null){&#xD;
&#xD;
document.getElementById('tdbarcode').innerHTML=generatedBarCode.getText();&#xD;
&#xD;
&#xD;
}&#xD;
&#xD;
}</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>7ce8eec4-ac9d-4a4c-bada-a123cf04b8e0</guid>
            <versionId>bd188d94-dbe1-40c4-8a39-107766194f5a</versionId>
        </inlineScript>
    </coachView>
</teamworks>

