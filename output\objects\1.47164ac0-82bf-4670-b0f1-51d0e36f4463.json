{"id": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "versionId": "57b9519b-c7b3-4dd4-9f16-ff374ea487f8", "name": "Get Charges Completed", "type": "process", "typeName": "Process", "details": {"processType": "12"}, "_fullObjectData": {"teamworks": {"process": {"id": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "Get Charges Completed", "lastModified": "1718004962623", "lastModifiedBy": "mohamed.reda", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "image": {"isNull": "true"}, "tabGroup": {"isNull": "true"}, "startingProcessItemId": ["2025.91e479a9-da3f-4608-a607-87e2586de9bd", "2025.91e479a9-da3f-4608-a607-87e2586de9bd"], "isRootProcess": "false", "processType": "12", "isErrorHandlerEnabled": "true", "errorHandlerItemId": ["2025.d143bea6-4b15-4826-8320-1bb2356a347a", "2025.d143bea6-4b15-4826-8320-1bb2356a347a"], "isLoggingVariables": "false", "isTransactional": "false", "processTimingLevel": {"isNull": "true"}, "participantRef": {"isNull": "true"}, "exposedType": "0", "isTrackingEnabled": "true", "xmlData": {"isNull": "true"}, "cachingType": "false", "itemLabel": {"isNull": "true"}, "cacheLength": "0", "mobileReady": "false", "sboSyncEnabled": "true", "externalId": {"isNull": "true"}, "isSecured": "false", "isAjaxExposed": "true", "description": {"isNull": "true"}, "guid": "fef01444-7ed5-47fe-ab99-74bf8f7221de", "versionId": "57b9519b-c7b3-4dd4-9f16-ff374ea487f8", "dependencySummary": "<dependencySummary id=\"bpdid:818bf8fd736d6b59:764a22fc:18ffdba29b8:b41\" />", "jsonData": "{\"rootElement\":[{\"flowElement\":[{\"parallelMultiple\":false,\"outgoing\":[\"3169196d-8190-4230-94ba-9b0a5be3feda\"],\"isInterrupting\":false,\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":25,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Start\",\"declaredType\":\"startEvent\",\"id\":\"105e3d80-0eb1-4e46-be98-d80ea339739e\"},{\"incoming\":[\"2bbd3977-29c3-4a57-8acb-7471aa53ad72\",\"1c1cd577-d00d-45c6-8df5-38e3aec21d86\",\"23dc5a98-be81-4e6f-8e45-52666fca2afe\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":24,\"x\":966,\"y\":80,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}],\"endStateId\":[\"guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35e7\"],\"preAssignmentScript\":[],\"saveExecutionContext\":[true]},\"name\":\"End\",\"declaredType\":\"endEvent\",\"id\":\"fa6c9000-3339-46dd-bb24-e0d2d0038bfe\"},{\"startQuantity\":1,\"outgoing\":[\"d1a76c83-69b1-4d3e-859e-00bb1274bf3c\"],\"incoming\":[\"bea97401-8a95-441f-9f38-fc74fa6cfbf5\"],\"extensionElements\":{\"postAssignmentScript\":[\"if (tw.local.InterestAndChargesList == undefined || tw.local.InterestAndChargesList == null) {\\r\\n\\ttw.local.InterestAndChargesList = [];\\r\\n}\"],\"nodeVisualInfo\":[{\"width\":95,\"x\":377,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Charges MW\",\"dataInputAssociation\":[{\"targetRef\":\"2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.productCode\"]}}]},{\"targetRef\":\"2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.user_id\"]}}]},{\"targetRef\":\"2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.currentProcessInstanceID\"]}}]},{\"targetRef\":\"2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.system.model.processApp.currentSnapshot.name\"]}}]},{\"targetRef\":\"2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.env.prefix\"]}}]},{\"targetRef\":\"2055.d8783716-2453-46e3-8522-b1b2504092a2\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.eventCode\"]}}]},{\"targetRef\":\"2055.fc2b795d-a78b-4e6c-8b59-c821d346b07f\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.requestCcy\"]}}]},{\"targetRef\":\"2055.b6919232-c019-43a5-8742-9783cfd63371\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"ODC CREATION AND AMENDMENT\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"f7d9f9a0-96cb-48df-8601-2a428bd9d0ad\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorCode\"]}}],\"sourceRef\":[\"2055.78c4796d-3236-45f3-883b-556500834b95\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.InterestAndChargesList\"]}}],\"sourceRef\":[\"2055.dd74dd40-8fa5-4359-8243-08e144b543d2\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMSG\"]}}],\"sourceRef\":[\"2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0\"]}],\"calledElement\":\"1.e059295b-f72a-4e32-a329-8d32ebe941de\"},{\"startQuantity\":1,\"outgoing\":[\"bea97401-8a95-441f-9f38-fc74fa6cfbf5\"],\"incoming\":[\"81407a06-de54-414f-b760-f65d6a5277e2\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":95,\"x\":227,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Set Event Code\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"8a59f99e-37b8-4ae1-a389-9775b8ff71df\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"try {\\r\\n\\ttw.local.amntList = [];\\r\\n\\ttw.local.inputList = [];\\r\\n\\ttw.local.hasFixedRate = false;\\r\\n\\r\\n\\tif (tw.local.requestType == tw.epv.RequestType.Amendment) {\\r\\n\\t\\ttw.local.event = \\\"AMND\\\";\\r\\n\\t\\ttw.local.eventCode = \\\"AMND\\\";\\r\\n\\t}else if (tw.local.requestType == tw.epv.RequestType.Collection){\\r\\n\\t\\ttw.local.eventCode = \\\"LIQD\\\";\\r\\n\\t}else{\\r\\n\\t\\ttw.local.eventCode = \\\"INIT\\\";\\r\\n\\t}\\r\\n} catch (err) {\\r\\n\\ttw.local.errorMSG = err.message;\\r\\n\\tthrow new Error(tw.local.errorMSG);\\r\\n}\"]}},{\"targetRef\":\"f7d9f9a0-96cb-48df-8601-2a428bd9d0ad\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Charges MW\",\"declaredType\":\"sequenceFlow\",\"id\":\"bea97401-8a95-441f-9f38-fc74fa6cfbf5\",\"sourceRef\":\"8a59f99e-37b8-4ae1-a389-9775b8ff71df\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"eventCode\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.26c6e13a-cca1-4718-b3b1-0c2f73830d00\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"Test Test\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMSG\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.2c9a74ef-caf7-49f4-9f40-87d8beb413bf\"},{\"itemSubjectRef\":\"itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e\",\"name\":\"InterestAndChargesList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.bde51344-9a08-478a-bf7f-dc360a21106a\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorCode\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.b293c088-7a31-43a0-ac3d-fc336e3047fb\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"errorMessage\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.ff316da1-1c0a-4065-907a-81200b7c567e\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isSuccessful\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.04ca9b7f-e2b2-455d-940b-3a6314b0246d\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"xmlRequest\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.3c3ede3c-8a3c-44d5-a5c2-f01d2b7ec60b\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"xmlResponse\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.8427754b-d8e3-4d14-9532-da4befaba0ad\"},{\"startQuantity\":1,\"outgoing\":[\"a058d91d-b10f-4cd3-bf38-4f27c1e7402f\"],\"incoming\":[\"d1a76c83-69b1-4d3e-859e-00bb1274bf3c\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":524,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}]},\"name\":\"Map Output\",\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"scriptTask\",\"id\":\"15b199e9-0afb-49d4-b9e3-d8c59ee0b673\",\"scriptFormat\":\"text\\/x-javascript\",\"script\":{\"content\":[\"try{\\r\\nvar j =0;\\r\\ntw.local.charges = [];\\r\\n\\r\\nif (tw.local.InterestAndChargesList.listLength > 0) {\\r\\n\\r\\n\\tfor (var i=0; i<tw.local.InterestAndChargesList.listLength ; i++) {\\r\\n\\t\\r\\n\\t\\tif (tw.local.InterestAndChargesList[i].ruleType == \\\"Charge\\\"){\\r\\n\\t\\t\\r\\n\\t\\t\\ttw.local.charges[j] = {};\\r\\n\\t\\t\\ttw.local.charges[j].defaultCurrency = {};\\r\\n\\t\\t\\ttw.local.charges[j].component = tw.local.InterestAndChargesList[i].component;\\r\\n\\t\\t\\ttw.local.charges[j].description = tw.local.InterestAndChargesList[i].description;\\r\\n\\t\\t\\ttw.local.charges[j].rateType = tw.local.InterestAndChargesList[i].rateType;\\r\\n\\t\\t\\ttw.local.charges[j].waiver = false;\\r\\n\\t\\t\\ttw.local.charges[j].debitedAmount = {};\\r\\n\\t\\t\\ttw.local.charges[j].debitedAccount = {};\\r\\n\\t\\t\\ttw.local.charges[j].debitedAccount.currency = {};\\r\\n\\t\\t\\ttw.local.charges[j].debitedAccount.accountClass = {};\\r\\n\\t\\t\\ttw.local.charges[j].debitedAccount.accountClass.name = \\\"001\\\";\\r\\n\\t\\t\\ttw.local.charges[j].debitedAccount.isOverDraft = null;\\r\\n\\/\\/\\t\\t\\ttw.local.charges[j].debitedAccount.accountClass.name = \\\"Customer Account\\\";\\r\\n\\r\\n\\/\\/\\t\\t\\ttw.local.charges[j].bookingCcy = tw.local.InterestAndChargesList[i].bookingCcy;\\r\\n\\/\\/\\t\\t\\ttw.local.charges[j].basisAmntCcy = tw.local.InterestAndChargesList[i].basisAmountCurrency;\\r\\n\\t\\r\\n\\t      \\/\\/FLAT AMOUNT-----------------------------------------------------------------------------------------------\\r\\n\\t\\tif (tw.local.InterestAndChargesList[i].rateType == \\\"Flat Amount\\\") {\\r\\n\\t\\t\\r\\n\\t\\t\\tvar flatAmnt = tw.local.InterestAndChargesList[i].flatAmount;\\r\\n\\t\\t\\tvar basisAmntCcy = tw.local.InterestAndChargesList[i].basisAmountCurrency;\\r\\n\\t\\t\\tvar bookingCcy = tw.local.InterestAndChargesList[i].bookingCcy;\\r\\n\\t\\t\\tvar requestCcy = tw.local.requestCcy;\\r\\n\\t\\t\\t\\r\\n\\t\\t\\t\\/\\/Contract CCY - Charge CCY\\r\\n\\t\\t\\tif (bookingCcy === \\\"Charge CCY\\\") {\\r\\n\\t\\t\\t\\ttw.local.charges[j].defaultCurrency.name =  basisAmntCcy;\\/\\/###########################################BasicAmountCcy\\r\\n\\t\\t\\t\\ttw.local.charges[j].defaultCurrency.value =  basisAmntCcy;\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\ttw.local.charges[j].defaultAmount = Number(flatAmnt.toFixed(2));\\r\\n\\t\\t\\t\\ttw.local.charges[j].changeAmount = Number(flatAmnt.toFixed(2));\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t}else if(bookingCcy === \\\"Contract CCY\\\"){\\r\\n\\t\\t\\t\\ttw.local.charges[j].defaultCurrency.name =  requestCcy;\\/\\/###########################################RequestCcy\\r\\n\\t\\t\\t\\ttw.local.charges[j].defaultCurrency.value =  requestCcy;\\r\\n\\t\\t\\t\\t\\r\\n\\t\\t\\t\\ttw.local.charges[j].flatAmount = flatAmnt;\\r\\n\\t\\t\\t\\ttw.local.charges[j].defaultAmount = Number(flatAmnt.toFixed(2));\\r\\n\\t\\t\\t\\ttw.local.charges[j].changeAmount = Number(flatAmnt.toFixed(2));\\r\\n\\t\\t\\t}\\r\\n\\t\\t}\\r\\n\\t        \\r\\n\\t     \\/\\/FIXED RATE---------------------------------------------------------------------------------------------------\\r\\n\\t        if (tw.local.InterestAndChargesList[i].rateType == \\\"Fixed Rate\\\") {\\r\\n\\t            tw.local.charges[j].defaultCurrency.name = requestCcy;\\r\\n\\t            tw.local.charges[j].defaultCurrency.value = requestCcy;\\r\\n\\t            \\r\\n\\t\\t\\ttw.local.charges[j].defaultPercentage = tw.local.InterestAndChargesList[i].rate;\\r\\n\\t\\t\\ttw.local.charges[j].changePercentage = tw.local.InterestAndChargesList[i].rate;\\r\\n\\t\\t\\ttw.local.hasFixedRate = true;\\r\\n\\t        }\\r\\n\\t        j+=1;\\r\\n\\t    }\\r\\n\\t}\\r\\n\\t\\r\\n\\t\\r\\n}else{\\r\\n\\ttw.local.noDataMessage = \\\"No Valid Charges Found\\\";\\r\\n}\\r\\n\\r\\n} catch (err) {\\r\\n\\ttw.local.errorMSG = err.message;\\r\\n\\tthrow new Error(tw.local.errorMSG);\\r\\n}\"]}},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"inputList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.70cf6b8a-f33e-48ba-a228-2609f4189524\"},{\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"amntList\",\"isCollection\":true,\"declaredType\":\"dataObject\",\"id\":\"2056.85cc93f6-95b8-49b7-bbe0-6d19190c38b1\"},{\"outgoing\":[\"81407a06-de54-414f-b760-f65d6a5277e2\",\"a2859f0c-f8bd-45e5-bd56-8d7887016b2a\"],\"incoming\":[\"3169196d-8190-4230-94ba-9b0a5be3feda\"],\"default\":\"81407a06-de54-414f-b760-f65d6a5277e2\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":94,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"AmntChanged?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"91e479a9-da3f-4608-a607-87e2586de9bd\"},{\"targetRef\":\"8a59f99e-37b8-4ae1-a389-9775b8ff71df\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"NO\",\"declaredType\":\"sequenceFlow\",\"id\":\"81407a06-de54-414f-b760-f65d6a5277e2\",\"sourceRef\":\"91e479a9-da3f-4608-a607-87e2586de9bd\"},{\"targetRef\":\"91e479a9-da3f-4608-a607-87e2586de9bd\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To AmntChanged?\",\"declaredType\":\"sequenceFlow\",\"id\":\"3169196d-8190-4230-94ba-9b0a5be3feda\",\"sourceRef\":\"105e3d80-0eb1-4e46-be98-d80ea339739e\"},{\"parallelMultiple\":false,\"outgoing\":[\"70eb5b5c-499d-49e9-88d9-4ec38e9d713f\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"a2f9fbc4-0d52-4b82-8dcc-2149f6ee9239\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"6da7d3d8-8ca3-4559-a75f-cfea36a74691\",\"otherAttributes\":{\"eventImplId\":\"1f409556-f821-4faa-8acd-c2fb382f1ab5\"}}],\"attachedToRef\":\"8a59f99e-37b8-4ae1-a389-9775b8ff71df\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":262,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error\",\"declaredType\":\"boundaryEvent\",\"id\":\"cbd21862-4ce4-4e97-bfd3-943f8535831d\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"2866fbfc-9306-4a6b-8d0f-074c33e62b7b\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2038ea78-2088-419b-82f7-1afc21b10337\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"b7623788-a620-46a5-af45-2af125724f23\",\"otherAttributes\":{\"eventImplId\":\"8937bcf5-4100-45ec-8d25-eeca0b100fce\"}}],\"attachedToRef\":\"f7d9f9a0-96cb-48df-8601-2a428bd9d0ad\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":412,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error1\",\"declaredType\":\"boundaryEvent\",\"id\":\"ffaf0225-b6d1-4031-bd93-53ae6bebd8dd\",\"outputSet\":{}},{\"parallelMultiple\":false,\"outgoing\":[\"da24e7e9-fb6e-4825-806a-c9421fd2e2c9\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"e843c207-c19e-4de5-be9c-83541c50f9c4\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"73a97e6c-a74a-414e-887b-a406c0aa4147\",\"otherAttributes\":{\"eventImplId\":\"01d26550-492f-47e6-8bcd-d0e6420b2c61\"}}],\"attachedToRef\":\"15b199e9-0afb-49d4-b9e3-d8c59ee0b673\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":559,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error2\",\"declaredType\":\"boundaryEvent\",\"id\":\"08e7321e-c669-4f2d-b928-6ec12f86f7c8\",\"outputSet\":{}},{\"startQuantity\":1,\"outgoing\":[\"23dc5a98-be81-4e6f-8e45-52666fca2afe\"],\"incoming\":[\"a2859f0c-f8bd-45e5-bd56-8d7887016b2a\",\"06860578-0514-48d9-8898-6a16b7633175\"],\"extensionElements\":{\"postAssignmentScript\":[],\"nodeVisualInfo\":[{\"width\":95,\"x\":785,\"y\":57,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Calc Default Amount\",\"dataInputAssociation\":[{\"targetRef\":\"2055.737f0e64-d76d-4c76-9b4b-e87c7878b455\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.charges\"]}}]},{\"targetRef\":\"2055.ba300aef-d348-49c6-bd13-01d0154c466e\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.collectableAmnt.toString()\"]}}]},{\"targetRef\":\"2055.d430a477-84cf-4f48-9ad4-6351b6639119\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297\",\"declaredType\":\"TFormalExpression\"}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"fd09a318-5101-424d-883c-4db3a15eb879\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.charges\"]}}],\"sourceRef\":[\"2055.f3009431-81a1-4331-8f48-100ccdda4aea\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.error\"]}}],\"sourceRef\":[\"2055.880a59bf-b853-4f92-94be-160455121ee0\"]}],\"calledElement\":\"1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd\"},{\"parallelMultiple\":false,\"outgoing\":[\"9294041a-4588-4b91-8e0c-f5d224cc34be\"],\"dataOutput\":[{\"itemSubjectRef\":\"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e\",\"name\":\"error\",\"isCollection\":false,\"id\":\"0ccb6700-2ccb-4988-8ba1-8180d12c6c3b\"}],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"34aa40bf-6f04-4f2f-ae4d-8f7a946d3197\",\"otherAttributes\":{\"eventImplId\":\"e61bbb49-ecb6-43ea-8cd0-9aec9a1e2c14\"}}],\"attachedToRef\":\"fd09a318-5101-424d-883c-4db3a15eb879\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":820,\"y\":115,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"cancelActivity\":true,\"name\":\"Error4\",\"declaredType\":\"boundaryEvent\",\"id\":\"fc6174ed-5c04-4971-8537-7c53279af857\",\"outputSet\":{}},{\"targetRef\":\"a4820def-4867-4c9e-8598-90726f0c65e5\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To hasFixedRate?\",\"declaredType\":\"sequenceFlow\",\"id\":\"a058d91d-b10f-4cd3-bf38-4f27c1e7402f\",\"sourceRef\":\"15b199e9-0afb-49d4-b9e3-d8c59ee0b673\"},{\"targetRef\":\"fd09a318-5101-424d-883c-4db3a15eb879\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isAmntChanged\\t  ==\\t  true\"]},\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topLeft\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true,\"customBendPoint\":[{\"x\":740,\"y\":14}]}]},\"name\":\"To Calc Default Amount\",\"declaredType\":\"sequenceFlow\",\"id\":\"a2859f0c-f8bd-45e5-bd56-8d7887016b2a\",\"sourceRef\":\"91e479a9-da3f-4608-a607-87e2586de9bd\"},{\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"hasFixedRate\",\"isCollection\":false,\"declaredType\":\"dataObject\",\"id\":\"2056.a6a7a146-00bf-497d-965a-d8fe74c31038\"},{\"startQuantity\":1,\"outgoing\":[\"2bbd3977-29c3-4a57-8acb-7471aa53ad72\"],\"incoming\":[\"70eb5b5c-499d-49e9-88d9-4ec38e9d713f\",\"2866fbfc-9306-4a6b-8d0f-074c33e62b7b\",\"da24e7e9-fb6e-4825-806a-c9421fd2e2c9\",\"9294041a-4588-4b91-8e0c-f5d224cc34be\",\"342a6e7e-5a40-434c-80b6-2c1d2fdcd22a\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#FF7782\",\"width\":95,\"x\":523,\"y\":259,\"declaredType\":\"TNodeVisualInfo\",\"height\":70}],\"activityType\":[\"CalledProcess\"]},\"name\":\"Exception Handling\",\"dataInputAssociation\":[{\"targetRef\":\"2055.5ea77901-7a17-422a-8958-67bb0e9c991b\",\"assignment\":[{\"from\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"\\\"Get ODC Charges\\\"\"]}}]}],\"isForCompensation\":false,\"completionQuantity\":1,\"declaredType\":\"callActivity\",\"id\":\"d143bea6-4b15-4826-8320-1bb2356a347a\",\"dataOutputAssociation\":[{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.isSuccessful\"]}}],\"sourceRef\":[\"2055.99eb514d-4b62-401e-8cdb-7edc096adffd\"]},{\"assignment\":[{\"to\":{\"evaluatesToTypeRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.errorMSG\"]}}],\"sourceRef\":[\"2055.81b82125-a5aa-45f7-8c0f-4870667eebbf\"]}],\"calledElement\":\"1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0\"},{\"targetRef\":\"d143bea6-4b15-4826-8320-1bb2356a347a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"70eb5b5c-499d-49e9-88d9-4ec38e9d713f\",\"sourceRef\":\"cbd21862-4ce4-4e97-bfd3-943f8535831d\"},{\"targetRef\":\"d143bea6-4b15-4826-8320-1bb2356a347a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"2866fbfc-9306-4a6b-8d0f-074c33e62b7b\",\"sourceRef\":\"ffaf0225-b6d1-4031-bd93-53ae6bebd8dd\"},{\"targetRef\":\"d143bea6-4b15-4826-8320-1bb2356a347a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"da24e7e9-fb6e-4825-806a-c9421fd2e2c9\",\"sourceRef\":\"08e7321e-c669-4f2d-b928-6ec12f86f7c8\"},{\"targetRef\":\"d143bea6-4b15-4826-8320-1bb2356a347a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"bottomCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"9294041a-4588-4b91-8e0c-f5d224cc34be\",\"sourceRef\":\"fc6174ed-5c04-4971-8537-7c53279af857\"},{\"targetRef\":\"fa6c9000-3339-46dd-bb24-e0d2d0038bfe\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"bottomCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"2bbd3977-29c3-4a57-8acb-7471aa53ad72\",\"sourceRef\":\"d143bea6-4b15-4826-8320-1bb2356a347a\"},{\"outgoing\":[\"06860578-0514-48d9-8898-6a16b7633175\",\"1c1cd577-d00d-45c6-8df5-38e3aec21d86\"],\"incoming\":[\"a058d91d-b10f-4cd3-bf38-4f27c1e7402f\"],\"default\":\"06860578-0514-48d9-8898-6a16b7633175\",\"gatewayDirection\":\"Unspecified\",\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":32,\"x\":656,\"y\":76,\"declaredType\":\"TNodeVisualInfo\",\"height\":32}]},\"name\":\"hasFixedRate?\",\"declaredType\":\"exclusiveGateway\",\"id\":\"a4820def-4867-4c9e-8598-90726f0c65e5\"},{\"targetRef\":\"fd09a318-5101-424d-883c-4db3a15eb879\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":true}]},\"name\":\"yes\",\"declaredType\":\"sequenceFlow\",\"id\":\"06860578-0514-48d9-8898-6a16b7633175\",\"sourceRef\":\"a4820def-4867-4c9e-8598-90726f0c65e5\"},{\"targetRef\":\"fa6c9000-3339-46dd-bb24-e0d2d0038bfe\",\"conditionExpression\":{\"declaredType\":\"TFormalExpression\",\"content\":[\"tw.local.hasFixedRate\\t  ==\\t  false\"]},\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"linkVisualInfo\":[{\"sourcePortLocation\":\"topCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"topCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":true,\"customBendPoint\":[{\"x\":935,\"y\":22}]}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"1c1cd577-d00d-45c6-8df5-38e3aec21d86\",\"sourceRef\":\"a4820def-4867-4c9e-8598-90726f0c65e5\"},{\"targetRef\":\"fa6c9000-3339-46dd-bb24-e0d2d0038bfe\",\"extensionElements\":{\"sequenceFlowImplementation\":[{\"fireValidation\":\"Never\",\"sboSyncEnabled\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation\"}],\"endStateId\":[\"guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3674\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":true,\"showLabel\":false}]},\"name\":\"To End\",\"declaredType\":\"sequenceFlow\",\"id\":\"23dc5a98-be81-4e6f-8e45-52666fca2afe\",\"sourceRef\":\"fd09a318-5101-424d-883c-4db3a15eb879\"},{\"targetRef\":\"15b199e9-0afb-49d4-b9e3-d8c59ee0b673\",\"extensionElements\":{\"endStateId\":[\"guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20\"],\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftCenter\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Map Output\",\"declaredType\":\"sequenceFlow\",\"id\":\"d1a76c83-69b1-4d3e-859e-00bb1274bf3c\",\"sourceRef\":\"f7d9f9a0-96cb-48df-8601-2a428bd9d0ad\"},{\"parallelMultiple\":false,\"outgoing\":[\"342a6e7e-5a40-434c-80b6-2c1d2fdcd22a\"],\"eventDefinition\":[{\"extensionElements\":{\"errorEventSettings\":[{\"catchAll\":true,\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings\"}]},\"declaredType\":\"errorEventDefinition\",\"id\":\"ddea1277-5f7c-488b-8afc-af10e255696f\",\"otherAttributes\":{\"eventImplId\":\"52b58b74-7ca7-4998-8c00-58e44320bac7\"}}],\"extensionElements\":{\"nodeVisualInfo\":[{\"width\":24,\"x\":416,\"y\":299,\"declaredType\":\"TNodeVisualInfo\",\"height\":24}]},\"name\":\"Error Event\",\"declaredType\":\"intermediateCatchEvent\",\"id\":\"60f2e4ff-ef7d-4399-8eb9-b942db9a6434\"},{\"targetRef\":\"d143bea6-4b15-4826-8320-1bb2356a347a\",\"extensionElements\":{\"linkVisualInfo\":[{\"sourcePortLocation\":\"rightCenter\",\"showCoachControlLabel\":false,\"labelPosition\":0.0,\"targetPortLocation\":\"leftBottom\",\"declaredType\":\"TLinkVisualInfo\",\"saveExecutionContext\":false,\"showLabel\":false}]},\"name\":\"To Exception Handling\",\"declaredType\":\"sequenceFlow\",\"id\":\"342a6e7e-5a40-434c-80b6-2c1d2fdcd22a\",\"sourceRef\":\"60f2e4ff-ef7d-4399-8eb9-b942db9a6434\"}],\"laneSet\":[{\"id\":\"6c828734-62d0-4148-b98d-b573440fe0cd\",\"lane\":[{\"flowNodeRef\":[\"105e3d80-0eb1-4e46-be98-d80ea339739e\",\"fa6c9000-3339-46dd-bb24-e0d2d0038bfe\",\"f7d9f9a0-96cb-48df-8601-2a428bd9d0ad\",\"8a59f99e-37b8-4ae1-a389-9775b8ff71df\",\"15b199e9-0afb-49d4-b9e3-d8c59ee0b673\",\"91e479a9-da3f-4608-a607-87e2586de9bd\",\"cbd21862-4ce4-4e97-bfd3-943f8535831d\",\"ffaf0225-b6d1-4031-bd93-53ae6bebd8dd\",\"08e7321e-c669-4f2d-b928-6ec12f86f7c8\",\"fd09a318-5101-424d-883c-4db3a15eb879\",\"fc6174ed-5c04-4971-8537-7c53279af857\",\"d143bea6-4b15-4826-8320-1bb2356a347a\",\"a4820def-4867-4c9e-8598-90726f0c65e5\",\"60f2e4ff-ef7d-4399-8eb9-b942db9a6434\"],\"extensionElements\":{\"nodeVisualInfo\":[{\"color\":\"#F8F8F8\",\"width\":3000,\"x\":0,\"y\":0,\"declaredType\":\"TNodeVisualInfo\",\"height\":500}]},\"name\":\"System\",\"partitionElementRef\":\"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b\",\"declaredType\":\"lane\",\"id\":\"3df597f4-a975-4960-bb3e-856990b317ea\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process\\/wle}isSystemLane\":\"true\"}}]}],\"isClosed\":false,\"extensionElements\":{\"isSecured\":[false],\"isAjaxExposed\":[true],\"sboSyncEnabled\":[true]},\"documentation\":[{\"textFormat\":\"text\\/plain\"}],\"name\":\"Get Charges Completed\",\"declaredType\":\"process\",\"id\":\"1.47164ac0-82bf-4670-b0f1-51d0e36f4463\",\"processType\":\"None\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/process}executionMode\":\"microflow\"},\"ioSpecification\":{\"dataOutput\":[{\"itemSubjectRef\":\"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"name\":\"charges\",\"isCollection\":true,\"id\":\"2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0\"},{\"itemSubjectRef\":\"itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45\",\"name\":\"error\",\"isCollection\":false,\"id\":\"2055.e25be4bb-685f-47a4-b385-deccef9a8af7\"},{\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"noDataMessage\",\"isCollection\":false,\"id\":\"2055.2fd421df-d7c7-448c-bdaa-b0e2fe295470\"}],\"extensionElements\":{\"epvProcessLinks\":[{\"epvProcessLinkRef\":[{\"epvId\":\"21.f79b6325-0b4a-48cd-b342-f9a61300eace\",\"epvProcessLinkId\":\"b5808579-505b-40b4-8cc5-fdb2d030b979\",\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef\"}],\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks\"}]},\"inputSet\":[{\"dataInputRefs\":[\"2055.ad7e0a45-5cb2-437f-b705-2ff310914291\",\"2055.1001aa15-5c17-4db8-ab30-0263431130b2\",\"2055.7d751a23-0e1b-4987-a9bc-41b25f644f54\",\"2055.66b62639-4042-49bf-9544-dbb15f00f3be\",\"2055.12683c54-ad5b-4c68-b734-93d1dea93938\",\"2055.86004a6f-9b72-4f74-a69a-aed7c869a281\",\"2055.********-c52c-4425-8c47-741f31a7fa66\"]}],\"outputSet\":[{\"dataOutputRefs\":[\"2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0\",\"2055.e25be4bb-685f-47a4-b385-deccef9a8af7\",\"2055.2fd421df-d7c7-448c-bdaa-b0e2fe295470\"]}],\"dataInput\":[{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\\\"INIT\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"event\",\"isCollection\":false,\"id\":\"2055.ad7e0a45-5cb2-437f-b705-2ff310914291\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"\\\"EGP\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestCcy\",\"isCollection\":false,\"id\":\"2055.1001aa15-5c17-4db8-ab30-0263431130b2\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"\\/\\/\\\"IAVC\\\"\\r\\n\\\"IUDC\\\"\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"productCode\",\"isCollection\":false,\"id\":\"2055.7d751a23-0e1b-4987-a9bc-41b25f644f54\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"tw.epv.RequestType.Create\"}]},\"itemSubjectRef\":\"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022\",\"name\":\"requestType\",\"isCollection\":false,\"id\":\"2055.66b62639-4042-49bf-9544-dbb15f00f3be\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"var autoObject = new tw.object.listOf.ChargesAndCommissions();\\nautoObject[0] = new tw.object.ChargesAndCommissions();\\nautoObject[0].component = \\\"\\\";\\nautoObject[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject[0].defaultCurrency.name = \\\"\\\";\\nautoObject[0].defaultCurrency.value = \\\"\\\";\\nautoObject[0].changeAmount = 0.0;\\nautoObject[0].waiver = false;\\nautoObject[0].debitedAccount = new tw.object.AccountDetails();\\nautoObject[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject[0].debitedAccount.accountClass.name = \\\"\\\";\\nautoObject[0].debitedAccount.accountClass.value = \\\"\\\";\\nautoObject[0].debitedAccount.glAccountNo = \\\"\\\";\\nautoObject[0].debitedAccount.customerAccountNo = \\\"\\\";\\nautoObject[0].debitedAccount.branchCode = \\\"\\\";\\nautoObject[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\\nautoObject[0].debitedAccount.currency.name = \\\"\\\";\\nautoObject[0].debitedAccount.currency.value = \\\"\\\";\\nautoObject[0].debitedAccount.balance = 0.0;\\nautoObject[0].debitedAccount.balanceSign = \\\"\\\";\\nautoObject[0].debitedAccount.isOverDraft = false;\\nautoObject[0].debitedAmount = new tw.object.AmountDetails();\\nautoObject[0].debitedAmount.standardExRate = 0.0;\\nautoObject[0].debitedAmount.negotiatedExRate = 0.0;\\nautoObject[0].debitedAmount.amountInAccount = 0.0;\\nautoObject[0].rateType = \\\"\\\";\\nautoObject[0].description = \\\"\\\";\\nautoObject[0].defaultPercentage = 0.0;\\nautoObject[0].changePercentage = 0.0;\\nautoObject[0].defaultAmount = 0.0;\\nautoObject[0].basicAmountCurrency = \\\"\\\";\\nautoObject[0].flatAmount = 0.0;\\nautoObject[0].isGLFound = false;\\nautoObject[0].glVerifyMSG = \\\"\\\";\\nautoObject\"}]},\"itemSubjectRef\":\"itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061\",\"name\":\"charges\",\"isCollection\":true,\"id\":\"2055.12683c54-ad5b-4c68-b734-93d1dea93938\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":true,\"value\":\"20000\"}]},\"itemSubjectRef\":\"itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee\",\"name\":\"collectableAmnt\",\"isCollection\":false,\"id\":\"2055.86004a6f-9b72-4f74-a69a-aed7c869a281\"},{\"extensionElements\":{\"defaultValue\":[{\"declaredType\":\"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue\",\"useDefault\":false,\"value\":\"false\"}]},\"itemSubjectRef\":\"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\",\"name\":\"isAmntChanged\",\"isCollection\":false,\"id\":\"2055.********-c52c-4425-8c47-741f31a7fa66\"}]}}],\"targetNamespace\":\"\",\"typeLanguage\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"expressionLanguage\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/expression-lang\\/javascript\"}", "field1": {"isNull": "true"}, "field2": {"isNull": "true"}, "field3": "0", "field4": {"isNull": "true"}, "field5": "false", "clobField1": {"isNull": "true"}, "blobField1": {"isNull": "true"}, "processParameter": [{"name": "event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.ad7e0a45-5cb2-437f-b705-2ff310914291", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "1", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "876674ca-149d-4b12-8ebc-2061c9ffb06f", "versionId": "f780c967-92de-491b-96c4-6f62f911d417"}, {"name": "requestCcy", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.1001aa15-5c17-4db8-ab30-0263431130b2", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "2", "hasDefault": "true", "defaultValue": "\"EGP\"", "isLocked": "false", "description": {"isNull": "true"}, "guid": "d4ee972e-1295-4317-8c47-ac34313b99fe", "versionId": "f55ebc96-bdbb-42a7-8626-52e6fe568c64"}, {"name": "productCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.7d751a23-0e1b-4987-a9bc-41b25f644f54", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "3", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "ac9d4fef-9e72-4c29-adba-b8daf998ae69", "versionId": "498c9a45-3904-49cb-8274-93611270c585"}, {"name": "requestType", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.66b62639-4042-49bf-9544-dbb15f00f3be", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "4", "hasDefault": "true", "defaultValue": "tw.epv.RequestType.Create", "isLocked": "false", "description": {"isNull": "true"}, "guid": "a39a4eaa-ba53-4aa6-8ad0-97f261814831", "versionId": "43d5cfe3-5c9e-49ae-9e3a-9686fef869e0"}, {"name": "charges", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.12683c54-ad5b-4c68-b734-93d1dea93938", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "1", "isArrayOf": "true", "classId": "/12.e3018bad-b453-4bf5-96fd-09a5141cd061", "seq": "5", "hasDefault": "true", "defaultValue": "var autoObject = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject[0] = new tw.object.ChargesAndCommissions();\r\nautoObject[0].component = \"\";\r\nautoObject[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject[0].defaultCurrency.name = \"\";\r\nautoObject[0].defaultCurrency.value = \"\";\r\nautoObject[0].changeAmount = 0.0;\r\nautoObject[0].waiver = false;\r\nautoObject[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject[0].debitedAccount.accountClass.name = \"\";\r\nautoObject[0].debitedAccount.accountClass.value = \"\";\r\nautoObject[0].debitedAccount.glAccountNo = \"\";\r\nautoObject[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject[0].debitedAccount.branchCode = \"\";\r\nautoObject[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject[0].debitedAccount.currency.name = \"\";\r\nautoObject[0].debitedAccount.currency.value = \"\";\r\nautoObject[0].debitedAccount.balance = 0.0;\r\nautoObject[0].debitedAccount.balanceSign = \"\";\r\nautoObject[0].debitedAccount.isOverDraft = false;\r\nautoObject[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject[0].debitedAmount.standardExRate = 0.0;\r\nautoObject[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject[0].rateType = \"\";\r\nautoObject[0].description = \"\";\r\nautoObject[0].defaultPercentage = 0.0;\r\nautoObject[0].changePercentage = 0.0;\r\nautoObject[0].defaultAmount = 0.0;\r\nautoObject[0].basicAmountCurrency = \"\";\r\nautoObject[0].flatAmount = 0.0;\r\nautoObject[0].isGLFound = false;\r\nautoObject[0].glVerifyMSG = \"\";\r\nautoObject", "isLocked": "false", "description": {"isNull": "true"}, "guid": "d79c4d92-0233-4754-9b38-ace13541c676", "versionId": "230ac9c2-8f83-477d-8e00-65398ad3d4cf"}, {"name": "collectableAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.86004a6f-9b72-4f74-a69a-aed7c869a281", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "seq": "6", "hasDefault": "true", "defaultValue": "20000", "isLocked": "false", "description": {"isNull": "true"}, "guid": "e1da6ca9-249a-4c24-be4b-1e4f6c14b433", "versionId": "8c7e2400-2883-430c-871d-f4f05d401601"}, {"name": "isAmntChanged", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.********-c52c-4425-8c47-741f31a7fa66", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "1", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "seq": "7", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "a43967e8-7309-48d2-983f-95cc007558c6", "versionId": "8ecc6622-3f54-44e4-8fc4-9ed92aad9263"}, {"name": "charges", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "2", "isArrayOf": "true", "classId": "/12.e3018bad-b453-4bf5-96fd-09a5141cd061", "seq": "8", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "1f9b428a-38ea-46dd-bfc5-488c8a2ecc3a", "versionId": "9be0108d-7a04-4652-a7f7-221c1ed44888"}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.e25be4bb-685f-47a4-b385-deccef9a8af7", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "2", "isArrayOf": "false", "classId": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "seq": "9", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "448b2be1-cdd6-4819-a5b8-941dd1120918", "versionId": "eb1b0feb-71a4-420b-b561-f80641f85b4f"}, {"name": "noDataMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processParameterId": "2055.2fd421df-d7c7-448c-bdaa-b0e2fe295470", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "parameterType": "2", "isArrayOf": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "seq": "10", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "isLocked": "false", "description": {"isNull": "true"}, "guid": "3501b9e6-2c46-44cb-ab2e-8d87d1e74bac", "versionId": "9a06c6bb-776b-411c-98c4-c61b1d8324c6"}], "processVariable": [{"name": "eventCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.26c6e13a-cca1-4718-b3b1-0c2f73830d00", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "1", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "300cd83c-e6dc-4eaf-a0e6-ba675b46a2e4", "versionId": "c4ad8422-01c7-4f94-a95a-6163995739df"}, {"name": "errorMSG", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.2c9a74ef-caf7-49f4-9f40-87d8beb413bf", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "2", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "true", "defaultValue": "\"Test Test\"", "guid": "5dc59f28-57ef-468e-b7bf-8584629b14e7", "versionId": "6a7eeea5-0245-4fdc-82db-a16645ff36cc"}, {"name": "InterestAndChargesList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.bde51344-9a08-478a-bf7f-dc360a21106a", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "3", "isArrayOf": "true", "isTransient": "false", "classId": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/12.e9f65280-afe9-44dc-9616-f95c2a14629e", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "11815779-372b-4aff-bd40-cc6e2c1d8f7d", "versionId": "7e98c8bf-f790-4e96-806b-f7b0c4c1ccd8"}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.b293c088-7a31-43a0-ac3d-fc336e3047fb", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "4", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "70620275-3fdf-4d30-a2ec-ba085e639387", "versionId": "4316f23e-1f03-44a3-8689-591e6f980fa2"}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.ff316da1-1c0a-4065-907a-81200b7c567e", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "5", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "bbcba873-893c-4bc8-91cc-da27e4d24ed3", "versionId": "f6e536b2-9e18-4a1c-8ee9-987c60c30f89"}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.04ca9b7f-e2b2-455d-940b-3a6314b0246d", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "6", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "978940c4-bdc3-4599-99de-c6490503b4da", "versionId": "fbdecdf2-5adf-426c-8b76-5cb02bfe917c"}, {"name": "xmlRequest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.3c3ede3c-8a3c-44d5-a5c2-f01d2b7ec60b", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "7", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "f5ed1b3d-e74b-4c25-bb56-94ed0a3112cf", "versionId": "43cff6c4-4d37-4247-83fc-79deaef983f7"}, {"name": "xmlResponse", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.8427754b-d8e3-4d14-9532-da4befaba0ad", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "8", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "50ef4c12-b312-4b67-b203-483530c459fa", "versionId": "77070616-291a-4b40-96aa-cf51bd4a18e0"}, {"name": "inputList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.70cf6b8a-f33e-48ba-a228-2609f4189524", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "9", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "200ab414-db10-4e48-85b3-9e84d2b5d9e1", "versionId": "2d375941-937a-47f0-9027-0705baf2ddef"}, {"name": "amntList", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.85cc93f6-95b8-49b7-bbe0-6d19190c38b1", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "10", "isArrayOf": "true", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "1634fd27-ae02-46b6-a567-68b04591d3a2", "versionId": "f107d184-2b10-4196-a397-ca2ec21e0bab"}, {"name": "hasFixedRate", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processVariableId": "2056.a6a7a146-00bf-497d-965a-d8fe74c31038", "description": {"isNull": "true"}, "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "namespace": "2", "seq": "11", "isArrayOf": "false", "isTransient": "false", "classId": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "hasDefault": "false", "defaultValue": {"isNull": "true"}, "guid": "3d76a06c-2f8a-4bbc-92a8-bb9c341276fd", "versionId": "43b66db6-db60-4f50-ac53-37fe77ee31dd"}], "item": [{"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.91e479a9-da3f-4608-a607-87e2586de9bd", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "AmntChanged?", "tWComponentName": "Switch", "tWComponentId": "3013.42f6e4ff-976b-43ca-8e85-38e0452f7b35", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35ed", "versionId": "16573b4b-b8af-4aa2-8750-944c88a80e57", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "94", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.42f6e4ff-976b-43ca-8e85-38e0452f7b35", "guid": "41f4c283-6739-44a1-a6a5-903de45f2b5e", "versionId": "f7dcf383-5831-4d33-b133-052f3c71a0fc", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.4609a927-e88c-4d23-bf65-8984f0fb5615", "switchId": "3013.42f6e4ff-976b-43ca-8e85-38e0452f7b35", "seq": "1", "endStateId": "guid:818bf8fd736d6b59:764a22fc:18ffdba29b8:b3f", "condition": "tw.local.isAmntChanged\t  ==\t  true", "guid": "637c05ad-8f8d-4ee3-99d4-b81ab84617a4", "versionId": "7247f566-0007-46f5-a864-5080d8ab1dfb"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "Charges MW", "tWComponentName": "SubProcess", "tWComponentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35e8", "versionId": "33520acd-fa0d-4c5d-aebc-1478afc54236", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.0fc6a86c-f0a6-4dbf-b890-4cf75b2c0850", "processItemId": "2025.f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "location": "2", "script": "if (tw.local.InterestAndChargesList == undefined || tw.local.InterestAndChargesList == null) {\r\r\n\ttw.local.InterestAndChargesList = [];\r\r\n}", "guid": "305396ac-c736-4625-810e-cb33a60c671b", "versionId": "035d9e9c-7a73-49c7-a181-3a833e4b56bf"}, "layoutData": {"x": "377", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error1", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3604", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "attachedProcessRef": "e7f5bcc3-f81d-4081-8e50-1313adb0bef4/1.e059295b-f72a-4e32-a329-8d32ebe941de", "guid": "3d77e0a5-4816-4ca7-a2c8-36df153554e3", "versionId": "d46baf6b-a00a-49a1-9323-ecbd740e7f05", "parameterMapping": [{"name": "chargesAndInterest", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1ccee4c2-57a7-41f5-8afd-8546ef3d2adc", "processParameterId": "2055.dd74dd40-8fa5-4359-8243-08e144b543d2", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.local.InterestAndChargesList", "classRef": "/12.e9f65280-afe9-44dc-9616-f95c2a14629e", "isList": "true", "isInput": "false", "guid": "da78bd5e-aae3-4434-9801-22549c4e1fd0", "versionId": "01e80022-a27d-4174-b43e-2dffa00ce6aa", "description": {"isNull": "true"}}, {"name": "errorCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.c3914141-0093-4879-aa24-3d16f4d36ea3", "processParameterId": "2055.78c4796d-3236-45f3-883b-556500834b95", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.local.errorCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "8fe3987d-**************-327d4e4ea1e0", "versionId": "01eddcca-9ac1-425d-8f8f-256486afadb6", "description": {"isNull": "true"}}, {"name": "userID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.15158dfa-b787-40ad-b078-6d0a732ad4ad", "processParameterId": "2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.system.user_id", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "23b83f2e-5d1a-4bf5-bba9-79494c516f31", "versionId": "1ffbf901-3d47-4852-bb17-281f8b8b44c6", "description": {"isNull": "true"}}, {"name": "snapshot", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0ae33f2b-ea46-45fa-a3c2-1d284be180b2", "processParameterId": "2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "04b52ab5-6d05-4a7e-8ae6-fc223ae7be92", "versionId": "2a486a5d-c7de-41d1-9e5c-bb256008d453", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.b3a8123b-91d1-40bd-937a-************", "processParameterId": "2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.local.errorMSG", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "73ae2440-0cd0-4226-a9e2-57919c35de65", "versionId": "399d633a-0a82-4867-bead-bf420ffdbfc6", "description": {"isNull": "true"}}, {"name": "prefix", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1f751590-f94c-4a64-af88-b8b0defef755", "processParameterId": "2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.env.prefix", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "116a5e55-95de-4b54-8a78-4a94a3e4e527", "versionId": "3c6b2d9f-2c68-406f-91c0-cbee84f3d1d3", "description": {"isNull": "true"}}, {"name": "requestAppID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.426624db-c528-4121-9994-15ba06acb989", "processParameterId": "2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.system.model.processApp.currentSnapshot.name", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "6a7a1736-cff9-4714-91f6-f80ac53b3ded", "versionId": "448b28a0-4db9-4ea4-8c35-69aa0afc0f3f", "description": {"isNull": "true"}}, {"name": "event", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e8b31c38-3ffe-49df-b66f-a034bbffae37", "processParameterId": "2055.d8783716-2453-46e3-8522-b1b2504092a2", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.local.eventCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "7ac35d4c-0e59-42fa-848e-f7cda94e50b3", "versionId": "57c28a5e-015e-47d6-8327-8a7806932364", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.4edb4e7f-c3b5-461d-9828-f2c42eedc6b4", "processParameterId": "2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "c82b6111-bb09-4c00-a274-8c10be8965d1", "versionId": "6c3944a3-a895-48f8-80ff-e4503ad31e53", "description": {"isNull": "true"}}, {"name": "productCode", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.642db792-042a-4f0d-ad6d-fbdbf7deb46b", "processParameterId": "2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.local.productCode", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "92a4ae7b-8fb8-4a00-9cc1-45a351dd187e", "versionId": "7bcf054c-7d43-457b-8023-ff0f23cc74c3", "description": {"isNull": "true"}}, {"name": "ContractCurrency", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.1a2e2edc-f017-43f7-9baf-cb12b52f0857", "processParameterId": "2055.fc2b795d-a78b-4e6c-8b59-c821d346b07f", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.local.requestCcy", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "4cd89eab-72da-45d7-b264-baaac883b682", "versionId": "aba0d6a1-11cd-40dd-a56e-30f113b9deae", "description": {"isNull": "true"}}, {"name": "instanceID", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5534b0d2-faf5-4d51-8742-2b51184e2399", "processParameterId": "2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "tw.system.currentProcessInstanceID", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "cfd3cbd7-fc81-400a-95dc-8c9cf7f7ecff", "versionId": "ddce6e78-000a-495d-875b-b76faa355ae6", "description": {"isNull": "true"}}, {"name": "processName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.0f64b479-da03-428b-a178-b6c48f4e6dc1", "processParameterId": "2055.b6919232-c019-43a5-8742-9783cfd63371", "parameterMappingParentId": "3012.138bb187-9d9e-4416-8c32-4d5695af6407", "useDefault": "false", "value": "\"ODC CREATION AND AMENDMENT\"", "classRef": "99742cc8-2f9d-4785-a6ca-364daee0b398/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "1c692dd4-ff37-483b-81e3-95a51caf6f0f", "versionId": "fd24aaf7-72a9-4d87-b7bb-fa3cf9f6599f", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.8a59f99e-37b8-4ae1-a389-9775b8ff71df", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "Set Event Code", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.f3d84288-0715-43c4-a54a-f325524b8d09", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35e9", "versionId": "42013752-df0f-4cbf-a826-9e1ac4e32584", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "227", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3604", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.f3d84288-0715-43c4-a54a-f325524b8d09", "scriptTypeId": "2", "isActive": "true", "script": "try {\r\r\n\ttw.local.amntList = [];\r\r\n\ttw.local.inputList = [];\r\r\n\ttw.local.hasFixedRate = false;\r\r\n\r\r\n\tif (tw.local.requestType == tw.epv.RequestType.Amendment) {\r\r\n\t\ttw.local.event = \"AMND\";\r\r\n\t\ttw.local.eventCode = \"AMND\";\r\r\n\t}else if (tw.local.requestType == tw.epv.RequestType.Collection){\r\r\n\t\ttw.local.eventCode = \"LIQD\";\r\r\n\t}else{\r\r\n\t\ttw.local.eventCode = \"INIT\";\r\r\n\t}\r\r\n} catch (err) {\r\r\n\ttw.local.errorMSG = err.message;\r\r\n\tthrow new Error(tw.local.errorMSG);\r\r\n}", "isRule": "false", "guid": "acd281a4-193f-4c04-a4c1-e4babebf93aa", "versionId": "df238a36-add0-4f42-a008-cdb97185b6d8"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.a4820def-4867-4c9e-8598-90726f0c65e5", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "hasFixedRate?", "tWComponentName": "Switch", "tWComponentId": "3013.a6870496-70b9-4104-b094-09ee429f870c", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:36ac", "versionId": "5f62b6b3-6f20-4324-ae2b-9bb8ba5a45cc", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "layoutData": {"x": "656", "y": "76", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchId": "3013.a6870496-70b9-4104-b094-09ee429f870c", "guid": "1abc61f7-3fdb-485f-bc84-7f971f86c50a", "versionId": "4c119b7e-e5b1-4162-8c7b-18e79963c09a", "SwitchCondition": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "switchConditionId": "3014.c2a6e661-1dfe-490b-94b7-29f19c4c23e1", "switchId": "3013.a6870496-70b9-4104-b094-09ee429f870c", "seq": "1", "endStateId": "guid:818bf8fd736d6b59:764a22fc:18ffdba29b8:b40", "condition": "tw.local.hasFixedRate\t  ==\t  false", "guid": "2029931d-6106-49cd-8671-3c2d01a65f5d", "versionId": "0e57539d-2159-4667-81c4-2da3c21f8c8d"}}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "Exception Handling", "tWComponentName": "SubProcess", "tWComponentId": "3012.621192be-d1bb-41af-a2ed-e0f03e80c293", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3604", "versionId": "abb70ea1-a5cc-46c1-b2ca-34a65c40e2ee", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": "#FF7782", "layoutData": {"x": "523", "y": "259", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.621192be-d1bb-41af-a2ed-e0f03e80c293", "attachedProcessRef": "/1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "guid": "fb839867-638b-4f6a-9f8b-ac63efd699ef", "versionId": "22d3056b-efd8-457e-b9ee-eb683ab2505e", "parameterMapping": [{"name": "serviceName", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5cd4b3a9-1ad4-4945-924c-e9c042545893", "processParameterId": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "parameterMappingParentId": "3012.621192be-d1bb-41af-a2ed-e0f03e80c293", "useDefault": "false", "value": "\"Get ODC Charges\"", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "db6e8281-8964-4204-b538-************", "versionId": "ae472237-f47f-4a9b-8169-9f46746cfb1a", "description": {"isNull": "true"}}, {"name": "isSuccessful", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.efb40b22-75a8-4ab7-b990-abd752cd4528", "processParameterId": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "parameterMappingParentId": "3012.621192be-d1bb-41af-a2ed-e0f03e80c293", "useDefault": "false", "value": "tw.local.isSuccessful", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isList": "false", "isInput": "false", "guid": "3673e247-d0fa-4a07-b736-7c3283a23f0e", "versionId": "c39dff1d-4505-4a1c-a4f9-60339320e4cd", "description": {"isNull": "true"}}, {"name": "errorMessage", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.5dd5e559-d123-4205-bd34-99c6414aa09a", "processParameterId": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "parameterMappingParentId": "3012.621192be-d1bb-41af-a2ed-e0f03e80c293", "useDefault": "false", "value": "tw.local.errorMSG", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "false", "guid": "8b739cda-4ef3-4a4d-8793-dc6671b9b42a", "versionId": "df43c092-c76b-427d-8264-cb1ce76878d0", "description": {"isNull": "true"}}]}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "Map Output", "tWComponentName": "<PERSON><PERSON><PERSON>", "tWComponentId": "3011.dfae1398-c4d3-470a-8101-0a30ccfe969b", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35ec", "versionId": "cf678686-c670-4e08-8124-f7775f4f473a", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.6a48c4c8-09be-448d-b86c-83148abf5a9c", "processItemId": "2025.15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "location": "2", "script": {"isNull": "true"}, "guid": "46abd21f-324b-456c-8698-5c6a78d6ca63", "versionId": "d1c6b36c-8055-4945-8f77-a151345460ca"}, "layoutData": {"x": "524", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error2", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3604", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "scriptId": "3011.dfae1398-c4d3-470a-8101-0a30ccfe969b", "scriptTypeId": "2", "isActive": "true", "script": "try{\r\r\nvar j =0;\r\r\ntw.local.charges = [];\r\r\n\r\r\nif (tw.local.InterestAndChargesList.listLength > 0) {\r\r\n\r\r\n\tfor (var i=0; i<tw.local.InterestAndChargesList.listLength ; i++) {\r\r\n\t\r\r\n\t\tif (tw.local.InterestAndChargesList[i].ruleType == \"Charge\"){\r\r\n\t\t\r\r\n\t\t\ttw.local.charges[j] = {};\r\r\n\t\t\ttw.local.charges[j].defaultCurrency = {};\r\r\n\t\t\ttw.local.charges[j].component = tw.local.InterestAndChargesList[i].component;\r\r\n\t\t\ttw.local.charges[j].description = tw.local.InterestAndChargesList[i].description;\r\r\n\t\t\ttw.local.charges[j].rateType = tw.local.InterestAndChargesList[i].rateType;\r\r\n\t\t\ttw.local.charges[j].waiver = false;\r\r\n\t\t\ttw.local.charges[j].debitedAmount = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount.currency = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount.accountClass = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount.accountClass.name = \"001\";\r\r\n\t\t\ttw.local.charges[j].debitedAccount.isOverDraft = null;\r\r\n//\t\t\ttw.local.charges[j].debitedAccount.accountClass.name = \"Customer Account\";\r\r\n\r\r\n//\t\t\ttw.local.charges[j].bookingCcy = tw.local.InterestAndChargesList[i].bookingCcy;\r\r\n//\t\t\ttw.local.charges[j].basisAmntCcy = tw.local.InterestAndChargesList[i].basisAmountCurrency;\r\r\n\t\r\r\n\t      //FLAT AMOUNT-----------------------------------------------------------------------------------------------\r\r\n\t\tif (tw.local.InterestAndChargesList[i].rateType == \"Flat Amount\") {\r\r\n\t\t\r\r\n\t\t\tvar flatAmnt = tw.local.InterestAndChargesList[i].flatAmount;\r\r\n\t\t\tvar basisAmntCcy = tw.local.InterestAndChargesList[i].basisAmountCurrency;\r\r\n\t\t\tvar bookingCcy = tw.local.InterestAndChargesList[i].bookingCcy;\r\r\n\t\t\tvar requestCcy = tw.local.requestCcy;\r\r\n\t\t\t\r\r\n\t\t\t//Contract CCY - Charge CCY\r\r\n\t\t\tif (bookingCcy === \"Charge CCY\") {\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.name =  basisAmntCcy;//###########################################BasicAmountCcy\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.value =  basisAmntCcy;\r\r\n\t\t\t\t\r\r\n\t\t\t\ttw.local.charges[j].defaultAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t\ttw.local.charges[j].changeAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t\t\r\r\n\t\t\t}else if(bookingCcy === \"Contract CCY\"){\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.name =  requestCcy;//###########################################RequestCcy\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.value =  requestCcy;\r\r\n\t\t\t\t\r\r\n\t\t\t\ttw.local.charges[j].flatAmount = flatAmnt;\r\r\n\t\t\t\ttw.local.charges[j].defaultAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t\ttw.local.charges[j].changeAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t        \r\r\n\t     //FIXED RATE---------------------------------------------------------------------------------------------------\r\r\n\t        if (tw.local.InterestAndChargesList[i].rateType == \"Fixed Rate\") {\r\r\n\t            tw.local.charges[j].defaultCurrency.name = requestCcy;\r\r\n\t            tw.local.charges[j].defaultCurrency.value = requestCcy;\r\r\n\t            \r\r\n\t\t\ttw.local.charges[j].defaultPercentage = tw.local.InterestAndChargesList[i].rate;\r\r\n\t\t\ttw.local.charges[j].changePercentage = tw.local.InterestAndChargesList[i].rate;\r\r\n\t\t\ttw.local.hasFixedRate = true;\r\r\n\t        }\r\r\n\t        j+=1;\r\r\n\t    }\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}else{\r\r\n\ttw.local.noDataMessage = \"No Valid Charges Found\";\r\r\n}\r\r\n\r\r\n} catch (err) {\r\r\n\ttw.local.errorMSG = err.message;\r\r\n\tthrow new Error(tw.local.errorMSG);\r\r\n}", "isRule": "false", "guid": "6da5840c-97ad-4de5-b508-d7c07e3dbc3f", "versionId": "36cc78a4-56a8-4f51-8024-03b81a70f8e5"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "End", "tWComponentName": "ExitPoint", "tWComponentId": "3008.6e1d7504-b0f9-41fb-8d47-91dcc0a60298", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "true", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "false", "errorHandlerItemId": {"isNull": "true"}, "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35e7", "versionId": "e52519ec-aced-494c-a882-4af9956e1cee", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.47f282ca-aeee-4323-9b6f-b086d2dd9ac9", "processItemId": "2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "location": "1", "script": {"isNull": "true"}, "guid": "b7d53714-a967-4054-ba34-8875de96c1bc", "versionId": "38744ef1-22ef-40e4-a62b-1cdae645d24a"}, "layoutData": {"x": "966", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "exitPointId": "3008.6e1d7504-b0f9-41fb-8d47-91dcc0a60298", "haltProcess": "false", "guid": "9556ed27-980d-4989-b1d6-c27e3e1b2f5e", "versionId": "91631805-835b-4f2a-b048-ad247d3e0e3d"}}, {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemId": "2025.fd09a318-5101-424d-883c-4db3a15eb879", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "name": "Calc Default Amount", "tWComponentName": "SubProcess", "tWComponentId": "3012.f2ea3974-ddfc-44fa-b703-b926191b8afc", "isLogEnabled": "false", "isTraceEnabled": "false", "traceCategory": {"isNull": "true"}, "traceLevel": {"isNull": "true"}, "traceMessage": {"isNull": "true"}, "traceSymbolTable": {"isNull": "true"}, "isExecutionContextTraced": "false", "saveExecutionContext": "false", "documentation": {"isNull": "true"}, "isErrorHandlerEnabled": "true", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "guid": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35ea", "versionId": "eb8c1a57-fc59-41f0-965a-8b5af756d94b", "externalServiceRef": {"isNull": "true"}, "externalServiceOp": {"isNull": "true"}, "nodeColor": {"isNull": "true"}, "processPrePosts": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processItemPrePostId": "2029.37240163-8d47-4643-b878-cbc9c95c3bef", "processItemId": "2025.fd09a318-5101-424d-883c-4db3a15eb879", "location": "2", "script": {"isNull": "true"}, "guid": "8066bd3e-ec37-4faf-9125-479c6742a256", "versionId": "1651695a-5cc4-4fa2-bb72-79279676b2b5"}, "layoutData": {"x": "785", "y": "57", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "errorHandler": {"name": "Error4", "locationId": "bottomCenter", "faultStyle": "1", "isCatchAll": "true", "errorHandlerItem": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3604", "errorHandlerItemId": "2025.d143bea6-4b15-4826-8320-1bb2356a347a", "errorHandlerLink": {"fromPort": {"locationId": "bottomCenter", "portType": "1"}, "toPort": {"locationId": "topCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "TWComponent": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "subProcessId": "3012.f2ea3974-ddfc-44fa-b703-b926191b8afc", "attachedProcessRef": "/1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd", "guid": "f86192ad-2e22-4ae7-9448-f7b28a2a6804", "versionId": "9c207542-8e75-4f46-b102-ea65474bd8ea", "parameterMapping": [{"name": "charges", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.48a9f784-9c4a-4515-8636-f6610cb6ae39", "processParameterId": "2055.f3009431-81a1-4331-8f48-100ccdda4aea", "parameterMappingParentId": "3012.f2ea3974-ddfc-44fa-b703-b926191b8afc", "useDefault": "false", "value": "tw.local.charges", "classRef": "/12.e3018bad-b453-4bf5-96fd-09a5141cd061", "isList": "true", "isInput": "false", "guid": "5bd47286-e372-486f-a2b7-fb5156107dc9", "versionId": "177178df-0546-43e9-ba9d-9df702d17d7e", "description": {"isNull": "true"}}, {"name": "collateralAmnt", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.ff49b1a0-44e0-45f6-b0b7-034090104510", "processParameterId": "2055.ba300aef-d348-49c6-bd13-01d0154c466e", "parameterMappingParentId": "3012.f2ea3974-ddfc-44fa-b703-b926191b8afc", "useDefault": "false", "value": "tw.local.collectableAmnt.toString()", "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isList": "false", "isInput": "true", "guid": "35d5a123-14df-49ec-90da-052ab8876f52", "versionId": "66e75a99-0b17-437e-808d-05047a81c2eb", "description": {"isNull": "true"}}, {"name": "charges", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.be3d6fe1-deff-491e-b835-7cc73b40aa4d", "processParameterId": "2055.737f0e64-d76d-4c76-9b4b-e87c7878b455", "parameterMappingParentId": "3012.f2ea3974-ddfc-44fa-b703-b926191b8afc", "useDefault": "false", "value": "tw.local.charges", "classRef": "/12.e3018bad-b453-4bf5-96fd-09a5141cd061", "isList": "true", "isInput": "true", "guid": "f624eab3-5116-4cef-bb00-61b6a7ef74a6", "versionId": "d673b5ab-7fbf-425c-b4bf-1cc7d86615a2", "description": {"isNull": "true"}}, {"name": "error", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "parameterMappingId": "2054.e7f01e2b-0d66-4363-9c0e-2727ff990bbf", "processParameterId": "2055.880a59bf-b853-4f92-94be-160455121ee0", "parameterMappingParentId": "3012.f2ea3974-ddfc-44fa-b703-b926191b8afc", "useDefault": "false", "value": "tw.local.error", "classRef": "28e0abf4-f65e-4a08-bd7b-dfe602249008/12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isList": "false", "isInput": "false", "guid": "3a675eea-8e41-48e9-881e-102b2fd60660", "versionId": "f7219e51-036d-4f22-b22a-6e800aa57db7", "description": {"isNull": "true"}}]}}], "EPV_PROCESS_LINK": {"lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "epvProcessLinkId": "2058.d579e57e-f60c-4189-ae80-2099ef14e564", "epvId": "/21.f79b6325-0b4a-48cd-b342-f9a61300eace", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "guid": "34083ac5-8f51-4ba5-8723-e1efd4ac549c", "versionId": "fce1b727-6349-444d-9c50-2cfff14549d7"}, "layoutData": {"noConversion": "true", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "startPoint": {"layoutData": {"x": "25", "y": "80", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "startLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftCenter", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "globalExceptionHandler": {"layoutData": {"x": "416", "y": "299", "errorLink": {"controlPoints": "", "showEndState": "false", "showName": "false"}}}, "globalExceptionLink": {"fromPort": {"locationId": "rightCenter", "portType": "1"}, "toPort": {"locationId": "leftBottom", "portType": "2"}, "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}}, "bpmn2Model": {"ns16:definitions": {"xmlns:ns16": "http://www.omg.org/spec/BPMN/20100524/MODEL", "xmlns:ns2": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup", "xmlns:ns3": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process", "xmlns:ns4": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle", "xmlns:ns5": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case", "xmlns:ns6": "http://www.ibm.com/bpm/Extensions", "xmlns:ns7": "http://www.ibm.com/xmlns/prod/bpm/uca", "xmlns:ns8": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team", "xmlns:ns9": "http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary", "xmlns:ns10": "http://www.omg.org/spec/DD/20100524/DI", "xmlns:ns11": "http://www.omg.org/spec/DD/20100524/DC", "xmlns:ns12": "http://www.omg.org/spec/BPMN/20100524/BPMNDI", "xmlns:ns13": "http://www.ibm.com/xmlns/prod/bpm/graph", "xmlns:ns14": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth", "xmlns:ns15": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice", "xmlns:ns17": "http://www.ibm.com/bpm/processappsettings", "xmlns:ns18": "http://www.ibm.com/xmlns/links", "xmlns:ns19": "http://www.ibm.com/bpm/CoachDesignerNG", "xmlns:ns20": "http://www.ibm.com/xmlns/tagging", "xmlns:ns21": "http://www.ibm.com/bpm/uitheme", "xmlns:ns22": "http://www.ibm.com/bpm/coachview", "targetNamespace": "", "expressionLanguage": "http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript", "typeLanguage": "http://www.w3.org/2001/XMLSchema", "ns16:process": {"name": "Get Charges Completed", "id": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "ns3:executionMode": "microflow", "ns16:documentation": {"textFormat": "text/plain"}, "ns16:extensionElements": {"ns3:sboSyncEnabled": "true", "ns3:isSecured": "false", "ns3:isAjaxExposed": "true"}, "ns16:ioSpecification": {"ns16:extensionElements": {"ns3:epvProcessLinks": {"ns3:epvProcessLinkRef": {"epvId": "21.f79b6325-0b4a-48cd-b342-f9a61300eace", "epvProcessLinkId": "b5808579-505b-40b4-8cc5-fdb2d030b979"}}}, "ns16:dataInput": [{"name": "event", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.ad7e0a45-5cb2-437f-b705-2ff310914291", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"INIT\"", "useDefault": "false"}}}, {"name": "requestCcy", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.1001aa15-5c17-4db8-ab30-0263431130b2", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"EGP\"", "useDefault": "true"}}}, {"name": "productCode", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.7d751a23-0e1b-4987-a9bc-41b25f644f54", "ns16:extensionElements": {"ns3:defaultValue": {"_": "//\"IAVC\"\r\r\n\"IUDC\"", "useDefault": "false"}}}, {"name": "requestType", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.66b62639-4042-49bf-9544-dbb15f00f3be", "ns16:extensionElements": {"ns3:defaultValue": {"_": "tw.epv.RequestType.Create", "useDefault": "true"}}}, {"name": "charges", "itemSubjectRef": "itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061", "isCollection": "true", "id": "2055.12683c54-ad5b-4c68-b734-93d1dea93938", "ns16:extensionElements": {"ns3:defaultValue": {"_": "var autoObject = new tw.object.listOf.ChargesAndCommissions();\r\nautoObject[0] = new tw.object.ChargesAndCommissions();\r\nautoObject[0].component = \"\";\r\nautoObject[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject[0].defaultCurrency.name = \"\";\r\nautoObject[0].defaultCurrency.value = \"\";\r\nautoObject[0].changeAmount = 0.0;\r\nautoObject[0].waiver = false;\r\nautoObject[0].debitedAccount = new tw.object.AccountDetails();\r\nautoObject[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject[0].debitedAccount.accountClass.name = \"\";\r\nautoObject[0].debitedAccount.accountClass.value = \"\";\r\nautoObject[0].debitedAccount.glAccountNo = \"\";\r\nautoObject[0].debitedAccount.customerAccountNo = \"\";\r\nautoObject[0].debitedAccount.branchCode = \"\";\r\nautoObject[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\r\nautoObject[0].debitedAccount.currency.name = \"\";\r\nautoObject[0].debitedAccount.currency.value = \"\";\r\nautoObject[0].debitedAccount.balance = 0.0;\r\nautoObject[0].debitedAccount.balanceSign = \"\";\r\nautoObject[0].debitedAccount.isOverDraft = false;\r\nautoObject[0].debitedAmount = new tw.object.AmountDetails();\r\nautoObject[0].debitedAmount.standardExRate = 0.0;\r\nautoObject[0].debitedAmount.negotiatedExRate = 0.0;\r\nautoObject[0].debitedAmount.amountInAccount = 0.0;\r\nautoObject[0].rateType = \"\";\r\nautoObject[0].description = \"\";\r\nautoObject[0].defaultPercentage = 0.0;\r\nautoObject[0].changePercentage = 0.0;\r\nautoObject[0].defaultAmount = 0.0;\r\nautoObject[0].basicAmountCurrency = \"\";\r\nautoObject[0].flatAmount = 0.0;\r\nautoObject[0].isGLFound = false;\r\nautoObject[0].glVerifyMSG = \"\";\r\nautoObject", "useDefault": "true"}}}, {"name": "collectableAmnt", "itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "false", "id": "2055.86004a6f-9b72-4f74-a69a-aed7c869a281", "ns16:extensionElements": {"ns3:defaultValue": {"_": "20000", "useDefault": "true"}}}, {"name": "isAmntChanged", "itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "id": "2055.********-c52c-4425-8c47-741f31a7fa66", "ns16:extensionElements": {"ns3:defaultValue": {"_": "false", "useDefault": "false"}}}], "ns16:dataOutput": [{"name": "charges", "itemSubjectRef": "itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061", "isCollection": "true", "id": "2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0"}, {"name": "error", "itemSubjectRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45", "isCollection": "false", "id": "2055.e25be4bb-685f-47a4-b385-deccef9a8af7"}, {"name": "noDataMessage", "itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "id": "2055.2fd421df-d7c7-448c-bdaa-b0e2fe295470"}], "ns16:inputSet": {"ns16:dataInputRefs": ["2055.ad7e0a45-5cb2-437f-b705-2ff310914291", "2055.1001aa15-5c17-4db8-ab30-0263431130b2", "2055.7d751a23-0e1b-4987-a9bc-41b25f644f54", "2055.66b62639-4042-49bf-9544-dbb15f00f3be", "2055.12683c54-ad5b-4c68-b734-93d1dea93938", "2055.86004a6f-9b72-4f74-a69a-aed7c869a281", "2055.********-c52c-4425-8c47-741f31a7fa66"]}, "ns16:outputSet": {"ns16:dataOutputRefs": ["2055.d7fd9cdb-ae43-4456-985d-9d004ab1dcf0", "2055.e25be4bb-685f-47a4-b385-deccef9a8af7", "2055.2fd421df-d7c7-448c-bdaa-b0e2fe295470"]}}, "ns16:laneSet": {"id": "6c828734-62d0-4148-b98d-b573440fe0cd", "ns16:lane": {"name": "System", "partitionElementRef": "24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b", "id": "3df597f4-a975-4960-bb3e-856990b317ea", "ns4:isSystemLane": "true", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "0", "y": "0", "width": "3000", "height": "500", "color": "#F8F8F8"}}, "ns16:flowNodeRef": ["105e3d80-0eb1-4e46-be98-d80ea339739e", "fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "8a59f99e-37b8-4ae1-a389-9775b8ff71df", "15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "91e479a9-da3f-4608-a607-87e2586de9bd", "cbd21862-4ce4-4e97-bfd3-943f8535831d", "ffaf0225-b6d1-4031-bd93-53ae6bebd8dd", "08e7321e-c669-4f2d-b928-6ec12f86f7c8", "fd09a318-5101-424d-883c-4db3a15eb879", "fc6174ed-5c04-4971-8537-7c53279af857", "d143bea6-4b15-4826-8320-1bb2356a347a", "a4820def-4867-4c9e-8598-90726f0c65e5", "60f2e4ff-ef7d-4399-8eb9-b942db9a6434"]}}, "ns16:startEvent": {"isInterrupting": "false", "parallelMultiple": "false", "name": "Start", "id": "105e3d80-0eb1-4e46-be98-d80ea339739e", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "25", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}}, "ns16:outgoing": "3169196d-8190-4230-94ba-9b0a5be3feda"}, "ns16:endEvent": {"name": "End", "id": "fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "966", "y": "80", "width": "24", "height": "24", "color": "#F8F8F8"}, "ns4:saveExecutionContext": "true", "ns3:endStateId": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:35e7", "ns3:preAssignmentScript": ""}, "ns16:incoming": ["2bbd3977-29c3-4a57-8acb-7471aa53ad72", "1c1cd577-d00d-45c6-8df5-38e3aec21d86", "23dc5a98-be81-4e6f-8e45-52666fca2afe"]}, "ns16:callActivity": [{"calledElement": "1.e059295b-f72a-4e32-a329-8d32ebe941de", "name": "Charges MW", "id": "f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "377", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:postAssignmentScript": "if (tw.local.InterestAndChargesList == undefined || tw.local.InterestAndChargesList == null) {\r\r\n\ttw.local.InterestAndChargesList = [];\r\r\n}"}, "ns16:incoming": "bea97401-8a95-441f-9f38-fc74fa6cfbf5", "ns16:outgoing": "d1a76c83-69b1-4d3e-859e-00bb1274bf3c", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.cc9ad0ce-2ac1-4b84-8ccf-c315832bb78a", "ns16:assignment": {"ns16:from": {"_": "tw.local.productCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.e710cd13-8a5f-4725-8b9a-5b6d41ea1820", "ns16:assignment": {"ns16:from": {"_": "tw.system.user_id", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.5fc47a08-7d5f-4227-8204-7ec10c72ea66", "ns16:assignment": {"ns16:from": {"_": "tw.system.currentProcessInstanceID", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.1bcf9cd7-71f0-424b-8510-e03e5fa1b0b6", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.3a2a24fc-f4e3-4263-81e7-6d9e710f3908", "ns16:assignment": {"ns16:from": {"_": "tw.system.model.processApp.currentSnapshot.name", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.1a22dd9c-8137-48eb-8a8b-cf03c32118b1", "ns16:assignment": {"ns16:from": {"_": "tw.env.prefix", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}, {"ns16:targetRef": "2055.d8783716-2453-46e3-8522-b1b2504092a2", "ns16:assignment": {"ns16:from": {"_": "tw.local.eventCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.fc2b795d-a78b-4e6c-8b59-c821d346b07f", "ns16:assignment": {"ns16:from": {"_": "tw.local.requestCcy", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.b6919232-c019-43a5-8742-9783cfd63371", "ns16:assignment": {"ns16:from": {"_": "\"ODC CREATION AND AMENDMENT\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.679f855a-2f38-4cd2-8d6f-6aebaae02d71", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.78c4796d-3236-45f3-883b-556500834b95", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorCode", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:sourceRef": "2055.dd74dd40-8fa5-4359-8243-08e144b543d2", "ns16:assignment": {"ns16:to": {"_": "tw.local.InterestAndChargesList", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e"}}}, {"ns16:sourceRef": "2055.fdbc9e9a-ec0b-4934-8f69-599de31f1fa0", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMSG", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}, {"calledElement": "1.4fce0274-fc59-42b2-9c70-d21b9bfbb9fd", "name": "Calc Default Amount", "id": "fd09a318-5101-424d-883c-4db3a15eb879", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "785", "y": "57", "width": "95", "height": "70"}, "ns4:activityType": "CalledProcess", "ns3:postAssignmentScript": ""}, "ns16:incoming": ["a2859f0c-f8bd-45e5-bd56-8d7887016b2a", "06860578-0514-48d9-8898-6a16b7633175"], "ns16:outgoing": "23dc5a98-be81-4e6f-8e45-52666fca2afe", "ns16:dataInputAssociation": [{"ns16:targetRef": "2055.737f0e64-d76d-4c76-9b4b-e87c7878b455", "ns16:assignment": {"ns16:from": {"_": "tw.local.charges", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061"}}}, {"ns16:targetRef": "2055.ba300aef-d348-49c6-bd13-01d0154c466e", "ns16:assignment": {"ns16:from": {"_": "tw.local.collectableAmnt.toString()", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, {"ns16:targetRef": "2055.d430a477-84cf-4f48-9ad4-6351b6639119", "ns16:assignment": {"ns16:from": {"xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297"}}}], "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.f3009431-81a1-4331-8f48-100ccdda4aea", "ns16:assignment": {"ns16:to": {"_": "tw.local.charges", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.e3018bad-b453-4bf5-96fd-09a5141cd061"}}}, {"ns16:sourceRef": "2055.880a59bf-b853-4f92-94be-160455121ee0", "ns16:assignment": {"ns16:to": {"_": "tw.local.error", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.5551644c-3dc6-4161-a939-4e7f27f8ce45"}}}]}, {"calledElement": "1.2b50ff68-f7d3-4895-88ad-a73a13fa91d0", "isForCompensation": "false", "startQuantity": "1", "completionQuantity": "1", "name": "Exception Handling", "id": "d143bea6-4b15-4826-8320-1bb2356a347a", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "523", "y": "259", "width": "95", "height": "70", "color": "#FF7782"}, "ns4:activityType": "CalledProcess"}, "ns16:incoming": ["70eb5b5c-499d-49e9-88d9-4ec38e9d713f", "2866fbfc-9306-4a6b-8d0f-074c33e62b7b", "da24e7e9-fb6e-4825-806a-c9421fd2e2c9", "9294041a-4588-4b91-8e0c-f5d224cc34be", "342a6e7e-5a40-434c-80b6-2c1d2fdcd22a"], "ns16:outgoing": "2bbd3977-29c3-4a57-8acb-7471aa53ad72", "ns16:dataInputAssociation": {"ns16:targetRef": "2055.5ea77901-7a17-422a-8958-67bb0e9c991b", "ns16:assignment": {"ns16:from": {"_": "\"Get ODC Charges\"", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}, "ns16:dataOutputAssociation": [{"ns16:sourceRef": "2055.99eb514d-4b62-401e-8cdb-7edc096adffd", "ns16:assignment": {"ns16:to": {"_": "tw.local.isSuccessful", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"}}}, {"ns16:sourceRef": "2055.81b82125-a5aa-45f7-8c0f-4870667eebbf", "ns16:assignment": {"ns16:to": {"_": "tw.local.errorMSG", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression", "evaluatesToTypeRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}}}]}], "ns16:scriptTask": [{"scriptFormat": "text/x-javascript", "name": "Set Event Code", "id": "8a59f99e-37b8-4ae1-a389-9775b8ff71df", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "227", "y": "57", "width": "95", "height": "70"}}, "ns16:incoming": "81407a06-de54-414f-b760-f65d6a5277e2", "ns16:outgoing": "bea97401-8a95-441f-9f38-fc74fa6cfbf5", "ns16:script": "try {\r\r\n\ttw.local.amntList = [];\r\r\n\ttw.local.inputList = [];\r\r\n\ttw.local.hasFixedRate = false;\r\r\n\r\r\n\tif (tw.local.requestType == tw.epv.RequestType.Amendment) {\r\r\n\t\ttw.local.event = \"AMND\";\r\r\n\t\ttw.local.eventCode = \"AMND\";\r\r\n\t}else if (tw.local.requestType == tw.epv.RequestType.Collection){\r\r\n\t\ttw.local.eventCode = \"LIQD\";\r\r\n\t}else{\r\r\n\t\ttw.local.eventCode = \"INIT\";\r\r\n\t}\r\r\n} catch (err) {\r\r\n\ttw.local.errorMSG = err.message;\r\r\n\tthrow new Error(tw.local.errorMSG);\r\r\n}"}, {"scriptFormat": "text/x-javascript", "name": "Map Output", "id": "15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "524", "y": "57", "width": "95", "height": "70"}, "ns3:postAssignmentScript": ""}, "ns16:incoming": "d1a76c83-69b1-4d3e-859e-00bb1274bf3c", "ns16:outgoing": "a058d91d-b10f-4cd3-bf38-4f27c1e7402f", "ns16:script": "try{\r\r\nvar j =0;\r\r\ntw.local.charges = [];\r\r\n\r\r\nif (tw.local.InterestAndChargesList.listLength > 0) {\r\r\n\r\r\n\tfor (var i=0; i<tw.local.InterestAndChargesList.listLength ; i++) {\r\r\n\t\r\r\n\t\tif (tw.local.InterestAndChargesList[i].ruleType == \"Charge\"){\r\r\n\t\t\r\r\n\t\t\ttw.local.charges[j] = {};\r\r\n\t\t\ttw.local.charges[j].defaultCurrency = {};\r\r\n\t\t\ttw.local.charges[j].component = tw.local.InterestAndChargesList[i].component;\r\r\n\t\t\ttw.local.charges[j].description = tw.local.InterestAndChargesList[i].description;\r\r\n\t\t\ttw.local.charges[j].rateType = tw.local.InterestAndChargesList[i].rateType;\r\r\n\t\t\ttw.local.charges[j].waiver = false;\r\r\n\t\t\ttw.local.charges[j].debitedAmount = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount.currency = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount.accountClass = {};\r\r\n\t\t\ttw.local.charges[j].debitedAccount.accountClass.name = \"001\";\r\r\n\t\t\ttw.local.charges[j].debitedAccount.isOverDraft = null;\r\r\n//\t\t\ttw.local.charges[j].debitedAccount.accountClass.name = \"Customer Account\";\r\r\n\r\r\n//\t\t\ttw.local.charges[j].bookingCcy = tw.local.InterestAndChargesList[i].bookingCcy;\r\r\n//\t\t\ttw.local.charges[j].basisAmntCcy = tw.local.InterestAndChargesList[i].basisAmountCurrency;\r\r\n\t\r\r\n\t      //FLAT AMOUNT-----------------------------------------------------------------------------------------------\r\r\n\t\tif (tw.local.InterestAndChargesList[i].rateType == \"Flat Amount\") {\r\r\n\t\t\r\r\n\t\t\tvar flatAmnt = tw.local.InterestAndChargesList[i].flatAmount;\r\r\n\t\t\tvar basisAmntCcy = tw.local.InterestAndChargesList[i].basisAmountCurrency;\r\r\n\t\t\tvar bookingCcy = tw.local.InterestAndChargesList[i].bookingCcy;\r\r\n\t\t\tvar requestCcy = tw.local.requestCcy;\r\r\n\t\t\t\r\r\n\t\t\t//Contract CCY - Charge CCY\r\r\n\t\t\tif (bookingCcy === \"Charge CCY\") {\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.name =  basisAmntCcy;//###########################################BasicAmountCcy\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.value =  basisAmntCcy;\r\r\n\t\t\t\t\r\r\n\t\t\t\ttw.local.charges[j].defaultAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t\ttw.local.charges[j].changeAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t\t\r\r\n\t\t\t}else if(bookingCcy === \"Contract CCY\"){\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.name =  requestCcy;//###########################################RequestCcy\r\r\n\t\t\t\ttw.local.charges[j].defaultCurrency.value =  requestCcy;\r\r\n\t\t\t\t\r\r\n\t\t\t\ttw.local.charges[j].flatAmount = flatAmnt;\r\r\n\t\t\t\ttw.local.charges[j].defaultAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t\ttw.local.charges[j].changeAmount = Number(flatAmnt.toFixed(2));\r\r\n\t\t\t}\r\r\n\t\t}\r\r\n\t        \r\r\n\t     //FIXED RATE---------------------------------------------------------------------------------------------------\r\r\n\t        if (tw.local.InterestAndChargesList[i].rateType == \"Fixed Rate\") {\r\r\n\t            tw.local.charges[j].defaultCurrency.name = requestCcy;\r\r\n\t            tw.local.charges[j].defaultCurrency.value = requestCcy;\r\r\n\t            \r\r\n\t\t\ttw.local.charges[j].defaultPercentage = tw.local.InterestAndChargesList[i].rate;\r\r\n\t\t\ttw.local.charges[j].changePercentage = tw.local.InterestAndChargesList[i].rate;\r\r\n\t\t\ttw.local.hasFixedRate = true;\r\r\n\t        }\r\r\n\t        j+=1;\r\r\n\t    }\r\r\n\t}\r\r\n\t\r\r\n\t\r\r\n}else{\r\r\n\ttw.local.noDataMessage = \"No Valid Charges Found\";\r\r\n}\r\r\n\r\r\n} catch (err) {\r\r\n\ttw.local.errorMSG = err.message;\r\r\n\tthrow new Error(tw.local.errorMSG);\r\r\n}"}], "ns16:sequenceFlow": [{"sourceRef": "8a59f99e-37b8-4ae1-a389-9775b8ff71df", "targetRef": "f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "name": "To Charges MW", "id": "bea97401-8a95-441f-9f38-fc74fa6cfbf5", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "91e479a9-da3f-4608-a607-87e2586de9bd", "targetRef": "8a59f99e-37b8-4ae1-a389-9775b8ff71df", "name": "NO", "id": "81407a06-de54-414f-b760-f65d6a5277e2", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "105e3d80-0eb1-4e46-be98-d80ea339739e", "targetRef": "91e479a9-da3f-4608-a607-87e2586de9bd", "name": "To AmntChanged?", "id": "3169196d-8190-4230-94ba-9b0a5be3feda", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "targetRef": "a4820def-4867-4c9e-8598-90726f0c65e5", "name": "To hasFixedRate?", "id": "a058d91d-b10f-4cd3-bf38-4f27c1e7402f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "91e479a9-da3f-4608-a607-87e2586de9bd", "targetRef": "fd09a318-5101-424d-883c-4db3a15eb879", "name": "To <PERSON><PERSON> De<PERSON> Amount", "id": "a2859f0c-f8bd-45e5-bd56-8d7887016b2a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topLeft", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "740", "y": "14"}, "ns13:saveExecutionContext": "false"}}, "ns16:conditionExpression": {"_": "tw.local.isAmntChanged\t  ==\t  true", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "cbd21862-4ce4-4e97-bfd3-943f8535831d", "targetRef": "d143bea6-4b15-4826-8320-1bb2356a347a", "name": "To Exception Handling", "id": "70eb5b5c-499d-49e9-88d9-4ec38e9d713f", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "ffaf0225-b6d1-4031-bd93-53ae6bebd8dd", "targetRef": "d143bea6-4b15-4826-8320-1bb2356a347a", "name": "To Exception Handling", "id": "2866fbfc-9306-4a6b-8d0f-074c33e62b7b", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "08e7321e-c669-4f2d-b928-6ec12f86f7c8", "targetRef": "d143bea6-4b15-4826-8320-1bb2356a347a", "name": "To Exception Handling", "id": "da24e7e9-fb6e-4825-806a-c9421fd2e2c9", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "fc6174ed-5c04-4971-8537-7c53279af857", "targetRef": "d143bea6-4b15-4826-8320-1bb2356a347a", "name": "To Exception Handling", "id": "9294041a-4588-4b91-8e0c-f5d224cc34be", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "bottomCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "d143bea6-4b15-4826-8320-1bb2356a347a", "targetRef": "fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "name": "To End", "id": "2bbd3977-29c3-4a57-8acb-7471aa53ad72", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "bottomCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "a4820def-4867-4c9e-8598-90726f0c65e5", "targetRef": "fd09a318-5101-424d-883c-4db3a15eb879", "name": "yes", "id": "06860578-0514-48d9-8898-6a16b7633175", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "a4820def-4867-4c9e-8598-90726f0c65e5", "targetRef": "fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "name": "To End", "id": "1c1cd577-d00d-45c6-8df5-38e3aec21d86", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "topCenter", "ns13:targetPortLocation": "topCenter", "ns13:showLabel": "true", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:customBendPoint": {"x": "935", "y": "22"}, "ns13:saveExecutionContext": "true"}}, "ns16:conditionExpression": {"_": "tw.local.hasFixedRate\t  ==\t  false", "xmlns:xsi": "http://www.w3.org/2001/XMLSchema-instance", "xsi:type": "ns16:tFormalExpression"}}, {"sourceRef": "fd09a318-5101-424d-883c-4db3a15eb879", "targetRef": "fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "name": "To End", "id": "23dc5a98-be81-4e6f-8e45-52666fca2afe", "ns16:extensionElements": {"ns3:sequenceFlowImplementation": {"sboSyncEnabled": "true"}, "ns3:endStateId": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3674", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "true"}}}, {"sourceRef": "f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "targetRef": "15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "name": "To Map Output", "id": "d1a76c83-69b1-4d3e-859e-00bb1274bf3c", "ns16:extensionElements": {"ns3:endStateId": "guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20", "ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftCenter", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}, {"sourceRef": "60f2e4ff-ef7d-4399-8eb9-b942db9a6434", "targetRef": "d143bea6-4b15-4826-8320-1bb2356a347a", "name": "To Exception Handling", "id": "342a6e7e-5a40-434c-80b6-2c1d2fdcd22a", "ns16:extensionElements": {"ns13:linkVisualInfo": {"ns13:sourcePortLocation": "rightCenter", "ns13:targetPortLocation": "leftBottom", "ns13:showLabel": "false", "ns13:showCoachControlLabel": "false", "ns13:labelPosition": "0.0", "ns13:saveExecutionContext": "false"}}}], "ns16:dataObject": [{"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "eventCode", "id": "2056.26c6e13a-cca1-4718-b3b1-0c2f73830d00"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMSG", "id": "2056.2c9a74ef-caf7-49f4-9f40-87d8beb413bf", "ns16:extensionElements": {"ns3:defaultValue": {"_": "\"Test Test\"", "useDefault": "true"}}}, {"itemSubjectRef": "itm.12.e9f65280-afe9-44dc-9616-f95c2a14629e", "isCollection": "true", "name": "InterestAndChargesList", "id": "2056.bde51344-9a08-478a-bf7f-dc360a21106a"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorCode", "id": "2056.b293c088-7a31-43a0-ac3d-fc336e3047fb"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "errorMessage", "id": "2056.ff316da1-1c0a-4065-907a-81200b7c567e"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "isSuccessful", "id": "2056.04ca9b7f-e2b2-455d-940b-3a6314b0246d"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "xmlRequest", "id": "2056.3c3ede3c-8a3c-44d5-a5c2-f01d2b7ec60b"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "false", "name": "xmlResponse", "id": "2056.8427754b-d8e3-4d14-9532-da4befaba0ad"}, {"itemSubjectRef": "itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "isCollection": "true", "name": "inputList", "id": "2056.70cf6b8a-f33e-48ba-a228-2609f4189524"}, {"itemSubjectRef": "itm.12.536b2aa5-a30f-4eca-87fa-3a28066753ee", "isCollection": "true", "name": "amntList", "id": "2056.85cc93f6-95b8-49b7-bbe0-6d19190c38b1"}, {"itemSubjectRef": "itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "isCollection": "false", "name": "hasFixedRate", "id": "2056.a6a7a146-00bf-497d-965a-d8fe74c31038"}], "ns16:exclusiveGateway": [{"default": "81407a06-de54-414f-b760-f65d6a5277e2", "name": "AmntChanged?", "id": "91e479a9-da3f-4608-a607-87e2586de9bd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "94", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "3169196d-8190-4230-94ba-9b0a5be3feda", "ns16:outgoing": ["81407a06-de54-414f-b760-f65d6a5277e2", "a2859f0c-f8bd-45e5-bd56-8d7887016b2a"]}, {"default": "06860578-0514-48d9-8898-6a16b7633175", "name": "hasFixedRate?", "id": "a4820def-4867-4c9e-8598-90726f0c65e5", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "656", "y": "76", "width": "32", "height": "32"}}, "ns16:incoming": "a058d91d-b10f-4cd3-bf38-4f27c1e7402f", "ns16:outgoing": ["06860578-0514-48d9-8898-6a16b7633175", "1c1cd577-d00d-45c6-8df5-38e3aec21d86"]}], "ns16:boundaryEvent": [{"cancelActivity": "true", "attachedToRef": "8a59f99e-37b8-4ae1-a389-9775b8ff71df", "parallelMultiple": "false", "name": "Error", "id": "cbd21862-4ce4-4e97-bfd3-943f8535831d", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "262", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "70eb5b5c-499d-49e9-88d9-4ec38e9d713f", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "a2f9fbc4-0d52-4b82-8dcc-2149f6ee9239"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "6da7d3d8-8ca3-4559-a75f-cfea36a74691", "eventImplId": "1f409556-f821-4faa-8acd-c2fb382f1ab5", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "parallelMultiple": "false", "name": "Error1", "id": "ffaf0225-b6d1-4031-bd93-53ae6bebd8dd", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "412", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "2866fbfc-9306-4a6b-8d0f-074c33e62b7b", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "2038ea78-2088-419b-82f7-1afc21b10337"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "b7623788-a620-46a5-af45-2af125724f23", "eventImplId": "8937bcf5-4100-45ec-8d25-eeca0b100fce", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "parallelMultiple": "false", "name": "Error2", "id": "08e7321e-c669-4f2d-b928-6ec12f86f7c8", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "559", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "da24e7e9-fb6e-4825-806a-c9421fd2e2c9", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "e843c207-c19e-4de5-be9c-83541c50f9c4"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "73a97e6c-a74a-414e-887b-a406c0aa4147", "eventImplId": "01d26550-492f-47e6-8bcd-d0e6420b2c61", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}, {"cancelActivity": "true", "attachedToRef": "fd09a318-5101-424d-883c-4db3a15eb879", "parallelMultiple": "false", "name": "Error4", "id": "fc6174ed-5c04-4971-8537-7c53279af857", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "820", "y": "115", "width": "24", "height": "24"}}, "ns16:outgoing": "9294041a-4588-4b91-8e0c-f5d224cc34be", "ns16:dataOutput": {"name": "error", "itemSubjectRef": "itm.19e8dc33-1100-46be-89a6-36c9040f7b3e", "isCollection": "false", "id": "0ccb6700-2ccb-4988-8ba1-8180d12c6c3b"}, "ns16:outputSet": "", "ns16:errorEventDefinition": {"id": "34aa40bf-6f04-4f2f-ae4d-8f7a946d3197", "eventImplId": "e61bbb49-ecb6-43ea-8cd0-9aec9a1e2c14", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}], "ns16:intermediateCatchEvent": {"name": "Error Event", "id": "60f2e4ff-ef7d-4399-8eb9-b942db9a6434", "ns16:extensionElements": {"ns13:nodeVisualInfo": {"x": "416", "y": "299", "width": "24", "height": "24"}}, "ns16:outgoing": "342a6e7e-5a40-434c-80b6-2c1d2fdcd22a", "ns16:errorEventDefinition": {"id": "ddea1277-5f7c-488b-8afc-af10e255696f", "eventImplId": "52b58b74-7ca7-4998-8c00-58e44320bac7", "ns16:extensionElements": {"ns4:errorEventSettings": {"ns4:catchAll": "true"}}}}}}}, "link": [{"name": "To Map Output", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.d1a76c83-69b1-4d3e-859e-00bb1274bf3c", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "2025.f7d9f9a0-96cb-48df-8601-2a428bd9d0ad"], "endStateId": "guid:8a32e7e0f533ea09:-114388d5:18941ad69c0:-5e20", "toProcessItemId": ["2025.15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "2025.15b199e9-0afb-49d4-b9e3-d8c59ee0b673"], "guid": "724d2709-768c-4d7e-a51e-57d5569fce4d", "versionId": "317fc4b9-35eb-4dbd-9ce9-aca21553791f", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "NO", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.81407a06-de54-414f-b760-f65d6a5277e2", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.91e479a9-da3f-4608-a607-87e2586de9bd", "2025.91e479a9-da3f-4608-a607-87e2586de9bd"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.8a59f99e-37b8-4ae1-a389-9775b8ff71df", "2025.8a59f99e-37b8-4ae1-a389-9775b8ff71df"], "guid": "ac1d1d95-09c0-41a6-bfbe-74452f90d71a", "versionId": "68a002f9-b920-4ba0-8f48-41c09f38d437", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To <PERSON><PERSON> De<PERSON> Amount", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a2859f0c-f8bd-45e5-bd56-8d7887016b2a", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.91e479a9-da3f-4608-a607-87e2586de9bd", "2025.91e479a9-da3f-4608-a607-87e2586de9bd"], "endStateId": "guid:818bf8fd736d6b59:764a22fc:18ffdba29b8:b3f", "toProcessItemId": ["2025.fd09a318-5101-424d-883c-4db3a15eb879", "2025.fd09a318-5101-424d-883c-4db3a15eb879"], "guid": "c7cd69ef-2e4d-479d-bf91-81577c95aca0", "versionId": "8d5e4ab3-ebd3-4904-bb59-c730f458b119", "layoutData": {"controlPoints": {"controlPoint": {"x": "740", "y": "14"}}, "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "topLeft", "portType": "2"}}, {"name": "To Charges MW", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.bea97401-8a95-441f-9f38-fc74fa6cfbf5", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.8a59f99e-37b8-4ae1-a389-9775b8ff71df", "2025.8a59f99e-37b8-4ae1-a389-9775b8ff71df"], "endStateId": "Out", "toProcessItemId": ["2025.f7d9f9a0-96cb-48df-8601-2a428bd9d0ad", "2025.f7d9f9a0-96cb-48df-8601-2a428bd9d0ad"], "guid": "92c17e0f-c4b2-439b-a528-3ddd707b664c", "versionId": "98355bc8-2d6f-45eb-a426-e1a60bd9faa0", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "yes", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.06860578-0514-48d9-8898-6a16b7633175", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a4820def-4867-4c9e-8598-90726f0c65e5", "2025.a4820def-4867-4c9e-8598-90726f0c65e5"], "endStateId": "DEFAULT", "toProcessItemId": ["2025.fd09a318-5101-424d-883c-4db3a15eb879", "2025.fd09a318-5101-424d-883c-4db3a15eb879"], "guid": "052a6541-60de-44fb-a1a7-ded34347105e", "versionId": "9e210a55-ac91-43c8-8266-ffa917e8c9ba", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To hasFixedRate?", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.a058d91d-b10f-4cd3-bf38-4f27c1e7402f", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.15b199e9-0afb-49d4-b9e3-d8c59ee0b673", "2025.15b199e9-0afb-49d4-b9e3-d8c59ee0b673"], "endStateId": "Out", "toProcessItemId": ["2025.a4820def-4867-4c9e-8598-90726f0c65e5", "2025.a4820def-4867-4c9e-8598-90726f0c65e5"], "guid": "e35ffba8-2ee9-4ad1-8760-e23c3eab3990", "versionId": "c7f5724c-e219-48d1-a459-84eec6a81092", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.1c1cd577-d00d-45c6-8df5-38e3aec21d86", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.a4820def-4867-4c9e-8598-90726f0c65e5", "2025.a4820def-4867-4c9e-8598-90726f0c65e5"], "endStateId": "guid:818bf8fd736d6b59:764a22fc:18ffdba29b8:b40", "toProcessItemId": ["2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe"], "guid": "2b3ee0ec-2767-4409-89bd-c33130ec8c86", "versionId": "d61b896b-54e0-4d9d-ab52-c3dbe1f5549d", "layoutData": {"controlPoints": {"controlPoint": {"x": "935", "y": "22"}}, "showEndState": "false", "showName": "true"}, "fromItemPort": {"locationId": "topCenter", "portType": "1"}, "toItemPort": {"locationId": "topCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.23dc5a98-be81-4e6f-8e45-52666fca2afe", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.fd09a318-5101-424d-883c-4db3a15eb879", "2025.fd09a318-5101-424d-883c-4db3a15eb879"], "endStateId": "guid:7cf3b5b49b9bcdb8:3c4f5f27:18bd738e4b3:3674", "toProcessItemId": ["2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe"], "guid": "dd75f6a3-d1eb-405a-84eb-346b826f1ee5", "versionId": "e4df9008-dfb9-445a-9ca8-284ac146c68c", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "leftCenter", "portType": "2"}}, {"name": "To End", "lastModified": {"isNull": "true"}, "lastModifiedBy": {"isNull": "true"}, "processLinkId": "2027.2bbd3977-29c3-4a57-8acb-7471aa53ad72", "processId": "1.47164ac0-82bf-4670-b0f1-51d0e36f4463", "description": {"isNull": "true"}, "fromProcessItemId": ["2025.d143bea6-4b15-4826-8320-1bb2356a347a", "2025.d143bea6-4b15-4826-8320-1bb2356a347a"], "endStateId": "guid:f6ffc3b64be5dfa5:4157166b:18ab80e0606:-6f83", "toProcessItemId": ["2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe", "2025.fa6c9000-3339-46dd-bb24-e0d2d0038bfe"], "guid": "d388ff01-a341-4cf5-aed6-6d7904d53cb4", "versionId": "eeb48d3f-09d9-4c23-8f32-568c12cd43f5", "layoutData": {"controlPoints": "", "showEndState": "false", "showName": "false"}, "fromItemPort": {"locationId": "rightCenter", "portType": "1"}, "toItemPort": {"locationId": "bottomCenter", "portType": "2"}}]}}}, "subType": "12", "hasDetails": false}