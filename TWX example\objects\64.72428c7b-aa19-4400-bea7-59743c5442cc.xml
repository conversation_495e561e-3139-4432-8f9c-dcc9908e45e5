<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <coachView id="64.72428c7b-aa19-4400-bea7-59743c5442cc" name="Validation Helper">
        <lastModified>1746716132931</lastModified>
        <lastModifiedBy>mohamed.reda</lastModifiedBy>
        <coachViewId>64.72428c7b-aa19-4400-bea7-59743c5442cc</coachViewId>
        <isTemplate>false</isTemplate>
        <layout>&lt;?xml version="1.0" encoding="UTF-8" standalone="yes"?&gt;&lt;ns2:layout xmlns:ns2="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns3="http://www.ibm.com/bpm/coachview"&gt;&lt;ns2:layoutItem xsi:type="ns2:ViewRef" version="8550" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;&lt;ns2:id&gt;273bba6a-96a1-4e9c-81f3-e2401d025234&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;ErrorSection_Panel1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;4ebd8d84-4d17-4e3d-8512-778d93a7de37&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Validation Section&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;77591e43-5001-4b69-8a6c-329a3e1b3808&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;3a7606dc-d49a-4da1-83d1-ff1a2ac3753b&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;SHOW&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;40188bf6-1d4b-4c2e-801b-41fa549c57d3&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;P&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;329ee8c2-3a08-4ee7-864e-81ddc65cae34&lt;/ns2:id&gt;&lt;ns2:optionName&gt;eventON_LOAD&lt;/ns2:optionName&gt;&lt;ns2:value&gt;me.setVisible(false,true)&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.aa70e5b9-aade-4334-b92d-1fb5e61f4b0a&lt;/ns2:viewUUID&gt;&lt;ns2:contentBoxContrib&gt;&lt;ns2:id&gt;82455ba0-5391-4c46-811f-e6014be96d92&lt;/ns2:id&gt;&lt;ns2:contentBoxId&gt;ContentBox1&lt;/ns2:contentBoxId&gt;&lt;ns2:contributions xsi:type="ns2:ViewRef" version="8550"&gt;&lt;ns2:id&gt;8ee61a1b-a2c5-4c91-893e-81ea74bbb81e&lt;/ns2:id&gt;&lt;ns2:layoutItemId&gt;Display_Text1&lt;/ns2:layoutItemId&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;267d5b17-ea64-4571-8e46-8fe453e62b0f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@label&lt;/ns2:optionName&gt;&lt;ns2:value&gt;Display text&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;2219e09f-a16e-4fa1-851e-de4c9cec9b31&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@helpText&lt;/ns2:optionName&gt;&lt;ns2:value&gt;&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;32db1a16-ad94-4207-8ec7-2d23bca9863f&lt;/ns2:id&gt;&lt;ns2:optionName&gt;@labelVisibility&lt;/ns2:optionName&gt;&lt;ns2:value&gt;HIDE&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;fc208d2a-37ce-4795-81b2-a5b4b2d22729&lt;/ns2:id&gt;&lt;ns2:optionName&gt;allowHTML&lt;/ns2:optionName&gt;&lt;ns2:value&gt;true&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;8ce0143a-b85e-4ed1-83b7-4d2e9aabdd02&lt;/ns2:id&gt;&lt;ns2:optionName&gt;colorStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;H&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;37e1e574-eb74-4aa7-86e3-8cd4a0a767fa&lt;/ns2:id&gt;&lt;ns2:optionName&gt;weightStyle&lt;/ns2:optionName&gt;&lt;ns2:value&gt;M&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:configData&gt;&lt;ns2:id&gt;a1907ac9-3e08-4136-8154-d81d86bee653&lt;/ns2:id&gt;&lt;ns2:optionName&gt;textWrap&lt;/ns2:optionName&gt;&lt;ns2:value&gt;{"isResponsiveData":true,"values":[{"deviceConfigID":"LargeID","value":"K"}]}&lt;/ns2:value&gt;&lt;/ns2:configData&gt;&lt;ns2:viewUUID&gt;64.f634f22e-7800-4bd7-9f1e-87177acfb3bc&lt;/ns2:viewUUID&gt;&lt;/ns2:contributions&gt;&lt;/ns2:contentBoxContrib&gt;&lt;/ns2:layoutItem&gt;&lt;/ns2:layout&gt;</layout>
        <paletteIcon isNull="true" />
        <previewImage isNull="true" />
        <hasLabel>false</hasLabel>
        <labelPosition>0</labelPosition>
        <nineSliceX1Coord>0</nineSliceX1Coord>
        <nineSliceX2Coord>0</nineSliceX2Coord>
        <nineSliceY1Coord>0</nineSliceY1Coord>
        <nineSliceY2Coord>0</nineSliceY2Coord>
        <emitBoundary>false</emitBoundary>
        <isPrototypeFunc>false</isPrototypeFunc>
        <enableDevMode>false</enableDevMode>
        <isMobileReady>true</isMobileReady>
        <loadJsFunction></loadJsFunction>
        <unloadJsFunction isNull="true" />
        <viewJsFunction></viewJsFunction>
        <changeJsFunction></changeJsFunction>
        <collaborationJsFunction isNull="true" />
        <description></description>
        <validateJsFunction></validateJsFunction>
        <previewAdvHtml isNull="true" />
        <previewAdvJs isNull="true" />
        <useUrlBinding>false</useUrlBinding>
        <guid>a93b3f4f-bdba-460e-81a4-1a71c20bda41</guid>
        <versionId>c79eb96d-5560-467c-84c7-7135acc836e4</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <configOption name="runTimeValid">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.03df0943-dcff-4c55-a4f0-7d0ac95b7de0</coachViewConfigOptionId>
            <coachViewId>64.72428c7b-aa19-4400-bea7-59743c5442cc</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>0</seq>
            <description></description>
            <groupName></groupName>
            <guid>b2aeddc0-d0e1-4818-bf19-ba1530d17f66</guid>
            <versionId>67078eff-f4e6-4c7f-8f12-34c48a12d461</versionId>
        </configOption>
        <configOption name="stop">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewConfigOptionId>66.c1066634-513c-4c8c-9f6b-6cbe897b2811</coachViewConfigOptionId>
            <coachViewId>64.72428c7b-aa19-4400-bea7-59743c5442cc</coachViewId>
            <isList>false</isList>
            <propertyType>OBJECT</propertyType>
            <label></label>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <processId isNull="true" />
            <actionflowId isNull="true" />
            <isAdaptive>false</isAdaptive>
            <seq>1</seq>
            <description></description>
            <groupName></groupName>
            <guid>6d62f5e6-0079-4091-9de0-9e08e83065a8</guid>
            <versionId>18dcf0f7-4a21-46d2-bdd4-d19fc5ead91a</versionId>
        </configOption>
        <inlineScript name="Inline Javascript">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.a9855160-afa9-42ae-9bd5-6db5ab5003c4</coachViewInlineScriptId>
            <coachViewId>64.72428c7b-aa19-4400-bea7-59743c5442cc</coachViewId>
            <scriptType>JS</scriptType>
            <scriptBlock>_this = this;&#xD;
&#xD;
var displayText = "Display_Text1";&#xD;
const generalTab = "GENERAL";&#xD;
var tabSectionId = "";&#xD;
var tabSection = "";&#xD;
const errorSectionId = "ErrorSection_Panel1";&#xD;
const stopVU = this.context.options.stop.get("value") || false;&#xD;
const runTime = this.context.options.runTimeValid.get("value") || false;&#xD;
const initExpanded = this.context.options.initExpanded?.get("value") || true;&#xD;
&#xD;
_this.startVU = function () {&#xD;
	tabSection = document.querySelector('[role="tablist"]');&#xD;
	tabSectionId = _this.getTabSectionId(tabSection);&#xD;
	const redCircles = document.querySelectorAll(".red-circle");&#xD;
&#xD;
	var viewErrorList = [];&#xD;
	viewErrorList = bpmext.ui.getInvalidViews();&#xD;
&#xD;
	if (viewErrorList.length == 0) {&#xD;
		this.ui.get(displayText).setText("");&#xD;
		_this.ui.get(errorSectionId).setVisible(false, true);&#xD;
		_this.resetRedCircle(redCircles);&#xD;
		return;&#xD;
	}&#xD;
&#xD;
	const errMapList = viewErrorList&#xD;
		.map((view) =&gt; _this.constructErrorMap(view))&#xD;
		.filter(function (obj) {&#xD;
			return obj != null;&#xD;
		});&#xD;
&#xD;
	var viewMapList = [];&#xD;
	_this.resetRedCircle(redCircles);&#xD;
	viewMapList = _this.organizeErrorsByTab(errMapList);&#xD;
	console.dir(viewMapList);&#xD;
	// Add counter red circle&#xD;
	_this.resetRedCircle(redCircles);&#xD;
	viewMapList.forEach((viewMap) =&gt; _this.addRedCircleToTab(viewMap));&#xD;
&#xD;
	//Add panel with tabs and messages&#xD;
	_this.ui.get(errorSectionId).setVisible(true, true);&#xD;
	setTimeout(() =&gt; {&#xD;
		_this.constructValidPanel(viewMapList);&#xD;
	}, 200);&#xD;
};&#xD;
&#xD;
_this.getTabInfoFirst = function (tabSection) {&#xD;
	const tabElementList = tabSection?.children;&#xD;
	var tabsInfo = {};&#xD;
	for (var i = 0; i &lt; tabElementList.length; i++) {&#xD;
		var tabElement = tabElementList[i];&#xD;
&#xD;
		var tabInnerText = tabElement.innerText.split("\n")[0].replaceAll(" ", "");&#xD;
		if (!tabInnerText || tabElement.getAttribute("role") !== "tab") continue;&#xD;
&#xD;
		tabsInfo[tabInnerText] = {&#xD;
			tabDomID: tabElement.id,&#xD;
			tabPathId: i,&#xD;
		};&#xD;
	}&#xD;
&#xD;
	return tabsInfo;&#xD;
};&#xD;
&#xD;
_this.resetRedCircle = function (redCircles) {&#xD;
	// const redCircles = document.querySelectorAll(".red-circle");&#xD;
	if (!redCircles) return;&#xD;
	redCircles.forEach((circle) =&gt; circle.remove());&#xD;
};&#xD;
&#xD;
_this.getTabSectionId = function (tabSection) {&#xD;
	if (!tabSection) return;&#xD;
	var currentElement = tabSection;&#xD;
&#xD;
	while (currentElement &amp;&amp; currentElement !== document.body) {&#xD;
		if (currentElement.classList.contains("Tab_Section")) {&#xD;
			tabSectionId = currentElement.getAttribute("control-name");&#xD;
			break;&#xD;
		}&#xD;
&#xD;
		currentElement = currentElement.parentElement;&#xD;
	}&#xD;
&#xD;
	return tabSectionId;&#xD;
};&#xD;
&#xD;
_this.constructValidPanel = function (viewMapList) {&#xD;
	if (!viewMapList || viewMapList.length == 0) return;&#xD;
	var tabNameListHTML = ``;&#xD;
&#xD;
	for (var i = 0; i &lt; viewMapList.length; i++) {&#xD;
		var tabData = viewMapList[i].tab;&#xD;
		var messageList = viewMapList[i].messages;&#xD;
&#xD;
		if (!tabData) continue;&#xD;
&#xD;
		var tabDomId = tabData.domId;&#xD;
		var tabName = tabData.name || generalTab;&#xD;
		var errorListId = `error-list-${tabDomId}`;&#xD;
		var tabIndex = tabData.pathId;&#xD;
		var errorListHTML = _this.generateErrorListHTML(messageList, tabName, tabIndex);&#xD;
&#xD;
		tabNameListHTML += _this.generateTabItemHTML(tabName, tabDomId, errorListId, tabIndex, errorListHTML);&#xD;
	}&#xD;
&#xD;
	tabNameListHTML = `&lt;ul class="tab-list"&gt;${tabNameListHTML}&lt;/ul&gt;`;&#xD;
	_this.ui.get(displayText).setText(tabNameListHTML);&#xD;
};&#xD;
&#xD;
_this.activateTab = function (tabDomId, tabName, tabIndex) {&#xD;
	if (!tabName || !tabIndex) return;&#xD;
&#xD;
	if (tabName !== generalTab) {&#xD;
		page.ui.get(tabSectionId).setCurrentPane(tabIndex);&#xD;
		if (tabDomId) {&#xD;
			var tabElement = document.getElementById(tabDomId);&#xD;
			_this.highLighElement(tabElement);&#xD;
		}&#xD;
	}&#xD;
};&#xD;
&#xD;
_this.activateField = function (tabName, panelString, tabIndex, fieldPathId) {&#xD;
	const panelList = panelString.split("@@").filter((e) =&gt; e !== "");&#xD;
&#xD;
	if (tabIndex &amp;&amp; tabSectionId &amp;&amp; tabName !== generalTab) {&#xD;
		page.ui.get(tabSectionId).setCurrentPane(tabIndex);&#xD;
	}&#xD;
	if (panelList &amp;&amp; panelList.length &gt; 0) {&#xD;
		for (let i = 0; i &lt; panelList.length; i++) {&#xD;
			page.ui.get(panelList[i]).expand();&#xD;
		}&#xD;
	}&#xD;
	if (!fieldPathId) return;&#xD;
&#xD;
	setTimeout(() =&gt; {&#xD;
		_this.focusOnElement(fieldPathId);&#xD;
	}, 300);&#xD;
};&#xD;
&#xD;
_this.focusOnElement = function (fieldPathId) {&#xD;
	var fieldElement = page.ui.get(fieldPathId).context.element;&#xD;
	_this.highLighElement(fieldElement);&#xD;
&#xD;
	page.ui.get(fieldPathId).focus();&#xD;
};&#xD;
&#xD;
_this.highLighElement = function (fieldElement) {&#xD;
	if (!fieldElement) return;&#xD;
&#xD;
	fieldElement.classList.add("highlighted-field");&#xD;
	setTimeout(function () {&#xD;
		fieldElement.classList.remove("highlighted-field");&#xD;
	}, 1500);&#xD;
};&#xD;
&#xD;
_this.addRedCircleToTab = function (viewMap) {&#xD;
	if (!viewMap.tab.domId) return;&#xD;
&#xD;
	const messagesCount = viewMap.messages.length;&#xD;
	const tabDomId = viewMap.tab.domId;&#xD;
	const tabElement = document.getElementById(tabDomId);&#xD;
	if (!tabElement) return;&#xD;
&#xD;
	// Combine DOM reads&#xD;
	const existingCircle = tabElement.querySelector(".red-circle");&#xD;
	const newCircleContent = `&lt;div class="red-circle"&gt;${messagesCount}&lt;/div&gt;`;&#xD;
&#xD;
	// Combine DOM writes&#xD;
	if (!existingCircle) {&#xD;
		tabElement.insertAdjacentHTML("beforeend", newCircleContent);&#xD;
	} else {&#xD;
		existingCircle.innerText = messagesCount;&#xD;
	}&#xD;
};&#xD;
&#xD;
_this.constructErrorMap = function (fieldElement) {&#xD;
	if (!fieldElement || !fieldElement.context.element || !fieldElement._bpmextViewNode) return null;&#xD;
&#xD;
	var fieldDomId = fieldElement.context.element.id;&#xD;
	var fieldParents = _this.getFieldParents(fieldDomId);&#xD;
      // var isField = Object.keys(fieldElement._bpmextViewNode._data.context.subview).length === 0;&#xD;
      var isField = true;&#xD;
	if (isField) {&#xD;
		errorMap = {&#xD;
			field: {&#xD;
				message: fieldElement._bpmextVE?.errors?.[0]?.message || "",&#xD;
				domId: fieldDomId,&#xD;
				pathId: fieldElement.context.element.getAttribute("control-name"),&#xD;
				viewId: fieldElement.context.element.getAttribute("data-viewid"),&#xD;
				label: fieldElement.getLabel(),&#xD;
			},&#xD;
&#xD;
			panels: fieldParents.cPanelList /*[list of "SPARKCPanel"]*/,&#xD;
&#xD;
			view: fieldParents.viewObj,&#xD;
		};&#xD;
		return errorMap;&#xD;
	}&#xD;
};&#xD;
&#xD;
_this.getFieldParents = function (elementId) {&#xD;
	var fieldParents = {&#xD;
		viewObj: {&#xD;
			name: "",&#xD;
			domId: "",&#xD;
			pathId: "",&#xD;
		},&#xD;
		cPanelList: [],&#xD;
	};&#xD;
	const cPanelClass = "Collapsible_Panel";&#xD;
	const tabClass = "tab-pane";&#xD;
&#xD;
	var currentElement = document.getElementById(elementId);&#xD;
&#xD;
	while (currentElement &amp;&amp; currentElement !== document.body) {&#xD;
		if (currentElement.classList.contains(tabClass)) {&#xD;
			fieldParents.viewObj.name = currentElement.getAttribute("aria-label");&#xD;
			fieldParents.viewObj.domId = currentElement.id;&#xD;
			fieldParents.viewObj.pathId = currentElement.getAttribute("control-name");&#xD;
		} else if (currentElement.classList.contains(cPanelClass)) {&#xD;
			fieldParents.cPanelList.unshift(currentElement.getAttribute("control-name"));&#xD;
		}&#xD;
&#xD;
		currentElement = currentElement.parentNode;&#xD;
	}&#xD;
	console.dir(fieldParents);&#xD;
	return fieldParents;&#xD;
};&#xD;
&#xD;
_this.organizeErrorsByTab = function (errorList) {&#xD;
	const viewMap = new Map();&#xD;
	let tabsInfo = {};&#xD;
&#xD;
	if (tabSection) {&#xD;
		tabsInfo = _this.getTabInfoFirst(tabSection);&#xD;
	}&#xD;
&#xD;
	errorList.forEach((error) =&gt; {&#xD;
		if (error) {&#xD;
			const viewName = error.view.name;&#xD;
			const sanitizedViewName = viewName?.replaceAll(" ", "");&#xD;
&#xD;
			if (!viewMap.has(viewName)) {&#xD;
				viewMap.set(viewName, {&#xD;
					view: {&#xD;
						name: viewName,&#xD;
						domId: error.view.domId,&#xD;
						pathId: error.view.pathId,&#xD;
					},&#xD;
					messages: [],&#xD;
					tab: {&#xD;
						name: viewName,&#xD;
						domId: tabsInfo[sanitizedViewName]?.tabDomID,&#xD;
						pathId: tabsInfo[sanitizedViewName]?.tabPathId,&#xD;
					},&#xD;
				});&#xD;
			}&#xD;
			// Add the error message to the corresponding tab entry&#xD;
			const viewEntry = viewMap.get(viewName);&#xD;
			viewEntry.messages.push({&#xD;
				message: error.field.message,&#xD;
				field: {&#xD;
					domId: error.field.domId,&#xD;
					pathId: error.field.pathId,&#xD;
					viewId: error.field.viewId,&#xD;
					label: error.field.label,&#xD;
					panels: [...error.panels],&#xD;
				},&#xD;
			});&#xD;
		}&#xD;
	});&#xD;
	// Convert the map values to an array of tab objects&#xD;
	return [...viewMap.values()];&#xD;
};&#xD;
&#xD;
_this.generateTabItemHTML = function (tabName, tabDomId, errorListId, tabIndex, errorListHTML) {&#xD;
	const initialButtonText = initExpanded ? "Hide" : "Show";&#xD;
	const initialDisplayStyle = initExpanded ? "block" : "none";&#xD;
	return `&lt;li class="tab-item"&gt;&#xD;
            &lt;div class="tab-container"&gt;&#xD;
                &lt;div class="tab-header"&gt;&#xD;
                    &lt;div class="gradient-box"&gt;&#xD;
                        &lt;a href="#${tabDomId}" class="tab-name"&#xD;
                            onclick="_this.toggleErrorList('${errorListId}'); event.preventDefault(); event.stopPropagation();"&gt;${tabName}&lt;/a&gt;&#xD;
                    &lt;/div&gt;&#xD;
                &lt;/div&gt;&#xD;
                &lt;ul id="${errorListId}" class="error-list" style="display: ${initialDisplayStyle};"&gt;${errorListHTML}&lt;/ul&gt;&#xD;
            &lt;/div&gt;&#xD;
        &lt;/li&gt;`;&#xD;
};&#xD;
&#xD;
_this.generateErrorListHTML = function (listOfErrors, tabName, tabIndex) {&#xD;
	return listOfErrors&#xD;
		.map(function (error) {&#xD;
			const fieldDomId = error.field.domId;&#xD;
			const fieldPathId = error.field.pathId;&#xD;
			const label = error.field.label;&#xD;
			const targetMessage = `&lt;b&gt;${label}&lt;/b&gt; : ${error.message}`;&#xD;
			const panelString = error.field.panels.join("@@");&#xD;
&#xD;
			return `&lt;li&gt;&lt;span class="bullet"&gt;&amp;#8226;&lt;/span&gt; &lt;a href="#${fieldDomId}" class="message-link" message-id="${fieldDomId}" onclick="_this.activateField('${tabName}','${panelString}','${tabIndex}','${fieldPathId}');"&gt;${targetMessage}&lt;/a&gt;&lt;/li&gt;`;&#xD;
		})&#xD;
		.join("");&#xD;
};&#xD;
&#xD;
_this.toggleErrorList = function (errorListId) {&#xD;
	const errorList = document.getElementById(errorListId);&#xD;
	if (errorList) {&#xD;
		const isCollapsed = errorList.style.display === "none" || !errorList.style.display;&#xD;
		errorList.style.display = isCollapsed ? "block" : "none";&#xD;
		//   if (button) {&#xD;
		//       button.textContent = isCollapsed ? "Hide" : "Show";&#xD;
		//   }&#xD;
	}&#xD;
};&#xD;
//=======================================REQUIRED===============================================//&#xD;
require(["com.ibm.bpm.coach/engine"], function (engine) {&#xD;
	var dve = engine._deliverValidationEvents;&#xD;
	engine._deliverValidationEvents = function (event, viewMap, isClear) {&#xD;
		dve(event, viewMap, isClear); // original processing first&#xD;
		// console.log("_deliverValidationEvents", event, viewMap, isClear);&#xD;
	}.bind(engine);&#xD;
	var hve = engine.handleValidationEvent;&#xD;
	engine.handleValidationEvent = function (event) {&#xD;
		hve(event);&#xD;
		// console.log("handleValidationEvent", event);&#xD;
		if (!stopVU) {&#xD;
			_this.startVU();&#xD;
		}&#xD;
	}.bind(engine);&#xD;
});&#xD;
&#xD;
var uvvs = bpmext &amp;&amp; bpmext.ui &amp;&amp; bpmext.ui.updateViewValidationState;&#xD;
if (uvvs) {&#xD;
	bpmext.ui.updateViewValidationState = function (view, event) {&#xD;
		uvvs(view, event); //call original handler&#xD;
		// console.log("updateViewValidationState", view, event);&#xD;
		if (!stopVU &amp;&amp; runTime == true) {&#xD;
			_this.startVU();&#xD;
		}&#xD;
	};&#xD;
}&#xD;
</scriptBlock>
            <seq>0</seq>
            <description></description>
            <guid>cdcb883c-06fd-4f25-971f-1812cdd2c77b</guid>
            <versionId>a5f50328-e148-4a0b-a4d5-62d1b458c8ea</versionId>
        </inlineScript>
        <inlineScript name="Inline CSS">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.e8032eb5-16ff-4348-8fc4-db9daabad9f5</coachViewInlineScriptId>
            <coachViewId>64.72428c7b-aa19-4400-bea7-59743c5442cc</coachViewId>
            <scriptType>CSS</scriptType>
            <scriptBlock>/* Style for the red circle counter */&#xD;
.red-circle {&#xD;
	position: absolute;&#xD;
	top: 0;&#xD;
	right: 0;&#xD;
	width: 17px;&#xD;
	height: 17px;&#xD;
	background-color: red;&#xD;
	border-radius: 50%;&#xD;
	display: flex;&#xD;
	justify-content: center;&#xD;
	align-items: center;&#xD;
	color: white;&#xD;
	font-weight: bold;&#xD;
}&#xD;
&#xD;
.tab-link {&#xD;
	font-size: medium;&#xD;
}&#xD;
&#xD;
/* Style for the tab list */&#xD;
.tab-list {&#xD;
	list-style-type: none;&#xD;
	padding: 0;&#xD;
	margin: 0; /* Remove default margin */&#xD;
}&#xD;
&#xD;
/* Style for each tab item */&#xD;
.tab-item {&#xD;
	margin-bottom: 5px; /* Reduce space between tabs */&#xD;
	border: none;&#xD;
	padding: 5px;&#xD;
	display: flex;&#xD;
	align-items: center;&#xD;
	list-style: none;&#xD;
}&#xD;
&#xD;
/* Style for the tab name */&#xD;
.tab-name {&#xD;
	font-size: 16px;&#xD;
	margin: 0;&#xD;
	color: #8a1412; /* Replaced red with a shade of orange */&#xD;
	text-decoration: none;&#xD;
	font-weight: bold;&#xD;
}&#xD;
&#xD;
.tab-name:hover {&#xD;
	text-decoration: underline;&#xD;
	color: #d35400;&#xD;
}&#xD;
&#xD;
.tab-name:focus {&#xD;
	text-decoration: underline;&#xD;
	color: #d35400;&#xD;
}&#xD;
&#xD;
.tab-container {&#xD;
	display: flex;&#xD;
	flex-direction: column;&#xD;
	width: 100%;&#xD;
}&#xD;
&#xD;
.tab-header {&#xD;
	display: flex;&#xD;
	align-items: center;&#xD;
	width: 100%;&#xD;
}&#xD;
&#xD;
/* Style for the gradient box */&#xD;
.gradient-box {&#xD;
	flex: 1;&#xD;
	padding: 10px 20px;&#xD;
	background: linear-gradient(45deg, rgba(255, 255, 255, 0.8), rgba(240, 240, 240, 0.8));&#xD;
	border-radius: 4px;&#xD;
	display: flex;&#xD;
	align-items: center;&#xD;
	cursor: auto; /* Change cursor to auto to indicate non-clickable */&#xD;
	transition: none;&#xD;
}&#xD;
&#xD;
/* .gradient-box:hover {&#xD;
	transform: scale(1.05);&#xD;
	background: linear-gradient(45deg, rgba(255, 255, 255, 1), rgba(240, 240, 240, 1));&#xD;
} */&#xD;
&#xD;
.tab {&#xD;
	position: relative;&#xD;
	width: 100%;&#xD;
}&#xD;
&#xD;
.tab::after {&#xD;
	content: attr(error-count);&#xD;
	color: red;&#xD;
	font-size: 10px;&#xD;
	position: absolute;&#xD;
	right: 5px;&#xD;
	top: 5px;&#xD;
}&#xD;
&#xD;
/* Add animation for the highlighted field */&#xD;
.highlighted-field {&#xD;
	animation-name: highlight;&#xD;
	animation-duration: 1.5s;&#xD;
}&#xD;
&#xD;
@keyframes highlight {&#xD;
	from {&#xD;
		background-color: yellow;&#xD;
	}&#xD;
	to {&#xD;
		background-color: initial;&#xD;
	}&#xD;
}&#xD;
&#xD;
.error-list {&#xD;
	display: none;&#xD;
	margin-left: 20px;&#xD;
	padding: 0;&#xD;
	list-style-type: none;&#xD;
	transition: max-height 0.2s ease-out;&#xD;
	overflow: hidden;&#xD;
}&#xD;
&#xD;
.error-list.collapsed {&#xD;
	max-height: 0;&#xD;
	transition: max-height 0.3s ease-out;&#xD;
}&#xD;
&#xD;
.error-list.expanded {&#xD;
	max-height: 500px; /* Adjust this value as needed */&#xD;
	transition: max-height 0.3s ease-in;&#xD;
}&#xD;
&#xD;
/* Style for the toggle button */&#xD;
.toggle-button {&#xD;
	margin-left: 10px;&#xD;
	background-color: #f7f7f7; /* Off-white background */&#xD;
	color: #8a1412; /* Darker red for text */&#xD;
	border: 1px solid #e0e0e0; /* Off-white border */&#xD;
	border-radius: 4px;&#xD;
	padding: 5px 10px;&#xD;
	cursor: pointer;&#xD;
	transition: background-color 0.3s ease, transform 0.3s ease;&#xD;
}&#xD;
&#xD;
.toggle-button:hover {&#xD;
	background-color: #e8e8e8; /* Slightly darker off-white on hover */&#xD;
	transform: scale(1.05);&#xD;
}&#xD;
&#xD;
.bullet {&#xD;
	color: #ff8c00; /* Replaced red with a shade of orange */&#xD;
	margin-right: 5px; /* Adjust spacing between bullet and link */&#xD;
}&#xD;
&#xD;
.message-link {&#xD;
	cursor: pointer;&#xD;
	color: #8a1412; /* Replaced red with a shade of orange */&#xD;
	text-decoration: none;&#xD;
	padding: 5px; /* Add padding for better hover area */&#xD;
	transition: color 0.2s ease, background-color 0.2s ease; /* Add transition for smooth animation */&#xD;
	position: relative;&#xD;
}&#xD;
&#xD;
.message-link:hover {&#xD;
	text-decoration: underline;&#xD;
	color: #d35400; /* Darker shade of orange on hover */&#xD;
	/* background-color: rgba(255, 140, 0, 0.1); */&#xD;
}&#xD;
</scriptBlock>
            <seq>1</seq>
            <description></description>
            <guid>a143696b-3c2e-43fd-95cc-6eae15c0a0ba</guid>
            <versionId>8b0f0d88-417d-4803-8a5b-3dbef159c15b</versionId>
        </inlineScript>
        <inlineScript name="Header HTML">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <coachViewInlineScriptId>68.924c635b-2702-4878-85ad-49cc254a3c60</coachViewInlineScriptId>
            <coachViewId>64.72428c7b-aa19-4400-bea7-59743c5442cc</coachViewId>
            <scriptType>HTML</scriptType>
            <scriptBlock></scriptBlock>
            <seq>2</seq>
            <description></description>
            <guid>1b6f4afa-8a79-4c6d-8d1d-04ca876b95bb</guid>
            <versionId>bf242b87-a903-466a-975b-0a2c7c35ee68</versionId>
        </inlineScript>
    </coachView>
</teamworks>

