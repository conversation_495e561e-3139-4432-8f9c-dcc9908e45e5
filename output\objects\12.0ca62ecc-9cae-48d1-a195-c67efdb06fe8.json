{"id": "12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8", "versionId": "f0053244-cb35-4537-a419-e0e17f8ad5f9", "name": "ResponseTemplatePojo", "type": "twClass", "typeName": "Business Object", "details": {}, "_fullObjectData": {"teamworks": {"twClass": {"id": "12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8", "name": "ResponseTemplatePojo", "lastModified": "1693480732491", "lastModifiedBy": "abdelrahman.saleh", "classId": "12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8", "type": "1", "isSystem": "false", "shared": "false", "isShadow": "true", "globalLifetime": "false", "internalName": {"isNull": "true"}, "extensionType": {"isNull": "true"}, "saveServiceRef": {"isNull": "true"}, "bpmn2Data": {"isNull": "true"}, "externalId": "itm.12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8", "dependencySummary": "<dependencySummary id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1736\">\r\n  <artifactReference id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1735\">\r\n    <refId>/6023.dcd8f66e-349d-46dd-9cd2-3f70834cddd4</refId>\r\n    <refType>1</refType>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1734\">\r\n      <name>externalId</name>\r\n      <value>http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd</value>\r\n    </nameValuePair>\r\n    <nameValuePair id=\"bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1733\">\r\n      <name>mimeType</name>\r\n      <value>xsd</value>\r\n    </nameValuePair>\r\n  </artifactReference>\r\n</dependencySummary>", "jsonData": "{\"attributeFormDefault\":\"unqualified\",\"elementFormDefault\":\"unqualified\",\"targetNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"complexType\":[{\"annotation\":{\"documentation\":[{\"content\":[\"ResponseTemplatePojo\"]}],\"appinfo\":[{\"shared\":[false],\"advancedProperties\":[{\"namespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"typeName\":\"ResponseTemplatePojo\"}],\"shadow\":[true]}]},\"sequence\":{\"element\":[{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"docBase64\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"string\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":1}]}]},\"name\":\"docBase64\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"ecmDocName\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"string\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":2}]}]},\"name\":\"ecmDocName\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"error\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.ibm.com\\/xmlns\\/prod\\/bpm\\/bpmn\\/ext\\/externalservice\\/swagger\\/xsd\\/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd\",\"_minOccurs\":0,\"typeName\":\"ErrorPojo\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":3}]}]},\"name\":\"error\",\"type\":\"{http:\\/\\/NBEODCR}BrokenReference\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.9ad7c20d-998e-4629-84b9-f365c85a6733\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"status\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"boolean\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":4}]}]},\"name\":\"status\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}Boolean\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.83ff975e-8dbc-42e5-b738-fa8bc08274a2\"}},{\"annotation\":{\"documentation\":[{}],\"appinfo\":[{\"propertyName\":[\"templateCode\"],\"propertyRequired\":[false],\"propertyHidden\":[false],\"advancedParameterProperties\":[{\"typeNamespace\":\"http:\\/\\/www.w3.org\\/2001\\/XMLSchema\",\"_minOccurs\":0,\"typeName\":\"string\",\"_nillable\":false,\"nodeType\":1,\"_maxOccurs\":1,\"order\":5}]}]},\"name\":\"templateCode\",\"type\":\"{http:\\/\\/lombardi.ibm.com\\/schema\\/}String\",\"otherAttributes\":{\"{http:\\/\\/www.ibm.com\\/bpmsdk}refid\":\"12.db884a3c-c533-44b7-bb2d-47bec8ad4022\"}}]},\"name\":\"ResponseTemplatePojo\"}],\"id\":\"_12.0ca62ecc-9cae-48d1-a195-c67efdb06fe8\"}", "description": "ResponseTemplatePojo", "guid": "guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:-1765", "versionId": "f0053244-cb35-4537-a419-e0e17f8ad5f9", "definition": {"property": [{"name": "docBase64", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "string", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "1", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "ecmDocName", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "string", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "2", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "error", "description": {"isNull": "true"}, "classRef": "/12.9ad7c20d-998e-4629-84b9-f365c85a6733", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "<PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>", "typeNamespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "3", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "status", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "boolean", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "4", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}, {"name": "templateCode", "description": {"isNull": "true"}, "classRef": "b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022", "arrayProperty": "false", "propertyDefault": {"isNull": "true"}, "propertyRequired": "false", "propertyHidden": "false", "annotation": {"type": "com.lombardisoftware.core.xml.XMLFieldAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "nodeType": "1", "name": {"isNull": "true"}, "namespace": {"isNull": "true"}, "typeName": "string", "typeNamespace": "http://www.w3.org/2001/XMLSchema", "minOccurs": "0", "maxOccurs": "1", "nillable": "false", "order": "5", "wrapArray": {"isNull": "true"}, "arrayTypeName": {"isNull": "true"}, "arrayTypeAnonymous": {"isNull": "true"}, "arrayItemName": {"isNull": "true"}, "arrayItemWildcard": {"isNull": "true"}, "wildcard": {"isNull": "true"}, "wildcardVariety": {"isNull": "true"}, "wildcardMode": {"isNull": "true"}, "wildcardNamespace": {"isNull": "true"}, "parentModelGroupCompositor": {"isNull": "true"}, "timeZone": {"isNull": "true"}}}], "validator": {"className": {"isNull": "true"}, "errorMessage": {"isNull": "true"}, "webWidgetJavaClass": {"isNull": "true"}, "externalType": {"isNull": "true"}, "configData": {"schema": {"simpleType": {"name": "ResponseTemplatePojo", "restriction": {"base": "String"}}}}}, "annotation": {"type": "com.lombardisoftware.core.xml.XMLTypeAnnotation", "version": "2.0", "exclude": {"isNull": "true"}, "anonymous": {"isNull": "true"}, "local": {"isNull": "true"}, "name": "ResponseTemplatePojo", "namespace": "http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/externalservice/swagger/xsd/240e7a4d-19e1-4926-9cf2-a3e2de0ef705.xsd", "elementName": {"isNull": "true"}, "elementNamespace": {"isNull": "true"}, "protoTypeName": {"isNull": "true"}, "baseTypeName": {"isNull": "true"}, "specialType": {"isNull": "true"}, "contentTypeVariety": {"isNull": "true"}, "xscRef": {"isNull": "true"}}}}}}}