<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <process id="1.a1d1f1de-87c1-424f-8115-086a8221db8b" name="Audit Reversal Data">
        <lastModified>1696365798535</lastModified>
        <lastModifiedBy>abdel<PERSON>man.saleh</lastModifiedBy>
        <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
        <image isNull="true" />
        <tabGroup isNull="true" />
        <startingProcessItemId>2025.f0cf680e-0585-42aa-862f-ea465fd74d18</startingProcessItemId>
        <isRootProcess>false</isRootProcess>
        <processType>12</processType>
        <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
        <errorHandlerItemId isNull="true" />
        <isLoggingVariables>false</isLoggingVariables>
        <isTransactional>false</isTransactional>
        <processTimingLevel isNull="true" />
        <participantRef isNull="true" />
        <exposedType>0</exposedType>
        <isTrackingEnabled>true</isTrackingEnabled>
        <xmlData isNull="true" />
        <cachingType>false</cachingType>
        <itemLabel isNull="true" />
        <cacheLength>0</cacheLength>
        <mobileReady>false</mobileReady>
        <sboSyncEnabled>true</sboSyncEnabled>
        <externalId isNull="true" />
        <isSecured>true</isSecured>
        <isAjaxExposed>true</isAjaxExposed>
        <description isNull="true" />
        <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-7438</guid>
        <versionId>12879b4c-7ebd-4500-9f7c-a58760291453</versionId>
        <dependencySummary>&lt;dependencySummary id="bpdid:cdc758d910e20d2d:-607ed7:18af686ab01:-5e01" /&gt;</dependencySummary>
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["2027.975eeafe-d4e5-48ee-89be-27b4006e069a"],"isInterrupting":false,"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":28,"y":81,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Start","declaredType":"startEvent","id":"b9669e8f-9d98-47a6-8e09-a99551148445"},{"incoming":["0864f911-6ea8-448c-8b12-ad9bd424a823","5a7b3bae-0572-4d18-865a-53929499680f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":855,"y":85,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-7436"],"saveExecutionContext":[true]},"name":"End","declaredType":"endEvent","id":"2db68125-c325-457f-8cce-ce6434d12856"},{"targetRef":"f0cf680e-0585-42aa-862f-ea465fd74d18","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"2027.975eeafe-d4e5-48ee-89be-27b4006e069a","sourceRef":"b9669e8f-9d98-47a6-8e09-a99551148445"},{"startQuantity":1,"outgoing":["ec7f6dc8-ac49-47ef-89a0-eb33971407c5"],"incoming":["9f9d383c-c471-4a96-85af-78bca45f8ecb"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":365,"y":58,"declaredType":"TNodeVisualInfo","height":70}],"preAssignmentScript":["tw.local.isLiquidated = false;"]},"name":"Sql Statement","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"8f4b8e24-3698-499a-8d9e-05c81baf7ee1","scriptFormat":"text\/x-javascript","script":{"content":["tw.local.sql = \"insert into ODC_REQUESTINFO (requestNo ,requestNature ,requestType ,requestState ,requestStatus ,parentRequestNo,REVERSALREASON,requestDate,isliquidated )\"\r\n+\"values('\"+tw.local.requestNumber+\"' , '\"+tw.local.requestNature+\"','\"+tw.local.requestType+\"','\"+tw.local.requestState+\"','\"+tw.local.requestStatus+\"','\"+tw.local.parentRequestNumber+\"','\"+tw.local.reversalReason+\"',sysdate,'\"+tw.local.isLiquidated+\"');\""]}},{"startQuantity":1,"outgoing":["0864f911-6ea8-448c-8b12-ad9bd424a823"],"incoming":["ec7f6dc8-ac49-47ef-89a0-eb33971407c5"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":570,"y":58,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"Sql Execute Statement","dataInputAssociation":[{"targetRef":"2055.25f5990f-9abf-4cf6-9101-497d43dee141","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.sql"]}}]},{"targetRef":"2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","declaredType":"TFormalExpression","content":["tw.local.parameters"]}}]},{"targetRef":"2055.53ee0328-837f-427d-b0a1-0255f36f7e0c","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d","declaredType":"TFormalExpression","content":["-1"]}}]},{"targetRef":"2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297","declaredType":"TFormalExpression","content":["tw.env.DATASOURCE_AUDIT"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","declaredType":"TFormalExpression","content":["tw.local.results"]}}],"sourceRef":["2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764"]}],"calledElement":"1.4e480adb-5741-4f5e-aead-27b3654e9cd2"},{"targetRef":"8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Sql Execute Statement","declaredType":"sequenceFlow","id":"ec7f6dc8-ac49-47ef-89a0-eb33971407c5","sourceRef":"8f4b8e24-3698-499a-8d9e-05c81baf7ee1"},{"targetRef":"2db68125-c325-457f-8cce-ce6434d12856","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"endStateId":["guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End","declaredType":"sequenceFlow","id":"0864f911-6ea8-448c-8b12-ad9bd424a823","sourceRef":"8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"sql","isCollection":false,"declaredType":"dataObject","id":"2056.1c23e358-64a9-42de-8a46-8fe53a7cfba9"},{"itemSubjectRef":"itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c","name":"parameters","isCollection":true,"declaredType":"dataObject","id":"2056.0eed5a49-c55a-470f-856d-f99dffee3e7f"},{"itemSubjectRef":"itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1","name":"results","isCollection":true,"declaredType":"dataObject","id":"2056.367bfbe9-667e-41c2-830f-392649c221d9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"errorMsg","isCollection":false,"declaredType":"dataObject","id":"2056.fdbde454-4a0b-4efe-896e-c91d4caf4a7f"},{"startQuantity":1,"outgoing":["cf22b896-7ca8-4eea-8518-09ce0b396a51"],"incoming":["69ae5b24-b096-4db7-8d78-bdc840601d9b"],"extensionElements":{"nodeVisualInfo":[{"color":"#FF7782","width":95,"x":559,"y":183,"declaredType":"TNodeVisualInfo","height":70}]},"name":"Script Task","isForCompensation":false,"completionQuantity":1,"declaredType":"scriptTask","id":"122f91f9-b324-47b0-8b4d-eb94313e89d2","scriptFormat":"text\/x-javascript","script":{"content":["log.info(\"*============ODC PRocess =============*\");\r\nlog.info(\"*========================================*\");\r\nlog.info(\"ODC Audit Reversal Process start\");\r\nlog.info(\"ProcessInstance :\" +tw.system.currentProcessInstanceID + \" Error : \" + tw.system.error.toString(true));\r\ntw.local.errorMsg=String(tw.system.error.getAttribute(\"type\")) +\",\"+ String(tw.system.error.getElementByTagName(\"localizedMessage\").item(0).getText());\r\nlog.info(\"ODC Audit Reversal Process End\");\r\nlog.info(\"*========================================*\");"]}},{"parallelMultiple":false,"outgoing":["69ae5b24-b096-4db7-8d78-bdc840601d9b"],"dataOutput":[{"itemSubjectRef":"itm.19e8dc33-1100-46be-89a6-36c9040f7b3e","name":"error","isCollection":false,"id":"41e015fd-95a2-4da6-868a-e20556bf3e2b"}],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"29c857b3-8a0c-4f37-881a-1a1769726896","otherAttributes":{"eventImplId":"59c72b31-dbd7-41d6-8170-0e8629013067"}}],"attachedToRef":"8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9","extensionElements":{"nodeVisualInfo":[{"width":24,"x":605,"y":116,"declaredType":"TNodeVisualInfo","height":24}]},"cancelActivity":true,"name":"Error","declaredType":"boundaryEvent","id":"6f070f0e-6735-4642-885f-3b60073667b0","outputSet":{}},{"targetRef":"122f91f9-b324-47b0-8b4d-eb94313e89d2","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Script Task","declaredType":"sequenceFlow","id":"69ae5b24-b096-4db7-8d78-bdc840601d9b","sourceRef":"6f070f0e-6735-4642-885f-3b60073667b0"},{"incoming":["cf22b896-7ca8-4eea-8518-09ce0b396a51"],"eventDefinition":[{"extensionElements":{"errorEventSettings":[{"catchAll":true,"errorCode":"","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TErrorEventSettings"}]},"declaredType":"errorEventDefinition","id":"7e075a65-62c6-4de3-8d85-a6424ac1a22b","otherAttributes":{"eventImplId":"511a644c-8bc9-4d20-83d8-7152f35ba416"}}],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":853,"y":207,"declaredType":"TNodeVisualInfo","height":24}],"endStateId":["2b742a79-9148-4919-8cbb-8cadce301167"]},"name":"End Event","dataInputAssociation":[{"assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.errorMsg"]}}]}],"declaredType":"endEvent","id":"819eb946-14b8-42b6-8776-ca2405aaab2c"},{"targetRef":"819eb946-14b8-42b6-8776-ca2405aaab2c","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}]},"name":"To End Event","declaredType":"sequenceFlow","id":"cf22b896-7ca8-4eea-8518-09ce0b396a51","sourceRef":"122f91f9-b324-47b0-8b4d-eb94313e89d2"},{"startQuantity":1,"outgoing":["9f9d383c-c471-4a96-85af-78bca45f8ecb"],"incoming":["2027.975eeafe-d4e5-48ee-89be-27b4006e069a"],"extensionElements":{"nodeVisualInfo":[{"width":95,"x":119,"y":58,"declaredType":"TNodeVisualInfo","height":70}],"activityType":["CalledProcess"]},"name":"retrieve Request Number","dataInputAssociation":[{"targetRef":"2055.e1ca7465-4084-405d-8ea6-ab3f3762de92","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentRequestNumber"]}}]}],"isForCompensation":false,"completionQuantity":1,"declaredType":"callActivity","id":"f0cf680e-0585-42aa-862f-ea465fd74d18","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.requestNumber"]}}],"sourceRef":["2055.29fabc80-90b8-4cad-81e3-1c319c6f595a"]}],"calledElement":"1.509c6ef8-f489-4ab8-bcb8-45c4531aa546"},{"targetRef":"8f4b8e24-3698-499a-8d9e-05c81baf7ee1","extensionElements":{"endStateId":["guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796"],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}]},"name":"To Parent Doesn't exist","declaredType":"sequenceFlow","id":"9f9d383c-c471-4a96-85af-78bca45f8ecb","sourceRef":"f0cf680e-0585-42aa-862f-ea465fd74d18"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNumber","isCollection":false,"declaredType":"dataObject","id":"2056.07e4ce9c-5f69-473e-804f-48f1b540db94"},{"outgoing":["5a7b3bae-0572-4d18-865a-53929499680f"],"default":"5a7b3bae-0572-4d18-865a-53929499680f","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":255,"y":77,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Parent Doesn't exist","declaredType":"exclusiveGateway","id":"8a551a3a-4599-44ff-82c5-655a2970f61c"},{"targetRef":"2db68125-c325-457f-8cce-ce6434d12856","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.requestNumber\t  ==\t  \"\""]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true,"customBendPoint":[{"x":553,"y":19}]}]},"name":"To End","declaredType":"sequenceFlow","id":"5a7b3bae-0572-4d18-865a-53929499680f","sourceRef":"8a551a3a-4599-44ff-82c5-655a2970f61c"}],"laneSet":[{"id":"c291c350-32dc-4f12-8851-a1702fab5aaa","lane":[{"flowNodeRef":["b9669e8f-9d98-47a6-8e09-a99551148445","2db68125-c325-457f-8cce-ce6434d12856","8f4b8e24-3698-499a-8d9e-05c81baf7ee1","8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9","122f91f9-b324-47b0-8b4d-eb94313e89d2","6f070f0e-6735-4642-885f-3b60073667b0","819eb946-14b8-42b6-8776-ca2405aaab2c","f0cf680e-0585-42aa-862f-ea465fd74d18","8a551a3a-4599-44ff-82c5-655a2970f61c"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":500}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"060db4c3-ea1d-4787-8959-f9caf7f44a29","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"isClosed":false,"extensionElements":{"isSecured":[true],"isAjaxExposed":[true],"sboSyncEnabled":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"Audit Reversal Data","declaredType":"process","id":"1.a1d1f1de-87c1-424f-8115-086a8221db8b","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"microflow"},"ioSpecification":{"inputSet":[{"dataInputRefs":["2055.04908bef-7f28-40ac-8549-5f7dc09ba2f5","2055.7a1d4892-c3f8-4ef7-8c17-ceca9bf6e718","2055.46f3c712-bc16-4b15-8e3e-bfe93f23af1a","2055.56a31ea0-68d1-4cc3-8ef9-47e17a8339b9","2055.b0b91458-15b1-498c-815f-64e89b9236be","2055.9f46da50-358f-4e10-86dc-b238e761a4c8","2055.28a2190e-0deb-4627-8623-69a697f2b4d8"]}],"outputSet":[{}],"dataInput":[{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestNature","isCollection":false,"id":"2055.04908bef-7f28-40ac-8549-5f7dc09ba2f5"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestType","isCollection":false,"id":"2055.7a1d4892-c3f8-4ef7-8c17-ceca9bf6e718"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"\"\""}]},"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"reversalReason","isCollection":false,"id":"2055.46f3c712-bc16-4b15-8e3e-bfe93f23af1a"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestState","isCollection":false,"id":"2055.56a31ea0-68d1-4cc3-8ef9-47e17a8339b9"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"requestStatus","isCollection":false,"id":"2055.b0b91458-15b1-498c-815f-64e89b9236be"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentRequestNumber","isCollection":false,"id":"2055.9f46da50-358f-4e10-86dc-b238e761a4c8"},{"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isLiquidated","isCollection":false,"id":"2055.28a2190e-0deb-4627-8623-69a697f2b4d8"}]}}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript"}</jsonData>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <processParameter name="requestNature">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.04908bef-7f28-40ac-8549-5f7dc09ba2f5</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>1</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>beed832f-6788-47a2-8a25-14dca7fb8913</guid>
            <versionId>f26587c9-2402-41b3-aed7-2382c7eb3ac2</versionId>
        </processParameter>
        <processParameter name="requestType">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.7a1d4892-c3f8-4ef7-8c17-ceca9bf6e718</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>2</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>a08bb13a-6fa2-4be7-a0ba-3ac7bb96a7cd</guid>
            <versionId>7cb5f348-d1ff-4319-aee6-a00b82c97f55</versionId>
        </processParameter>
        <processParameter name="reversalReason">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.46f3c712-bc16-4b15-8e3e-bfe93f23af1a</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>3</seq>
            <hasDefault>true</hasDefault>
            <defaultValue>""</defaultValue>
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>ca8d0e1b-215c-4d1d-bfa5-1aa69046eba6</guid>
            <versionId>9a048a50-8e79-4a58-a3ce-99a4f5240f68</versionId>
        </processParameter>
        <processParameter name="requestState">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.56a31ea0-68d1-4cc3-8ef9-47e17a8339b9</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>4</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>19081158-2e7d-4dca-857e-fe00060a0816</guid>
            <versionId>8d26b509-1d37-425b-96b7-4719ee5075c3</versionId>
        </processParameter>
        <processParameter name="requestStatus">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.b0b91458-15b1-498c-815f-64e89b9236be</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>5</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>48ada59f-2402-4dc5-b245-416da73d54fd</guid>
            <versionId>93d5f3a2-2b0c-4925-8426-c1302da7ae2c</versionId>
        </processParameter>
        <processParameter name="parentRequestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.9f46da50-358f-4e10-86dc-b238e761a4c8</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>2de3d2c7-cccd-40eb-8730-4cd8a6e8a281</guid>
            <versionId>0fa48e0e-48dc-49dd-8ea5-56c83b2d2c9a</versionId>
        </processParameter>
        <processParameter name="">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.a8113ed2-c5a7-4d81-976f-4dafd46e4317</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>3</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <seq>6</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>7f954a9e-e9f1-4260-b3cc-b35c426c2451</guid>
            <versionId>37cd6df8-700f-40ee-9ae5-2765f1359add</versionId>
        </processParameter>
        <processParameter name="isLiquidated">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processParameterId>2055.28a2190e-0deb-4627-8623-69a697f2b4d8</processParameterId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
            <seq>7</seq>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isLocked>false</isLocked>
            <description isNull="true" />
            <guid>b361ade6-8a69-4a15-99a5-68e1b2355a09</guid>
            <versionId>88961d70-8dac-4231-bbf2-bd94b004e43e</versionId>
        </processParameter>
        <processVariable name="sql">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.1c23e358-64a9-42de-8a46-8fe53a7cfba9</processVariableId>
            <description isNull="true" />
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <namespace>2</namespace>
            <seq>1</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>4f20f905-ebf9-497f-9553-98a1d5a4a873</guid>
            <versionId>6f4cd391-0acf-42aa-9cd1-94890e594eb6</versionId>
        </processVariable>
        <processVariable name="parameters">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.0eed5a49-c55a-470f-856d-f99dffee3e7f</processVariableId>
            <description isNull="true" />
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <namespace>2</namespace>
            <seq>2</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>a7d46b7d-45f8-4bda-a613-b24313f52b45</guid>
            <versionId>5ffa4f93-e4b3-40d1-abe8-576c5f66bc9f</versionId>
        </processVariable>
        <processVariable name="results">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.367bfbe9-667e-41c2-830f-392649c221d9</processVariableId>
            <description isNull="true" />
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <namespace>2</namespace>
            <seq>3</seq>
            <isArrayOf>true</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>b47d9dc6-9d08-4cb9-b66e-f306b7b2cf93</guid>
            <versionId>*************-4f2c-ab5a-67ec11d58faf</versionId>
        </processVariable>
        <processVariable name="errorMsg">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.fdbde454-4a0b-4efe-896e-c91d4caf4a7f</processVariableId>
            <description isNull="true" />
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <namespace>2</namespace>
            <seq>4</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>cc9fecbc-02fb-4104-b173-fab78e3cfc34</guid>
            <versionId>7f4fb2f6-17eb-4eed-8a8b-66f5a61674f4</versionId>
        </processVariable>
        <processVariable name="requestNumber">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processVariableId>2056.07e4ce9c-5f69-473e-804f-48f1b540db94</processVariableId>
            <description isNull="true" />
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <namespace>2</namespace>
            <seq>5</seq>
            <isArrayOf>false</isArrayOf>
            <isTransient>false</isTransient>
            <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <guid>bb9aeca6-78c7-442e-9bcf-50014b1f8f82</guid>
            <versionId>29b6e21a-a240-49d0-bd38-6368ef281296</versionId>
        </processVariable>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.819eb946-14b8-42b6-8776-ca2405aaab2c</processItemId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <name>End Event</name>
            <tWComponentName>Exception</tWComponentName>
            <tWComponentId>3007.df01ffb4-3540-4dae-b948-1bcab7926ec7</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-6b04</guid>
            <versionId>16714139-3768-402c-a689-e2e39980dec7</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="853" y="207">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exceptionId>3007.df01ffb4-3540-4dae-b948-1bcab7926ec7</exceptionId>
                <message></message>
                <faultStyle>1</faultStyle>
                <guid>1d5c5bab-ac86-474d-a480-c38315373a6d</guid>
                <versionId>3547ee5d-bc2f-4581-8300-0420f9e047f1</versionId>
                <parameterMapping name="">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.419e1760-1e69-48bd-a455-c7bbd8e3ccad</parameterMappingId>
                    <processParameterId>2055.a8113ed2-c5a7-4d81-976f-4dafd46e4317</processParameterId>
                    <parameterMappingParentId>3007.df01ffb4-3540-4dae-b948-1bcab7926ec7</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.errorMsg</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>eb08e599-af05-4040-89b3-8f1b9d8f25b5</guid>
                    <versionId>f51ff231-b511-4171-a97b-d87ca8f3dc74</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8a551a3a-4599-44ff-82c5-655a2970f61c</processItemId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <name>Parent Doesn't exist</name>
            <tWComponentName>Switch</tWComponentName>
            <tWComponentId>3013.fb6f42b6-82ba-4a8a-9a5c-a9f6f5daad94</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:d4186c1fd70cbec4:-6b2ed2c1:18ac3cd4e1d:342d</guid>
            <versionId>29b6d6b2-efda-4fe7-97ca-1ed33569f518</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="255" y="77">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <switchId>3013.fb6f42b6-82ba-4a8a-9a5c-a9f6f5daad94</switchId>
                <guid>cca37204-47dd-4c35-985c-a2a5d7278753</guid>
                <versionId>7a6dca36-a8a5-41ba-80df-3b2af9a60f86</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9</processItemId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <name>Sql Execute Statement</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.82eb7ef2-1f12-454d-82f8-f10da99ef0a6</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>true</isErrorHandlerEnabled>
            <errorHandlerItemId>2025.122f91f9-b324-47b0-8b4d-eb94313e89d2</errorHandlerItemId>
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-73eb</guid>
            <versionId>53e8e430-e44d-4ed5-bf8f-70129d0f9b0f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="570" y="58">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <errorHandler>
                <name>Error</name>
                <locationId>bottomCenter</locationId>
                <faultStyle>1</faultStyle>
                <isCatchAll>true</isCatchAll>
                <errorHandlerItem>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-6b30</errorHandlerItem>
                <errorHandlerItemId>2025.122f91f9-b324-47b0-8b4d-eb94313e89d2</errorHandlerItemId>
                <errorHandlerLink>
                    <fromPort locationId="bottomCenter" portType="1" />
                    <toPort locationId="topCenter" portType="2" />
                    <layoutData>
                        <controlPoints />
                        <showEndState>false</showEndState>
                        <showName>false</showName>
                    </layoutData>
                </errorHandlerLink>
            </errorHandler>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.82eb7ef2-1f12-454d-82f8-f10da99ef0a6</subProcessId>
                <attachedProcessRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/1.4e480adb-5741-4f5e-aead-27b3654e9cd2</attachedProcessRef>
                <guid>dab1d6da-7d1a-44e7-8add-365f3ea5a277</guid>
                <versionId>6115cf61-4443-4c55-b76b-cb73c4411acb</versionId>
                <parameterMapping name="parameters">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.e376307a-c9ad-4ba9-a9b6-9d5650720fab</parameterMappingId>
                    <processParameterId>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</processParameterId>
                    <parameterMappingParentId>3012.82eb7ef2-1f12-454d-82f8-f10da99ef0a6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parameters</value>
                    <classRef>/12.f453f500-ca4e-4264-a371-72c1892e8b7c</classRef>
                    <isList>true</isList>
                    <isInput>true</isInput>
                    <guid>09ff203f-5f0d-4641-92ad-f1209743af33</guid>
                    <versionId>1f8f7f01-debd-4c60-b100-84ad19508db3</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="results">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.069ecaa0-afac-471f-a511-66b4152fae47</parameterMappingId>
                    <processParameterId>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</processParameterId>
                    <parameterMappingParentId>3012.82eb7ef2-1f12-454d-82f8-f10da99ef0a6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.results</value>
                    <classRef>/12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1</classRef>
                    <isList>true</isList>
                    <isInput>false</isInput>
                    <guid>a81a6cbb-03be-49c2-ba50-9fcaae63b64f</guid>
                    <versionId>3a11b45d-0140-40b3-ac45-c3191ac5bb55</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="maxRows">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.6af63086-4ca1-4d8c-97a1-f77de52e43c0</parameterMappingId>
                    <processParameterId>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</processParameterId>
                    <parameterMappingParentId>3012.82eb7ef2-1f12-454d-82f8-f10da99ef0a6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>-1</value>
                    <classRef>/12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>b54ef2a9-c25d-4c7b-a412-979817c0012a</guid>
                    <versionId>506e287f-3aa8-432a-a34c-a0253bb04ebc</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="dataSourceName">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.766e772f-581e-469e-9600-82641185f96d</parameterMappingId>
                    <processParameterId>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</processParameterId>
                    <parameterMappingParentId>3012.82eb7ef2-1f12-454d-82f8-f10da99ef0a6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.env.DATASOURCE_AUDIT</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>25bec04f-77c4-4c38-9a6e-4ea46c46a7eb</guid>
                    <versionId>62dc0d60-de91-4e41-a3c4-8eabfaf43cb0</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="sql">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.5d77d308-f045-4b92-9110-ef577c82137b</parameterMappingId>
                    <processParameterId>2055.25f5990f-9abf-4cf6-9101-497d43dee141</processParameterId>
                    <parameterMappingParentId>3012.82eb7ef2-1f12-454d-82f8-f10da99ef0a6</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.sql</value>
                    <classRef>/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>7f491e3e-fcae-4a28-966f-82f05fac36f8</guid>
                    <versionId>91c585e4-c424-434f-afcf-d7660307831d</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.f0cf680e-0585-42aa-862f-ea465fd74d18</processItemId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <name>retrieve Request Number</name>
            <tWComponentName>SubProcess</tWComponentName>
            <tWComponentId>3012.903d64bd-e08b-4c34-a216-86e78e42ff6d</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:989374ae3827db3a:f13aa6c:18aa2e844f6:-4073</guid>
            <versionId>b65e354e-7ed5-4ae6-a56c-11db635a997f</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="119" y="58">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <subProcessId>3012.903d64bd-e08b-4c34-a216-86e78e42ff6d</subProcessId>
                <attachedProcessRef>/1.509c6ef8-f489-4ab8-bcb8-45c4531aa546</attachedProcessRef>
                <guid>37c0e85f-d980-4a0d-a24c-4f95f3acdc11</guid>
                <versionId>1b8a288f-c354-496b-a957-02b083058d7c</versionId>
                <parameterMapping name="newRequestId">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.21a0b95f-8f26-4e5b-8c63-fc0aad8ff769</parameterMappingId>
                    <processParameterId>2055.29fabc80-90b8-4cad-81e3-1c319c6f595a</processParameterId>
                    <parameterMappingParentId>3012.903d64bd-e08b-4c34-a216-86e78e42ff6d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.requestNumber</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>false</isInput>
                    <guid>d5d196de-7146-41e9-b306-d47355fa97d8</guid>
                    <versionId>0913c8b6-a965-4309-baaf-f8b72f5e99ac</versionId>
                    <description isNull="true" />
                </parameterMapping>
                <parameterMapping name="parentRequestNumber">
                    <lastModified isNull="true" />
                    <lastModifiedBy isNull="true" />
                    <parameterMappingId>2054.245fe95a-fd30-4b90-b93e-0805d09eef31</parameterMappingId>
                    <processParameterId>2055.e1ca7465-4084-405d-8ea6-ab3f3762de92</processParameterId>
                    <parameterMappingParentId>3012.903d64bd-e08b-4c34-a216-86e78e42ff6d</parameterMappingParentId>
                    <useDefault>false</useDefault>
                    <value>tw.local.parentRequestNumber</value>
                    <classRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classRef>
                    <isList>false</isList>
                    <isInput>true</isInput>
                    <guid>ae640a18-62e7-45af-bc3d-4b105e087f6d</guid>
                    <versionId>5cae10f4-294a-46e4-afad-2a7f67bc9bca</versionId>
                    <description isNull="true" />
                </parameterMapping>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.2db68125-c325-457f-8cce-ce6434d12856</processItemId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <name>End</name>
            <tWComponentName>ExitPoint</tWComponentName>
            <tWComponentId>3008.4207667f-d02a-4a41-a1ca-5002009f8749</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>true</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-7436</guid>
            <versionId>b68b19e9-f39a-45a5-8ef6-526fabcf6158</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <layoutData x="855" y="85">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <exitPointId>3008.4207667f-d02a-4a41-a1ca-5002009f8749</exitPointId>
                <haltProcess>false</haltProcess>
                <guid>972e60e0-760a-4874-866f-6368b796e837</guid>
                <versionId>05926305-cb47-40fb-af87-4529b6daed70</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.122f91f9-b324-47b0-8b4d-eb94313e89d2</processItemId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <name>Script Task</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.230ebd56-bffb-48e5-9cb4-162f699a0cd9</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-6b30</guid>
            <versionId>c46fd6c6-4ccd-47c2-bab4-3b6fac2f567d</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor>#FF7782</nodeColor>
            <layoutData x="559" y="183">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.230ebd56-bffb-48e5-9cb4-162f699a0cd9</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>log.info("*============ODC PRocess =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("ODC Audit Reversal Process start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMsg=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("ODC Audit Reversal Process End");&#xD;
log.info("*========================================*");</script>
                <isRule>false</isRule>
                <guid>ced69be4-5958-408d-a7d0-0a3a79ce8462</guid>
                <versionId>2c26bb24-a893-4f59-84b4-bb0b6c3a74ff</versionId>
            </TWComponent>
        </item>
        <item>
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processItemId>2025.8f4b8e24-3698-499a-8d9e-05c81baf7ee1</processItemId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <name>Sql Statement</name>
            <tWComponentName>Script</tWComponentName>
            <tWComponentId>3011.50b149f6-5d38-4a76-ba62-d799b2e9af2e</tWComponentId>
            <isLogEnabled>false</isLogEnabled>
            <isTraceEnabled>false</isTraceEnabled>
            <traceCategory isNull="true" />
            <traceLevel isNull="true" />
            <traceMessage isNull="true" />
            <traceSymbolTable isNull="true" />
            <isExecutionContextTraced>false</isExecutionContextTraced>
            <saveExecutionContext>false</saveExecutionContext>
            <documentation isNull="true" />
            <isErrorHandlerEnabled>false</isErrorHandlerEnabled>
            <errorHandlerItemId isNull="true" />
            <guid>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-73ec</guid>
            <versionId>d4a45acc-b578-4c8d-97aa-0352201461b2</versionId>
            <externalServiceRef isNull="true" />
            <externalServiceOp isNull="true" />
            <nodeColor isNull="true" />
            <processPrePosts>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <processItemPrePostId>2029.3d14ed90-0e0f-42ee-90b4-9fd30351194e</processItemPrePostId>
                <processItemId>2025.8f4b8e24-3698-499a-8d9e-05c81baf7ee1</processItemId>
                <location>1</location>
                <script>tw.local.isLiquidated = false;</script>
                <guid>d22d0eb7-52df-4745-b7cf-6b56df1d0a55</guid>
                <versionId>210f57f8-1a30-4f1f-834e-c8ed2456f9e2</versionId>
            </processPrePosts>
            <layoutData x="365" y="58">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
            <TWComponent>
                <lastModified isNull="true" />
                <lastModifiedBy isNull="true" />
                <scriptId>3011.50b149f6-5d38-4a76-ba62-d799b2e9af2e</scriptId>
                <scriptTypeId>2</scriptTypeId>
                <isActive>true</isActive>
                <script>tw.local.sql = "insert into ODC_REQUESTINFO (requestNo ,requestNature ,requestType ,requestState ,requestStatus ,parentRequestNo,REVERSALREASON,requestDate,isliquidated )"&#xD;
+"values('"+tw.local.requestNumber+"' , '"+tw.local.requestNature+"','"+tw.local.requestType+"','"+tw.local.requestState+"','"+tw.local.requestStatus+"','"+tw.local.parentRequestNumber+"','"+tw.local.reversalReason+"',sysdate,'"+tw.local.isLiquidated+"');"</script>
                <isRule>false</isRule>
                <guid>8ae0f5c4-e505-42e5-b32f-b2ca32e736b9</guid>
                <versionId>664f55b3-26f6-440f-b3a8-390aac75a173</versionId>
            </TWComponent>
        </item>
        <startingProcessItemId>2025.f0cf680e-0585-42aa-862f-ea465fd74d18</startingProcessItemId>
        <errorHandlerItemId isNull="true" />
        <layoutData noConversion="true">
            <errorLink>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </errorLink>
        </layoutData>
        <startPoint>
            <layoutData x="28" y="81">
                <errorLink>
                    <controlPoints />
                    <showEndState>false</showEndState>
                    <showName>false</showName>
                </errorLink>
            </layoutData>
        </startPoint>
        <startLink>
            <fromPort locationId="rightCenter" portType="1" />
            <toPort locationId="leftCenter" portType="2" />
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
        </startLink>
        <bpmn2Model>
            <ns16:definitions xmlns:ns16="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns15="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns17="http://www.ibm.com/bpm/processappsettings" xmlns:ns18="http://www.ibm.com/xmlns/links" xmlns:ns19="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns20="http://www.ibm.com/xmlns/tagging" xmlns:ns21="http://www.ibm.com/bpm/uitheme" xmlns:ns22="http://www.ibm.com/bpm/coachview" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript" typeLanguage="http://www.w3.org/2001/XMLSchema">
                
                
                <ns16:process name="Audit Reversal Data" id="1.a1d1f1de-87c1-424f-8115-086a8221db8b" ns3:executionMode="microflow">
                    
                    
                    <ns16:documentation textFormat="text/plain" />
                    
                    
                    <ns16:extensionElements>
                        
                        
                        <ns3:isSecured>true</ns3:isSecured>
                        
                        
                        <ns3:isAjaxExposed>true</ns3:isAjaxExposed>
                        
                        
                        <ns3:sboSyncEnabled>true</ns3:sboSyncEnabled>
                        
                    
                    </ns16:extensionElements>
                    
                    
                    <ns16:ioSpecification>
                        
                        
                        <ns16:dataInput name="requestNature" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.04908bef-7f28-40ac-8549-5f7dc09ba2f5" />
                        
                        
                        <ns16:dataInput name="requestType" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.7a1d4892-c3f8-4ef7-8c17-ceca9bf6e718" />
                        
                        
                        <ns16:dataInput name="reversalReason" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.46f3c712-bc16-4b15-8e3e-bfe93f23af1a">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns3:defaultValue useDefault="true">""</ns3:defaultValue>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:dataInput>
                        
                        
                        <ns16:dataInput name="requestState" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.56a31ea0-68d1-4cc3-8ef9-47e17a8339b9" />
                        
                        
                        <ns16:dataInput name="requestStatus" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.b0b91458-15b1-498c-815f-64e89b9236be" />
                        
                        
                        <ns16:dataInput name="parentRequestNumber" itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" id="2055.9f46da50-358f-4e10-86dc-b238e761a4c8" />
                        
                        
                        <ns16:dataInput name="isLiquidated" itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" id="2055.28a2190e-0deb-4627-8623-69a697f2b4d8" />
                        
                        
                        <ns16:inputSet>
                            
                            
                            <ns16:dataInputRefs>2055.04908bef-7f28-40ac-8549-5f7dc09ba2f5</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.7a1d4892-c3f8-4ef7-8c17-ceca9bf6e718</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.46f3c712-bc16-4b15-8e3e-bfe93f23af1a</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.56a31ea0-68d1-4cc3-8ef9-47e17a8339b9</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.b0b91458-15b1-498c-815f-64e89b9236be</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.9f46da50-358f-4e10-86dc-b238e761a4c8</ns16:dataInputRefs>
                            
                            
                            <ns16:dataInputRefs>2055.28a2190e-0deb-4627-8623-69a697f2b4d8</ns16:dataInputRefs>
                            
                        
                        </ns16:inputSet>
                        
                        
                        <ns16:outputSet />
                        
                    
                    </ns16:ioSpecification>
                    
                    
                    <ns16:laneSet id="c291c350-32dc-4f12-8851-a1702fab5aaa">
                        
                        
                        <ns16:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="060db4c3-ea1d-4787-8959-f9caf7f44a29" ns4:isSystemLane="true">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns13:nodeVisualInfo x="0" y="0" width="3000" height="500" color="#F8F8F8" />
                                
                            
                            </ns16:extensionElements>
                            
                            
                            <ns16:flowNodeRef>b9669e8f-9d98-47a6-8e09-a99551148445</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>2db68125-c325-457f-8cce-ce6434d12856</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8f4b8e24-3698-499a-8d9e-05c81baf7ee1</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>122f91f9-b324-47b0-8b4d-eb94313e89d2</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>6f070f0e-6735-4642-885f-3b60073667b0</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>819eb946-14b8-42b6-8776-ca2405aaab2c</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>f0cf680e-0585-42aa-862f-ea465fd74d18</ns16:flowNodeRef>
                            
                            
                            <ns16:flowNodeRef>8a551a3a-4599-44ff-82c5-655a2970f61c</ns16:flowNodeRef>
                            
                        
                        </ns16:lane>
                        
                    
                    </ns16:laneSet>
                    
                    
                    <ns16:startEvent isInterrupting="false" parallelMultiple="false" name="Start" id="b9669e8f-9d98-47a6-8e09-a99551148445">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="28" y="81" width="24" height="24" color="#F8F8F8" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>2027.975eeafe-d4e5-48ee-89be-27b4006e069a</ns16:outgoing>
                        
                    
                    </ns16:startEvent>
                    
                    
                    <ns16:endEvent name="End" id="2db68125-c325-457f-8cce-ce6434d12856">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="855" y="85" width="24" height="24" color="#F8F8F8" />
                            
                            
                            <ns4:saveExecutionContext>true</ns4:saveExecutionContext>
                            
                            
                            <ns3:endStateId>guid:7338a5af3678fb7f:3977eb80:18a89eb1cf1:-7436</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>0864f911-6ea8-448c-8b12-ad9bd424a823</ns16:incoming>
                        
                        
                        <ns16:incoming>5a7b3bae-0572-4d18-865a-53929499680f</ns16:incoming>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="b9669e8f-9d98-47a6-8e09-a99551148445" targetRef="f0cf680e-0585-42aa-862f-ea465fd74d18" name="To End" id="2027.975eeafe-d4e5-48ee-89be-27b4006e069a">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Sql Statement" id="8f4b8e24-3698-499a-8d9e-05c81baf7ee1">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="365" y="58" width="95" height="70" />
                            
                            
                            <ns3:preAssignmentScript>tw.local.isLiquidated = false;</ns3:preAssignmentScript>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>9f9d383c-c471-4a96-85af-78bca45f8ecb</ns16:incoming>
                        
                        
                        <ns16:outgoing>ec7f6dc8-ac49-47ef-89a0-eb33971407c5</ns16:outgoing>
                        
                        
                        <ns16:script>tw.local.sql = "insert into ODC_REQUESTINFO (requestNo ,requestNature ,requestType ,requestState ,requestStatus ,parentRequestNo,REVERSALREASON,requestDate,isliquidated )"&#xD;
+"values('"+tw.local.requestNumber+"' , '"+tw.local.requestNature+"','"+tw.local.requestType+"','"+tw.local.requestState+"','"+tw.local.requestStatus+"','"+tw.local.parentRequestNumber+"','"+tw.local.reversalReason+"',sysdate,'"+tw.local.isLiquidated+"');"</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:callActivity calledElement="1.4e480adb-5741-4f5e-aead-27b3654e9cd2" isForCompensation="false" startQuantity="1" completionQuantity="1" name="Sql Execute Statement" id="8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="570" y="58" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>ec7f6dc8-ac49-47ef-89a0-eb33971407c5</ns16:incoming>
                        
                        
                        <ns16:outgoing>0864f911-6ea8-448c-8b12-ad9bd424a823</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.25f5990f-9abf-4cf6-9101-497d43dee141</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.sql</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.8b844f2a-0a1f-4f12-b34b-e9fd5191dc43</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c">tw.local.parameters</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.53ee0328-837f-427d-b0a1-0255f36f7e0c</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3fa0d7a0-828a-4d60-99cc-db5ed143fc2d">-1</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.81a41d09-a504-4a63-bdaa-a92bac26ad7e</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.c09c9b6e-aabd-4897-bef2-ed61db106297">tw.env.DATASOURCE_AUDIT</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.976cdc78-9d8d-41d4-ae85-f6c9a50c6764</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1">tw.local.results</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="8f4b8e24-3698-499a-8d9e-05c81baf7ee1" targetRef="8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9" name="To Sql Execute Statement" id="ec7f6dc8-ac49-47ef-89a0-eb33971407c5">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:sequenceFlow sourceRef="8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9" targetRef="2db68125-c325-457f-8cce-ce6434d12856" name="To End" id="0864f911-6ea8-448c-8b12-ad9bd424a823">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="sql" id="2056.1c23e358-64a9-42de-8a46-8fe53a7cfba9" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.f453f500-ca4e-4264-a371-72c1892e8b7c" isCollection="true" name="parameters" id="2056.0eed5a49-c55a-470f-856d-f99dffee3e7f" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.3b1e3757-a1f4-4c3f-bbb8-16fecd4d65c1" isCollection="true" name="results" id="2056.367bfbe9-667e-41c2-830f-392649c221d9" />
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="errorMsg" id="2056.fdbde454-4a0b-4efe-896e-c91d4caf4a7f" />
                    
                    
                    <ns16:scriptTask scriptFormat="text/x-javascript" name="Script Task" id="122f91f9-b324-47b0-8b4d-eb94313e89d2">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="559" y="183" width="95" height="70" color="#FF7782" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>69ae5b24-b096-4db7-8d78-bdc840601d9b</ns16:incoming>
                        
                        
                        <ns16:outgoing>cf22b896-7ca8-4eea-8518-09ce0b396a51</ns16:outgoing>
                        
                        
                        <ns16:script>log.info("*============ODC PRocess =============*");&#xD;
log.info("*========================================*");&#xD;
log.info("ODC Audit Reversal Process start");&#xD;
log.info("ProcessInstance :" +tw.system.currentProcessInstanceID + " Error : " + tw.system.error.toString(true));&#xD;
tw.local.errorMsg=String(tw.system.error.getAttribute("type")) +","+ String(tw.system.error.getElementByTagName("localizedMessage").item(0).getText());&#xD;
log.info("ODC Audit Reversal Process End");&#xD;
log.info("*========================================*");</ns16:script>
                        
                    
                    </ns16:scriptTask>
                    
                    
                    <ns16:boundaryEvent cancelActivity="true" attachedToRef="8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9" parallelMultiple="false" name="Error" id="6f070f0e-6735-4642-885f-3b60073667b0">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="605" y="116" width="24" height="24" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>69ae5b24-b096-4db7-8d78-bdc840601d9b</ns16:outgoing>
                        
                        
                        <ns16:dataOutput name="error" itemSubjectRef="itm.19e8dc33-1100-46be-89a6-36c9040f7b3e" isCollection="false" id="41e015fd-95a2-4da6-868a-e20556bf3e2b" />
                        
                        
                        <ns16:outputSet />
                        
                        
                        <ns16:errorEventDefinition id="29c857b3-8a0c-4f37-881a-1a1769726896" eventImplId="59c72b31-dbd7-41d6-8170-0e8629013067">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:boundaryEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="6f070f0e-6735-4642-885f-3b60073667b0" targetRef="122f91f9-b324-47b0-8b4d-eb94313e89d2" name="To Script Task" id="69ae5b24-b096-4db7-8d78-bdc840601d9b">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>bottomCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:endEvent name="End Event" id="819eb946-14b8-42b6-8776-ca2405aaab2c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="853" y="207" width="24" height="24" />
                            
                            
                            <ns3:endStateId>2b742a79-9148-4919-8cbb-8cadce301167</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>cf22b896-7ca8-4eea-8518-09ce0b396a51</ns16:incoming>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.errorMsg</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:errorEventDefinition id="7e075a65-62c6-4de3-8d85-a6424ac1a22b" eventImplId="511a644c-8bc9-4d20-83d8-7152f35ba416">
                            
                            
                            <ns16:extensionElements>
                                
                                
                                <ns4:errorEventSettings>
                                    
                                    
                                    <ns4:catchAll>true</ns4:catchAll>
                                    
                                    
                                    <ns4:errorCode />
                                    
                                
                                </ns4:errorEventSettings>
                                
                            
                            </ns16:extensionElements>
                            
                        
                        </ns16:errorEventDefinition>
                        
                    
                    </ns16:endEvent>
                    
                    
                    <ns16:sequenceFlow sourceRef="122f91f9-b324-47b0-8b4d-eb94313e89d2" targetRef="819eb946-14b8-42b6-8776-ca2405aaab2c" name="To End Event" id="cf22b896-7ca8-4eea-8518-09ce0b396a51">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:callActivity calledElement="1.509c6ef8-f489-4ab8-bcb8-45c4531aa546" isForCompensation="false" startQuantity="1" completionQuantity="1" name="retrieve Request Number" id="f0cf680e-0585-42aa-862f-ea465fd74d18">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="119" y="58" width="95" height="70" />
                            
                            
                            <ns4:activityType>CalledProcess</ns4:activityType>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:incoming>2027.975eeafe-d4e5-48ee-89be-27b4006e069a</ns16:incoming>
                        
                        
                        <ns16:outgoing>9f9d383c-c471-4a96-85af-78bca45f8ecb</ns16:outgoing>
                        
                        
                        <ns16:dataInputAssociation>
                            
                            
                            <ns16:targetRef>2055.e1ca7465-4084-405d-8ea6-ab3f3762de92</ns16:targetRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.parentRequestNumber</ns16:from>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataInputAssociation>
                        
                        
                        <ns16:dataOutputAssociation>
                            
                            
                            <ns16:sourceRef>2055.29fabc80-90b8-4cad-81e3-1c319c6f595a</ns16:sourceRef>
                            
                            
                            <ns16:assignment>
                                
                                
                                <ns16:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022">tw.local.requestNumber</ns16:to>
                                
                            
                            </ns16:assignment>
                            
                        
                        </ns16:dataOutputAssociation>
                        
                    
                    </ns16:callActivity>
                    
                    
                    <ns16:sequenceFlow sourceRef="f0cf680e-0585-42aa-862f-ea465fd74d18" targetRef="8f4b8e24-3698-499a-8d9e-05c81baf7ee1" name="To Parent Doesn't exist" id="9f9d383c-c471-4a96-85af-78bca45f8ecb">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>rightCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>leftCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>false</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:saveExecutionContext>false</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                            
                            <ns3:endStateId>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796</ns3:endStateId>
                            
                        
                        </ns16:extensionElements>
                        
                    
                    </ns16:sequenceFlow>
                    
                    
                    <ns16:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="requestNumber" id="2056.07e4ce9c-5f69-473e-804f-48f1b540db94" />
                    
                    
                    <ns16:exclusiveGateway default="5a7b3bae-0572-4d18-865a-53929499680f" name="Parent Doesn't exist" id="8a551a3a-4599-44ff-82c5-655a2970f61c">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns13:nodeVisualInfo x="255" y="77" width="32" height="32" />
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:outgoing>5a7b3bae-0572-4d18-865a-53929499680f</ns16:outgoing>
                        
                    
                    </ns16:exclusiveGateway>
                    
                    
                    <ns16:sequenceFlow sourceRef="8a551a3a-4599-44ff-82c5-655a2970f61c" targetRef="2db68125-c325-457f-8cce-ce6434d12856" name="To End" id="5a7b3bae-0572-4d18-865a-53929499680f">
                        
                        
                        <ns16:extensionElements>
                            
                            
                            <ns3:sequenceFlowImplementation sboSyncEnabled="true" />
                            
                            
                            <ns13:linkVisualInfo>
                                
                                
                                <ns13:sourcePortLocation>topCenter</ns13:sourcePortLocation>
                                
                                
                                <ns13:targetPortLocation>topCenter</ns13:targetPortLocation>
                                
                                
                                <ns13:showLabel>true</ns13:showLabel>
                                
                                
                                <ns13:showCoachControlLabel>false</ns13:showCoachControlLabel>
                                
                                
                                <ns13:labelPosition>0.0</ns13:labelPosition>
                                
                                
                                <ns13:customBendPoint x="553" y="19" />
                                
                                
                                <ns13:saveExecutionContext>true</ns13:saveExecutionContext>
                                
                            
                            </ns13:linkVisualInfo>
                            
                        
                        </ns16:extensionElements>
                        
                        
                        <ns16:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns16:tFormalExpression">tw.local.requestNumber	  ==	  ""</ns16:conditionExpression>
                        
                    
                    </ns16:sequenceFlow>
                    
                
                </ns16:process>
                
            
            </ns16:definitions>
        </bpmn2Model>
        <link name="To Parent Doesn't exist">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.9f9d383c-c471-4a96-85af-78bca45f8ecb</processLinkId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.f0cf680e-0585-42aa-862f-ea465fd74d18</fromProcessItemId>
            <endStateId>guid:7338a5af3678fb7f:3977eb80:18a8954cb2f:3796</endStateId>
            <toProcessItemId>2025.8f4b8e24-3698-499a-8d9e-05c81baf7ee1</toProcessItemId>
            <guid>13ff3b87-361c-4d69-ba3b-d52306de522f</guid>
            <versionId>4e71f834-3f69-47ad-9852-4a7bd97c5ea9</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.f0cf680e-0585-42aa-862f-ea465fd74d18</fromProcessItemId>
            <toProcessItemId>2025.8f4b8e24-3698-499a-8d9e-05c81baf7ee1</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.5a7b3bae-0572-4d18-865a-53929499680f</processLinkId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8a551a3a-4599-44ff-82c5-655a2970f61c</fromProcessItemId>
            <endStateId>DEFAULT</endStateId>
            <toProcessItemId>2025.2db68125-c325-457f-8cce-ce6434d12856</toProcessItemId>
            <guid>3795deef-a828-4488-973f-37fc74dd1079</guid>
            <versionId>89b2ffdc-9c92-4b27-b1cd-c675a912b20f</versionId>
            <layoutData>
                <controlPoints>
                    <controlPoint x="553" y="19" />
                </controlPoints>
                <showEndState>false</showEndState>
                <showName>true</showName>
            </layoutData>
            <fromItemPort locationId="topCenter" portType="1" />
            <toItemPort locationId="topCenter" portType="2" />
            <fromProcessItemId>2025.8a551a3a-4599-44ff-82c5-655a2970f61c</fromProcessItemId>
            <toProcessItemId>2025.2db68125-c325-457f-8cce-ce6434d12856</toProcessItemId>
        </link>
        <link name="To Sql Execute Statement">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.ec7f6dc8-ac49-47ef-89a0-eb33971407c5</processLinkId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8f4b8e24-3698-499a-8d9e-05c81baf7ee1</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9</toProcessItemId>
            <guid>d082d2a1-261a-4b55-beae-77824503d500</guid>
            <versionId>bfdf71bb-63e3-4c63-933a-40b3bb9a0cb1</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8f4b8e24-3698-499a-8d9e-05c81baf7ee1</fromProcessItemId>
            <toProcessItemId>2025.8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9</toProcessItemId>
        </link>
        <link name="To End">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.0864f911-6ea8-448c-8b12-ad9bd424a823</processLinkId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9</fromProcessItemId>
            <endStateId>guid:d7766b7587753602:16bdf27c:11586c10093:-7fe7</endStateId>
            <toProcessItemId>2025.2db68125-c325-457f-8cce-ce6434d12856</toProcessItemId>
            <guid>20ee92b9-caf6-4468-88bf-81006510a624</guid>
            <versionId>e15400cb-1bb0-4e33-a10f-491a16f7c8ee</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.8013ed1b-7ba6-4ee8-8d41-2d0de240bbc9</fromProcessItemId>
            <toProcessItemId>2025.2db68125-c325-457f-8cce-ce6434d12856</toProcessItemId>
        </link>
        <link name="To End Event">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <processLinkId>2027.cf22b896-7ca8-4eea-8518-09ce0b396a51</processLinkId>
            <processId>1.a1d1f1de-87c1-424f-8115-086a8221db8b</processId>
            <description isNull="true" />
            <fromProcessItemId>2025.122f91f9-b324-47b0-8b4d-eb94313e89d2</fromProcessItemId>
            <endStateId>Out</endStateId>
            <toProcessItemId>2025.819eb946-14b8-42b6-8776-ca2405aaab2c</toProcessItemId>
            <guid>b568e29d-b36e-4695-b44a-bcabcf60412c</guid>
            <versionId>e8784097-d2ce-4b7d-9dcb-64557618820b</versionId>
            <layoutData>
                <controlPoints />
                <showEndState>false</showEndState>
                <showName>false</showName>
            </layoutData>
            <fromItemPort locationId="rightCenter" portType="1" />
            <toItemPort locationId="leftCenter" portType="2" />
            <fromProcessItemId>2025.122f91f9-b324-47b0-8b4d-eb94313e89d2</fromProcessItemId>
            <toProcessItemId>2025.819eb946-14b8-42b6-8776-ca2405aaab2c</toProcessItemId>
        </link>
    </process>
</teamworks>

