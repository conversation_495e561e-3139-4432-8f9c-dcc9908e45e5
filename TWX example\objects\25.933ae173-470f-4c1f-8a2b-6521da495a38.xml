<?xml version="1.0" encoding="UTF-8"?>
<teamworks>
    <bpd id="25.933ae173-470f-4c1f-8a2b-6521da495a38" name="ODC Reversal اعادة قيد تحصيل مستندى تصدير">
        <bpdParameter name="odcRequest">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.86f87789-824f-4930-8c19-5a719284c659</bpdParameterId>
            <bpdId>25.933ae173-470f-4c1f-8a2b-6521da495a38</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
            <seq>0</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue>var autoObject = new tw.object.ODCRequest();
autoObject.initiator = "";
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestNature.name = "";
autoObject.requestNature.value = "";
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.requestType.name = "";
autoObject.requestType.value = "";
autoObject.cif = "";
autoObject.customerName = "";
autoObject.parentRequestNo = "";
autoObject.requestDate = new TWDate();
autoObject.ImporterName = "";
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();
autoObject.appInfo.requestDate = "";
autoObject.appInfo.status = "";
autoObject.appInfo.subStatus = "";
autoObject.appInfo.initiator = "";
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.appInfo.branch.name = "";
autoObject.appInfo.branch.value = "";
autoObject.appInfo.requestName = "";
autoObject.appInfo.requestType = "";
autoObject.appInfo.stepName = "";
autoObject.appInfo.appRef = "";
autoObject.appInfo.appID = "";
autoObject.appInfo.instanceID = "";
autoObject.CustomerInfo = new tw.object.CustomerInfo();
autoObject.CustomerInfo.cif = "";
autoObject.CustomerInfo.customerName = "";
autoObject.CustomerInfo.addressLine1 = "";
autoObject.CustomerInfo.addressLine2 = "";
autoObject.CustomerInfo.addressLine3 = "";
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.customerSector.name = "";
autoObject.CustomerInfo.customerSector.value = "";
autoObject.CustomerInfo.customerType = "";
autoObject.CustomerInfo.customerNoCBE = "";
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.CustomerInfo.facilityType.name = "";
autoObject.CustomerInfo.facilityType.value = "";
autoObject.CustomerInfo.commercialRegistrationNo = "";
autoObject.CustomerInfo.commercialRegistrationOffice = "";
autoObject.CustomerInfo.taxCardNo = "";
autoObject.CustomerInfo.importCardNo = "";
autoObject.CustomerInfo.initiationHub = "";
autoObject.CustomerInfo.country = "";
autoObject.BasicDetails = new tw.object.BasicDetails();
autoObject.BasicDetails.requestNature = "";
autoObject.BasicDetails.requestType = "";
autoObject.BasicDetails.parentRequestNo = "";
autoObject.BasicDetails.requestState = "";
autoObject.BasicDetails.flexCubeContractNo = "";
autoObject.BasicDetails.contractStage = "";
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.exportPurpose.name = "";
autoObject.BasicDetails.exportPurpose.value = "";
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.paymentTerms.name = "";
autoObject.BasicDetails.paymentTerms.value = "";
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.productCategory.name = "";
autoObject.BasicDetails.productCategory.value = "";
autoObject.BasicDetails.commodityDescription = "";
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.BasicDetails.CountryOfOrigin.name = "";
autoObject.BasicDetails.CountryOfOrigin.value = "";
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();
autoObject.BasicDetails.Invoice[0].invoiceNo = "";
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();
autoObject.GeneratedDocumentInfo.customerName = "";
autoObject.GeneratedDocumentInfo.customerAddress = "";
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = "";
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.Instructions[0] = "";
autoObject.GeneratedDocumentInfo.InstructionsExtra = "";
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();
autoObject.FinancialDetailsBR.documentAmount = 0.0;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.currency.name = "";
autoObject.FinancialDetailsBR.currency.value = "";
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.collectionAccount.name = "";
autoObject.FinancialDetailsBR.collectionAccount.value = "";
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";
autoObject.FcCollections = new tw.object.FCCollections();
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.currency.name = "";
autoObject.FcCollections.currency.value = "";
autoObject.FcCollections.standardExchangeRate = 0.0;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;
autoObject.FcCollections.fromDate = new TWDate();
autoObject.FcCollections.ToDate = new TWDate();
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.accountNo.name = "";
autoObject.FcCollections.accountNo.value = "";
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.retrievedTransactions[0].currency = "";
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();
autoObject.FcCollections.selectedTransactions[0].accountNo = "";
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;
autoObject.FcCollections.selectedTransactions[0].currency = "";
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;
autoObject.FcCollections.isReversed = false;
autoObject.FcCollections.usedAmount = 0.0;
autoObject.FcCollections.calculatedAmount = 0.0;
autoObject.FcCollections.totalAllocatedAmount = 0.0;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FcCollections.listOfAccounts[0].name = "";
autoObject.FcCollections.listOfAccounts[0].value = "";
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();
autoObject.FinancialDetailsFO.discount = 0.0;
autoObject.FinancialDetailsFO.extraCharges = 0.0;
autoObject.FinancialDetailsFO.ourCharges = 0.0;
autoObject.FinancialDetailsFO.amountSight = 0.0;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;
autoObject.FinancialDetailsFO.referenceNo = "";
autoObject.FinancialDetailsFO.financeApprovalNo = "";
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.FinancialDetailsFO.executionHub.name = "";
autoObject.FinancialDetailsFO.executionHub.value = "";
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;
autoObject.ImporterDetails = new tw.object.ImporterDetails();
autoObject.ImporterDetails.importerName = "";
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.importerCountry.name = "";
autoObject.ImporterDetails.importerCountry.value = "";
autoObject.ImporterDetails.importerAddress = "";
autoObject.ImporterDetails.importerPhoneNo = "";
autoObject.ImporterDetails.bank = "";
autoObject.ImporterDetails.BICCode = "";
autoObject.ImporterDetails.ibanAccount = "";
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ImporterDetails.bankCountry.name = "";
autoObject.ImporterDetails.bankCountry.value = "";
autoObject.ImporterDetails.bankAddress = "";
autoObject.ImporterDetails.bankPhoneNo = "";
autoObject.ImporterDetails.collectingBankReference = "";
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";
autoObject.ProductShipmentDetails.shippingDate = new TWDate();
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ProductShipmentDetails.shipmentMethod.name = "";
autoObject.ProductShipmentDetails.shipmentMethod.value = "";
autoObject.OdcCollection = new tw.object.ODCCollection();
autoObject.OdcCollection.amount = 0.0;
autoObject.OdcCollection.currency = "";
autoObject.OdcCollection.informCADAboutTheCollection = false;
autoObject.ReversalReason = new tw.object.ReversalReason();
autoObject.ReversalReason.reversalReason = "";
autoObject.ReversalReason.closureReason = "";
autoObject.ContractCreation = new tw.object.ContractCreation();
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractCreation.productCode.name = "";
autoObject.ContractCreation.productCode.value = "";
autoObject.ContractCreation.productDescription = "";
autoObject.ContractCreation.Stage = "";
autoObject.ContractCreation.userReference = "";
autoObject.ContractCreation.sourceReference = "";
autoObject.ContractCreation.currency = "";
autoObject.ContractCreation.amount = 0.0;
autoObject.ContractCreation.baseDate = new TWDate();
autoObject.ContractCreation.valueDate = new TWDate();
autoObject.ContractCreation.tenorDays = 0;
autoObject.ContractCreation.transitDays = 0;
autoObject.ContractCreation.maturityDate = new TWDate();
autoObject.Parties = new tw.object.odcParties();
autoObject.Parties.Drawer = new tw.object.Drawer();
autoObject.Parties.Drawer.partyId = "";
autoObject.Parties.Drawer.partyName = "";
autoObject.Parties.Drawer.country = "";
autoObject.Parties.Drawer.Language = "";
autoObject.Parties.Drawer.Reference = "";
autoObject.Parties.Drawer.address1 = "";
autoObject.Parties.Drawer.address2 = "";
autoObject.Parties.Drawer.address3 = "";
autoObject.Parties.Drawee = new tw.object.Drawee();
autoObject.Parties.Drawee.partyId = "";
autoObject.Parties.Drawee.partyName = "";
autoObject.Parties.Drawee.country = "";
autoObject.Parties.Drawee.Language = "";
autoObject.Parties.Drawee.Reference = "";
autoObject.Parties.Drawee.address1 = "";
autoObject.Parties.Drawee.address2 = "";
autoObject.Parties.Drawee.address3 = "";
autoObject.Parties.collectingBank = new tw.object.CollectingBank();
autoObject.Parties.collectingBank.id = "";
autoObject.Parties.collectingBank.name = "";
autoObject.Parties.collectingBank.country = "";
autoObject.Parties.collectingBank.language = "";
autoObject.Parties.collectingBank.reference = "";
autoObject.Parties.collectingBank.address1 = "";
autoObject.Parties.collectingBank.address2 = "";
autoObject.Parties.collectingBank.address3 = "";
autoObject.Parties.collectingBank.cif = "";
autoObject.Parties.collectingBank.media = "";
autoObject.Parties.collectingBank.address = "";
autoObject.Parties.partyTypes = new tw.object.partyTypes();
autoObject.Parties.partyTypes.partyCIF = "";
autoObject.Parties.partyTypes.partyId = "";
autoObject.Parties.partyTypes.partyName = "";
autoObject.Parties.partyTypes.country = "";
autoObject.Parties.partyTypes.language = "";
autoObject.Parties.partyTypes.refrence = "";
autoObject.Parties.partyTypes.address1 = "";
autoObject.Parties.partyTypes.address2 = "";
autoObject.Parties.partyTypes.address3 = "";
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.Parties.partyTypes.partyType.name = "";
autoObject.Parties.partyTypes.partyType.value = "";
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();
autoObject.ChargesAndCommissions[0].component = "";
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;
autoObject.ChargesAndCommissions[0].waiver = false;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;
autoObject.ChargesAndCommissions[0].rateType = "";
autoObject.ChargesAndCommissions[0].description = "";
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();
autoObject.ContractLiquidation.liqAmount = 0.0;
autoObject.ContractLiquidation.liqCurrency = "";
autoObject.ContractLiquidation.debitValueDate = new TWDate();
autoObject.ContractLiquidation.creditValueDate = new TWDate();
autoObject.ContractLiquidation.debitedAccountNo = "";
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";
autoObject.ContractLiquidation.creditedAccount.branchCode = "";
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.ContractLiquidation.creditedAccount.currency.name = "";
autoObject.ContractLiquidation.creditedAccount.currency.value = "";
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;
autoObject.complianceApproval = false;
autoObject.stepLog = new tw.object.StepLog();
autoObject.stepLog.startTime = new TWDate();
autoObject.stepLog.endTime = new TWDate();
autoObject.stepLog.userName = "";
autoObject.stepLog.role = "";
autoObject.stepLog.step = "";
autoObject.stepLog.action = "";
autoObject.stepLog.comment = "";
autoObject.stepLog.terminateReason = "";
autoObject.stepLog.returnReason = "";
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();
autoObject.actions[0] = "";
autoObject.attachmentDetails = new tw.object.attachmentDetails();
autoObject.attachmentDetails.folderID = "";
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();
autoObject.attachmentDetails.ecmProperties.fullPath = "";
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();
autoObject.attachmentDetails.attachment[0].name = "";
autoObject.attachmentDetails.attachment[0].description = "";
autoObject.attachmentDetails.attachment[0].arabicName = "";
autoObject.complianceComments = new tw.object.listOf.StepLog();
autoObject.complianceComments[0] = new tw.object.StepLog();
autoObject.complianceComments[0].startTime = new TWDate();
autoObject.complianceComments[0].endTime = new TWDate();
autoObject.complianceComments[0].userName = "";
autoObject.complianceComments[0].role = "";
autoObject.complianceComments[0].step = "";
autoObject.complianceComments[0].action = "";
autoObject.complianceComments[0].comment = "";
autoObject.complianceComments[0].terminateReason = "";
autoObject.complianceComments[0].returnReason = "";
autoObject.History = new tw.object.listOf.StepLog();
autoObject.History[0] = new tw.object.StepLog();
autoObject.History[0].startTime = new TWDate();
autoObject.History[0].endTime = new TWDate();
autoObject.History[0].userName = "";
autoObject.History[0].role = "";
autoObject.History[0].step = "";
autoObject.History[0].action = "";
autoObject.History[0].comment = "";
autoObject.History[0].terminateReason = "";
autoObject.History[0].returnReason = "";
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();
autoObject.documentSource.name = "";
autoObject.documentSource.value = "";
autoObject.folderID = "";
autoObject.isLiquidated = false;
autoObject</defaultValue>
            <isReadOnly>false</isReadOnly>
            <guid>c8ae64ba-c918-41ea-9219-8efc28f6786d</guid>
            <versionId>9edff5d8-2eaf-42a4-bf97-7d75328a5f60</versionId>
        </bpdParameter>
        <bpdParameter name="routingDetails">
            <lastModified isNull="true" />
            <lastModifiedBy isNull="true" />
            <bpdParameterId>2007.684c7152-3e6b-4505-8f37-4ed21a611236</bpdParameterId>
            <bpdId>25.933ae173-470f-4c1f-8a2b-6521da495a38</bpdId>
            <parameterType>1</parameterType>
            <isArrayOf>false</isArrayOf>
            <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
            <seq>1</seq>
            <documentation></documentation>
            <hasDefault>false</hasDefault>
            <defaultValue isNull="true" />
            <isReadOnly>false</isReadOnly>
            <guid>2dc15982-92f8-448b-841e-85594042843c</guid>
            <versionId>76c1cd5f-993d-45d9-bcf4-2b598cf4c7b2</versionId>
        </bpdParameter>
        <lastModified>*************</lastModified>
        <lastModifiedBy>eslam1</lastModifiedBy>
        <bpdId>25.933ae173-470f-4c1f-8a2b-6521da495a38</bpdId>
        <isTrackingEnabled>true</isTrackingEnabled>
        <isSpcEnabled>false</isSpcEnabled>
        <restrictedName isNull="true" />
        <isCriticalPathEnabled>false</isCriticalPathEnabled>
        <participantRef isNull="true" />
        <businessDataParticipantRef isNull="true" />
        <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</perfMetricParticipantRef>
        <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ownerTeamParticipantRef>
        <timeScheduleType isNull="true" />
        <timeScheduleName>NBEWork</timeScheduleName>
        <timeScheduleExpression isNull="true" />
        <holidayScheduleType isNull="true" />
        <holidayScheduleName>NBEHoliday</holidayScheduleName>
        <holidayScheduleExpression isNull="true" />
        <timezoneType isNull="true" />
        <timezone>Egypt</timezone>
        <timezoneExpression isNull="true" />
        <internalName isNull="true" />
        <description></description>
        <type>1</type>
        <rootBpdId isNull="true" />
        <parentBpdId isNull="true" />
        <parentFlowObjectId isNull="true" />
        <xmlData isNull="true" />
        <bpmn2Data>&lt;ns15:definitions xmlns:ns15="http://www.omg.org/spec/BPMN/20100524/MODEL" xmlns:ns2="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/trackinggroup" xmlns:ns3="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process" xmlns:ns4="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/wle" xmlns:ns5="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/case" xmlns:ns6="http://www.ibm.com/bpm/Extensions" xmlns:ns7="http://www.ibm.com/xmlns/prod/bpm/uca" xmlns:ns8="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/team" xmlns:ns9="http://www.ibm.com/xmlns/bpmnx/20100524/v1/BusinessVocabulary" xmlns:ns10="http://www.omg.org/spec/DD/20100524/DI" xmlns:ns11="http://www.omg.org/spec/DD/20100524/DC" xmlns:ns12="http://www.omg.org/spec/BPMN/20100524/BPMNDI" xmlns:ns13="http://www.ibm.com/xmlns/prod/bpm/graph" xmlns:ns14="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/extservice" xmlns:ns16="http://www.ibm.com/bpm/processappsettings" xmlns:ns17="http://www.ibm.com/xmlns/prod/bpm/bpmn/ext/process/auth" xmlns:ns18="http://www.ibm.com/bpm/CoachDesignerNG" xmlns:ns19="http://www.ibm.com/xmlns/links" xmlns:ns20="http://www.ibm.com/bpm/uitheme" xmlns:ns21="http://www.ibm.com/bpm/coachview" xmlns:ns22="http://www.ibm.com/xmlns/tagging" id="fe1ac9f6-647e-47c2-9e22-608f9d73ef7a" targetNamespace="" expressionLanguage="http://www.ibm.com/xmlns/prod/bpm/expression-lang/javascript"&gt;&lt;ns15:process name="ODC Reversal اعادة قيد تحصيل مستندى تصدير" id="25.933ae173-470f-4c1f-8a2b-6521da495a38" ns3:executionMode="longRunning"&gt;&lt;ns15:documentation /&gt;&lt;ns15:extensionElements&gt;&lt;ns4:bpdExtension instanceName="&amp;quot; Request Type: &amp;quot;+ tw.local.odcRequest.requestType.name  +&amp;quot; , &amp;quot;+&amp;quot; CIF: &amp;quot;+ tw.local.odcRequest.cif +&amp;quot; , &amp;quot; +&amp;quot;Request Number: &amp;quot; +tw.system.process.instanceId" dueDateEnabled="false" atRiskCalcEnabled="false" enableTracking="false" allowProjectedPathManagement="false" optimizeExecForLatency="false" sBOSyncEnabled="true" allowContentOperations="false" autoTrackingEnabled="false" autoTrackingName="at1693545399110"&gt;&lt;ns4:dueDateSettings type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;8&lt;/ns4:dueDate&gt;&lt;/ns4:dueDateSettings&gt;&lt;ns4:workSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:timezone&gt;Egypt&lt;/ns4:timezone&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:workSchedule&gt;&lt;/ns4:bpdExtension&gt;&lt;ns5:caseExtension&gt;&lt;ns5:caseFolder id="5aba4da2-4484-4810-835b-d5873189e7d7" /&gt;&lt;/ns5:caseExtension&gt;&lt;ns5:isConvergedProcess&gt;true&lt;/ns5:isConvergedProcess&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:ioSpecification&gt;&lt;ns15:extensionElements&gt;&lt;ns3:epvProcessLinks&gt;&lt;ns3:epvProcessLinkRef epvId="21.daf76aa9-3be6-432b-b228-01c27b3012d3" epvProcessLinkId="1e68ed8f-13f7-4cb5-806c-8d8636ef2b03" /&gt;&lt;ns3:epvProcessLinkRef epvId="21.dbbaa047-8f02-4397-b1b5-41f11b0256b3" epvProcessLinkId="014acb69-59a8-45e7-822c-05953bccc3e0" /&gt;&lt;/ns3:epvProcessLinks&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:dataInput name="odcRequest" itemSubjectRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9" isCollection="false" id="2007.86f87789-824f-4930-8c19-5a719284c659"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:searchableField id="7b6a22dc-233a-4ecd-8cbb-ee6921af4adc" alias="InitiatorHubBranch" path=".initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="f8755e6e-8272-431b-82c8-e9f1e7ed15ce" alias="parentRequestNumber" path=".parentRequestNo" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="7eb6d0f9-71e2-4496-86ce-d833141978e6" alias="requestDate" path=".requestDate" type="12.68474ab0-d56f-47ee-b7e9-510b45a2a8be" /&gt;&lt;ns4:searchableField id="a339ecd6-4974-4376-8f65-0fcf632e8222" alias="requestStatus" path=".appInfo.status" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="cbf1ac69-8dd1-4ef4-8633-6640a9052e85" alias="InitiatorUserName" path=".appInfo.initiator" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="e19ec9a2-b0e8-430d-89e9-a631fd4af208" alias="customerCIF" path=".CustomerInfo.cif" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="83c8ec89-336a-4889-8831-2db11de97e8d" alias="customerName" path=".customerName" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns4:searchableField id="891a5bad-505c-4409-8a7d-e7068726a11a" alias="customerName_1" path=".CustomerInfo.customerName" type="12.db884a3c-c533-44b7-bb2d-47bec8ad4022" /&gt;&lt;ns3:defaultValue useDefault="false"&gt;var autoObject = new tw.object.ODCRequest();&#xD;
autoObject.initiator = "";&#xD;
autoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.requestNature.name = "";&#xD;
autoObject.requestNature.value = "";&#xD;
autoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.requestType.name = "";&#xD;
autoObject.requestType.value = "";&#xD;
autoObject.cif = "";&#xD;
autoObject.customerName = "";&#xD;
autoObject.parentRequestNo = "";&#xD;
autoObject.requestDate = new TWDate();&#xD;
autoObject.ImporterName = "";&#xD;
autoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();&#xD;
autoObject.appInfo.requestDate = "";&#xD;
autoObject.appInfo.status = "";&#xD;
autoObject.appInfo.subStatus = "";&#xD;
autoObject.appInfo.initiator = "";&#xD;
autoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.appInfo.branch.name = "";&#xD;
autoObject.appInfo.branch.value = "";&#xD;
autoObject.appInfo.requestName = "";&#xD;
autoObject.appInfo.requestType = "";&#xD;
autoObject.appInfo.stepName = "";&#xD;
autoObject.appInfo.appRef = "";&#xD;
autoObject.appInfo.appID = "";&#xD;
autoObject.appInfo.instanceID = "";&#xD;
autoObject.CustomerInfo = new tw.object.CustomerInfo();&#xD;
autoObject.CustomerInfo.cif = "";&#xD;
autoObject.CustomerInfo.customerName = "";&#xD;
autoObject.CustomerInfo.addressLine1 = "";&#xD;
autoObject.CustomerInfo.addressLine2 = "";&#xD;
autoObject.CustomerInfo.addressLine3 = "";&#xD;
autoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.CustomerInfo.customerSector.name = "";&#xD;
autoObject.CustomerInfo.customerSector.value = "";&#xD;
autoObject.CustomerInfo.customerType = "";&#xD;
autoObject.CustomerInfo.customerNoCBE = "";&#xD;
autoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.CustomerInfo.facilityType.name = "";&#xD;
autoObject.CustomerInfo.facilityType.value = "";&#xD;
autoObject.CustomerInfo.commercialRegistrationNo = "";&#xD;
autoObject.CustomerInfo.commercialRegistrationOffice = "";&#xD;
autoObject.CustomerInfo.taxCardNo = "";&#xD;
autoObject.CustomerInfo.importCardNo = "";&#xD;
autoObject.CustomerInfo.initiationHub = "";&#xD;
autoObject.CustomerInfo.country = "";&#xD;
autoObject.BasicDetails = new tw.object.BasicDetails();&#xD;
autoObject.BasicDetails.requestNature = "";&#xD;
autoObject.BasicDetails.requestType = "";&#xD;
autoObject.BasicDetails.parentRequestNo = "";&#xD;
autoObject.BasicDetails.requestState = "";&#xD;
autoObject.BasicDetails.flexCubeContractNo = "";&#xD;
autoObject.BasicDetails.contractStage = "";&#xD;
autoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.exportPurpose.name = "";&#xD;
autoObject.BasicDetails.exportPurpose.value = "";&#xD;
autoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.paymentTerms.name = "";&#xD;
autoObject.BasicDetails.paymentTerms.value = "";&#xD;
autoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.productCategory.name = "";&#xD;
autoObject.BasicDetails.productCategory.value = "";&#xD;
autoObject.BasicDetails.commodityDescription = "";&#xD;
autoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.BasicDetails.CountryOfOrigin.name = "";&#xD;
autoObject.BasicDetails.CountryOfOrigin.value = "";&#xD;
autoObject.BasicDetails.Bills = new tw.object.listOf.Bills();&#xD;
autoObject.BasicDetails.Bills[0] = new tw.object.Bills();&#xD;
autoObject.BasicDetails.Bills[0].billOfLadingRef = "";&#xD;
autoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();&#xD;
autoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();&#xD;
autoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();&#xD;
autoObject.BasicDetails.Invoice[0].invoiceNo = "";&#xD;
autoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();&#xD;
autoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();&#xD;
autoObject.GeneratedDocumentInfo.customerName = "";&#xD;
autoObject.GeneratedDocumentInfo.customerAddress = "";&#xD;
autoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.deliveryTerms[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.deliveryTermsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.paymentInstructions[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.paymentInstructionsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.Instructions[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.InstructionsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.GeneratedDocumentInfo.specialInstructions[0] = "";&#xD;
autoObject.GeneratedDocumentInfo.specialInstructionsExtra = "";&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetter = false;&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = "";&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = "";&#xD;
autoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = "";&#xD;
autoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();&#xD;
autoObject.FinancialDetailsBR.documentAmount = 0.0;&#xD;
autoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.currency.name = "";&#xD;
autoObject.FinancialDetailsBR.currency.value = "";&#xD;
autoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();&#xD;
autoObject.FinancialDetailsBR.amountAdvanced = 0.0;&#xD;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = "";&#xD;
autoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = "";&#xD;
autoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.collectionAccount.name = "";&#xD;
autoObject.FinancialDetailsBR.collectionAccount.value = "";&#xD;
autoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsBR.listOfAccounts[0].name = "";&#xD;
autoObject.FinancialDetailsBR.listOfAccounts[0].value = "";&#xD;
autoObject.FcCollections = new tw.object.FCCollections();&#xD;
autoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.currency.name = "";&#xD;
autoObject.FcCollections.currency.value = "";&#xD;
autoObject.FcCollections.standardExchangeRate = 0.0;&#xD;
autoObject.FcCollections.negotiatedExchangeRate = 0.0;&#xD;
autoObject.FcCollections.fromDate = new TWDate();&#xD;
autoObject.FcCollections.ToDate = new TWDate();&#xD;
autoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.accountNo.name = "";&#xD;
autoObject.FcCollections.accountNo.value = "";&#xD;
autoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();&#xD;
autoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();&#xD;
autoObject.FcCollections.retrievedTransactions[0].accountNo = "";&#xD;
autoObject.FcCollections.retrievedTransactions[0].referenceNumber = "";&#xD;
autoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();&#xD;
autoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();&#xD;
autoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;&#xD;
autoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;&#xD;
autoObject.FcCollections.retrievedTransactions[0].currency = "";&#xD;
autoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
autoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();&#xD;
autoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();&#xD;
autoObject.FcCollections.selectedTransactions[0].accountNo = "";&#xD;
autoObject.FcCollections.selectedTransactions[0].referenceNumber = "";&#xD;
autoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();&#xD;
autoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();&#xD;
autoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions[0].currency = "";&#xD;
autoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;&#xD;
autoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;&#xD;
autoObject.FcCollections.isReversed = false;&#xD;
autoObject.FcCollections.usedAmount = 0.0;&#xD;
autoObject.FcCollections.calculatedAmount = 0.0;&#xD;
autoObject.FcCollections.totalAllocatedAmount = 0.0;&#xD;
autoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FcCollections.listOfAccounts[0].name = "";&#xD;
autoObject.FcCollections.listOfAccounts[0].value = "";&#xD;
autoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();&#xD;
autoObject.FinancialDetailsFO.discount = 0.0;&#xD;
autoObject.FinancialDetailsFO.extraCharges = 0.0;&#xD;
autoObject.FinancialDetailsFO.ourCharges = 0.0;&#xD;
autoObject.FinancialDetailsFO.amountSight = 0.0;&#xD;
autoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;&#xD;
autoObject.FinancialDetailsFO.amountDefAvalization = 0.0;&#xD;
autoObject.FinancialDetailsFO.collectableAmount = 0.0;&#xD;
autoObject.FinancialDetailsFO.outstandingAmount = 0.0;&#xD;
autoObject.FinancialDetailsFO.maturityDate = new TWDate();&#xD;
autoObject.FinancialDetailsFO.noOfDaysMaturity = 0;&#xD;
autoObject.FinancialDetailsFO.referenceNo = "";&#xD;
autoObject.FinancialDetailsFO.financeApprovalNo = "";&#xD;
autoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.FinancialDetailsFO.executionHub.name = "";&#xD;
autoObject.FinancialDetailsFO.executionHub.value = "";&#xD;
autoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();&#xD;
autoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();&#xD;
autoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();&#xD;
autoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;&#xD;
autoObject.ImporterDetails = new tw.object.ImporterDetails();&#xD;
autoObject.ImporterDetails.importerName = "";&#xD;
autoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ImporterDetails.importerCountry.name = "";&#xD;
autoObject.ImporterDetails.importerCountry.value = "";&#xD;
autoObject.ImporterDetails.importerAddress = "";&#xD;
autoObject.ImporterDetails.importerPhoneNo = "";&#xD;
autoObject.ImporterDetails.bank = "";&#xD;
autoObject.ImporterDetails.BICCode = "";&#xD;
autoObject.ImporterDetails.ibanAccount = "";&#xD;
autoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ImporterDetails.bankCountry.name = "";&#xD;
autoObject.ImporterDetails.bankCountry.value = "";&#xD;
autoObject.ImporterDetails.bankAddress = "";&#xD;
autoObject.ImporterDetails.bankPhoneNo = "";&#xD;
autoObject.ImporterDetails.collectingBankReference = "";&#xD;
autoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();&#xD;
autoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ProductShipmentDetails.CBECommodityClassification.name = "";&#xD;
autoObject.ProductShipmentDetails.CBECommodityClassification.value = "";&#xD;
autoObject.ProductShipmentDetails.shippingDate = new TWDate();&#xD;
autoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ProductShipmentDetails.shipmentMethod.name = "";&#xD;
autoObject.ProductShipmentDetails.shipmentMethod.value = "";&#xD;
autoObject.OdcCollection = new tw.object.ODCCollection();&#xD;
autoObject.OdcCollection.amount = 0.0;&#xD;
autoObject.OdcCollection.currency = "";&#xD;
autoObject.OdcCollection.informCADAboutTheCollection = false;&#xD;
autoObject.ReversalReason = new tw.object.ReversalReason();&#xD;
autoObject.ReversalReason.reversalReason = "";&#xD;
autoObject.ReversalReason.closureReason = "";&#xD;
autoObject.ContractCreation = new tw.object.ContractCreation();&#xD;
autoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ContractCreation.productCode.name = "";&#xD;
autoObject.ContractCreation.productCode.value = "";&#xD;
autoObject.ContractCreation.productDescription = "";&#xD;
autoObject.ContractCreation.Stage = "";&#xD;
autoObject.ContractCreation.userReference = "";&#xD;
autoObject.ContractCreation.sourceReference = "";&#xD;
autoObject.ContractCreation.currency = "";&#xD;
autoObject.ContractCreation.amount = 0.0;&#xD;
autoObject.ContractCreation.baseDate = new TWDate();&#xD;
autoObject.ContractCreation.valueDate = new TWDate();&#xD;
autoObject.ContractCreation.tenorDays = 0;&#xD;
autoObject.ContractCreation.transitDays = 0;&#xD;
autoObject.ContractCreation.maturityDate = new TWDate();&#xD;
autoObject.Parties = new tw.object.odcParties();&#xD;
autoObject.Parties.Drawer = new tw.object.Drawer();&#xD;
autoObject.Parties.Drawer.partyId = "";&#xD;
autoObject.Parties.Drawer.partyName = "";&#xD;
autoObject.Parties.Drawer.country = "";&#xD;
autoObject.Parties.Drawer.Language = "";&#xD;
autoObject.Parties.Drawer.Reference = "";&#xD;
autoObject.Parties.Drawer.address1 = "";&#xD;
autoObject.Parties.Drawer.address2 = "";&#xD;
autoObject.Parties.Drawer.address3 = "";&#xD;
autoObject.Parties.Drawee = new tw.object.Drawee();&#xD;
autoObject.Parties.Drawee.partyId = "";&#xD;
autoObject.Parties.Drawee.partyName = "";&#xD;
autoObject.Parties.Drawee.country = "";&#xD;
autoObject.Parties.Drawee.Language = "";&#xD;
autoObject.Parties.Drawee.Reference = "";&#xD;
autoObject.Parties.Drawee.address1 = "";&#xD;
autoObject.Parties.Drawee.address2 = "";&#xD;
autoObject.Parties.Drawee.address3 = "";&#xD;
autoObject.Parties.collectingBank = new tw.object.CollectingBank();&#xD;
autoObject.Parties.collectingBank.id = "";&#xD;
autoObject.Parties.collectingBank.name = "";&#xD;
autoObject.Parties.collectingBank.country = "";&#xD;
autoObject.Parties.collectingBank.language = "";&#xD;
autoObject.Parties.collectingBank.reference = "";&#xD;
autoObject.Parties.collectingBank.address1 = "";&#xD;
autoObject.Parties.collectingBank.address2 = "";&#xD;
autoObject.Parties.collectingBank.address3 = "";&#xD;
autoObject.Parties.collectingBank.cif = "";&#xD;
autoObject.Parties.collectingBank.media = "";&#xD;
autoObject.Parties.collectingBank.address = "";&#xD;
autoObject.Parties.partyTypes = new tw.object.partyTypes();&#xD;
autoObject.Parties.partyTypes.partyCIF = "";&#xD;
autoObject.Parties.partyTypes.partyId = "";&#xD;
autoObject.Parties.partyTypes.partyName = "";&#xD;
autoObject.Parties.partyTypes.country = "";&#xD;
autoObject.Parties.partyTypes.language = "";&#xD;
autoObject.Parties.partyTypes.refrence = "";&#xD;
autoObject.Parties.partyTypes.address1 = "";&#xD;
autoObject.Parties.partyTypes.address2 = "";&#xD;
autoObject.Parties.partyTypes.address3 = "";&#xD;
autoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.Parties.partyTypes.partyType.name = "";&#xD;
autoObject.Parties.partyTypes.partyType.value = "";&#xD;
autoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();&#xD;
autoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();&#xD;
autoObject.ChargesAndCommissions[0].component = "";&#xD;
autoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ChargesAndCommissions[0].defaultCurrency.name = "";&#xD;
autoObject.ChargesAndCommissions[0].defaultCurrency.value = "";&#xD;
autoObject.ChargesAndCommissions[0].changeAmount = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].waiver = false;&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.branchCode = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.currency.name = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.currency.value = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = "";&#xD;
autoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].rateType = "";&#xD;
autoObject.ChargesAndCommissions[0].description = "";&#xD;
autoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].changePercentage = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].defaultAmount = 0.0;&#xD;
autoObject.ChargesAndCommissions[0].basicAmountCurrency = "";&#xD;
autoObject.ChargesAndCommissions[0].flatAmount = 0.0;&#xD;
autoObject.ContractLiquidation = new tw.object.ContractLiquidation();&#xD;
autoObject.ContractLiquidation.liqAmount = 0.0;&#xD;
autoObject.ContractLiquidation.liqCurrency = "";&#xD;
autoObject.ContractLiquidation.debitValueDate = new TWDate();&#xD;
autoObject.ContractLiquidation.creditValueDate = new TWDate();&#xD;
autoObject.ContractLiquidation.debitedAccountNo = "";&#xD;
autoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();&#xD;
autoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ContractLiquidation.creditedAccount.accountClass.name = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.accountClass.value = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.glAccountNo = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.customerAccountNo = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.branchCode = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.ContractLiquidation.creditedAccount.currency.name = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.currency.value = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.balance = 0.0;&#xD;
autoObject.ContractLiquidation.creditedAccount.balanceSign = "";&#xD;
autoObject.ContractLiquidation.creditedAccount.isOverDraft = false;&#xD;
autoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();&#xD;
autoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;&#xD;
autoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;&#xD;
autoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;&#xD;
autoObject.complianceApproval = false;&#xD;
autoObject.stepLog = new tw.object.StepLog();&#xD;
autoObject.stepLog.startTime = new TWDate();&#xD;
autoObject.stepLog.endTime = new TWDate();&#xD;
autoObject.stepLog.userName = "";&#xD;
autoObject.stepLog.role = "";&#xD;
autoObject.stepLog.step = "";&#xD;
autoObject.stepLog.action = "";&#xD;
autoObject.stepLog.comment = "";&#xD;
autoObject.stepLog.terminateReason = "";&#xD;
autoObject.stepLog.returnReason = "";&#xD;
autoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();&#xD;
autoObject.actions[0] = "";&#xD;
autoObject.attachmentDetails = new tw.object.attachmentDetails();&#xD;
autoObject.attachmentDetails.folderID = "";&#xD;
autoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();&#xD;
autoObject.attachmentDetails.ecmProperties.fullPath = "";&#xD;
autoObject.attachmentDetails.ecmProperties.cmisQuery = "";&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = "";&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;&#xD;
autoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;&#xD;
autoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();&#xD;
autoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();&#xD;
autoObject.attachmentDetails.attachment[0].name = "";&#xD;
autoObject.attachmentDetails.attachment[0].description = "";&#xD;
autoObject.attachmentDetails.attachment[0].arabicName = "";&#xD;
autoObject.complianceComments = new tw.object.listOf.StepLog();&#xD;
autoObject.complianceComments[0] = new tw.object.StepLog();&#xD;
autoObject.complianceComments[0].startTime = new TWDate();&#xD;
autoObject.complianceComments[0].endTime = new TWDate();&#xD;
autoObject.complianceComments[0].userName = "";&#xD;
autoObject.complianceComments[0].role = "";&#xD;
autoObject.complianceComments[0].step = "";&#xD;
autoObject.complianceComments[0].action = "";&#xD;
autoObject.complianceComments[0].comment = "";&#xD;
autoObject.complianceComments[0].terminateReason = "";&#xD;
autoObject.complianceComments[0].returnReason = "";&#xD;
autoObject.History = new tw.object.listOf.StepLog();&#xD;
autoObject.History[0] = new tw.object.StepLog();&#xD;
autoObject.History[0].startTime = new TWDate();&#xD;
autoObject.History[0].endTime = new TWDate();&#xD;
autoObject.History[0].userName = "";&#xD;
autoObject.History[0].role = "";&#xD;
autoObject.History[0].step = "";&#xD;
autoObject.History[0].action = "";&#xD;
autoObject.History[0].comment = "";&#xD;
autoObject.History[0].terminateReason = "";&#xD;
autoObject.History[0].returnReason = "";&#xD;
autoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();&#xD;
autoObject.documentSource.name = "";&#xD;
autoObject.documentSource.value = "";&#xD;
autoObject.folderID = "";&#xD;
autoObject.isLiquidated = false;&#xD;
autoObject&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataInput&gt;&lt;ns15:dataInput name="routingDetails" itemSubjectRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727" isCollection="false" id="2007.684c7152-3e6b-4505-8f37-4ed21a611236" /&gt;&lt;ns15:inputSet /&gt;&lt;ns15:inputSet id="_33e6e276-9f24-47a5-8f75-c1819ec07f1d" /&gt;&lt;ns15:outputSet /&gt;&lt;ns15:outputSet id="_5b5b1b2e-cab6-4f6f-96d2-c0d0f82c82e8" /&gt;&lt;/ns15:ioSpecification&gt;&lt;ns15:laneSet id="320b2a03-2ef6-48f6-9980-49703310afb3"&gt;&lt;ns15:lane name="Hub / center Maker" partitionElementRef="24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce" id="2e4a3c95-9839-4c37-870f-b0215a22597b" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="0" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;4d00b4e6-b345-483f-af99-b6815f8ebb6f&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;cfc9c782-1e15-4918-8597-81c8a226db13&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;c43302a8-b1fa-4fa9-805b-d2abed461cf9&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="Hub / center Checker" partitionElementRef="24.8e005024-3fe0-4848-8c3c-f1e9483900c6" id="7e6e9014-5f8d-44eb-8ceb-ecba6505368c" ns4:isSystemLane="false"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="201" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;204734d8-ab45-42fa-8dde-9f2d35136f07&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;a66b788f-eb9d-4662-8d5a-15e1618e166e&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;6869d68e-0a73-474c-a9da-f7c788cf4d26&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;60aa4589-527a-4330-8c62-e27d874075b2&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;462fbe06-d514-4387-8592-2acd0f6a0a5f&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;ns15:lane name="System" partitionElementRef="24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b" id="9180d59b-6f02-4dad-a33f-d017ed000296" ns4:isSystemLane="true"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="0" y="402" width="3000" height="200" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:flowNodeRef&gt;956dcbec-d5f4-4a3d-8f90-86bed3f21e33&lt;/ns15:flowNodeRef&gt;&lt;ns15:flowNodeRef&gt;c8b8dc76-b1ba-4d4e-8653-ea6dc4248654&lt;/ns15:flowNodeRef&gt;&lt;/ns15:lane&gt;&lt;/ns15:laneSet&gt;&lt;ns15:startEvent name="Start" id="4d00b4e6-b345-483f-af99-b6815f8ebb6f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="39" y="82" width="24" height="24" color="#F8F8F8" /&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;true&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:default&gt;8adc5346-4dc4-48ea-88bc-847fe8c3e8bb&lt;/ns3:default&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;8adc5346-4dc4-48ea-88bc-847fe8c3e8bb&lt;/ns15:outgoing&gt;&lt;/ns15:startEvent&gt;&lt;ns15:endEvent name="Approve" id="6869d68e-0a73-474c-a9da-f7c788cf4d26"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="728" y="128" width="24" height="24" color="#F8F8F8" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:callActivity calledElement="1.b9bf1811-6e1e-41a3-828b-39059fa168cb" default="1d1902fe-e246-4af8-843c-92b62fbe98d3" name="ACT01 Create ODC Reversal Request" id="cfc9c782-1e15-4918-8597-81c8a226db13"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="338" y="59" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Create ODC Reversal Request –  إنشاء طلب اعادة قيد تحصيل مستندى تصدير &amp;lt;#= tw.system.process.instanceId #&amp;gt;&lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.RACT01)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;false&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;cc8203a1-7910-4b2f-8009-0eeb7a8e3387&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;8adc5346-4dc4-48ea-88bc-847fe8c3e8bb&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;1d1902fe-e246-4af8-843c-92b62fbe98d3&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.602aae62-b023-4b72-8c17-f498f81897a8&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2"&gt;tw.local.isFirstTime&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.0f07a9fc-f9d5-4131-8028-34ad2f6c6eed&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.system.user.fullName&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.46373b11-9f39-48b9-8787-37bd5d89f78a&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727"&gt;tw.local.routingDetails&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.1bba753e-260d-4827-8632-e837d4c62da9&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="LastUser" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.302e4a54-16ab-47b9-9b70-d933329c72e7"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_ODC_HUB_"+tw.local.routingDetails.hubCode+"_CU_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_MKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:callActivity calledElement="1.c71c7233-23ac-4e5f-a241-09cd96502615" default="d1c41520-7c04-4ae2-8052-46fb397d3f2a" name="ACT02 Review ODC Reversal Request" id="204734d8-ab45-42fa-8dde-9f2d35136f07"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;ns13:nodeVisualInfo x="338" y="45" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:subject&gt;Review ODC Reversal Request – مراجعة طلب اعادة قيد تحصيل مستندى تصدير &lt;/ns4:subject&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;parseFloat(tw.epv.ODCCreationSLA.RACT02)&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;Africa/Cairo&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timeScheduleName&gt;NBEWork&lt;/ns4:timeScheduleName&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;ns4:holidayScheduleName&gt;NBEHoliday&lt;/ns4:holidayScheduleName&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;UserTask&lt;/ns4:activityType&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;1d1902fe-e246-4af8-843c-92b62fbe98d3&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;d1c41520-7c04-4ae2-8052-46fb397d3f2a&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.a82241b6-421c-44f4-83f7-f6d3d3d3a024&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.8b489eb0-ce3f-46d6-8896-60fbb2cd2288&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.lastAction&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.34b891a3-f943-4cf2-8753-3a356f300282&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.parentPath&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataOutputAssociation&gt;&lt;ns15:sourceRef&gt;2055.d49e3545-463a-4eb4-8273-1e61d066f668&lt;/ns15:sourceRef&gt;&lt;ns15:assignment&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:to&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataOutputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService serviceRef="1.302e4a54-16ab-47b9-9b70-d933329c72e7"&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.b95f0910-f183-4dab-9065-28297a14ceef&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_ODC_HUB_"+tw.local.routingDetails.hubCode+"_CU_CHKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.routingDetails.hubCode&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_CHKR"&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;/ns4:teamFilterService&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:sequenceFlow sourceRef="cfc9c782-1e15-4918-8597-81c8a226db13" targetRef="204734d8-ab45-42fa-8dde-9f2d35136f07" name="To ACT02 Review ODC Reversal Request" id="1d1902fe-e246-4af8-843c-92b62fbe98d3"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:exclusiveGateway default="cc8203a1-7910-4b2f-8009-0eeb7a8e3387" name="Checker Decision Action" id="a66b788f-eb9d-4662-8d5a-15e1618e166e"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="532" y="64" width="32" height="32" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;d1c41520-7c04-4ae2-8052-46fb397d3f2a&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;45efef31-a3f7-48df-8e5f-55d0bd5a1a21&lt;/ns15:outgoing&gt;&lt;ns15:outgoing&gt;cc8203a1-7910-4b2f-8009-0eeb7a8e3387&lt;/ns15:outgoing&gt;&lt;/ns15:exclusiveGateway&gt;&lt;ns15:sequenceFlow sourceRef="204734d8-ab45-42fa-8dde-9f2d35136f07" targetRef="a66b788f-eb9d-4662-8d5a-15e1618e166e" name="To Checker Decision Action" id="d1c41520-7c04-4ae2-8052-46fb397d3f2a"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="Cancel" id="60aa4589-527a-4330-8c62-e27d874075b2"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="729" y="67" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;45efef31-a3f7-48df-8e5f-55d0bd5a1a21&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="a66b788f-eb9d-4662-8d5a-15e1618e166e" targetRef="60aa4589-527a-4330-8c62-e27d874075b2" name="To Cancel" id="45efef31-a3f7-48df-8e5f-55d0bd5a1a21"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.cancelRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="a66b788f-eb9d-4662-8d5a-15e1618e166e" targetRef="6869d68e-0a73-474c-a9da-f7c788cf4d26" name="To Approve" id="8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.approveRequest&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="a66b788f-eb9d-4662-8d5a-15e1618e166e" targetRef="cfc9c782-1e15-4918-8597-81c8a226db13" name="Back To initiator" id="cc8203a1-7910-4b2f-8009-0eeb7a8e3387"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;topCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;rightCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;true&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:conditionExpression xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.&lt;/ns15:conditionExpression&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2" isCollection="false" name="isFirstTime" id="862e24d6-e072-4653-8abd-681972b119f0"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:defaultValue useDefault="true"&gt;true&lt;/ns3:defaultValue&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:dataObject&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="lastAction" id="159f0712-06fa-4aa8-8a5d-bef2234b0a9f" /&gt;&lt;ns15:sequenceFlow sourceRef="4d00b4e6-b345-483f-af99-b6815f8ebb6f" targetRef="cfc9c782-1e15-4918-8597-81c8a226db13" name="To ODC Initiators" id="8adc5346-4dc4-48ea-88bc-847fe8c3e8bb"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:callActivity calledElement="1.d7acf968-6740-4e52-b037-2049466eeeb2" default="949eda48-8282-4104-8add-a859c1fa92f3" name="Send Escalation mail" id="956dcbec-d5f4-4a3d-8f90-86bed3f21e33"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:deleteTaskOnCompletion&gt;true&lt;/ns4:deleteTaskOnCompletion&gt;&lt;ns13:nodeVisualInfo x="378" y="22" width="95" height="70" /&gt;&lt;ns4:userTaskSettings&gt;&lt;ns4:activityPriority type="Priority"&gt;&lt;ns4:priority&gt;Normal&lt;/ns4:priority&gt;&lt;/ns4:activityPriority&gt;&lt;ns4:activityDueDate type="TimeCalculation"&gt;&lt;ns4:dueDate unit="Hours" timeOfDay="00:00"&gt;1&lt;/ns4:dueDate&gt;&lt;ns4:timeZone type="TimeZone"&gt;&lt;ns4:value&gt;(use default)&lt;/ns4:value&gt;&lt;/ns4:timeZone&gt;&lt;/ns4:activityDueDate&gt;&lt;ns4:activityAssignmentType&gt;Lane&lt;/ns4:activityAssignmentType&gt;&lt;ns4:activityWorkSchedule&gt;&lt;ns4:timeScheduleType&gt;0&lt;/ns4:timeScheduleType&gt;&lt;ns4:timezoneType&gt;0&lt;/ns4:timezoneType&gt;&lt;ns4:holidayScheduleType&gt;0&lt;/ns4:holidayScheduleType&gt;&lt;/ns4:activityWorkSchedule&gt;&lt;/ns4:userTaskSettings&gt;&lt;ns4:activityType&gt;ServiceTask&lt;/ns4:activityType&gt;&lt;ns4:activityExtension conditional="false"&gt;&lt;ns4:conditionScript /&gt;&lt;/ns4:activityExtension&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;f88a6f6e-d92d-4874-8501-b54942aec83d&lt;/ns15:incoming&gt;&lt;ns15:incoming&gt;f2324e42-816f-420c-8247-62c22a708d72&lt;/ns15:incoming&gt;&lt;ns15:outgoing&gt;949eda48-8282-4104-8add-a859c1fa92f3&lt;/ns15:outgoing&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9"&gt;tw.local.odcRequest&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.taskId&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns15:dataInputAssociation&gt;&lt;ns15:targetRef&gt;2055.9771d7e8-ca59-430e-8b1a-194cf04c1182&lt;/ns15:targetRef&gt;&lt;ns15:assignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression" evaluatesToTypeRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;/ns15:assignment&gt;&lt;/ns15:dataInputAssociation&gt;&lt;ns4:activityPerformer distribution="None" name="Lane"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns4:activityPerformer distribution="None" name="Team"&gt;&lt;ns4:teamFilterService /&gt;&lt;/ns4:activityPerformer&gt;&lt;ns15:performer name="Expert" /&gt;&lt;/ns15:callActivity&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="taskId" id="db3fa75c-e63e-443f-8487-98f335bea12e" /&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="mailTo" id="4e5d0b49-dad8-4560-8d99-82f26b0daeb8" /&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="cfc9c782-1e15-4918-8597-81c8a226db13" parallelMultiple="false" name="Boundary Event1" id="c43302a8-b1fa-4fa9-805b-d2abed461cf9"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="347" y="117" width="24" height="24" /&gt;&lt;ns3:default&gt;f88a6f6e-d92d-4874-8501-b54942aec83d&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskId&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;f88a6f6e-d92d-4874-8501-b54942aec83d&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="cb3ec0ad-00f1-4b8c-8e62-ac8b2c7b2945" eventImplId="cfbc88db-07d4-47c8-816b-abf2a1695f2f"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:boundaryEvent cancelActivity="false" attachedToRef="204734d8-ab45-42fa-8dde-9f2d35136f07" parallelMultiple="false" name="Boundary Event2" id="462fbe06-d514-4387-8592-2acd0f6a0a5f"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="373" y="103" width="24" height="24" /&gt;&lt;ns3:default&gt;f2324e42-816f-420c-8247-62c22a708d72&lt;/ns3:default&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.taskId&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.system.step.task.id&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;ns3:postAssignment&gt;&lt;ns15:from xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;tw.local.mailTo&lt;/ns15:from&gt;&lt;ns15:to xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance" xsi:type="ns15:tFormalExpression"&gt;""&lt;/ns15:to&gt;&lt;/ns3:postAssignment&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:outgoing&gt;f2324e42-816f-420c-8247-62c22a708d72&lt;/ns15:outgoing&gt;&lt;ns15:timerEventDefinition id="613ceb42-45eb-44ef-8435-406142743427" eventImplId="819261af-c815-4817-85c4-22b850f6f31a"&gt;&lt;ns15:extensionElements&gt;&lt;ns4:timerEventSettings&gt;&lt;ns4:dateType&gt;DueDate&lt;/ns4:dateType&gt;&lt;ns4:relativeDirection&gt;AfterDueDate&lt;/ns4:relativeDirection&gt;&lt;ns4:relativeTime&gt;0&lt;/ns4:relativeTime&gt;&lt;ns4:relativeTimeResolution&gt;Minutes&lt;/ns4:relativeTimeResolution&gt;&lt;ns4:toleranceInterval&gt;0&lt;/ns4:toleranceInterval&gt;&lt;ns4:toleranceIntervalResolution&gt;Minutes&lt;/ns4:toleranceIntervalResolution&gt;&lt;ns4:useCalendar&gt;false&lt;/ns4:useCalendar&gt;&lt;/ns4:timerEventSettings&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:timerEventDefinition&gt;&lt;/ns15:boundaryEvent&gt;&lt;ns15:sequenceFlow sourceRef="c43302a8-b1fa-4fa9-805b-d2abed461cf9" targetRef="956dcbec-d5f4-4a3d-8f90-86bed3f21e33" name="To Send Escalation mail" id="f88a6f6e-d92d-4874-8501-b54942aec83d"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:sequenceFlow sourceRef="462fbe06-d514-4387-8592-2acd0f6a0a5f" targetRef="956dcbec-d5f4-4a3d-8f90-86bed3f21e33" name="To Send Escalation mail" id="f2324e42-816f-420c-8247-62c22a708d72"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;bottomCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;topLeft&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;false&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:endEvent name="End Event" id="c8b8dc76-b1ba-4d4e-8653-ea6dc4248654"&gt;&lt;ns15:extensionElements&gt;&lt;ns13:nodeVisualInfo x="715" y="45" width="24" height="24" /&gt;&lt;/ns15:extensionElements&gt;&lt;ns15:incoming&gt;949eda48-8282-4104-8add-a859c1fa92f3&lt;/ns15:incoming&gt;&lt;/ns15:endEvent&gt;&lt;ns15:sequenceFlow sourceRef="956dcbec-d5f4-4a3d-8f90-86bed3f21e33" targetRef="c8b8dc76-b1ba-4d4e-8653-ea6dc4248654" name="To End Event" id="949eda48-8282-4104-8add-a859c1fa92f3"&gt;&lt;ns15:extensionElements&gt;&lt;ns3:sequenceFlowImplementation sboSyncEnabled="true" /&gt;&lt;ns13:linkVisualInfo&gt;&lt;ns13:sourcePortLocation&gt;rightCenter&lt;/ns13:sourcePortLocation&gt;&lt;ns13:targetPortLocation&gt;leftCenter&lt;/ns13:targetPortLocation&gt;&lt;ns13:showLabel&gt;false&lt;/ns13:showLabel&gt;&lt;ns13:showCoachControlLabel&gt;false&lt;/ns13:showCoachControlLabel&gt;&lt;ns13:labelPosition&gt;0.0&lt;/ns13:labelPosition&gt;&lt;ns13:saveExecutionContext&gt;true&lt;/ns13:saveExecutionContext&gt;&lt;/ns13:linkVisualInfo&gt;&lt;ns3:happySequence&gt;true&lt;/ns3:happySequence&gt;&lt;/ns15:extensionElements&gt;&lt;/ns15:sequenceFlow&gt;&lt;ns15:dataObject itemSubjectRef="itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022" isCollection="false" name="parentPath" id="f2304205-e593-453a-805b-8a8ffe5c8497" /&gt;&lt;ns15:resourceRole name="participantRef" /&gt;&lt;ns15:resourceRole name="businessDataParticipantRef" /&gt;&lt;ns15:resourceRole name="perfMetricParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;ns15:resourceRole name="ownerTeamParticipantRef"&gt;&lt;ns15:resourceRef&gt;24.48d9e8e1-2b51-4173-ac71-9a6c533d134e&lt;/ns15:resourceRef&gt;&lt;/ns15:resourceRole&gt;&lt;/ns15:process&gt;&lt;ns15:interface name="ODC ReversalInterface" id="_749e4b07-74c9-4dac-8864-d13deb52baf9" /&gt;&lt;/ns15:definitions&gt;&#xD;
</bpmn2Data>
        <dependencySummary isNull="true" />
        <jsonData>{"rootElement":[{"flowElement":[{"parallelMultiple":false,"outgoing":["8adc5346-4dc4-48ea-88bc-847fe8c3e8bb"],"isInterrupting":true,"extensionElements":{"default":["8adc5346-4dc4-48ea-88bc-847fe8c3e8bb"],"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":39,"y":82,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["true"]}}]},"name":"Start","declaredType":"startEvent","id":"4d00b4e6-b345-483f-af99-b6815f8ebb6f"},{"incoming":["8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":24,"x":728,"y":128,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Approve","declaredType":"endEvent","id":"6869d68e-0a73-474c-a9da-f7c788cf4d26"},{"outgoing":["1d1902fe-e246-4af8-843c-92b62fbe98d3"],"incoming":["cc8203a1-7910-4b2f-8009-0eeb7a8e3387","8adc5346-4dc4-48ea-88bc-847fe8c3e8bb"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":338,"y":59,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Create ODC Reversal Request \u2013  \u0625\u0646\u0634\u0627\u0621 \u0637\u0644\u0628 \u0627\u0639\u0627\u062f\u0629 \u0642\u064a\u062f \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631 &lt;#= tw.system.process.instanceId #&gt;","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.RACT01)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["false"]}}]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"LastUser","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_ODC_HUB_\"+tw.local.routingDetails.hubCode+\"_CU_MKR\""]}}]},{"targetRef":"2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_MKR\""]}}]}],"serviceRef":"1.302e4a54-16ab-47b9-9b70-d933329c72e7"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"1d1902fe-e246-4af8-843c-92b62fbe98d3","name":"ACT01 Create ODC Reversal Request","dataInputAssociation":[{"targetRef":"2055.602aae62-b023-4b72-8c17-f498f81897a8","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","declaredType":"TFormalExpression","content":["tw.local.isFirstTime"]}}]},{"targetRef":"2055.0f07a9fc-f9d5-4131-8028-34ad2f6c6eed","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.system.user.fullName"]}}]},{"targetRef":"2055.46373b11-9f39-48b9-8787-37bd5d89f78a","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","declaredType":"TFormalExpression","content":["tw.local.routingDetails"]}}]},{"targetRef":"2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"cfc9c782-1e15-4918-8597-81c8a226db13","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}],"sourceRef":["2055.1bba753e-260d-4827-8632-e837d4c62da9"]},{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}],"sourceRef":["2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108"]}],"calledElement":"1.b9bf1811-6e1e-41a3-828b-39059fa168cb"},{"outgoing":["d1c41520-7c04-4ae2-8052-46fb397d3f2a"],"incoming":["1d1902fe-e246-4af8-843c-92b62fbe98d3"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"nodeVisualInfo":[{"width":95,"x":338,"y":45,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","subject":"Review ODC Reversal Request \u2013 \u0645\u0631\u0627\u062c\u0639\u0629 \u0637\u0644\u0628 \u0627\u0639\u0627\u062f\u0629 \u0642\u064a\u062f \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631 ","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"parseFloat(tw.epv.ODCCreationSLA.RACT02)","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"Africa\/Cairo"},"type":"TimeCalculation"}}],"activityType":["UserTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{"dataInputAssociation":[{"targetRef":"2055.b95f0910-f183-4dab-9065-28297a14ceef","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_ODC_HUB_\"+tw.local.routingDetails.hubCode+\"_CU_CHKR\""]}}]},{"targetRef":"2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97","assignment":[{"from":{"evaluatesToTypeRef":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.routingDetails.hubCode"]}}]},{"targetRef":"2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["\"BPM_IDC_HUB_\"+tw.local.routingDetails.hubCode+\"_EXE_CHKR\""]}}]}],"serviceRef":"1.302e4a54-16ab-47b9-9b70-d933329c72e7"}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"d1c41520-7c04-4ae2-8052-46fb397d3f2a","name":"ACT02 Review ODC Reversal Request","dataInputAssociation":[{"targetRef":"2055.a82241b6-421c-44f4-83f7-f6d3d3d3a024","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.8b489eb0-ce3f-46d6-8896-60fbb2cd2288","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.lastAction"]}}]},{"targetRef":"2055.34b891a3-f943-4cf2-8753-3a356f300282","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.parentPath"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"204734d8-ab45-42fa-8dde-9f2d35136f07","dataOutputAssociation":[{"assignment":[{"to":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}],"sourceRef":["2055.d49e3545-463a-4eb4-8273-1e61d066f668"]}],"calledElement":"1.c71c7233-23ac-4e5f-a241-09cd96502615"},{"targetRef":"204734d8-ab45-42fa-8dde-9f2d35136f07","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ACT02 Review ODC Reversal Request","declaredType":"sequenceFlow","id":"1d1902fe-e246-4af8-843c-92b62fbe98d3","sourceRef":"cfc9c782-1e15-4918-8597-81c8a226db13"},{"outgoing":["8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3","45efef31-a3f7-48df-8e5f-55d0bd5a1a21","cc8203a1-7910-4b2f-8009-0eeb7a8e3387"],"incoming":["d1c41520-7c04-4ae2-8052-46fb397d3f2a"],"default":"cc8203a1-7910-4b2f-8009-0eeb7a8e3387","gatewayDirection":"Unspecified","extensionElements":{"nodeVisualInfo":[{"width":32,"x":532,"y":64,"declaredType":"TNodeVisualInfo","height":32}]},"name":"Checker Decision Action","declaredType":"exclusiveGateway","id":"a66b788f-eb9d-4662-8d5a-15e1618e166e"},{"targetRef":"a66b788f-eb9d-4662-8d5a-15e1618e166e","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Checker Decision Action","declaredType":"sequenceFlow","id":"d1c41520-7c04-4ae2-8052-46fb397d3f2a","sourceRef":"204734d8-ab45-42fa-8dde-9f2d35136f07"},{"incoming":["45efef31-a3f7-48df-8e5f-55d0bd5a1a21"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":729,"y":67,"declaredType":"TNodeVisualInfo","height":24}]},"name":"Cancel","declaredType":"endEvent","id":"60aa4589-527a-4330-8c62-e27d874075b2"},{"targetRef":"60aa4589-527a-4330-8c62-e27d874075b2","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.cancelRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"To Cancel","declaredType":"sequenceFlow","id":"45efef31-a3f7-48df-8e5f-55d0bd5a1a21","sourceRef":"a66b788f-eb9d-4662-8d5a-15e1618e166e"},{"targetRef":"6869d68e-0a73-474c-a9da-f7c788cf4d26","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions.approveRequest"]},"extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":true}],"happySequence":[true]},"name":"To Approve","declaredType":"sequenceFlow","id":"8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3","sourceRef":"a66b788f-eb9d-4662-8d5a-15e1618e166e"},{"targetRef":"cfc9c782-1e15-4918-8597-81c8a226db13","conditionExpression":{"declaredType":"TFormalExpression","content":["tw.local.odcRequest.stepLog.action\t  ==\t  tw.epv.CreationActions."]},"extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"topCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"rightCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":true}],"happySequence":[true]},"name":"Back To initiator","declaredType":"sequenceFlow","id":"cc8203a1-7910-4b2f-8009-0eeb7a8e3387","sourceRef":"a66b788f-eb9d-4662-8d5a-15e1618e166e"},{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":true,"value":"true"}]},"itemSubjectRef":"itm.12.83ff975e-8dbc-42e5-b738-fa8bc08274a2","name":"isFirstTime","isCollection":false,"declaredType":"dataObject","id":"862e24d6-e072-4653-8abd-681972b119f0"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"lastAction","isCollection":false,"declaredType":"dataObject","id":"159f0712-06fa-4aa8-8a5d-bef2234b0a9f"},{"targetRef":"cfc9c782-1e15-4918-8597-81c8a226db13","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To ODC Initiators","declaredType":"sequenceFlow","id":"8adc5346-4dc4-48ea-88bc-847fe8c3e8bb","sourceRef":"4d00b4e6-b345-483f-af99-b6815f8ebb6f"},{"outgoing":["949eda48-8282-4104-8add-a859c1fa92f3"],"incoming":["f88a6f6e-d92d-4874-8501-b54942aec83d","f2324e42-816f-420c-8247-62c22a708d72"],"extensionElements":{"activityExtension":[{"conditionScript":"","conditional":false,"transactionalBehavior":"NotSet","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityExtension"}],"deleteTaskOnCompletion":[true],"nodeVisualInfo":[{"width":95,"x":378,"y":22,"declaredType":"TNodeVisualInfo","height":70}],"userTaskSettings":[{"activityAssignmentType":"Lane","activityWorkSchedule":{"timezoneType":0,"timeScheduleType":0,"holidayScheduleType":0},"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TUserTaskSettings","activityPriority":{"type":"Priority","priority":"Normal"},"activityDueDate":{"dueDate":{"unit":"Hours","value":"1","timeOfDay":"00:00"},"timeZone":{"type":"TimeZone","value":"(use default)"},"type":"TimeCalculation"}}],"activityType":["ServiceTask"]},"declaredType":"callActivity","startQuantity":1,"resourceRole":[{"teamAssignmentType":"Reference","name":"Lane","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"teamAssignmentType":"Reference","name":"Team","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TActivityPerformer","distribution":"None","teamFilterService":{}},{"name":"Expert","declaredType":"performer"}],"default":"949eda48-8282-4104-8add-a859c1fa92f3","name":"Send Escalation mail","dataInputAssociation":[{"targetRef":"2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","declaredType":"TFormalExpression","content":["tw.local.odcRequest"]}}]},{"targetRef":"2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.taskId"]}}]},{"targetRef":"2055.9771d7e8-ca59-430e-8b1a-194cf04c1182","assignment":[{"from":{"evaluatesToTypeRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","declaredType":"TFormalExpression","content":["tw.local.mailTo"]}}]}],"isForCompensation":false,"completionQuantity":1,"id":"956dcbec-d5f4-4a3d-8f90-86bed3f21e33","calledElement":"1.d7acf968-6740-4e52-b037-2049466eeeb2"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"taskId","isCollection":false,"declaredType":"dataObject","id":"db3fa75c-e63e-443f-8487-98f335bea12e"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"mailTo","isCollection":false,"declaredType":"dataObject","id":"4e5d0b49-dad8-4560-8d99-82f26b0daeb8"},{"parallelMultiple":false,"outgoing":["f88a6f6e-d92d-4874-8501-b54942aec83d"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"cb3ec0ad-00f1-4b8c-8e62-ac8b2c7b2945","otherAttributes":{"eventImplId":"cfbc88db-07d4-47c8-816b-abf2a1695f2f"}}],"attachedToRef":"cfc9c782-1e15-4918-8597-81c8a226db13","extensionElements":{"default":["f88a6f6e-d92d-4874-8501-b54942aec83d"],"nodeVisualInfo":[{"width":24,"x":347,"y":117,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskId"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event1","declaredType":"boundaryEvent","id":"c43302a8-b1fa-4fa9-805b-d2abed461cf9"},{"parallelMultiple":false,"outgoing":["f2324e42-816f-420c-8247-62c22a708d72"],"eventDefinition":[{"extensionElements":{"timerEventSettings":[{"relativeTime":"0","relativeTimeResolution":"Minutes","dateType":"DueDate","toleranceInterval":"0","useCalendar":false,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TTimerEventSettings","toleranceIntervalResolution":"Minutes","relativeDirection":"AfterDueDate"}]},"declaredType":"timerEventDefinition","id":"613ceb42-45eb-44ef-8435-406142743427","otherAttributes":{"eventImplId":"819261af-c815-4817-85c4-22b850f6f31a"}}],"attachedToRef":"204734d8-ab45-42fa-8dde-9f2d35136f07","extensionElements":{"default":["f2324e42-816f-420c-8247-62c22a708d72"],"nodeVisualInfo":[{"width":24,"x":373,"y":103,"declaredType":"TNodeVisualInfo","height":24}],"postAssignment":[{"from":{"declaredType":"TFormalExpression","content":["tw.local.taskId"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["tw.system.step.task.id"]}},{"from":{"declaredType":"TFormalExpression","content":["tw.local.mailTo"]},"declaredType":"TAssignment","to":{"declaredType":"TFormalExpression","content":["\"\""]}}]},"cancelActivity":false,"name":"Boundary Event2","declaredType":"boundaryEvent","id":"462fbe06-d514-4387-8592-2acd0f6a0a5f"},{"targetRef":"956dcbec-d5f4-4a3d-8f90-86bed3f21e33","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"f88a6f6e-d92d-4874-8501-b54942aec83d","sourceRef":"c43302a8-b1fa-4fa9-805b-d2abed461cf9"},{"targetRef":"956dcbec-d5f4-4a3d-8f90-86bed3f21e33","extensionElements":{"linkVisualInfo":[{"sourcePortLocation":"bottomCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"topLeft","declaredType":"TLinkVisualInfo","saveExecutionContext":false,"showLabel":false}],"happySequence":[true]},"name":"To Send Escalation mail","declaredType":"sequenceFlow","id":"f2324e42-816f-420c-8247-62c22a708d72","sourceRef":"462fbe06-d514-4387-8592-2acd0f6a0a5f"},{"incoming":["949eda48-8282-4104-8add-a859c1fa92f3"],"extensionElements":{"nodeVisualInfo":[{"width":24,"x":715,"y":45,"declaredType":"TNodeVisualInfo","height":24}]},"name":"End Event","declaredType":"endEvent","id":"c8b8dc76-b1ba-4d4e-8653-ea6dc4248654"},{"targetRef":"c8b8dc76-b1ba-4d4e-8653-ea6dc4248654","extensionElements":{"sequenceFlowImplementation":[{"fireValidation":"Never","sboSyncEnabled":true,"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TSequenceFlowImplementation"}],"linkVisualInfo":[{"sourcePortLocation":"rightCenter","showCoachControlLabel":false,"labelPosition":0.0,"targetPortLocation":"leftCenter","declaredType":"TLinkVisualInfo","saveExecutionContext":true,"showLabel":false}],"happySequence":[true]},"name":"To End Event","declaredType":"sequenceFlow","id":"949eda48-8282-4104-8add-a859c1fa92f3","sourceRef":"956dcbec-d5f4-4a3d-8f90-86bed3f21e33"},{"itemSubjectRef":"itm.12.db884a3c-c533-44b7-bb2d-47bec8ad4022","name":"parentPath","isCollection":false,"declaredType":"dataObject","id":"f2304205-e593-453a-805b-8a8ffe5c8497"}],"laneSet":[{"id":"320b2a03-2ef6-48f6-9980-49703310afb3","lane":[{"flowNodeRef":["4d00b4e6-b345-483f-af99-b6815f8ebb6f","cfc9c782-1e15-4918-8597-81c8a226db13","c43302a8-b1fa-4fa9-805b-d2abed461cf9"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":0,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Hub \/ center Maker","partitionElementRef":"24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce","declaredType":"lane","id":"2e4a3c95-9839-4c37-870f-b0215a22597b","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["204734d8-ab45-42fa-8dde-9f2d35136f07","a66b788f-eb9d-4662-8d5a-15e1618e166e","6869d68e-0a73-474c-a9da-f7c788cf4d26","60aa4589-527a-4330-8c62-e27d874075b2","462fbe06-d514-4387-8592-2acd0f6a0a5f"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":201,"declaredType":"TNodeVisualInfo","height":200}]},"name":"Hub \/ center Checker","partitionElementRef":"24.8e005024-3fe0-4848-8c3c-f1e9483900c6","declaredType":"lane","id":"7e6e9014-5f8d-44eb-8ceb-ecba6505368c","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"false"}},{"flowNodeRef":["956dcbec-d5f4-4a3d-8f90-86bed3f21e33","c8b8dc76-b1ba-4d4e-8653-ea6dc4248654"],"extensionElements":{"nodeVisualInfo":[{"color":"#F8F8F8","width":3000,"x":0,"y":402,"declaredType":"TNodeVisualInfo","height":200}]},"name":"System","partitionElementRef":"24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b","declaredType":"lane","id":"9180d59b-6f02-4dad-a33f-d017ed000296","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process\/wle}isSystemLane":"true"}}]}],"resourceRole":[{"name":"participantRef","declaredType":"resourceRole"},{"name":"businessDataParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6","name":"perfMetricParticipantRef","declaredType":"resourceRole"},{"resourceRef":"24.48d9e8e1-2b51-4173-ac71-9a6c533d134e","name":"ownerTeamParticipantRef","declaredType":"resourceRole"}],"isClosed":false,"extensionElements":{"bpdExtension":[{"allowContentOperations":false,"enableTracking":false,"workSchedule":{"timezoneType":0,"timeScheduleType":0,"timezone":"Egypt","timeScheduleName":"NBEWork","holidayScheduleName":"NBEHoliday","holidayScheduleType":0},"instanceName":"\" Request Type: \"+ tw.local.odcRequest.requestType.name  +\" , \"+\" CIF: \"+ tw.local.odcRequest.cif +\" , \" +\"Request Number: \" +tw.system.process.instanceId","dueDateSettings":{"dueDate":{"unit":"Hours","value":"8","timeOfDay":"00:00"},"type":"TimeCalculation"},"autoTrackingName":"at1693545399110","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TBPDExtension","optimizeExecForLatency":false,"dueDateEnabled":false,"atRiskCalcEnabled":false,"allowProjectedPathManagement":false,"autoTrackingEnabled":false,"sboSyncEnabled":true}],"caseExtension":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmcaseext.TCaseExtension","caseFolder":{"allowSubfoldersCreation":false,"allowLocalDoc":false,"id":"5aba4da2-4484-4810-835b-d5873189e7d7","allowExternalFolder":false,"allowExternalDoc":false}}],"isConvergedProcess":[true]},"documentation":[{"textFormat":"text\/plain"}],"name":"ODC Reversal \u0627\u0639\u0627\u062f\u0629 \u0642\u064a\u062f \u062a\u062d\u0635\u064a\u0644 \u0645\u0633\u062a\u0646\u062f\u0649 \u062a\u0635\u062f\u064a\u0631","declaredType":"process","id":"25.933ae173-470f-4c1f-8a2b-6521da495a38","processType":"None","otherAttributes":{"{http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/bpmn\/ext\/process}executionMode":"longRunning"},"ioSpecification":{"extensionElements":{"epvProcessLinks":[{"epvProcessLinkRef":[{"epvId":"21.daf76aa9-3be6-432b-b228-01c27b3012d3","epvProcessLinkId":"1e68ed8f-13f7-4cb5-806c-8d8636ef2b03","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"},{"epvId":"21.dbbaa047-8f02-4397-b1b5-41f11b0256b3","epvProcessLinkId":"014acb69-59a8-45e7-822c-05953bccc3e0","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinkRef"}],"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TEpvProcessLinks"}]},"inputSet":[{},{"id":"_33e6e276-9f24-47a5-8f75-c1819ec07f1d"}],"outputSet":[{},{"id":"_5b5b1b2e-cab6-4f6f-96d2-c0d0f82c82e8"}],"dataInput":[{"extensionElements":{"defaultValue":[{"declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmext.TDefaultValue","useDefault":false,"value":"var autoObject = new tw.object.ODCRequest();\nautoObject.initiator = \"\";\nautoObject.requestNature = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestNature.name = \"\";\nautoObject.requestNature.value = \"\";\nautoObject.requestType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.requestType.name = \"\";\nautoObject.requestType.value = \"\";\nautoObject.cif = \"\";\nautoObject.customerName = \"\";\nautoObject.parentRequestNo = \"\";\nautoObject.requestDate = new TWDate();\nautoObject.ImporterName = \"\";\nautoObject.appInfo = new tw.object.toolkit.NBEC.AppInfo();\nautoObject.appInfo.requestDate = \"\";\nautoObject.appInfo.status = \"\";\nautoObject.appInfo.subStatus = \"\";\nautoObject.appInfo.initiator = \"\";\nautoObject.appInfo.branch = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.appInfo.branch.name = \"\";\nautoObject.appInfo.branch.value = \"\";\nautoObject.appInfo.requestName = \"\";\nautoObject.appInfo.requestType = \"\";\nautoObject.appInfo.stepName = \"\";\nautoObject.appInfo.appRef = \"\";\nautoObject.appInfo.appID = \"\";\nautoObject.appInfo.instanceID = \"\";\nautoObject.CustomerInfo = new tw.object.CustomerInfo();\nautoObject.CustomerInfo.cif = \"\";\nautoObject.CustomerInfo.customerName = \"\";\nautoObject.CustomerInfo.addressLine1 = \"\";\nautoObject.CustomerInfo.addressLine2 = \"\";\nautoObject.CustomerInfo.addressLine3 = \"\";\nautoObject.CustomerInfo.customerSector = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.customerSector.name = \"\";\nautoObject.CustomerInfo.customerSector.value = \"\";\nautoObject.CustomerInfo.customerType = \"\";\nautoObject.CustomerInfo.customerNoCBE = \"\";\nautoObject.CustomerInfo.facilityType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.CustomerInfo.facilityType.name = \"\";\nautoObject.CustomerInfo.facilityType.value = \"\";\nautoObject.CustomerInfo.commercialRegistrationNo = \"\";\nautoObject.CustomerInfo.commercialRegistrationOffice = \"\";\nautoObject.CustomerInfo.taxCardNo = \"\";\nautoObject.CustomerInfo.importCardNo = \"\";\nautoObject.CustomerInfo.initiationHub = \"\";\nautoObject.CustomerInfo.country = \"\";\nautoObject.BasicDetails = new tw.object.BasicDetails();\nautoObject.BasicDetails.requestNature = \"\";\nautoObject.BasicDetails.requestType = \"\";\nautoObject.BasicDetails.parentRequestNo = \"\";\nautoObject.BasicDetails.requestState = \"\";\nautoObject.BasicDetails.flexCubeContractNo = \"\";\nautoObject.BasicDetails.contractStage = \"\";\nautoObject.BasicDetails.exportPurpose = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.exportPurpose.name = \"\";\nautoObject.BasicDetails.exportPurpose.value = \"\";\nautoObject.BasicDetails.paymentTerms = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.paymentTerms.name = \"\";\nautoObject.BasicDetails.paymentTerms.value = \"\";\nautoObject.BasicDetails.productCategory = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.productCategory.name = \"\";\nautoObject.BasicDetails.productCategory.value = \"\";\nautoObject.BasicDetails.commodityDescription = \"\";\nautoObject.BasicDetails.CountryOfOrigin = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.BasicDetails.CountryOfOrigin.name = \"\";\nautoObject.BasicDetails.CountryOfOrigin.value = \"\";\nautoObject.BasicDetails.Bills = new tw.object.listOf.Bills();\nautoObject.BasicDetails.Bills[0] = new tw.object.Bills();\nautoObject.BasicDetails.Bills[0].billOfLadingRef = \"\";\nautoObject.BasicDetails.Bills[0].billOfLadingDate = new TWDate();\nautoObject.BasicDetails.Invoice = new tw.object.listOf.Invoice();\nautoObject.BasicDetails.Invoice[0] = new tw.object.Invoice();\nautoObject.BasicDetails.Invoice[0].invoiceNo = \"\";\nautoObject.BasicDetails.Invoice[0].invoiceDate = new TWDate();\nautoObject.GeneratedDocumentInfo = new tw.object.GeneratedDocumentInfo();\nautoObject.GeneratedDocumentInfo.customerName = \"\";\nautoObject.GeneratedDocumentInfo.customerAddress = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTerms = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.deliveryTerms[0] = \"\";\nautoObject.GeneratedDocumentInfo.deliveryTermsExtra = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.paymentInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.paymentInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.Instructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.Instructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.InstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.GeneratedDocumentInfo.specialInstructions[0] = \"\";\nautoObject.GeneratedDocumentInfo.specialInstructionsExtra = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetter = false;\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.name = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterOption.value = \"\";\nautoObject.GeneratedDocumentInfo.regenerateRemLetterTitle = \"\";\nautoObject.FinancialDetailsBR = new tw.object.FinancialDetailsBranch();\nautoObject.FinancialDetailsBR.documentAmount = 0.0;\nautoObject.FinancialDetailsBR.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.currency.name = \"\";\nautoObject.FinancialDetailsBR.currency.value = \"\";\nautoObject.FinancialDetailsBR.maxCollectionDate = new TWDate();\nautoObject.FinancialDetailsBR.amountAdvanced = 0.0;\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.name = \"\";\nautoObject.FinancialDetailsBR.chargesAndCommissionsAccount.value = \"\";\nautoObject.FinancialDetailsBR.collectionAccount = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.collectionAccount.name = \"\";\nautoObject.FinancialDetailsBR.collectionAccount.value = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsBR.listOfAccounts[0].name = \"\";\nautoObject.FinancialDetailsBR.listOfAccounts[0].value = \"\";\nautoObject.FcCollections = new tw.object.FCCollections();\nautoObject.FcCollections.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.currency.name = \"\";\nautoObject.FcCollections.currency.value = \"\";\nautoObject.FcCollections.standardExchangeRate = 0.0;\nautoObject.FcCollections.negotiatedExchangeRate = 0.0;\nautoObject.FcCollections.fromDate = new TWDate();\nautoObject.FcCollections.ToDate = new TWDate();\nautoObject.FcCollections.accountNo = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.accountNo.name = \"\";\nautoObject.FcCollections.accountNo.value = \"\";\nautoObject.FcCollections.retrievedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.retrievedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.retrievedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.retrievedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.retrievedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].currency = \"\";\nautoObject.FcCollections.retrievedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.retrievedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.selectedTransactions = new tw.object.listOf.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0] = new tw.object.FCTransactions();\nautoObject.FcCollections.selectedTransactions[0].accountNo = \"\";\nautoObject.FcCollections.selectedTransactions[0].referenceNumber = \"\";\nautoObject.FcCollections.selectedTransactions[0].postingDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].valueDate = new TWDate();\nautoObject.FcCollections.selectedTransactions[0].transactionAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].existAmount = 0.0;\nautoObject.FcCollections.selectedTransactions[0].currency = \"\";\nautoObject.FcCollections.selectedTransactions[0].amountAllocatedForCurrencyRequest = 0.0;\nautoObject.FcCollections.selectedTransactions[0].allocatedAmountInRequestCurrency = 0.0;\nautoObject.FcCollections.isReversed = false;\nautoObject.FcCollections.usedAmount = 0.0;\nautoObject.FcCollections.calculatedAmount = 0.0;\nautoObject.FcCollections.totalAllocatedAmount = 0.0;\nautoObject.FcCollections.listOfAccounts = new tw.object.listOf.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0] = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FcCollections.listOfAccounts[0].name = \"\";\nautoObject.FcCollections.listOfAccounts[0].value = \"\";\nautoObject.FinancialDetailsFO = new tw.object.FinancialDetailsFO();\nautoObject.FinancialDetailsFO.discount = 0.0;\nautoObject.FinancialDetailsFO.extraCharges = 0.0;\nautoObject.FinancialDetailsFO.ourCharges = 0.0;\nautoObject.FinancialDetailsFO.amountSight = 0.0;\nautoObject.FinancialDetailsFO.amountDefNoAvalization = 0.0;\nautoObject.FinancialDetailsFO.amountDefAvalization = 0.0;\nautoObject.FinancialDetailsFO.collectableAmount = 0.0;\nautoObject.FinancialDetailsFO.outstandingAmount = 0.0;\nautoObject.FinancialDetailsFO.maturityDate = new TWDate();\nautoObject.FinancialDetailsFO.noOfDaysMaturity = 0;\nautoObject.FinancialDetailsFO.referenceNo = \"\";\nautoObject.FinancialDetailsFO.financeApprovalNo = \"\";\nautoObject.FinancialDetailsFO.executionHub = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.FinancialDetailsFO.executionHub.name = \"\";\nautoObject.FinancialDetailsFO.executionHub.value = \"\";\nautoObject.FinancialDetailsFO.multiTenorDates = new tw.object.listOf.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0] = new tw.object.MultiTenorDates();\nautoObject.FinancialDetailsFO.multiTenorDates[0].date = new TWDate();\nautoObject.FinancialDetailsFO.multiTenorDates[0].amount = 0.0;\nautoObject.ImporterDetails = new tw.object.ImporterDetails();\nautoObject.ImporterDetails.importerName = \"\";\nautoObject.ImporterDetails.importerCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.importerCountry.name = \"\";\nautoObject.ImporterDetails.importerCountry.value = \"\";\nautoObject.ImporterDetails.importerAddress = \"\";\nautoObject.ImporterDetails.importerPhoneNo = \"\";\nautoObject.ImporterDetails.bank = \"\";\nautoObject.ImporterDetails.BICCode = \"\";\nautoObject.ImporterDetails.ibanAccount = \"\";\nautoObject.ImporterDetails.bankCountry = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ImporterDetails.bankCountry.name = \"\";\nautoObject.ImporterDetails.bankCountry.value = \"\";\nautoObject.ImporterDetails.bankAddress = \"\";\nautoObject.ImporterDetails.bankPhoneNo = \"\";\nautoObject.ImporterDetails.collectingBankReference = \"\";\nautoObject.ProductShipmentDetails = new tw.object.ProductShipmentDetails();\nautoObject.ProductShipmentDetails.CBECommodityClassification = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.CBECommodityClassification.name = \"\";\nautoObject.ProductShipmentDetails.CBECommodityClassification.value = \"\";\nautoObject.ProductShipmentDetails.shippingDate = new TWDate();\nautoObject.ProductShipmentDetails.shipmentMethod = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ProductShipmentDetails.shipmentMethod.name = \"\";\nautoObject.ProductShipmentDetails.shipmentMethod.value = \"\";\nautoObject.OdcCollection = new tw.object.ODCCollection();\nautoObject.OdcCollection.amount = 0.0;\nautoObject.OdcCollection.currency = \"\";\nautoObject.OdcCollection.informCADAboutTheCollection = false;\nautoObject.ReversalReason = new tw.object.ReversalReason();\nautoObject.ReversalReason.reversalReason = \"\";\nautoObject.ReversalReason.closureReason = \"\";\nautoObject.ContractCreation = new tw.object.ContractCreation();\nautoObject.ContractCreation.productCode = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractCreation.productCode.name = \"\";\nautoObject.ContractCreation.productCode.value = \"\";\nautoObject.ContractCreation.productDescription = \"\";\nautoObject.ContractCreation.Stage = \"\";\nautoObject.ContractCreation.userReference = \"\";\nautoObject.ContractCreation.sourceReference = \"\";\nautoObject.ContractCreation.currency = \"\";\nautoObject.ContractCreation.amount = 0.0;\nautoObject.ContractCreation.baseDate = new TWDate();\nautoObject.ContractCreation.valueDate = new TWDate();\nautoObject.ContractCreation.tenorDays = 0;\nautoObject.ContractCreation.transitDays = 0;\nautoObject.ContractCreation.maturityDate = new TWDate();\nautoObject.Parties = new tw.object.odcParties();\nautoObject.Parties.Drawer = new tw.object.Drawer();\nautoObject.Parties.Drawer.partyId = \"\";\nautoObject.Parties.Drawer.partyName = \"\";\nautoObject.Parties.Drawer.country = \"\";\nautoObject.Parties.Drawer.Language = \"\";\nautoObject.Parties.Drawer.Reference = \"\";\nautoObject.Parties.Drawer.address1 = \"\";\nautoObject.Parties.Drawer.address2 = \"\";\nautoObject.Parties.Drawer.address3 = \"\";\nautoObject.Parties.Drawee = new tw.object.Drawee();\nautoObject.Parties.Drawee.partyId = \"\";\nautoObject.Parties.Drawee.partyName = \"\";\nautoObject.Parties.Drawee.country = \"\";\nautoObject.Parties.Drawee.Language = \"\";\nautoObject.Parties.Drawee.Reference = \"\";\nautoObject.Parties.Drawee.address1 = \"\";\nautoObject.Parties.Drawee.address2 = \"\";\nautoObject.Parties.Drawee.address3 = \"\";\nautoObject.Parties.collectingBank = new tw.object.CollectingBank();\nautoObject.Parties.collectingBank.id = \"\";\nautoObject.Parties.collectingBank.name = \"\";\nautoObject.Parties.collectingBank.country = \"\";\nautoObject.Parties.collectingBank.language = \"\";\nautoObject.Parties.collectingBank.reference = \"\";\nautoObject.Parties.collectingBank.address1 = \"\";\nautoObject.Parties.collectingBank.address2 = \"\";\nautoObject.Parties.collectingBank.address3 = \"\";\nautoObject.Parties.collectingBank.cif = \"\";\nautoObject.Parties.collectingBank.media = \"\";\nautoObject.Parties.collectingBank.address = \"\";\nautoObject.Parties.partyTypes = new tw.object.partyTypes();\nautoObject.Parties.partyTypes.partyCIF = \"\";\nautoObject.Parties.partyTypes.partyId = \"\";\nautoObject.Parties.partyTypes.partyName = \"\";\nautoObject.Parties.partyTypes.country = \"\";\nautoObject.Parties.partyTypes.language = \"\";\nautoObject.Parties.partyTypes.refrence = \"\";\nautoObject.Parties.partyTypes.address1 = \"\";\nautoObject.Parties.partyTypes.address2 = \"\";\nautoObject.Parties.partyTypes.address3 = \"\";\nautoObject.Parties.partyTypes.partyType = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.Parties.partyTypes.partyType.name = \"\";\nautoObject.Parties.partyTypes.partyType.value = \"\";\nautoObject.ChargesAndCommissions = new tw.object.listOf.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0] = new tw.object.ChargesAndCommissions();\nautoObject.ChargesAndCommissions[0].component = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].defaultCurrency.name = \"\";\nautoObject.ChargesAndCommissions[0].defaultCurrency.value = \"\";\nautoObject.ChargesAndCommissions[0].changeAmount = 0.0;\nautoObject.ChargesAndCommissions[0].waiver = false;\nautoObject.ChargesAndCommissions[0].debitedAccount = new tw.object.AccountDetails();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.accountClass.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.glAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.customerAccountNo = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.branchCode = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.name = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.currency.value = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.balance = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAccount.balanceSign = \"\";\nautoObject.ChargesAndCommissions[0].debitedAccount.isOverDraft = false;\nautoObject.ChargesAndCommissions[0].debitedAmount = new tw.object.AmountDetails();\nautoObject.ChargesAndCommissions[0].debitedAmount.standardExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.negotiatedExRate = 0.0;\nautoObject.ChargesAndCommissions[0].debitedAmount.amountInAccount = 0.0;\nautoObject.ChargesAndCommissions[0].rateType = \"\";\nautoObject.ChargesAndCommissions[0].description = \"\";\nautoObject.ChargesAndCommissions[0].defaultPercentage = 0.0;\nautoObject.ChargesAndCommissions[0].changePercentage = 0.0;\nautoObject.ChargesAndCommissions[0].defaultAmount = 0.0;\nautoObject.ChargesAndCommissions[0].basicAmountCurrency = \"\";\nautoObject.ChargesAndCommissions[0].flatAmount = 0.0;\nautoObject.ContractLiquidation = new tw.object.ContractLiquidation();\nautoObject.ContractLiquidation.liqAmount = 0.0;\nautoObject.ContractLiquidation.liqCurrency = \"\";\nautoObject.ContractLiquidation.debitValueDate = new TWDate();\nautoObject.ContractLiquidation.creditValueDate = new TWDate();\nautoObject.ContractLiquidation.debitedAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount = new tw.object.AccountDetails();\nautoObject.ContractLiquidation.creditedAccount.accountClass = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.accountClass.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.accountClass.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.glAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.customerAccountNo = \"\";\nautoObject.ContractLiquidation.creditedAccount.branchCode = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.ContractLiquidation.creditedAccount.currency.name = \"\";\nautoObject.ContractLiquidation.creditedAccount.currency.value = \"\";\nautoObject.ContractLiquidation.creditedAccount.balance = 0.0;\nautoObject.ContractLiquidation.creditedAccount.balanceSign = \"\";\nautoObject.ContractLiquidation.creditedAccount.isOverDraft = false;\nautoObject.ContractLiquidation.creditedAmount = new tw.object.AmountDetails();\nautoObject.ContractLiquidation.creditedAmount.standardExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.negotiatedExRate = 0.0;\nautoObject.ContractLiquidation.creditedAmount.amountInAccount = 0.0;\nautoObject.complianceApproval = false;\nautoObject.stepLog = new tw.object.StepLog();\nautoObject.stepLog.startTime = new TWDate();\nautoObject.stepLog.endTime = new TWDate();\nautoObject.stepLog.userName = \"\";\nautoObject.stepLog.role = \"\";\nautoObject.stepLog.step = \"\";\nautoObject.stepLog.action = \"\";\nautoObject.stepLog.comment = \"\";\nautoObject.stepLog.terminateReason = \"\";\nautoObject.stepLog.returnReason = \"\";\nautoObject.actions = new tw.object.listOf.toolkit.TWSYS.String();\nautoObject.actions[0] = \"\";\nautoObject.attachmentDetails = new tw.object.attachmentDetails();\nautoObject.attachmentDetails.folderID = \"\";\nautoObject.attachmentDetails.ecmProperties = new tw.object.ECMproperties();\nautoObject.attachmentDetails.ecmProperties.fullPath = \"\";\nautoObject.attachmentDetails.ecmProperties.cmisQuery = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties = new tw.object.listOf.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0] = new tw.object.toolkit.SYSCM.ECMDefaultProperty();\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].name = \"\";\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].value = null;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].editable = false;\nautoObject.attachmentDetails.ecmProperties.defaultProperties[0].hidden = false;\nautoObject.attachmentDetails.attachment = new tw.object.listOf.Attachment();\nautoObject.attachmentDetails.attachment[0] = new tw.object.Attachment();\nautoObject.attachmentDetails.attachment[0].name = \"\";\nautoObject.attachmentDetails.attachment[0].description = \"\";\nautoObject.attachmentDetails.attachment[0].arabicName = \"\";\nautoObject.complianceComments = new tw.object.listOf.StepLog();\nautoObject.complianceComments[0] = new tw.object.StepLog();\nautoObject.complianceComments[0].startTime = new TWDate();\nautoObject.complianceComments[0].endTime = new TWDate();\nautoObject.complianceComments[0].userName = \"\";\nautoObject.complianceComments[0].role = \"\";\nautoObject.complianceComments[0].step = \"\";\nautoObject.complianceComments[0].action = \"\";\nautoObject.complianceComments[0].comment = \"\";\nautoObject.complianceComments[0].terminateReason = \"\";\nautoObject.complianceComments[0].returnReason = \"\";\nautoObject.History = new tw.object.listOf.StepLog();\nautoObject.History[0] = new tw.object.StepLog();\nautoObject.History[0].startTime = new TWDate();\nautoObject.History[0].endTime = new TWDate();\nautoObject.History[0].userName = \"\";\nautoObject.History[0].role = \"\";\nautoObject.History[0].step = \"\";\nautoObject.History[0].action = \"\";\nautoObject.History[0].comment = \"\";\nautoObject.History[0].terminateReason = \"\";\nautoObject.History[0].returnReason = \"\";\nautoObject.documentSource = new tw.object.toolkit.TWSYS.NameValuePair();\nautoObject.documentSource.name = \"\";\nautoObject.documentSource.value = \"\";\nautoObject.folderID = \"\";\nautoObject.isLiquidated = false;\nautoObject"}],"searchableField":[{"path":".initiator","alias":"InitiatorHubBranch","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"7b6a22dc-233a-4ecd-8cbb-ee6921af4adc","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".parentRequestNo","alias":"parentRequestNumber","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"f8755e6e-8272-431b-82c8-e9f1e7ed15ce","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".requestDate","alias":"requestDate","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"7eb6d0f9-71e2-4496-86ce-d833141978e6","type":"12.68474ab0-d56f-47ee-b7e9-510b45a2a8be"},{"path":".appInfo.status","alias":"requestStatus","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"a339ecd6-4974-4376-8f65-0fcf632e8222","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".appInfo.initiator","alias":"InitiatorUserName","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"cbf1ac69-8dd1-4ef4-8633-6640a9052e85","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".CustomerInfo.cif","alias":"customerCIF","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"e19ec9a2-b0e8-430d-89e9-a631fd4af208","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".customerName","alias":"customerName","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"83c8ec89-336a-4889-8831-2db11de97e8d","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"},{"path":".CustomerInfo.customerName","alias":"customerName_1","declaredType":"com.ibm.bpmsdk.model.bpmn20.ibmwleext.TSearchableField","id":"891a5bad-505c-4409-8a7d-e7068726a11a","type":"12.db884a3c-c533-44b7-bb2d-47bec8ad4022"}]},"itemSubjectRef":"itm.12.5f8547c1-515a-481c-b74a-f20ec40faff9","name":"odcRequest","isCollection":false,"id":"2007.86f87789-824f-4930-8c19-5a719284c659"},{"itemSubjectRef":"itm.12.faf28340-88a9-4a2a-8098-1c0b7c2ae727","name":"routingDetails","isCollection":false,"id":"2007.684c7152-3e6b-4505-8f37-4ed21a611236"}]}},{"name":"ODC ReversalInterface","declaredType":"interface","id":"_749e4b07-74c9-4dac-8864-d13deb52baf9"}],"targetNamespace":"","typeLanguage":"http:\/\/www.w3.org\/2001\/XMLSchema","expressionLanguage":"http:\/\/www.ibm.com\/xmlns\/prod\/bpm\/expression-lang\/javascript","id":"fe1ac9f6-647e-47c2-9e22-608f9d73ef7a"}</jsonData>
        <migrationData isNull="true" />
        <rwfData isNull="true" />
        <rwfStatus isNull="true" />
        <templateId isNull="true" />
        <externalId isNull="true" />
        <guid>guid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:6750</guid>
        <versionId>********-b951-4eed-b5a7-b9c8ff257a6c</versionId>
        <field1 isNull="true" />
        <field2 isNull="true" />
        <field3>0</field3>
        <field4 isNull="true" />
        <field5>false</field5>
        <clobField1 isNull="true" />
        <blobField1 isNull="true" />
        <BusinessProcessDiagram id="bpdid:412aec78ca1e02a9:-2e093335:18c5ee54b61:-151">
            <metadata>
                <entry>
                    <key>SAP_META.SHOULDRECOVER</key>
                    <value>no</value>
                </entry>
            </metadata>
            <name>ODC Reversal اعادة قيد تحصيل مستندى تصدير</name>
            <documentation></documentation>
            <name>ODC Reversal اعادة قيد تحصيل مستندى تصدير</name>
            <dimension>
                <size w="600" h="150" />
            </dimension>
            <author>mohamed.reda</author>
            <isTrackingEnabled>false</isTrackingEnabled>
            <isCriticalPathEnabled>false</isCriticalPathEnabled>
            <isSpcEnabled>false</isSpcEnabled>
            <isDueDateEnabled>false</isDueDateEnabled>
            <isAtRiskCalcEnabled>false</isAtRiskCalcEnabled>
            <creationDate>1693545399117</creationDate>
            <modificationDate>1702815828398</modificationDate>
            <perfMetricParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.6b3a9a63-f7c3-4ad5-9119-11b7ccecc0d6</perfMetricParticipantRef>
            <ownerTeamParticipantRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.48d9e8e1-2b51-4173-ac71-9a6c533d134e</ownerTeamParticipantRef>
            <metricSettings itemType="2" />
            <instanceNameExpression>" Request Type: "+ tw.local.odcRequest.requestType.name  +" , "+" CIF: "+ tw.local.odcRequest.cif +" , " +"Request Number: " +tw.system.process.instanceId</instanceNameExpression>
            <dueDateType>2</dueDateType>
            <dueDateTime>8</dueDateTime>
            <dueDateTimeResolution>1</dueDateTimeResolution>
            <dueDateTimeTOD>00:00</dueDateTimeTOD>
            <dueDateCustom></dueDateCustom>
            <officeIntegration>
                <sharePointParentSiteDisabled>true</sharePointParentSiteDisabled>
                <sharePointParentSiteName>&lt;#= tw.system.process.name #&gt;</sharePointParentSiteName>
                <sharePointParentSiteTemplate>ParentSiteTemplate.stp</sharePointParentSiteTemplate>
                <sharePointWorkspaceSiteName>&lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;</sharePointWorkspaceSiteName>
                <sharePointWorkspaceSiteDescription>This site has been automatically generated for managing collaborations and documents 
for the Lombardi TeamWorks process instance: &lt;#= tw.system.process.name #&gt; &lt;#= tw.system.process.instanceId #&gt;

TeamWorks Link:  http://bpmpcdev:9080/portal/jsp/getProcessDetails.do?bpdInstanceId=&lt;#= tw.system.process.instanceId #&gt;

</sharePointWorkspaceSiteDescription>
                <sharePointWorkspaceSiteTemplate>WorkspaceSiteTemplate.stp</sharePointWorkspaceSiteTemplate>
                <sharePointLCID>1033</sharePointLCID>
            </officeIntegration>
            <timeScheduleType>0</timeScheduleType>
            <timeScheduleName>NBEWork</timeScheduleName>
            <holidayScheduleName>NBEHoliday</holidayScheduleName>
            <holidayScheduleType>0</holidayScheduleType>
            <timezone>Egypt</timezone>
            <timezoneType>0</timezoneType>
            <executionProfile>default</executionProfile>
            <isSBOSyncEnabled>true</isSBOSyncEnabled>
            <allowContentOperations>false</allowContentOperations>
            <isLegacyCaseMigrated>false</isLegacyCaseMigrated>
            <hasCaseObjectParams>false</hasCaseObjectParams>
            <defaultPool>
                <BpmnObjectId id="320b2a03-2ef6-48f6-9980-49703310afb3" />
            </defaultPool>
            <defaultInstanceUI id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7893" />
            <ownerTeamInstanceUI id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7894" />
            <simulationScenario id="bpdid:ab265fcfa74a0689:-2c3f1876:18a4b3d9273:6763">
                <name>Default</name>
                <simNumInstances>100</simNumInstances>
                <simMinutesBetween>30</simMinutesBetween>
                <maxInstances>100</maxInstances>
                <useMaxInstances>true</useMaxInstances>
                <continueFromReal>false</continueFromReal>
                <useParticipantCalendars>false</useParticipantCalendars>
                <useDuration>false</useDuration>
                <duration>86400</duration>
                <startTime>1693545399551</startTime>
            </simulationScenario>
            <flow id="1d1902fe-e246-4af8-843c-92b62fbe98d3" connectionType="SequenceFlow">
                <name>To ACT02 Review ODC Reversal Request</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7845" />
                </connection>
            </flow>
            <flow id="cc8203a1-7910-4b2f-8009-0eeb7a8e3387" connectionType="SequenceFlow">
                <name>Back To initiator</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784f">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="45efef31-a3f7-48df-8e5f-55d0bd5a1a21" connectionType="SequenceFlow">
                <name>To Cancel</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7853">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.cancelRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="f88a6f6e-d92d-4874-8501-b54942aec83d" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7844" />
                </connection>
            </flow>
            <flow id="d1c41520-7c04-4ae2-8052-46fb397d3f2a" connectionType="SequenceFlow">
                <name>To Checker Decision Action</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7843" />
                </connection>
            </flow>
            <flow id="8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3" connectionType="SequenceFlow">
                <name>To Approve</name>
                <documentation></documentation>
                <nameVisible>true</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7851">
                        <expression>tw.local.odcRequest.stepLog.action	  ==	  tw.epv.CreationActions.approveRequest</expression>
                    </condition>
                </connection>
            </flow>
            <flow id="8adc5346-4dc4-48ea-88bc-847fe8c3e8bb" connectionType="SequenceFlow">
                <name>To ODC Initiators</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7842" />
                </connection>
            </flow>
            <flow id="949eda48-8282-4104-8add-a859c1fa92f3" connectionType="SequenceFlow">
                <name>To End Event</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7841" />
                </connection>
            </flow>
            <flow id="f2324e42-816f-420c-8247-62c22a708d72" connectionType="SequenceFlow">
                <name>To Send Escalation mail</name>
                <documentation></documentation>
                <nameVisible>false</nameVisible>
                <metricSettings itemType="32" />
                <connection>
                    <lineType>0</lineType>
                    <condition id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7840" />
                </connection>
            </flow>
            <pool id="320b2a03-2ef6-48f6-9980-49703310afb3">
                <name>Pool</name>
                <documentation></documentation>
                <restrictedName>at1693545399110</restrictedName>
                <dimension>
                    <size w="3000" h="600" />
                </dimension>
                <autoTrackingEnabled>false</autoTrackingEnabled>
                <lane id="2e4a3c95-9839-4c37-870f-b0215a22597b">
                    <name>Hub / center Maker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce</attachedParticipant>
                    <flowObject id="cfc9c782-1e15-4918-8597-81c8a226db13" componentType="Activity">
                        <name>ACT01 Create ODC Reversal Request</name>
                        <position>
                            <location x="338" y="59" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.b9bf1811-6e1e-41a3-828b-39059fa168cb</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>3</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.RACT01)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Create ODC Reversal Request –  إنشاء طلب اعادة قيد تحصيل مستندى تصدير &lt;#= tw.system.process.instanceId #&gt;</subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787e">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.602aae62-b023-4b72-8c17-f498f81897a8</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787d">
                                    <name>isFirstTime</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                                    <input>true</input>
                                    <value>tw.local.isFirstTime</value>
                                    <parameterId>2055.51c68e38-ec0a-4b49-8f5f-ad85bf61b258</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787c">
                                    <name>routingDetails</name>
                                    <classId>/12.faf28340-88a9-4a2a-8098-1c0b7c2ae727</classId>
                                    <input>true</input>
                                    <value>tw.local.routingDetails</value>
                                    <parameterId>2055.46373b11-9f39-48b9-8787-37bd5d89f78a</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787b">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.5150bbb4-cae4-43e1-8dd2-6f8cad5d3c74</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-787a">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.9158db1b-6ea5-48e1-8fbc-cc201780a32d</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7879">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.1bba753e-260d-4827-8632-e837d4c62da9</parameterId>
                                </outputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7878">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.b2b9c2f2-5126-4036-84c5-a9eec1e44108</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7877">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.4e9bd6e4-9a98-4011-8d08-9420e03b8dce</teamRef>
                                    <attachedActivityId>/1.302e4a54-16ab-47b9-9b70-d933329c72e7</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7876">
                                        <name>hubCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.hubCode</value>
                                        <parameterId>2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7875">
                                        <name>hubGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_MKR"</value>
                                        <parameterId>2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7874">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7850">
                            <positionId>rightCenter</positionId>
                            <input>true</input>
                            <flow ref="cc8203a1-7910-4b2f-8009-0eeb7a8e3387" />
                        </inputPort>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784d">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="8adc5346-4dc4-48ea-88bc-847fe8c3e8bb" />
                        </inputPort>
                        <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785b">
                            <positionId>bottomCenter</positionId>
                            <flow ref="1d1902fe-e246-4af8-843c-92b62fbe98d3" />
                        </outputPort>
                        <assignment id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7880">
                            <assignTime>2</assignTime>
                            <to>false</to>
                            <from>tw.local.isFirstTime</from>
                        </assignment>
                        <attachedEvent id="c43302a8-b1fa-4fa9-805b-d2abed461cf9" componentType="Event">
                            <name>Boundary Event1</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomLeft</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="cb3ec0ad-00f1-4b8c-8e62-ac8b2c7b2945">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="cfbc88db-07d4-47c8-816b-abf2a1695f2f">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784c">
                                <positionId>bottomCenter</positionId>
                                <flow ref="f88a6f6e-d92d-4874-8501-b54942aec83d" />
                            </outputPort>
                            <assignment id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7863">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskId</from>
                            </assignment>
                            <assignment id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7862">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="4d00b4e6-b345-483f-af99-b6815f8ebb6f" componentType="Event">
                        <name>Start</name>
                        <documentation></documentation>
                        <position>
                            <location x="39" y="82" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>1</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784e">
                            <positionId>rightCenter</positionId>
                            <flow ref="8adc5346-4dc4-48ea-88bc-847fe8c3e8bb" />
                        </outputPort>
                        <assignment id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7864">
                            <assignTime>2</assignTime>
                            <to>true</to>
                            <from>tw.local.isFirstTime</from>
                        </assignment>
                    </flowObject>
                </lane>
                <lane id="7e6e9014-5f8d-44eb-8ceb-ecba6505368c">
                    <name>Hub / center Checker</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>false</systemLane>
                    <attachedParticipant>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</attachedParticipant>
                    <flowObject id="204734d8-ab45-42fa-8dde-9f2d35136f07" componentType="Activity">
                        <name>ACT02 Review ODC Reversal Request</name>
                        <position>
                            <location x="338" y="45" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>1</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>1</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.c71c7233-23ac-4e5f-a241-09cd96502615</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>parseFloat(tw.epv.ODCCreationSLA.RACT02)</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <subject>Review ODC Reversal Request – مراجعة طلب اعادة قيد تحصيل مستندى تصدير </subject>
                                <forceSend>true</forceSend>
                                <timeSchedule>NBEWork</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>Africa/Cairo</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>NBEHoliday</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7872">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.a82241b6-421c-44f4-83f7-f6d3d3d3a024</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7871">
                                    <name>lastAction</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.lastAction</value>
                                    <parameterId>2055.8b489eb0-ce3f-46d6-8896-60fbb2cd2288</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7870">
                                    <name>parentPath</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.parentPath</value>
                                    <parameterId>2055.34b891a3-f943-4cf2-8753-3a356f300282</parameterId>
                                </inputActivityParameterMapping>
                                <outputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786f">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.d49e3545-463a-4eb4-8273-1e61d066f668</parameterId>
                                </outputActivityParameterMapping>
                                <laneFilter id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786e">
                                    <serviceType>1</serviceType>
                                    <teamRef>c3405c7d-d925-4ac6-9fc0-d8676db5885b/24.8e005024-3fe0-4848-8c3c-f1e9483900c6</teamRef>
                                    <attachedActivityId>/1.302e4a54-16ab-47b9-9b70-d933329c72e7</attachedActivityId>
                                    <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786d">
                                        <name>hubCode</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>tw.local.routingDetails.hubCode</value>
                                        <parameterId>2055.5909d6ac-ed7e-4812-a0d9-7336ebc88b97</parameterId>
                                    </inputActivityParameterMapping>
                                    <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786c">
                                        <name>hubGroupName</name>
                                        <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                        <input>true</input>
                                        <value>"BPM_IDC_HUB_"+tw.local.routingDetails.hubCode+"_EXE_CHKR"</value>
                                        <parameterId>2055.8f1cd69d-9820-463a-9d92-e01a1fea45c2</parameterId>
                                    </inputActivityParameterMapping>
                                </laneFilter>
                                <teamFilter id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-786b">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785a">
                            <positionId>topCenter</positionId>
                            <input>true</input>
                            <flow ref="1d1902fe-e246-4af8-843c-92b62fbe98d3" />
                        </inputPort>
                        <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7859">
                            <positionId>rightCenter</positionId>
                            <flow ref="d1c41520-7c04-4ae2-8052-46fb397d3f2a" />
                        </outputPort>
                        <attachedEvent id="462fbe06-d514-4387-8592-2acd0f6a0a5f" componentType="Event">
                            <name>Boundary Event2</name>
                            <documentation></documentation>
                            <position>
                                <location x="0" y="0" />
                            </position>
                            <positionId>bottomCenter</positionId>
                            <dropIconUrl>0</dropIconUrl>
                            <colorInput>Color</colorInput>
                            <component>
                                <nameVisible>true</nameVisible>
                                <eventType>3</eventType>
                                <cancelActivity>true</cancelActivity>
                                <repeatable>false</repeatable>
                                <doCloseTask>false</doCloseTask>
                                <EventAction id="613ceb42-45eb-44ef-8435-406142743427">
                                    <actionType>2</actionType>
                                    <actionSubType>0</actionSubType>
                                    <EventActionImplementation id="819261af-c815-4817-85c4-22b850f6f31a">
                                        <dateType>1</dateType>
                                        <relativeDirection>1</relativeDirection>
                                        <relativeTime>0</relativeTime>
                                        <relativeTimeResolution>0</relativeTimeResolution>
                                        <toleranceInterval>0</toleranceInterval>
                                        <toleranceIntervalResolution>0</toleranceIntervalResolution>
                                        <UseCalendar>false</UseCalendar>
                                    </EventActionImplementation>
                                </EventAction>
                            </component>
                            <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784a">
                                <positionId>bottomCenter</positionId>
                                <flow ref="f2324e42-816f-420c-8247-62c22a708d72" />
                            </outputPort>
                            <assignment id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785f">
                                <assignTime>2</assignTime>
                                <to>tw.system.step.task.id</to>
                                <from>tw.local.taskId</from>
                            </assignment>
                            <assignment id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-785e">
                                <assignTime>2</assignTime>
                                <to>""</to>
                                <from>tw.local.mailTo</from>
                            </assignment>
                        </attachedEvent>
                    </flowObject>
                    <flowObject id="6869d68e-0a73-474c-a9da-f7c788cf4d26" componentType="Event">
                        <name>Approve</name>
                        <documentation></documentation>
                        <position>
                            <location x="728" y="128" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#F8F8F8</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7852">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="60aa4589-527a-4330-8c62-e27d874075b2" componentType="Event">
                        <name>Cancel</name>
                        <documentation></documentation>
                        <position>
                            <location x="729" y="67" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7854">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="45efef31-a3f7-48df-8e5f-55d0bd5a1a21" />
                        </inputPort>
                    </flowObject>
                    <flowObject id="a66b788f-eb9d-4662-8d5a-15e1618e166e" componentType="Gateway">
                        <name>Checker Decision Action</name>
                        <documentation></documentation>
                        <position>
                            <location x="532" y="64" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <gatewayType>1</gatewayType>
                            <splitJoinType>0</splitJoinType>
                        </component>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7858">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="d1c41520-7c04-4ae2-8052-46fb397d3f2a" />
                        </inputPort>
                        <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7857">
                            <positionId>bottomCenter</positionId>
                            <flow ref="8c1b8c28-7e50-4f1c-8598-1dcf0a6c12c3" />
                        </outputPort>
                        <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7856">
                            <positionId>rightCenter</positionId>
                            <flow ref="45efef31-a3f7-48df-8e5f-55d0bd5a1a21" />
                        </outputPort>
                        <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7855">
                            <positionId>topCenter</positionId>
                            <flow ref="cc8203a1-7910-4b2f-8009-0eeb7a8e3387" />
                        </outputPort>
                    </flowObject>
                </lane>
                <lane id="9180d59b-6f02-4dad-a33f-d017ed000296">
                    <name>System</name>
                    <height>200</height>
                    <laneColor>0</laneColor>
                    <systemLane>true</systemLane>
                    <attachedParticipant>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</attachedParticipant>
                    <flowObject id="956dcbec-d5f4-4a3d-8f90-86bed3f21e33" componentType="Activity">
                        <name>Send Escalation mail</name>
                        <documentation>Escalation mail service: &lt;div&gt;   this service is running when the task has delay to be done &lt;/div&gt;</documentation>
                        <position>
                            <location x="378" y="22" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <loopType>0</loopType>
                            <loopMaximum>1</loopMaximum>
                            <startQuantity>1</startQuantity>
                            <isAutoflowable>false</isAutoflowable>
                            <MIOrdering>1</MIOrdering>
                            <MIFlowCondition>0</MIFlowCondition>
                            <cancelRemainingInstances>false</cancelRemainingInstances>
                            <metricSettings itemType="4" />
                            <implementationType>4</implementationType>
                            <isConditional>false</isConditional>
                            <conditionScript></conditionScript>
                            <bpmnTaskType>3</bpmnTaskType>
                            <activityOptionType>REQUIRED</activityOptionType>
                            <activityExecutionType>NONE</activityExecutionType>
                            <activityExecutionTypePreviousValue>AUTOMATIC</activityExecutionTypePreviousValue>
                            <isHidden>false</isHidden>
                            <isRepeatable>false</isRepeatable>
                            <transactionalBehavior>0</transactionalBehavior>
                            <isRobotTask>false</isRobotTask>
                            <implementation>
                                <attachedActivityId>/1.d7acf968-6740-4e52-b037-2049466eeeb2</attachedActivityId>
                                <sendToType>1</sendToType>
                                <taskRouting>0</taskRouting>
                                <dueDateType>1</dueDateType>
                                <dueDateTime>1</dueDateTime>
                                <dueDateTimeResolution>1</dueDateTimeResolution>
                                <dueDateTimeTOD>00:00</dueDateTimeTOD>
                                <priorityType>0</priorityType>
                                <priority>30.30</priority>
                                <forceSend>true</forceSend>
                                <noTask>true</noTask>
                                <timeSchedule>(use default)</timeSchedule>
                                <timeScheduleType>0</timeScheduleType>
                                <timeZone>(use default)</timeZone>
                                <timeZoneType>0</timeZoneType>
                                <holidaySchedule>(use default)</holidaySchedule>
                                <holidayScheduleType>0</holidayScheduleType>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7869">
                                    <name>odcRequest</name>
                                    <classId>/12.5f8547c1-515a-481c-b74a-f20ec40faff9</classId>
                                    <input>true</input>
                                    <value>tw.local.odcRequest</value>
                                    <parameterId>2055.c037eb14-745b-44c9-8a5d-219c09ddfa0f</parameterId>
                                </inputActivityParameterMapping>
                                <inputActivityParameterMapping id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7868">
                                    <name>taskId</name>
                                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                                    <input>true</input>
                                    <value>tw.local.taskId</value>
                                    <parameterId>2055.4bbb4320-0b75-42b2-8b36-8317a0a9bac1</parameterId>
                                </inputActivityParameterMapping>
                                <laneFilter id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7866">
                                    <serviceType>1</serviceType>
                                    <teamRef>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/24.6fd38d02-81cf-48ab-bd42-8ff4c0a1628b</teamRef>
                                </laneFilter>
                                <teamFilter id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7865">
                                    <serviceType>1</serviceType>
                                </teamFilter>
                            </implementation>
                        </component>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-784b">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="f88a6f6e-d92d-4874-8501-b54942aec83d" />
                        </inputPort>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7849">
                            <positionId>topLeft</positionId>
                            <input>true</input>
                            <flow ref="f2324e42-816f-420c-8247-62c22a708d72" />
                        </inputPort>
                        <outputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7848">
                            <positionId>rightCenter</positionId>
                            <flow ref="949eda48-8282-4104-8add-a859c1fa92f3" />
                        </outputPort>
                    </flowObject>
                    <flowObject id="c8b8dc76-b1ba-4d4e-8653-ea6dc4248654" componentType="Event">
                        <name>End Event</name>
                        <documentation></documentation>
                        <position>
                            <location x="715" y="45" />
                        </position>
                        <dropIconUrl>0</dropIconUrl>
                        <colorInput>#A5B7CD</colorInput>
                        <component>
                            <nameVisible>true</nameVisible>
                            <eventType>2</eventType>
                            <cancelActivity>true</cancelActivity>
                            <repeatable>false</repeatable>
                            <doCloseTask>true</doCloseTask>
                        </component>
                        <inputPort id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7847">
                            <positionId>leftCenter</positionId>
                            <input>true</input>
                            <flow ref="949eda48-8282-4104-8add-a859c1fa92f3" />
                        </inputPort>
                    </flowObject>
                </lane>
                <inputParameter id="86f87789-824f-4930-8c19-5a719284c659">
                    <bpdParameterId>2007.86f87789-824f-4930-8c19-5a719284c659</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <inputParameter id="684c7152-3e6b-4505-8f37-4ed21a611236">
                    <bpdParameterId>2007.684c7152-3e6b-4505-8f37-4ed21a611236</bpdParameterId>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                </inputParameter>
                <privateVariable id="862e24d6-e072-4653-8abd-681972b119f0">
                    <name>isFirstTime</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.83ff975e-8dbc-42e5-b738-fa8bc08274a2</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>true</hasDefault>
                    <defaultValue>true</defaultValue>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="159f0712-06fa-4aa8-8a5d-bef2234b0a9f">
                    <name>lastAction</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="db3fa75c-e63e-443f-8487-98f335bea12e">
                    <name>taskId</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="4e5d0b49-dad8-4560-8d99-82f26b0daeb8">
                    <name>mailTo</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <privateVariable id="f2304205-e593-453a-805b-8a8ffe5c8497">
                    <name>parentPath</name>
                    <description></description>
                    <classId>b5a7448c-61c1-4e1e-9933-3912eb5c29ad/12.db884a3c-c533-44b7-bb2d-47bec8ad4022</classId>
                    <arrayOf>false</arrayOf>
                    <hasDefault>false</hasDefault>
                    <visibleInSearch>false</visibleInSearch>
                    <isProcessInstanceCorrelator>false</isProcessInstanceCorrelator>
                    <isSharedContext>false</isSharedContext>
                </privateVariable>
                <epv id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7882">
                    <epvId>/21.daf76aa9-3be6-432b-b228-01c27b3012d3</epvId>
                </epv>
                <epv id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7881">
                    <epvId>/21.dbbaa047-8f02-4397-b1b5-41f11b0256b3</epvId>
                </epv>
                <searchableField id="7b6a22dc-233a-4ecd-8cbb-ee6921af4adc">
                    <name>InitiatorHubBranch</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4894">
                        <expression>tw.local.odcRequest.initiator</expression>
                    </expression>
                </searchableField>
                <searchableField id="f8755e6e-8272-431b-82c8-e9f1e7ed15ce">
                    <name>parentRequestNumber</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4893">
                        <expression>tw.local.odcRequest.parentRequestNo</expression>
                    </expression>
                </searchableField>
                <searchableField id="7eb6d0f9-71e2-4496-86ce-d833141978e6">
                    <name>requestDate</name>
                    <type>5</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4892">
                        <expression>tw.local.odcRequest.requestDate</expression>
                    </expression>
                </searchableField>
                <searchableField id="a339ecd6-4974-4376-8f65-0fcf632e8222">
                    <name>requestStatus</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4891">
                        <expression>tw.local.odcRequest.appInfo.status</expression>
                    </expression>
                </searchableField>
                <searchableField id="cbf1ac69-8dd1-4ef4-8633-6640a9052e85">
                    <name>InitiatorUserName</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-4890">
                        <expression>tw.local.odcRequest.appInfo.initiator</expression>
                    </expression>
                </searchableField>
                <searchableField id="e19ec9a2-b0e8-430d-89e9-a631fd4af208">
                    <name>customerCIF</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-488f">
                        <expression>tw.local.odcRequest.CustomerInfo.cif</expression>
                    </expression>
                </searchableField>
                <searchableField id="83c8ec89-336a-4889-8831-2db11de97e8d">
                    <name>customerName</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-488e">
                        <expression>tw.local.odcRequest.customerName</expression>
                    </expression>
                </searchableField>
                <searchableField id="891a5bad-505c-4409-8a7d-e7068726a11a">
                    <name>customerName_1</name>
                    <type>2</type>
                    <expression id="bpdid:a32442299065080d:748af990:1975177df1a:-488d">
                        <expression>tw.local.odcRequest.CustomerInfo.customerName</expression>
                    </expression>
                </searchableField>
            </pool>
            <extension id="bpdid:7cf3b5b49b9bcdb8:3c4f5f27:18bdf297ae9:-7846" type="CASE">
                <caseFolder id="5aba4da2-4484-4810-835b-d5873189e7d7">
                    <allowLocalDoc>false</allowLocalDoc>
                    <allowExternalDoc>false</allowExternalDoc>
                    <allowSubfoldersCreation>false</allowSubfoldersCreation>
                    <allowExternalFolder>false</allowExternalFolder>
                </caseFolder>
            </extension>
        </BusinessProcessDiagram>
    </bpd>
</teamworks>

